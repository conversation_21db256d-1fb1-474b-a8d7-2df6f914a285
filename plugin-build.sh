#!/bin/bash
set -eu
set -o pipefail

function set_go_env() {
    # GO ENV
    go env -w GO111MODULE=on
    go env -w GONOPROXY=\*\*.baidu.com\*\*
    go env -w GONOSUMDB=\*

    unset GOPROXY
    go env -w GOPROXY=https://goproxy.baidu-int.com

    go mod tidy
}

function package_deploy()
{
    set_go_env

    # 获取源码最近一次 git commit log，包含 commit sha 值，以及 commit message
    GitCommitLog=`git log --pretty=oneline -n 1`
    # 将 log 原始字符串中的单引号替换成双引号
    GitCommitLog=${GitCommitLog//\'/\"}
    # 获取当前时间
    BuildTime=`date +'%Y.%m.%d.%H%M%S'`
    BuildUsername=${BUILD_USERNAME}

    # 将以上变量序列化至 LDFlags 变量中
    LDFlags=" \
        -X 'icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/buildinfo.GitCommitLog=${GitCommitLog}' \
        -X 'icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/buildinfo.BuildTime=${BuildTime}' \
        -X 'icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/buildinfo.BuildUsername=${BuildUsername}' \
    "

    declare -A pluginMap
    # 组件列表，key 是名称，用于镜像名，value 是包路径，用于 go build
    pluginMap["cce-base"]="./base"
    pluginMap["cce-ingress-controller"]="./services/network/ingress-controller"
    # cce-network-inspector 有多个镜像, 先只更新一个
    pluginMap["cce-network-inspector"]="./services/network/inspector/client"
    pluginMap["cce-lb-controller"]="./services/network/lb-controller"
    # csi 代码已经移到开源库进行维护，不再通过这里进行构建
    # pluginMap["cce-csi-cds-plugin"]="./services/storage/cmd/cds"
    # cce-cloud-node-controller
    pluginMap["cce-cloud-node-controller"]="./services/cluster/cce-cloud-node-controller"
    pluginMap["cce-node-sla-controller"]="./services/hybrid/node-sla-controller"

    # cce-virtual-kubelet
    pluginMap["cce-virtual-kubelet"]="./services/serverless/virtual-kubelet/baidu-bci"

    # kube-external-auditor
    pluginMap["kube-external-auditor"]="./services/monitor/external-auditer"
    # cce-onepilot
    pluginMap["cce-onepilot"]="./services/monitor/cce-onepilot"
    pluginMap["agent-downloader"]="./services/monitor/cce-onepilot/agent-downloader"
    # cce-log-operator
    pluginMap["cce-log-operator"]="./services/monitor/cce-log-operator"
    
    declare -A pluginMapArm64
    pluginMapArm64["cce-ingress-controller"]="./services/network/ingress-controller"
    pluginMapArm64["cce-lb-controller"]="./services/network/lb-controller"
    pluginMapArm64["cce-cloud-node-controller"]="./services/cluster/cce-cloud-node-controller"

    rm -rf output
    mkdir output
    for name in ${!pluginMap[*]}
    do
        path=${pluginMap[${name}]}
        bin_path="${path}/${name}"

        echo "==========Package begin: name=${name} path=${path} bin_path=${bin_path}============"
        mkdir -p output/${name}
        cp ${path}/Dockerfile output/${name}/

        if [ ${name} = "cce-base" ]; then
            cp -r ${path}/bins output/${name}/
        elif [ ${name} = "cce-virtual-kubelet" ]; then
            CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/virtual-kubelet.bin ${path}/cmd/virtual-kubelet
            if [[ $? -ne 0 ]]; then
                echo "[----------'GO build failed!----------]"
                echo "Command: CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/virtual-kubelet.bin ${path}/cmd/virtual-kubelet"
                exit -1
            fi
            cp ${path}/virtual-kubelet.bin output/${name}/
            cp -r ${path}/docker output/${name}/
        elif [ ${name} = "cce-log-operator" ]; then
            CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/${name} ${path}/
            if [[ $? -ne 0 ]]; then
                echo "[----------'GO build failed!----------]"
                echo "Command: CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/${name} ${path}/"
                exit -1
            fi
            cp ${path}/${name} output/${name}/
            cp -r ${path}/deploy output/${name}/
        else
            # 编译二进制
            # -trimpath: 去掉该参数, 不同路径相同代码, 编译出来二进制 MD5 不一样
            CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/${name} ${path}/
            if [[ $? -ne 0 ]]; then
                echo "[----------'GO build failed!----------]"
                echo "Command: CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/${name} ${path}/"
                exit -1
            fi
            cp ${path}/${name} output/${name}/
        fi
        echo "==========Package success: name=${name}======="
    done
    cp plugin_build_info output/

    # Build ARM-64 二进制并将其移入 output 目录
    echo "==========Build ARM-64 bins begin ======="
    for plugin_name in ${!pluginMapArm64[*]}
    do
        path=${pluginMapArm64[${plugin_name}]}

        bin_name_arm64="${plugin_name}-arm64"
        docker_file_arm64="${path}/Dockerfile-arm64"

        mkdir -p output/${plugin_name}
        cp $docker_file_arm64 output/${plugin_name}/

        if [ ${plugin_name} = "cce-base" ]; then
            cp -r ${path}/bins output/${plugin_name}/
        else
            # 编译二进制
            # -trimpath: 去掉该参数, 不同路径相同代码, 编译出来二进制 MD5 不一样
            CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -trimpath -ldflags "${LDFlags}" -o ${path}/${bin_name_arm64} ${path}/
            if [[ $? -ne 0 ]]; then
                echo "[----------'GO build failed!----------]"
                echo "Command: CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -trimpath -ldflags ${LDFlags} -o ${path}/${bin_name_arm64} ${path}/"
                exit -1
            fi
            cp ${path}/${bin_name_arm64} output/${plugin_name}/
        fi
        echo "==========Package ARM-64 success: name=${plugin_name}======="
    done
    echo "==========Build ARM-64 bins end ======="

    echo "==========Package begin: chart files============"
    cp -r pkg/plugin/helm/charts output/
    echo "==========Package success: chart files======="
}

parameter=${1:-build}
if [[ $parameter == "quick" ]];then
    package_deploy
else
    package_deploy
fi