# CCE 项目周报 - 2022-03-25 ~ 2022-04-01

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6694: [集群管理][集群升级] 取消 workflow-controller 更新 cluster phase 逻辑
  * CCE-6694: [集群管理][集群升级] 取消 workflow-controller 更新 cluster phase 逻辑
  * CCE-6297: [集群管理][集群升级] 增加 TaskClientList 和 TaskGrou 一致性校验
  * CCE-6297: [集群管理][集群升级] UpgradeNodes 取消 CCEInstanceIDList 去重校验, 避免重试 Status.Task 和 TaskClient 不一致
  * CCE-6674: [集群管理][集群升级] DrainNode timeout 参数修复
  * CCE-6604: [集群管理][集群升级] kube-apiserver 升级 1.20 增加 --enable-admission-plugins 等配置校验
  * CCE-6652: [集群管理][集群升级] Master 变更执行完成后再 updateK8SVersion

* 子超
  * 无进展

### 支持前置校验@启龙

* 启龙
  * 无进展

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 冀超
  * CCE-6678 [集群管理][体验优化] 增加修改ClusterCRD Label接口

* 臧浩
  * CCE-5615:[集群管理][体验优化] 节点组删除接口超时

### 集群扩容

* 霖皇
  * CCE-6721 [集群管理][集群扩容] cceadm 支持 ak,sk 手动添加节点
  * CCE-6721 [集群管理][集群扩容] cceadm 支持 ak,sk 手动添加节点

### Serverless集群

* 子超
  * CCE-6301 [集群管理][Serverless集群] Always enable service controller for bec serverless cluster
  * CCE-6301 [集群管理][Serverless集群] Support bec serverless cluster

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * 无进展

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * CCE-6472 [Improvement] [CCR][功能完善] 网络非阻塞调和

* 温满祥
  * Merge "CCE-6472 [Improvement] [CCR][功能完善] 网络非阻塞调和"

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * CCE-6649: [CCE 可观测性][CCE 黄金指标建设] CCE 集群监控新加告警不生效问题修复

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6439 [容器网络][流量接入优化] 允许转发规则重排序
  * CCE-6439 [容器网络][流量接入优化] 允许转发规则重排序
  * CCE-6591 [容器网络][流量接入优化] IngressController限制后端数
  * CCE-6591 [容器网络][流量接入优化] IngressController限制后端数
  * CCE-6591 [容器网络][流量接入优化] IngressController限制后端数
  * CCE-6627 [容器网络][流量接入优化] CCM 删除幂等
  * CCE-6626 [容器网络][流量接入优化] CCM BLB名字超长处理

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 运维运营

### 南京开服

* 陈欢
  * CCE-6684: [运维运营][南京开服] 增加 values-nj.yaml SDK Endpoint
  * CCE-6684: [运维运营][南京开服] 补充 VIP 申请信息
  * CCE-6684: [运维运营][南京开服] cce-dev clusterrole 通过 cce-stack helm chart 管理
  * CCE-6681: [运维运营][南京开服] 新地域开服 SOP

* 周珂
  * CCE-6684: [运维运营][南京开服] 补充mysql endpoint
  * CCE-6684: [运维运营][南京开服] 补充k8s集群搭建和vip申请

### 稳定性

* 陈欢
  * CCE-6075: [运维运营][稳定性] CCE 历史值班问题梳理

## 可观测性

### cprom

* 子华
  * CCE-4976: [可观测性][cprom] 检测使用了容器监控的用户集群
  * CCE-4976: [可观测性][cprom] list binding cluster 接口添加 agentCreationTimestamp 字段
  * CCE-4976: [可观测性][cprom] 更新 grafana dashboard 变量更新状态
  * CCE-4976: [可观测性][cprom] 更新 grafana dashboard 默认值
  * CCE-4976: [可观测性][cprom] 更新 grafana v7.5.0 => v8.4.4
  * CCE-4976: [可观测性][cprom] cprom 所有组件添加版本信息
  * CCE-4976: [可观测性][cprom] 调整 cprom gpu grafana provisoning
  * CCE-4976: [可观测性][cprom] 调整 cprom grafana provisoning

## 不规范提交

* author={zhanghong15} commit={CCE-6686 update CreateBCCSubnetID to use zoneD subnet}
* author={zhanghong15} commit={CCE-6686 调整北京回归测试使用的机型为N5 bcc.g4.c2m8}
* author={zhouke03} commit={CCE-6642 [New Feature] CCE南京金山区开服}
* author={liqilong} commit={CCE-6555 [New Feature] 询价接口增加spec}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-172 [Task] 告警通知回调}
* author={liqilong} commit={CCE-6555 [New Feature] 询价接口增加spec}
* author={duzhanwei} commit={Cloud-Prometheus-Product-149 [Task] 增加用户白名单查询}
* author={duzhanwei} commit={CCE-6625 [Task] registry操作转发}
* author={duzhanwei} commit={ CCE-6669 [New Feature] iregistry list project的优化}
* author={wenmanxiang} commit={CCE-6675 [Task] [CCR] 修复ccr-iregistry模糊查询匹配错误}
* author={duzhanwei} commit={CCE-6669 [New Feature] iregistry list project的优化}
* author={duzhanwei} commit={CCE-6625 [Task] 对接iregistry权限}
* author={chenyaqi01} commit={CCE-6719 downgrade netlink}
* author={chenyaqi01} commit={CCE-6719 build eni cache to reduce CPU usage}
* author={chenyaqi01} commit={CCE-6685 rename cloud api to be more appropriate}
* author={chenyaqi01} commit={CCE-6685 update github.com/vishvananda/netlink and log to stderr if err is not type of syscall.Errno}
