# CCE 2021-06 

## 2021-06-01

### 功能

* [集群管理]
  * CCE-3681 create bid client 问题修复; @宏伟
* [边缘集群]
  * OTE-5733 更新最新cce@bec 插件镜像;
  * OTE-5800 同步cce-edge上线镜像版本
  * OTE-5733 完善CCE@Edge k8s插件的chart模板
  * CCE-3679 master 可选安全组开放云边通道 8765 端口; @思远
* [AI]
  * CCE-3755 node 增加 gpu label; @冀超
  * CCE-3580 cgpu 兼容 ubuntu; @刘霖
  * CCE-3580 cgpu 插件脚本兼容ubuntu, cgpu-monitor独占模式也部署; @刘霖
  * CCE-3699: node-exporter Dockfile 安装 CUDA; @陈欢
  * CCE-3653 Support paddle job; @子超
  * CCE-3653 Update paddlejob crd to latest; @子超
  * CCE-3653 Implement SetJobVolcanoQueue for PaddleJob.; @子超
  * CCE-3653 Add OwnerReferences in app service pod list; @子超
  * CCE-3697 Update README of cce-aibox; @子超
  * CCE-3745 Fix sa used by mxnet-operator; @子超
  * CCE-3738 Support k8s name keyword filter; @子超
  * CCE-3693 Implement IsGPUManagerAvailable api; @子超
  * CCE-3693 Implement CompletionTime for paddlejob; @子超
  * CCE-3693 Make ai queue compatibe with v1 cluster; @子超
  * CCE-3697 Update README of cce-aibox and cce-rdma-plugin; @子超
  * CCE-3693 Make ISGPUManagerAvailable compatibe with v1 cluster; @子超
  * CCE-3756 Return releases in all statuses in helm service list; @子超
  * CCE-3693 Eliminate dependency on aijob service for ISGPUManagerAvailable; @子超
  * CCE-3756 更新 volcano 镜像; @子华
  * CCE-3746 cce-volcano 插件 README 修改; @子华
  * CCE-3746 podgroup 默认创建方案; @子华
  * CCE-3746 podgroup 默认创建功能支持; @子华
  * CCE-3756 更新 volcano 调度配置; @子华
  * CCE-3746 aibox rbac errors 修复; @子华G
  * CCE-3702 volcano webhook 反注册; @子华
  * CCE-3756 解决 volcano helm delete; @子华
  * CCE-3702 volcano webhook service 修改名字; @子华
  * CCE-3756 解决 volcano webhook secret 循环依赖问题; @子华
  * CCE-3756 更新 gen-admission-secret.sh 脚本确保 helm update 最终一致; @子华
* [容器网络]
  * CCE-3674: 网络工具命令记录; @陈欢
  * CCE-3708 cce-lb-controller 增加单测; @冀超
  * CCE-3739 cce-lb-controller 支持使用tag; @冀超
  * CCE-3708 cce-lb-controller 使用服务器组; @冀超
  * CCE-3568 补充斗鱼 CNI 文档; @亚奇
  * CCE-3741 GetClusterExtraInfo 返回 ENI 子网; @亚奇
  * CCE-3568 斗鱼 CNI 支持 dynamic config 动态扩容; @亚奇
* [Serverless]
  * CCE-3723 [Task] [BCI] 统计订单耗时; @子超
  * CCE-1618 Wait at most 150s for serverless pod to be running in e2e test; @子超
  * CCE-3680 Use Env instead of Region in clusterID of vk to avoid conflict between gz and gztest; @子超
* [自动测试]
  * CCE-3660: 开启 gztest managed_cluster 回归; @陈欢
* [可观测性]
  * CCE-3711 user-cluster master node dashboard; @子华
  * CCE-3742 user-cluster control-plane-metrics access cert configmap; @子华
  * CCE-3681 修复 node notready 恢复后持续报警的问题; @陈欢
* [其他]
  * CCE-3724: upload.py 运维工具; @陈欢

## 2021-06-08

### 功能

* [集群管理]
  * CCE-3804: UserGroup RBAC 注释; @陈欢
  * CCE-3813 [Task] 支持 CentOS 7.9; @启龙
  * CCE-3835: BLB InternalAPI 增加 source=cce 参数; @陈欢
  * CCE-3835: internalBLB 增加 GetLoadBalancer 方法; @陈欢
  * CCE-3819: 修复 getMachine exist=false, 导致空指针问题; @陈欢
  * CCE-3365 管理员扮演 Role 获取 OIDC 集群凭证; @宏伟
  * CCE-3805 安全组不主动解绑非 SecurityGroup 设置的安全组; @宏伟
  * CCE-3798: 创建集群的子用户默认具备 cluster-admin RBAC 权限; @陈欢
  * CCE-3772: server.Addresses.FixedAddr.AddrIPV4 is empty 情况下保证删除幂等; @陈欢
  * CCE-3718: 移出 Node 时删除 /etc/systemd/system/kubelet.conf 配置, 避免重新加入节点; @陈欢
  * CCE-3667 [New Feature] 【安全部需求】内部账户-创建集群新建节点-默认选择内部上云镜像; @宏伟
  * CCE-3767 节点组暂停发单用户体验优化（除了超时以外其他错误先重试，重试5次后都失败再暂停，并且添加暂停最长时间30min); @思远
* [插件管理]
  * CCE-3789 1.13.10 CCM 使用指定版本部署; @冀超
  * CCE-3782 增加用NodeName查询Instance接口; @冀超
  * CCE-3782 ccm node 使用 NodeName查询Instance接口; @冀超
* [CCE@BEC]
  * CCE-3679 增加云边集群CCE Master默认安全组规则; @思远
  * CCE-3792 kube-proxy 10249 10256端口只对内网地址开放; @思远
  * CCE-3792 kube-external-auditer 监听端口只对localhost开放; @思远
  * CCE-3792 云边集群/纯边集群 10255、10251、10252端口只对内网开放; @思远
  * CCE-3792 云边集群/纯边集群 10255 端口只对内网开放 10251、10252 只对 localhost 开放; @思远
* [AI]
  * CCE-3799 aijob 测试 yaml; @庭丽
  * CCE-3711 更新 volcano 插件; @子华
  * CCE-3800 修复监控数据近七天查询失败; @子华
  * CCE-3580 cgpu 更新镜像，补充版本信息; @子华
  * CCE-3583 Add aijob and aiqueue resources in rbac role: @子超
  * CCE-3831 [New Feature] 【CCE AI】队列禁止删除逻辑—接口层; @启龙
  * CCE-3693 Be compatible with paddlejob with no ps; @子超
  * CCE-3583 Fetch job in all namespaces in one requests; @子超
  * CCE-3693 Eliminate dependency on user kubeconfig for ISGPUManagerAvailable; @子超
* [容器网络]
  * CCE-3568 更新 CNI 探测工具; @亚奇
  * CCE-3568 斗鱼CNI，优化 iaas 主从同步处理; @亚奇
  * CCE-3740 cce-lb-controller update逻辑优化; @冀超
* [标准错误码]
  * CCE-3797 BLB 权限不足标准错误; @陈欢
  * CCE-3801 ENI 删除失败标准错误; @陈欢
  * CCE-3497 错误信息返回删除失败弹性网卡; @陈欢
  * CCE-3841: BCC INVALID_BCC_FLAVOR_CONFIG 错误; @陈欢
* [Serverless]
  * CCE-2448 Use CCR for kube-proxy sidecar image; @子超
  * CCE-3758 Add weight and quota for subnet selection; @子超
* [可观测性]
  * CCE-3667 crm endpoint; @启龙
  * CCE-3773 cce 服务画像接口梳理; @子华
  * CCE-3711 用户集群 etcd 监控面板; @子华
  * CCE-3711 用户集群 kube-apiserver 监控面板; @子华
  * CCE-3660: 报警增加屏蔽提示; @陈欢
  * CCE-3840: ClusterHealthCheck 增加用户信息; @陈欢
  * CCE-3840: ClusterHealthCheck 报警增加用户信息; @陈欢
  * CCE-3660: ClusterHealthCheck 增加第一次故障时间; @陈欢
  * CCE-3660: ClusterHealthCheck 重试 3 次连续异常才写入 HealthCheck; @陈欢
  * CCE-3707 pod 异常事件格式化; @宏伟
  * CCE-3707 serverless pod 异常事件报警; @宏伟
  * CCE-3707 修复 pod 异常事件报警不能及时停止的问题; @宏伟
  * CCE-3681 修复 k8s-event-collector svc dns 解析异常; @宏伟
  * CCE-3681 修复 k8s-event-collector 获取 metacluster 异常; @宏伟
* [私有化]
  * CCE-3568 更新镜像地址; @刘霖
* [自动化测试]
  * CCE-3583 Enable managed cluster e2e in hkg and gztest; @子超
  * CCE-3767 instancegroup e2e test case 等待超时时间增加到20min; @思远

## 2021-06-10

### 功能

* [集群管理]
  * CCE-3808 修复 FloatingIP 不存在的错误; @陈欢
  * CCE-3821: 北京 auth-service endpoint 修改; @刘霖
  * CCE-3851 instance 支持自定义 annotations; @思远
  * CCE-3846 cluster-service configmap 格式错误修复; @启龙
  * CCE-3848 instancegroup controller 支持spec指定移入移出的节点; @思远
  * CCE-3848 instancegroup controller 计算当前实际节点数时考虑指定移入和移出的节点; @思远
* [AI]
  * CCE-3831 新增queue下joblist 接口; @启龙
  * CCE-3832 [New Feature] 【CCE AI】队列禁止删除逻辑—webhook; @启龙
* [容器网络]
  * CCE-3849 Fix IP 占用后继续尝试获取 IP; @亚奇
* [插件管理]
  * CCE-3776 CCM Node-Controller部署与单测; @冀超
* [标准错误码]
  * CCE-3850: NodeNotReady 错误码; @陈欢
  * CCE-3845: wrap deploy failed error; @陈欢
  * CCE-3796: SSH 登录失败的标准错误码; @陈欢
* [可观测性]
  * CCE-3846 BCM 开关与 ES 持久化开关解耦; @子华
  * CCE-3828 存量容器没创建日志采集规则; @子华
  * CCE-3855 cce-thanos 采集 vip 用户所有集群; @子华
  * CCE-3790 只采集一个 master 节点的 kube-state-metrics; @子华

## 2021-06-15

### 功能

* [集群管理]
  * CCE-3789 CCM 区分 v1, v2 版本; @冀超
  * CCE-3805 已有节点附加 CCE 安全组; @宏伟
  * CCE-3667 内部账户默认选择内部上云镜像，功能开放; @启龙
* [可观测性]
  * CCE-3860 prometheus 告警 demo 添加; @子华
  * CCE-3667 内部账户默认选择内部上云镜像，功能开放; @启龙
* [私有化]
  * CCE-3414 行业云3.0集测部署联调 @刘霖

## 2021-06-17

### 功能

* [集群管理]
  * CCE-3952 descheduler 插件化; @子华
  * CCE-3766 task controller 框架; @思远
  * CCE-3922 [Improvement] 去除无效机型gn3; @启龙
  * CCE-3908: 透传 InstanceType, 支持 AMD 机型; @陈欢
  * CCE-3766 task controller instancegroup handler 实现; @思远
  * CCE-3904: custom、autoscaler集群镜像centos6.3替换成centos7.3; @庭丽
* [竞价实例]
  * CCE-3923: 修复竞价实例移入 RootDiskSize 为空; @陈欢
  * CCE-3898: instance-gc-manager 设计和提测说明; @陈欢
  * CCE-3885: 回收竞价实例使用 cluster-service 接口; @陈欢
  * CCE-3898: 修改 instance-polling 为 instance-gc-manager; @陈欢
  * CCE-3885: BID Instance 被释放后才 Delete CCE Instance; @陈欢
  * CCE-3924: 调用 cluster-service 回收 Instance, 修复竞价失败未回收问题; @陈欢
* [镜像仓库]
  * CCE-3867 image plugin yaml error; @启龙
  * CCE-3867 [Improvement] 去除cce-registry-secret; @启龙
  * CCE-3867 [Improvement] 【镜像】meta cluster 替换成免密拉取; @启龙
* [插件管理]
  * CCE-3755 node 增加 gpu label; @冀超
  * CCE-3899 集群必备插件 Helm 化部署; @冀超
  * CCE-3253 LB Controller 与 Ingress Controller 支持资源 Tag; @冀超
  * CCE-3414 gpu 插件 nodeSelector label 替换为 beta.kubernetes.io/instance-gpu=true; @刘霖
* [serverless]
  * CCE-3415 s-k8s-39 Fix token not match flake; @子超
  * CCE-3906 BCI+Serverless+火花思维问题排查DOC+工具; @子超
  * CCE-3415 Support standard vk config in CreateClusterRequest; @子超
  * CCE-3920 [Serverless] Check if subnet is belong to zone available for bci creation in CreateClusterRequest; @子超
* [容器网络]
  * CCE-3868 更新 cni yaml; @亚奇
* [私有化]
  * CCE-3414 行业云3.0集测部署联调; @刘霖
  * CCE-3918 产品运维平台对接V2集群; @刘霖
* [可观测性]
  * CCE-3907 告警提示监控链接; @子华
  * CCE-3665 添加 cce-thanos 告警; @子华
  * CCE-3907 格式化 hi 消息通知格式; @子华
  * CCE-3912 logRule 添加 bes_id 字段;@子华
  * CCE-3905 thanos rule 添加 rule-reloader 容器; @子华
  * CCE-3897 cce-alert-webhook 添加 hi 机器人告警; @子华

## 2021-06-22

* [集群管理]
  * CCE-3908: 透传 InstanceType, 支持 AMD 机型
  * CCE-3922 [Improvement] 去除无效机型gn3
  * CCE-3923: 修复竞价实例移入 RootDiskSize 为空
  * CCE-3924: 调用 cluster-service 回收 Instance, 修复竞价失败未回收问题
* [插件管理]
  * CCE-3952 descheduler 插件化
  * CCE-3755 node增加gpu label
  * CCE-3899 集群必备插件 Helm 化部署
  * CCE-3253 LB Controller 与 Ingress Controller 支持资源 Tag
  * CCE-3904:custom/autoscaler集群镜像centos6.3替换成centos7.3
  * CCE-3414 gpu 插件 nodeSelector label 替换为 beta.kubernetes.io/instance-gpu=true
* [行业云私有化]
  * CCE-3414 行业云3.0集测部署联调
  * CCE-3918 产品运维平台对接V2集群
* [Serverless]
  * CCE-3415 s-k8s-39 Fix token not match flake
  * CCE-3906 BCI+Serverless+火花思维问题排查DOC+工具
  * CCE-3920 [Serverless] 修正 zoneResource 接口返参
  * CCE-3920 Adapt serverless e2e test to zone validation logic
  * CCE-3415 Support standard vk config in CreateClusterRequest
  * CCE-3920 [Serverless] Check if subnet is belong to zone available for bci creation in CreateClusterRequest
* [监控日志]
  * CCE-3907 告警提示监控链接
  * CCE-3907 格式化 hi 消息通知格式
  * CCE-3665 添加 cce-thanos 告警
  * CCE-3912 logRule 添加 bes_id 字段
  * CCE-3897 cce-alert-webhook 添加 hi 机器人告警
  * CCE-3905 thanos rule 添加 rule-reloader 容器
* [节点组]
  * CCE-3766 task controller 框架
  * CCE-3766 task controller instancegroup handler 实现
* [镜像仓库]
  * CCE-3867 [Improvement] 去除 cce-registry-secret
  * CCE-3867 [Improvement] meta cluster 替换成免密拉取
  * CCE-3867 image plugin yaml error
* [容器网络]
  * CCE-3868 更新 cni yaml
* [资源弹性]
  * CCE-3898: instance-gc-manager 设计和提测说明
  * CCE-3885: 回收竞价实例使用 cluster-service 接口
  * CCE-3885: BID Instance 被释放后才 Delete CCE Instance
  * CCE-3898: 修改 instance-polling 为 instance-gc-manager

## 2021-06-24

* [集群管理]
  * CCE-3959 [Bug] 修复 lgn2 报错
  * CCE-3439: 竞价实例落库, 支持前端实例类型展示
  * CCE-3766 通过deletionTimestamp提前判断instance phase
* [插件管理]
  * CCE-3956 支持cce-node-feature-discovery
  * CCE-3960 1.13 ip-masq-agent 部署 yaml 修复
* [节点组]
  * CCE-3782 node-controller 增加并发参数
  * CCE-3929 cluster-service 支持节点组 task api
  * CCE-3766 task controller instancegroup handler 实现
  * CCE-3931 db sync controller 开发，支持同步 task 信息到 db
  * CCE-3931 bugfix：task models 中需要json序列号存储的字段实现 sql.Value sql.Scan interface
  * CCE-3931 优化节点组 task 发单timeout处理逻辑，一旦出现发单timeout，后续重试不再发单，仅仅通过instance name查询虚机
* [行业云私有化]
  * CCE-3414 行业云3.0集测部署联调
  * CCE-3918 吉利产品运维平台对接v2集群
  * CCE-3949: 修复运维平台数据库数据不对问题
  * CCE-3935: 去除 NewClientByRegion 方法, 兼容私有化环境
* [Serverless]
  * CCE-3415 Add verbose log for token validation failure
  * CCE-3920 Block instance deletion when bci pod is Terminating
* [监控日志]
  * CCE-3957 kvass-coordinator 调整 max-shard
  * CCE-3958 调整容器监控 node proxy 为 node port

## 2021-06-29

* [集群管理]
  * CCE-3414 公有云支持CentOS 7.7，7.8镜像，补充回归case
  * CCE-3886 [Improvement] 移入节点新增 tag 无效 cluster-service
  * CCE-3929 cluster-serivce update instanceGroup 时默认同时 update db，避免ca从db读配置不是最新配置
  * CCE-3924: 新增 kubernetes.io/cce.instance.not-handler-by-cce 便于 kubectl annotate 快速加黑 Instance
* [nginx-ingress]
  * CCE-3972 修改nginx ingress service 命名生成规则
* [插件管理]
  * CCE-3956 支持cce-node-feature-discovery
  * CCE-3875:cloud node controller测试：增加label校验
* [节点组]
  * CCE-3983 修改task字段名称
  * CCE-3766 增加 task 创建时间精度
  * CCE-3983 节点组task e2e case bugfix
  * CCE-3983 节点组task e2e case 参数修改
  * CCE-3929 修改 task 获取接口 metircs 结构
  * CCE-3961 db-sync-controller 支持 instanceGroup
  * CCE-3766 task controller 相关组件与e2e test全地域开放
  * CCE-3766 使用string存创建时间，增加 task 创建时间精度到ns
  * CCE-3929 修改 t_cce_instancegroup_replicas_task 表sql
  * CCE-3929 cluster-service task 读取接口添加description字段 && 内部sdk
* [行业云私有化]
  * CCE-3414 移出代码region依赖
  * CCE-3414 行业云3.0集测部署联调
  * CCE-3414 私有化不判断用户类型，默认为非内部用户
* [EKS]
  * CCE-3772 update 内部上云接口人
  * CCE-3981: cce-gc-controller 全地域上线, 支持竞价实例退场
* [监控日志]
  * CCE-3970 定义 v2 logrule crd
  * CCE-3858 upate log rule 接口支持修改 BESID 字段
  * CCE-4004 alertmanager 添加 config-reloader 容器
