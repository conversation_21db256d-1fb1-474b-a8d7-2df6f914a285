# CCE 项目周报 - 2022-01-21 ~ 2022-01-28

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6004: [集群管理][集群升级] UpgradeKubelet 前支持 DrainNode
  * CCE-5886: [集群管理][集群升级] UpgradeNodeK8SVersion 采用特权容器升级 Kubelet
  * CCE-5886: [集群管理][集群升级] UpgradeNodeK8SVersion 采用特权容器升级 Kubelet
  * CCE-5932: [集群管理][集群升级] 根据 WorkflowType 生成 TaskGroupList 保证幂等
  * CCE-5932: [集群管理][集群升级] Cluster 升级前后更新 ClusterPhase 和 Handler, 避免 ClusterController 处理
  * CCE-5932: [集群管理][集群升级] UpgradeContainerziedMasterK8SVersion 实现容器化 Master K8SVersion 升级
  * CCE-5932: [集群管理][集群升级] 实现 UpdateMetaK8SVersion Task
  * CCE-5958: [集群管理][集群升级] 定义 TaskBaseConfig 简化 Workflow/Task 初始化方法
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 GetWorkflow 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 GetWorkflow 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 GetWorkflow 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 GetWorkflow 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 ListWorkflows 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 ListWorkflows 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 ListWorkflows 接口
  * CCE-5958: [集群管理][集群升级] cluster-service 新增 CreateWorkflow 接口

* 子超
  * 无进展

### 支持前置校验@启龙

* 启龙
  * 无进展

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * CCE-5456 [集群管理][支持异常重试] cluster 重试 ensure spec

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

* 陈欢
  * CCE-5932: [集群管理][节点组优化] 修复 ListInstanceByInstanceGroupID 分页问题
  * CCE-5932: [集群管理][节点组优化] 自动扩缩容 CA 组件增加 ctx
  * CCE-6004: [集群管理][节点组优化] InstanceGroup Reconcile 解绑 Tag 增加重试间隔

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 冀超
  * CCE-5832 [集群管理][体验优化] cce-cloud-node-controller 容忍 Network Unavailable Taint

### 节点组功能完善

* 冀超
  * CCE-5594 [集群管理][节点组功能完善] 增加节点组 RetryCount
  * CCE-5594 [集群管理][节点组功能完善] 增加节点组 RetryCount

### 流量接入优化

* 冀超
  * CCE-5832 [集群管理][流量接入优化] lb-controller 启动优化
  * CCE-5832 [集群管理][流量接入优化] lb-controller 启动优化

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践

* 雷若风
  * CCE-5098 [云原生 AI][最佳实践] 新增最佳实践

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * CCE-5822 [行业云私有化][AI私有化-百舸] console-hub 配置同步
  * CCE-5822 [行业云私有化][AI私有化-百舸] 20220125发版
  * CCE-5822 [行业云私有化][AI私有化-百舸] 镜像更新
  * CCE-5822 [行业云私有化][AI私有化-百舸] 镜像更新
  * CCE-5822 [行业云私有化][AI私有化-百舸] 镜像更新

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 企业版 CCR@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 功能完善

* 占伟
  * CCE-5978 [CCR][功能完善] CCR企业版chart版本算错chart制品数

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

* 亚奇
  * CCE-5948 [容器网络][VPC-CNI 支持 IP 池] 容器网络方案文档整理

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * 无进展

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

### 版本升级

* 亚奇
  * CCE-5970 [容器网络][版本升级] 更新 helm chart、镜像版本

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 可观测性

### cprom

* 子华
  * CCE-6002: [可观测性][cprom] 调整 monitr agent 状态更新逻辑
  * CCE-5880: [可观测性][cprom] monitir instance 支持默认创建服务网卡
  * CCE-6002: [可观测性][cprom] 同步 vm-operator CRD 定义
  * CCE-5979: [可观测性][cprom] monitor agent 状态同步
  * CCE-5979: [可观测性][cprom] sidecar 支持上报状态 & 获取采集任务
  * CCE-5957: [可观测性][cprom] cprom-manager 组件支持上报 agent 状态 & 下发采集任务
  * CCE-5955: [可观测性][cprom] ccev2 sdk 实现 ListClusters 接口
  * CCE-5880: [可观测性][cprom] cprom-service 获取关联 CCE 集群列表
  * CCE-5880: [可观测性][cprom] cprom-service 接入网络 API 接口开发

## 不规范提交

* author={chenyaqi01} commit={CCE-5948 Bump cni to v1.3.2}
* author={zhanghong15} commit={CCE-5971 modify wait time(+10s) of case modifyInstanceGroupLabelsAndTaints}
* author={chenyaqi01} commit={CCE-5948 重命名文档目录}
* author={yezichao} commit={CCE-5714 Implement containerized managed cluster creation}
* author={wangyufeng06} commit={CCE-5992 [Task] fix: Notify Rule 增删改查List api至可用}
* author={leiruofeng} commit={CCE-5874 AI Training Operator 插件更新}
* author={zhanghong15} commit={CCE-5971 add log info to case modifyInstanceGroupLabelsAndTaints}
* author={zhuangruiqing} commit={CCE-5971 增加节点组label判定等待时间}
* author={wangyufeng06} commit={CCE-5945 [Task] NotifyRule CRD API定义}
* author={wangzhuyun} commit={CCE-5976 gpushare-manage组件与gpu-manager同步}
* author={leiruofeng} commit={CCE-5973 [云原生 AI] 新定义 Terminating 状态}
* author={yezichao} commit={CCE-5714 Ensure master instance set for containerizedManaged cluster}
* author={leiruofeng} commit={CCE-5098 [云原生 AI] 更新 AI Training Operator resource}
* author={yezichao} commit={CCE-5466 WIP: upgrade k8s version metadata for cluster}
* author={yezichao} commit={CCE-5466 WIP: upgrade k8s version for master}
* author={chenhuan} commit={CCE-5883: 增加周报说明}
* author={duzhanwei} commit={CCE-5098 周会模版更新}
* author={chenhuan} commit={CCE-5883: CHANGELOG-2022-01-21.md}
* author={wenmanxiang} commit={Merge "CCE-5988 [Bug] [CCR] [功能完善] 修复chart和chart版本在有过来条件下总条数显示错误"}
* author={wenmanxiang} commit={CCE-5988 [Bug] [CCR] [功能完善] 修复chart和chart版本在有过来条件下总条数显示错误}
* author={wenmanxiang} commit={CCE-5975 [Bug] [CCR] [功能完善] 控制台访问控制用户docker login错误，修改为使用子用户name}
