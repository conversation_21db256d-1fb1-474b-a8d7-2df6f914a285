# CCE 项目周报 - 2022-02-18 ~ 2022-02-25

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6226: [集群管理][集群升级] Workflow 数据库同步工具 db-sync 实现
  * CCE-6257: [集群管理][集群升级] Workflow 设置数据库 gorm 注解
  * CCE-6247: [集群管理][集群升级] Task 增加 Name 字段, 便于前端进度展示, 修复单测
  * CCE-6247: [集群管理][集群升级] Task 增加 Name 字段, 便于前端进度展示
  * CCE-6088: [集群管理][集群升级] ListInstances API 增加 UpgradeNodesFields, 支持返回可升级 Nodes 列表
  * CCE-6090: [集群管理][集群升级] cce-sdk 增加 GetClusterEventSteps 等接口
  * CCE-6090: [集群管理][集群升级] 集群升级进度展示 API, TaskList 转 EventSteps
  * CCE-6090: [集群管理][集群升级] 集群升级中 Event 查询, 复用创建 Event 接口
  * CCE-6088: [集群管理][集群升级] ListInstances API 优化附加 K8SNode Info 实现
  * CCE-6096: [集群管理][集群升级] UpdateWorkflow API 实现, 支持 Pause/Resume/UpdateSpec 操作
  * CCE-6096: [集群管理][集群升级] UpdateWorkflow API 实现, 支持 Pause/Resume/UpdateSpec 操作
  * CCE-6093: [集群管理][集群升级] DeleteWorkflow API 实现, 支持 AccountID 和 ClusterID 鉴权
  * CCE-6094: [集群管理][集群升级] 创建 Workflow 支持同集群多个实例校验
  * CCE-6223: [集群管理][集群升级] Workflow API 文档
  * CCE-6094: [集群管理][集群升级] 创建 Workflow WatchDogConfig 设置为可选
  * CCE-6094: [集群管理][集群升级] 创建 Workflow WatchDogConfig 设置为可选
  * CCE-6011: [集群管理][集群升级] Nodes 升级完成后统一清理 cce-agent-pod
  * CCE-6037: [集群管理][集群升级] cce-agent-pod Image 信息通过配置传入

* 子超
  * 无进展

### 支持前置校验@启龙

* 启龙
  * CCE-6217 [集群管理][支持前置校验] check balance
  * CCE-6216 [集群管理][支持前置校验] check account auth
  * CCE-6111 [集群管理][支持前置校验] add clients endpoint in cluster service
  * CCE-6111 [集群管理][支持前置校验] check eip quota
  * CCE-6111 [集群管理][支持前置校验] 修改check方式
  * CCE-6111 [集群管理][支持前置校验] check blb quotas
  * CCE-6111 [集群管理][支持前置校验] check cce quotas
  * CCE-6111 [集群管理][支持前置校验] check bcc quotas

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * CCE-6244 [集群管理][节点组优化] 扩缩容 Task 新增以数量上限的删除策略

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 庭丽
  * CCE-6246 [集群管理][体验优化]2个回归集群使用logic bcc创建
  * CCE-6246 [集群管理][体验优化]指定回归machineSpec

* 陈欢
  * CCE-6246: [集群管理][体验优化] gztest 回归环境暂时关闭 3 个 cases
  * CCE-6245: [集群管理][体验优化] 创建 KubeConfig 支持自定义过期时间
  * CCE-6245: [集群管理][体验优化] 创建 KubeConfig 支持自定义过期时间
  * CCE-6242: [集群管理][体验优化] 创建 RBAC KubeConfig 增加对 AccountID 鉴权
  * CCE-6241: [集群管理][体验优化] 支持创建临时 KubeConfig 供客户提供问题排查
  * CCE-6241: [集群管理][体验优化] 支持创建临时 KubeConfig 供客户提供问题排查
  * CCE-6224: [集群管理][体验优化] instance-controller 删除 v1-to-v2 & prepay 节点, annotation 空指针问题修复
  * CCE-6224: [集群管理][体验优化] instance-controller 删除 v1-to-v2 预付费节点, annotation 空指针修复

### 故障自愈

* 苗永昌
  * CCE-6135 [集群管理][故障自愈]新增NPD插件

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * 无进展

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

* 子华
  * CCE-6222: [行业云私有化][AI私有化-百舸] master-service-discovery 组件配置更新

## CCR@占伟

### 功能完善@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6236 [容器网络][流量接入优化] 更新LB Controller yaml
  * CCE-6236 [容器网络][流量接入优化] LB Controller 支持 Service 设置 BLB 最大后端数
  * CCE-6236 [容器网络][流量接入优化] LB Controller 支持 Service 设置 BLB 最大后端数
  * CCE-6236 [容器网络][流量接入优化] LB Controller 支持 Service 设置 BLB 最大后端数

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

### 作业帮需求

* 亚奇
  * CCE-6109 [容器网络][作业帮需求] cleanup go.sum
  * CCE-6109 [容器网络][作业帮需求] 添加 node 粒度配置派生功能

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 可观测性

### cprom

* 子华
  * CCE-4976: [可观测性][cprom] 添加 agent 管理 api 文档
  * CCE-4976: [可观测性][cprom] instance、agent、scrapeJob、binding_cluster list 接口支持排序
  * CCE-4976: [可观测性][cprom] CCE 统一监控部署文档
  * CCE-4976: [可观测性][cprom] 支持前端传入 ReplicationFactor 配置
  * CCE-4976: [可观测性][cprom] 监控实例支持公网鉴权访问

## 不规范提交

* author={wangyufeng06} commit={Cloud-Prometheus-Product-67 [Task] 5-API接口文档补充（预聚合、告警、通知）}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-56 [Task] 通知策略Status 更新时间填充}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-55 [Task] 通知策略创建与instanceID解耦}
* author={leiruofeng} commit={CCE-5098 [云原生 AI] 进通过 trainer 判断是否设置容错}
* author={wenmanxiang} commit={CCE-6040 更新ccr-controller配置文件harbor-addon版本}
* author={wenmanxiang} commit={CCE-6040 [Improvement] [CCR] [功能完善] 优化project查询性能，采用从数据库获取总数}
