# CCE 2020-08 功能

## 2020-08-03

### 功能

* [集群管理]
  * InstanceFillSpec 改成并发模式, 提升已有实例速度 @陈欢
  * Cluster/Instance 在 meta-cluster 不存在, 删除时保证幂等 @陈欢
  * 删除 Instance 时, 去除对 Cluster 依赖, 避免 MetaCluster 中 Cluster 不存在时, 无法删除 Instance @陈欢;
    * 出现场景: 创建集群后, 在 InstanceCRD 提交前, 立马删除集群, 会导致 Cluster 不存在, 但Instance 残留.
  * kubelet进程配置补齐，避免加载动态配置之前创建的pod配置不对 @刘霖
  * kube-external-auditer 部署包单独从bos拉取，不再使用kubebin部署包中的二进制 @刘霖

### BugFix

* [集群管理]
  * 修复centos6 拉取部署包失败 @刘霖
  * 修复启动 kube-audit 进程操作不幂等问题 @刘霖
  * 修复 Kubelet 启动后, 动态配置未加载, 导致新启动 Pod 解析 ServiceName 失败问题 @刘霖
  * 增加由于 VPC 无 BCC 导致创建 BLB 失败等待间隔, 避免集群快速进去 CreateFailed 状态 @陈欢

## 2020-08-06

### 功能

* [集群管理]
  * GetCACert 兼容新旧集群 @李启龙
  * kubelet dynamic config 检查增加重试 @刘霖
  * 非BBC master，没有指定 etcd 数据目录时，使用第一块 CDS磁盘作为数据目录 @刘霖
  * cluster-controller/cluster-service 使用 ServiceAccount 代替 kubeconfig 挂载 @陈欢
  * 删除cluster时关联删除cluster内的InstanceGroup @潘思远
* [插件管理]
  * kube-proxy ipvs-modprobe镜像移至 CCR @刘霖

### BugFix

* [集群管理]
  * Instance/Cluster CRD UpdateStatus old 变更为深拷贝, 减少 update etcd 次数 @陈欢
  
## 2020-08-11

### 功能

* [集群管理兼容]
  * GetCACert 兼容新旧集群 @李启龙
* [集群管理]
  * e2e test 增加节点组扩容cases: 指定节点加入节点组、不指定节点扩容节点组 @潘思远
  * e2e test 增加节点组缩容cases: 缩容时指定节点移出节点组、缩容时指定节点移出节点组并删除节点、缩容时不指定节点 @潘思远

### BugFix

* [权限管理]
  * 获取子用户RBAC权限列表，过滤掉已经删除的集群 @张朋

### 数据库变更

t_cce_user_script

```mysql
CREATE TABLE `t_cce_user_script` (
      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'auto-increasing ID',
      `created_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT 'create time',
      `updated_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT 'update time',
      `deleted_at` timestamp NOT NULL DEFAULT '1971-01-01 00:00:01' COMMENT 'delete time',
      `script_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'user script ID',
      `script_name` varchar(128) NOT NULL DEFAULT '' COMMENT 'user script name',
      `user_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'user ID',
      `account_id` varchar(128) NOT NULL DEFAULT '' COMMENT 'account ID',
      `script_content` varchar(2000) NOT NULL DEFAULT '' COMMENT 'script content',
      `status` tinyint(11) NOT NULL DEFAULT '0' COMMENT 'current status',
      `addon` varchar(255) NOT NULL DEFAULT '' COMMENT 'addon',
      PRIMARY KEY (`id`),
      UNIQUE KEY `script_id_unique_key` (`script_id`),
      KEY `user_id_index` (`user_id`),
      KEY `account_id_index` (`account_id`)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8 comment ='cce user script table';
```

t_cce_instance

```mysql
/*确认不会在新加字段上进行条件检索*/ALTER TABLE t_cce_instance ADD COLUMN `deploy_custom_config` varchar(5000) NOT NULL DEFAULT '{}' COMMENT 'deploy_custom_config'; 
```

## 2020-08-13

### 功能

* [集群管理兼容]
  * cce-cluster-service GetCluster 兼容新旧集群 @李启龙@陈欢
  * cce-cluster-service GetKubeConfig 兼容新旧集群 @陈欢
* [集群管理]
  * 修复 kubeproxy IPv6 配置文件 bug @孙天元
  * 修复 kubelet IPv6 CNI 配置不为 kubenet 的 bug @孙天元
  * 修复 ip-masq-agent 未挂载 /lib/modules 导致无法加载模块的问题 @陈亚奇
  * 自动扩缩容 cluster-service 实现 @余宏伟
  * 删除节点组内instance，节点组自恢复 test case @潘思远
  * 新增部署前、部署后用户自定义脚本功能 @刘霖

### BugFix

* 日志调用深度bugfix，添加对应单测 @潘思远

## 2020-08-18

### 功能

* [集群管理]
  * V2 集群支持 AutoScaler;
* [插件管理]
  * 插件管理新增 AutoScaler 插件;
* [监控日志]
  * 修改 PromSQL 解决慢查询问题;
* [容器网络]
  * cni-suite: 增加 README 文档.

## 2020-08-20

### 功能

* [集群管理]
  * 扩容节点允许默认设为 cordon @刘霖;
  * 新增 serverless 集群类型支持 @子超;
  * 已有实例移入不重装, 增加 Machine Status 检查 @潘思远;
  * ManagedCluster InternalEIP 增加 source=cce 字段 @陈欢;
  * ClusterSync 增加 ClusterSpec/InstanceSpec 的 Database 和 MetaCluster 不一致扫描 @陈欢;
* [自动化测试]
  * 新增"自定义脚本"自动化测试 @刘霖;
* [监控日志]
  * 监控接口查询速度优化 @徐亚松;
* [权限管理] (暂未开放路由)
  * oidc-provider /keys 接口 @李启龙;
  * oidc-provider /.wellknown 接口 @李启龙;
  * oidc-provider /get_token 接口 @李启龙;
  * oidc-provider /refresh_token 接口 @李启龙;

### BugFix

* [集群管理]
  * GetInstances 方法增加 ClusterID 进行隔离, 并做参数校验 @陈欢;
  * ccetypes.BLB.Description 修改为 ccetypes.BLB.LBDescription, gorm 入库覆盖 ClusterSpec.Description @陈欢;


## 2020-08-25

### 功能

* [集群管理]
  * ClusterSync 全地域开启部署 @陈欢;
  * 支持 etcd secret KMS Provider 加密 @陈欢;
  * 允许自定义镜像(ImageType = Custom) @陈欢;
  * 集群部署支持开启 OIDC @余宏伟;
  * get kubeconfig 接口兼容 OIDC @余宏伟;
  * GPUType 兼容 BCC OpenAPI 和 LogicBCC @陈欢;
  * InstanceGroup 请求 CCE V2 换成 OpenAPI @思远;
  * 全地域后端开启 InstanceGroup 功能开关 @思远；
* [自动化测试]
  * cce-e2e-test 使用 CCE V2 OpenAPI Endpoint @陈欢;
* [运维运营]
  * cce-devops-backend 接入 cce-stack @徐亚松

### BugFix

* [集群管理]
  * ClusterSync 临时修复初始化 meta.Interface 并发写 map @陈欢;

## 2020-08-27

### 功能

* [集群管理]
  * V1 集群迁移至 V2, Cluster 对象迁移 @陈欢;
  * Sleep 随机秒, 临时解决创建 BCC LimiteRate 问题 @陈欢;
  * KMS plugin 开发以及部署文档 @刘霖;
  * 新版容器网段、ClusterIP网段推荐 @陈亚奇
  * get kubeconfig 接口支持 oidc
* [监控日志]
  * monitor-service-discovery 移入 cce-stack 库 @徐亚松

### BugFix

* [集群管理]
  * 检查 ClusterIP Range 最小数量为 8 @陈亚奇;
  * 增加bcc删除接口err判断，如果资源不存在则认为删除成功 @思远
  
* [自动化测试]
  * e2e test 删除节点组但保留节点的case参数问题修复 @思远
