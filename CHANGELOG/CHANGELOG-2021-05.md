# CCE 2021-05 功能

## 2021-05-07

### 功能

* [集群管理]
  * CCE-3364 修复Instance更新覆盖了部分没有落库的字段 @宏伟
  * CCE-3545【容器镜像】添加 helm plugin 配置 @启龙
  * CCE-3364 服务打印请求体 @宏伟
  * CCE-3528 ClourProvider Node Controller 代码框架 @冀超
  * CCE-3364 instance 添加竞价状态 @宏伟
  * CCE-3364 竞价实例退场轮询 @宏伟
  * CCE-3539 CA deployment 升级策略修改为 Recreate，避免旧pod阻塞新pod创建 @思远
  * CCE-3502: AutoScaler 测试 Deployment YAML 整理 @陈欢
* [边缘集群]
  * CCE-3453 bec 三线节点名称缩写从i改为l @思远
  * CCE-3453 云边集群 calico node 跳过节点IP重复检查 && 添加新线上bec节点IPPool、BGPPeer @思远
* [监控日志]
  * CCE-3551: 插件支持 nvidia-gpu-prometheus-exporter @陈欢
  * CCE-3542 CCE user cluster 监控规划 @子华
  *	CCE-3535 CCE 监控大盘添加异常调和事件监控 @子华
* [容器网络]
  * CCE-3557: 自动测试容器网络模式默认为: vpc-route-auto-detect @陈欢
* [BUG]
  * CCE-3550 修复GPU询价报错 @启龙

## 2021-05-11

### 功能

* [集群管理]
  * CCE-3363: 节点列表 bug 修复; @宏伟
  * CCE-3364: 添加根磁盘不足的标准错误; @宏伟
  * CCE-3371: Docker 版本更新至 20; @刘霖
  * CCE-3530: deployer 本地部署 local_exec 实现; @刘霖
* [EKS]
  * CCE-3363: 竞价实例轮询部署; @宏伟
  * CCE-3502: CA 自动化测试; @陈欢
  * CCE-3502: 数据库记录 CA 扩缩容记录; @陈欢
* [Serverless]
  * CCE-3566: Re-enable serverless cluster regression in gztest @子超
* [节点组]
  * CCE-3566: CA 流程增加各状态 Instance 数量; @思远
  * CCE-3564: 节点组默认清理策略从 Remain 改为 Delete; @思远
  * CCE-3558: 节点组不指定节点缩容时优先缩 CreateFailed，其次 Creating，最后 Running; @思远
* [插件管理]
  * CCE-3322: Nginx Ingress YAML; @冀超
  * CCE-3528: ClourProvider Node Controller 业务代码; @冀超
  * CCE-3571: Tolerate slow clients or request delay between clients and cce-gateway when last token expires. @子超
* [可观测性]
  * CCE-3407: Add bjdd BCI cn machines; @子超
  * CCE-3599: cce-log-agent 中文日志无法采集; @子华
  * CCE-3542: CCE cce-prometheus-tooklit 插件升级; @子华
  * CCE-3596: cce-monitor-toolkit 支持 helm 插件部署; @子华
* [私有化]
  * CCE-3494: 私有化自动化改造问题汇总; @刘霖
* [AI]
  * CCE-3494: 私有化自动化改造问题汇总; @刘霖
  * CCE-3594: tf-job 和 volcano-job YAML; @陈欢
  * CCE-3567 dcgm-exporter 插件化; @子华
* [自动化测试]
  * CCE-3566: GZTest Worker 安全组修改; @陈欢
  * CCE-3566: 自动化测试主力覆盖 1.18.9 集群; @陈欢

## 2021-05-13

### 功能

* [集群管理]
  * CCE-3530: TKE edgectl 添加节点脚本实现; @刘霖
  * CCE-3627: cluster-service 创建 instance crd 资源时设置 finalizer; @思远
* [插件管理]
  * CCE-3616: 插件安装顺序修改; @冀超
  * CCE-3478: 插件部署修复状态判断错误; @冀超
  * CCE-3322: Nginx Ingress 插件后端代码; @冀超
  * CCE-3478 插件部署过程增加插件参数编辑接口; @冀超
* [节点组]
  * CCE-3363: 修复节点组删除删除节点 orderID 为空的问题; @思远
* [可观测性]
  * CCE-3473: 事件推送 BCM 开关; @宏伟
  * CCE-3574: Pod GPU 资源监控面板; @子华
  * CCE-3575: 用户集群 GPU 监控面板; @子华
  * CCE-3576: kube-state-metrics rbac; @思远
* [AI]
  * CCE-3580: 节点显存共享操作; @刘霖
  * CCE-3600: 定义 AIJob.Interface; @陈欢
  * CCE-3577: volcano helm 插件部署支持; @子华
  * CCE-3600: CCE Webhook 新增 AI Validate; @陈欢
  * CCE-3631: [Task] 解决 pytorch 依赖冲突; @启龙
  * CCE-3588: dcgm-exporter helm 插件部署支持; @子华
  * CCE-3478: volcano aibox helm charts 补全; @子华
  * CCE-3600: 实现 TFJob GetJobVolcanoQueues 方法; @陈欢
* [EKS]
  * CCE-3626: e2e test case 支持 vpc-cni 集群 CA 扩缩竞价实例; @思远

## 2021-05-18

### 功能

* [集群管理]
  * CCE-3639: Tag 相关接口及单测; @冀超
  * CCE-3663: 托管 Master 安全组开放 ICMP 规则; @宏伟
  * CCE-3640: Dockerd 进程热重启、fs.inotify 配置优化; @刘霖
* [EKS]
  * CCE-3365: 竞价实例事件查询接口测试; @宏伟
  * CCE-3363: 节点列表返回竞价实例参数; @宏伟
  * CCE-3365: 竞价实例被动释放，优雅退场; @宏伟
* [AI]
  * CCE-3581: Queue 更新接口; @启龙
  * CCE-3628: 节点列表GPU资源展示; @刘霖
  * CCE-3581: 队列信息添加子用户信息; @启龙
  * CCE-3632: [Task] webhook helm plugin; @启龙
  * CCE-3581: [New Feature] AI Queue 管理后端; @启龙
  * CCE-3583: Add aijob list api; @子超
  * CCE-3583: Respect list option for aijob list api; @子超
  * CCE-3636: Pod cpu、memory、gpu metrics 接口获取 API; @子华
  * CCE-3636: 更新 Pod cpu、memory、gpu metric API 接口文档; @子华
  * CCE-3642: 更新 mxnet operator 的名字; @子华
  * CCE-3645: HPC cluster cadvisor 服务发现; @子华
  * CCE-3642: 添加 paddle operator & mxnet-operator; @子华
  * CCE-3659: 更新 dcgm-exporter metrics 数据采集方式; @子华
  * CCE-3643 GPU dcgm 监控插件集成到 cce-monitor-toolkit 中; @子华
* [插件管理]
  * CCE-3528: ClourProvider Node Controller Helm; @冀超
  * CCE-3528: ClourProvider Node Controller 单测; @冀超
  * CCE-3528: ClourProvider Node Controller 增加间隔事件参数; @冀超
  * CCE-3590 Cloud-Node-Controller 去除 CloudConfig 依赖; @冀超
* [节点组]
  * CCE-3634: 节点组内节点列表支持模糊搜索 && 支持显示cordon状态; @思远
  * CCE-3625: 创建中的已有 BCC 移入集群时通过bcc接口拿不到 tags，可以直接使用移入接口获取到的 tags; @思远
* [可观测性]
  * CCE-3643: 更新 cce-monitor-toolkit prometheus 配置; @子华
  * CCE-3637: AI Studio 旧 cce-thanos GPU 监控接口记录; @陈欢

## 2021-05-20

### 功能

* [EKS]
  * CCE-3503: ca e2e test 支持独立设置子网可用区; @思远
  * CCE-3503: ca e2e test 支持自定义触发deployment配置和节点组配置; @思远
* [CCE@BEC]
  * CCE-3658: 云边集群 service group crd update; @思远
  * CCE-3669: 云边集群cloud-tunnel端口和安全组冲突问题; @思远
* [AI]
  * CCE-3478: AI 容器插件名称修改; @冀超
  * CCE-3638 device 列表接口; @启龙
  * CCE-3638 队列接口路由、参数变更; @启龙
  * CCE-3638 队列capability接口参数调整; @启龙
  * CCE-3638 队列list接口 排序分页搜索&内存数值单位; @启龙
  * CCE-3583: Add ai job detail api; @子超
  * CCE-3672 Add aijob kinds api; @子超
  * CCE-3680 AIJob create/delete api; @子超
  * CCE-3583: Fix APPService letter case; @子超
  * CCE-3672 Add route for misc/aijobKinds; @子超
  * CCE-3583: Fix aijob pod label selector; @子超
  * CCE-3583: Fix app service endpoint uri; @子超
  * CCE-3680 Bugfix for AIJob create/delete api; @子超
  * CCE-3583: Adapt aijob list api to aibox plugin; @子超
  * CCE-3583: Add pod metrics fetch in app service client; @子超
  * CCE-3657 Support multiple keywords in chart list api; @子超
* [Serverless]
  * CCE-3620: Add scripts for replaying bci create request based on orderId; @子超
  * CCE-3572: Apply jitter to regular pod cache sync to avoid request converge; @子超
* [节点组]
  * CCE-3671: 修复获取节点组节点列表中cordon状态问题; @思远
  * CCE-3671: 节点组内节点列表中labels/taints使用实际k8s node的labels/taints; @思远
* [可观测性]
  * CCE-3670: kube-proxy 开启 metrics; @子华
  * CCE-3637: Debug 及 Docker pprof 文档; @陈欢
  * CCE-3649: ClusterHealthCheck 增加报警; @陈欢
  * CCE-3664: 大客户集群 cce-thanos 覆盖检查; @陈欢
  * CCE-3664: cce-status-alert 报错打印时间; @陈欢
  * CCE-3666: 请求 APIServer 异常加入 HealthCheck; @陈欢
  * CCE-3677: 支持用户集群采集控制平面组件的 metrics; @子华
  * CCE-3544: 更新 namespace-pod-detail grfana 面板; @子华
  * CCE-3664: 增加斗鱼/知乎/A/车和家/一点/晶泰集群 thanos 覆盖检查; @陈欢
  * CCE-3675: 增加 bci/cce-devops/healt-check/cce-monitor namespace pod 报警; @陈欢
  * CCE-3544: 添加用户集群 cluster-overview、namespace-pod-detail、prometheus 监控 grfana 面板; @子华
* [自动化测试]
  * CCE-3666: GZ online 回归变更安全组; @陈欢
  * CCE-3671 节点组缩容 e2e test case 中判断缩容完成使用 actualReplicas == expectedReplicas; @思远
* [整点深度]
  * CCE-3674: CCE 容器网络培训大纲; @陈欢

## 2021-05-26

* [集群管理]
  * CCE-3727: Webhook 限制 meta-cluster crds 删除; @陈欢
  * CCE-3724: meta-cluster kubeconfig 限制删除 CRD 权限; @陈欢
* [EKS]
  * CCE-3364 竞价状态竞价中状态及时更新; @宏伟
  * CCE-3365 随市场价出价竞价实例回归测试; @宏伟
* [节点组]
  * CCE-3719 节点组 instance node controller 全地域开放
* [CCE@BEC]
  * CCE-3691 节点列表模糊搜索支持bec节点实例组ID搜索; @思远
* [AI]
  * CCE-3581 check plugin; @启龙
  * CCE-3581 队列列表可选过滤; @启龙
  * CCE-3581 GPU资源增加displayName; @启龙
  * CCE-3694 queue operate state接口; @启龙
  * CCE-3581 修正 new queue service的error code; @启龙
  * CCE-3581 根据队列的子用户annotation 过滤队列列表; @启龙
  * CCE-3580 节点显存共享操作; @刘霖
  * CCE-3580 CCE GPU Manager 插件; @刘霖
  * CCE-3714 更新 cce-volcano 默认镜像; @子华
  * CCE-3726 ai job demo yaml & 文档完善; @子华
  * CCE-3689 monitor query range step 优化; @子华
  * CCE-3692 cce-volcano 支持传入 binpack 参数; @子华
  * CCE-3570: Volcano Job Demo; @陈欢
  * CCE-3713: AI Job With Queue 设定 Demo
  * CCE-3699: AI Queue Webhook 部署; @陈欢
  * CCE-3699: 增加 cce-gpu-manager README; @陈欢
  * CCE-3699: cce-gpu-manager 确定组件 YAML 结构及命名; @陈欢
  * CCE-3699: AI Queue User Webhook 支持 Update 校验; @陈欢
  * CCE-3699: cce-nvidia-mps Dockerfile 和 YAML; @陈欢
  * CCE-3699: cce-nvidia-node-exporter Dockfiler 和 YAML; @陈欢
  * CCE-3699: cce-nvidia-node-exporter gcc 8 Dockfiler 和 YAML; @陈欢
  * CCE-3699: AI Queue User Webhook 集成至 Volcano Helm Chart; @陈欢
  * CCE-3478 AI容器插件版本写入PluginConfig; @冀超
  * CCE-3697 Initialize cce-rdma-plugin; @子超
  * CCE-3583 Fix duplicate queue names; @子超
  * CCE-3697 Update README of cce-rdma-plugin; @子超
  * CCE-3697 Addon code for cce-rdma-plugin; @子超
  * CCE-3680 Expose k8s error to end user; @子超
  * CCE-3583 Remove redundant field in manifest; @子超
  * CCE-3583 Add podPhase in app service sdk; @子超
  * CCE-3653 Add MXJob implementation in aijob; @子超
  * CCE-3583 Fix TestUniqQueueNames test flake; @子超
  * CCE-3701 Adapt pod detail for unknown owner kinds; @子超
  * CCE-3583 Implement SetJobVolcanoQueue for kubeflow jobs; @子超
  * CCE-3583 Fetch full namespaces from app service to solve user permission; @子超
  * CCE-3583 Implement errorcode EssentialPluginNotFound and AIOperatorNotFound; @子超
  * CCE-3583 Copy object when generate manifest to avoid unexpected modification; @子超
  * CCE-3700 Return AIJobMissingCPUOrMemoryRequest error code if cpu/mem is not set; @子超
* [可观测试]
  * CCE-3725: node-exporter runAsUser=0; @陈欢
  * CCE-3685 user-cluster apiserver dashboard; @子华
  * CCE-3664: alert-webhook 增加 imagePullSecrets; @陈欢
  * CCE-3664: Master Endpoint ClusterID 转义为小写; @陈欢
  * CCE-3732: thanos-store 以 root 运行 runAsUser=0; @陈欢
  * CCE-3695 手动添加 user-cluster-master-endpoint 脚本; @子华
  * CCE-3664: 删除监控 endpoints 时, 确定 cluster 确实不存在; @陈欢
* [容器网络]
  * CCE-3322 Nginx Ingress Yaml修改; @冀超
  * CCE-3568 清理 RULE 和空闲 ENI; @亚奇
  * CCE-3646 Ingress-Controller 使用 tag; @冀超
  * CCE-3733 替换 ip-masq-agent 镜像为 cni 迭代版本; @亚奇
* [文档]
  * CCE-3715 新人文档完善; @占伟


  
