# CCE 项目周报 - 2022-02-04 ~ 2022-02-11

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6061: [集群管理][集群升级] Master 升级支持 root 密码传参
  * CCE-6049: [集群管理][集群升级] 目录结构调整 Task 区分 master 和 node 实现
  * CCE-6049: [集群管理][集群升级] 支持 Nodes 并行升级实现
  * CCE-6049: [集群管理][集群升级] 区分 Master & Nodes Workflow 升级参数
  * CCE-6047: [集群管理][集群升级] 独立集群容器化部署 Master 升级实现
  * CCE-6044: [集群管理][集群升级] 托管集群二进制部署 Master 升级实现
  * CCE-6044: [集群管理][集群升级] 独立集群二进制部署 Master 升级实现
  * CCE-6038: [集群管理][集群升级] CheckStatusAfterExecute 支持轮询及超时
  * CCE-6020: [集群管理][集群升级] KubeProxy 升级完成后检查 Pods Image 和状态
  * CCE-6020: [集群管理][集群升级] 支持 KubeProxy 升级补充单测
  * CCE-6020: [集群管理][集群升级] 支持 KubeProxy 升级
  * CCE-6032: [集群管理][集群升级] Kubelet 升级完成后 Node & Pod 状态检查
  * CCE-6029: [集群管理][集群升级] Workflow 及 WorkflowTaskGroup Phase 更新
  * CCE-6029: [集群管理][集群升级] Workflow 及 WorkflowTaskGroup Phase 更新
  * CCE-6017: [集群管理][集群升级] 容器化 Master 升级 NeedUpgrade 实现保证幂等
  * CCE-6017: [集群管理][集群升级] 根据 TargetK8SVersion 统一修改 deployer.Cluster 和 deployer.Instance 参数
  * CCE-6017: [集群管理][集群升级] Kubelet 升级完成后执行 uncordon
  * CCE-6017: [集群管理][集群升级] 特权容器升级 Kubelet

* 子超
  * CCE-6035 [集群管理][集群升级] 容器化 Master 升级前置检查 Master Node Ready

### 支持前置校验@启龙

* 启龙
  * 无进展

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * CCE-5832 [集群管理][节点组优化] 去除节点组Remain策略删除Tag步骤
  * CCE-5832 [集群管理][节点组优化] 去除节点组RetryCount和Delete策略删除Tag步骤
  * CCE-5832 [集群管理][节点组优化] 去除节点组RetryCount和Delete策略删除Tag步骤
  * CCE-5832 [集群管理][节点组优化] 去除节点组RetryCount和Delete策略删除Tag步骤
  * CCE-5832 [集群管理][节点组优化] 去除节点组RetryCount和Delete策略删除Tag步骤
  * CCE-5832 [集群管理][节点组优化] 优化集群删除中节点组删除逻辑
  * CCE-5832 [集群管理][节点组优化] 优化集群删除中节点组删除逻辑
  * CCE-5832 [集群管理][节点组优化] 优化集群删除中节点组删除逻辑

* 雷若风
  * CCE-5891 [集群管理][节点组优化] 节点移出节点组支持移除选项

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * CCE-5822 [行业云私有化][AI私有化-百舸] 插件镜像配置化,kube-state-metrics 改为 yaml 部署
  * CCE-5822 [行业云私有化][AI私有化-百舸] 插件镜像配置化,kube-state-metrics 改为 yaml 部署
  * CCE-5822 [行业云私有化][AI私有化-百舸] 插件镜像配置化,kube-state-metrics 改为 yaml 部署
  * CCE-5822 [行业云私有化][AI私有化-百舸] 插件镜像配置化

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * CCE-6041 [CCR][功能完善] ingress nginx的metric获取失败

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * 无进展

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 容器存储

### BOS CSI

* 子超
  * CCE-5897 [容器存储][BOS CSI] Bump bosfs version

## 运维运营

### 数据统计

* 陈欢
  * CCE-5370: [运维运营][数据统计] t_cce_vm_instance 增加 GPU_TYPE 和 GPU_COUNT 字段

## 不规范提交

* author={wangzhuyun} commit={CCE-5822 AI智算版-百舸 更新gpu-manager镜像}
* author={liqilong} commit={CCE-6018 [集群管理] [支持异常重试] 已有节点列表按照黑名单过滤机型}
* author={zhanghong15} commit={CCE-5971 modify wait time(6min) of case modifyInstanceGroupLabelsAndTaints}
* author={chenhuan} commit={[集群管理][集群升级] CCE-6014: K8S Node 升级前对比 KubeletVersion 实现幂等}
