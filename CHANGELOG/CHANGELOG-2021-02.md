# CCE 2021-02 功能

## 2021-02-02

### 功能

* [集群管理]
  * CCE-3027: 安全组 SDK 实现;@宏伟
  * CCE-3027: 安全组优化代码结构;@宏伟
  * CCE-3095: 更新 Cluster/Instance/InstanceGroup CRD;@启龙
  * CCE-3109: 如果instance对应的machine没创建出来，删除时跳过解绑tag;@思远
* [裸金属集群]
  * CCE-3135: 去除'限制删除BBC'逻辑;@启龙
  * CCE-3095: cluster-service fillspec 支持新建 BBC;@启龙
* [自动化测试]
  * CCE-3069: 增加 chaos 测试集群;@庭丽
* [监控日志]
  * CCE-3110: healtcheck cronjob 参数修改;@陈欢
* [Serverless]
  * CCE-3119: Migrate external etcd cluster to ssd machines in bj;@子超
* [公私架构统一]
  * CCE-3111: 管理 cce-nginx config;@刘霖
  * CCE-3111: 统一 K8S 插件 Namespace;@刘霖
  * cce-3118: 私有化环境不区分内、外部用户;@启龙
  * CCE-3111: cce-stack JUJU Charm 渲染;@刘霖
  * CCE-3027: 私有化 post eip price 接口注册;@宏伟
  * CCE-3138: drill 环境 ccev2 endpoint 配置;@思远
  * CCE-3118: 私有化环境禁用 APIServer WebHook;@陈欢
  * CCE-3027: 私有化 put security group 接口注册;@宏伟
  * CCE-3138: cluster-scaler 支持 ImageID 配置注入;@思远
  * CCE-3111: 私有化环境支持 docker 配置 insecure registry;@刘霖
  * CCE-3111: 新增 cce-stack-private 组件, 用于镜像/BOS等同步;@刘霖
  * CCE-2997: monitor-service 支持 prometheus ImageID 配置注入;@陈欢
  * CCE-3111: kube-proxy 镜像迁移至 CCR cce-plugin-pro namespace;@刘霖
* [镜像仓库]
  * CCE-3100 Add admin task for setting registry to readonly;@子超
  
    
## 2021-02-23
 
### 功能

* [私有化]
  * CCE-3114: 私有化注册post rule方法 @余宏伟
  * CCE-3111: juju charm 渲染 ©刘霖 
* [监控日志]
  * CCE-3134: 重构 cce-health-check ©陈欢
  * CCE-3137: monitor-service去除无用配置 ©陈欢
  * CCE-3111: k8s组件日志等级调整为2 @刘霖
  * CCE-1381: 日志采集输入源强制编码成UTF-8 @张朋
  * CCE-3137: monitor-service cds-flex-volume imagelD 使用配置 ©陈欢
  * CCE-3137: monitor-service node-exporter imagelD ©陈欢
  * CCE-3137: monitor-service kube-metric-state imagelD 使用配置 ©陈欢 
* [容器网络]
  * CCE-3144: VPC-CNI支持私有化Endpoint @陈亚奇
  * CCE-3095: 私有化ENI、辅助IP接口注册 ©陈亚奇
  * CCE-3144: fix ip-masq-agent image ©陈亚奇
* [集群管理]
  * CCE-3162: BBC询价接口 @李启龙
  * CCE-3163: BBC flavor镜像列表 ©李启龙
  * CCE-3114: 节点列表返回封锁状态 @余宏伟
  * CCE-3163: BBC Diskinfo @李启龙
  * CCE-3070: Implement bee signer based on cce-gateway token ©叶子超
  * CCE-3152: CCM 参数可配置 ©冀超
  * CCE-3114: 实名认证区分初级和高级 @余宏伟
  * CCE-3095: 安全组问题修复 @余宏伟 
  * CCE-3095: 私有化pst ENI接口注册 @余宏伟
  * CCE-3140: ca 部署改为 hostNetwork ©潘思远
  * CCE-3027: master安全组provider实现 @余宏伟
  * CCE-3105: kubelet统计memory使用脚本 ©陈欢
  * CCE-2967: 修复osp页面重定向导致CCE收入获取失败的问题 @余宏伟
  * CCE-3095: 私有化注册delete rule接口 @余宏伟
  * CCE-3095: bbc测试 @李启龙
  * CCE-3080: Add max length check for each instance label or taint @叶子超
  * CCE-3061: Fix chartmuseum endpoint in gzns-drill for helm service ©叶子超
  * CCE-3094: cordon/uncordon nodes @余宏伟
  * CCE-3143: BLB配额前置检查 @余宏伟
  * CCE-3095: bbc sdk & bbc provider @李启龙
  * CCE-3095:: bbc开关 @李启龙
* [流量接入]
  * CCE-3086: Ingress Event与注释 ©冀超 
  * CCE-3152: Ingress 私有化参数可配置 ©翼超
  * CCE-3086: Ingress Finalizer ©翼超
* [应用管理]
  * CCE-3157: Ingress 查询返回 DeletionTimestamp ©翼超
  * CCE-3137: AppService Userinfo 增加日志 ©陈欢
  * CCE-3137: app-service 新增 ingress list/get 日志 @陈欢 
* [回归测试]
  * CCE-3114: 回归测试适配节点列表逻辑变更 @余宏伟
* [文档]
  * CCE-3013: 补充tag包readme说明 ©潘思远
  * CCE-3131: drill环境csi验证问题记录 ©潘思远 
* [Serverless]
  * CCE-3150: serverless 集群master 镜像切换到registry.baidubce.com @潘思远
    
    
## 2021-02-25
 
### 功能
* [私有化]
  * CCE-3095 添加私有化 region: cnjd @余宏伟
  * CCE-3095 cnjd api-gateway iaas 接口配置 @余宏伟
  * CCE-3095 cnjd api-gateway iaas 接口注册 @余宏伟
  * CCE-3111 私有化改造 @刘霖
  * CCE-3095 cnjd 白名单 @余宏伟

 
* [集群管理]
  * CCE-3095 安全组联调问题修复 @余宏伟
  * CCE-3095 移入节点支持新版安全组逻辑 @余宏伟
  * CCE-2374 Read cce-plugin-token from secret directly if token volume is not found @叶子超
  * CCE-3163 BBC 安全组ID @李启龙
  * CCE-3170 BBC、BCC 获取镜像详情逻辑拆分 @李启龙
  * CCE-3170 delete bbc provider @李启龙
  
* [流量接入]
  * CCE-3172 service nil annotation bug fix @冀超