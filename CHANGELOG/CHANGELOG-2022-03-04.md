# CCE 项目周报 - 2022-02-25 ~ 2022-03-04

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6362: [集群管理][集群升级] 增加 workflow count metrics 指标
  * CCE-6362: [集群管理][集群升级] 增加 workflow count metrics 指标
  * CCE-6337: [集群管理][集群升级] cce-workflow-controller 接入 CI/CD
  * CCE-6117: [集群管理][集群升级] 升级 Master/Proxy 去除 FromK8SVersion 参数
  * CCE-6117: [集群管理][集群升级] 升级 Nodes 去除 FromK8SVersion 参数
  * CCE-6097: [集群管理][集群升级] Cluster 设置 label kubernetes.io/cce.cluster.nodes-can-be-upgraded, 支持所有 Node 可选升级
  * CCE-6330: [集群管理][集群升级] Workflow 支持按数量及保留时间自动清理
  * CCE-6314: [集群管理][集群升级] 删除 Workflow 重置 Cluster 状态
  * CCE-6296: [集群管理][集群升级] 集群升级 e2etest 修改容器网段
  * CCE-6296: [集群管理][集群升级] 集群升级 e2etest 增加 1.18 至 1.20 case
  * CCE-6296: [集群管理][集群升级] 升级容器化 Master 集群 e2etest
  * CCE-6309: [集群管理][集群升级] Workflow API 文档
  * CCE-6309: [集群管理][集群升级] API 返回 Master/Node 可升级 K8S 版本
  * CCE-6299: [集群管理][集群升级] 升级容器化 Master 后置检查
  * CCE-6295: [集群管理][集群升级] 升级 Node 后置检查
  * CCE-6295: [集群管理][集群升级] 升级 Node 后置检查
  * CCE-6226: [集群管理][集群升级] cce-db-sync-controller RBAC 格式错误 Fix
  * CCE-6226: [集群管理][集群升级] 实现 models.Interface Workflow 相关方法
  * CCE-6226: [集群管理][集群升级] 实现 models.Interface Workflow 相关方法

* 子超
  * 无进展

### 支持前置校验@启龙

* 启龙
  * CCE-6291 [集群管理][支持前置校验] 接口设计&开发
  * CCE-6300 [集群管理][支持前置校验] add precheck config
  * CCE-6300 [集群管理][支持前置校验] 集群最大节点数、余量，节点最大pod数
  * CCE-6260 [New Feature] [集群管理][支持前置校验] 检查子网ip余量

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 陈欢
  * CCE-6339: [集群管理][体验优化] 统一 Master 升级类型为 UpgradeMasterK8SVersion, API 不区分托管/独立等
  * CCE-6339: [集群管理][体验优化] 统一 Master 升级类型为 UpgradeMasterK8SVersion, API 不区分托管/独立等
  * CCE-6338: [集群管理][体验优化] WatchDog 暂停 Workflow 增加 Reason 字段
  * CCE-6296: [集群管理][体验优化] workflow 自动化回归
  * CCE-6296: [集群管理][体验优化] workflow 自动化回归
  * CCE-6316: [集群管理][体验优化] e2etest 支持串并行组合

### Serverless

* 子超
  * CCE-6317 [集群管理][Serverless] Query bci instance with accountID

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * CCE-6228 [云原生 AI][数据集管理] 数据集接口联调 fix
  * CCE-6228 [云原生 AI][数据集管理] 数据集接口实现

* 占伟
  * CCE-6228 [云原生 AI][数据集管理] 数据集方法名字fix

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * CCE-5822 [行业云私有化][AI私有化-百舸] 修改单侧
  * CCE-5822 [行业云私有化][AI私有化-百舸] 修改 gpu 判断逻辑
  * CCE-5822 [行业云私有化][AI私有化-百舸] 修改 gpu 判断逻辑
  * CCE-5822 [行业云私有化][AI私有化-百舸] 私有化统一监控配置
  * CCE-5822 [行业云私有化][AI私有化-百舸] 更新bbc openapi 配置、镜像

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * CCE-6356 [CCR][功能完善] 鉴权失败

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

* 郭威
  * CCE-6072 [容器网络][VPC-CNI 支持 IP 池] ，新增terway源码地址

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6335 [容器网络][流量接入优化] LB Controller 定向开源: 支持 NodeIP+NodePort 作为后端
  * CCE-6333 [容器网络][流量接入优化] LB Controller 定向开源: 支持Service Annotation配置 VPC ID
  * CCE-6333 [容器网络][流量接入优化] LB Controller 定向开源: 支持Service Annotation配置 VPC ID
  * CCE-6332 [容器网络][流量接入优化] LB Controller 定向开源: 收缩依赖库
  * CCE-6313 [容器网络][流量接入优化] 修复 CCM v2 默认子网选择问题
  * CCE-6312 [容器网络][流量接入优化] 修复lb/ingress controller Tag Client 的 Proxy 缺失问题
  * CCE-6209 [容器网络][流量接入优化] LB Controller 兼容普通型 BLB

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

* 雷若风
  * CCE-6165 [容器网络][支持 RDMA Roce 容器网络] Neutron API Port 相关接口封装

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

### 插件升级

* 亚奇
  * CCE-6109 [容器网络][插件升级] Bump cni to v1.3.3

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## CCE

### 回归测试

* 庄瑞卿
  * CCE-6315 [CCE][回归测试]虚机创建对齐machinespec和instancetype

## 不规范提交

* author={sunlei19} commit={CCE-6365 [Task] upgrade hybrid plugin 0.1.1}
* author={chenhuan} commit={CCE-5883: CCE 研发能效建议}
* author={chenhuan} commit={CCE-5883: CCE 研发能效建议}
* author={chenhuan} commit={CCE-5883: CCE 研发能效建议}
* author={liqilong} commit={CCE-6340 [New Feature] 调整logic bcc 的参数校验}
* author={duzhanwei} commit={Cloud-Prometheus-Product-12 [Story] Targets 信息}
* author={wangzhuyun} commit={CCE-5822 AI智算版-百舸 升级pytorch-operator}
* author={chenhuan} commit={CCE-5883: CHANGELOG-2022-02-25.md}
* author={wenmanxiang} commit={CCE-5878 [Improvement] 认证token cache 优化，采用LRU算法}
* author={chenyaqi01} commit={CCE-6269 修复路由控制器特殊情况误删路由}
* author={chenyaqi01} commit={CCE-6269 Bump cni to v1.3.3}
* author={chenyaqi01} commit={CCE-6269 fix ut}
* author={chenyaqi01} commit={CCE-6269 修复路由控制器特殊情况误删路由}
