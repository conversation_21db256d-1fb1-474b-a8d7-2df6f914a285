# CCE 项目周报 - 2022-01-14 ~ 2022-01-21

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-5883: [集群管理][集群升级] 修改 README.md

* 子超
  * 无进展

### 支持前置校验@启龙

* 启龙
  * 无进展

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * CCE-5456 [集群管理][支持异常重试] cluster 重试(service 部分)

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

* 陈欢
  * CCE-5932: [集群管理][节点组优化] AutoScaler 更新 RBAC 支持 storage.k8s.io
  * CCE-5932: [集群管理][节点组优化] 自动扩缩容编译优化去除 vendor

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

### 流量接入优化

* 冀超
  * CCE-5594 [集群管理][流量接入优化] 修改LB Controller 版本号

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * 无进展

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 企业版 CCR@占伟

* 占伟
  * CCE-5924 [CCR][企业版 CCR] IAM鉴权接口返回格式不对
  * CCE-5917 [CCR][企业版 CCR] 修复用户未登录，拉取公共镜像错误

* 温满祥
  * CCE-5917 [Bug] [CCR][企业版 CCR] 修复用户未登录，拉取公共镜像错误

* 牛康龙
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * 无进展

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

* 亚奇
  * CCE-5942: [容器网络][体验优化] 路由超出 quota 的 node 禁止调度

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

### 工程效率

* 陈欢
  * CCE-5883: [用户体验][工程效率] cce-weekly-report 运行成功

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## CCE 文档

### 值班文档

* 霖皇
  * CCE-5895 [CCE 文档][值班文档] 值班文档更新
  * CCE-5895 [CCE 文档][值班文档] 值班文档更新

## 可观测性

### cprom

* 子华
  * CCE-5880: [可观测性][cprom] cprom-service 上线 scrape job api 接口
  * CCE-5880: [可观测性][cprom] cprom-service scrapeJob 管理 API
  * CCE-5880: [可观测性][cprom] cprom-service 重构复用 middlew 代码
  * CCE-5880: [可观测性][cprom] cprom-service api 接口路由调整
  * CCE-5880: [可观测性][cprom] cprom-service gztest 上线
  * CCE-5880: [可观测性][cprom] cprom-service 在用户集群部署 agent
  * CCE-5880: [可观测性][cprom] cprom-service 调用 cce sdk 获取用户集群 kubeconfig
  * CCE-5880: [可观测性][cprom] 创建 monitorAgent 写入 cncnetwork 的状态
  * CCE-5880: [可观测性][cprom] 创建 monitorAgent 时 patch cncnetwork

## 不规范提交

* author={wenmanxiang} commit={CCE-5883: CCE Weekly Report - 增加Terraform项目持续迭代和可观测性}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 增加体验优化}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 增加体验优化}
* author={leiruofeng} commit={CCE-5874 [云原生 AI] AI Training Operator 插件更新}
* author={helonghua} commit={CCE-5098 周会模版更新}
* author={leiruofeng} commit={CCE-5911 [云原生 AI] Fix: set priority for aitrainingjob}
* author={liulin15} commit={CCE-5905 [集群管理] gpu支持镜像放开}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 自动提交 CHANGELOG 到 icode 仓库}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 自动提交 CHANGELOG 到 icode 仓库}
* author={wangzhuyun} commit={CCE-5098 周会模版更新}
* author={wangyufeng06} commit={CCE-5916 [Task] 熟悉Cprom项目&更新README.md}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 增加 BCI Console 代码}
* author={liulin15} commit={CCE-5905 [集群管理] gpu支持镜像放开}
* author={leiruofeng} commit={CCE-5910 Add `scaling` status for AI job}
* author={jichao04} commit={CCE-5899 周会模板更新}
* author={chenhuan} commit={CCE-5899: CCE 运营平台文档说明}
* author={chenhuan} commit={CCE-5899: CCE 运营平台文档说明}
* author={chenhuan} commit={CCE-5899: CCE 运营平台文档说明}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 增加非既定项目展示}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 增加非既定项目展示}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report - 增加非既定项目展示}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report}
* author={yuanzihua} commit={CCE-5880 monitorAgent 调和}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report 单测修复}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report}
* author={chenhuan} commit={CCE-5883: CCE Weekly Report}
* author={chenhuan} commit={CCE Weekly Report}
* author={liulin15} commit={CCE-5895 值班文档更新}
* author={chenhuan} commit={CCE-5761: Workflow TaskList 按阶段分类展示}
* author={chenhuan} commit={CCE-5761: TaskList 按阶段分类展示}
* author={chenhuan} commit={CCE-5821: kubelet 采用特权容器方式升级验证}
* author={chenhuan} commit={CCE-5821: 特权容器 kubelet 升级验证}
* author={yuanzihua} commit={CCE-5842 monitor agent 管理 API}
* author={leiruofeng} commit={CCE-5850 Fix: create aijob with EnableOversell/EnableFaultTolerance options}
* author={duzhanwei} commit={CCE-5915 [Task] CCR企业版 增加distribution Docker编译}
