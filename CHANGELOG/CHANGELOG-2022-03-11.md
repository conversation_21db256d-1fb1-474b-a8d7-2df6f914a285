# CCE 项目周报 - 2022-03-04 ~ 2022-03-11

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6467: [集群管理][集群升级] 优化 TaskTotalCount 实现方式
  * CCE-6444: [集群管理][集群升级] CCE K8S 版本 Changelog
  * CCE-6449: [集群管理][集群升级] WatchDog 健康检查增加重试, 避免偶发异常
  * CCE-6449: [集群管理][集群升级] WatchDog 健康检查增加重试, 避免偶发异常
  * CCE-6444: [集群管理][集群升级] CCE K8S 版本 Changelog
  * CCE-6398: [集群管理][集群升级] change log 整理
  * CCE-6434: [集群管理][集群升级] Webhook 支持 allow-multiple-within-cluster Label
  * CCE-6434: [集群管理][集群升级] Webhook 支持 allow-multiple-within-cluster Label
  * CCE-6421: [集群管理][集群升级] 增加 WatchDogStatus 展示 UnhealthyPod 详情
  * CCE-6421: [集群管理][集群升级] 增加 WatchDogStatus 展示 UnhealthyPod 详情
  * CCE-6415: [集群管理][集群升级] WorkflowStatus 增加 TaskCount 反映进度
  * CCE-6398: [集群管理][集群升级] CreateWorkflow API 文档补充具体例子
  * CCE-6403: [集群管理][集群升级] 增加 Task 粒度 ErrorMessage
  * CCE-6398: [集群管理][集群升级] 新增 Webhook 策略禁止 Cluster 存在多个非 Succeeded Workflow
  * CCE-6399: [集群管理][集群升级] 创建 Workflow 添加公共 Labels
  * CCE-6400: [集群管理][集群升级] ListCluster 接口支持 WorkflowID 参数, 便于前端跳转
  * CCE-6337: [集群管理][集群升级] workflow-controller GZTest 环境上线
  * CCE-6337: [集群管理][集群升级] WorkflowCRD 接入 CI/CD

* 子超
  * CCE-6035 [集群管理][集群升级] Reduce arg size for new goroutine
  * CCE-6035 [集群管理][集群升级] Add workflow UpgradeMasterK8SVersionPreCheckConfig

### 支持前置校验@启龙

* 启龙
  * CCE-6291 [集群管理][支持前置校验]  接口设计&开发
  * CCE-6261 [集群管理][支持前置校验] workflow deployment update strategy
  * CCE-6261 [集群管理][支持前置校验] task 改为并发执行

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 陈欢
  * CCE-6383: [集群管理][体验优化] 临时 Kubeconfig 复用调用方 User 的 Role/ClusterRole
  * CCE-6383: [集群管理][体验优化] 临时 Kubeconfig 复用调用方 User 的 Role/ClusterRole

### BBC OPEN API

* 启龙
  * CCE-5750 [集群管理][BBC OPEN API] open api 新建裸金属-自测

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * CCE-5822 [行业云私有化][AI私有化-百舸] 日志管理兼容私有化 region
  * CCE-5822 [行业云私有化][AI私有化-百舸] 日志管理兼容私有化 region

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

* 亚奇
  * CCE-6406 [容器网络][VPC-CNI 支持 IP 池] IP冷却时间可配置
  * CCE-6406 [容器网络][VPC-CNI 支持 IP 池] IPAM 高于水位释放 IP 否则仅标记未分配
  * CCE-6406 [容器网络][VPC-CNI 支持 IP 池] 添加水位参数和校验
  * CCE-6072 [容器网络][VPC-CNI 支持 IP 池] 添加单测
  * CCE-6072 [容器网络][VPC-CNI 支持 IP 池] 添加单测

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6445 [容器网络][流量接入优化] LB Controller 修复 NodePort 变更不生效问题
  * CCE-6407 [容器网络][流量接入优化] LB Controller 定向开源: HelmChart与实用文档
  * CCE-6431 [容器网络][流量接入优化] LB Controller 定向开源: 修改单测
  * CCE-6407 [容器网络][流量接入优化] LB Controller 定向开源: HelmChart与实用文档
  * CCE-6332 [容器网络][流量接入优化] LB Controller 定向开源: 使用开源 BCE SDK 库

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 可观测性

### cprom

* 子华
  * CCE-4976: [可观测性][cprom] 调整 controller default result 充实时间为 5s
  * CCE-4976: [可观测性][cprom] ingress-nginx 指定 blb name 为 instance name
  * CCE-4976: [可观测性][cprom] instance controller 支持删除关联的 monitor agent
  * CCE-4976: [可观测性][cprom] 添加采集任务管理 api 文档

## 不规范提交

* author={duzhanwei} commit={Cloud-Prometheus-Product-120 [Task] 创建alerthook的部署yaml}
* author={yezichao} commit={[集群管理][集群升级] CCE-6390 Implement managed master upgrade precheck}
* author={zhanghong15} commit={CCE-6296 add CheckClusterPods}
* author={zhuguodong} commit={IME-ECS-398 适配BEC三线节点，给节点上到容器的路由加上src信息}
* author={yezichao} commit={[集群管理][集群升级] CCE-6035 Support multiple workflows within one cluster in white list style}
* author={liqilong} commit={CCE-6045 [Improvement] 【运营数据】 gpu_type 、 gpu_count}
* author={leiruofeng} commit={cloudnativeAI-68 [云原生 AI] Cloud Node Controller 插件参数更新}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-114 [Task] alert consumer fix}
* author={duzhanwei} commit={Cloud-Prometheus-Product-108 [Bug] 后端支持版本为base-v1,dev-v1,前端传入为advance-v1,base-v2,advance-v2不支持}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-113 [Task] vmrules labels格式更新}
* author={yezichao} commit={[容器存储][PFS CSI] CCE-6390 Update README}
* author={yezichao} commit={[容器存储][PFS CSI] CCE-6390 Update clean-recycle container image to 6.6.3b}
* author={yezichao} commit={[容器存储][PFS CSI] CCE-6390 Remove yr related fields in pfs csi chart values}
* author={yezichao} commit={[容器存储][PFS CSI] CCE-6390 Upgrade pfs csi plugin and chart version}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-111 [Task] alert-template configmap更新}
* author={leiruofeng} commit={cloudnativeAI-68 [云原生 AI] Cloud Node Controller 插件模板更新}
* author={leiruofeng} commit={cloudnativeAI-68 [云原生 AI] 为新增节点添加 SwitchID Label}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-35 [Task] 【后端】消费Redis告警消息队列}
* author={wangzhuyun} commit={CCE-5822 AI智算版-百舸 修改dcgm affinity}
* author={chenhuan} commit={CCE-5883: CHANGELOG-2022-03-04.md}
* author={wenmanxiang} commit={Merge changes I04c495f9,I77381088,I03ff760e}
