# CCE 2020-09 功能

## 2020-09-01

### 功能

* [集群管理]
  * ClientSets 初始化使用单例模式 @陈欢;
  * 添加一次性推荐容器网络参数接口 @陈亚奇;
  * V1 迁移至 V2 完成: Cluster/KubeConfig/CaCert @陈欢;
  * 提供 GetClusterOfBBE 接口, 提供 AdminCA 证书 @陈欢;
  * BaseController 增加 clusterID 是否属于 Account 校验, 防止越权 @陈欢;
  * cce-stack 定义 pkg/clientset, 统一三方 Config/Client/Factory 初始化及调用 @陈欢;

### BugFix

* [权限审计]
  * OIDC 集群暂时跳过 RBAC 检查 @余宏伟;
  * 修复 OIDC GetKubeConfig 失败问题 @陈欢;
  * app-service 使用 OIDC kubeconfig @余宏伟;

## 2020-09-03

### 功能

* [集群管理]
  * 部署流程增加虚机时间同步 @刘霖;
  * 集群管理后端去除 BCC 机型限制 @思远;
  * V1 迁移至 V2: Instance 完成 @陈欢;
  * CCE 支持 GPUSharing 类型集群 @张朋;
  * pause 容器镜像版本与 k8s 版本对齐, 升级到 pause 3.1 @刘霖;
* [容器网络]
  * 修复 ipam 并发分配大量 IP 失败的问题 @亚奇;
* [运维运营]
  * 增加 V2 集群计费统计 @启龙;
* [自动化回归]
  * 增加 monitor-service SDK @庭丽;
* [监控日志]
  * 托管 master 统一监控 Shard 逻辑 @亚松;
* [Serverless K8S]
  * Serverless Cluster 删除时释放 CoreDNS @思远;
  * Serverless Cluster Master 使用独立安全组 @叶子超;

### BugFix

* [集群管理]
  * 修复 patch node config 失败问题 @刘霖;
* [监控日志]
  * monitor-service overview 为空返回 从 nil 替换成 [] @陈欢;
* [容器网络]
  * ip-masq-agent 增加托管 Master ************/24 保留子网的 nonMasquerade @亚松.
* [Serverless K8S]
  * Serverless Cluster 修复 vk 使用 kube-proxy 的 kubeconfig 和 apiserver 生成 kube-proxy 的 kubeconfig 竞争关系 @思远;

## 2020-09-09

### 功能

* [集群管理]
  * K8S Node 支持选择 IP or Hostname @刘霖;
  * 支持 BBE Cluster 查询, 返回 Node FloatingIP @宏伟;
  * InstanceSpec MachineOption 替换成 InstanceOption @子超;
  * cluster-controller MaxRetryCount 调大至 20, 提前预警 @亚松;
  * 托管集群删除 ClusterBLB, 增加使用 UUID, 兼容 V1 托管集群 @陈欢;
  * helm-service 统一 ClusterID 校验至 utils.ValidClusterID @子超;
  * V1 迁移 V2 增加 handler=handler-v1-to-v2, controller 仅进行 reconcileDelete 操作 @陈欢;
* [应用管理]
  * Webshell OIDC 认证集群跳过 RBAC 检查 @冀超;
* [运维运营]
  * 集群操作失败报警, 增加用户信息 @亚松;
* [自动化测试]
  * 新增 WaitForResourceReady @李丽丽;
  * 新增 hostname 自动化测试 case @刘霖;
  * 新增 AutoScaler 自动化测试 case @李丽丽;
  * 新增 LoadBalancer Service 自动化测试 case @李丽丽;

### BugFix

* [集群管理]
  * 修复 instance name 生成方式 @刘霖;
  * 修复 retry count 到达上限后无法删除 instance 问题 @刘霖;
  
## 2020-09-15

百度世界大会封网, 推迟到 2020-09-16 发布.

## 2020-09-16

### 功能

* [集群管理]
  * 修改托管 Master 默认规格配置 @宏伟;
  * serverless cluster 支持多可用区/子网 @子超;
  * 增加 Serverless Cluster 跳过删除 ETCD Annotation @子超;
  * Instance 增加 Priority 字段, 支持 InstanceGroup 下优先删除 @冀超;
  * CreateCluster 事件展示取消 ClusterStepCreateInfrastructure @宏伟;
  * instance-controller 获取 InstanceID 立即更新 Status, 降低 controller 重启导致 InstanceID 丢失风险 @陈欢;
  * Ubuntu 18 kubelet --resolv-conf 参数, 判断 /run/systemd/resolve/resolv.conf 不存在情况下使用 /etc/resolv.conf, 兼容知乎场景 @刘霖
* [插件管理]
  * 新增 lb-controller-for-serverless 插件部署 @思远;
* [应用管理]
  * 删除无用 go-restful/examples package @冀超;
  * 新增 app-service 部署 ingress-controller SDK @陈欢;
* [运维运营]
  * CCE 元仓增加 V2 Cluster/Instance 采集 @陈欢;
* [自动化测试]
  * 新增 AppService SDK @庭丽;
  * 新增 lb service 回归测试 @丽丽;
  * 新增 cce-monitor-service GetCluster 回归测试 @庭丽;
  * 自动化测试使用 app-service 部署 ingress-controller @陈欢;

### BugFix

* [集群管理]
  * 修复 EIP BindWidth = 0 询价问题 @宏伟;
  * GPU 机器去掉 LocalDiskSize 限制 @陈欢;
  * 修复 pkg/plugin/kubectl 单集群并行执行冲突问题 @陈欢;
  * InstanceController 创建完虚机后立即 UpdateStatus, 避免 Controller 重启导致 InstanceID 丢失(GetBCCByName 不稳定待解决) @陈欢;


## 2020-09-17

### 功能

* [容器网络]
  * V2 集群 IngressController 使用 V2 OpenAPI GetCluster 获取 BLB 子网 @陈欢;
* [运维运营]
  * 集群操作报警增加用户信息 @亚松;
* [自动化测试]
  * cce-task 有任务未完成情况下, 阻塞 cce-system 下 pod 更新 @陈欢;

### BugFix

* [集群管理]
  * 修复 OpenAPI 移入已有实例 InstanceName 不正确问题(知乎问题) @陈欢;

## 2020-09-22

### 功能

* [集群管理]
  * 新增获取集群节点子网等信息接口 @宏伟;
  * InstanceGroup 优化 BCC RateLimit 导致创建订单失败问题 @思远;
* [Serverless]
  * VK Node 增加 ephemeral-storage Quota @子超;
* [应用管理]
  * 报错信息屏蔽 FloatingIP 信息 @亚松;

### BugFix

* [集群管理]
  * 修复删除 CreateFailed CRD 时, Service 和 Controller 同时更新 RetryCount 冲突问题 @陈欢;

## 2020-09-24

### 功能

* [集群管理]
  * 新增 K8S 1.18.9 版本, Docker 19.03.13 版本 @刘霖;
  * 细化 Instance Event 支持 Instance 独立 Event @宏伟;
  * 修改 Deployment/DaemonSet APIVersion 从 extensions/v1beta1 到 apps/v1 @陈欢;
  * Instance 增加 Priority 参数, 支持 Terraform 指定 Instance 缩容 InstanceGroup @冀超;
* [Serverless]
  * Serverless 支持创建前检查, 避免地域无 BCC 创建 BLB 失败 @子超;
* [集群巡检]
  * 增加 cce-health-check helm 插件 @陈欢;
  * 集成 network-inspector/node-problem-detector + MySQL + Grafana, 用于问题定位及集群实时状态巡检 @陈欢;