# CCE 2020-07 功能

## 2020-07-02

### 功能

* [集群管理] 集群操作询价接口 @余宏伟;
* [集群管理][CCE-1779](http://newicafe.baidu.com/issue/CCE-1779/show): cce-cluster-service 兼容新老集群&迁移旧集群 @李启龙;
* [容器网络] 斗鱼 FixIP 功能 IPAM 开发 @陈亚奇
* [应用管理] cce-app-service on K8S 全地域上线 @徐亚松;
* [容器存储] CSI 使用 Interface 重构, 优化单测速度 @潘思远;
* [自动化测试] 集成测试新增创建托管集群 + 缩容自动化 Case @陈欢.
* [DevOps] cce_health_check 健康检查新增 cce-system 下 Job 类型任务检查 @张朋.
* [集群管理] [CCE-1778](http://newicafe.baidu.com/v5/issue/CCE-1778/show) CCE 询价接口实现 @余宏伟;
* [监控日志][CCE-1795](http://newicafe.baidu.com/issue/CCE-1795/show): cce-monitor-service 监控组件部署不单独校验 IAM 权限, 和 monitor 其他功能保持一致 @陈欢;

### BugFix

* [集群管理]: [CCE-1792](http://newicafe.baidu.com/v5/issue/CCE-1792/show) 修复 CDS 挂载目录获取错误 @余宏伟 【最终提交于 2020-06-30】
* [集群管理]: [CCE-1793](http://newicafe.baidu.com/v5/issue/CCE-1793/show) 修复 移入节点各种短ID未记录数据库 @余宏伟 【最终提交于 2020-07-01】

## 2020-07-14

### 功能

* [集群管理] CCE 枚举类型定义 @余宏伟;
* [容器网络] IPAM 支持 FixIP @陈亚奇;
* [集群管理] CCE InstanceGroup Go SDK @潘思远;
* [监控日志] cce-log-agent 迁移到 cce-stack 仓库 @张朋;
* [集群管理] CCE InstanceType 和 BCC OpenAPI 一致 @余宏伟;
* [集群管理] CCE InstanceStatus 和 BCC OpenAPI 一致 @余宏伟;
* [插件管理] 插件管理相关设计与实现，支持 coredns 部署 @王城程;
* [监控日志] monitor-service 增加网络、io 等监控 api 指标 @徐亚松;
* [自动化测试] 自动化测试新增 CDS 挂载/自定义 Master 多可用区回归案例 @陈欢;
* [监控日志] monitor-service RBAC 生成 KubeConfig 迁移 cce-stack @张朋;
* [集群管理] ClusterSpec 去除 CustomMaster 配置, 自定义 Master 配置和新建 Node 一致 @陈欢;
* [集群管理] ClusterSpec 中去除 CustomMasterConfig 配置, 自定义 Master 配置和新建 Node 一致 @陈欢;
* [节点组管理] instance表新增节点组相关字段, instancegroup controller开发完成，沙盒、gztest环境 @潘思远;
* [集群管理] 集群部署切换至 deployer v2 版本: 优化集群部署流程、新增各组件自定义配置、用户自定义脚本、多CDS挂载 @刘霖;

### BugFix

* [集群管理] 非默认可用区创建 BCC 需指定 AZ @陈欢;
* [集群管理] Instance Labels 缺失 clusterID @陈欢;
* [集群管理] 托管 Master 默认磁盘类型为 hp1 @陈欢;
* [集群管理] 托管 Master 默认机型配置为 N3 和 OpenAPI 一致 @陈欢;
* [应用管理] 修复 部署、普通任务、守护进程、有状态部署详情页中pod列表不全 @冀超;

## 2020-07-14

### 功能

* [集群管理] CCE 部署支持自定义 K8S 参数 @刘霖
* [集群管理] master etcd 根据 CCEInstanceID 排序 @刘霖

### BugFix

* [集群管理] [CCE-1808](http://newicafe.baidu.com/v5/issue/CCE-1808/show) 托管 Master 生成短 ID @余宏伟;

## 2020-07-21

### 功能

* [集群管理]
  * 移入节点和删除移出节点解耦 @潘思远
  * Kubelet 支持 CNI 网络模式 @陈亚奇
  * 变更 BLB 的询价接口为 LogicBLB @余宏伟;
  * 当没有指定 Docker、Kubelet 目录时，使用 CDS 盘作为数据目录 @刘霖;
  * front-proxy-client 使用集群默认根证书, 修复 metric-server 权限问题 @徐亚松
  * 去除 ClusterSpec 中 MasterTypeCustomBCC, MasterTypeExistedBCC, MasterTypeExistsBBC 类型, 统一为 MasterTypeCustom @陈欢;
* [插件管理]
  * 插件管理增加 cce-ingress-controller @陈欢;
  * 插件管理增加 vpc-cni 和 vpc-route @王城程@陈亚奇;
* [权限审计] 
  * CCE RBAC 接口迁移至 cce-stack @张朋;
* [应用管理] 
  * Helm V3 兼容新旧集群 @子超;
* [自动化测试] 
  * 增加 LB Service 创建的自动化测试 case @陈欢;
  * 增加 CCE Ingress 创建的自动化测试 case @陈欢;
  * 增加容器网络回归测试, 部署 cce-network-inspector @陈欢;

### BugFix

* [集群管理] 
  * 修复 ETCD Index 乱序问题 @刘霖;
  * 修复 1.11 版本 K8S 不支持 kubectl config set-context --current 命令 @陈欢;
  * 修复 1.13 及 1.11 低版本 K8S 集群释放时, 删除 Service/Ingress 失败问题 @陈欢;
* [插件管理] 
  * 修复 K8S dynamicClient 处理 Global 资源 BUG @陈欢;
  * pkg/plugin/kubectl 每次初始化临时 kubeconfig, 完成操作后释放 (考虑 RBAC 场景, 同一集群会有不同用户) @陈欢;
* [应用管理]
  * 调整 webssh 在 panic 时 wg.done 关闭连接顺序 @冀超;
* [自动化测试] 
  * 修复 checkClusterInstance: cluster_phase = running 时 node 不一定 ready(二者解耦) @陈欢;

## 2020-07-23

### 功能

* [集群管理]
  * 增加 kubelet 动态配置生效检查 @陈欢;
  * 新增compatible包，兼容 CACert @李启龙;
  * 删除集群检查 Service/Ingress 对应 IaaS 资源是否释放 @陈欢;
  * ManagedCluster 类型 Master.AvailableZone 和 Node 一致, 默认为 zoneA @陈欢;
  * 插件管理增加kube-proxy组件，集群部署kube-proxy默认容器启动,兼容二进制部署 @刘霖;
  * 创建集群增加容器网络检查 @余宏伟
  * kubenet 模式添加 ip-masq-agent 插件 @陈亚奇
  
* [日志管理]
  * 新增Pod健康检查探针 @张朋;
  * 修改日志采集部署方式 @张朋;
  * 新增日志解析，防止json 转换失败导致Pod 崩溃 @张朋;
* [自动化测试]
  * 增加 VPC Route 增加回归检查 @陈欢;
* [插件管理]
  * 支持cronhpa、metrics-adapter、nvidia-gpu、kunlun-nvidia 插件 @王城程;
* [错误码规范]
  * CheckContainerNetworkCIDR 方法错误码规范 @陈欢;

### BugFix

* [集群管理]
  * 修复创建 BCC 指定 InstanceName 未生效问题 @陈欢;
  * 修复前端节点状态展示问题、页面跳转失败问题 @李启龙；

## 2020-07-28

### 功能

* [集群管理]
  * 移除deployerV1相关代码 @刘霖;
  * 解绑删除 ENI @余宏伟;
  * 删除实例路由 @余宏伟;
  * kubelet 重启删除 cni 配置文件 @陈亚奇
* [插件管理]
  * 默认部署插件增加nvidia-device-plugin @刘霖;
* [权限审计] 
  * 新增获取RBAC列表接口 @张朋;
  * 为子用户赋权所有集群接口 @张朋;
* [自动化测试]
  * 增加 OS 全量覆盖 @李丽丽;
  * 增加 GPU 机器 回归检查 @陈欢;

### BugFix
* [集群管理]
  * 修复 ubuntu 18 安装nfs失败 @刘霖;
* [事件中心]
  * 修复 ES GC 数据失败问题 @张朋  
* [容器网络]
  * 修复两台 BCC 在同一物理机时 VPC-CNI 连通性问题 @陈亚奇;


### 数据库变更

t_cce_cluster

```mysql
/*确认不会在新加字段上进行条件检索*/ALTER TABLE t_cce_cluster ADD COLUMN `k8s_custom_config` varchar(2000) NOT NULL DEFAULT '{}' COMMENT 'k8s_custom_config';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE t_cce_cluster ADD COLUMN `eni_vpc_subnet_ids` varchar(2000) NOT NULL DEFAULT '{}' COMMENT 'eni_vpc_subnet_ids';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE t_cce_cluster ADD COLUMN `eni_security_group_id` varchar(256) NOT NULL DEFAULT '' COMMENT 'eni_security_group_id';
```

t_cce_instance

```mysql
```

## 2020-07-30

### 功能
* [集群管理]
  * BBC 删除强制设置为移出 @陈欢;
  * BBC etcd, kubelet, docker 数据目录默认值为 /home/<USER>/ @刘霖;
* [自动化测试]
  * 自动化测试由 Map 改成 List, 保证 Case 运行顺序 @陈欢;
* [容器网络]
  * 新增 sysctl cni 插件 @陈亚奇;

### BugFix
* [集群管理]
  * 修复 etcd 数据目录为空，设置默认值 /var/lib/etcd @刘霖;
