# CCE 2020-11 功能

## 2020-11-03

### 文档

* CCE-2638: 值班指南文档 @冀超;
* CCE-2542: 更新 BFE 转发公有云 CCE 最佳实践 @亚奇;

### 功能

* [集群管理]
  * CCE-2488: 集群操作增加事件标准错误类型 @宏伟;
  * CCE-2627: 自定义 Tag, 设置不可更改的参数 @宏伟;
  * CCE-2502: 后端去除虚机镜像版本校验, 避免未知镜像导致失败 @启龙;
* [插件管理]
  * CCE-2554: cce-stack K8S 插件 CI 脚本开发 @陈欢;
* [容器网络]
  * CCE-2635: ENI 挂载数超出配置仅报错 @亚奇;
* [CCE@Edge]
  * CCE-2551: Edge Cluster Provider 实现 @思远;
  * CCE-2557: Edge Instnace Add/Del Provider 实现 @思远;
  * CCE-2551: 边缘集群中的 BEC 虚机删除时跳过删除 ENI 和 Route @思远;
* [自动化测试]
  * CCE-2558: 优化 InstanceGroup 回归测试任务速度 @思远;
* [运维运营]
  * CCE-2560: 修改 cluster-controller HostNetwork 部署 DNS 解析为 ClusterFirstWithHostNet @启龙.

### BugFix

* [集群管理]
  * CCE-2555: CA 根证书过期时间从 5 年提高到 10 年 @亚松;
  * 修复 Service 删除 Cluster 联动删除 Instance BUG @陈欢;
  * CCE-2561: 修复虚机报售罄后，controller 空指针 Panic @启龙;
  * CCE-2556: 去除 EIP InstanceType 和 Status 强校验, 避免 EIP 新增类型导致失败 @陈欢.

## 2020-11-05

### 功能

* [集群管理]
  * CCE-2655: 后端禁止直接 Delete Master Instance @宏伟;
  * CCE-2560: 集群操作 Step 入库, 指导稳定性和性能优化 @启龙;
  * CCE-1661: GPUSharing 类型集群, 增加 KubeExtenderSchedulerFTPAddress 配置 @刘霖;
* [插件管理]
  * CCE-2548: 优化 CCM 代码目录结构 @冀超;
  * CCE-2630: 插件发布 helm push 至 helm-service @陈欢;
  * CCE-2630: cce-ingress-controller 推送至百度官方仓库 @陈欢;
  * CCE-2650: Be compatible with helm v3 duplicated name error @子超;
* [自动化测试]
  * CCE-2648: InstanceGroup Case 去除删除 Instance 逻辑, 避免数据库主从同步导致重复删除 @思远

### BugFix

* [集群管理]
  * CCE-2654: 修复 Sync Instance 接口导致 DB 和 CRD 数据不一致问题 @宏伟;
  * CCE-2655: ClusterService errorHandler 增加 Panic, 修复报错后继续执行 @宏伟;
  
## 2020-11-10

### 文档

* CCE-2542 更新 BFE 转发公有云 CCE 最佳实践 @亚奇;
* CCE-2638: CCE MetaCluster docker,kubelet 安装 @刘霖;
* CCE-2664 Serverless 集群Master可用性优化设计文档 @思远;
* CCE-2683: 增加运维 SOP, 批量删除 Failed Instance @陈欢;

### 功能

* [集群管理]
  * CCE-2561: 增加 Cluster/Instance Step 信息采集, gztest 上线 @启龙
  * CCE-2627: 增加 admission webhook demo, 用于 Cluster/Instance CRD 参数校验 @宏伟
  * CCE-2662: CCE 支持 GPUSharing 集群, 增加 kongming-device-plugin 镜像地址 @陈欢
* [CCE@BEC]
  * CCE-2667: cluster-service 支持创建边缘集群 @思远;
  * CCE-2648: 各个地域添加 BEC 的IAM endpoint配置: 思远;
  * CCE-2667: 改 SDK 符合 BEC OpenAPI 实际返回结构体 @思远;
  * CCE-2667: 边缘 BLB 名称字符限制和云上 BLB 不一致，不能包含 / @思远;
  * CCE-2557: 临时 sts 授权使用 accountID && 保证cce 和 bec上下游使用一致的 iam 后端 @思远;
  * CCE-2667: bec machine status中设置uuid为短id，绕过uuid判空的检查 && bec lb 名称命名规范不允许大写字母 @思远;
* [Serverless]
  * CCE-2341: cce-virtual-kubelet 在 bj, su 部署 @子超
  * CCE-2341 Add ServerlessETCD configs for region su and bj @子超
* [应用管理]
  * CCE-2669: AppService 增加 api-resources 判断集群版本 @陈欢
* [插件管理]
  *	CCE-2631: cce-ingress-controller 通过 Helm 仓库部署 Demo @陈欢
* [运维运营]
  * CCE-2685: cce-status-alert 跳过 deleting 中 CRD 一致性检查 @陈欢
* [Helm仓库]
  * CCE-2653: Add extra check for admin chart upload @子超
* [自动化测试]
  * CCE-2663: 回归集群创建失败, 取消删除集群 @陈欢;
  * CCE-2631: 绑定解绑 ENI 会导致流表残留, 临时去除 VPC-CNI 回归 @陈欢;
  * CCE-2648: InstanceGroup Case 去除删除 Instance 逻辑, 避免数据库主从同步导致重复删除 @思远;

### BugFix

* [集群管理]
  * CCE-2679: 移入节点直接使用OpenBCC API返回的instanceName @宏伟

## 2020-11-12

### 文档

* CCE-2681: 容器网络 出入站流量 相关产品调研笔记 @冀超
* CCE-2515: 官方文档修改 Deployment/DaemonSet/StatefulSet 为 AppsV1 @启龙;

### 功能

* [集群管理]
  * CCE-2353: 托管集群缩容限制最小节点数 @宏伟;
  * CCE-2353: 修改托管集群 Node 数目报错信息 @宏伟;
  * CCE-1661: 修改 api-server、kubelet 日志等级为 4 Debug @刘霖;
  * CCE-2449: IPv6 on 1.18 ControllerManager 支持 node-cidr-ipv6 和 ipvb4 参数 @冀超;
* [CCE@BEC]
  * CCE-2667: bec-client 开启 debug 模式 @思远;
  * CCE-2667: 通过 BEC 接口获取 VM 对应的 DeploymentID @思远;
  * CCE-2667: EDGE Cluster 绑定 LB 后端时使用 DeploymentID @思远;
* [Serverless]
  * CCE-2697: Disable etcd compaction from serverless apiserver @刘霖;
  * CCE-2689: serverless 集群容器网络模式为 BLB clusterIP 时跳过网络冲突检查 @思远;
  * CCE-2341: Enable serverless cluster creation button in su and bj and deploy vk daemonset in gz @子超;
* [资源弹性]
  * CCE-2671: Cluster AutoScaler 支持 1.18 @宏伟;
* [应用管理]
  * CCE-2669: 应用管理 Deployment/DaemonSet/StatefulSet/ReplicaSet 使用 AppsV1() @陈欢;
* [监控日志]
  * CCE-2386:	增加 Cluster/Instance HealthCheck CRD @亚松;
* [容器网络]
  * CCE-2676: Ingress 支持 IPv6 @亚奇;
* [运维运营]
  * CCE-2667: Instance/Cluster 报警过滤 handler != default @思远;
* [自动化回归]
  * CCE-2698: 增加 1.18 Cluster 回归测试 @启龙;
  * CCE-2158: 增加 app-service StatefulSet/DaemonSet 回归 Case @庭丽;
  * CCE-2695: 修改 gztest 创建 BCC 子网为可用区 C, 减少 BCC 资源导致失败概率 @陈欢;

### BugFix

* [运维运营]
  * CCE-2695: 修复 cce-status-alert 指针赋值导致报警错误问题 @陈欢;
* [CCE@BEC]
  * CCE-2667: 修复 edge cluster EnsureClusterLBBackendServer nil map @思远.

## 2020-11-17

### 功能

* [集群管理]
  * CCE-2704: 节点列表 Master 节点默认排序靠前 @宏伟;
  * CCE-2705: Logic 接口获取 CCE 支持镜像列表 @启龙;
  * CCE-2561: Cluster/Instance Step 采集全地域上线 @启龙;
  * CCE-2679: InstanceGroup 发单前，instanceName 加随机后缀 @思远;
  * CCE-2704: Instance 状态同步仅同步 Running 及 Failed 实例 @宏伟;
  * CCE-2704: 增加标准错误信息 InsufficientBalance, IPInSubnetNotEnoughExceptions @宏伟
* [插件管理]
  * CCE-2727: CCE hub 镜像迁移至 registry @陈欢;
* [CCE@BEC]
  * CCE-2701: 支持部署calico网络插件 @思远;
  * CCE-2712: 边缘集群支持部署 nvidia-docker @思远;
  * CCE-2701: 边缘集群创建 LB 后，如果发现 LB 还没有分配 IP，进行等待避免快速重试 20 次导致失败 @思远;
  * CCE-2701: helm template 不会输出 CRDs 目录的内容，calico 的 crds 移动到 template 目录 @思远;
* [监控日志]
  * CCE-2386: 增加 health-controller @亚松;
* [运维运营]
  * CCE-2772: status-alert 过滤 BEC Instance 检查; @陈欢;
  * CCE-2767: status-alert cronjob 增加 startingDeadlineSeconds, 避免失败后停止运行 @陈欢;
* [自动化测试]
  * CCE-2702: 去除 e2etest SDK 重试 @陈欢;
  * CCE-2622: 回归任务 Pod=Failed, 直接退出检查, 加快速度 @陈欢;
  * CCE-2341: Enable serverless e2e test in su and bj @子超;
  * CCE-2703: 修复 e2etest 同名 Case 初始化相同实例冲突问题 @思远;
  * CCE-2710: 去除 vpc-cni 回归中应用管理 case, 避免 IP 不足 @陈欢;
* [Serverless]
  * CCE-2771: Fix logrotate in serverless debug container @子超;
  * CCE-2709: Add --request-timeout to kubectl to avoid hang @子超;

### BugFix

* [集群管理]
  * CCE-2704: 修复托管集群限制最小 Node 阻塞 InstanceGroup 删除 @宏伟;

## 2020-11-19

### 功能

* [集群管理]
  * CCE-1661: 支持跳过docker安装 @刘霖;
  * CCE-1661: 删除 kubelet 废弃配置 @刘霖;
  * CCE-2620: EIP 新增"BOS 合并计费"模式 @李启龙;
  * CCE-2712: 修复创建 Instance 时指定 Labels 丢失 @思远;
  * CCE-2776: GPUSharing 集群使用独立 cluster_fillspec @陈欢;
  * CCE-2776: GPUShare 集群类型, extender-scheduler 使用 admin.conf @陈欢;
* [Serverless]
  * CCE-2770: serverless 集群中部署 core-dns 时适配两种 service 网络模式 @思远;
  * CCE-2770: blb service 模式的 serverless 集群 apiserver 镜像定制化，能直接跑检查版本的 e2e test case @思远;
  * CCE-2770: BLB Service 模式下 Serverless 集群 kube-dns service 不能同时监听 TCP 和 UDP 的53端口，因为 BLB 不支持同时监听不同协议的同一个端口 @思远;
* [应用管理]
  * CCE-2732: 修复 AppService PodInfo == nil 问题, 及 CreateAndDeleteDeployment 异常返回 @陈欢;
* [CCE@BEC]
  * CCE-2712: 移除 epel.repo，提高部署中 yum 速度 @思远;
  * CCE-2712: BEC GPU Instance 设置 docker 默认 runtime 为 nvidia-docker @思远;
  * CCE-2712: Instance 同步 Label 到 Node，边缘集群 GPU Instance 创建时默认带有 GPU Labels && 设置边缘集群默认部署 Plugins @思远;
* [运维运营]
  * CCE-2704: 增加新版集群 Customers 统计 @宏伟;
* [自动化测试]
  * CCE-2770: BLB Service 模式 Serverless 集群回归 case @思远;
  * CCE-2770: BLB Service 模式 Serverless 集群网络, 修复单 Pod -> BLB 回流单 Pod 概率不通问题 @思远;

### BugFix

* [CCE@BEC]
  * CCE-2712: 通过 Name 获取 BEC 的 BLB @思远;
* [自动化测试]
  * CCE-2158: 应用管理修复 Create/Delete K8S Resource 后, 立即 Get 数目不对问题 @陈欢;
* [运维运营]
  * CCE-2710: 暂时取消 cce_stack_control.sh 镜像 build 并行 @陈欢;

## 2020-11-24

### 文档

* CCE-2659: 添加对等连接容器流量转发配置说明 @亚奇;

### 功能

* [集群管理]
  * CCE-2705: CCE "支持镜像"和"不支持镜像" Interface 定义和实现 @启龙;
* [报错优化]
  * CCE-2679: 集群新增规范错误类型 ZoneResourceNotAvailable @宏伟;
* [Serverless]
  * CCE-2346: 1.16.8 apiserver disable clusterip assigning diff @思远;
* [CCE@BEC]
  * CCE-2701: edge cluster 部署 calico 默认使用 BGP @思远;
  * CCE-2712: BEC 虚机 disable epel 和 baidu-bcm yum repo @思远;
* [容器网络]
  * CCE-2738: 增加 vpc native cni 部署 @亚奇;
  * CCE-2786: 不允许用户 NodeBackend Service 和 PodBackend Service 之间转换 @冀超;
  * CCE-2783: 修复 lb-controller 和 ccm service-controller 分工重叠及缓存使用错误 @冀超;
* [监控日志]
  * CCE-2386: 增加 HealthCheck CRD 下发逻辑 @刘霖;
* [插件管理]
  * CCE-2386: npd helm chart @亚松;
  * CCE-2715: vpc route cni helm chart @亚奇;
  * CCE-2716: csi-bosplugin helm chart @思远;
  * CCE-2717: csi-cdsplugin helm chart @思远;
  * CCE-2787: cce-web-terminal helm chart @陈欢;
  * CCE-1661: nvidia-device-plugin helm chart @刘霖;
  * CCE-2386: cce-prometheus-grafana helm chart @亚松;
  * CCE-2714: core-dns, metrics-server helm chart @启龙;
  * CCE-2715: ip-masq-agent、kube-proxy helm chart @亚奇;
  * CCE-2788: Add readme check for admin chart upload @子超;
  * CCE-2784: 镜像版本号使用 AGILE_RELEASE_VERSION @陈欢;
  * CCE-2722: cce-cluster-autoscaler, cce-network-inspector helm chart @亚松;
* [自动化测试]
  * CCE-2160: 增加 helm 仓库自动化测试 @丽丽;
  * CCE-2811: 增加插件 CheckPluginsExist 回归测试 @陈欢;
  * CCE-2158: 删除 app-service delete 后的 pod 检查 @庭丽;
  * CCE-2710: 去掉 vpc-cni 回归, 待亚奇上线 navite-vpc-cni @陈欢;
* [行业云私有化]
  * CCE-1661: 行业云 hzpro 厂内环境配置 values-cnhzpro.yaml @刘霖.

## 2020-11-26

### 文档

* CCE-2659: 添加对等连接容器流量转发配置说明 @亚奇;

### 功能

* [集群管理]
  * CCE-2627: cluster instance 添加 validate tag, 调整 webhook 部署文件 @宏伟;
  * CCE-2780: InstanceOS 新增 special_version 解决内部上云和公共云镜像同名问题 @启龙;
  * CCE-2679: InstanceProvider 新增 GetHostName 方式, 通过 hostname 获取主机名实现 @宏伟;
* [报错优化]
  * CCE-2627: 增加错误类型 BCCLackedWhenCreateBLB, AccountInsufficientBalance @宏伟;
* [运维运营]
  * CCE-2814: MetaCluster rc.local 配置管理新增 Prometheus & Thanos 等开机自启动 @亚松;
* [监控日志]
  * CCE-2386: 增加 cce-status-alert RBAC 支持 HealthCheck 资源 GET/LIST/UPDATE @亚松;
* [插件管理]
  * CCE-2817: NetworkInspector 回归 result Pod 使用非 HostNetwork 启动 @陈欢;
* [自动化测试]
  * CCE-2162: 增加 csi case, 包括 bos/cds/cfs pv/pvc 挂载 @丽丽;
  * CCE-2818: gztest 开启 blb service serverless 集群 e2etest @思远;
* [行业云私有化]
  * CCE-1661: 增加私有化环境 SQL 记录 @刘霖;
  * CCE-2796: 私有化 hzpro 环境 e2etest values.yaml @刘霖;
  * CCE-1661: 新增 cce_stack_controller_private.sh, 支持私有化 CI/CD @刘霖;

