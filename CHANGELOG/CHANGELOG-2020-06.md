# CCE 2020-06 功能

## 2020-06-01

### 功能

* [监控日志][CCE-1678](http://newicafe.baidu.com/v5/issue/CCE-1678/show): cce-monitor-service 兼容新老集群。 @陈欢

### BugFix

* [监控日志][CCE-1714](http://newicafe.baidu.com/v5/issue/CCE-1714/sho): cce-monitor-service 兼容 1.16 的 prometheus 配置; @亚松
* [权限审计][CCE-1716](http://newicafe.baidu.com/v5/issue/CCE-1716/show): 审计服务修改接口参数，导致审计事件无法获取, cce-monitor-service 增加 currentUser 参数。 @陈欢

### 修改表结构

修改 kubeconfig_info 表结构

```mysql
use hkg_cce_service;/*系统自动添加*/
/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info drop primary key;

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info drop index config_id;

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN `id` bigint primary key auto_increment comment 'auto-increasing ID';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN `created_at` TIMESTAMP NOT NULL DEFAULT '1971-01-01 00:00:01' comment 'create time';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN `updated_at` TIMESTAMP NOT NULL DEFAULT '1971-01-01 00:00:01' comment 'update time';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN `deleted_at` TIMESTAMP NOT NULL DEFAULT '1971-01-01 00:00:01' comment 'delete time';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN eip VARCHAR(128) NOT NULL DEFAULT "" COMMENT 'BLB EIP';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN vpc_ip varchar(128) NOT NULL DEFAULT "" COMMENT 'BLB VPC IP';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD COLUMN floating_ip varchar(128) NOT NULL DEFAULT "" COMMENT 'BLB FLOATING IP';

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD unique key(config_id);

/*确认不会在新加字段上进行条件检索*/ALTER TABLE kubeconfig_info ADD unique key(cluster_id, subuser_id);

SELECT config_id FROM kubeconfig_info WHERE id = 1;
SELECT config_id FROM kubeconfig_info WHERE config_id = "config_id";
SELECT config_id FROM kubeconfig_info WHERE cluster_id = "cluster_id" AND subuser_id = "subuser_id";
```

## 2020-06-03

### 功能

* [集群管理][CCE-1531](http://newicafe.baidu.com/v5/issue/CCE-1531/show): 已有实例创建集群, 声明式集群管理上线. @启龙

### BugFix

* [监控日志][CCE-1750](http://newicafe.baidu.com/v5/issue/CCE-1750/show): 修复集群被删除后, 删除集群快照失败; @陈欢

## 2020-06-04

### 功能

* [监控日志][CCE-1759](http://newicafe.baidu.com/v5/issue/CCE-1759/show): cce-monitor-service 修改 ThanosQueryEndpoint 配置从 127.0.0.1:8890 为 VIP; @陈欢

### BugFix

* [监控日志]: cce-monitor-service 集群备份兼容 account_id 和 user_id 查询. @陈欢
* [集群管理][CCE-1758](http://newicafe.baidu.com/v5/issue/CCE-1758/show): 兼容 kubeconfig_info 旧集群无 floating_ip 及 vpc_ip 等字段. @陈欢

## 2020-06-05

### 功能

* [监控日志]: [CCE-1705](http://newicafe.baidu.com/issue/CCE-1705/show): 新增查询该地域所有非删除状态的集群

### BugFix

* [插件 CNI] [CCE-1611](http://newicafe.baidu.com/issue/cce-1611/show?from=page): @孙天元 kubenet 模式集群部署时，修复 ip-masq-agent 部署 Pod 无法启动问题
## 2020-06-09

### BugFix

* [监控日志]: [CCE-1766](http://newicafe.baidu.com/v5/issue/CCE-1766/show) db 连接没有复用，导致数据库连接数过高
* [集群管理]: [CCE-1611](http://newicafe.baidu.com/issue/cce-1611/show?from=page) 修复 EIP Available 时, InstanceType 为空
* [集群管理]: [CCE-1611](http://newicafe.baidu.com/issue/cce-1611/show?from=page) 修复 Node 部署, 生成 kubeconfig 依赖 APIServerWhiteList 问题
* [集群管理]: [CCE-1611](http://newicafe.baidu.com/issue/cce-1611/show?from=page) fix ccm deploy ipv6 clusterCIDR

## 2020-06-10

### 功能

* [监控日志]: [CCE-1733](http://newicafe.baidu.com:80/issue/CCE-1733/show?from=page) A区部署K8S 插件使用hostNetwork 模式启动 @张朋
* [服务画像]: [CCE-1636](http://newicafe.baidu.com:80/issue/CCE-1636/show?from=page) k8s-event-collector/k8s-report-collector相关模块的数据库操作移动到pkg/modules公共库中 @张朋

## 2020-06-16

### 功能

* [集群管理]: cce-cluster-sync 开发完成 @李启龙
* [监控日志]: cce-event-collector 兼容新老集群 @张朋
* [集群管理]: cce-cluster-service 增加 InstanceGroup 对象, 批量创建 Instance 使用 InstanceSet 结构体 @李启龙
* [事件进度]: [CCE-1690](http://newicafe.baidu.com/v5/issue/CCE-1757/show) 集群创建删除, 节点创建删除事件展示 @余宏伟
* [自动化测试]: [CCE-1782](http://newicafe.baidu.com/v5/issue/CCE-1782/show) CCE 自动化测试工具 @陈欢

## 2020-06-17

### 功能

* [监控日志]:  cce-event-collector B区二进制组件迁移到K8S上部署 @张朋
* [自动化测试]: [CCE-1717](http://newicafe.baidu.com/issue/CCE-1717/show) Hi Robot CodeReview 通知 @陈欢
* [自动化测试]: [CCE-1782](http://newicafe.baidu.com/issue/CCE-1782/show) 集成测试增加到 Agile 流程 @陈欢


## 2020-06-24

### 功能

* [应用管理]: app-service移入 cce-stack（上线不切流量），client-go 兼容改造，修改鉴权和 db 读取逻辑 @徐亚松
* [监控告警]：修改 monitor-config-reload 的 dnspolicy，解决报警规则无法生效问题 @徐亚松
* [服务画像]: [CCE-1636](http://newicafe.baidu.com:80/issue/CCE-1636/show?from=page) k8s-report-collector 调整代码结构，新增收集健康汇总数据特性 @张朋
* [服务画像]: [CCE-1636](http://newicafe.baidu.com:80/issue/CCE-1636/show?from=page) monitor-service 新增集群健康检查报表接口、集群健康趋势图接口 @张朋
* [安全组]: [CCE-1780](http://newicafe.baidu.com/v5/issue/CCE-1780/show) CCE 安全组规则检查、添加，新建默认安全组 @余宏伟

## 2020-06-26

### 功能

* [集成测试]: e2e-test-client 增加 -show-param 命令, 支持显示对应 Case 的参数格式.

## 2020-06-30

### 功能

* [监控日志]: monitor-service 服务画像;@张朋
* [集群管理]: cluster-service 增加安全组校验, 添加规则等接口;@余宏伟
* [自动化测试]: 增加 K8S Node ephemeralStorage 和 maxPods 检查;@陈欢
* [集群管理]: 删除实例 DeleteOption 默认参数为: DeleteResource=true, DeleteCDSSnapshot=true.@陈欢

### BugFix

* [集群管理]: Create Logic BCC 删除 AvailableZone 参数校验.@陈欢

