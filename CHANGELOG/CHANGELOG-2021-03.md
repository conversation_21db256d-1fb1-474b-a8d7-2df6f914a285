# CCE 2021-03 功能

## 2021-03-02

### 功能

* [集群管理]
  * CCE-2905 安全组优化;@宏伟
  * CCE-3114 节点列表返回封锁状态;@宏伟
* [边缘集群]
  * CCE-3130 支持云上master集群添加bec节点 @思远
* [自动化测试]
  * 
* [监控日志]
  * CCE-3134: cce-cluster-health-check cronjob 上线;@陈欢
* [公私架构统一]
  * CCE-3111 私有化改造;@刘霖
* [BUG]
  * CCE-3179 Bug 修复: service 名称超长时 block 删除操作的bug;@冀超
  * CCE-3176 缓存使用与Ingress删除Bug fix@冀超
  *	CCE-3079 Ingress 优先级问题修复；@冀超
* [DOC]
  * CCE-3134: CCE 技术全景图;@陈欢

## 2021-03-04

### 功能

* [容器网络]
  * CCE-3180 斗鱼 CNI 支持 API 限速，去掉打污点逻辑@亚齐
* [serverless]
  * s-k8s-86 CCE-3201 保定开服 etcd manager 部署配置@子超
* [监控日志]
  * CCE-3134: 增加 health-check namespace serviceaccount;@陈欢
* [公私架构统一]
  * CCE-3111 私有化CI/CD;@刘霖
  * CCE-3111 日志组件镜像可配置@刘霖
  * CCE-3111 cce-registry 包配置优化@刘霖
* [BUG]
  * CCE-3180 斗鱼 CNI 幂等处理 eni 删除错误;@亚奇
  * CCE-3170 bbc 询价接口 bug修复;@启龙
  
## 2021-03-11

### 功能

* [集群管理]
  * CCE-3245 B站边缘集群基于1.18.9提供一个特殊版本APIServer@思远
  * CCE-3130 云边集群和bec节点kubelet service跳过删除cni配置@思远
  * CCE-3208 容器化master pod删除resource request@思远
  * CCE-3095 CCE新建BBC、BBC节点，支持选择部署集@李启龙
  * CCE-3244 bec lb 后端绑定接口升级不兼容修复@思远
  * CCE-3027 安全组规则整理@宏伟
  * CCE-3208 kubelet 部署时支持设置providerID && 删除node时支持通过providerID判断是否是目标节点@思远
  * CCE-3130 支持bec unbuntu虚机@思远
  * CCE-3240 Support custom serverless master spec@子超
  * CCE-3208 容器化master删除时删除路由@思远
  * CCE-3114 实名认证提测问题修复@宏伟
  * CCE-3208 master容器化文档 && 云边集群去除部署cni@思远
  * CCE-3208 支持master容器化@思远
* [容器网络]
  * CCE-3211 修复 clusterip 渲染问题@亚奇
  * CCE-3218 LB Controller Endpoint&EIPPurchaseType 参数可配置@冀超
  * CCE-3185 VPC-Hybrid 集群移出/删除节点释放辅助 IP@亚奇
  * CCE-3213 IngressController 日志优化@冀超
* [监控日志]
  * CCE-1381 日志采集输入源强制编码成UTF-8@张朋
* [公私架构统一]
  * CCE-3111 私有化配置@刘霖
  * CCE-3111 logagent bos endpoint 可配置@刘霖
  * CCE-3111 私有化关闭健康检查@刘霖
* [回归测试]
  * CCE-3201 Enable serverless cluster regression in bd@子超
  * CCE-2818 Re-enable online blb serverless cluster regression@子超
* [BUG]
  * CCE-3027 节点列表按状态筛选异常 case 展示问题修复：节点存在 DB，不存在于集群，应该返回 not_ready@宏伟
  * CCE-3243 优化日志与修复 ctx RedID 断链 Bug@冀超
  
## 2021-03-16

### 功能

* [集群管理]
  * CCE-2758 EIP 绑定和解绑 @宏伟

* [公私架构统一]
  * CCE-3111 部署包迁移至 layer-cce-stack @刘霖
  * CCE-3111 汽车城现场环境配置 @刘霖
  * CCE-3111 移除 cce-package 配置 @刘霖 
  * CCE-3111 cce-helm-service 端口冲突 @刘霖
  * CCE-3111 cds-flex-volume endpoint 可配置 @刘霖
  * CCE-3111 私有化改造 @刘霖

* [回归测试]
  * CCE-3248 添加 master 容器化自定义集群回归 @思远
  * CCE-3196 Reserve pod for manual check if e2e case ServerlessClusterNetwork failed @子超

* [性能测试]
  * CCE-3202 s-k8s-101 Add fake bci backend to implement vk dry-run @子超
  * CCE-3202 Add delay for API operation to simulate more accurately @子超
  * CCE-3202 Add comments and ut @子超
  * CCE-3202 Fix the case that virtual node cannot recover from NotReady @子超

* [DOC]
  * CCE-3251 新人文档完善 @子华
  * CCE-3218 lb/ingress controller readme @冀超

## 2021-03-23

### 功能

* [集群管理]
  * CCE-2758: EIP 绑定解绑信息补全; @宏伟
* [裸金属集群]
  * CCE-3284: 移入BBC设置 Flavor; @启龙
  * CCE-3163: BBC 新增 flavor 和 image 接口; @启龙
* [Serverless]
  * CCE-3271: Fix logrotate in serverless master; @子超
  * CCE-3271: Update build.sh for serverless master 1.16.8; @子超
  * CCE-3233: Fix listMyBCIPods when pods exceeds bci.MaxListPageSize; @子超
* [容器网络]
  * CCE-3276: 修复斗鱼 IP 误删; @亚奇
* [公私架构统一]
  * CCE-3111: 吉利生产环境配置; @刘霖
  * CCE-3218: 私有化镜像列表增加 ingress-controller 和 lb-controller; @刘霖
* [监控日志]
  * CCE-3263: 修复集群快照在 chrome 浏览器上无法下载; @子华
* [资源弹性]
  * CCE-3270: ClusterAutoScaler 添加 anti-affinity 和 ports; @思远

## 2021-03-25

### 功能

* [集群管理]
  * CCE-3295: BCC EIP 支持 purchase type;@启龙
* [节点组]
  * CCE-3264: 节点组支持细粒度instance状态数量统计;@思远
  * CCE-3301: 修复节点组缩容时不同重试中随机选择节点可能出现不一致的问题;@思远
* [CCE@BEC]
  * CCE-3257: cloudEdge cluster addon plugin support;@思远
  * CCE-3256: CCE-3257 支持云边集群apiserver特殊配置、支持部署云边集群calico、云边通道coredns、云边通道两端组件;@思远
  * CCE-3256: CCE-3257 云边集群kubelet通过--node-ip设置node InternalIP && calico controller 指定部署到master && 添加master BGPPeer and IPPool;@思远
* [Serverless]
  * CCE-3296: Update CRD for vkConfig;@子超
  * CCE-3296: Add pod cache feature gate in vk;@子超
  * CCE-3296: Consider container status when deduce BCI pod phase;@子超
* [容器网络]
  * CCE-3298: LB/Ingress 环境变量空值 BUG;@冀超
  * CCE-3294: VPC-CNI 网络模式下 kube-controller-manager 不分配 node cidrs;@袁子华
* [可观测性]
  * CCE-3288: thanos master 监控自动发现;@陈欢
* [公私架构统一]
  * CCE-3111: 吉利生产环境配置;@刘霖
* [自动化测试]
  * CCE-3274: 精简无用 tese-case, 临时关闭 CreateBLBServiceServerlessCluster;@陈欢

## 2021-03-30

### 功能

* [集群管理]
  * CCE-3334: 新增附加安全组回归测试;@宏伟
  * CCE-2758: 安全组重复创建问题修复;@宏伟
  * CCE-3307: 斗鱼升级 docker 至 20;@亚奇
  * CCE-2758; 修复 eip 绑定 BBC 的问题;@启龙
  * CCE-3313: 知乎 kube-proxy 配置自动生成;@亚奇
  * CCE-3257; 插件管理检查资源跳过crd定义还没部署的资源;@思远
  * CCE-2758: CCE 安全组规则修改：内网节点互通协议 tcp->all;@宏伟
* [裸金属集群]
  * CCE-2983; 裸金属辅助 IP 集群关闭 CCM 路由;@亚奇
* [CCE@BEC]
  * CCE-3257: cloudEdge cluster calico crds;@思远
  * CCE-3310: 云边集群云边通道部署插件;@思远
  * CCE-3310: 云边集群默认部署lb controller;@思远
  * CCE-3299: BEC 虚机节点支持记录节点地域、城市和运营商信息;@思远
  * CCE-3310: 云边集群 cloud tunnel lb service 只暴露 eip;@思远
* [容器网络]
  * CCE-3276; 修复斗鱼 IPAM 误删 IP;@亚奇
  * CCE-3303: vpc-route 模式切换为 route-controller daemonset;@冀超
  * CCE-3310: lb controller 支持service annotation 指定只暴露 eip;@思远
  * CCE-3310: lb-controller 支持 master node 支持通过 annotations 设置需要处理的 svc;@思远
* [公私架构统一]
  * CCE-3111: 私有化镜像补充;@刘霖
* [监控日志]
  * CCE-3325: cce-thanos 上线 fwh/su/bdbl;@陈欢
  * CCE-3308: Grafana Provisioning CI/CD;@陈欢


