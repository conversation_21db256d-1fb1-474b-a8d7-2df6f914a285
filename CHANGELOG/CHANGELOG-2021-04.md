# CCE 2021-04 功能

## 2021-04-06

### 功能

* [集群管理]
  * CCE-3111 fix 部署列表，容器组展示错误; @刘霖
  * CCE-3311 迁移动态调度插件到 cce-stack; @子华
* [边缘集群]
  * CCE-3310 云边集群云端使用ippools; @思远
* [监控日志]
  * CCE-3361: 增加 Grafana Dashboard; @陈欢
* [BUG]
  * CCE-3360 meta client opts 空指针异常处理；@子华

## 2021-04-08

### 功能

* [集群管理]
  * CCE-3364: 竞价实例 SDK; @宏伟
  * CCE-3364: cluster step 修改联通 APIServer;@宏伟
  * CCE-3364: 竞价实例 cluster-service 竞价参数检查; @宏伟
* [安全审计]
  * CCE-3364: 安全组规则修改; @宏伟
* [监控日志]
  * CCE-3388: 添加 kubenetes-service job; @子华
  * CCE-3373: Thanos 监控 Grafana Dashboard; @陈欢
  * CCE-3390: 修改 kvass shard.max-series = 100W; @陈欢
  * CCE-3388: 添加 kube-state-metrics 到 cce-thanos; @子华
  * CCE-3361: 新增 MetaCluster Pod 资源 Grafana Dashboard; @子华
  * CCE-3361: 解决 thanos sidecar 上传文件 > 128 MB 不完整问题; @陈欢
  * CCE-3388: 添加 node-exporter 部署 & 修改 prometheus configmap; @子华
  * CCE-3356: Annotation kubernetes.io/cce.cluster.skip-prometheus-scrape 禁止 prometheus 采集; @陈欢
  *	CCE-3388: 调整 节点的 job name & relabel cadvisor [node,pod] 标签和 kube-state-metrics 保持一致; @子华
* [DevOps]
  * CCE-3391: 修复 cce_stack_control.sh 计算目录 md5 方法; @陈欢


## 2021-04-13

### 功能

* [集群管理]
  * CCE-3364: CCE 支持竞价实例创建; @宏伟
  * CCE-3364: 节点进度展示添加等待就绪步骤; @宏伟
  * CCE-3364: 安全组文档和配置更新：去除 B-NAT 规则，合并 NodePort 规则; @宏伟
* [Serverless]
  * CCE-3404: Calculate pod conditions based on pod phase; @子超
  * CCE-3404: Use subnet in zoneA for serverless cluster regression; @子超
* [边缘集群]
  * CCE-3403: 云边集群支持部署metrics server; @思远
* [安全审计]
  * CCE-3398: CCE 安全组特殊设置说明; @宏伟
* [监控日志]
  * CCE-3407: 支持 BCI 节点 metrics 监控; @子华
  * CCE-3345: Add bci monitor targets 记录; @子超
  * CCE-3354: 规范 CCE Grafana Dashboard 命名; @陈欢
  * CCE-3416: 调整 coordinator request/limit; @陈欢
  * CCE-3354: cce-monitor-service HTTP Request dashboard; @陈欢
  * CCE-3401: master-service-discovery 支持按照集群大小采集 metrics: @子华
  * CCE-3388: 添加集群 prometheus 自身监控 & namespace-pod 面板 ns 过滤; @子华
  * CCE-3354: cce-monitor-service 暴露 cce_http_request_total metrics; @陈欢
  * CCE-3388: thanos 开启 hpa & 开启 cluster-controller 框架内 metrics; @子华
  * CCE-3416: Thanos Grafana Dashboard 增加 Prometheus MEM 和 CPU 使用; @陈欢
  * CCE-3388: 添加集群 meta-cluster & user-cluster overview grafana 面板; @子华
* [容器网络]
  * CCE-3330: CNI 部署支持 BCC/BBC 混布; @亚奇
  * CCE-3285: 释放 IP 忽略 BBC 已删除的错误; @亚奇

## 2021-04-15

### 功能

* [容器网络]
  * CCE-3420 斗鱼清理泄露 rule 工具
* [Serverless]
  * CCE-3407 bci 节点域名解析不出来支持跳过
  * CCE-3415 Sync pod from console regularly when pod updatedTime does not change
* [容器存储]
  * CCE-3430 开源版本 csi 插件部署 helm chart 兼容公有云/私有云
* [监控日志]
  * CCE-3352 Alertmanger Webhooks
  * CCE-3428 thanos query 开启数据去重功能
  * CCE-3433 cce-gateway 服务开启 metrics
  * CCE-3432 helm-service 服务开启 metrics
  * CCE-3423: app-service 暴露 prometheus metrics
  * CCE-3422: cluster-service 暴露 prometheus metrics
  * CCE-3423: CCE HTTP Request Grafana Dashboard
  * CCE-3434 调整 meta cluster prometheus grafana 面板
  * CCE-3401 master-service-discovery 修复 listInstance labelselector语法错误

## 2021-04-20

* [集群管理]
  * CCE-3419 kubelet硬驱逐配置 inodes
  * CCE-3443【集群管理】exist bbc cannot fill GPU instanceType
* [Serverless]
  * CCE-3415 Fix bci search by CCE cluster id
  * CCE-3415 Polish log and fix regularSyncInterval
  * CCE-3464 Return vk subnets as instance subnets in cluster extra info for serverless cluster
* [CCE@BEC]
  * CCE-3413 支持bec节点打CCE标签
  * CCE-3441 删除 bec lb 时返回404认为删除成功
  * CCE-3467 云边集群部署过程集成topology server
  * CCE-3438 云边集群部署集成service group controller
* [资源弹性]
  * CCE-3364 竞价实例创建问题修复
  * CCE-3364 节点组支持竞价实例创建
* [容器存储]
  * CCE-3430 支持在部署CSI插件helm chart中显式指定获取节点拓扑信息的方式
* [监控日志]
  * CCE-3173 报警格式修改
  * CCE-3462 删除 metrics path 标签
  * CCE-3419 cce-alert-webhook bugfix
  * CCE-3435 kvass sidecar 添加环境变量
  * CCE-3435 grafana 面板添加 go 进程监控
  * CCE-3444 cce 组件添加接口请求延时 metrics
  * CCE-3431 事件报警: 接入 HealthCheck 事件源
  * CCE-3173 alertmanager webhook bcm 事件类型修改

## 2021-04-22

* [集群管理]
  * CCE-3419 kubelet 监听 0.0.0.0
* [CCE@BEC]
  * CCE-3467 云边集群kube-proxy.conf特殊配置
  * CCE-3467 云边集群部署过程集成topology server
  * CCE-3467 云边集群topology server clusterrolebinding
  * CCE-3467 CCE-3468 云边集群部署集成topology server && coredns 使用服务拓扑
  * CCE-3467 CCE-3469 云边集群topology server 配置修改 && bec 节点兼容数据盘信息和GPU信息
* [容器网络]
  * CCE-3418 ClusterIP 校验与 kube-apiserver 对齐
* [资源弹性]
  * CCE-3364 竞价实例移入 cluster-service 实现
* [插件管理]
  * CCE-3321 插件管理与 2 个插件的插件化
* [监控日志]
  * CCE-3173 CCE 报警部署


## 2021-04-27

### 功能

* [集群管理]
  * CCE-3492: CA 开启选主; @宏伟
  * CCE-3517	CA Deployment 更新时只更新 node 参数 @chenhuan
  * CCE-3488 api 新增 specId 字段，GPU机型必须字段
  * CCE-3510: core-dns 和 metrics-server 增加 node-affinity 避免进自动扩缩容节点组
  * CCE-3458 节点资源预留 @liulin
* [Serverless]
  * CCE-3415	vk pod cache查询方式兼容console可能db主从不一致; @子超
* [监控日志]
  * CCE-3484	Master 节点提供独立部署 kube-state-metric 等方式; @子华
  * CCE-3344: 旧版本 thanos 下线 @chenhuan
  * CCE-3511 调整 cluster-controller 调和错误面板 @zihua
  * CCE-3423: app-service 增加 HTTP Metrics @chenhuan
  * CCE-3487 tf-operator 插件整合到 CCE @zihua
  * CCE-3513: auto-scaler 暴露 prometheus metrics @chenhuan

## 2021-04-29

* [集群管理]
  * CCE-3368	从instance controller中拆分出负责node labels/taints/cordon等针对k8s node的controller; @思远
  * CCE-3500	cluster-service提供修改节点组的接口; @思远
  * CCE-3364	竞价实例创建、竞价与主动释放; @宏伟
* [Serverless]   
  * CCE-1618	Fix resource mapping from instance to bci @叶子超
* [监控日志]
  * CCE-3534 Instance 报警增加 ErrorCode;@陈欢
  * CCE-3352 CCE 报警全地域开放 @宏伟
* [容器网络]
  * CCE-2528 集团云 EKS 容器网络联调 @亚奇
* [插件管理]
  * CCE-3478	插件helm修改与接入文档 @冀超
* [边缘集群]
  * CCE-3453	云边集群整体联调与提测 @潘思远  

