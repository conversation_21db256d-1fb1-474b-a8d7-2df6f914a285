# CCE 2020-10 功能

## 2020-10-13

### 功能

* [集群管理]
  * CCE IPv6 回归测试 @陈欢;
  * 多子网自动重试创建虚机 @启龙;
  * centos 安装 nvidia-docker-toolkit 原理 @陈欢;
  * OpenAPI文档整理 @冀超;
  * 后端新增同步 BCC 状态接口 @宏伟;
  * 新增 cce-alert-webhook @亚松;
  * 新增 VK 节点 @亚松;

* [容器网络]
  * 支持通过annotation改变blb udp health check string @思远;
  
* [Serverless]
  * 添加 iptables-ensurer 保证所有 chain 创建成功 @子超;
  * serverless 集群的apiserver 的blb添加对于443端口的listener @思远;

## 2020-10-20

### 功能

* [集群管理]
  * 同步 BCC 状态 @宏伟;
  * 优化节点删除后的事件展示 @宏伟;
  * 支持创建集群跳过 ClusterPodCIDR 冲突检查 @亚奇;
  * 异构资源: Nvidia Docker 支持 CentOS 操作系统 @刘霖;
  * Instance 支持绑定多 VPC Subnet, 解决单可用区资源授权问题 @启龙;
* [Serverless]
  * VK 支持 Terminal Resize @子超;
  * 删除 serverless  集群时清理service blb eip @思远;
  * 支持创建enable service controller的serverless集群 @思远;
  * CCE-2448 kube-proxy 使用 args 参数避免 cmdline 过长 @子超;
  * vk 在 kube-dns service 存在并且启用 kube-proxy 或 service-controller 的时候注入 DNS 配置到 Pod 内 @思远;
* [监控日志]
  * 优化服务画像打分逻辑, 异常条数/总条数 @亚松;
* [自动化测试]
  * InstanceGroup 测试集去除 CheckClusterInstance, 避免缩容导致的冲突 @陈欢
* [插件管理]
  * 修改 CoreDNS ResourceLimit 为 1 Core 1 G @陈欢
* [安全隔离]
  * CCE-2460 CVE-2020-14386 漏洞修复公告 @冀超
* [运维运营]
  * showX 安全组和路由表查询支持 V2 集群 @亚奇
  * MetaCluster 增加 Pod 和 Node 异常巡检报警 @陈欢
  * cce-event-collector 及 cluster-status-alert 修改为 HostNetwork @陈欢
* [OpenAPI/SDK]
  * 集群错误码 @冀超
  * V1 和 V2 OpenAPI 差异说明 @冀超

### BugFix

* [集群管理]
  * coredns service ip 概率被占用 @刘霖;
* [应用管理]
  * 分配 tty 时 stderr 不为 true, 解决 webshell 连接报错 @冀超;
* [Serverless]
  * CCE-2438 补充需要注入serverless集群service controller的环境变量 @思远
  * CCE-2438 修复 serverless 集群 service controller 启动脚本 && service controller 指定选主 namespace @思远
* [容器网络]
  * 修复老版 1.13 集群 cni 插件部署异常导致 Pod 创建失败 @亚奇;
* [权限审计]
  * 增加"对全部集群授权"时 clusterID = "" 校验 @陈欢;

## 2020-10-22

### 功能

* [集群管理]
  * 增加 CCE 支持 OS 接口 @启龙;
  * 支持用户添加 IaaS 自定义 Tag @宏伟;
* [自动化测试]
  * 通过 CronJob 将回归任务结果存入 Palo @陈欢;
* [Serverless]
  * CCE-2378 Remove bcc count pre-check for blb @子超;
  * CCE-2442 Support kube-proxy sidecar for virtual node @子超

### BugFix

* [集群管理]
  * CCE-1661 保证 CoreDNS 第一个部署, 避免 CoreDNS ClusterIP 被占用 @刘霖;
* [BCE-SDK]
  * 增加 bcesdk 最大重试次数为 5 次(太复杂, 不适合大改) @陈欢;

## 2020-10-27

### 功能

* [集群管理]
  * CCE-2489 节点部署事件细化 @宏伟
  * CCE-2330 集群详情页面的子网信息 宏伟
  * CCE-2518 deployer 支持不部署 CCM @思远
  * CCE-2518 deployer 支持基于任意机器部署 K8S @思远
  * CCE-2502 skipFilter=true 返回所有 OS 信息 @启龙
  * CCE-1661 支持 K8S 1.18 版本, APIVersion 为 Apps/V1 @刘霖
* [BCE-SDK]
  * CCE-2527 当 Request Header 无 x-bce-request-id 时, 使用随机串作为 RequestID @陈欢
* [插件管理]
  * 增加 web-terminal 插件 @陈欢
* [运维运营]
  * cce-status-alert 重构, 定义 alert.Interface 支持多种资源 @陈欢
  * 增加 Cluster/Instance Spec 数据库和 CRD 不一致检查报警 @陈欢
* [自动化测试]
  * CCE-2517 Add limited retry for CCEGatewayToken check case @子超

### BugFix

* [集群管理]
  * CCE-2526 修复 cluster-service application/json 格式返回报错内容 @启龙
  * CCE-2093 Use client-go to apply token to eliminate zombie kubectl process @子超

## 2020-10-29

### 功能

* [集群管理]
  * CCE-2488 规范事件返回错误信息 @宏伟;
  * CCE-2488 虚机创建失败错误类型定义 @宏伟;
  * CCE-2538 CCM 代码仓库迁移至 cce-stack @冀超;
  * CCE-2543 InstancePhase == running, 跳过 reconcile 流程 @陈欢;
  * CCE-2483 Cluster/Instance 数据库中存在, CRD 不存在时, 保证删除幂等 @陈欢;
* [Serverless]
  * CCE-2093 Use empty k8s version if proper version cannot be parsed @子超;
* [运维运营]
  * CCE-2542 添加 BFE 转发公有云 CCE 最佳实践 @亚奇;
  * CCE-2552 B 区机器使用 DaemonSet 初始化 rc.local 文件 @亚松;
  * CCE-2480 cce-palo-collector 增加 Cluster/Instance Step 采集 @启龙;

### BugFix

* [集群管理]
  * CCE-2503: 修复 core.go 中 SendRequest 死循环问题 @陈欢;
  * CCE-2483: Cluster/Instance 数据库存在, 但 MetaCluster 不存在时, 保证删除幂等 @陈欢.

