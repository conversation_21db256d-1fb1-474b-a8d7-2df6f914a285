# CCE 项目周报 - 2022-03-11 ~ 2022-03-18

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6533: [集群管理][集群升级] 规范 TaskName 英文描述
  * CCE-6533: [集群管理][集群升级] 规范 TaskName 英文描述
  * CCE-6531: [集群管理][集群升级] RetryCount=Max 后 WorkflowPhase = Failed
  * CCE-6531: [集群管理][集群升级] RetryCount=Max 后 WorkflowPhase = Failed
  * CCE-6515: [集群管理][集群升级] ListInstance 接口优先排序可升级节点
  * CCE-6502: [集群管理][集群升级] HKG 地域自动化回归 ClusterIPCIDR 调整
  * CCE-6518: [集群管理][集群升级] PreCheck 类型 Workflow 自动清理按创建时间
  * CCE-6517: [集群管理][集群升级] cluster-service 创建 workflow 时直接修改状态为 upgrading
  * CCE-6506: [集群管理][集群升级] 集群升级前置检查并行执行
  * CCE-6120: [集群管理][集群升级] 新增前置检查项不支持二进制 KubeProxy 升级
  * CCE-6494: [集群管理][集群升级] 调整升级 Node 后置检查等待时间
  * CCE-6494: [集群管理][集群升级] ErrorMessage 展示 UpgradeNode 失败详情
  * CCE-6486: [集群管理][集群升级] ListInstance 支持按 IPList 查询
  * CCE-6429: [集群管理][集群升级] Workflow 增加 Event 详情
  * CCE-6398: [集群管理][集群升级] Workflow Webhook 校验过滤 allow-multiple-within-cluster Label
  * CCE-6416: [集群管理][集群升级] Workflow.Interface 新增 NeedUpgradeCluster 方法, PreCheck 类型不更新集群状态
  * CCE-6337: [集群管理][集群升级] Workflow 自动清理各地域增加配置

* 子超
  * CCE-6390 [集群管理][集群升级] Bypass node health check for cluster without node
  * CCE-6390 [集群管理][集群升级] Check minimum csi bos plugin version for k8s 1.20 upgrade
  * CCE-6390 [集群管理][集群升级] Allow identical src and dst version for k8s version upgrade
  * CCE-6390 [集群管理][集群升级] Fix errorcode returned by WorkflowConfigToWorkflow
  * CCE-6390 [集群管理][集群升级] Disable post check for check tasks
  * CCE-6390 [集群管理][集群升级] Validate UpgradeMasterK8SVersionPreCheckConfig before commit

### 支持前置校验@启龙

* 启龙
  * CCE-6505 [集群管理][支持前置校验] bbc openapi & precheck scale limitation
  * CCE-6505 [集群管理][支持前置校验] create cluster config skip validation
  * CCE-6505 [集群管理][支持前置校验] 失败提示，说明文档

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 陈欢
  * CCE-6438: [集群管理][体验优化] montor-service 返回 worker 列表增加 kubeletVersion 和 needUpgrade 字段
  * CCE-6502: [集群管理][体验优化] Workflow 集群升级后端上线
  * CCE-3842: [集群管理][体验优化] 创建临时 kubeconfig API 文档

* 臧浩
  * CCE-6455:[集群管理][体验优化] CFC 批量删除节点超时

### Serverless集群

* 子超
  * CCE-6301 [集群管理][Serverless集群] Support 1.18.9-bilibili-mixprotocols version for serverless cluster

### 资源删除

* 亚奇
  * CCE-6499 [集群管理][资源删除] 容器化自定义集群 master ENI 兜底回收

### 新版本兼容

* 冀超
  * CCE-6158 [集群管理][新版本兼容] Node 部分 Label 兼容 k8s 迭代

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * 无进展

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * CCE-6504 [CCR][功能完善] 修复引入的distribution包错误

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6459 [容器网络][流量接入优化] 支持配置EIP计费方式
  * CCE-6459 [容器网络][流量接入优化] 修改HelmChart ReadeMe:BLB VIP 分配配置化
  * CCE-6459 [容器网络][流量接入优化] BLB VIP 分配配置化

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 可观测性

### cprom

* 子华
  * CCE-4976: [可观测性][cprom] control plane targets 封装 chart
  * CCE-4976: [可观测性][cprom] cprom 后端组件 helm chart 添加 vm-operator
  * CCE-4976: [可观测性][cprom] cprom 后端组件 helm chart 封装
  * CCE-4976: [可观测性][cprom] grafana admin password 校验 base64 编码 & cprom crd 添加 alias printcolumn
  * CCE-4976: [可观测性][cprom] 修复 namespace 被删掉后 monitorInstance 无法删除
  * CCE-4976: [可观测性][cprom] 添加 grafana datasource task

## 不规范提交

* author={wenmanxiang} commit={CCE-6498 [Task] 单测接入流水线中}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-136 [Task] 消息存储ES更新为线上 增加权限校验}
* author={liqilong} commit={CCE-6475 [Bug] 移入 BBC 替换 open api}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-123 [Task] 通知调用线上消息中心 增加重试机制 增加UserGroup通知}
* author={chenhuan} commit={CCE-6484: CCE 服务简介}
* author={chenhuan} commit={CCE-6484: CCE 服务简介}
* author={chenhuan} commit={CCE-6484: CCE 服务简介}
* author={chenhuan} commit={CCE-6484: CCE 服务简介}
* author={chenyun05} commit={Cloud-Prometheus-Product-29[Task] package search}
* author={chenyun05} commit={Cloud-Prometheus-Product-29[Task] package search}
* author={chenyun05} commit={Cloud-Prometheus-Product-29[Task] package search}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-121 [Task] 消息发送模板更新&防止service crash}
* author={liqilong} commit={CCE-6045 [Improvement] 【运营数据】 gpu_type 、 gpu_count}
* author={duzhanwei} commit={Cloud-Prometheus-Product-77 [Task] 【体验/bug】修改Grafana密码后登录新旧密码都失败}
* author={duzhanwei} commit={Cloud-Prometheus-Product-77 [Task] 【体验/bug】修改Grafana密码后登录新旧密码都失败}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-116 [Task] redis connection fix}
* author={duzhanwei} commit={Cloud-Prometheus-Product-106 [Bug] agent管理页面显示有误}
