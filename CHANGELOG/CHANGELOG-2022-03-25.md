# CCE 项目周报 - 2022-03-18 ~ 2022-03-25

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-6624: [集群管理][集群升级] 支持自定义 DrainNode 参数
  * CCE-6015: [集群管理][集群升级] DrainNode 参数配置和 kubectl drain node 保持一致
  * CCE-6015: [集群管理][集群升级] DrainNode 参数配置和 kubectl drain node 保持一致
  * CCE-4755: [集群管理][集群升级] WorkflowPhase 统一采用大写
  * CCE-6015: [集群管理][集群升级] DrainNode 参数配置和 kubectl drain node 保持一致
  * CCE-6604: [集群管理][集群升级] 车和家升级问题汇总, 更新 K8S Changelog
  * CCE-6596: [集群管理][集群升级] Node 升级对 cceInstanceList 参数去重
  * CCE-6596: [集群管理][集群升级] Node 升级对 cceInstanceList 参数去重
  * CCE-6586: [集群管理][集群升级] 增加 FailedPodOwnerReferencesSet, 记录升级前就失败的 Pod
  * CCE-6586: [集群管理][集群升级] 增加 FailedPodOwnerReferencesSet, 记录升级前就失败的 Pod
  * CCE-6597: [集群管理][集群升级] Failed Task 重试后重置状态为 Upgrading
  * CCE-6586: [集群管理][集群升级] 增加 FailedPodOwnerReferencesSet, 记录升级前就失败的 Pod
  * CCE-6588: [集群管理][集群升级] Workflow 关联 Cluster CRD 和数据库状态更新保证一致
  * CCE-4755: [集群管理][集群升级] Workflow OpenAPI 未上线, 暂时下线 HKG Workflow 回归
  * CCE-6444: [集群管理][集群升级] CCE 发布 Kubernetes 1.20 版本说明
  * CCE-6444: [集群管理][集群升级] CCE 发布 Kubernetes 1.18 版本说明
  * CCE-6502: [集群管理][集群升级] cluster-service 线上开启 EnableUpgradeWorkflow, ListCluster 返回 WorkflowID
  * CCE-6552: [集群管理][集群升级] GetCluster 集群详情增加 WorkflowID
  * CCE-6548: [集群管理][集群升级] ListCluster 增加 WorkflowID 过滤前置检查 WorkflowType
  * CCE-6548: [集群管理][集群升级] ListCluster 增加 WorkflowID 过滤前置检查 WorkflowType
  * CCE-6543: [集群管理][集群升级] KubeProxy 升级超时时间优化
  * CCE-6538: [集群管理][集群升级] 不支持升级 1.22, 暂时允许同版本升级, 方便测试
  * CCE-6538: [集群管理][集群升级] 不支持升级 1.22, 暂时允许同版本升级, 方便测试
  * CCE-6536: [集群管理][集群升级] TaskName 对外暴露 VPC IP
  * CCE-6533: [集群管理][集群升级] 规范 TaskName 英文描述
  * CCE-6533: [集群管理][集群升级] 规范 TaskName 英文描述

* 子超
  * CCE-6390 [集群管理][集群升级] Add pre-check for nfs-client-provisioner
  * CCE-6390 [集群管理][集群升级] Fix installed plugins check
  * CCE-6390 [集群管理][集群升级] Block k8s version upgrade for cluster with ai plugin installed
  * CCE-6009 [集群管理][集群升级] cleanup kubectl cache after apiserver upgrade
  * CCE-6390 [集群管理][集群升级] Beautify precheck message for node status check

* 亚奇
  * CCE-6610 [集群管理][集群升级] 车和家kube-proxy容器化升级 SOP

### 支持前置校验@启龙

* 启龙
  * CCE-6505 [集群管理][支持前置校验] 优化错误说明展示
  * CCE-6505 [集群管理][支持前置校验] 优化错误说明展示

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

### BBC OPEN API

* 启龙
  * CCE-5750 [集群管理][BBC OPEN API] open api 新建裸金属 打开开关

### 新版本兼容

* 冀超
  * CCE-6157 [集群管理][新版本兼容] 上线: Node 部分 Label 兼容 k8s 迭代

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * 无进展

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

* 亚奇
  * CCE-6600 [容器网络][VPC-CNI 支持 IP 池] 修复 BCC GetInstanceDetail 无法获取部分 BBC 机型信息
  * CCE-6406 [容器网络][VPC-CNI 支持 IP 池] 创建 ENI 失败抛出 event
  * CCE-6406 [容器网络][VPC-CNI 支持 IP 池] 低水位补充 IP

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6332 [容器网络][流量接入优化] LB Controller 定向开源: 修复ClientToken缺失
  * CCE-6587 [容器网络][流量接入优化] 兼容 CCM 预付费 EIP
  * CCE-6587 [容器网络][流量接入优化] 兼容 CCM 预付费 EIP
  * CCE-6534 [容器网络][流量接入优化] 修复 LB Service 转发规则未更新

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## 可观测性

### cprom

* 子华
  * CCE-4976: [可观测性][cprom] grafana 面板规范
  * CCE-4976: [可观测性][cprom] monitorAgent 添加 region 启动参数
  * CCE-4976: [可观测性][cprom] monitorInstance Grafana 配置确保设置上
  * CCE-4976: [可观测性][cprom] 删除 monitor instance Unknown 状态
  * CCE-4976: [可观测性][cprom] 调整 rescource-cluster values 配置
  * CCE-4976: [可观测性][cprom] 调整香港地区 cprom-service 副本数为 3
  * CCE-4976: [可观测性][cprom] 调整 free-v1 实例 limit 资源
  * CCE-4976: [可观测性][cprom] 修复 agent instance 更新状态不准确的问题
  * CCE-4976: [可观测性][cprom] 修复 cprom-controller 状态未更新错误
  * CCE-4976: [可观测性][cprom] cprom-manager 以非 hostNetwork 运行
  * CCE-4976: [可观测性][cprom] 修改 cprom 生成 crds 配置
  * CCE-4976: [可观测性][cprom] 修改 cprom gztest gz 地区的 values 配置
  * CCE-4976: [可观测性][cprom] 修改 resource-controller 启动参数
  * CCE-4976: [可观测性][cprom] 添加免费版监控实例
  * CCE-4976: [可观测性][cprom] 添加 gz 地区的 values 配置
  * CCE-4976: [可观测性][cprom] 调整 configmap 配置格式
  * CCE-4976: [可观测性][cprom] 系统默认采集任务添加
  * CCE-4976: [可观测性][cprom] agent deployer 部署前检测 plugin 是否安装

## 容器存储

### CSI

* 子超
  * CCE-6126 [容器存储][CSI] Optimize performance of csi plugin

## 不规范提交

* author={wangyufeng06} commit={Cloud-Prometheus-Product-165 [Bug] 告警通知有时会重复发送}
* author={chenyun05} commit={Cloud-Prometheus-Product-164[TASK] fwh conf}
* author={wenmanxiang} commit={CCE-6609 [Task] 完善关于 CCE 发布流程的说明.md}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-158 [Bug] 告警不生效}
* author={liqilong} commit={CCE-6108 [Improvement] bcc 联动创建eip支持 eip name}
* author={chenyun05} commit={Cloud-Prometheus-Product-155[TASK] template}
* author={duzhanwei} commit={Cloud-Prometheus-Product-77 [Task] 【体验/bug】延迟5秒修改admin密码}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-154 [Task] 香港上线配置}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-151 [Task] 苏州上线配置}
* author={chenyun05} commit={Cloud-Prometheus-Product-153[Task] bd conf}
* author={chenyun05} commit={Cloud-Prometheus-Product-153[Task] bd conf}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-151 [Task] 苏州上线配置}
* author={chenyun05} commit={Cloud-Prometheus-Product-152[Task] bj conf}
* author={chenyun05} commit={Cloud-Prometheus-Product-152[Task] bj conf}
* author={chenyun05} commit={Cloud-Prometheus-Product-152[Task] bj conf}
* author={wangzhuyun} commit={CCE-6553 [New Feature] 云原生AI组件私有化部署}
* author={duzhanwei} commit={Cloud-Prometheus-Product-149 [Task] 增加用户白名单查询}
* author={duzhanwei} commit={Cloud-Prometheus-Product-149 [Task] 增加用户白名单查询}
* author={zhanghong15} commit={Cloud-Prometheus-Product-145 modify cprom chart name}
* author={zhanghong15} commit={Cloud-Prometheus-Product-145 add cprom_build profile in ci.yml}
* author={zhanghong15} commit={Cloud-Prometheus-Product-145 add cprom_build.sh}
* author={duzhanwei} commit={Cloud-Prometheus-Product-101 [Bug] 监控实例后端返回异常，前端抛出success工单}
* author={duzhanwei} commit={Cloud-Prometheus-Product-138 [Bug] alert-hook在推数据失败的时候不应该返回200}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-133 [Bug] 根据告警名称筛选结果有误}
* author={chenhuan} commit={CCE-5883: CHANGELOG-2022-03-18.md}
* author={wenmanxiang} commit={CCE-6191 [Task]  获取镜像相关接口开发}
* author={yezichao} commit={Merge branch 'generic-type' into master}
* author={yezichao} commit={CCE-6126 Retry internally on ControllerUnpublishVolume}
* author={yezichao} commit={CCE-6126 Fix build pipeline}
* author={yezichao} commit={CCE-6126 Wrap long operations with internal retry on Aborted}
* author={yezichao} commit={CCE-6127 Use larger timeout when waiting for bosfs container}
* author={yezichao} commit={CCE-5555 Bump patch version}
