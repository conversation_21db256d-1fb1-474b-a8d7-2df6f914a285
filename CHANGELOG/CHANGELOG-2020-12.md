# CCE 2020-11 功能

## 2020-12-01

### 功能

* [集群管理]
  * CCE-2644: 节点列表增加按节点组筛选功能; @思远
  * CCE-2818: 删除集群跳过清理 default/kubernetes Service @陈欢
  * CCE-2822: 删除节点组接口添加是否释放节点对应的 IaaS 资源参数; @陈欢
  * CCE-2627: 添加 webhook helm chart 文件，仅开启 sandbox 和 gztest; @宏伟
  * CCE-2627: cce-apiserver-webhook 修改 service name, 使用 daemonset 部署; @宏伟
  * CCE-2679: cluster hostname 模式，将 nodename 从 instanceName 改为 hostname @宏伟
* [容器存储]
  * CCE-2836: 去掉 bos-plugin csi 对于挂载目录 meta 数据检查; @思远
* [行业云私有化]
  * CCE-1661: 增加私有化 cce-stack charm; @刘霖
  * CCE-2796: 私有化 hzpro 环境 e2etest values.yaml 完成; @刘霖
  * CCE-2796: 私有化 on K8S, cluster-service 联调完成, 配置更新; @陈欢
* [容器网络]
  * CCE-2737: 基于 netperf 的网络性能测试; @宏伟
* [自动化测试]
  * CCE-2162: 增加 Agile 指定 Case 运行脚本; @丽丽
  * CCE-2804: 默认关闭边缘集群创建的 e2etest; @思远
  * CCE-2632: 解决 e2e 测试集群容器网络冲突问题; @陈欢
  * CCE-2790: PodBackend LB Service 增加 e2etest; @冀超
* [运维运营]
  * CCE-2796: 临时去除 InstanceOS internal_cloud 报警; @陈欢
  
## 2020-12-03

### 功能

* [集群管理]
  * CCE-2627: 开启线上 validate-webhook; @宏伟
  * CCE-2847: 集群管理增加 InstanceQuotaLimitExceeded 和 HostNameDuplicateException 标准报错; @宏伟
* [容器存储]
  * CCE-2857: CSI helm chart 设置 kubelet 数据目录默认为 /home/<USER>/kubelet; @思远
* [插件管理]
  * CCE-2840: 新增 LBController 插件 Helm Chart; @冀超
* [容器网络]
  * CCE-2840: Service 用 UDP 接口时, 设置健康检查报文内容; @冀超
* [容器存储]
  * CCE-2836 Be compatible with csi spec 1.2; @子超
* [自动化测试]
  * CCE-2853: 增加基于 ping 的自动化测试; @宏伟
  * CCE-2832: 优化 Cluster AutoScaler 自动化测试; @丽丽
  * CCE-2858: 新增集群完整功能回归 CaseGroup, 用于二代 BBC 验证; @丽丽
  
## 2020-12-10
### 功能

* [集群管理]
  * CCE-2883: Instance 磁盘类型支持 HDD @启龙
  * CCE-2679: 修复托管 master Hostname 冲突问题 @宏伟
  * CCE-2855: 添加kubelet数据目录hex编码到instance label @思远
  * CCE-1661: CCE ETCD/Kubelet/Docker 数据目录切换为 /home/<USER>
  * CCE-2879: InstanceID 不存在时, 保证 moveOut=true 亦可以移出 @宏伟
  * CCE-2872: CCE InstanceType 检查同时支持 OpenAPI 及 LogicAPI @启龙
  * CCE-2781: CCE 和 BCC/BBC InstanceType/OS 前后端保持一致; @启龙
* [容器网络]
  * CCE-2844: 添加 calico felix 部署，支持 netpol @亚奇
  * CCE-2853: 网络性能自动化测试工具优化测试速度 @宏伟
  * CCE-2840: 删除 DescribeAppBLB 接口中实际不存在的 SubnetID @冀超
* [插件管理]
  * CCE-2679: ClusterAutoScaler 组件副本数设置为 2 @宏伟
* [监控日志]
  * CCE-2386: HealthCheck 健康检查上线 @亚松
* [容器存储]
  * CCE-2881: csi helm chart 支持 kubelet 数据目录异构集群 @思远
* [应用管理]
  * CCE-2863: AppService DeployYAML 增加日志, 定位 crash 问题 @陈欢
* [监控日志]
  * CCE-2880: 部署 Prometheus/Grafana 时增加 CDS FlexVolume @亚松
* [权限审计]
  * CCE-2824: ClusterService 添加统主子用户鉴权入口 @启龙
* [Serverless]
  * CCE-2482: Be compatible with user selected ClusterBLBVPCSubnetID for serverless cluster @子超
* [行业云私有化]
  * CCE-2870: 增加 cce-stack JUJU Stack @刘霖
  * CCE-2867: cce-plugin-config 使用独立 ConfigMap, 便于自定义私有化环境镜像 @陈欢
  * CCE-2864: 定义 cce infrastructure package, 修改 deploy/chart 目录至 infra @陈欢
* [运维运营]
  * CCE-2679: cce 计费统计添加 cds @宏伟
  * CCE-2808: 增加 /etc/crontab, 用于 /home/<USER>/cce/ 日志定期清理, 保留 15 天日志 @陈欢

## 2020-12-15

### 功能

* [集群管理]
  * CCE-2885: CCE 支持 NodeLocalDNS; @亚奇
  * CCE-2380: Fix possible inconsistent between signedHeadersToString and toCanonicalHeaderString; @子超
* [IaaSProvision]
  * CCE-2874: 提供 CCE 不支持的 InstanceType 接口; @启龙
* [行业云私有化]
  * CCE-2870: Registry 支持 HTTPS; @刘霖
  * CCE-2870: cce-stack 部署 K8S JuJu Charm; @刘霖
  * CCE-2921: cluster-service 公私代码配置统一; @陈欢
  * CCE-2919: 无 kubeproxy 情况下支持使用 serviceaccount 方案; @陈欢
  * CCE-2915: Plugin Image 配置解耦, 支持不同环境自定义 Image Name; @陈欢
* [容器网络]
  * CCE-2773: showx 新增路由表冲突检查工具; @宏伟
  * CCE-2737: 新增网络性能自动测试 Helm Charts; @宏伟
* [插件管理]
  *	CCE-2837: 新增 cce-lb-controller 的 Helm Chart; @冀超
  * CCE-2884: 新增根据 kubelet dir 打 label helm chart, 解决 CSI kubelet 数据目录混布兼容性问题; @思远
* [运维运营]
  * CCE-2737: 修复 OIDC 集群获取 kubeconfig 失败; @亚松
  * CCE-2909: cce-status-alert Node NotReady 过滤 Unscheduler; @陈欢
* [自动化测试]
  * CCE-2838: 新增 CCM service-controller 的 e2etest; @冀超

## 2020-12-17

### 功能

* [集群管理]
  * CCE-2870: 支持 CPU 独占绑核; @刘霖
  * CCE-2870: 支持 K8S 1.14.9 小红书 POC; @刘霖
  * CCE-2923: 添加标准错误 InstanceNoStock; @宏伟
  * CCE-2923: 添加标准错误 EIPByTrafficBandWidthQuotaExceeded; @宏伟
  * CCE-2937: InstanceController/CCM 调大 NodeWorker 至 400; @陈欢
  * CCE-2931: 节点组读接口返回添加 deletedAt 用于表示删除中的节点组; @思远
* [应用管理]
	* CCE-2816: cce-app-service 增加日志, 定位空指针; @陈欢
* [行业云私有化]
  * CCE-2928: imageSecret HelmChart 设置独立开关; @陈欢
* [容器网络]
  * CCE-2916: cce-lb-controller 支持 IPv6; @冀超
  * CCE-2896: cce-lb-controller 支持 Node 后端; @冀超
  * CCE-2925: cce-lb-controller LoadBalancer Service 增加 BLB 类型 Annotation; @冀超
* [容器存储]
  * CCE-2910: cds-plugin ControllerGetCapabilities 实现; @思远
  * CCE-2910 csi 插件通过 NodeName 提供 NodeID，通过 Node ProviderID 获取 InstanceID; @思远
* [运维运营]
  * CCE-2917: status-alert 修复 RetryCount < 10 耗时超过 10 min, 导致漏报问题; @陈欢

## 2020-12-22

### 功能

* [集群管理]
  * CCE-2870 etcd 参数优化; @刘霖
  * CCE-2927 标准错误检查框架; @宏伟
  * CCE-2927 添加账户余额查询接口; @宏伟
  * CCE-2923 修改image client入口参数，更通用; @宏伟
  * CCE-2873 BCC/BBC 机型、OS 检查工具 主逻辑; @李启龙
  * CCE-2927 检查子网 IP 不足，检查 instance 配额的实现; @宏伟
  * CCE-2955 serverless 集群instance检查master blb not ready时，sleep 3s，降低重试速度; @思远
* [行业云私有化]
  * CCE-2953 CSI cdsplugin 去除cce api的依赖，支持在自建集群中运行; @思远
* [容器网络]
  * CCE-2935 增加高级选项部署cce-lb-controller; @冀超
  * CCE-2801 修复 ipvlan cni 下，master 访问 pod 不通的问题 @亚奇
* [运维运营]
  * CCE-2941 调大rest client QPS/Burst 参数; @思远
* [文档]
  * CCE-2958 更新API文档; @李启龙
  
## 2020-12-24

### 功能

* [集群管理]
  * CCE-2873 iaas check gztest 上线; @启龙
  * CCE-2852: 补充Serverless集群功能点; @子超
  * CCE-2955 延长serverless集群master等待etcd ready时重试间隔; @子超
  * CCE-2962 cluster name 校验与前端提示对齐 & 开放centos8，Ubuntu18 @启龙
* [容器存储]
  * CCE-2953 csi plugin helm chart 支持设置镜像和服务endpoint; @思远
* [自动化测试]
  * CCE-2950 cce-lb-controller e2e-test修改; @冀超 
* [文档]
  * CCE-2852 更新容器网络总结; @亚奇

## 2020-12-29

### 功能

* [集群管理]
  * CCE-2977: kube-api-qps 参数默认 500;@陈欢
  * CCE-2927: 集群 quota 去除集群 ID 限制;@宏伟
  * CCE-2927: 添加 cluster/node quota 检查;@宏伟
  * CCE-2873: 修改user-cluster k8sclient qps、timeout;@启龙
  * CCE-2985: 添加node ready的检查时间 && 打标作为部署的部署;@思远
* [集群部署]
  * CCE-2870: 移除软驱逐配置;@刘霖
  * CCE-2870: 支持跳过 NFS 安装;@刘霖
  * CCE-2870 移除 apiserver 无效配置;@刘霖
  * CCE-2870: docker TasksMax=infinity;@刘霖
  * CCE-2870 kubelet 启动参数使用 --config;@刘霖
  * CCE-2852: 设置 evictionHart memory 为绝对值;@陈欢
  * CCE-2870: 已创建文件系统的磁盘，跳过磁盘 mount;@刘霖
* [容器网络]
  * CCE-2903: CCM 使用 V2 OpenAPI;@冀超
  * CCE-2982: 添加 BBC 网络相关 API;@亚奇
  * CCE-2956: 大客户 SOP 添加网络相关;@亚奇
  * CCE-2979: agent 仅 list-watch 本节点;@亚奇
* [容器存储]
  * CCE-2986: 默认部署 CSI 插件使用 HostNetwork;@思远
* [插件管理]
  * CCE-2966: CSI cdsplugin 构建发布流程集成到流水线;@启龙
* [运维运营]
  * CCE-2893: 减少 cce 日志保留时间;@陈欢
  * CCE-2873: alert client AtUsers 方法;@启龙

## 2020-12-31

### 功能

* [集群管理]
  * CCE-2905: 增加用户余额校验;@宏伟
  * CCE-2905: ListENIs 请求失败随机回退;@宏伟
  * CCE-2989: kubelet 配置格式使用 --config;@刘霖
  * CCE-2985: 提高节点组移入 Instance 并发度;@思远
  * CCE-2954: 增加创建 BCC VPC Config 的参数校验;@冀超
  * CCE-2985: UpdatePhase 状态转换限制 Running 回滚;@思远
  * CCE-2992: controller 对 running 对象不做任何操作;@陈欢
  * CCE-2985: 修改 Meta 集群组件资源 Limit，添加节点组缩容选择节点的日志打印;@思远
* [资源弹性]
  * CCE-2968: CA 添加默认镜像;@思远
* [容器网络]
  * CCE-2985: ccm添加日志排查性能瓶颈;@思远
  * CCE-2985: CCM 添加 Instance Cache，减少请求 OpenAPI 次数;@思远
* [容器存储]
  * CCE-2966: csi-cdsplugin Dockerfile;@思远
  * CCE-2966: 修改脚本中 csi-cdsplugin 名称和流水线配置一致;@思远
* [自动化测试]
  * CCE-2838: lb-controller test cases bugfix;@冀超
* [运维运营]
  * CCE-2996: /etc/crontab 添加 run user;@陈欢
