# CCE 2021-01 功能

## 2021-01-05

### 功能

* [集群管理]
  * CCE-2997 暂时关闭 Instance 一致性检查 @陈欢
  * CCE-2905 instance yaml 查询和更新, 仅 cluster-service 接口实现 @余宏伟
  * CCE-2838 修改app service vip @李启龙
  * CCE-2985 提高节点组移入节点并发度 @潘思远
  * CCE-2985 ccm 添加instance cache，减少请求openapi次数@潘思远
  * CCE-2954 VPC-Config BCC创建参数校验 @冀超
  * CCE-2905 修改账户余额检查 @余宏伟
  *  CCE-2985 修改meta集群组件资源limit，添加节点组缩容选择节点的日志打印 @潘思远
* [自动化测试]
  * CCE-2838 lb-controller e2e-test 线上集成测试 @冀超
* [文档]
  * CCE-2867 小红书分析 @陈欢


## 2021-01-07

### 功能

* [集群管理]
  * CCE-3001 CCM Node与Route的回归测试 @冀超
  * CCE-2905 Instance crd 敏感字段返回 ****** @余宏伟
  * CCE-2905 Instance crd 不显示敏感字段，不返回冗余字段 @余宏伟
  * CCE-2713 Add helm chart for virtual kubelet @叶子超
  * CCE-2974 提供一个instanceGroup crd struct的get和update接口 @潘思远
* [容器存储]
  * CCE-2997 清理hdfs/bos命令更新 @叶子超
* [行业云私有化]
  * CCE-3000 公、私有云统一：配置 @李启龙
* [运维运营]
  * CCE-3000 添加 timeout 报警通知 @李启龙
* [文档]
  * CCE-2936 小红书新任务记录


## 2021-01-12

### 功能

* [集群管理]
  * CCE-2998 CCM 请求精简 @冀超
  * CCE-3035 节点列表支持内网IP、实例ID模糊搜索 @李启龙
  * CCE-3029 节点组crd资源update时支持patch @潘思远
  * CCE-2905 根据 gorm tag 将 struct 转成 map, 便于直接通过 struct 更新零值到 DB @余宏伟
  * CCE-2989 kubelet配置文件切换为 --config @刘霖
* [容器存储]
  * CCE-3023 csi cdsplugin 部署 helm chart 默认开启节点亲和性 @潘思远
* [行业云私有化]
  * CCE-3010 公私同构，cce-service conf @李启龙
* [文档]
  * CCE-2976 阿里云容器安全调研 @潘思远
  * CCE-2997 小红书任务总结 @陈欢

### BugFix

* [回归测试]
  * CCE-2989 fix: 动态配置导致回归失败 @刘霖

## 2021-01-14

### 功能

* [集群管理]
  * CCE-3006  OS 排序 & 默认值 @李启龙
  * CCE-2969 添加 bcc tag SDK @余宏伟
  * CCE-2998 CCM增加cce-gateway环境变量启动参数 @冀超
  * CCE-3033 CCM拆出Node-Controller到Instance-Controller @冀超
  * CCE-3037 节点组删除时未分发状态的虚机清理复用del provider && del provider 添加批量删除虚机interface实现 @潘思远
* [回归测试]
  * CCE-2998 BLB接口不稳定临时关闭gz-test的回归 @冀超
  
## 2021-01-19

### 功能

* [集群管理]
  * CCE-3006: 镜像列表结果为空不报错;@启龙
  * CCE-2969: 新建 BCC 添加默认 TAG;@宏伟
* [Serverless]
  * CCE-2713: Support apiserver endpoint override;@子超
* [行业云私有化]
  * CCE-3009 修复吉利域名超时跳转问题;@刘霖
  * CCE-3010: cce-service 配置 jraft 端口;@启龙

## 2021-01-21

### 功能

* [集群管理]
  * CCE-2905: 新增 instance crd 更新校验;@宏伟
  * CCE-3013: 支持虚机 Iaas 资源 owner tag 打标;@思远
  * CCE-3013: 先 unbind instance tag 再删除 iaas 层资源;@思远
  * CCE-3013: 移动 Tag provider 的包到 pkg，方便 service 复用;@思远
  * CCE-3047: 集群额外信息增加 master BLB 和 LB service 子网信息;@宏伟
* [CCM容器化]
  * CCE-3034: Deploy 支持关闭 CCM 配置;@冀超
* [容器网络]
  * CCE-3035: 拆分 route-controller;@冀超
  * CCE-3075: 增加等待 AppBLB 状态为 Available 操作;@冀超
  * CCE-3053: 限制 route-controller 的 memory limit;@亚奇
* [容器存储]
  * CCE-3068: cdsplugin dettach 前检查 cds 状态，如果已经 avaliable 则直接返回成功;@思远
* [容器安全]
  * CCE-2976: 补充阿里云容器配置安全的说明;@思远
* [行业云私有化]
  * CCE-3009: 新增私有化数据库记录;@刘霖
* [性能测试]
  * CCE-3069: 增加性能测试环境的 values.yaml;@庭丽
* [回归测试]
  * CCE-3033: 暂时关闭 lb-service-e2e-test;@冀超

## 2021-01-26

### 功能

* [集群管理]
  * CCE-3093: 增加实名认证预检查;@宏伟
  * CCE-3044: 节点列表支持按状态筛选;@宏伟
  * CCE-3080: Add syntax check for instance labels and taints;@子超
  * CCE-3059: 修复 Instance List 接口查询未创建完成的集群的 kubeconfig;@宏伟
  * CCE-3013: CreateMachine 后把 status copy 到原来的 instance 的 status;@思远
* [IaaSProvision]
  * CCE-3003: 增加 CCE 支持 ImageType 接口;@启龙
  * CCE-3007: 询价接口 Instance InstanceType 兼容;@启龙
* [容器网络]
  * CCE-2983: 支持部署 BCC 辅助 IP CNI;@亚奇
  * CCE-3081: LB Service 使用 Finalizer;@冀超
  * CCE-2983: 网段检查支持二代 BBC ENI 模式;@亚奇
* [应用管理]
  * CCE-3081: app-service 修复单测错误;@冀超
  * CCE-3042 Decouple helm user with image user;@子超
  * CCE-3042: Bugfix for standalone helm user operation;@子超
* [行业云私有化]
  * CCE-3011: CCE V2 接口发布;@刘霖
  * CCE-3059: console-hub 配置管理;@宏伟
  * CCE-3052: 私有化环境支持 web shell;@刘霖
  * CCE-3067: 新增私有化创建集群回归测试;@陈欢
  * CCE-2734: 将 helm service 发布到 juju charm;@陈欢
  * CCE-2734: B 区镜像更新 gztest-drill value.yaml;@陈欢
  * CCE-3052: Add nginx conf for cce webshell in private-cloud;@刘霖
  * CCE-2734: Drill 环境增加 BCC/TAG/BLB/EIP 等 OpenAPI Service/Interface;@陈欢
  * CCE-3083: serivce cluster-ip 和 node 同段, 手动指定 kubernetes service cluster-ip;@陈欢
* [回归测试]
  * CCE-3089: e2etest 设置 dnsPolicy 为变量, 私有化环境均为 dnsPolicy: default;@陈欢

## 2021-01-28

### 功能

* [应用管理]
  * CCE-3042: Add reserved username;@子超
  * CCE-3042: Add api doc for AddAccount and UpdatePassword in helm service;@子超
* [行业云私有化]
  * CCE-3011: cce-gateway 配置;@刘霖
  * CCE-3011: 镜像列表、推送脚本维护;@刘霖
  * CCE-3011: pause image 地址可配置;@刘霖
  * CCE-3027: middleware 适配私有化环境;@宏伟
  * CCE-3105: 私有化环境增加 GET ENI 接口;@陈欢
  * CCE-3103: 私有化环境新增 TAG Interface;@陈欢
  * CCE-3027: cce-service 私有化 conf 更新;@宏伟
  * CCE-3027: 账户余额和实名认证检查适私有化环境;@余宏伟
  * CCE-3107: 私有化cce-service 的endpoint 配置;@启龙
  * CCE-3107: Cluster EIP 支持 PurchaseType 参数;@启龙