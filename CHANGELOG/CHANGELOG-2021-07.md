# CCE 2021-07

## 2021-07-06

* [集群管理]
  * CCE-3984 k8s 1.20.8 版本支持
  * CCE-4005: ListInstance 支持特殊传参返回 FloatingIP 等字段
  * CCE-3491: 规范 meta-cluster annotation, 区分 cluster 和 instance
* [插件管理]
  * CCE-4061 允许集群部署时安装csi-bos插件
  * CCE-4003 查询集群 Ingress-Class 列表
  * CCE-3875:回归开启cloud node controller集群
  * CCE-4006 增加查询所有 IngressClass 的 Ingress 接口
  * CCE-4016 避免NGINX-Ingress-Controller同命名空间多实例部署冲突
* [应用管理]
  * CCE-4007 Secret 列表接口支持以 SecretType 过滤查询
* [容器网络]
  * CCE-3955 Bump CNI version to v1.2.3
* [AI]
  * CCE-3984 mps 改成前台运行
  * CCE-4015 volcano change log
* [EKS]
  * CCE-3491: EKS gc-controller 日志优化
  * CCE-3898: 优化 instance-gc-manager 并发, 避免多余 goroutine
  * CCE-4000: Cluster 增加 Annotation 可以跳过 gc-controller 处理
* [行业云私有化]
  * CCE-3827 武汉银商私有化
  * CCE-3414 行业云3.0集测部署联调
  * CCE-4062 [New Feature] 银商 e2e test
* [Serverless]
  * CCE-3758 Support subnet option of vk in serverless master
* [监控日志]
  * CCE-3924: CCE 监控
  * CCE-3985: 云原生监控调研
  * CCE-4031 logrule 连通性校验
  * CCE-4018 cce-logrule controller
  * CCE-4048 cce-log-operator helm 插件化
  * CCE-4042 logrule CRD 生成 fluentd 配置
  * CCE-4048 cce-log-controller => cce-log-operator 并添加 daemonset 部署yaml

## 2021-07-08

* [集群管理]
  * CCE-4074: 新建节点, 创建 BCC 时候后支持回滚
  * CCE-4074: 支持虚机创建失败后, CCE 回滚删除 Instance
  * CCE-3827 fix: kube-proxy bind address 以及先于kubelt部署
  * CCE-3886 [Improvement] 移入节点新增 tag 无效 cluster-controller
* [插件管理]
  * CCE-4077 修改 cce-rdma-plugin node-selector
  * CCE-4077 默认部署node-feature-discovery插件
  * CCE-4076 LB Controller & Ingress Controller 增加使用 Tag 开关
* [行业云私有化]
  * CCE-3827 武汉银商私有化
  * CCE-3827 私有化询价接口不传 specId
  * CCE-4057 [New Feature] 银商环境开关变量
  * CCE-4057 [New Feature] 银商环境 cluster infra 适配
* [监控日志]
  * CCE-3852 日志组件文档
* [EKS]
  * CCE-4075: instance-gc-manager 配置读取优化
  * CCE-4071: gc-manager 操作前检查 Instance 确实存在
  * CCE-3921: gc-controller 避免重复处理已经被 Drain Instance
  * CCE-4078: gc-manager 增加 sync.Map 避免 Instance 重复进入队列
  * CCE-4067: BID Instance 的 Drain 和 Delete CCE Instance 解耦
  * CCE-4071: gc-manager 处理前确保 Instance 在 meta-cluster 存在
  * CCE-3898: gc-manager Select 和 Process 解耦, 采用生产者-消费者模式
  * CCE-4072: 新增 Annotation kubernetes.io/cce.cluster.not-delete-instance-by-gc-controller, 不被 gc-manager 处理

