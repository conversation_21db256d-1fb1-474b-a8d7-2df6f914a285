# CCE 项目周报 - 2022-02-11 ~ 2022-02-18

## 说明

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队同学及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 集群管理@陈欢

### 集群升级@陈欢

* 陈欢
  * CCE-4755: [集群管理][集群升级] 设计文档补充优化, 方便 QA 补充自动化 case
  * CCE-4755: [集群管理][集群升级] 设计文档补充优化, 方便 QA 补充自动化 case
  * CCE-6131: [集群管理][集群升级] 统一 InstanceTask 返回 Result 格式
  * CCE-6131: [集群管理][集群升级] WorkflowController 使用 IaaSConfig 初始化
  * CCE-6130: [集群管理][集群升级] Cluster 升级 Workflow, 包含 Master 和 Node
  * CCE-6120: [集群管理][集群升级] 增加 KubeProxy 前置校验, 兼容 KubeProxy 二进制部署情况
  * CCE-6125: [集群管理][集群升级] Workflow 支持 Paused 暂停
  * CCE-6125: [集群管理][集群升级] Workflow 支持 Paused 暂停
  * CCE-6030: [集群管理][集群升级] KubeProxy 升级暂时放入 Master 升级流程
  * CCE-6119: [集群管理][集群升级] Nodes 升级后置检查
  * CCE-6062: [集群管理][集群升级] 容器化 Master 升级 1.20 删除 --runtime-config=settings.k8s.io/v1alpha1=true 配置
  * CCE-6009: [集群管理][集群升级] 容器化 Master 通过替换 ImageID 实现升级
  * CCE-6112: [集群管理][集群升级] Task 开始后更新 StartTime 和 Phase=Upgrading
  * CCE-6112: [集群管理][集群升级] 容器化 Master 升级后置状态检查
  * CCE-6062: [集群管理][集群升级] kube-apiserver 升级 1.20 删除 --runtime-config 中 settings.k8s.io/v1alpha1=true
  * CCE-6062: [集群管理][集群升级] kube-apiserver 升级 1.20 删除 --runtime-config 配置
  * CCE-6104: [集群管理][集群升级] 二进制 Master 升级后检查 kubectl get cs
  * CCE-6104: [集群管理][集群升级] 二进制 Master 升级后置检查
  * CCE-6096: [集群管理][集群升级] UpdateWorkflow 接口 Demo
  * CCE-6093: [集群管理][集群升级] 取消升级删除 Workflow 接口 Demo
  * CCE-6088: [集群管理][集群升级] 查询集群可升级 NodeList 接口 Demo
  * CCE-6049: [集群管理][集群升级] Nodes 并发升级实现
  * CCE-6049: [集群管理][集群升级] Nodes 并发升级实现
  * CCE-6049: [集群管理][集群升级] Node 升级按照 BatchSize 进行 Task 拆分
  * CCE-6061: [集群管理][集群升级] Nodes 并行升级实现

* 子超
  * 无进展

### 支持前置校验@启龙

* 启龙
  * CCE-6111 [集群管理][支持前置校验] 集群操作前置检查 init

* 陈欢
  * 无进展

### 支持异常重试@启龙

* 启龙
  * 无进展

### 支持 Agent 部署@霖皇

* 霖皇
  * 无进展

* 陈欢
  * 无进展

### 节点组优化@冀超

* 冀超
  * 无进展

* 雷若风
  * 无进展

* 庭丽
  * CCE-6046 [集群管理][节点组优化] 节点移出节点组支持移除选项回归场景
  * CCE-6046 [集群管理][节点组优化] 节点移出节点组支持移除选项回归场景

### NPD 故障自愈@苗永昌

* 苗永昌
  * 无进展

* 臧浩
  * 无进展

### 斗鱼升级 V2@陈欢

* 陈欢
  * 无进展

### 体验优化@启龙

* 启龙
  * 无进展

* 冀超
  * CCE-6158 [集群管理][体验优化] 阻止自定义集群 ClusterBLB 建在专属集群上

* 陈欢
  * CCE-6160: [集群管理][体验优化] 预付费节点释放删除 kubelet.conf 防止重启后加入

## 云原生 AI@王竹云

### 数据集管理@王竹云

* 王竹云
  * 无进展

* 雷若风
  * 无进展

### GPU Manager支持MIG虚拟化@康志强

* 康志强
  * 无进展

### cGPU内核实现产品化@王竹云

* 王竹云
  * 无进展

### ai-native产品功能公私有云拉齐@王竹云

* 王竹云
  * 无进展

* 康志强
  * 无进展

* wangxingqi
  * 无进展

### 最佳实践@雷若风

* 雷若风
  * 无进展

## 组件管理@冀超

## 云原生监控@子华

### CProm 产品上线@子华

* 子华
  * 无进展

* 占伟
  * 无进展

## CCE 规模化@贺龙华

### CCE 规模化@贺龙华

* 贺龙华
  * 无进展

* 陈欢
  * 无进展

* 林战波
  * 无进展

* 周珂
  * 无进展

## 行业云私有化@霖皇

### 行业云交接 DET@霖皇

* 霖皇
  * 无进展

### AI私有化-百舸@霖皇

* 霖皇
  * CCE-5822 [行业云私有化][AI私有化-百舸] 更新bbc openapi 配置、镜像

* 王竹云
  * 无进展

* 王林芳
  * 无进展

* 锦桃
  * 无进展

* 庭丽
  * 无进展

* 云凤
  * 无进展

## CCR@占伟

### 功能完善@占伟

* 占伟
  * CCE-6183 [New Feature] [CCR][功能完善] 性能测试工具开发
  * CCE-6071 [CCR][功能完善] gztest配置更新
  * CCE-6071 [CCR][功能完善] ccr配置自动同步
  * CCE-6071 [CCR][功能完善] ccr配置自动同步
  * CCE-6071 [New Feature] [CCR][功能完善] ccr配置自动同步

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### iregistry接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 开源贡献@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

* 牛康龙
  * 无进展

### 私有化接入@占伟

* 占伟
  * 无进展

* 温满祥
  * 无进展

## CCE 可观测性@子华

### CCE 黄金指标建设@陈欢

* 陈欢
  * 无进展

* 子华
  * 无进展

## 容器网络@郭威

### VPC-CNI 支持 IP 池@chenyaqi

* chenyaqi
  * 无进展

* 亚奇
  * CCE-6072 [容器网络][VPC-CNI 支持 IP 池] 添加优先权队列实现
  * CCE-6072 [容器网络][VPC-CNI 支持 IP 池] terway IP 池实现源码分析

### NodeLocalDNS 默认开启@chenyaqi

* chenyaqi
  * 无进展

### 流量接入优化@冀超

* 冀超
  * CCE-6103 [容器网络][流量接入优化] 支持TagClient可配置
  * CCE-6103 [容器网络][流量接入优化] 支持TagClient可配置
  * CCE-6086 [容器网络][流量接入优化] 上线LB/Ingress Controller: 历史问题修复版本

### BBC 支持 VPC-CNI@郭威

* 郭威
  * 无进展

### 支持 RDMA Roce 容器网络@郭威

* 郭威
  * 无进展

### Cillium 及 eBPF 落地@郭威

* 郭威
  * 无进展

### 体验优化@chenyaqi

* chenyaqi
  * 无进展

## Terraform@温满祥

### 持续迭代@温满祥

* 温满祥
  * 无进展

### 可观测性@温满祥

* 温满祥
  * 无进展

## 用户体验@袁晓沛

### 前端错误码优化@袁晓沛

* 袁晓沛
  * 无进展

## 客户线索@袁晓沛

### 作业帮@林战波

* 林战波
  * 无进展

## Task

### 联调

* 占伟
  * Cloud-Prometheus-Product-44 [Task][联调] network返回详细信息

## 可观测性

### cprom

* 子华
  * CCE-4976: [可观测性][cprom] 监控实例 ingress 证书不在 CRD 上设置
  * CCE-4976: [可观测性][cprom] 重构监控实例模版
  * CCE-4976: [可观测性][cprom] 添加 监控实例自身监控 API 文档
  * CCE-4976: [可观测性][cprom] 监控实例自身监控 API 接口开发
  * CCE-4976: [可观测性][cprom] list binding cluster 接口 resp body 支持返回 agentID
  * CCE-4976: [可观测性][cprom] list instance agent 接口支持分页查询

## 运维运营

### 数据统计

* 陈欢
  * CCE-5370: [运维运营][数据统计] t_cce_k8s_cluster 增加 GPU 统计

## 客户支持

### 车和家

* 陈欢
  * CCE-6115: [客户支持][车和家] 修改容器网段 SOP
  * CCE-6115: [客户支持][车和家] 扩大 cce-yicszp5xv1 容器网段支持扩容 1000 节点

## 不规范提交

* author={wangyufeng06} commit={Cloud-Prometheus-Product-51 [Task] 告警模板Deploy配置更新}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-41 [Task] 告警模板管理}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-49 [Task] 通知策略列表与InstanceID解耦 返回Account下的NotifyRules}
* author={wangzhuyun} commit={CCE-6123 [Bug] [CCE]修复cgpu独占模式环境变量错误}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-48 [Task] 通知策略列表联调}
* author={wangyufeng06} commit={Cloud-Prometheus-Product-47 [Task] 【前后端联调】通知策略创建}
* author={zhutingli} commit={CCE-5971 集群状态校验增加重试间隔}
* author={wangyufeng06} commit={Cloud-Prometheus-63 [Task] Monitor instance可关联多个VMrules}
* author={chenhuan} commit={CCE-5883: CHANGELOG-2022-02-11.md}
* author={wangyufeng06} commit={Cloud-Prometheus-42 [Task] 注册VMRules至Runtime Scheme && 增加VMRuleType Label}
