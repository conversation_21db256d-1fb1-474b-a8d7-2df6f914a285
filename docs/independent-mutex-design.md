# 独立互斥锁设计方案

## 背景问题

在Kubernetes多master节点环境中，审计组件以静态Pod部署，当开启审计推送BLS时，会修改APIserver的YAML文件导致APIserver重启。多个master节点上的审计组件会同时检测到配置变更，导致所有APIserver并发重启，造成集群不稳定。

## 设计目标

1. **无需Pod身份识别**：不依赖节点名称、Pod IP等信息
2. **严格串行执行**：确保同一时间只有一个APIserver在重启
3. **简单可靠**：使用最简洁的实现方案
4. **自动容错**：支持超时和异常恢复机制

## 核心设计思路

### 基于时间戳的抢占式锁
- 每个审计组件实例生成唯一ID（时间戳+随机数）
- 通过ConfigMap实现分布式互斥锁
- 先到先得的抢占机制
- 自动超时释放避免死锁

### 锁的ConfigMap结构
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: apiserver-restart-lock
  namespace: kube-system
data:
  lock_holder: "instance-1642581234567"    # 锁持有者ID
  locked_at: "2024-01-01T10:00:00Z"       # 加锁时间
  last_updated: "2024-01-01T10:01:30Z"    # 最后更新时间
  operation: "enable_audit"               # 当前操作类型
  lock_timeout: "900"                     # 锁超时时间（秒）
  status: "waiting_health"                # 执行状态
```

### 状态说明
- **executing**: 正在执行APIServer配置修改
- **waiting_health**: 等待APIServer健康检查
- **completed**: 操作完成且健康检查通过
- **failed**: 操作执行失败
- **health_check_failed**: 操作完成但健康检查超时

## 实现方案

### 1. 独立互斥锁类

```go
// SimpleMutex 简单互斥锁
type SimpleMutex struct {
    kubeClient kubernetes.Interface
    instanceID string  // 当前实例的唯一ID
}

// 常量定义
const (
    LockConfigMapName = "apiserver-restart-lock"
    LockNamespace     = "kube-system"
    DefaultLockTimeout = 15 * time.Minute  // 默认锁超时时间
    MaxWaitTime       = 45 * time.Minute   // 最大等待时间
)
```

### 2. 核心方法

#### 创建互斥锁
```go
func NewSimpleMutex(kubeClient kubernetes.Interface) *SimpleMutex {
    // 生成唯一实例ID（基于时间戳 + 随机数）
    instanceID := fmt.Sprintf("instance-%d-%d", 
        time.Now().UnixNano(), 
        rand.Intn(10000))
    
    return &SimpleMutex{
        kubeClient: kubeClient,
        instanceID: instanceID,
    }
}
```

#### 获取锁
```go
func (m *SimpleMutex) Lock(ctx context.Context, operation string) error {
    // 等待获取锁
    return m.waitAndAcquireLock(ctx, operation)
}
```

#### 释放锁
```go
func (m *SimpleMutex) Unlock(ctx context.Context) error {
    // 删除锁ConfigMap
    err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Delete(ctx, 
        LockConfigMapName, metav1.DeleteOptions{})
    
    if err != nil && !errors.IsNotFound(err) {
        return fmt.Errorf("failed to release lock: %v", err)
    }
    
    return nil
}
```

### 3. 锁获取逻辑

#### 等待并获取锁
```go
func (m *SimpleMutex) waitAndAcquireLock(ctx context.Context, operation string) error {
    timeout := MaxWaitTime
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()
    
    // 随机初始延迟，避免雷群效应
    initialDelay := time.Duration(rand.Intn(5000)) * time.Millisecond
    time.Sleep(initialDelay)
    
    ticker := time.NewTicker(2 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-timeoutCtx.Done():
            return fmt.Errorf("timeout waiting for lock")
        case <-ticker.C:
            // 尝试获取锁
            acquired, err := m.tryAcquireLock(ctx, operation)
            if err != nil {
                continue
            }
            
            if acquired {
                return nil
            }
        }
    }
}
```

#### 尝试获取锁
```go
func (m *SimpleMutex) tryAcquireLock(ctx context.Context, operation string) (bool, error) {
    lockData := map[string]string{
        "lock_holder":  m.instanceID,
        "locked_at":    time.Now().Format(time.RFC3339),
        "operation":    operation,
        "lock_timeout": strconv.Itoa(int(DefaultLockTimeout.Seconds())),
        "status":       "executing",
    }
    
    lockCM := &corev1.ConfigMap{
        ObjectMeta: metav1.ObjectMeta{
            Name:      LockConfigMapName,
            Namespace: LockNamespace,
        },
        Data: lockData,
    }
    
    // 尝试创建锁
    _, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Create(ctx, 
        lockCM, metav1.CreateOptions{})
    
    if err == nil {
        // 创建成功，获得锁
        return true, nil
    }
    
    if !errors.IsAlreadyExists(err) {
        return false, err
    }
    
    // 锁已存在，检查是否可以抢占
    return m.tryTakeOverLock(ctx, operation)
}
```

#### 锁抢占机制
```go
func (m *SimpleMutex) tryTakeOverLock(ctx context.Context, operation string) (bool, error) {
    // 获取现有锁
    existingCM, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx, 
        LockConfigMapName, metav1.GetOptions{})
    if err != nil {
        if errors.IsNotFound(err) {
            // 锁已被删除，重新尝试创建
            return m.tryAcquireLock(ctx, operation)
        }
        return false, err
    }
    
    // 检查锁是否超时
    if m.isLockExpired(existingCM) {
        // 更新锁信息
        existingCM.Data["lock_holder"] = m.instanceID
        existingCM.Data["locked_at"] = time.Now().Format(time.RFC3339)
        existingCM.Data["operation"] = operation
        existingCM.Data["status"] = "executing"
        
        _, err = m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Update(ctx, 
            existingCM, metav1.UpdateOptions{})
        
        return err == nil, err
    }
    
    // 锁未超时，无法获取
    return false, nil
}
```

### 4. 集成到Controller

#### Controller结构体修改
```go
type Controller struct {
    // ... 现有字段
    mutex *SimpleMutex
}
```

#### 初始化
```go
func NewController(...) *Controller {
    controller := &Controller{
        // ... 现有初始化
        mutex: NewSimpleMutex(kubeClient),
    }
    return controller
}
```

#### 主要函数修改
```go
func (c *Controller) modifyStandAloneApiServe(ctx context.Context, isOpen bool) error {
    // 检查是否需要协调
    if !c.needsCoordination(ctx) {
        return c.executeAPIServerModification(ctx, isOpen)
    }

    operation := "enable_audit"
    if !isOpen {
        operation = "disable_audit"
    }

    // 使用带健康检查的执行方法
    return c.mutex.ExecuteWithHealthCheck(ctx, operation, func() error {
        return c.executeAPIServerModification(ctx, isOpen)
    })
}
```

#### 核心执行方法
```go
func (m *SimpleMutex) ExecuteWithHealthCheck(ctx context.Context, operation string, executeFunc func() error) error {
    // 1. 获取锁
    if err := m.Lock(ctx, operation); err != nil {
        return fmt.Errorf("failed to acquire lock: %v", err)
    }

    // 2. 确保释放锁
    defer func() {
        if err := m.Unlock(ctx); err != nil {
            logger.Warnf(ctx, "Failed to release lock: %v", err)
        }
    }()

    // 3. 更新锁状态为执行中
    m.updateLockStatus(ctx, "executing")

    // 4. 执行操作
    if err := executeFunc(); err != nil {
        m.updateLockStatus(ctx, "failed")
        return fmt.Errorf("operation failed: %v", err)
    }

    // 5. 更新状态为等待健康检查
    m.updateLockStatus(ctx, "waiting_health")

    // 6. 等待APIserver健康（5分钟超时）
    if err := m.waitForAPIServerHealthy(ctx); err != nil {
        logger.Warnf(ctx, "APIServer health check failed: %v", err)
        m.updateLockStatus(ctx, "health_check_failed")
        // 不返回错误，允许继续流程
    } else {
        m.updateLockStatus(ctx, "completed")
    }

    return nil
}
```

## 方案特点

### 优势
1. **完全独立**：无需识别Pod身份，每个实例生成唯一ID
2. **抢占式锁**：基于时间戳，先到先得
3. **自动超时**：15分钟超时自动释放，避免死锁
4. **简单可靠**：只用一个ConfigMap，逻辑简单
5. **容错性强**：支持锁超时接管和异常恢复
6. **最小改动**：只需在Controller中添加mutex字段

### 执行流程
```
实例A: 获取锁 → 执行重启 → 等待健康检查(5分钟) → 释放锁
实例B: 等待锁 → 获取锁 → 执行重启 → 等待健康检查(5分钟) → 释放锁
实例C: 等待锁 → 获取锁 → 执行重启 → 等待健康检查(5分钟) → 释放锁
```

### 详细执行步骤
1. **获取锁**: 实例尝试获取互斥锁
2. **执行重启**: 修改APIServer配置文件
3. **等待健康检查**: 等待APIServer重启完成并通过健康检查
4. **超时处理**: 如果5分钟内健康检查未通过，记录警告但继续流程
5. **释放锁**: 释放锁供下一个实例使用

### 配置参数
- **锁超时时间**：15分钟（可配置）
- **最大等待时间**：30分钟
- **APIServer健康检查超时**：5分钟
- **健康检查间隔**：10秒
- **锁获取检查间隔**：2秒
- **随机延迟**：0-5秒（避免雷群效应）

## 部署要求

1. **权限要求**：审计组件需要对kube-system命名空间的ConfigMap有创建、读取、更新、删除权限
2. **环境变量**：可选设置`COORDINATION_DISABLED=true`禁用协调机制
3. **兼容性**：完全兼容现有代码，无需修改YAML配置

## 监控和调试

### 查看锁状态
```bash
kubectl get configmap apiserver-restart-lock -n kube-system -o yaml
```

### 手动释放锁（紧急情况）
```bash
kubectl delete configmap apiserver-restart-lock -n kube-system
```

### 日志关键信息
- 锁获取和释放日志
- 超时和抢占日志
- 执行状态日志

## APIServer健康检查机制

### 健康检查方法
1. **ComponentStatuses检查**: 调用`/api/v1/componentstatuses`
2. **节点列表检查**: 尝试列出集群节点
3. **命名空间检查**: 尝试列出命名空间

### 健康检查流程
```go
func (m *SimpleMutex) waitForAPIServerHealthy(ctx context.Context) error {
    timeout := 5 * time.Minute
    timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
    defer cancel()

    ticker := time.NewTicker(10 * time.Second)
    defer ticker.Stop()

    for {
        select {
        case <-timeoutCtx.Done():
            return fmt.Errorf("timeout waiting for APIServer to become healthy")
        case <-ticker.C:
            if m.isAPIServerHealthy(ctx) {
                return nil
            }
        }
    }
}
```

### 超时处理策略
- **5分钟超时**: 如果APIServer在5分钟内未通过健康检查
- **记录警告**: 记录健康检查失败的警告日志
- **继续流程**: 不阻塞后续实例的执行
- **状态标记**: 将状态标记为`health_check_failed`

## 改进效果

### 问题解决
1. **真正串行**: 确保前一个APIServer完全重启完成后才执行下一个
2. **健康保证**: 通过健康检查确保APIServer正常运行
3. **超时保护**: 5分钟超时避免无限等待
4. **状态透明**: 详细的状态跟踪便于监控和调试

### 执行时序
```
时间线:
T+0s:    实例A获取锁，开始重启APIServer
T+30s:   实例A的APIServer重启完成
T+40s:   实例A健康检查通过，释放锁
T+41s:   实例B获取锁，开始重启APIServer
T+71s:   实例B的APIServer重启完成
T+81s:   实例B健康检查通过，释放锁
T+82s:   实例C获取锁，开始重启APIServer
...

结果: 集群始终保持至少2/3的APIServer可用
```

这个方案通过简单的互斥锁机制和健康检查，完全解决了多master环境下APIserver并发重启的问题，确保集群稳定性。
