# APIServer滚动重启协调机制设计方案

## 1. 问题背景

### 1.1 当前问题
在Kubernetes多master节点环境中，当审计组件检测到BLS配置变更时，会同时修改所有master节点上的APIserver静态Pod配置文件，导致：
- 所有APIserver同时重启
- 集群出现短时间不可用
- 可能导致集群不稳定

### 1.2 目标
实现多master节点APIserver的滚动重启，确保：
- 任何时刻至少有2/3的APIserver可用
- 自动协调重启顺序
- 完整的状态追溯和监控
- 异常情况的容错处理

## 2. 总体架构设计

### 2.1 核心设计思路
采用**时间窗口自协调机制**，通过持久化ConfigMap进行状态管理：
- 基于节点名称哈希计算重启顺序
- 使用时间窗口错峰执行
- 持久化协调状态，支持历史追溯
- 无需传统Leader Election，适合静态Pod环境

### 2.2 架构组件
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Master-1      │    │   Master-2      │    │   Master-3      │
│  审计组件       │    │  审计组件       │    │  审计组件       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────────┐
                    │  协调ConfigMap      │
                    │  (持久化状态管理)   │
                    └─────────────────────┘
```

## 3. 持久化协调ConfigMap设计

### 3.1 ConfigMap结构
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: apiserver-restart-coordination
  namespace: kube-system
  labels:
    component: external-auditor
    managed-by: external-auditor-controller
  annotations:
    coordination.external-auditor/version: "v1"
    coordination.external-auditor/created-at: "2024-01-01T09:00:00Z"
    coordination.external-auditor/last-updated: "2024-01-01T10:30:00Z"
data:
  # 当前操作状态
  current_operation: "enable_audit"                    # enable_audit/disable_audit/idle
  current_status: "completed"                          # idle/initializing/coordinating/completed/failed
  current_batch_id: "20240101-103000-enable"          # 当前批次ID
  
  # 节点发现配置
  discovery_method: "auto"                             # auto/label/manual
  node_label_selector: "node-role.kubernetes.io/master="
  manual_nodes: ""                                     # 手动指定节点列表
  
  # 协调参数
  window_size_seconds: "60"                           # 重启窗口间隔
  max_coordination_wait: "600"                        # 最大协调等待时间
  health_check_timeout: "120"                         # 健康检查超时
  
  # 当前批次状态
  current_nodes_sequence: "master-1,master-2,master-3"
  current_nodes_status: |
    master-1:completed:2024-01-01T10:00:00Z:2024-01-01T10:01:30Z:0
    master-2:completed:2024-01-01T10:01:00Z:2024-01-01T10:02:45Z:60
    master-3:completed:2024-01-01T10:02:00Z:2024-01-01T10:03:20Z:120
    # 格式：节点名:状态:开始时间:完成时间:延迟秒数
  
  # 统计信息
  current_total_nodes: "3"
  current_completed_nodes: "3"
  current_failed_nodes: "0"
  current_initiated_at: "2024-01-01T10:00:00Z"
  current_completed_at: "2024-01-01T10:03:20Z"
  current_initiated_by: "master-1"
  
  # 历史记录 (保留最近10次)
  history_count: "3"
  history_operations: |
    batch-1:20240101-090000-enable:enable_audit:completed:2024-01-01T09:00:00Z:2024-01-01T09:05:30Z:master-1
    batch-2:20240101-095000-disable:disable_audit:completed:2024-01-01T09:50:00Z:2024-01-01T09:53:15Z:master-2
    batch-3:20240101-103000-enable:enable_audit:completed:2024-01-01T10:00:00Z:2024-01-01T10:03:20Z:master-1
    # 格式：批次序号:批次ID:操作类型:最终状态:开始时间:结束时间:发起节点
  
  # 最后一次操作的详细状态（用于故障排查）
  last_operation_details: |
    batch_id: 20240101-103000-enable
    operation: enable_audit
    status: completed
    nodes_detail:
      master-1: completed:success:apiserver_restarted_successfully
      master-2: completed:success:apiserver_restarted_successfully  
      master-3: completed:success:apiserver_restarted_successfully
    timeline:
      - 2024-01-01T10:00:00Z: coordination_initialized_by_master-1
      - 2024-01-01T10:00:05Z: master-1_started_execution
      - 2024-01-01T10:01:30Z: master-1_completed_successfully
      - 2024-01-01T10:01:05Z: master-2_started_execution
      - 2024-01-01T10:02:45Z: master-2_completed_successfully
      - 2024-01-01T10:02:05Z: master-3_started_execution
      - 2024-01-01T10:03:20Z: master-3_completed_successfully
      - 2024-01-01T10:03:25Z: coordination_completed_and_cleaned
```

### 3.2 生命周期管理
- **初始化**：首次创建时建立基础结构
- **批次管理**：每次操作创建新批次，保留历史
- **状态更新**：使用乐观锁机制更新节点状态
- **历史归档**：自动保留最近10次操作记录
- **持久化**：ConfigMap永不删除，仅更新状态

## 4. 节点发现和排序策略

### 4.1 多重发现策略
```go
type NodeDiscoveryStrategy struct {
    strategies []NodeDiscoveryFunc
}

// 策略1：通过节点标签发现
func discoverByNodeRole() // node-role.kubernetes.io/master=

// 策略2：通过节点污点发现  
func discoverByTaints() // node-role.kubernetes.io/master:NoSchedule

// 策略3：通过静态Pod发现
func discoverByStaticPods() // 查找运行kube-apiserver静态Pod的节点

// 策略4：通过配置文件发现
func discoverByConfig() // 从环境变量或ConfigMap读取
```

### 4.2 智能排序算法
```go
func smartSortNodes(nodes []string) []string {
    // 1. 尝试数字排序（提取节点名中的数字）
    if sorted := tryNumericSort(nodes); sorted != nil {
        return sorted
    }
    
    // 2. 尝试IP排序（如果节点名包含IP）
    if sorted := tryIPSort(nodes); sorted != nil {
        return sorted
    }
    
    // 3. 默认字典序排序
    sort.Strings(nodes)
    return nodes
}
```

### 4.3 时间窗口计算
```go
func calculateNodeExecutionDelay(nodeName string, allNodes []string, windowSize time.Duration) time.Duration {
    // 1. 对节点列表进行排序（确保一致性）
    sort.Strings(allNodes)
    
    // 2. 找到当前节点在序列中的位置
    position := findNodePosition(nodeName, allNodes)
    
    // 3. 计算延迟时间
    delay := time.Duration(position) * windowSize
    return delay
}
```

## 5. 核心函数重构设计

### 5.1 原函数重构
```go
// 原有函数重命名为执行函数
func (c *Controller) executeAPIServerModification(ctx context.Context, isOpen bool) error {
    // 原 modifyStandAloneApiServe 函数的实现
    logger.Infof(ctx, "[executeAPIServerModification] openOrClose: %s", isOpen)
    var err error = nil
    changeFlag := true
    
    if !isOpen {
        changeFlag = utils.PrepareAuditYaml(false)
        if changeFlag {
            err = utils.ChangeAuditYaml(ctx)
        }
        return err
    } else {
        logger.Infof(ctx, "isOldFalse %s", true)
        changeFlag = utils.PrepareAuditYaml(true)
        if changeFlag {
            if err := utils.ChangeAuditYaml(ctx); err != nil {
                logger.Errorf(ctx, "[executeAPIServerModification] failed to change AuditYaml: %s", err.Error())
                return err
            }
        }
    }
    return nil
}

// 新的协调包装函数
func (c *Controller) modifyStandAloneApiServe(ctx context.Context, isOpen bool) error {
    return c.coordinatedModifyStandAloneApiServe(ctx, isOpen)
}
```

### 5.2 协调函数设计
```go
func (c *Controller) coordinatedModifyStandAloneApiServe(ctx context.Context, isOpen bool) error {
    // 1. 检查是否需要协调（多master环境）
    if !c.needsCoordination(ctx) {
        return c.executeAPIServerModification(ctx, isOpen)
    }
    
    // 2. 获取或初始化持久化协调配置
    coordConfig, created, err := c.getOrCreatePersistentCoordinationConfig(ctx, isOpen)
    if err != nil {
        return fmt.Errorf("failed to initialize coordination: %v", err)
    }
    
    // 3. 计算当前节点的执行时间窗口
    delay, err := c.calculateExecutionDelay(ctx, coordConfig)
    if err != nil {
        return err
    }
    
    // 4. 等待执行时间窗口
    if err := c.waitForExecutionWindow(ctx, delay); err != nil {
        return err
    }
    
    // 5. 更新状态为执行中
    c.updateNodeStatus(ctx, c.getCurrentNodeName(), "executing", nil)
    
    // 6. 执行APIserver配置修改（原有逻辑）
    if err := c.executeAPIServerModification(ctx, isOpen); err != nil {
        c.updateNodeStatus(ctx, c.getCurrentNodeName(), "failed", map[string]string{
            "error": err.Error(),
        })
        return err
    }
    
    // 7. 更新状态为完成
    c.updateNodeStatus(ctx, c.getCurrentNodeName(), "completed", map[string]string{
        "completed_at": time.Now().Format(time.RFC3339),
    })
    
    return nil
}
```

## 6. 执行流程设计

### 6.1 四阶段执行流程
```
阶段1：协调初始化
├── 检测多master环境
├── 创建/更新协调ConfigMap
├── 发现所有master节点
└── 计算重启时间窗口

阶段2：时间窗口等待
├── 计算当前节点延迟时间
├── 等待执行时间窗口
└── 更新节点状态为"executing"

阶段3：串行执行
├── 执行APIserver配置修改
├── 等待APIserver重启完成
├── 进行健康检查
└── 更新节点状态为"completed"

阶段4：状态管理
├── 检查所有节点完成状态
├── 归档当前批次到历史记录
└── 重置状态为"idle"
```

### 6.2 并发冲突处理
```go
func (c *Controller) getOrCreatePersistentCoordinationConfig(ctx context.Context, isOpen bool) (*CoordinationConfig, bool, error) {
    // 1. 尝试获取现有ConfigMap
    existing, err := c.kubeClient.CoreV1().ConfigMaps(namespace).Get(ctx, configMapName, metav1.GetOptions{})
    if err == nil {
        // 检查当前状态，决定是否可以开始新批次
        return c.handleExistingConfigMap(ctx, existing, isOpen)
    }
    
    if !errors.IsNotFound(err) {
        return nil, false, err
    }
    
    // 2. ConfigMap不存在，创建新的
    newConfigMap := c.buildInitialCoordinationConfigMap(ctx, isOpen)
    created, err := c.kubeClient.CoreV1().ConfigMaps(namespace).Create(ctx, newConfigMap, metav1.CreateOptions{})
    if err != nil {
        if errors.IsAlreadyExists(err) {
            // 并发创建冲突，重新获取
            return c.getOrCreatePersistentCoordinationConfig(ctx, isOpen)
        }
        return nil, false, err
    }
    
    config, err := c.parseCoordinationConfig(created)
    return config, true, err
}
```

## 7. 异常处理和容错机制

### 7.1 异常场景处理
- **节点重启失败**：记录失败状态，不阻塞其他节点
- **健康检查超时**：标记为失败，继续处理下一个节点
- **网络分区**：基于时间窗口的设计天然容忍网络分区
- **进程重启**：通过持久化ConfigMap恢复状态

### 7.2 超时和重试机制
```go
const (
    RestartInterval        = 60 * time.Second   // 重启窗口间隔
    HealthCheckTimeout     = 120 * time.Second  // 健康检查超时
    MaxCoordinationWait    = 600 * time.Second  // 最大协调等待时间
    ConfigMapUpdateRetries = 3                  // ConfigMap更新重试次数
)
```

### 7.3 回滚机制
- **配置备份**：修改前备份原始APIserver配置
- **失败回滚**：重启失败时自动恢复原始配置
- **手动干预**：提供手动清理和重置接口

## 8. 监控和可观测性

### 8.1 状态查询接口
```go
// 获取当前协调状态
func (c *Controller) GetCurrentCoordinationStatus(ctx context.Context) (*CoordinationStatus, error)

// 获取协调历史记录
func (c *Controller) GetCoordinationHistory(ctx context.Context, limit int) ([]HistoryEntry, error)

// 获取最后一次操作的详细信息
func (c *Controller) GetLastOperationDetails(ctx context.Context) (*OperationDetails, error)
```

### 8.2 日志和指标
- **详细日志**：记录每个阶段的执行情况和时间戳
- **错误日志**：包含详细的错误原因和上下文
- **指标监控**：重启成功率、耗时、当前状态等指标
- **事件通知**：重启开始/完成/失败事件

## 9. 配置参数

### 9.1 环境变量配置
```bash
# 协调相关配置
COORDINATION_ENABLED=true                    # 是否启用协调机制
RESTART_WINDOW_SIZE=60                       # 重启窗口间隔（秒）
MAX_COORDINATION_WAIT=600                    # 最大协调等待时间（秒）
HEALTH_CHECK_TIMEOUT=120                     # 健康检查超时（秒）

# 节点发现配置
NODE_DISCOVERY_METHOD=auto                   # auto/label/manual
NODE_LABEL_SELECTOR="node-role.kubernetes.io/master="
MANUAL_MASTER_NODES=""                       # 手动指定节点列表

# 节点标识
NODE_NAME=${NODE_NAME}                       # 当前节点名称
```

## 10. 实施计划

### 10.1 实施阶段
```
第一阶段：基础重构
├── 重命名现有函数为executeAPIServerModification
├── 新增coordinatedModifyStandAloneApiServe包装函数
└── 实现基础的协调ConfigMap管理

第二阶段：协调逻辑
├── 实现时间窗口计算逻辑
├── 添加节点发现和状态管理
└── 实现等待和执行机制

第三阶段：完善机制
├── 添加健康检查和异常处理
├── 实现状态清理和超时机制
└── 添加监控和日志

第四阶段：测试验证
├── 单master环境兼容性测试
├── 多master环境协调功能测试
└── 异常场景和恢复测试
```

### 10.2 预期效果
- **高可用性**：整个过程中至少保持2/3的APIServer可用
- **自动协调**：无需人工干预，自动完成滚动重启
- **状态追溯**：完整的历史记录和故障排查信息
- **容错能力**：单点故障不影响整体流程

## 11. 总结

本设计方案通过时间窗口自协调机制，完美解决了多master环境下APIserver并发重启导致的集群不稳定问题。方案具有以下特点：

1. **适合静态Pod环境**：无需传统Leader Election
2. **持久化状态管理**：支持历史追溯和故障排查
3. **高可用保证**：确保集群在重启过程中始终稳定
4. **自动化程度高**：无需人工干预
5. **容错能力强**：完善的异常处理机制

该方案既保持了原有代码的兼容性，又提供了企业级的可靠性和可观测性。
