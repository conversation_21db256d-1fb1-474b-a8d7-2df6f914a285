# cce-cgroup-agent

## 背景

用于在离线混布:

```text
1. kernel: http://ikernel.baidu.com/4.19.0/x86_64/redbook/4.19.0-4.0.0.1/baidukernel-4.19.0-4.0.0.1.x86_64.rpm

2. 设置离线业务 offline（cgroup下增加 cpu.offline，设置为1，即使用离线业务策略运行）
echo 1 > /sys/fs/cgroup/cpu/kubepods/burstable/pod01a07389-9cd4-43fa-80c8-4733a36e82a9/a50eae434e166219e3923779079fef33c269942606e7ef02da4b809033d4fa9a/cpu.offline

3. 目前测试情况：
Ref to： http://wiki.baidu.com/pages/viewpage.action?pageId=1538879148

```

参考:

* bt scheduler 参考: [https://lwn.net/Articles/791681/](https://lwn.net/Articles/791681/)
* bt scheudler
  代码: [https://lore.kernel.org/patchwork/project/lkml/list/?submitter=26383&archive=both&order=delegate](https://lore.kernel.org/patchwork/project/lkml/list/?submitter=26383&archive=both&order=delegate)
* MutationWebhook
  开发: [https://github.com/kubernetes/kubernetes/blob/release-1.9/test/images/webhook/main.go](https://github.com/kubernetes/kubernetes/blob/release-1.9/test/images/webhook/main.go)

## 实现

cce-cgroup-agent Watch docker.sock, 对于设置了环境变量的容器, 执行注入相关 cgrpup 配置。

## 支持功能:

1. 容器标记为离线任务

```yaml
env:
  - name: "ENABLE_CGROUP_CPU_OFFLINE"
    value: "true"
```

2. 开启后台 `page cache` 自动回收, 三个参数必须同时设置

```yaml
env:
  - name: "ENABLE_CGROUP_MEMORY_RECLAIM"
    value: "true"
  - name: "CGROUP_MEMORY_HIGH_WMARK_DISTANCE"
    value: "10G"
  - name: "CGROUP_MEMORY_LOW_WMARK_DISTANCE"
    value: "1G"
```


