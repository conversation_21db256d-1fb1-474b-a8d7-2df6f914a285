// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/13 16:19:00, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package manager

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"os"
	"path/filepath"
	"strings"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/events"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/cce-log-operator/pkg/agent/docker"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

const (
	CPUCGroup    = "cpu"
	MemoryCGroup = "memory"

	EnableCGroupCPUOfflineEnvKey        = "ENABLE_CGROUP_CPU_OFFLINE"
	EnableCGroupMemoryReclaimEnvKey     = "ENABLE_CGROUP_MEMORY_RECLAIM"
	CGroupMemoryHighWMarkDistanceEnvKey = "CGROUP_MEMORY_HIGH_WMARK_DISTANCE"
	CGroupMemoryLowWMarkDistanceEnvKey  = "CGROUP_MEMORY_LOW_WMARK_DISTANCE"

	CPUOfflineFile      = "cpu.offline"
	CPUSharesFile       = "cpu.shares"
	CPUBTSharesFile     = "cpu.bt_shares"
	MemoryReclaimFile   = "memory.background_reclaim"
	MemoryHighWMarkFile = "memory.high_wmark_distance"
	MemoryLowWMarkFile  = "memory.low_wmark_distance"
)

type containerHandler func(ctx context.Context, container *types.ContainerJSON) error

type Manager struct {
	dockerClient *docker.Client
}

func NewManager(ctx context.Context) (*Manager, error) {
	dockerClient, err := docker.New()
	if err != nil {
		logger.Errorf(ctx, "dockerwrapper.NewDockerWrapper failed: %s", err)
		return nil, err
	}

	return &Manager{
		dockerClient: dockerClient,
	}, nil
}

func (m *Manager) Run() error {
	msgChan, errChan := m.dockerClient.WatchContainerEvent(context.TODO())
	for {
		select {
		case msg := <-msgChan:
			ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())

			if err := m.processContainerEvent(ctx, msg); err != nil {
				logger.Errorf(ctx, "processContainerEvent failed: %s", err)
			}
		case err := <-errChan:
			if err == io.EOF || err == io.ErrUnexpectedEOF {
				return nil
			} else {
				ctx := context.TODO()
				logger.Errorf(ctx, "err receive: %s", err)

				msgChan, errChan = m.dockerClient.WatchContainerEvent(ctx)
			}
		}
	}
}

// enableMemoryReclaim 开启 memory reclaim 功能
func (m *Manager) enableMemoryReclaim(ctx context.Context, container *types.ContainerJSON) error {
	value, ok1 := m.withEnv(container, EnableCGroupMemoryReclaimEnvKey)
	highValue, ok2 := m.withEnv(container, CGroupMemoryHighWMarkDistanceEnvKey)
	lowValue, ok3 := m.withEnv(container, CGroupMemoryLowWMarkDistanceEnvKey)

	if !ok1 || value != "true" || !ok2 || !ok3 {
		logger.Warnf(ctx, "%v, %v , %v must be set at the same time, skip", EnableCGroupMemoryReclaimEnvKey, CGroupMemoryHighWMarkDistanceEnvKey, CGroupMemoryLowWMarkDistanceEnvKey)
		return nil
	}

	// echo 1 > /xxx/memory.background_reclaim
	memoryReclaimPath, err := m.getCGroupPathWithFile(ctx, container, MemoryCGroup, MemoryReclaimFile)
	if err != nil {
		logger.Errorf(ctx, "getCGroupPathWithFile failed: %s", err)
		return err
	}

	if err := ioutil.WriteFile(memoryReclaimPath, []byte("1"), os.ModePerm); err != nil {
		logger.Errorf(ctx, "write failed: %s", err)
		return err
	}

	logger.Infof(ctx, "container: %s set %v success", container.ID, memoryReclaimPath)

	// echo $value > /xxx/memory.high_wmark_distance
	highWMarkPath, err := m.getCGroupPathWithFile(ctx, container, MemoryCGroup, MemoryHighWMarkFile)
	if err != nil {
		logger.Errorf(ctx, "getCGroupPathWithFile failed: %s", err)
		return err
	}

	if err := ioutil.WriteFile(highWMarkPath, []byte(highValue), os.ModePerm); err != nil {
		logger.Errorf(ctx, "write %v failed: %s", highValue, err)
		return err
	}

	logger.Infof(ctx, "container: %s set %v success", container.ID, highWMarkPath)

	// echo $value > /xxx/memory.low_wmark_distance
	lowWMarkPath, err := m.getCGroupPathWithFile(ctx, container, MemoryCGroup, MemoryLowWMarkFile)
	if err != nil {
		logger.Errorf(ctx, "getCGroupPathWithFile failed: %s", err)
		return err
	}

	if err := ioutil.WriteFile(lowWMarkPath, []byte(lowValue), os.ModePerm); err != nil {
		logger.Errorf(ctx, "write %v failed: %s", lowValue, err)
		return err
	}

	logger.Infof(ctx, "container: %s set %v success", container.ID, lowWMarkPath)

	return nil
}

// enableCPUOffline 开启 cpu.offline 功能
// echo 1 > /xxx/cpu.offline
func (m *Manager) enableCPUOffline(ctx context.Context, container *types.ContainerJSON) error {
	if value, ok := m.withEnv(container, EnableCGroupCPUOfflineEnvKey); !ok || value != "true" {
		logger.Warnf(ctx, "not found %v=true, skip", EnableCGroupCPUOfflineEnvKey)
		return nil
	}

	path, err := m.getCGroupPathWithFile(ctx, container, CPUCGroup, CPUOfflineFile)
	if err != nil {
		logger.Errorf(ctx, "getCGroupPathWithFile failed: %s", err)
		return err
	}

	// 执行 echo 1 > cpu.offline
	if err := ioutil.WriteFile(path, []byte("1"), os.ModePerm); err != nil {
		logger.Errorf(ctx, "write failed: %s", err)
		return err
	}

	logger.Infof(ctx, "container: %s set %v success", container.ID, path)
	return nil
}

// inheritBTShare cpu.bt_shares 继承 cpu.shares
// copy /xxx/cpu.shares  /xxx/cpu.bt_shares
func (m *Manager) inheritCPUShares(ctx context.Context, container *types.ContainerJSON) error {
	cpuSharesPath, err := m.getCGroupPathWithFile(ctx, container, CPUCGroup, CPUSharesFile)
	if err != nil {
		logger.Errorf(ctx, "get cpuSharesFile err: %s", err)
		return err
	}

	cpuBTSharesPath, err := m.getCGroupPathWithFile(ctx, container, CPUCGroup, CPUBTSharesFile)
	if err != nil {
		logger.Errorf(ctx, "get cpuBTSharesFile err: %s", err)
		return err
	}

	logger.Infof(ctx, "container: %s, cpu.shares: %s, cpu.bt_shares: %v", container.ID, cpuSharesPath, cpuBTSharesPath)

	cpuSharesValues, err := ioutil.ReadFile(cpuSharesPath)
	if err != nil {
		logger.Errorf(ctx, "read: %v err: %s", cpuSharesPath, err)
		return err
	}

	if err := ioutil.WriteFile(cpuBTSharesPath, cpuSharesValues, os.ModePerm); err != nil {
		logger.Errorf(ctx, "read: %v err: %s", cpuSharesPath, err)
		return err
	}

	logger.Infof(ctx, "copy: %v to: %v success", cpuSharesPath, cpuBTSharesPath)
	return nil
}

func (m *Manager) processContainerEvent(ctx context.Context, msg events.Message) error {
	containerID := msg.Actor.ID

	switch msg.Action {
	case "start", "restart":
		container, err := m.dockerClient.GetContainerInfo(ctx, containerID)
		if err != nil {
			logger.Errorf(ctx, "get container info err: %v", err)
			return err
		}

		handlers := []containerHandler{m.inheritCPUShares, m.enableCPUOffline, m.enableMemoryReclaim}
		for _, h := range handlers {
			if err := h(ctx, container); err != nil {
				logger.Errorf(ctx, "run container handler err: %v", err)
				return err
			}
		}
	}

	return nil
}

// withEnv 判断容器上是否设置 env
// 如果设置 返回 true 并且返回 value
func (m *Manager) withEnv(container *types.ContainerJSON, envKey string) (string, bool) {
	// 检查是否设置 ENV
	for _, env := range container.Config.Env {
		if strings.Contains(env, envKey) {
			sp := strings.Split(env, "=")

			if len(sp) != 2 {
				return "", true
			}

			return sp[1], true
		}
	}

	return "", false
}

// getCGroupPath 获取 kind cgroup 路径
// kind 可选 cpu memory
func (m *Manager) getCGroupPath(ctx context.Context, container *types.ContainerJSON, kind string) (string, error) {
	if container == nil {
		return "", fmt.Errorf("container is nil")
	}

	if container.HostConfig == nil {
		return "", fmt.Errorf("container.HostConfig is nil")
	}

	// e.g. /kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2
	containerID := container.ID
	cgroupParent := container.HostConfig.CgroupParent

	// mount /sys/fs/cgroup/cpu/ to /cce-cgroup/ in cpu-offline-agent Container
	containerCGroupPath := filepath.Join("/cce-cgroup", kind, cgroupParent, containerID)
	containerCGroupHostPath := filepath.Join("/sys/fs/cgroup", kind, cgroupParent, containerID)

	logger.Infof(ctx, "mount cgroup path: %s", containerCGroupPath)    // For debug
	logger.Infof(ctx, "host cgroup path: %s", containerCGroupHostPath) // For debug

	return containerCGroupPath, nil
}

// getCGroupPathWithFile 获取 kind filename cgroup 路径
// kind 可选 cpu memory
func (m *Manager) getCGroupPathWithFile(ctx context.Context, container *types.ContainerJSON, kind, filename string) (string, error) {
	cgroupPath, err := m.getCGroupPath(ctx, container, kind)
	if err != nil {
		logger.Errorf(ctx, "getCGroupPath kind: %v, filename: %v err: %v", kind, filename, err)
		return cgroupPath, err
	}

	return filepath.Join(cgroupPath, filename), nil
}
