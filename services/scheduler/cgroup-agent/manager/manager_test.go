// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/13 16:19:00, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package manager

import (
	"bytes"
	"context"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/docker/docker/api/types"
	"github.com/docker/docker/api/types/container"
	"github.com/docker/docker/api/types/events"
	"github.com/docker/docker/client"
	"github.com/google/go-cmp/cmp"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/cce-log-operator/pkg/agent/docker"
)

type transportFunc func(*http.Request) (*http.Response, error)

func (tf transportFunc) RoundTrip(req *http.Request) (*http.Response, error) {
	return tf(req)
}

func newMockClient(doer func(*http.Request) (*http.Response, error)) *http.Client {
	return &http.Client{
		Transport: transportFunc(doer),
	}
}

func TestManager_withEnv(t *testing.T) {
	type fields struct {
		dockerClient *docker.Client
	}
	type args struct {
		container *types.ContainerJSON
		envKey    string
	}

	type result struct {
		value string
		ok    bool
	}

	tests := []struct {
		name   string
		fields fields
		args   args
		want   result
	}{
		{
			name:   "get ENABLE_CGROUP_CPU_OFFLINE",
			fields: fields{},
			args: args{
				container: &types.ContainerJSON{
					Config: &container.Config{
						Env: []string{"ENABLE_CGROUP_CPU_OFFLINE=true"},
					},
				},
				envKey: EnableCGroupCPUOfflineEnvKey,
			},
			want: result{"true", true},
		},
		{
			name:   "get CGROUP_MEMORY_HIGH_WMARK_DISTANCE",
			fields: fields{},
			args: args{
				container: &types.ContainerJSON{
					Config: &container.Config{
						Env: []string{"CGROUP_MEMORY_HIGH_WMARK_DISTANCE=1G"},
					},
				},
				envKey: CGroupMemoryHighWMarkDistanceEnvKey,
			},
			want: result{"1G", true},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				dockerClient: tt.fields.dockerClient,
			}
			got, ok := m.withEnv(tt.args.container, tt.args.envKey)

			r := result{got, ok}

			if r != tt.want {
				t.Errorf("diff= %v", cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestManager_getCGroupPathWithFile(t *testing.T) {
	type fields struct {
		dockerClient *docker.Client
	}
	type args struct {
		ctx       context.Context
		container *types.ContainerJSON
		kind      string
		filename  string
	}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "get cpu.offline",
			fields: fields{},
			args: args{
				ctx:      context.TODO(),
				kind:     CPUCGroup,
				filename: CPUOfflineFile,
				container: &types.ContainerJSON{
					ContainerJSONBase: &types.ContainerJSONBase{
						ID: "container-id",
						HostConfig: &container.HostConfig{
							Resources: container.Resources{
								CgroupParent: "/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2",
							},
						},
					},
				},
			},
			want:    "/cce-cgroup/cpu/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2/container-id/cpu.offline",
			wantErr: false,
		},
		{
			name:   "get cpu.shares file",
			fields: fields{},
			args: args{
				ctx:      context.TODO(),
				kind:     CPUCGroup,
				filename: CPUSharesFile,
				container: &types.ContainerJSON{
					ContainerJSONBase: &types.ContainerJSONBase{
						ID: "container-id",
						HostConfig: &container.HostConfig{
							Resources: container.Resources{
								CgroupParent: "/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2",
							},
						},
					},
				},
			},
			want:    "/cce-cgroup/cpu/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2/container-id/cpu.shares",
			wantErr: false,
		},
		{
			name:   "get cpu.bt_shares file",
			fields: fields{},
			args: args{
				ctx:      context.TODO(),
				kind:     CPUCGroup,
				filename: CPUBTSharesFile,
				container: &types.ContainerJSON{
					ContainerJSONBase: &types.ContainerJSONBase{
						ID: "container-id",
						HostConfig: &container.HostConfig{
							Resources: container.Resources{
								CgroupParent: "/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2",
							},
						},
					},
				},
			},
			want:    "/cce-cgroup/cpu/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2/container-id/cpu.bt_shares",
			wantErr: false,
		},
		{
			name:   "get memory.background_reclaim file",
			fields: fields{},
			args: args{
				ctx:      context.TODO(),
				kind:     MemoryCGroup,
				filename: MemoryReclaimFile,
				container: &types.ContainerJSON{
					ContainerJSONBase: &types.ContainerJSONBase{
						ID: "container-id",
						HostConfig: &container.HostConfig{
							Resources: container.Resources{
								CgroupParent: "/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2",
							},
						},
					},
				},
			},
			want:    "/cce-cgroup/memory/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2/container-id/memory.background_reclaim",
			wantErr: false,
		},
		{
			name:   "get memory.high_wmark_distance file",
			fields: fields{},
			args: args{
				ctx:      context.TODO(),
				kind:     MemoryCGroup,
				filename: MemoryHighWMarkFile,
				container: &types.ContainerJSON{
					ContainerJSONBase: &types.ContainerJSONBase{
						ID: "container-id",
						HostConfig: &container.HostConfig{
							Resources: container.Resources{
								CgroupParent: "/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2",
							},
						},
					},
				},
			},
			want:    "/cce-cgroup/memory/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2/container-id/memory.high_wmark_distance",
			wantErr: false,
		},
		{
			name:   "get memory.low_wmark_distance file",
			fields: fields{},
			args: args{
				ctx:      context.TODO(),
				kind:     MemoryCGroup,
				filename: MemoryLowWMarkFile,
				container: &types.ContainerJSON{
					ContainerJSONBase: &types.ContainerJSONBase{
						ID: "container-id",
						HostConfig: &container.HostConfig{
							Resources: container.Resources{
								CgroupParent: "/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2",
							},
						},
					},
				},
			},
			want:    "/cce-cgroup/memory/kubepods/burstable/pod1cb381bd-5c8a-4f8f-988b-853c06fc5dc2/container-id/memory.low_wmark_distance",
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			m := &Manager{
				dockerClient: tt.fields.dockerClient,
			}
			got, err := m.getCGroupPathWithFile(tt.args.ctx, tt.args.container, tt.args.kind, tt.args.filename)
			if (err != nil) != tt.wantErr {
				t.Errorf("error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("diff= %v", cmp.Diff(got, tt.want))
			}
		})
	}
}

func Test_Run(t *testing.T) {
	eventsCases := []struct {
		events []events.Message
	}{
		{
			events: []events.Message{},
		},
		{

			events: []events.Message{
				{
					Type:   "container",
					ID:     "test-container-id",
					Action: "restart",
				},
			},
		},
	}

	cli, err := client.NewClientWithOpts(client.WithHTTPClient(newMockClient(func(req *http.Request) (*http.Response, error) {
		if strings.Contains(req.URL.Path, "events") {
			buffer := new(bytes.Buffer)
			for _, eventsCase := range eventsCases {
				for _, e := range eventsCase.events {
					b, _ := json.Marshal(e)
					buffer.Write(b)
				}
			}
			return &http.Response{
				StatusCode: http.StatusOK,
				Body:       ioutil.NopCloser(buffer),
			}, nil
		}

		header := http.Header{}
		header.Set("Content-Type", "application/json")
		response := types.ContainerJSON{
			ContainerJSONBase: &types.ContainerJSONBase{
				ID: "test-container-id",
			},
			Mounts: nil,
			Config: &container.Config{
				Labels: map[string]string{
					"io.kubernetes.pod.name":       "test-pod",
					"io.kubernetes.pod.namespace":  "test-namespace",
					"io.kubernetes.container.name": "test-container",
				},
			},
			NetworkSettings: nil,
		}
		body, err := json.Marshal(response)
		if err != nil {
			return nil, err
		}

		return &http.Response{
			StatusCode: 200,
			Body:       ioutil.NopCloser(bytes.NewReader(body)),
			Header:     header,
		}, nil

	})))
	if err != nil {
		panic(err)
	}

	m := Manager{dockerClient: &docker.Client{Cli: cli}}

	go func() {
		m.Run()
	}()

	time.Sleep(time.Second * 3)
}
