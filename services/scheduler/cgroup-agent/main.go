// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/13 16:19:00, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package main

import (
	"context"
	"flag"
	"os"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	beelog "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger/beego"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/cgroup-agent/manager"
)

func main() {
	var logFile string

	flag.StringVar(&logFile, "log", "", "log file path")
	flag.Parse()

	// 初始化 Logger
	logger.SetLogger(beelog.NewLogger(logFile))

	ctx := context.TODO()

	// 初始化 manager
	m, err := manager.NewManager(ctx)
	if err != nil {
		logger.Errorf(ctx, "NewManager failed: %v", err)
		os.Exit(1)
	}

	// 运行
	if err = m.Run(); err != nil {
		logger.Errorf(ctx, "Run failed: %v", err)
		os.Exit(1)
	}

	os.Exit(0)
}
