apiVersion: v1
kind: Pod
metadata:
  name: cce-cpuoffline-agent
  namespace: kube-system
spec:
  containers:
  - image: registry.baidubce.com/cce-plugin-dev/cpuoffline-agent:chenhuan
    imagePullPolicy: Always
    name: cce-cpuoffline-agent
    volumeMounts:
    - name: sock
      mountPath: /var/run/docker.sock
    - name: cgroup-cpu
      mountPath: /cce-cgroup/
  dnsPolicy: ClusterFirst
  nodeName: *************
  volumes:
  - name: sock
    hostPath:
      path: /var/run/docker.sock
  - name: cgroup-cpu
    hostPath:
      path: /sys/fs/cgroup/cpu
