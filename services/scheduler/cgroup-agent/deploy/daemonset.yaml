apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cce-cgroup-agent
  namespace: kube-system
  labels:
    app: cce-cgroup-agent
spec:
  selector:
    matchLabels:
      app: cce-cgroup-agent
  template:
    metadata:
      labels:
        app: cce-cgroup-agent
    spec:
      containers:
      - name: cce-cgroup-agent
        image: registry.baidubce.com/cce-plugin-dev/cgroup-agent:chenhuan
        imagePullPolicy: Always
        volumeMounts:
        - name: sock
          mountPath: /var/run/docker.sock
        - name: cgroup
          mountPath: /cce-cgroup/
      volumes:
      - name: sock
        hostPath:
          path: /var/run/docker.sock
      - name: cgroup
        hostPath:
          path: /sys/fs/cgroup/