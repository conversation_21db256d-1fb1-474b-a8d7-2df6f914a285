apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: master-reloader
rules:
  - apiGroups: [ "" ]
    resources:
      - services
      - configmaps
    verbs: [ "get", "list"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: master-reloader
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: master-reloader
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: master-reloader
subjects:
  - kind: ServiceAccount
    name: master-reloader
    namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    k8s-app: master-reloader
  name: master-reloader
  namespace: kube-system
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      k8s-app: master-reloader
  template:
    metadata:
      labels:
        k8s-app: master-reloader
    spec:
      containers:
        - command:
            - /bin/master-reloader
            - --enables=scheduler-reload
          image: registry.baidubce.com/cce-plugin-dev/master-reloader:v0.2.0
          imagePullPolicy: Always
          name: master-reloader
          volumeMounts:
            - mountPath: /etc/kubernetes/
              name: kubernetes
            - mountPath: /etc/reloader
              name: master-reloader
      hostNetwork: true
      nodeSelector:
        node-role.kubernetes.io/master: ""
      priorityClassName: system-node-critical
      serviceAccount: master-reloader
      restartPolicy: Always
      tolerations:
        - operator: Exists
      volumes:
        - hostPath:
            path: /etc/kubernetes/
          name: kubernetes
        - configMap:
            defaultMode: 420
            name: master-reloader
          name: master-reloader
  updateStrategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
