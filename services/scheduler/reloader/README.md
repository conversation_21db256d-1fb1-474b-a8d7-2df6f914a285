## master-reload

master-reload 以插件化的方式管理用户集群 master 节点上各个组件配置的统一生成，并执行更新，当前只支持 scheduler；

### 功能列表

* `scheduler-reload` 支持 scheduler extender 配置更新以及 reload；

### scheduler-reload 工作原理

* 监听集群内所有带有 `cce-scheduler-extender-metadata: "true` 标签的 configmap 的事件
* configmap Data 字段以 key value 的方式指定配置, key 为 cce-scheduler-extender-metadata， value 为 scheduler extender 配置, 详情如下：
    ```yaml
  data:
      cce-scheduler-extender-metadata: |
        {
          "extender":{
              "urlPrefix":"http://<endpoint>/dysched/extender", # <endpoint> 为占位符，scheduler-reload 会动态替换为 svc 的 ip
              "prioritizeVerb":"prioritize",
              "filterVerb":"filter",
              "enableHttps":false,
              "nodeCacheCapable":true,
              "ignorable":true,
              "weight":10
          },
          "service":"cce-dysched-extender", # extender 插件对应的 svc 
          "port":8080 # extender 插件对应的 svc 对应的端口
        }
    ```
* scheduler-reload 间隔 60s 统一处理 configmap 事件队列中堆积的事件，并按照如下规则重新生成新的 policy 配置
    1. 检测 originPolicyFile 是否存在，如果不存在拷贝一份 outputFile 的内容作为 originPolicyFile
    2. list 所有 configmap, 在 originPolicyFile extenders 基础上 append configmap 里面的 extender 配置，记为 tmpPolicy
    3. 解析 policyFile 得到 currentPolicy
    4. 对比 currentPolicy 与 tmpPolicy hash 值，如果不相同则覆盖 currentPolicy，并触发 scheduler 重启；

### scheduler-reload 使用

#### 以守护进程方式部署在 master 节点上

1. 部署 master-reload ds

```bash

$ vim deploy.yaml # 根据实际需要启用相关插件 --enables=scheduler-reload 启用多 --enables=scheduler-reload,apiserver-reload
$ kubectl apply -f deploy.yaml
```

3. 部署 extender 插件，并按照如下要求配置创建 extender 配置

```yaml
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
  labels:
    app: cce-dysched-extender
  name: cce-dysched-extender # extender 插件对应的 svc
  namespace: kube-system
spec:
  ports:
    - name: http
      port: 8080
      targetPort: 8080
  selector:
    app: cce-dysched-extender
  sessionAffinity: None
  type: LoadBalancer
---
apiVersion: v1
data:
  cce-scheduler-extender-metadata: |
    {
      "extender":{
          "urlPrefix":"http://<endpoint>/dysched/extender", # <endpoint> 为占位符，scheduler-reload 会动态替换为 svc 的 ip
          "prioritizeVerb":"prioritize",
          "filterVerb":"filter",
          "enableHttps":false,
          "nodeCacheCapable":true,
          "ignorable":true,
          "weight":10
      },
      "service":"cce-dysched-extender", # extender 插件对应的 svc 
      "port":8080 # extender 插件对应的 svc 对应的端口
    }
kind: ConfigMap
metadata:
  labels:
    cce-scheduler-extender-metadata: "true" #  scheduler-reload 会通过这个标签 watch 这个 configmap 的变化
  name: dysched-extender
  namespace: kube-system
```

#### 以 job 方式在 helm hook 启用

参考 `pkg/plugin/helm/charts/cce-gpu-manager/templates/helm-hook.yaml`