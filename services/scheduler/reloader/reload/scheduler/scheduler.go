package scheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"sort"
	"strings"
	"time"

	"github.com/mitchellh/hashstructure/v2"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	schedconfig "k8s.io/kube-scheduler/config/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/executor"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/reloader/config"
)

const (
	Name                  = "scheduler-reload"
	policyFile            = "/etc/kubernetes/scheduler-policy.json"
	originPolicyFile      = "/etc/kubernetes/scheduler-policy.json.origin"
	binReloadScript       = `systemctl restart kube-scheduler`
	containerReloadScript = `mv /etc/kubernetes/manifests/kube-scheduler.yaml /tmp/kube-scheduler.yaml && sleep 3 && mv /tmp/kube-scheduler.yaml /etc/kubernetes/manifests/kube-scheduler.yaml`
	metadataKey           = "cce-scheduler-extender-metadata"
	placeholder           = "<endpoint>"
)

var selector = fmt.Sprintf("%v=true", metadataKey)

type schedulerReloader struct {
	cfg          config.ReloadConfig
	client       kubernetes.Interface
	exec         executor.Executor
	reloadScript string
	kubeConfig   string
}

// NewSchedulerReloader new
func NewSchedulerReloader(ctx context.Context, kubeConfig string, cfg config.ReloadConfig) (*schedulerReloader, error) {
	client, err := utils.NewClientSet(kubeConfig)
	if err != nil {
		logger.Errorf(ctx, "new k8s clientset err: %v", err)
		return nil, err
	}

	s := &schedulerReloader{
		cfg:          cfg,
		client:       client,
		reloadScript: containerReloadScript,
		exec:         executor.NewExecutor("/bin/sh"),
	}

	if cfg.RunEnv == config.Bin {
		s.reloadScript = binReloadScript
	}
	return s, nil
}

// Name 返回插件的名字
func (s *schedulerReloader) Name() string {
	return Name
}

// Run should use go s.Run(ctx)
func (s *schedulerReloader) Run(ctx context.Context, runOne bool) error {
	logger.Infof(ctx, "start %v ...", Name)

	if err := s.initOriginPolicy(ctx); err != nil {
		logger.Errorf(ctx, "init origin policy err: %v", err)
		return err
	}

	// runOne 以 job 的方式运行
	if runOne {
		// job 方式运行把错误抛给 os 触发 job 重试
		return s.sync()
	}

	// 后台进程运行
	wait.Until(func() {
		if err := s.sync(); err != nil {
			logger.Warnf(ctx, "sync err: %v, retry after 3m...", err)
		}
	}, time.Minute*3, ctx.Done())
	return nil
}

// sync 同步
// 1. 检测 originPolicyFile 是否存在，如果不存在拷贝一份 policyFile 的内容作为 originPolicyFile
// 2. list 所有 configmap, 在 originPolicyFile extenders 基础上 append configmap 里面的 extender 配置，记为 tmpPolicy
// 3. 解析 policyFile 得到 currentPolicy
// 4. 对比 currentPolicy 与 tmpPolicy hash 值，如果不相同则覆盖 currentPolicy
func (s *schedulerReloader) sync() error {
	ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())

	tmpPolicy, err := s.buildPolicy(ctx)
	if err != nil {
		logger.Errorf(ctx, "build policy err: %v", err)
		return err
	}

	curPolicy, err := s.readPolicy(ctx, policyFile)
	if err != nil {
		logger.Errorf(ctx, "get policy from %v err: %v", policyFile, err)
		return err
	}

	tmpHash, _ := hashstructure.Hash(tmpPolicy, hashstructure.FormatV2, nil)
	curHash, _ := hashstructure.Hash(curPolicy, hashstructure.FormatV2, nil)

	if tmpHash == curHash {
		logger.Infof(ctx, "policy not change, skip ...")
		return nil
	}

	if err := s.savePolicy(ctx, policyFile, *tmpPolicy); err != nil {
		logger.Errorf(ctx, "save policy err: %v", err)
		return err
	}

	logger.Infof(ctx, "set %v success.", policyFile)

	output, stderr, err := s.exec.ShellExec(ctx, s.reloadScript)
	if err != nil {
		logger.Errorf(ctx, "run: '%v' err: %v, output: %v, stderr: %v", s.reloadScript, err, output, stderr)
		return err
	}

	logger.Infof(ctx, "reload scheduler success.")
	return nil
}

// buildPolicy 从 originPolicyFile 中构建 policy
func (s *schedulerReloader) buildPolicy(ctx context.Context) (*schedconfig.Policy, error) {
	policy, err := s.readPolicy(ctx, originPolicyFile)
	if err != nil {
		logger.Errorf(ctx, "get policy from %v err: %v", originPolicyFile, err)
		return nil, err
	}

	cmList, err := s.client.CoreV1().ConfigMaps(metav1.NamespaceAll).List(ctx, metav1.ListOptions{
		LabelSelector: selector,
	})

	if err != nil {
		logger.Errorf(ctx, "list configmap err: %v", err)
		return nil, err
	}

	for _, cm := range cmList.Items {
		if strings.Contains(s.cfg.IgnoreConfigmaps, fmt.Sprintf("%s/%s", cm.Namespace, cm.Name)) {
			logger.Warnf(ctx, "ignore configmap %v/%v", metadataKey, cm.Namespace, cm.Name)
			continue
		}

		data, ok := cm.Data[metadataKey]
		if !ok {
			logger.Errorf(ctx, "not found %v in configmap %v/%v", metadataKey, cm.Namespace, cm.Name)
			continue
		}

		var em ExtenderMetadata

		if err := json.Unmarshal([]byte(data), &em); err != nil {
			logger.Errorf(ctx, "not found %v in configmap", metadataKey)
			continue
		}

		if len(em.Namespace) == 0 {
			em.Namespace = cm.Namespace
		}

		extender, err := s.buildExtender(ctx, em)
		if err != nil {
			logger.Errorf(ctx, "build extender err: %v", err)
			// 构建 extender 失败需要重试，有可能是因为 vpc blb 未就绪
			continue
		}

		policy.Extenders = append(policy.Extenders, *extender)
	}

	sort.SliceStable(policy.Extenders, func(i, j int) bool {
		return policy.Extenders[i].URLPrefix < policy.Extenders[j].URLPrefix
	})

	return policy, nil
}

// buildExtender 动态生成 extender URLPrefix
func (s *schedulerReloader) buildExtender(ctx context.Context, em ExtenderMetadata) (*schedconfig.LegacyExtender, error) {
	svc, err := s.client.CoreV1().Services(em.Namespace).Get(ctx, em.ServiceName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get %s/%s svc err: %v", em.Namespace, em.ServiceName, err)
		return nil, err
	}

	lb := svc.Spec.ClusterIP

	// 二进制部署 master extender ip 为内网 blb ip
	if s.cfg.RunEnv == config.Bin {
		if len(svc.Status.LoadBalancer.Ingress) == 0 {
			return nil, fmt.Errorf("bin env need vip internel blb, but not found")
		}

		lb = svc.Status.LoadBalancer.Ingress[0].IP
	}

	endpoint := fmt.Sprintf("%s:%d", lb, em.Port)

	em.Extender.URLPrefix = strings.Replace(em.Extender.URLPrefix, placeholder, endpoint, 1)

	return &em.Extender, nil
}

// initOriginPolicy 初始化 检测 originPolicyFile 是否存在，如果不存在拷贝一份 outputFile 的内容作为 originPolicyFile
func (s *schedulerReloader) initOriginPolicy(ctx context.Context) error {
	f, err := os.Stat(originPolicyFile)
	if err != nil && !os.IsNotExist(err) {
		logger.Errorf(ctx, "check %v err: %v", originPolicyFile, err)
		return err
	}

	// originPolicyFile 不存在
	if os.IsNotExist(err) {
		logger.Infof(ctx, "%v not exist, try copy from %v", originPolicyFile, policyFile)

		input, err := ioutil.ReadFile(policyFile)
		if err != nil {
			logger.Errorf(ctx, "read %v err: %v", policyFile, err)
			return err
		}

		if err := ioutil.WriteFile(originPolicyFile, input, 0644); err != nil {
			logger.Errorf(ctx, "write %v err: %v", policyFile, err)
			return err
		}

		return nil
	}

	logger.Infof(ctx, "%v exist, last updated at %v", originPolicyFile, f.ModTime())
	return nil
}

func (s *schedulerReloader) savePolicy(ctx context.Context, filename string, policy schedconfig.Policy) error {
	data, err := json.Marshal(policy)
	if err != nil {
		logger.Errorf(ctx, "marshal policy err: %v", err)
		return err
	}

	if err := ioutil.WriteFile(filename, data, 0664); err != nil {
		logger.Errorf(ctx, "write file err: %v", err)
		return err
	}

	return nil
}

// readPolicy 从指定文件中获取 Scheduler Policy
func (s *schedulerReloader) readPolicy(ctx context.Context, filename string) (*schedconfig.Policy, error) {
	data, err := ioutil.ReadFile(filename)
	if err != nil {
		logger.Errorf(ctx, "read %v err: %v", filename)
		return nil, err
	}

	var policy schedconfig.Policy

	if err := json.Unmarshal(data, &policy); err != nil {
		logger.Errorf(ctx, "unmarshal data err: %v", err)
		return nil, err
	}

	sort.SliceStable(policy.Extenders, func(i, j int) bool {
		return policy.Extenders[i].URLPrefix < policy.Extenders[j].URLPrefix
	})

	return &policy, nil
}
