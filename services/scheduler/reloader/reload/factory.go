package reload

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/reloader/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/reloader/reload/scheduler"
)

// Interface 定义对象的行为
type Interface interface {
	Run(ctx context.Context, runOne bool) error
	Name() string
}

// New new
func New(ctx context.Context, name, kubeConfig string, cfg config.ReloadConfig) (Interface, error) {
	switch name {
	case scheduler.Name:
		return scheduler.NewSchedulerReloader(ctx, kubeConfig, cfg)
	default:
		return nil, fmt.Errorf("not support %v", name)
	}
}
