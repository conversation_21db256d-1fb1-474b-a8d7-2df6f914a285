// Copyright 2019 Baidu Inc. All rights reserved.
// Use of this source code is governed by a xxx
// license that can be found in the LICENSE file.
package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"strings"
	"syscall"

	"golang.org/x/sync/errgroup"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger/beego"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/reloader/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/reloader/reload"
)

func main() {
	var (
		kubeConfig string
		enables    string
		runOne     bool
		term       = make(chan os.Signal)
		cfg        config.ReloadConfig
	)

	flag.StringVar(&kubeConfig, "kubeconfig", "", "kube config")
	flag.StringVar(&enables, "enables", "", "enable run reload components, split by ','")
	flag.BoolVar(&runOne, "run-one", runOne, "run as job or daemon, true run as job, default as daemon")
	flag.StringVar(&cfg.RunEnv, "env", config.Container, "run env")
	flag.StringVar(&cfg.IgnoreConfigmaps, "ignore-configmaps", "", "ignore configmaps key, split by ',' such as 'kube-system/gpushare-scheduler-extender'")
	flag.Parse()

	// 初始化 Logger
	logger.SetLogger(beego.NewLogger(""))

	ctx, cancel := context.WithCancel(context.Background())
	ctx = context.WithValue(ctx, logger.RequestID, logger.GetUUID())

	enablePlugins := strings.Split(strings.Trim(enables, " "), ",")
	if len(enablePlugins) == 0 {
		logger.Errorf(ctx, "enables specify empty params")
		os.Exit(1)
	}

	wg, ctx := errgroup.WithContext(ctx)

	for _, item := range enablePlugins {
		w, err := reload.New(ctx, item, kubeConfig, cfg)
		if err != nil {
			logger.Errorf(ctx, "new %s err: %v", item, err)
			os.Exit(1)
		}

		wg.Go(func() error { return w.Run(ctx, runOne) })
	}

	if !runOne {
		signal.Notify(term, os.Interrupt, syscall.SIGTERM)
		<-term

		cancel()
	}

	if err := wg.Wait(); err != nil {
		logger.Errorf(ctx, "received err: %v", err)
		os.Exit(1)
	}

}
