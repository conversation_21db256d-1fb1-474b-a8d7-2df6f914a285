#!/usr/bin/env bash
# exit immediately when a command fails
set -e
# only exit with zero if all commands of the pipeline exit successfully
set -o pipefail
# error on unset variables
set -u

make prepare

GO_PKG=icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg

TAG=$(git rev-parse --short HEAD)
BUILD=$(date +%FT%T%z)
DATE=$(date +%Y%m%d%H%M%S)

go run -ldflags "-X ${GO_PKG}/version.Version=${TAG} \
    -X ${GO_PKG}/version.Build=${BUILD} \
    -X ${GO_PKG}/version.Date=${DATE} \
    -X ${GO_PKG}/version.Tag=${TAG}" \
  main.go "--kubeconfig=$HOME/.kube/config-dev"
