// Copyright 2016 The prometheus-controller Authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package utils

import (
	"fmt"
	"os"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

// KubeConfigEnv (optionally) specify the location of kubeconfig file
const KubeConfigEnv = "KUBECONFIG"

// NewClusterConfig new kubeconfig
func NewClusterConfig(kubeconfig string) (*rest.Config, error) {
	var (
		cfg *rest.Config
		err error
	)

	if kubeconfig == "" {
		kubeconfig = os.Getenv(KubeConfigEnv)
	}

	if kubeconfig != "" {
		cfg, err = clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, fmt.Errorf("error creating config from specified file: %v %w", kubeconfig, err)
		}
	} else {
		if cfg, err = rest.InClusterConfig(); err != nil {
			return nil, err
		}
	}

	// client 设置超时事件为 5min
	cfg.Timeout = time.Minute * 3

	cfg.QPS = 500
	cfg.Burst = 500

	return cfg, nil
}

// NewClientset new client set
func NewClientset(kubeconfig string) (*kubernetes.Clientset, error) {
	cfg, err := NewClusterConfig(kubeconfig)
	if err != nil {
		return nil, err
	}

	return kubernetes.NewForConfig(cfg)
}
