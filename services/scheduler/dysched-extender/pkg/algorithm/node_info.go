package algorithm

import (
	"math"

	"k8s.io/apimachinery/pkg/api/resource"
)

// NodeInfo 节点状态统计
type NodeInfo struct {
	NodeName string
	// 真实 cpu 使用率 0--100
	RealCPURate int64
	// cpu request 率 0--100
	RequestCPURate int64

	// 节点剩余可分配 cpu
	RemainAllocatableCPU resource.Quantity

	// 节点 labels
	Labels map[string]string
}

// NodeInfos 多个节点的统计状态
type NodeInfos []NodeInfo

// Less 倒序排序 最后一个节点权重最高
func (s NodeInfos) Less(i, j int) bool {
	// 真实使用率 绝对值 < 5% 认为相等,使用率相等情况下以 RequestCPURate 排序，也是倒序排序
	if math.Abs(float64(s[i].RealCPURate-s[j].RealCPURate)) < 5.0 {

		// request rate < 5% 认为相等，
		if math.Abs(float64(s[i].RequestCPURate-s[j].RequestCPURate)) < 5.0 {
			// 剩余量从小到大排序
			return s[i].RemainAllocatableCPU.MilliValue() < s[j].RemainAllocatableCPU.MilliValue()
		}

		return s[i].RequestCPURate > s[j].RequestCPURate
	}

	return s[i].RealCPURate > s[j].RealCPURate
}

// Swap s
func (s NodeInfos) Swap(i, j int) {
	s[i], s[j] = s[j], s[i]
}

// Len l
func (s NodeInfos) Len() int {
	return len(s)
}
