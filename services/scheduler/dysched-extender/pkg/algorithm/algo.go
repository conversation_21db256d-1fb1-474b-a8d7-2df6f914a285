package algorithm

import (
	"container/list"
	"context"
	"fmt"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	coreinformerv1 "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog"
	metricsv1beta1 "k8s.io/metrics/pkg/apis/metrics/v1beta1"
	metricsclientset "k8s.io/metrics/pkg/client/clientset/versioned"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/utils"
)

var (
	reSyncPeriod = time.Minute

	nodePodIndexName = "node_pod"
)

// Cache cache
type Cache interface {
	GetNodeInfos(nodeNames []string, pod *corev1.Pod) NodeInfos
	GetNodeInfo(nodeName string, pod *corev1.Pod) NodeInfo
	Close()
}

// NodeCache 节点缓存
type NodeCache struct {
	metricsClient metricsclientset.Interface
	// 每一个节点缓存 15 个时间窗口内的 	metrics.NodeMetrics
	nodeMetrics  map[string]*list.List
	clientSet    kubernetes.Interface
	nodeInformer coreinformerv1.NodeInformer
	podInformer  cache.SharedIndexInformer
	stopCh       chan struct{}

	sync.RWMutex
}

// NewNodeCache new node cache
func NewNodeCache(kubeconfig string) (*NodeCache, error) {
	cfg, err := utils.NewClusterConfig(kubeconfig)
	if err != nil {
		return nil, err
	}

	metricsClient, err := metricsclientset.NewForConfig(cfg)
	if err != nil {
		return nil, err
	}

	client, err := kubernetes.NewForConfig(cfg)
	if err != nil {
		return nil, err
	}

	nc := NodeCache{
		nodeMetrics:   make(map[string]*list.List),
		clientSet:     client,
		stopCh:        make(chan struct{}),
		metricsClient: metricsClient,
	}

	if err := nc.init(); err != nil {
		return nil, err
	}

	return &nc, nil
}

func (nc *NodeCache) init() error {
	informerFactory := informers.NewSharedInformerFactory(nc.clientSet, reSyncPeriod)

	nodeInformer := informerFactory.Core().V1().Nodes()
	go nodeInformer.Informer().Run(nc.stopCh)

	if !cache.WaitForCacheSync(nc.stopCh, nodeInformer.Informer().HasSynced) {
		return fmt.Errorf("wait for node cache sync error")
	}

	klog.Info("sync node cache successful.")

	podInformer := coreinformerv1.NewPodInformer(
		nc.clientSet, metav1.NamespaceAll, 0,
		cache.Indexers{
			nodePodIndexName: func(obj interface{}) ([]string, error) {
				pod, ok := obj.(*corev1.Pod)
				if !ok {
					return nil, fmt.Errorf("type error")
				}
				return []string{pod.Spec.NodeName}, nil
			},
		},
	)

	go podInformer.Run(nc.stopCh)

	if !cache.WaitForCacheSync(nc.stopCh, podInformer.HasSynced) {
		return fmt.Errorf("wait for all pod cache sync error")
	}

	klog.Info("sync all pod cache successful.")

	nc.nodeInformer = nodeInformer
	nc.podInformer = podInformer

	// 尝试获取节点的 metrics 5 次，尽量在初始化阶段获取所有节点的 资源使用情况
	for i := 0; i < 5; i++ {
		if nc.scrapeNodeMetrics() {
			klog.Info("init all node metrics successful.")
			break
		}
		klog.Warningf("try init all node metrics failed, on %d times", i)
	}

	go func() {
		// 一分钟更新一次 nodeMetrics 缓存
		wait.Until(func() { nc.scrapeNodeMetrics() }, time.Minute, nc.stopCh)
	}()

	return nil
}

func (nc *NodeCache) calcNodeRequestCPUTotal(nodeName string, pod *corev1.Pod) resource.Quantity {
	var total resource.Quantity
	objs, err := nc.podInformer.GetIndexer().ByIndex(nodePodIndexName, nodeName)
	if err != nil {
		return total
	}

	klog.V(3).Infof("get %d pod for node %v", len(objs), nodeName)

	objs = append(objs, pod)

	for _, item := range objs {
		p, ok := item.(*corev1.Pod)
		if !ok {
			klog.Errorf("kind is not *corev1.Pod")
			continue
		}

		for _, c := range p.Spec.Containers {
			if c.Resources.Requests.Cpu() != nil {
				total.Add(*c.Resources.Requests.Cpu())
			}
		}
	}

	return total
}

func (nc *NodeCache) scrapeNodeMetrics() bool {
	klog.V(3).Infof("start to scrape node metrics...")

	nodes, err := nc.nodeInformer.Lister().List(labels.Everything())
	if err != nil {
		klog.Errorf("list node from node informer err: %v", err)
		return false
	}

	for _, n := range nodes {
		metrics, err := nc.metricsClient.MetricsV1beta1().NodeMetricses().Get(context.TODO(), n.Name, metav1.GetOptions{})
		if err != nil {
			klog.Warningf("get node: %v metrics err: %v", n.Name, err)
			continue
		}

		klog.V(3).Infof("get %v node metrics success", n.Name)

		nc.Lock()
		if _, ok := nc.nodeMetrics[n.Name]; !ok {
			nc.nodeMetrics[n.Name] = list.New()
		}

		nc.nodeMetrics[n.Name].PushBack(metrics)

		// 保证每一个节点只缓存最近 15 个时间窗口的 metrics
		if nc.nodeMetrics[n.Name].Len() > 15 {
			nc.nodeMetrics[n.Name].Remove(nc.nodeMetrics[n.Name].Front())
		}
		nc.Unlock()

		// 50ms 调用 metrics server 的接口一次，避免请求太频繁导致 apiserver 受影响
		time.Sleep(time.Millisecond * 50)

	}

	nc.RLock()
	res := len(nc.nodeMetrics) == len(nodes)
	nc.RUnlock()

	return res
}

func (nc *NodeCache) getNodeMetrics(nodeName string) *metricsv1beta1.NodeMetrics {
	nc.RLock()
	defer nc.RUnlock()

	// 这里暂时只取队最新的一个节点

	if nc.nodeMetrics[nodeName] == nil {
		return nil
	}

	// todo 考虑更精确的计算算法
	e := nc.nodeMetrics[nodeName].Back()
	if e == nil {
		return nil
	}

	return e.Value.(*metricsv1beta1.NodeMetrics)
}

// GetNodeInfos get nodes cpu state
func (nc *NodeCache) GetNodeInfos(nodeNames []string, pod *corev1.Pod) NodeInfos {
	infos := make([]NodeInfo, len(nodeNames))

	wg := sync.WaitGroup{}
	for i, name := range nodeNames {
		wg.Add(1)
		go func(i int, name string) {
			defer wg.Done()
			infos[i] = nc.GetNodeInfo(name, pod)
		}(i, name)
	}

	wg.Wait()
	return infos
}

// GetNodeInfo get single node cpu state
func (nc *NodeCache) GetNodeInfo(nodeName string, pod *corev1.Pod) NodeInfo {
	info := NodeInfo{
		NodeName:       nodeName,
		RealCPURate:    100,
		RequestCPURate: 100,
	}

	node, err := nc.nodeInformer.Lister().Get(nodeName)
	if err != nil {
		klog.Errorf("ge%v err: %v", nodeName, err)
		return info
	}

	info.Labels = node.Labels

	// getNodeMetrics 有可能得到 nil
	metrics := nc.getNodeMetrics(nodeName)
	if metrics == nil {
		return info
	}

	if use, ok := metrics.Usage[corev1.ResourceCPU]; ok {
		totalRequest := nc.calcNodeRequestCPUTotal(nodeName, pod)

		lastTotal := *node.Status.Allocatable.Cpu()

		lastTotal.Sub(totalRequest)

		info.RemainAllocatableCPU = lastTotal

		info.RequestCPURate = int64(100 * (float64(totalRequest.MilliValue()) / float64(node.Status.Allocatable.Cpu().MilliValue())))
		info.RealCPURate = int64(100 * (float64(use.MilliValue()) / float64(node.Status.Capacity.Cpu().MilliValue())))
	}

	return info
}

// Close close every thing
func (nc *NodeCache) Close() {
	klog.Infof("close node cache")
	close(nc.stopCh)
}
