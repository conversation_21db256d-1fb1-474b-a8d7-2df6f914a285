package metrics

import (
	"fmt"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus"
)

var (
	RequestsCounter = prometheus.NewCounterVec(prometheus.CounterOpts{
		Namespace: "dysched",
		Subsystem: "scheduler",
		Name:      "extender",
		Help:      "Requests count by method/path/status.",
	}, []string{"method", "path", "status"})
)

func init() {
	prometheus.MustRegister(
		RequestsCounter,
	)
}

// RequestCount gin mid
func RequestCount() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 404 不统计
		if c.Writer.Status() == http.StatusNotFound {
			return
		}

		path := c.FullPath()

		labelValues := []string{c.Request.Method, path, fmt.Sprintf("%d", c.Writer.Status())}

		RequestsCounter.WithLabelValues(labelValues...).Inc()

	}
}
