package apis

import (
	"net/http"
	"net/http/pprof"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

type API interface {
	RegisterAPI(r *gin.Engine)
}

type Handler struct {
}

type RestAPI interface {
	Create(c *gin.Context)
	Update(c *gin.Context)
	Delete(c *gin.Context)
	Get(c *gin.Context)
	List(c *gin.Context)
}

type DebugAPI struct {
}

func (DebugAPI) ok(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "success"})
}

func (d *DebugAPI) RegisterAPI(r *gin.Engine) {
	r.GET("/healthz", d.ok)
	r.GET("/debug/pprof", gin.WrapF(pprof.Index))
	r.GET("/debug/pprof/cmdline", gin.WrapF(pprof.Cmdline))
	r.GET("/debug/pprof/profile", gin.WrapF(pprof.Profile))
	r.GET("/debug/pprof/symbol", gin.WrapF(pprof.Symbol))
	r.GET("/debug/pprof/trace", gin.WrapF(pprof.Trace))
	r.GET("/metrics", gin.WrapH(promhttp.Handler()))
}
