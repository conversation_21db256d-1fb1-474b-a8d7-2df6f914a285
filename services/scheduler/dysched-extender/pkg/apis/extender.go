package apis

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	extenderv1 "k8s.io/kube-scheduler/extender/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/algorithm"
)

var (
	errParamsBody = gin.H{"message": "params error"}
)

// Extender scheduler dysched
type Extender struct {
	ToleranceRate int64
	NodeCache     algorithm.Cache
}

// Filter impl scheduler filter func
func (e *Extender) Filter(c *gin.Context) {
	var args extenderv1.ExtenderArgs

	if err := c.Bind(&args); err != nil {
		c.JSON(http.StatusBadRequest, errParamsBody)
		return
	}

	klog.V(2).Infof("scheduler call filter for %v/%v", args.Pod.Namespace, args.Pod.Name)

	c.JSO<PERSON>(http.StatusOK, e.filter(args))
}

// Prioritize impl scheduler prioritize func
func (e *Extender) Prioritize(c *gin.Context) {
	var args extenderv1.ExtenderArgs

	if err := c.Bind(&args); err != nil {
		c.JSON(http.StatusBadRequest, errParamsBody)
		return
	}

	klog.V(2).Infof("scheduler call prioritize for %v/%v", args.Pod.Namespace, args.Pod.Name)

	c.JSON(http.StatusOK, e.prioritize(args))
}

func (e *Extender) filter(args extenderv1.ExtenderArgs) *extenderv1.ExtenderFilterResult {
	result := extenderv1.ExtenderFilterResult{
		FailedNodes: make(extenderv1.FailedNodesMap),
	}

	if args.NodeNames == nil {
		result.Error = "filter args.NodeNames must be specify"
		return &result
	}

	feasibleNodes := make([]string, 0)

	if args.Pod.Namespace != v1.NamespaceSystem {
		nodesStats := e.NodeCache.GetNodeInfos(*args.NodeNames, args.Pod)
		for _, item := range nodesStats {
			if item.RealCPURate > e.ToleranceRate {
				result.FailedNodes[item.NodeName] = fmt.Sprintf("Real cpu rate > %v", e.ToleranceRate)
			} else {
				feasibleNodes = append(feasibleNodes, item.NodeName)
			}
		}

	} else {
		feasibleNodes = *args.NodeNames
	}

	result.NodeNames = &feasibleNodes

	if klog.V(3) {
		body, _ := json.Marshal(feasibleNodes)
		klog.V(3).Infof("feasible nodes body: %s", string(body))

		body, _ = json.Marshal(result.FailedNodes)

		klog.V(3).Infof("failed nodes body: %s", string(body))
	}

	return &result
}

func (e *Extender) prioritize(args extenderv1.ExtenderArgs) *extenderv1.HostPriorityList {
	result := extenderv1.HostPriorityList{}

	if args.NodeNames == nil {
		return &result
	}

	nodeStats := e.NodeCache.GetNodeInfos(*args.NodeNames, args.Pod)

	sort.Slice(nodeStats, func(i, j int) bool {
		// todo 这里可以添加更多的优先级策略
		return nodeStats.Less(i, j)
	})

	if klog.V(3) {
		body, _ := json.Marshal(nodeStats)
		klog.V(3).Infof("prioritize body: %s", string(body))
	}

	for i := len(nodeStats) - 1; i >= 0; i-- {
		item := extenderv1.HostPriority{
			Host:  nodeStats[i].NodeName,
			Score: int64(i),
		}

		result = append(result, item)
	}

	return &result
}

func (e *Extender) debug(c *gin.Context) {
	pod := corev1.Pod{}

	pod.Namespace = c.Query("namespace")
	pod.Name = c.Query("name")
	nodes := strings.Split(c.Query("nodes"), ",")
	if len(nodes) <= 1 {
		c.JSON(http.StatusOK, e.NodeCache.GetNodeInfo(nodes[0], &pod))
		return
	}

	nodeStats := e.NodeCache.GetNodeInfos(nodes, &pod)

	sort.Slice(nodeStats, func(i, j int) bool {
		return nodeStats.Less(i, j)
	})

	if klog.V(3) {
		body, _ := json.Marshal(nodeStats)
		klog.V(3).Infof("body: %s", string(body))
	}

	c.JSON(http.StatusOK, nodeStats)
}

func (e *Extender) RegisterAPI(r *gin.Engine) {
	r.POST("/dysched/extender/filter", e.Filter)
	r.POST("/dysched/extender/prioritize", e.Prioritize)
	r.GET("/dysched/extender/debug", e.debug)
}
