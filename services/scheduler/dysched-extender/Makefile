# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output

# init command params
GO      := $(GO_1_16_BIN)/go
GOROOT  := $(GO_1_16_HOME)
GOPATH  := $(shell $(GO) env GOPATH)
GOMOD   := $(GO) mod
GOBUILD := $(GO) build
GOTEST  := $(GO) test -gcflags="-N -l"
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")
TAG     := $(shell git rev-parse --short HEAD)
IMAGE_NAME :=registry.baidubce.com/registry-zihua/cce-dysched-extender
RELEASE_IMAGE_NAME :=registry.baidubce.com/cce-plugin-pro/cce-dysched-extender

V:=$(shell git describe --tags $(shell git rev-list --tags --max-count=1))

# test cover files
COVPROF := $(HOMEDIR)/covprof.out  # coverage profile
COVFUNC := $(HOMEDIR)/covfunc.txt  # coverage profile information for each function
COVHTML := $(HOMEDIR)/covhtml.html # HTML representation of coverage profile

# make, make all
all: prepare compile package

# set proxy env
set-env:
	$(GO) env -w GO111MODULE=on
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com
	$(GO) env -w GONOSUMDB=\*

#make prepare, download dependencies
prepare: gomod

gomod: set-env
	$(GOMOD) download

#make compile
compile: build

build_local:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o deploy/bin/cce-dysched-extender

build:
	$(GOBUILD) -o $(HOMEDIR)/cce-dysched-extender

# make test, test your code
test: prepare test-case
test-case:
	$(GOTEST) -v -cover $(GOPKGS)

# make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	mv cce-dysched-extender  $(OUTDIR)/

image: build_local
	docker build -f deploy/Dockerfile deploy -t $(IMAGE_NAME):$(TAG)
	docker push $(IMAGE_NAME):$(TAG)
	@cd deploy/yaml && kustomize edit set image "$(IMAGE_NAME)=$(IMAGE_NAME):$(TAG)"

deploy_yaml:
	kustomize build deploy/yaml > deploy/deploy.yaml



release: build_local
	docker build -f deploy/Dockerfile deploy -t $(RELEASE_IMAGE_NAME):$(V)
	docker push $(RELEASE_IMAGE_NAME):$(V)
	@cd deploy/yaml && kustomize edit set image "$(RELEASE_IMAGE_NAME)=$(RELEASE_IMAGE_NAME):$(V)"
	@kustomize build deploy/yaml > deploy/deploy.yaml

# make clean
clean:
	$(GO) clean
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/cce-dysched-extender
	rm -rf $(GOPATH)/pkg/darwin_amd64

# avoid filename conflict and speed up build 
.PHONY: all prepare compile test package clean build image build_local deploy_yaml release
