apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app.kubernetes.io/name: cce-dysched-extender
  name: cce-dysched-extender
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - pods
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - metrics.k8s.io
    resources:
      - nodes
      - pods
    verbs:
      - get
      - list
      - watch
