apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-dysched-extender
  labels:
    app: cce-dysched-extender
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cce-dysched-extender
  template:
    metadata:
      labels:
        app: cce-dysched-extender
    spec:
      serviceAccountName: cce-dysched-extender
      containers:
        - name: dysched-dysched
          args:
            - -v=3
          image: registry.baidubce.com/cce-plugin-pro/cce-dysched-extender:v0.1.0
          imagePullPolicy: Always
          resources:
            limits:
              cpu: "4"
              memory: 4Gi
            requests:
              cpu: "100m"
              memory: 100Mi
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /healthz
              port: http
              scheme: HTTP
            periodSeconds: 60
          readinessProbe:
            httpGet:
              path: /healthz
              port: http
              scheme: HTTP
            periodSeconds: 60