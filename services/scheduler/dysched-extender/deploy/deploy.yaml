apiVersion: v1
kind: ServiceAccount
metadata:
  labels:
    app: cce-dysched-extender
    app.kubernetes.io/component: controller
    app.kubernetes.io/name: cce-dysched-extender
  name: cce-dysched-extender
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  labels:
    app: cce-dysched-extender
    app.kubernetes.io/name: cce-dysched-extender
  name: cce-dysched-extender
rules:
- apiGroups:
  - ""
  resources:
  - nodes
  - pods
  verbs:
  - get
  - list
  - watch
- apiGroups:
  - metrics.k8s.io
  resources:
  - nodes
  - pods
  verbs:
  - get
  - list
  - watch
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  labels:
    app: cce-dysched-extender
    app.kubernetes.io/name: cce-dysched-extender
  name: cce-dysched-extender
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-dysched-extender
subjects:
- kind: ServiceAccount
  name: cce-dysched-extender
  namespace: kube-system
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/cce-load-balancer-internal-vpc: "true"
  labels:
    app: cce-dysched-extender
  name: cce-dysched-extender
  namespace: kube-system
spec:
  ports:
  - name: http
    port: 8080
    targetPort: 8080
  selector:
    app: cce-dysched-extender
  sessionAffinity: None
  type: LoadBalancer
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: cce-dysched-extender
  name: cce-dysched-extender
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cce-dysched-extender
  template:
    metadata:
      labels:
        app: cce-dysched-extender
    spec:
      containers:
      - args:
        - -v=3
        image: registry.baidubce.com/cce-plugin-pro/cce-dysched-extender:v0.1.0
        imagePullPolicy: Always
        livenessProbe:
          httpGet:
            path: /healthz
            port: http
            scheme: HTTP
          periodSeconds: 60
        name: dysched-dysched
        ports:
        - containerPort: 8080
          name: http
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /healthz
            port: http
            scheme: HTTP
          periodSeconds: 60
        resources:
          limits:
            cpu: "4"
            memory: 4Gi
          requests:
            cpu: 100m
            memory: 100Mi
      serviceAccountName: cce-dysched-extender
