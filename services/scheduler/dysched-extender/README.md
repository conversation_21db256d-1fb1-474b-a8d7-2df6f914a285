# cce-dysched-extender(dynamic scheduling extender)

k8s 默认调度器动态调度扩展插件，利用 scheduler extender 机制向 kube-scheduler 注册 Filter、Prioritize 钩子，来干预默认调度器的调度。

> 动态调度：指根据节点的真实资源使用情况进行调度

* 注：当前节点的 metrics 数据主要来自 metrics-server 组件，在部署 cce-dysched-extender 之前需要提前部署好 metrics-server。

## 快速开始

```bash
$ bash ./hack/dev-external.sh # 快速运行，用来 debug
```

## 测试&构建镜像

```bash
$ make test
$ make image
```

## 获取部署 yaml

```bash
$ make deploy_yaml
```

* 部署的 yaml 在 /deploy/deploy.yaml 中

* 部署 cce-dysched-extender 插件
    
```bash
$ make release
$ kubectl apply -f deploy/deploy.yaml
$ DYSCHED_EXTENDER_SVC_IP=$(kubectl get service cce-dysched-extender -n kube-system | awk '{print $4}' | grep -v EXTER)
```

* 修改 /etc/kubernetes/scheduler-policy.json 这个文件内容如下,并把 DYSCHED_EXTENDER_SVC_IP 替换为如上步骤中获取的值

```json
{
  ...... 这里省略了其他字段,
  
  "extenders": [
    ...... 这里省略了其他的 extenders
    {
      "urlPrefix": "http://<DYSCHED_EXTENDER_SVC_IP>:8080/dysched/extender",
      "prioritizeVerb": "prioritize",
      "filterVerb": "filter",
      "enableHttps": false,
      "nodeCacheCapable": true,
      "ignorable": true,
      "weight": 1
    }
  ]
}
```

* 如果没有 /etc/kubernetes/scheduler-policy.json 这个文件，需要复制一份 hack/policy.json 文件的内容，并把 DYSCHED_EXTENDER_SVC_IP 替换为如上步骤中获取的值，然后继续下面步骤
    * 修改 kube-scheduler 的启动参数，加上 --policy-config-file=/etc/kubernetes/scheduler-policy.json
    
* 重启 kube-scheduler



## 主干开发

1. 本地新建分支

```
git checkout -b feature-123 origin/master
git commit
```

2. 向 master 提交评审

```
git push origin HEAD:refs/for/master
```

3. 在 iCode 查看 diff，将代码合入 master

4. 在 iCode 或 Agile上 发布 master (标记 tag)

5. 取消 commit

```
$git reset --soft origin/master
```

参考:

* CCE 迭代发布: http://wiki.baidu.com/pages/viewpage.action?pageId=1117572998
* ICode 工作流: http://wiki.baidu.com/pages/viewpage.action?pageId=193045668

## 讨论

百度Golang交流群：1450752

## 链接

[百度golang代码库组织和引用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=515622823)
[百度内Go Module使用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=917601678)

