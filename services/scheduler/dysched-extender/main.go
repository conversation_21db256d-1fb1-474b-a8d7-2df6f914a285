package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/sync/errgroup"
	"k8s.io/klog"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/algorithm"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/apis"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/metrics"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/scheduler/dysched-extender/pkg/version"
)

var cfg config.Config

func init() {
	flagSet := flag.CommandLine
	klog.InitFlags(flagSet)

	flagSet.Int64Var(&cfg.ToleranceRate, "tolerance-rate", 80, "- Tolerance real resource use reate")
	flagSet.StringVar(&cfg.SrvAddr, "server-address", ":8080", "- Server address")
	flagSet.StringVar(&cfg.Mode, "server-mode", "debug", "- Server run mode")
	flagSet.StringVar(&cfg.KubeConfig, "kubeconfig", "", "- Server address")

	flagSet.Parse(os.Args[1:])
}

func serve(ctx context.Context, server *http.Server) func() error {
	return func() (err error) {
		go func() {
			klog.Infof("start insecure server, listen on %s", server.Addr)
			if err = server.ListenAndServe(); err != http.ErrServerClosed {
				err = fmt.Errorf("start insecure server err: %w", err)
			}
		}()

		<-ctx.Done()

		if err = server.Shutdown(ctx); err != nil {
			err = fmt.Errorf("server shutdown err: %w", err)
		}

		time.Sleep(3)
		return
	}
}

func start() int {
	gin.SetMode(cfg.Mode)
	klog.Infof("start cce-dysched-extender, version '%v'", version.Version)

	var (
		route = gin.New()
		term  = make(chan os.Signal)
		srv   = &http.Server{Addr: cfg.SrvAddr, Handler: route}
	)

	route.Use(gin.Recovery())
	route.Use(metrics.RequestCount())

	nodeCache, err := algorithm.NewNodeCache(cfg.KubeConfig)
	if err != nil {
		klog.Fatalf("init node cache err: %v", err)
		return 1
	}

	register := []apis.API{
		&apis.DebugAPI{},
		&apis.Extender{NodeCache: nodeCache, ToleranceRate: cfg.ToleranceRate},
	}

	for _, r := range register {
		r.RegisterAPI(route)
	}

	ctx, cancel := context.WithCancel(context.Background())
	wg, ctx := errgroup.WithContext(ctx)

	wg.Go(serve(ctx, srv))

	signal.Notify(term, os.Interrupt, syscall.SIGTERM)

	select {
	case <-term:
		klog.Info("received SIGTERM, exiting gracefully...")
	case <-ctx.Done():
	}

	cancel()

	nodeCache.Close()

	if err := wg.Wait(); err != nil {
		klog.Error("unhandled error received. Exiting...", err)
		return 1
	}

	return 0
}

func main() {
	os.Exit(start())
}
