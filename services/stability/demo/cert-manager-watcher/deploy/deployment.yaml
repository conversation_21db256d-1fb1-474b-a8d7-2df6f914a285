apiVersion: apps/v1
kind: Deployment
metadata:
  name: cert-manager-watcher
  labels:
    app: cce
    control-plane: cert-manager-watcher
spec:
  replicas: 1
  strategy:  
    type: Recreate
  selector:
    matchLabels:
      app: cce
      control-plane: cert-manager-watcher
  template:
    metadata:
      labels:
        app: cce
        control-plane: cert-manager-watcher
    spec:
      imagePullSecrets:	
      - name: ccr-registry-secret
      containers:
        - name: cert-manager-watcher
          image: registry.baidubce.com/cce-plugin-dev/cert-manager-watcher:chenhuan
          imagePullPolicy: Always
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
      hostNetwork: true
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: cert-manager-watcher-serviceaccount
      terminationGracePeriodSeconds: 10
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cert-manager-watcher
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  - clusterissuers
  - issuers
  verbs:
  - get
  - list
  - update
  - watch
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: default
  name: cert-manager-watcher-serviceaccount
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cert-manager-watcher-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cert-manager-watcher
subjects:
- kind: ServiceAccount
  namespace: default
  name: cert-manager-watcher-serviceaccount