apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cert-manager-watcher
rules:
- apiGroups:
  - cert-manager.io
  resources:
  - certificates
  - certificaterequests
  - clusterissuers
  - issuers
  verbs:
  - get
  - list
  - update
  - watch
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: default
  name: cert-manager-watcher-serviceaccount
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cert-manager-watcher-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cert-manager-watcher
subjects:
- kind: ServiceAccount
  namespace: default
  name: cert-manager-watcher-serviceaccount

