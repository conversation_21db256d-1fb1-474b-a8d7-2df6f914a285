# 打镜像
FROM registry.baidubce.com/cce-service-pro/cce-base:v1.0.0

WORKDIR /home/<USER>/cce/cert-manager-watcher/

RUN mkdir -p /home/<USER>/cce/cert-manager-watcher/ && \
mkdir -p /home/<USER>/cce/cert-manager-watcher/conf && \
mkdir -p /home/<USER>/cce/cert-manager-watcher/logs && \
mkdir -p /home/<USER>/cce/plugins/temp_kube_config && \
mkdir -p /home/<USER>/cce/plugins/temp_helm_values

# 设置时区
ENV TZ=Asia/Shanghai

# 依赖二进制在 /cce/bin/ 目录
ENV PATH=${PATH}:/cce/bin/

COPY  cert-manager-watcher  /home/<USER>/cce/cert-manager-watcher/

ENTRYPOINT ["/home/<USER>/cce/cert-manager-watcher/cert-manager-watcher"]
