// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/04/24 17:26:00, by <EMAIL>, create
*/
/*
复现 cert-manager Version Convert 导致 APIServer OOM
*/

package main

import (
	"context"
	"os"
	"sync"
	"time"

	v1beta1 "github.com/jetstack/cert-manager/pkg/apis/certmanager/v1beta1"
	versioned "github.com/jetstack/cert-manager/pkg/client/clientset/versioned"
	certmanagerv1beta1 "github.com/jetstack/cert-manager/pkg/client/informers/externalversions/certmanager/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

func main() {
	ctx := context.TODO()

	if err := run(ctx); err != nil {
		logger.Errorf(ctx, "run failed: %s", err)
		os.Exit(1)
	}
}

func run(ctx context.Context) error {
	// Init informer
	cfg, err := rest.InClusterConfig()
	if err != nil {
		logger.Errorf(ctx, "InClusterConfig failed: %s", err)
		return err
	}
	cfg.Burst = 400
	cfg.QPS = 400
	client := versioned.NewForConfigOrDie(cfg)

	wg := sync.WaitGroup{}

	// certificateRequest
	wg.Add(1)
	go func() {
		defer wg.Done()
		certificateRequest(ctx, client)
	}()

	// certificates
	wg.Add(1)
	go func() {
		defer wg.Done()
		certificate(ctx, client)
	}()

	// issuers
	wg.Add(1)
	go func() {
		defer wg.Done()
		issuers(ctx, client)
	}()

	// clusterissuers
	wg.Add(1)
	go func() {
		defer wg.Done()
		clusterissuers(ctx, client)
	}()

	wg.Wait()

	return nil
}

func certificateRequest(ctx context.Context, client *versioned.Clientset) {
	informer := certmanagerv1beta1.NewCertificateRequestInformer(client, metav1.NamespaceAll, 0, nil)

	// Run informer
	stopCh := make(chan struct{})
	go informer.Run(stopCh)

	// AddFunciont
	update := func(oldObj, newObj interface{}) {
		key, ok := keyFunc(newObj)

		if !ok {
			return
		}

		logger.Infof(ctx, "%s", key)
	}

	added := func(newObj interface{}) {
		key, ok := keyFunc(newObj)
		if !ok {
			return
		}
		logger.Infof(ctx, "%s", key)
	}

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{UpdateFunc: update, AddFunc: added})

	// List informer
	wait.Until(func() {
		requests := informer.GetIndexer().List()

		for _, r := range requests {
			request, ok := r.(*v1beta1.CertificateRequest)
			if !ok {
				logger.Errorf(ctx, "expect *v1beta1.CertificateRequest")
				continue
			}

			namespae := request.GetNamespace()

			if request.Annotations == nil {
				request.Annotations = map[string]string{}
			}

			request.Annotations["time-now"] = time.Now().Format("2006-01-02 15:04:05")

			if _, err := client.CertmanagerV1beta1().CertificateRequests(namespae).Update(ctx, request, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "Update failed: %s", err)
			}

			logger.Infof(ctx, "CertificateRequest: %s", utils.ToJSON(request))
		}
	}, 0, ctx.Done())
}

func certificate(ctx context.Context, client *versioned.Clientset) {
	informer := certmanagerv1beta1.NewCertificateInformer(client, metav1.NamespaceAll, 0, nil)

	// Run informer
	stopCh := make(chan struct{})
	go informer.Run(stopCh)

	// AddFunciont
	update := func(oldObj, newObj interface{}) {
		key, ok := keyFunc(newObj)

		if !ok {
			return
		}

		logger.Infof(ctx, "%s", key)
	}

	added := func(newObj interface{}) {
		key, ok := keyFunc(newObj)
		if !ok {
			return
		}
		logger.Infof(ctx, "%s", key)
	}

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{UpdateFunc: update, AddFunc: added})

	// List informer
	wait.Until(func() {
		requests := informer.GetIndexer().List()

		for _, r := range requests {
			request, ok := r.(*v1beta1.Certificate)
			if !ok {
				logger.Errorf(ctx, "expect *v1beta1.Certificate")
				continue
			}

			namespae := request.GetNamespace()

			if request.Annotations == nil {
				request.Annotations = map[string]string{}
			}

			request.Annotations["time-now"] = time.Now().Format("2006-01-02 15:04:05")

			if _, err := client.CertmanagerV1beta1().Certificates(namespae).Update(ctx, request, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "Update failed: %s", err)
			}

			logger.Infof(ctx, "Certificate: %s", utils.ToJSON(request))
		}
	}, time.Second*30, ctx.Done())
}

func issuers(ctx context.Context, client *versioned.Clientset) {
	informer := certmanagerv1beta1.NewIssuerInformer(client, metav1.NamespaceAll, 0, nil)

	// Run informer
	stopCh := make(chan struct{})
	go informer.Run(stopCh)

	// AddFunciont
	update := func(oldObj, newObj interface{}) {
		key, ok := keyFunc(newObj)

		if !ok {
			return
		}

		logger.Infof(ctx, "%s", key)
	}

	added := func(newObj interface{}) {
		key, ok := keyFunc(newObj)
		if !ok {
			return
		}
		logger.Infof(ctx, "%s", key)
	}

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{UpdateFunc: update, AddFunc: added})

	// List informer
	wait.Until(func() {
		requests := informer.GetIndexer().List()

		for _, r := range requests {
			request, ok := r.(*v1beta1.Issuer)
			if !ok {
				logger.Errorf(ctx, "expect *v1beta1.Issuer")
				continue
			}

			namespae := request.GetNamespace()

			if request.Annotations == nil {
				request.Annotations = map[string]string{}
			}

			request.Annotations["time-now"] = time.Now().Format("2006-01-02 15:04:05")

			if _, err := client.CertmanagerV1beta1().Issuers(namespae).Update(ctx, request, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "Update failed: %s", err)
			}

			logger.Infof(ctx, "Issuer: %s", utils.ToJSON(request))
		}
	}, time.Second*30, ctx.Done())
}

func clusterissuers(ctx context.Context, client *versioned.Clientset) {
	informer := certmanagerv1beta1.NewClusterIssuerInformer(client, 0, nil)

	// Run informer
	stopCh := make(chan struct{})
	go informer.Run(stopCh)

	// AddFunciont
	update := func(oldObj, newObj interface{}) {
		key, ok := keyFunc(newObj)

		if !ok {
			return
		}

		logger.Infof(ctx, "%s", key)
	}

	added := func(newObj interface{}) {
		key, ok := keyFunc(newObj)
		if !ok {
			return
		}
		logger.Infof(ctx, "%s", key)
	}

	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{UpdateFunc: update, AddFunc: added})

	// List informer
	wait.Until(func() {
		requests := informer.GetIndexer().List()

		for _, r := range requests {
			request, ok := r.(*v1beta1.ClusterIssuer)
			if !ok {
				logger.Errorf(ctx, "expect *v1beta1.ClusterIssuer")
				continue
			}

			if request.Annotations == nil {
				request.Annotations = map[string]string{}
			}

			request.Annotations["time-now"] = time.Now().Format("2006-01-02 15:04:05")

			if _, err := client.CertmanagerV1beta1().ClusterIssuers().Update(ctx, request, metav1.UpdateOptions{}); err != nil {
				logger.Errorf(ctx, "Update failed: %s", err)
			}

			logger.Infof(ctx, "Issuer: %s", utils.ToJSON(request))
		}
	}, time.Second*30, ctx.Done())
}

func keyFunc(obj interface{}) (string, bool) {
	k, err := cache.DeletionHandlingMetaNamespaceKeyFunc(obj)
	if err != nil {
		return k, false
	}
	return k, true
}
