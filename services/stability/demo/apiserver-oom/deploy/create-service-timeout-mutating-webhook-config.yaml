apiVersion: admissionregistration.k8s.io/v1beta1
kind: MutatingWebhookConfiguration
metadata:
  name: cce-create-service-timeout-webhook
webhooks:
  - name: validate.cce.com
    failurePolicy: Ignore
    timeoutSeconds: 30
    clientConfig:
      service:
        name: cce-validate-webhook
        namespace: cce-system
        path: "/validate"
      caBundle: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSUN2RENDQWFRQ0NRQy9jMk4zR0lZWS96QU5CZ2txaGtpRzl3MEJBUXNGQURBZk1SMHdHd1lEVlFRRERCUmoKWTJVdGRtRnNhV1JoZEdVdGQyVmlhRzl2YXpBZ0Z3MHlNakF4TURZd09URXdOVFphR0E4eU1USXhNVEl4TXpBNQpNVEExTmxvd0h6RWRNQnNHQTFVRUF3d1VZMk5sTFhaaGJHbGtZWFJsTFhkbFltaHZiMnN3Z2dFaU1BMEdDU3FHClNJYjNEUUVCQVFVQUE0SUJEd0F3Z2dFS0FvSUJBUURrWXM4aFJ0eEFLeWEwVkhvUVBJUlF0QlBjVXVUUVBFZEQKMVdkUExPQUtqMXZsR1RmWnFwMnNHdjRvb0RqejNmVGJ1SVFTVWtWZm4wWWt2d0ZDVXJkeGc3NlJwdHlweGZ3OApSUW1UajM4N0M3UXFCT2ZkWEcyUU53bmlsN240M1kwQUVoQkNXQkdDZlhXRVdyTTNkWW1hRVM2cGJJQ0lGbHEyClg5QXlTV0w3bjNkNWZQQmxWZUlvdjl5cjc1M3A3S3J6Q1RoMlBydVdBYmFRZDluNWFEMmlPTFdzTS9Ea1dTdzcKVDJWbW5xclY2U2poSm8xVytIU2VwUEtFZHNBZWRpZDJtS1Z3SWNzQ1NBT2pWZ3ozaHpObE1sMGVJaWVWM0ZURQpDL0Myc2JQTmxMdGVxclBqK0EzSW5mRWlRWkx1WHVnSkxpWXBFNnpsbm56NlNORzJHeXBKQWdNQkFBRXdEUVlKCktvWklodmNOQVFFTEJRQURnZ0VCQUlPSlZjdG5vUkkvcVRaaHNHdlZWMzdORXBXM2pERVY2cXN1NXZtMjVzU00KQzd0cHVJcXlDYmlPSzJIQVkxSWdRSnJPenhzOGFEc3J0YjM2WFpiS1VrenVpN2ptcGNZSERtT0UvYThRRkI1dAo4ZXFKMVhvSXFNMG1LRnlZd29lNDkyWEg1SnVHaFRXTXg3cllnUHQ5VTJzdEt1dk9lbjc3MnMvaERSZXlJZ0dOCldOS1A1VHdqbUJNSXprV1JrRnFyUlc5Ny9DeFd0dzFqWVlqK3pzbFhtNmw1VGFOOWhqaFV1YWtUK2FMYkYvbHMKM3VuZ0lOSGcwVFV5MjZyNC9iSU1ZNFdCczFGVEV4QXhDWU5qYXhPa0pWNFk4R0k4bWlmSHluUU9wcGtNN0ZGbQo3N2lkN2V3dWkrclZIaEppbXdXeXdFQ2ZYMU44R09tc3VueUREbXZzdGlVPQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0t
    rules:
      - operations: ["CREATE"]
        apiGroups: ["*"]
        apiVersions: ["*"]
        resources: ["services"]
    sideEffects: None  