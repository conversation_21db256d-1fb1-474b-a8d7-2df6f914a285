apiVersion: v1
kind: ServiceAccount
metadata:
  name: create-service-sa
  namespace: cce-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: create-service-cr
rules:
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - "*"
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: create-service-crb
subjects:
  - kind: ServiceAccount
    name: create-service-sa
    namespace: cce-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: create-service-cr
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: create-service
  namespace: cce-system
  labels:
    app: create-service
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      app: create-service
  template:
    metadata:
      labels:
        app: create-service
    spec:
      serviceAccount: create-service-sa
      restartPolicy: Always
      containers:
        - name: create-service
          image: registry.baidubce.com/cce-plugin-dev/apiserver-oom:chenhuan
          imagePullPolicy: Always