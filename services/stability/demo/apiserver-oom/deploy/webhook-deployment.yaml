apiVersion: v1
kind: Service
metadata:
  name: cce-validate-webhook
  namespace: cce-system
  labels:
    app: cce-validate-webhook
spec:
  ports:
    - port: 443
      targetPort: 443
  selector:
    app: cce-validate-webhook
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-validate-webhook-sa
  namespace: cce-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cce-validate-webhook-cr
rules:
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - "*"
---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cce-validate-webhook-crb
subjects:
  - kind: ServiceAccount
    name: cce-validate-webhook-sa
    namespace: cce-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-validate-webhook-cr
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-validate-webhook
  namespace: cce-system
  labels:
    app: cce-validate-webhook
spec:
  replicas: 1
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce-validate-webhook
  template:
    metadata:
      labels:
        app: cce-validate-webhook
    spec:
      serviceAccount: cce-validate-webhook-sa
      imagePullSecrets:
        - name: ccr-registry-secret
      restartPolicy: Always
      containers:
        - name: cce-validate-webhook
          image: registry.baidubce.com/cce-plugin-dev/cce-validate-webhook:time-out
          imagePullPolicy: Always
          args:
            - -beego-config=/home/<USER>/cce/cce-validate-webhook/conf/app.conf
            - -log-file=/home/<USER>/cce/cce-validate-webhook/logs/cce-validate-webhook.log
          volumeMounts:
            - name: cce-validate-webhook-config
              mountPath: /home/<USER>/cce/cce-validate-webhook/conf/
      volumes:
        - name: cce-validate-webhook-config
          configMap:
            defaultMode: 420
            name: cce-validate-webhook-config
---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-system
  name: cce-validate-webhook-config
data:
  app.conf: |
    appname = cce-validate-webhook
    runmode = dev
    autorender = false
    copyrequestbody = true
    EnableDocs = true
    EnableHTTPS = true
    EnableHttpTLS = true
    HttpsPort = 443
    HTTPSCertFile = "/home/<USER>/cce/cce-validate-webhook/conf/server-cert.pem"
    HTTPSKeyFile = "/home/<USER>/cce/cce-validate-webhook/conf/server-key.pem"
  server-cert.pem: |
    -----BEGIN CERTIFICATE-----
    MIIDUjCCAjqgAwIBAgIJALsdyOtHxSMBMA0GCSqGSIb3DQEBBQUAMB8xHTAbBgNV
    BAMMFGNjZS12YWxpZGF0ZS13ZWJob29rMB4XDTIyMDEwNjA5MTA1NloXDTMyMDEw
    NDA5MTA1NlowLjEsMCoGA1UEAwwjY2NlLXZhbGlkYXRlLXdlYmhvb2suY2NlLXN5
    c3RlbS5zdmMwggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQC7AjUUwUVY
    nmEM75AgJBVfeVGKoF4thaDQZTKzidM1IBfPkI1SEckkNlz2E1RDKorgaq+8VxV5
    PgRruxF9ee7BbVEfudEiUu2Po9UmLEVEuJfZDoLZqIn04h/Ox4qz6uI4gZvYv00H
    qL+vBbQFetNQqi6tkvURhBnXR6LZi/DCdwQe+1unRw46peGa+8P97FY3RnIPS0gH
    5tqpE04puJ6H44kXqzDl6/RQVavMeQqofaUrLazfnEF+fv9q+68GIlsjZqg2Lq9Q
    74W3W1nTHvGPmL2oQwxp28M95EXYRkCYOmG159p0QGXB2OhZO4joorjWxXjnFUFD
    N7kV7Ur1VUJ/AgMBAAGjgYEwfzAJBgNVHRMEAjAAMAsGA1UdDwQEAwIF4DBlBgNV
    HREEXjBcghRjY2UtdmFsaWRhdGUtd2ViaG9va4IfY2NlLXZhbGlkYXRlLXdlYmhv
    b2suY2NlLXN5c3RlbYIjY2NlLXZhbGlkYXRlLXdlYmhvb2suY2NlLXN5c3RlbS5z
    dmMwDQYJKoZIhvcNAQEFBQADggEBAILjWbXeKkMxOzDywVZ4sFhB9cap67X9PhY9
    zNmgyjUJloYe3lzwm9gIRXS+4EIVh5a5oVnN7m5DdQLRh0ognQfzx6QHfc8lazsI
    q7ZdTlPi/ZdN9GKjFSfoZQOQVejAFxUQISwnS6LW/R3tG4hW6lHcwI/qwca/WzaG
    dg4VnRqp+INr2juMEI/34F9YGL8uW0T0fcRhQNqWUH5/sdsrHaP7ajVXu8Ubs3Ee
    ASZv30SonNgLE3S1dNhs56oRfvsCokEA88Izs33vjp3DNwU72yXAjDuBFWsAgXGc
    HU2HcRXpY+8o9VjGXZtfLhwvLjh4RUkNptcpNndEo+LGhv4Gcyo=
    -----END CERTIFICATE-----
  server-key.pem: |
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************