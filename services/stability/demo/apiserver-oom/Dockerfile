# 打镜像
FROM registry.baidubce.com/cce-service-pro/cce-base:v1.0.0

WORKDIR /home/<USER>/cce/apiserver-oom/

RUN mkdir -p /home/<USER>/cce/apiserver-oom/ && \
mkdir -p /home/<USER>/cce/apiserver-oom/conf && \
mkdir -p /home/<USER>/cce/apiserver-oom/logs && \
mkdir -p /home/<USER>/cce/plugins/temp_kube_config && \
mkdir -p /home/<USER>/cce/plugins/temp_helm_values

# 设置时区
ENV TZ=Asia/Shanghai

# 依赖二进制在 /cce/bin/ 目录
ENV PATH=${PATH}:/cce/bin/

COPY  apiserver-oom  /home/<USER>/cce/apiserver-oom/

ENTRYPOINT ["/home/<USER>/cce/apiserver-oom/apiserver-oom"]
