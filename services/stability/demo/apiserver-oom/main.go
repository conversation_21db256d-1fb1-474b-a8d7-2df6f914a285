// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/04/24 17:26:00, by <EMAIL>, create
*/
/*
复现 cert-manager Version Convert 导致 APIServer OOM
*/

package main

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/cache"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

func main() {
	ctx := context.TODO()

	if err := run(ctx); err != nil {
		logger.Errorf(ctx, "run failed: %s", err)
		os.Exit(1)
	}
}

func run(ctx context.Context) error {
	// Init informer
	cfg, err := rest.InClusterConfig()
	if err != nil {
		logger.Errorf(ctx, "InClusterConfig failed: %s", err)
		return err
	}
	cfg.Burst = 400
	cfg.QPS = 400

	client, err := kubernetes.NewForConfig(cfg)
	if err != nil {
		logger.Errorf(ctx, "kubernetes.NewForConfig failed: %s", err)
		return err
	}

	wg := sync.WaitGroup{}

	// certificateRequest
	wg.Add(1)
	go func() {
		defer wg.Done()
		createServiceRequest(ctx, client)
	}()

	wg.Wait()

	return nil
}

func createServiceRequest(ctx context.Context, client *kubernetes.Clientset) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	timer := time.NewTimer(1 * time.Minute)
	defer timer.Stop()

	count := 0
	for {
		select {
		case <-ticker.C:
			for i := 0; i < 100; i++ {
				go func(cnt int) {
					serviceName := fmt.Sprintf("service-%d", cnt)
					service := service(ctx, serviceName)
					client.CoreV1().Services("default").Create(ctx, service, metav1.CreateOptions{})

					logger.Infof(ctx, "create service %s succeed", serviceName)
				}(count)

				count++

				if count == 10000 {
					os.Exit(1)
				}
			}
		case <-timer.C:
			logger.Errorf(ctx, "timeout waiting for service created")
		}
	}
}

func service(ctx context.Context, name string) *corev1.Service {
	return &corev1.Service{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "v1",
			Kind:       "Service",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: "default",
			Labels: map[string]string{
				"job-name": "jobbb",
			},
		},
		Spec: corev1.ServiceSpec{
			Selector: map[string]string{
				"job-name": "jobbb",
			},
			Ports: []corev1.ServicePort{
				{
					Name:       "tensorboard",
					Port:       6006,
					Protocol:   corev1.ProtocolTCP,
					TargetPort: intstr.FromInt(6006),
				},
			},
			Type: corev1.ServiceTypeClusterIP,
		},
	}
}

func keyFunc(obj interface{}) (string, bool) {
	k, err := cache.DeletionHandlingMetaNamespaceKeyFunc(obj)
	if err != nil {
		return k, false
	}
	return k, true
}
