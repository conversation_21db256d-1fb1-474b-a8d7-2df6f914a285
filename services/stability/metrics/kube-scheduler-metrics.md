# kube-scheduler metrics

## Metrics

```md
# TYPE scheduler_pending_pods gauge

# TYPE scheduler_binding_duration_seconds histogram
# TYPE scheduler_e2e_scheduling_duration_seconds histogram
# TYPE scheduler_pod_scheduling_duration_seconds histogram
# TYPE scheduler_scheduling_algorithm_duration_seconds histogram

# TYPE scheduler_plugin_execution_duration_seconds histogram
# TYPE scheduler_framework_extension_point_duration_seconds histogram

# TYPE scheduler_pod_scheduling_attempts histogram
# TYPE scheduler_preemption_attempts_total counter
# TYPE scheduler_preemption_victims histogram
# TYPE scheduler_queue_incoming_pods_total counter
# TYPE scheduler_schedule_attempts_total counter
# TYPE scheduler_scheduler_cache_size gauge
# TYPE scheduler_scheduler_goroutines gauge
# TYPE scheduler_scheduling_algorithm_preemption_evaluation_seconds histogram
```

## TYPE

```md
# TYPE apiserver_audit_event_total counter
# TYPE apiserver_audit_requests_rejected_total counter
# TYPE apiserver_client_certificate_expiration_seconds histogram
# TYPE apiserver_envelope_encryption_dek_cache_fill_percent gauge
# TYPE apiserver_storage_data_key_generation_duration_seconds histogram
# TYPE apiserver_storage_data_key_generation_failures_total counter
# TYPE apiserver_storage_envelope_transformation_cache_misses_total counter
# TYPE go_gc_duration_seconds summary
# TYPE go_goroutines gauge
# TYPE go_info gauge
# TYPE go_memstats_alloc_bytes gauge
# TYPE go_memstats_alloc_bytes_total counter
# TYPE go_memstats_buck_hash_sys_bytes gauge
# TYPE go_memstats_frees_total counter
# TYPE go_memstats_gc_cpu_fraction gauge
# TYPE go_memstats_gc_sys_bytes gauge
# TYPE go_memstats_heap_alloc_bytes gauge
# TYPE go_memstats_heap_idle_bytes gauge
# TYPE go_memstats_heap_inuse_bytes gauge
# TYPE go_memstats_heap_objects gauge
# TYPE go_memstats_heap_released_bytes gauge
# TYPE go_memstats_heap_sys_bytes gauge
# TYPE go_memstats_last_gc_time_seconds gauge
# TYPE go_memstats_lookups_total counter
# TYPE go_memstats_mallocs_total counter
# TYPE go_memstats_mcache_inuse_bytes gauge
# TYPE go_memstats_mcache_sys_bytes gauge
# TYPE go_memstats_mspan_inuse_bytes gauge
# TYPE go_memstats_mspan_sys_bytes gauge
# TYPE go_memstats_next_gc_bytes gauge
# TYPE go_memstats_other_sys_bytes gauge
# TYPE go_memstats_stack_inuse_bytes gauge
# TYPE go_memstats_stack_sys_bytes gauge
# TYPE go_memstats_sys_bytes gauge
# TYPE go_threads gauge
# TYPE kubernetes_build_info gauge
# TYPE leader_election_master_status gauge
# TYPE process_cpu_seconds_total counter
# TYPE process_max_fds gauge
# TYPE process_open_fds gauge
# TYPE process_resident_memory_bytes gauge
# TYPE process_start_time_seconds gauge
# TYPE process_virtual_memory_bytes gauge
# TYPE process_virtual_memory_max_bytes gauge
# TYPE rest_client_exec_plugin_certificate_rotation_age histogram
# TYPE rest_client_exec_plugin_ttl_seconds gauge
# TYPE rest_client_request_duration_seconds histogram
# TYPE rest_client_requests_total counter

# TYPE scheduler_binding_duration_seconds histogram
# TYPE scheduler_e2e_scheduling_duration_seconds histogram
# TYPE scheduler_framework_extension_point_duration_seconds histogram
# TYPE scheduler_pending_pods gauge
# TYPE scheduler_plugin_execution_duration_seconds histogram
# TYPE scheduler_pod_scheduling_attempts histogram
# TYPE scheduler_pod_scheduling_duration_seconds histogram
# TYPE scheduler_preemption_attempts_total counter
# TYPE scheduler_preemption_victims histogram
# TYPE scheduler_queue_incoming_pods_total counter
# TYPE scheduler_schedule_attempts_total counter
# TYPE scheduler_scheduler_cache_size gauge
# TYPE scheduler_scheduler_goroutines gauge
# TYPE scheduler_scheduling_algorithm_duration_seconds histogram
# TYPE scheduler_scheduling_algorithm_preemption_evaluation_seconds histogram

# TYPE workqueue_adds_total counter
# TYPE workqueue_depth gauge
# TYPE workqueue_longest_running_processor_seconds gauge
# TYPE workqueue_queue_duration_seconds histogram
# TYPE workqueue_retries_total counter
# TYPE workqueue_unfinished_work_seconds gauge
# TYPE workqueue_work_duration_seconds histogram
```

## HELP

```md
# HELP apiserver_audit_event_total [ALPHA] Counter of audit events generated and sent to the audit backend.
# HELP apiserver_audit_requests_rejected_total [ALPHA] Counter of apiserver requests rejected due to an error in audit logging backend.
# HELP apiserver_client_certificate_expiration_seconds [ALPHA] Distribution of the remaining lifetime on the certificate used to authenticate a request.
# HELP apiserver_envelope_encryption_dek_cache_fill_percent [ALPHA] Percent of the cache slots currently occupied by cached DEKs.
# HELP apiserver_storage_data_key_generation_duration_seconds [ALPHA] Latencies in seconds of data encryption key(DEK) generation operations.
# HELP apiserver_storage_data_key_generation_failures_total [ALPHA] Total number of failed data encryption key(DEK) generation operations.
# HELP apiserver_storage_envelope_transformation_cache_misses_total [ALPHA] Total number of cache misses while accessing key decryption key(KEK).
# HELP go_gc_duration_seconds A summary of the pause duration of garbage collection cycles.
# HELP go_goroutines Number of goroutines that currently exist.
# HELP go_info Information about the Go environment.
# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
# HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even if freed.
# HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table.
# HELP go_memstats_frees_total Total number of frees.
# HELP go_memstats_gc_cpu_fraction The fraction of this program's available CPU time used by the GC since the program started.
# HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
# HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use.
# HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used.
# HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use.
# HELP go_memstats_heap_objects Number of allocated objects.
# HELP go_memstats_heap_released_bytes Number of heap bytes released to OS.
# HELP go_memstats_heap_sys_bytes Number of heap bytes obtained from system.
# HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection.
# HELP go_memstats_lookups_total Total number of pointer lookups.
# HELP go_memstats_mallocs_total Total number of mallocs.
# HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures.
# HELP go_memstats_mcache_sys_bytes Number of bytes used for mcache structures obtained from system.
# HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures.
# HELP go_memstats_mspan_sys_bytes Number of bytes used for mspan structures obtained from system.
# HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place.
# HELP go_memstats_other_sys_bytes Number of bytes used for other system allocations.
# HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator.
# HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
# HELP go_memstats_sys_bytes Number of bytes obtained from system.
# HELP go_threads Number of OS threads created.
# HELP kubernetes_build_info [ALPHA] A metric with a constant '1' value labeled by major, minor, git version, git commit, git tree state, build date, Go version, and compiler from which Kubernetes was built, and platform on which it is running.
# HELP leader_election_master_status [ALPHA] Gauge of if the reporting system is master of the relevant lease, 0 indicates backup, 1 indicates master. 'name' is the string used to identify the lease. Please make sure to group by name.
# HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.
# HELP process_max_fds Maximum number of open file descriptors.
# HELP process_open_fds Number of open file descriptors.
# HELP process_resident_memory_bytes Resident memory size in bytes.
# HELP process_start_time_seconds Start time of the process since unix epoch in seconds.
# HELP process_virtual_memory_bytes Virtual memory size in bytes.
# HELP process_virtual_memory_max_bytes Maximum amount of virtual memory available in bytes.
# HELP rest_client_exec_plugin_certificate_rotation_age [ALPHA] Histogram of the number of seconds the last auth exec plugin client certificate lived before being rotated. If auth exec plugin client certificates are unused, histogram will contain no data.
# HELP rest_client_exec_plugin_ttl_seconds [ALPHA] Gauge of the shortest TTL (time-to-live) of the client certificate(s) managed by the auth exec plugin. The value is in seconds until certificate expiry (negative if already expired). If auth exec plugins are unused or manage no TLS certificates, the value will be +INF.
# HELP rest_client_request_duration_seconds [ALPHA] Request latency in seconds. Broken down by verb and URL.
# HELP rest_client_requests_total [ALPHA] Number of HTTP requests, partitioned by status code, method, and host.
# HELP scheduler_binding_duration_seconds [ALPHA] (Deprecated since 1.20.0) Binding latency in seconds
# HELP scheduler_e2e_scheduling_duration_seconds [ALPHA] E2e scheduling latency in seconds (scheduling algorithm + binding)
# HELP scheduler_framework_extension_point_duration_seconds [ALPHA] Latency for running all plugins of a specific extension point.
# HELP scheduler_pending_pods [ALPHA] Number of pending pods, by the queue type. 'active' means number of pods in activeQ; 'backoff' means number of pods in backoffQ; 'unschedulable' means number of pods in unschedulableQ.
# HELP scheduler_plugin_execution_duration_seconds [ALPHA] Duration for running a plugin at a specific extension point.
# HELP scheduler_pod_scheduling_attempts [ALPHA] Number of attempts to successfully schedule a pod.
# HELP scheduler_pod_scheduling_duration_seconds [ALPHA] E2e latency for a pod being scheduled which may include multiple scheduling attempts.
# HELP scheduler_preemption_attempts_total [ALPHA] Total preemption attempts in the cluster till now
# HELP scheduler_preemption_victims [ALPHA] Number of selected preemption victims
# HELP scheduler_queue_incoming_pods_total [ALPHA] Number of pods added to scheduling queues by event and queue type.
# HELP scheduler_schedule_attempts_total [ALPHA] Number of attempts to schedule pods, by the result. 'unschedulable' means a pod could not be scheduled, while 'error' means an internal scheduler problem.
# HELP scheduler_scheduler_cache_size [ALPHA] Number of nodes, pods, and assumed (bound) pods in the scheduler cache.
# HELP scheduler_scheduler_goroutines [ALPHA] Number of running goroutines split by the work they do such as binding.
# HELP scheduler_scheduling_algorithm_duration_seconds [ALPHA] Scheduling algorithm latency in seconds
# HELP scheduler_scheduling_algorithm_preemption_evaluation_seconds [ALPHA] (Deprecated since 1.20.0) Scheduling algorithm preemption evaluation duration in seconds
# HELP workqueue_adds_total [ALPHA] Total number of adds handled by workqueue
# HELP workqueue_depth [ALPHA] Current depth of workqueue
# HELP workqueue_longest_running_processor_seconds [ALPHA] How many seconds has the longest running processor for workqueue been running.
# HELP workqueue_queue_duration_seconds [ALPHA] How long in seconds an item stays in workqueue before being requested.
# HELP workqueue_retries_total [ALPHA] Total number of retries handled by workqueue
# HELP workqueue_unfinished_work_seconds [ALPHA] How many seconds of work has done that is in progress and hasn't been observed by work_duration. Large values indicate stuck threads. One can deduce the number of stuck threads by observing the rate at which this increases.
# HELP workqueue_work_duration_seconds [ALPHA] How long in seconds processing an item from workqueue takes.
```