# Telegraf Proc

## /proc/1927/status

```bash
# cat  /proc/1927/status
Name:	systemd-journal
Umask:	0022
State:	S (sleeping)
Tgid:	1927
Ngid:	0
Pid:	1927
PPid:	1
TracerPid:	0
Uid:	0	0	0	0
Gid:	0	0	0	0
FDSize:	64
Groups:
VmPeak:	  316668 kB
VmSize:	   64820 kB
VmLck:	       0 kB
VmPin:	       0 kB
VmHWM:	  196112 kB
VmRSS:	   20228 kB
RssAnon:	     576 kB
RssFile:	   19648 kB
RssShmem:	       4 kB
VmData:	    1388 kB
VmStk:	     132 kB
VmExe:	     320 kB
VmLib:	    3472 kB
VmPTE:	     152 kB
VmSwap:	       0 kB
Threads:	1
SigQ:	1/64102
SigPnd:	0000000000000000
ShdPnd:	0000000000000000
SigBlk:	0000000000004a02
SigIgn:	0000000000001000
SigCgt:	0000000180000040
CapInh:	0000000000000000
CapPrm:	00000005402800cf
CapEff:	00000005402800cf
CapBnd:	00000005402800cf
CapAmb:	0000000000000000
NoNewPrivs:	0
Seccomp:	0
Speculation_Store_Bypass:	vulnerable
Cpus_allowed:	f
Cpus_allowed_list:	0-3
Mems_allowed:	00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000000,00000001
Mems_allowed_list:	0
voluntary_ctxt_switches:	518653
nonvoluntary_ctxt_switches:	396343
```

## HELP

```bash
# HELP procstat_created_at Telegraf collected metric


# HELP procstat_voluntary_context_switches Telegraf collected metric
# HELP procstat_involuntary_context_switches Telegraf collected metric


# HELP procstat_major_faults Telegraf collected metric
# HELP procstat_minor_faults Telegraf collected metric
# HELP procstat_child_major_faults Telegraf collected metric
# HELP procstat_child_minor_faults Telegraf collected metric



# HELP procstat_cpu_time_guest Telegraf collected metric
# HELP procstat_cpu_time_guest_nice Telegraf collected metric
# HELP procstat_cpu_time_idle Telegraf collected metric

# HELP procstat_cpu_time_iowait Telegraf collected metric

# HELP procstat_cpu_time_irq Telegraf collected metric
# HELP procstat_cpu_time_soft_irq Telegraf collected metric
# HELP procstat_cpu_time_steal Telegraf collected metric

# HELP procstat_cpu_time_nice Telegraf collected metric
# HELP procstat_cpu_time_system Telegraf collected metric
# HELP procstat_cpu_time Telegraf collected metric
# HELP procstat_cpu_time_user Telegraf collected metric
# HELP procstat_cpu_usage Telegraf collected metric





# HELP procstat_lookup_pid_count Telegraf collected metric
# HELP procstat_lookup_result_code Telegraf collected metric
# HELP procstat_lookup_running Telegraf collected metric


# HELP procstat_memory_data Telegraf collected metric
# HELP procstat_memory_locked Telegraf collected metric
# HELP procstat_memory_rss Telegraf collected metric
# HELP procstat_memory_stack Telegraf collected metric
# HELP procstat_memory_swap Telegraf collected metric
# HELP procstat_memory_usage Telegraf collected metric
# HELP procstat_memory_vms Telegraf collected metric


# HELP procstat_nice_priority Telegraf collected metric

# HELP procstat_num_fds Telegraf collected metric
# HELP procstat_num_threads Telegraf collected metric


# HELP procstat_pid Telegraf collected metric
# HELP procstat_ppid Telegraf collected metric


# HELP procstat_realtime_priority Telegraf collected metric

# HELP procstat_rlimit_cpu_time_hard Telegraf collected metric
# HELP procstat_rlimit_cpu_time_soft Telegraf collected metric
# HELP procstat_rlimit_file_locks_hard Telegraf collected metric
# HELP procstat_rlimit_file_locks_soft Telegraf collected metric
# HELP procstat_rlimit_memory_data_hard Telegraf collected metric
# HELP procstat_rlimit_memory_data_soft Telegraf collected metric
# HELP procstat_rlimit_memory_locked_hard Telegraf collected metric
# HELP procstat_rlimit_memory_locked_soft Telegraf collected metric
# HELP procstat_rlimit_memory_rss_hard Telegraf collected metric
# HELP procstat_rlimit_memory_rss_soft Telegraf collected metric
# HELP procstat_rlimit_memory_stack_hard Telegraf collected metric
# HELP procstat_rlimit_memory_stack_soft Telegraf collected metric

# HELP procstat_rlimit_memory_vms_hard Telegraf collected metric
# HELP procstat_rlimit_memory_vms_soft Telegraf collected metric

# HELP procstat_rlimit_nice_priority_hard Telegraf collected metric
# HELP procstat_rlimit_nice_priority_soft Telegraf collected metric

# HELP procstat_rlimit_num_fds_hard Telegraf collected metric
# HELP procstat_rlimit_num_fds_soft Telegraf collected metric

# HELP procstat_rlimit_realtime_priority_hard Telegraf collected metric
# HELP procstat_rlimit_realtime_priority_soft Telegraf collected metric
# HELP procstat_rlimit_signals_pending_hard Telegraf collected metric
# HELP procstat_rlimit_signals_pending_soft Telegraf collected metric

# HELP procstat_signals_pending Telegraf collected metric


# HELP procstat_write_bytes Telegraf collected metric
# HELP procstat_write_count Telegraf collected metric

# HELP procstat_read_bytes Telegraf collected metric
# HELP procstat_read_count Telegraf collected metric
```

## TYPE

```bash
# TYPE procstat_child_major_faults untyped
# TYPE procstat_child_minor_faults untyped
# TYPE procstat_cpu_time_guest_nice untyped
# TYPE procstat_cpu_time_guest untyped
# TYPE procstat_cpu_time_idle untyped
# TYPE procstat_cpu_time_iowait untyped
# TYPE procstat_cpu_time_irq untyped
# TYPE procstat_cpu_time_nice untyped
# TYPE procstat_cpu_time_soft_irq untyped
# TYPE procstat_cpu_time_steal untyped
# TYPE procstat_cpu_time_system untyped
# TYPE procstat_cpu_time untyped
# TYPE procstat_cpu_time_user untyped
# TYPE procstat_cpu_usage untyped
# TYPE procstat_created_at untyped
# TYPE procstat_involuntary_context_switches untyped
# TYPE procstat_lookup_pid_count untyped
# TYPE procstat_lookup_result_code untyped
# TYPE procstat_lookup_running untyped
# TYPE procstat_major_faults untyped
# TYPE procstat_memory_data untyped
# TYPE procstat_memory_locked untyped
# TYPE procstat_memory_rss untyped
# TYPE procstat_memory_stack untyped
# TYPE procstat_memory_swap untyped
# TYPE procstat_memory_usage untyped
# TYPE procstat_memory_vms untyped
# TYPE procstat_minor_faults untyped
# TYPE procstat_nice_priority untyped
# TYPE procstat_num_fds untyped
# TYPE procstat_num_threads untyped
# TYPE procstat_pid untyped
# TYPE procstat_ppid untyped
# TYPE procstat_read_bytes untyped
# TYPE procstat_read_count untyped
# TYPE procstat_realtime_priority untyped
# TYPE procstat_rlimit_cpu_time_hard untyped
# TYPE procstat_rlimit_cpu_time_soft untyped
# TYPE procstat_rlimit_file_locks_hard untyped
# TYPE procstat_rlimit_file_locks_soft untyped
# TYPE procstat_rlimit_memory_data_hard untyped
# TYPE procstat_rlimit_memory_data_soft untyped
# TYPE procstat_rlimit_memory_locked_hard untyped
# TYPE procstat_rlimit_memory_locked_soft untyped
# TYPE procstat_rlimit_memory_rss_hard untyped
# TYPE procstat_rlimit_memory_rss_soft untyped
# TYPE procstat_rlimit_memory_stack_hard untyped
# TYPE procstat_rlimit_memory_stack_soft untyped
# TYPE procstat_rlimit_memory_vms_hard untyped
# TYPE procstat_rlimit_memory_vms_soft untyped
# TYPE procstat_rlimit_nice_priority_hard untyped
# TYPE procstat_rlimit_nice_priority_soft untyped
# TYPE procstat_rlimit_num_fds_hard untyped
# TYPE procstat_rlimit_num_fds_soft untyped
# TYPE procstat_rlimit_realtime_priority_hard untyped
# TYPE procstat_rlimit_realtime_priority_soft untyped
# TYPE procstat_rlimit_signals_pending_hard untyped
# TYPE procstat_rlimit_signals_pending_soft untyped
# TYPE procstat_signals_pending untyped
# TYPE procstat_voluntary_context_switches untyped
# TYPE procstat_write_bytes untyped
# TYPE procstat_write_count untyped
```

## 采集代码

```go
// Add metrics a single Process
func (p *Procstat) addMetric(proc Process, acc telegraf.Accumulator, t time.Time) {
	var prefix string
	if p.Prefix != "" {
		prefix = p.Prefix + "_"
	}

	fields := map[string]interface{}{}

	//If process_name tag is not already set, set to actual name
	if _, nameInTags := proc.Tags()["process_name"]; !nameInTags {
		name, err := proc.Name()
		if err == nil {
			proc.Tags()["process_name"] = name
		}
	}

	//If user tag is not already set, set to actual name
	if _, ok := proc.Tags()["user"]; !ok {
		user, err := proc.Username()
		if err == nil {
			proc.Tags()["user"] = user
		}
	}

	//If pid is not present as a tag, include it as a field.
	if _, pidInTags := proc.Tags()["pid"]; !pidInTags {
		fields["pid"] = int32(proc.PID())
	}

	//If cmd_line tag is true and it is not already set add cmdline as a tag
	if p.CmdLineTag {
		if _, ok := proc.Tags()["cmdline"]; !ok {
			Cmdline, err := proc.Cmdline()
			if err == nil {
				proc.Tags()["cmdline"] = Cmdline
			}
		}
	}

	numThreads, err := proc.NumThreads()
	if err == nil {
		fields[prefix+"num_threads"] = numThreads
	}

	fds, err := proc.NumFDs()
	if err == nil {
		fields[prefix+"num_fds"] = fds
	}

	ctx, err := proc.NumCtxSwitches()
	if err == nil {
		fields[prefix+"voluntary_context_switches"] = ctx.Voluntary
		fields[prefix+"involuntary_context_switches"] = ctx.Involuntary
	}

	faults, err := proc.PageFaults()
	if err == nil {
		fields[prefix+"minor_faults"] = faults.MinorFaults
		fields[prefix+"major_faults"] = faults.MajorFaults
		fields[prefix+"child_minor_faults"] = faults.ChildMinorFaults
		fields[prefix+"child_major_faults"] = faults.ChildMajorFaults
	}

	io, err := proc.IOCounters()
	if err == nil {
		fields[prefix+"read_count"] = io.ReadCount
		fields[prefix+"write_count"] = io.WriteCount
		fields[prefix+"read_bytes"] = io.ReadBytes
		fields[prefix+"write_bytes"] = io.WriteBytes
	}

	createdAt, err := proc.CreateTime() //Returns epoch in ms
	if err == nil {
		fields[prefix+"created_at"] = createdAt * 1000000 //Convert ms to ns
	}

	cpuTime, err := proc.Times()
	if err == nil {
		fields[prefix+"cpu_time_user"] = cpuTime.User
		fields[prefix+"cpu_time_system"] = cpuTime.System
		fields[prefix+"cpu_time_idle"] = cpuTime.Idle
		fields[prefix+"cpu_time_nice"] = cpuTime.Nice
		fields[prefix+"cpu_time_iowait"] = cpuTime.Iowait
		fields[prefix+"cpu_time_irq"] = cpuTime.Irq
		fields[prefix+"cpu_time_soft_irq"] = cpuTime.Softirq
		fields[prefix+"cpu_time_steal"] = cpuTime.Steal
		fields[prefix+"cpu_time_guest"] = cpuTime.Guest
		fields[prefix+"cpu_time_guest_nice"] = cpuTime.GuestNice
	}

	cpuPerc, err := proc.Percent(time.Duration(0))
	if err == nil {
		if p.solarisMode {
			fields[prefix+"cpu_usage"] = cpuPerc / float64(runtime.NumCPU())
		} else {
			fields[prefix+"cpu_usage"] = cpuPerc
		}
	}

	mem, err := proc.MemoryInfo()
	if err == nil {
		fields[prefix+"memory_rss"] = mem.RSS
		fields[prefix+"memory_vms"] = mem.VMS
		fields[prefix+"memory_swap"] = mem.Swap
		fields[prefix+"memory_data"] = mem.Data
		fields[prefix+"memory_stack"] = mem.Stack
		fields[prefix+"memory_locked"] = mem.Locked
	}

	memPerc, err := proc.MemoryPercent()
	if err == nil {
		fields[prefix+"memory_usage"] = memPerc
	}

	rlims, err := proc.RlimitUsage(true)
	if err == nil {
		for _, rlim := range rlims {
			var name string
			switch rlim.Resource {
			case process.RLIMIT_CPU:
				name = "cpu_time"
			case process.RLIMIT_DATA:
				name = "memory_data"
			case process.RLIMIT_STACK:
				name = "memory_stack"
			case process.RLIMIT_RSS:
				name = "memory_rss"
			case process.RLIMIT_NOFILE:
				name = "num_fds"
			case process.RLIMIT_MEMLOCK:
				name = "memory_locked"
			case process.RLIMIT_AS:
				name = "memory_vms"
			case process.RLIMIT_LOCKS:
				name = "file_locks"
			case process.RLIMIT_SIGPENDING:
				name = "signals_pending"
			case process.RLIMIT_NICE:
				name = "nice_priority"
			case process.RLIMIT_RTPRIO:
				name = "realtime_priority"
			default:
				continue
			}

			fields[prefix+"rlimit_"+name+"_soft"] = rlim.Soft
			fields[prefix+"rlimit_"+name+"_hard"] = rlim.Hard
			if name != "file_locks" { // gopsutil doesn't currently track the used file locks count
				fields[prefix+name] = rlim.Used
			}
		}
	}

	ppid, err := proc.Ppid()
	if err == nil {
		fields[prefix+"ppid"] = ppid
	}

	acc.AddFields("procstat", fields, proc.Tags(), t)
}
```

## Page faults

https://stackoverflow.com/questions/5684365/what-causes-page-faults