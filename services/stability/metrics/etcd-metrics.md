# etcd metrics

## Metrics

### Server Status

```md
# TYPE etcd_server_id gauge
# TYPE etcd_server_version gauge
# TYPE etcd_server_go_version gauge
# TYPE etcd_server_health_failures counter
# TYPE etcd_server_health_success counter
```

### Raft Leader Election

```md
# TYPE etcd_server_has_leader gauge
# TYPE etcd_server_is_leader gauge
# TYPE etcd_server_leader_changes_seen_total counter
# TYPE etcd_server_heartbeat_send_failures_total counter

# TYPE etcd_network_peer_round_trip_time_seconds histogram
```

### Grpc Server

```md
# TYPE grpc_server_handled_total counter
# TYPE grpc_server_started_total counter
# TYPE grpc_server_msg_sent_total counter
# TYPE grpc_server_msg_received_total counter
```

### Raft Log Replication

```md
# TYPE etcd_network_active_peers gauge

# TYPE etcd_network_peer_sent_bytes_total counter
# TYPE etcd_network_peer_received_bytes_total counter

# TYPE etcd_network_client_grpc_received_bytes_total counter
# TYPE etcd_network_client_grpc_sent_bytes_total counter

# TYPE etcd_server_proposals_applied_total gauge
# TYPE etcd_server_proposals_committed_total gauge
# TYPE etcd_server_proposals_pending gauge
# TYPE etcd_server_proposals_failed_total counter
```

### WAL

```md
# TYPE etcd_disk_wal_fsync_duration_seconds histogram
```

### MVCC Request

```md
# TYPE etcd_mvcc_put_total counter
# TYPE etcd_mvcc_txn_total counter
# TYPE etcd_mvcc_range_total counter
# TYPE etcd_mvcc_delete_total counter

# TYPE etcd_debugging_store_expires_total counter
# TYPE etcd_debugging_store_reads_total counter
# TYPE etcd_debugging_store_watchers gauge
# TYPE etcd_debugging_store_watch_requests_total counter
# TYPE etcd_debugging_store_writes_total counter


# TYPE etcd_debugging_mvcc_put_total counter
# TYPE etcd_debugging_mvcc_txn_total counter
# TYPE etcd_debugging_mvcc_range_total counter
# TYPE etcd_debugging_mvcc_delete_total counter
# TYPE etcd_debugging_mvcc_events_total counter
# TYPE etcd_debugging_mvcc_pending_events_total gauge
# TYPE etcd_debugging_mvcc_watcher_total gauge
# TYPE etcd_debugging_mvcc_watch_stream_total gauge
# TYPE etcd_debugging_mvcc_slow_watcher_total gauge

# TYPE etcd_mvcc_db_open_read_transactions gauge
```

### MVCC BoltDB

```md
# TYPE etcd_debugging_mvcc_current_revision gauge
# TYPE etcd_debugging_mvcc_keys_total gauge

# TYPE etcd_server_quota_backend_bytes gauge
# TYPE etcd_mvcc_db_total_size_in_bytes gauge
# TYPE etcd_mvcc_db_total_size_in_use_in_bytes gauge
# TYPE etcd_debugging_mvcc_db_total_size_in_bytes gauge

# TYPE etcd_disk_backend_commit_duration_seconds histogram
# TYPE etcd_disk_backend_defrag_duration_seconds histogram
# TYPE etcd_disk_backend_snapshot_duration_seconds histogram

# TYPE etcd_debugging_disk_backend_commit_rebalance_duration_seconds histogram
# TYPE etcd_debugging_disk_backend_commit_spill_duration_seconds histogram
# TYPE etcd_debugging_disk_backend_commit_write_duration_seconds histogram

# TYPE etcd_mvcc_hash_duration_seconds histogram
# TYPE etcd_mvcc_hash_rev_duration_seconds histogram
```

### Compaction

```md
# TYPE etcd_debugging_mvcc_compact_revision gauge
# TYPE etcd_debugging_mvcc_db_compaction_keys_total counter
# TYPE etcd_debugging_mvcc_db_compaction_pause_duration_milliseconds histogram
# TYPE etcd_debugging_mvcc_db_compaction_total_duration_milliseconds histogram

# TYPE etcd_debugging_mvcc_index_compaction_pause_duration_milliseconds histogram
```

### Snapshot

```md
# TYPE etcd_snap_fsync_duration_seconds histogram

# TYPE etcd_snap_db_fsync_duration_seconds histogram
# TYPE etcd_snap_db_save_total_duration_seconds histogram

# TYPE etcd_debugging_snap_save_marshalling_duration_seconds histogram
# TYPE etcd_debugging_snap_save_total_duration_seconds histogram

# TYPE etcd_server_snapshot_apply_in_progress_total gauge
```

### Lease

```md
# TYPE etcd_debugging_lease_granted_total counter
# TYPE etcd_debugging_lease_renewed_total counter
# TYPE etcd_debugging_lease_revoked_total counter
# TYPE etcd_debugging_lease_ttl_total histogram

# TYPE etcd_debugging_server_lease_expired_total counter
```

### Learner

```md
# TYPE etcd_server_is_learner gauge
# TYPE etcd_server_learner_promote_successes counter
```

### RPC Proxy

```md
# TYPE etcd_grpc_proxy_cache_hits_total gauge
# TYPE etcd_grpc_proxy_cache_keys_total gauge
# TYPE etcd_grpc_proxy_cache_misses_total gauge
# TYPE etcd_grpc_proxy_events_coalescing_total counter
# TYPE etcd_grpc_proxy_watchers_coalescing_total gauge
```

### Others

```md
# TYPE etcd_cluster_version gauge

# TYPE etcd_server_read_indexes_failed_total counter
# TYPE etcd_server_slow_apply_total counter
# TYPE etcd_server_slow_read_indexes_total counter

```

## TYPE

```md
# TYPE etcd_cluster_version gauge
# TYPE etcd_debugging_disk_backend_commit_rebalance_duration_seconds histogram
# TYPE etcd_debugging_disk_backend_commit_spill_duration_seconds histogram
# TYPE etcd_debugging_disk_backend_commit_write_duration_seconds histogram
# TYPE etcd_debugging_lease_granted_total counter
# TYPE etcd_debugging_lease_renewed_total counter
# TYPE etcd_debugging_lease_revoked_total counter
# TYPE etcd_debugging_lease_ttl_total histogram
# TYPE etcd_debugging_mvcc_compact_revision gauge
# TYPE etcd_debugging_mvcc_current_revision gauge
# TYPE etcd_debugging_mvcc_db_compaction_keys_total counter
# TYPE etcd_debugging_mvcc_db_compaction_pause_duration_milliseconds histogram
# TYPE etcd_debugging_mvcc_db_compaction_total_duration_milliseconds histogram
# TYPE etcd_debugging_mvcc_db_total_size_in_bytes gauge
# TYPE etcd_debugging_mvcc_delete_total counter
# TYPE etcd_debugging_mvcc_events_total counter
# TYPE etcd_debugging_mvcc_index_compaction_pause_duration_milliseconds histogram
# TYPE etcd_debugging_mvcc_keys_total gauge
# TYPE etcd_debugging_mvcc_pending_events_total gauge
# TYPE etcd_debugging_mvcc_put_total counter
# TYPE etcd_debugging_mvcc_range_total counter
# TYPE etcd_debugging_mvcc_slow_watcher_total gauge
# TYPE etcd_debugging_mvcc_txn_total counter
# TYPE etcd_debugging_mvcc_watcher_total gauge
# TYPE etcd_debugging_mvcc_watch_stream_total gauge
# TYPE etcd_debugging_server_lease_expired_total counter
# TYPE etcd_debugging_snap_save_marshalling_duration_seconds histogram
# TYPE etcd_debugging_snap_save_total_duration_seconds histogram
# TYPE etcd_debugging_store_expires_total counter
# TYPE etcd_debugging_store_reads_total counter
# TYPE etcd_debugging_store_watchers gauge
# TYPE etcd_debugging_store_watch_requests_total counter
# TYPE etcd_debugging_store_writes_total counter
# TYPE etcd_disk_backend_commit_duration_seconds histogram
# TYPE etcd_disk_backend_defrag_duration_seconds histogram
# TYPE etcd_disk_backend_snapshot_duration_seconds histogram
# TYPE etcd_disk_wal_fsync_duration_seconds histogram
# TYPE etcd_grpc_proxy_cache_hits_total gauge
# TYPE etcd_grpc_proxy_cache_keys_total gauge
# TYPE etcd_grpc_proxy_cache_misses_total gauge
# TYPE etcd_grpc_proxy_events_coalescing_total counter
# TYPE etcd_grpc_proxy_watchers_coalescing_total gauge
# TYPE etcd_mvcc_db_open_read_transactions gauge
# TYPE etcd_mvcc_db_total_size_in_bytes gauge
# TYPE etcd_mvcc_db_total_size_in_use_in_bytes gauge
# TYPE etcd_mvcc_delete_total counter
# TYPE etcd_mvcc_hash_duration_seconds histogram
# TYPE etcd_mvcc_hash_rev_duration_seconds histogram
# TYPE etcd_mvcc_put_total counter
# TYPE etcd_mvcc_range_total counter
# TYPE etcd_mvcc_txn_total counter
# TYPE etcd_network_active_peers gauge
# TYPE etcd_network_client_grpc_received_bytes_total counter
# TYPE etcd_network_client_grpc_sent_bytes_total counter
# TYPE etcd_network_peer_received_bytes_total counter
# TYPE etcd_network_peer_round_trip_time_seconds histogram
# TYPE etcd_network_peer_sent_bytes_total counter
# TYPE etcd_server_go_version gauge
# TYPE etcd_server_has_leader gauge
# TYPE etcd_server_health_failures counter
# TYPE etcd_server_health_success counter
# TYPE etcd_server_heartbeat_send_failures_total counter
# TYPE etcd_server_id gauge
# TYPE etcd_server_is_leader gauge
# TYPE etcd_server_is_learner gauge
# TYPE etcd_server_leader_changes_seen_total counter
# TYPE etcd_server_learner_promote_successes counter
# TYPE etcd_server_proposals_applied_total gauge
# TYPE etcd_server_proposals_committed_total gauge
# TYPE etcd_server_proposals_failed_total counter
# TYPE etcd_server_proposals_pending gauge
# TYPE etcd_server_quota_backend_bytes gauge
# TYPE etcd_server_read_indexes_failed_total counter
# TYPE etcd_server_slow_apply_total counter
# TYPE etcd_server_slow_read_indexes_total counter
# TYPE etcd_server_snapshot_apply_in_progress_total gauge
# TYPE etcd_server_version gauge
# TYPE etcd_snap_db_fsync_duration_seconds histogram
# TYPE etcd_snap_db_save_total_duration_seconds histogram
# TYPE etcd_snap_fsync_duration_seconds histogram
# TYPE go_gc_duration_seconds summary
# TYPE go_goroutines gauge
# TYPE go_info gauge
# TYPE go_memstats_alloc_bytes gauge
# TYPE go_memstats_alloc_bytes_total counter
# TYPE go_memstats_buck_hash_sys_bytes gauge
# TYPE go_memstats_frees_total counter
# TYPE go_memstats_gc_cpu_fraction gauge
# TYPE go_memstats_gc_sys_bytes gauge
# TYPE go_memstats_heap_alloc_bytes gauge
# TYPE go_memstats_heap_idle_bytes gauge
# TYPE go_memstats_heap_inuse_bytes gauge
# TYPE go_memstats_heap_objects gauge
# TYPE go_memstats_heap_released_bytes gauge
# TYPE go_memstats_heap_sys_bytes gauge
# TYPE go_memstats_last_gc_time_seconds gauge
# TYPE go_memstats_lookups_total counter
# TYPE go_memstats_mallocs_total counter
# TYPE go_memstats_mcache_inuse_bytes gauge
# TYPE go_memstats_mcache_sys_bytes gauge
# TYPE go_memstats_mspan_inuse_bytes gauge
# TYPE go_memstats_mspan_sys_bytes gauge
# TYPE go_memstats_next_gc_bytes gauge
# TYPE go_memstats_other_sys_bytes gauge
# TYPE go_memstats_stack_inuse_bytes gauge
# TYPE go_memstats_stack_sys_bytes gauge
# TYPE go_memstats_sys_bytes gauge
# TYPE go_threads gauge
# TYPE grpc_server_handled_total counter
# TYPE grpc_server_msg_received_total counter
# TYPE grpc_server_msg_sent_total counter
# TYPE grpc_server_started_total counter
# TYPE process_cpu_seconds_total counter
# TYPE process_max_fds gauge
# TYPE process_open_fds gauge
# TYPE process_resident_memory_bytes gauge
# TYPE process_start_time_seconds gauge
# TYPE process_virtual_memory_bytes gauge
# TYPE process_virtual_memory_max_bytes gauge
# TYPE promhttp_metric_handler_requests_in_flight gauge
# TYPE promhttp_metric_handler_requests_total counter
```

## HELP

```md
# HELP etcd_cluster_version Which version is running. 1 for 'cluster_version' label with current cluster version
# HELP etcd_debugging_disk_backend_commit_rebalance_duration_seconds The latency distributions of commit.rebalance called by bboltdb backend.
# HELP etcd_debugging_disk_backend_commit_spill_duration_seconds The latency distributions of commit.spill called by bboltdb backend.
# HELP etcd_debugging_disk_backend_commit_write_duration_seconds The latency distributions of commit.write called by bboltdb backend.
# HELP etcd_debugging_lease_granted_total The total number of granted leases.
# HELP etcd_debugging_lease_renewed_total The number of renewed leases seen by the leader.
# HELP etcd_debugging_lease_revoked_total The total number of revoked leases.
# HELP etcd_debugging_lease_ttl_total Bucketed histogram of lease TTLs.
# HELP etcd_debugging_mvcc_compact_revision The revision of the last compaction in store.
# HELP etcd_debugging_mvcc_current_revision The current revision of store.
# HELP etcd_debugging_mvcc_db_compaction_keys_total Total number of db keys compacted.
# HELP etcd_debugging_mvcc_db_compaction_pause_duration_milliseconds Bucketed histogram of db compaction pause duration.
# HELP etcd_debugging_mvcc_db_compaction_total_duration_milliseconds Bucketed histogram of db compaction total duration.
# HELP etcd_debugging_mvcc_db_total_size_in_bytes Total size of the underlying database physically allocated in bytes.
# HELP etcd_debugging_mvcc_delete_total Total number of deletes seen by this member.
# HELP etcd_debugging_mvcc_events_total Total number of events sent by this member.
# HELP etcd_debugging_mvcc_index_compaction_pause_duration_milliseconds Bucketed histogram of index compaction pause duration.
# HELP etcd_debugging_mvcc_keys_total Total number of keys.
# HELP etcd_debugging_mvcc_pending_events_total Total number of pending events to be sent.
# HELP etcd_debugging_mvcc_put_total Total number of puts seen by this member.
# HELP etcd_debugging_mvcc_range_total Total number of ranges seen by this member.
# HELP etcd_debugging_mvcc_slow_watcher_total Total number of unsynced slow watchers.
# HELP etcd_debugging_mvcc_txn_total Total number of txns seen by this member.
# HELP etcd_debugging_mvcc_watcher_total Total number of watchers.
# HELP etcd_debugging_mvcc_watch_stream_total Total number of watch streams.
# HELP etcd_debugging_server_lease_expired_total The total number of expired leases.
# HELP etcd_debugging_snap_save_marshalling_duration_seconds The marshalling cost distributions of save called by snapshot.
# HELP etcd_debugging_snap_save_total_duration_seconds The total latency distributions of save called by snapshot.
# HELP etcd_debugging_store_expires_total Total number of expired keys.
# HELP etcd_debugging_store_reads_total Total number of reads action by (get/getRecursive), local to this member.
# HELP etcd_debugging_store_watchers Count of currently active watchers.
# HELP etcd_debugging_store_watch_requests_total Total number of incoming watch requests (new or reestablished).
# HELP etcd_debugging_store_writes_total Total number of writes (e.g. set/compareAndDelete) seen by this member.
# HELP etcd_disk_backend_commit_duration_seconds The latency distributions of commit called by backend.
# HELP etcd_disk_backend_defrag_duration_seconds The latency distribution of backend defragmentation.
# HELP etcd_disk_backend_snapshot_duration_seconds The latency distribution of backend snapshots.
# HELP etcd_disk_wal_fsync_duration_seconds The latency distributions of fsync called by WAL.
# HELP etcd_grpc_proxy_cache_hits_total Total number of cache hits
# HELP etcd_grpc_proxy_cache_keys_total Total number of keys/ranges cached
# HELP etcd_grpc_proxy_cache_misses_total Total number of cache misses
# HELP etcd_grpc_proxy_events_coalescing_total Total number of events coalescing
# HELP etcd_grpc_proxy_watchers_coalescing_total Total number of current watchers coalescing
# HELP etcd_mvcc_db_open_read_transactions The number of currently open read transactions
# HELP etcd_mvcc_db_total_size_in_bytes Total size of the underlying database physically allocated in bytes.
# HELP etcd_mvcc_db_total_size_in_use_in_bytes Total size of the underlying database logically in use in bytes.
# HELP etcd_mvcc_delete_total Total number of deletes seen by this member.
# HELP etcd_mvcc_hash_duration_seconds The latency distribution of storage hash operation.
# HELP etcd_mvcc_hash_rev_duration_seconds The latency distribution of storage hash by revision operation.
# HELP etcd_mvcc_put_total Total number of puts seen by this member.
# HELP etcd_mvcc_range_total Total number of ranges seen by this member.
# HELP etcd_mvcc_txn_total Total number of txns seen by this member.
# HELP etcd_network_active_peers The current number of active peer connections.
# HELP etcd_network_client_grpc_received_bytes_total The total number of bytes received from grpc clients.
# HELP etcd_network_client_grpc_sent_bytes_total The total number of bytes sent to grpc clients.
# HELP etcd_network_peer_received_bytes_total The total number of bytes received from peers.
# HELP etcd_network_peer_round_trip_time_seconds Round-Trip-Time histogram between peers
# HELP etcd_network_peer_sent_bytes_total The total number of bytes sent to peers.
# HELP etcd_server_go_version Which Go version server is running with. 1 for 'server_go_version' label with current version.
# HELP etcd_server_has_leader Whether or not a leader exists. 1 is existence, 0 is not.
# HELP etcd_server_health_failures The total number of failed health checks
# HELP etcd_server_health_success The total number of successful health checks
# HELP etcd_server_heartbeat_send_failures_total The total number of leader heartbeat send failures (likely overloaded from slow disk).
# HELP etcd_server_id Server or member ID in hexadecimal format. 1 for 'server_id' label with current ID.
# HELP etcd_server_is_leader Whether or not this member is a leader. 1 if is, 0 otherwise.
# HELP etcd_server_is_learner Whether or not this member is a learner. 1 if is, 0 otherwise.
# HELP etcd_server_leader_changes_seen_total The number of leader changes seen.
# HELP etcd_server_learner_promote_successes The total number of successful learner promotions while this member is leader.
# HELP etcd_server_proposals_applied_total The total number of consensus proposals applied.
# HELP etcd_server_proposals_committed_total The total number of consensus proposals committed.
# HELP etcd_server_proposals_failed_total The total number of failed proposals seen.
# HELP etcd_server_proposals_pending The current number of pending proposals to commit.
# HELP etcd_server_quota_backend_bytes Current backend storage quota size in bytes.
# HELP etcd_server_read_indexes_failed_total The total number of failed read indexes seen.
# HELP etcd_server_slow_apply_total The total number of slow apply requests (likely overloaded from slow disk).
# HELP etcd_server_slow_read_indexes_total The total number of pending read indexes not in sync with leader's or timed out read index requests.
# HELP etcd_server_snapshot_apply_in_progress_total 1 if the server is applying the incoming snapshot. 0 if none.
# HELP etcd_server_version Which version is running. 1 for 'server_version' label with current version.
# HELP etcd_snap_db_fsync_duration_seconds The latency distributions of fsyncing .snap.db file
# HELP etcd_snap_db_save_total_duration_seconds The total latency distributions of v3 snapshot save
# HELP etcd_snap_fsync_duration_seconds The latency distributions of fsync called by snap.
# HELP go_gc_duration_seconds A summary of the GC invocation durations.
# HELP go_goroutines Number of goroutines that currently exist.
# HELP go_info Information about the Go environment.
# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
# HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even if freed.
# HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table.
# HELP go_memstats_frees_total Total number of frees.
# HELP go_memstats_gc_cpu_fraction The fraction of this program's available CPU time used by the GC since the program started.
# HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
# HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use.
# HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used.
# HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use.
# HELP go_memstats_heap_objects Number of allocated objects.
# HELP go_memstats_heap_released_bytes Number of heap bytes released to OS.
# HELP go_memstats_heap_sys_bytes Number of heap bytes obtained from system.
# HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection.
# HELP go_memstats_lookups_total Total number of pointer lookups.
# HELP go_memstats_mallocs_total Total number of mallocs.
# HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures.
# HELP go_memstats_mcache_sys_bytes Number of bytes used for mcache structures obtained from system.
# HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures.
# HELP go_memstats_mspan_sys_bytes Number of bytes used for mspan structures obtained from system.
# HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place.
# HELP go_memstats_other_sys_bytes Number of bytes used for other system allocations.
# HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator.
# HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
# HELP go_memstats_sys_bytes Number of bytes obtained from system.
# HELP go_threads Number of OS threads created.
# HELP grpc_server_handled_total Total number of RPCs completed on the server, regardless of success or failure.
# HELP grpc_server_msg_received_total Total number of RPC stream messages received on the server.
# HELP grpc_server_msg_sent_total Total number of gRPC stream messages sent by the server.
# HELP grpc_server_started_total Total number of RPCs started on the server.
# HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.
# HELP process_max_fds Maximum number of open file descriptors.
# HELP process_open_fds Number of open file descriptors.
# HELP process_resident_memory_bytes Resident memory size in bytes.
# HELP process_start_time_seconds Start time of the process since unix epoch in seconds.
# HELP process_virtual_memory_bytes Virtual memory size in bytes.
# HELP process_virtual_memory_max_bytes Maximum amount of virtual memory available in bytes.
# HELP promhttp_metric_handler_requests_in_flight Current number of scrapes being served.
# HELP promhttp_metric_handler_requests_total Total number of scrapes by HTTP status code.
```

