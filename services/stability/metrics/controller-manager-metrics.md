# kube-controller-manager metrics

## 分类

## TYPE

```bash
# TYPE kubernetes_build_info gauge
# TYPE leader_election_master_status gauge


# TYPE process_max_fds gauge
# TYPE process_open_fds gauge
# TYPE process_start_time_seconds gauge
# TYPE process_resident_memory_bytes gauge
# TYPE process_virtual_memory_bytes gauge
# TYPE process_virtual_memory_max_bytes gauge
# TYPE process_cpu_seconds_total counter


# TYPE go_info gauge
# TYPE go_threads gauge
# TYPE go_goroutines gauge
# TYPE go_gc_duration_seconds summary
# TYPE go_memstats_alloc_bytes gauge
# TYPE go_memstats_alloc_bytes_total counter
# TYPE go_memstats_buck_hash_sys_bytes gauge
# TYPE go_memstats_frees_total counter
# TYPE go_memstats_gc_cpu_fraction gauge
# TYPE go_memstats_gc_sys_bytes gauge
# TYPE go_memstats_heap_alloc_bytes gauge
# TYPE go_memstats_heap_idle_bytes gauge
# TYPE go_memstats_heap_inuse_bytes gauge
# TYPE go_memstats_heap_objects gauge
# TYPE go_memstats_heap_released_bytes gauge
# TYPE go_memstats_heap_sys_bytes gauge
# TYPE go_memstats_last_gc_time_seconds gauge
# TYPE go_memstats_lookups_total counter
# TYPE go_memstats_mallocs_total counter
# TYPE go_memstats_mcache_inuse_bytes gauge
# TYPE go_memstats_mcache_sys_bytes gauge
# TYPE go_memstats_mspan_inuse_bytes gauge
# TYPE go_memstats_mspan_sys_bytes gauge
# TYPE go_memstats_next_gc_bytes gauge
# TYPE go_memstats_other_sys_bytes gauge
# TYPE go_memstats_stack_inuse_bytes gauge
# TYPE go_memstats_stack_sys_bytes gauge
# TYPE go_memstats_sys_bytes gauge


# TYPE token_cleaner_rate_limiter_use gauge
# TYPE cronjob_controller_rate_limiter_use gauge
# TYPE daemon_controller_rate_limiter_use gauge
# TYPE deployment_controller_rate_limiter_use gauge
# TYPE bootstrap_signer_rate_limiter_use gauge
# TYPE endpoint_controller_rate_limiter_use gauge
# TYPE endpoint_slice_controller_rate_limiter_use gauge
# TYPE endpoint_slice_mirroring_controller_rate_limiter_use gauge
# TYPE gc_controller_rate_limiter_use gauge
# TYPE node_ipam_controller_rate_limiter_use gauge
# TYPE node_lifecycle_controller_rate_limiter_use gauge
# TYPE persistentvolume_protection_controller_rate_limiter_use gauge
# TYPE persistentvolumeclaim_protection_controller_rate_limiter_use gauge
# TYPE replicaset_controller_rate_limiter_use gauge
# TYPE replication_controller_rate_limiter_use gauge
# TYPE resource_quota_controller_rate_limiter_use gauge
# TYPE job_controller_rate_limiter_use gauge
# TYPE namespace_controller_rate_limiter_use gauge
# TYPE root_ca_cert_publisher_rate_limiter_use gauge
# TYPE service_controller_rate_limiter_use gauge
# TYPE serviceaccount_controller_rate_limiter_use gauge
# TYPE serviceaccount_tokens_controller_rate_limiter_use gauge
# TYPE ttl_after_finished_controller_rate_limiter_use gauge


# TYPE apiserver_audit_event_total counter
# TYPE apiserver_audit_requests_rejected_total counter
# TYPE apiserver_client_certificate_expiration_seconds histogram
# TYPE apiserver_envelope_encryption_dek_cache_fill_percent gauge
# TYPE apiserver_storage_data_key_generation_duration_seconds histogram
# TYPE apiserver_storage_data_key_generation_failures_total counter
# TYPE apiserver_storage_envelope_transformation_cache_misses_total counter


# TYPE authentication_attempts counter
# TYPE authenticated_user_requests counter
# TYPE authentication_duration_seconds histogram


# TYPE attachdetach_controller_forced_detaches counter


# TYPE endpoint_slice_controller_desired_endpoint_slices gauge
# TYPE endpoint_slice_controller_endpoints_added_per_sync histogram
# TYPE endpoint_slice_controller_endpoints_desired gauge
# TYPE endpoint_slice_controller_endpoints_removed_per_sync histogram
# TYPE endpoint_slice_controller_num_endpoint_slices gauge
# TYPE endpoint_slice_mirroring_controller_endpoints_sync_duration histogram


# TYPE get_token_count counter
# TYPE get_token_fail_count counter


# TYPE node_collector_evictions_number counter
# TYPE node_collector_unhealthy_nodes_in_zone gauge
# TYPE node_collector_zone_size gauge
# TYPE node_collector_zone_health gauge


# TYPE node_ipam_controller_cidrset_cidrs_allocations_total counter
# TYPE node_ipam_controller_cidrset_usage_cidrs gauge


# TYPE rest_client_requests_total counter
# TYPE rest_client_request_duration_seconds histogram
# TYPE rest_client_exec_plugin_ttl_seconds gauge
# TYPE rest_client_exec_plugin_certificate_rotation_age histogram

# TYPE ssh_tunnel_open_count counter
# TYPE ssh_tunnel_open_fail_count counter

# TYPE workqueue_depth gauge
# TYPE workqueue_adds_total counter
# TYPE workqueue_longest_running_processor_seconds gauge
# TYPE workqueue_queue_duration_seconds histogram
# TYPE workqueue_retries_total counter
# TYPE workqueue_unfinished_work_seconds gauge
# TYPE workqueue_work_duration_seconds histogram
```

## HELP

```bash
# HELP apiserver_audit_event_total [ALPHA] Counter of audit events generated and sent to the audit backend.
# HELP apiserver_audit_requests_rejected_total [ALPHA] Counter of apiserver requests rejected due to an error in audit logging backend.
# HELP apiserver_client_certificate_expiration_seconds [ALPHA] Distribution of the remaining lifetime on the certificate used to authenticate a request.
# HELP apiserver_envelope_encryption_dek_cache_fill_percent [ALPHA] Percent of the cache slots currently occupied by cached DEKs.
# HELP apiserver_storage_data_key_generation_duration_seconds [ALPHA] Latencies in seconds of data encryption key(DEK) generation operations.
# HELP apiserver_storage_data_key_generation_failures_total [ALPHA] Total number of failed data encryption key(DEK) generation operations.
# HELP apiserver_storage_envelope_transformation_cache_misses_total [ALPHA] Total number of cache misses while accessing key decryption key(KEK).
# HELP attachdetach_controller_forced_detaches [ALPHA] Number of times the A/D Controller performed a forced detach
# HELP authenticated_user_requests [ALPHA] Counter of authenticated requests broken out by username.
# HELP authentication_attempts [ALPHA] Counter of authenticated attempts.
# HELP authentication_duration_seconds [ALPHA] Authentication duration in seconds broken out by result.
# HELP bootstrap_signer_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for bootstrap_signer
# HELP cronjob_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for cronjob_controller
# HELP daemon_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for daemon_controller
# HELP deployment_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for deployment_controller
# HELP endpoint_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for endpoint_controller
# HELP endpoint_slice_controller_desired_endpoint_slices [ALPHA] Number of EndpointSlices that would exist with perfect endpoint allocation
# HELP endpoint_slice_controller_endpoints_added_per_sync [ALPHA] Number of endpoints added on each Service sync
# HELP endpoint_slice_controller_endpoints_desired [ALPHA] Number of endpoints desired
# HELP endpoint_slice_controller_endpoints_removed_per_sync [ALPHA] Number of endpoints removed on each Service sync
# HELP endpoint_slice_controller_num_endpoint_slices [ALPHA] Number of EndpointSlices
# HELP endpoint_slice_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for endpoint_slice_controller
# HELP endpoint_slice_mirroring_controller_endpoints_sync_duration [ALPHA] Duration of syncEndpoints() in seconds
# HELP endpoint_slice_mirroring_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for endpoint_slice_mirroring_controller
# HELP gc_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for gc_controller
# HELP get_token_count [ALPHA] Counter of total Token() requests to the alternate token source
# HELP get_token_fail_count [ALPHA] Counter of failed Token() requests to the alternate token source
# HELP go_gc_duration_seconds A summary of the pause duration of garbage collection cycles.
# HELP go_goroutines Number of goroutines that currently exist.
# HELP go_info Information about the Go environment.
# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
# HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even if freed.
# HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table.
# HELP go_memstats_frees_total Total number of frees.
# HELP go_memstats_gc_cpu_fraction The fraction of this program's available CPU time used by the GC since the program started.
# HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
# HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use.
# HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used.
# HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use.
# HELP go_memstats_heap_objects Number of allocated objects.
# HELP go_memstats_heap_released_bytes Number of heap bytes released to OS.
# HELP go_memstats_heap_sys_bytes Number of heap bytes obtained from system.
# HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection.
# HELP go_memstats_lookups_total Total number of pointer lookups.
# HELP go_memstats_mallocs_total Total number of mallocs.
# HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures.
# HELP go_memstats_mcache_sys_bytes Number of bytes used for mcache structures obtained from system.
# HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures.
# HELP go_memstats_mspan_sys_bytes Number of bytes used for mspan structures obtained from system.
# HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place.
# HELP go_memstats_other_sys_bytes Number of bytes used for other system allocations.
# HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator.
# HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
# HELP go_memstats_sys_bytes Number of bytes obtained from system.
# HELP go_threads Number of OS threads created.
# HELP job_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for job_controller
# HELP kubernetes_build_info [ALPHA] A metric with a constant '1' value labeled by major, minor, git version, git commit, git tree state, build date, Go version, and compiler from which Kubernetes was built, and platform on which it is running.
# HELP leader_election_master_status [ALPHA] Gauge of if the reporting system is master of the relevant lease, 0 indicates backup, 1 indicates master. 'name' is the string used to identify the lease. Please make sure to group by name.
# HELP namespace_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for namespace_controller
# HELP node_collector_evictions_number [ALPHA] Number of Node evictions that happened since current instance of NodeController started.
# HELP node_collector_unhealthy_nodes_in_zone [ALPHA] Gauge measuring number of not Ready Nodes per zones.
# HELP node_collector_zone_health [ALPHA] Gauge measuring percentage of healthy nodes per zone.
# HELP node_collector_zone_size [ALPHA] Gauge measuring number of registered Nodes per zones.
# HELP node_ipam_controller_cidrset_cidrs_allocations_total [ALPHA] Counter measuring total number of CIDR allocations.
# HELP node_ipam_controller_cidrset_usage_cidrs [ALPHA] Gauge measuring percentage of allocated CIDRs.
# HELP node_ipam_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for node_ipam_controller
# HELP node_lifecycle_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for node_lifecycle_controller
# HELP persistentvolume_protection_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for persistentvolume_protection_controller
# HELP persistentvolumeclaim_protection_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for persistentvolumeclaim_protection_controller
# HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.
# HELP process_max_fds Maximum number of open file descriptors.
# HELP process_open_fds Number of open file descriptors.
# HELP process_resident_memory_bytes Resident memory size in bytes.
# HELP process_start_time_seconds Start time of the process since unix epoch in seconds.
# HELP process_virtual_memory_bytes Virtual memory size in bytes.
# HELP process_virtual_memory_max_bytes Maximum amount of virtual memory available in bytes.
# HELP replicaset_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for replicaset_controller
# HELP replication_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for replication_controller
# HELP resource_quota_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for resource_quota_controller
# HELP rest_client_exec_plugin_certificate_rotation_age [ALPHA] Histogram of the number of seconds the last auth exec plugin client certificate lived before being rotated. If auth exec plugin client certificates are unused, histogram will contain no data.
# HELP rest_client_exec_plugin_ttl_seconds [ALPHA] Gauge of the shortest TTL (time-to-live) of the client certificate(s) managed by the auth exec plugin. The value is in seconds until certificate expiry (negative if already expired). If auth exec plugins are unused or manage no TLS certificates, the value will be +INF.
# HELP rest_client_request_duration_seconds [ALPHA] Request latency in seconds. Broken down by verb and URL.
# HELP rest_client_requests_total [ALPHA] Number of HTTP requests, partitioned by status code, method, and host.
# HELP root_ca_cert_publisher_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for root_ca_cert_publisher
# HELP service_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for service_controller
# HELP serviceaccount_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for serviceaccount_controller
# HELP serviceaccount_tokens_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for serviceaccount_tokens_controller
# HELP ssh_tunnel_open_count [ALPHA] Counter of ssh tunnel total open attempts
# HELP ssh_tunnel_open_fail_count [ALPHA] Counter of ssh tunnel failed open attempts
# HELP token_cleaner_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for token_cleaner
# HELP ttl_after_finished_controller_rate_limiter_use [ALPHA] A metric measuring the saturation of the rate limiter for ttl_after_finished_controller
# HELP workqueue_adds_total [ALPHA] Total number of adds handled by workqueue
# HELP workqueue_depth [ALPHA] Current depth of workqueue
# HELP workqueue_longest_running_processor_seconds [ALPHA] How many seconds has the longest running processor for workqueue been running.
# HELP workqueue_queue_duration_seconds [ALPHA] How long in seconds an item stays in workqueue before being requested.
# HELP workqueue_retries_total [ALPHA] Total number of retries handled by workqueue
# HELP workqueue_unfinished_work_seconds [ALPHA] How many seconds of work has done that is in progress and hasn't been observed by work_duration. Large values indicate stuck threads. One can deduce the number of stuck threads by observing the rate at which this increases.
# HELP workqueue_work_duration_seconds [ALPHA] How long in seconds processing an item from workqueue takes.
```