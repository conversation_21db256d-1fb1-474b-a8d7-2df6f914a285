# Kubernetes APIServer Metrics 说明

## 概要

```bash
# TYPE kubernetes_build_info gauge


# TYPE apiserver_request_total counter
# TYPE apiserver_selfrequest_total counter
# TYPE apiserver_request_total_chenhuan counter
# TYPE apiserver_dropped_requests_total counter
# TYPE apiserver_request_terminations_total counter


# TYPE apiserver_tls_handshake_errors_total counter
# TYPE apiserver_request_duration_seconds histogram
# TYPE apiserver_request_filter_duration_seconds histogram


# TYPE apiserver_response_sizes histogram


# TYPE apiserver_longrunning_gauge gauge
# TYPE apiserver_requested_deprecated_apis gauge


# TYPE apiserver_init_events_total counter

# TYPE apiserver_registered_watchers gauge
# TYPE apiserver_watch_events_sizes histogram
# TYPE apiserver_watch_events_total counter
# TYPE watch_cache_capacity_increase_total counter


# TYPE etcd_object_counts gauge
# TYPE etcd_db_total_size_in_bytes gauge
# TYPE etcd_lease_object_counts histogram
# TYPE etcd_request_duration_seconds histogram


# TYPE aggregator_unavailable_apiservice gauge
# TYPE aggregator_openapi_v2_regeneration_count counter
# TYPE aggregator_openapi_v2_regeneration_duration gauge


# TYPE apiserver_admission_controller_admission_duration_seconds histogram
# TYPE apiserver_admission_step_admission_duration_seconds histogram
# TYPE apiserver_admission_step_admission_duration_seconds_summary summary


# TYPE apiserver_audit_event_total counter
# TYPE apiserver_audit_level_total counter
# TYPE apiserver_audit_requests_rejected_total counter

# TYPE apiserver_envelope_encryption_dek_cache_fill_percent gauge


# TYPE apiserver_current_inflight_requests gauge
# TYPE apiserver_current_inqueue_requests gauge

# TYPE apiserver_flowcontrol_current_executing_requests gauge
# TYPE apiserver_flowcontrol_current_inqueue_requests gauge
# TYPE apiserver_flowcontrol_dispatched_requests_total counter
# TYPE apiserver_flowcontrol_priority_level_request_count_samples histogram
# TYPE apiserver_flowcontrol_priority_level_request_count_watermarks histogram
# TYPE apiserver_flowcontrol_read_vs_write_request_count_samples histogram
# TYPE apiserver_flowcontrol_read_vs_write_request_count_watermarks histogram
# TYPE apiserver_flowcontrol_rejected_requests_total counter
# TYPE apiserver_flowcontrol_request_concurrency_limit gauge
# TYPE apiserver_flowcontrol_request_execution_seconds histogram
# TYPE apiserver_flowcontrol_request_queue_length_after_enqueue histogram
# TYPE apiserver_flowcontrol_request_wait_duration_seconds histogram



# TYPE apiserver_storage_data_key_generation_failures_total counter
# TYPE apiserver_storage_data_key_generation_duration_seconds histogram
# TYPE apiserver_storage_envelope_transformation_cache_misses_total counter



# TYPE authentication_attempts counter
# TYPE authenticated_user_requests counter
# TYPE authentication_duration_seconds histogram
# TYPE authentication_token_cache_active_fetch_count gauge
# TYPE authentication_token_cache_fetch_total counter
# TYPE authentication_token_cache_request_duration_seconds histogram
# TYPE authentication_token_cache_request_total counter


# TYPE get_token_count counter
# TYPE get_token_fail_count counter


# TYPE grpc_client_handled_total counter
# TYPE grpc_client_msg_received_total counter
# TYPE grpc_client_msg_sent_total counter
# TYPE grpc_client_started_total counter


# TYPE serviceaccount_legacy_tokens_total counter
# TYPE serviceaccount_stale_tokens_total counter
# TYPE serviceaccount_valid_tokens_total counter


# TYPE rest_client_requests_total counter
# TYPE rest_client_request_duration_seconds histogram
# TYPE rest_client_exec_plugin_ttl_seconds gauge
# TYPE rest_client_exec_plugin_certificate_rotation_age histogram


# TYPE ssh_tunnel_open_count counter
# TYPE ssh_tunnel_open_fail_count counter


# TYPE node_authorizer_graph_actions_duration_seconds histogram


# TYPE workqueue_depth gauge
# TYPE workqueue_adds_total counter
# TYPE workqueue_retries_total counter
# TYPE workqueue_unfinished_work_seconds gauge
# TYPE workqueue_work_duration_seconds histogram
# TYPE workqueue_queue_duration_seconds histogram
# TYPE workqueue_longest_running_processor_seconds gauge


# TYPE go_info gauge
# TYPE go_threads gauge
# TYPE go_goroutines gauge

# TYPE go_gc_duration_seconds summary

# TYPE go_memstats_alloc_bytes gauge
# TYPE go_memstats_alloc_bytes_total counter
# TYPE go_memstats_buck_hash_sys_bytes gauge
# TYPE go_memstats_frees_total counter
# TYPE go_memstats_gc_cpu_fraction gauge
# TYPE go_memstats_gc_sys_bytes gauge

# TYPE go_memstats_heap_alloc_bytes gauge
# TYPE go_memstats_heap_idle_bytes gauge
# TYPE go_memstats_heap_inuse_bytes gauge
# TYPE go_memstats_heap_objects gauge
# TYPE go_memstats_heap_released_bytes gauge
# TYPE go_memstats_heap_sys_bytes gauge

# TYPE go_memstats_last_gc_time_seconds gauge
# TYPE go_memstats_lookups_total counter
# TYPE go_memstats_mallocs_total counter
# TYPE go_memstats_mcache_inuse_bytes gauge
# TYPE go_memstats_mcache_sys_bytes gauge
# TYPE go_memstats_mspan_inuse_bytes gauge
# TYPE go_memstats_mspan_sys_bytes gauge
# TYPE go_memstats_next_gc_bytes gauge
# TYPE go_memstats_other_sys_bytes gauge
# TYPE go_memstats_stack_inuse_bytes gauge
# TYPE go_memstats_stack_sys_bytes gauge
# TYPE go_memstats_sys_bytes gauge


# TYPE process_cpu_seconds_total counter
# TYPE process_resident_memory_bytes gauge
# TYPE process_virtual_memory_bytes gauge
# TYPE process_virtual_memory_max_bytes gauge


# TYPE process_max_fds gauge
# TYPE process_open_fds gauge
# TYPE process_start_time_seconds gauge
```

## Admission Metrics

k8s.io/apiserver/pkg/admission/metrics/metrics.go

```bash
# apiserver_admission_step_admission_duration_seconds Histogram
# apiserver_admission_webhook_admission_duration_seconds Histogram
# apiserver_admission_controller_admission_duration_seconds Histogram
```

```go
// newAdmissionMetrics create a new AdmissionMetrics, configured with default metric names.
func newAdmissionMetrics() *AdmissionMetrics {
    // Admission metrics for a step of the admission flow. The entire admission flow is broken down into a series of steps
    // Each step is identified by a distinct type label value.
    step := newMetricSet("step",
        []string{"type", "operation", "rejected"},
        "Admission sub-step %s, broken out for each operation and API resource and step type (validate or admit).", true)

    // Built-in admission controller metrics. Each admission controller is identified by name.
    controller := newMetricSet("controller",
        []string{"name", "type", "operation", "rejected"},
        "Admission controller %s, identified by name and broken out for each operation and API resource and type (validate or admit).", false)

    // Admission webhook metrics. Each webhook is identified by name.
    webhook := newMetricSet("webhook",
        []string{"name", "type", "operation", "rejected"},
        "Admission webhook %s, identified by name and broken out for each operation and API resource and type (validate or admit).", false)

    webhookRejection := metrics.NewCounterVec(
        &metrics.CounterOpts{
            Namespace:      namespace,
            Subsystem:      subsystem,
            Name:           "webhook_rejection_count",
            Help:           "Admission webhook rejection count, identified by name and broken out for each admission type (validating or admit) and operation. Additional labels specify an error type (calling_webhook_error or apiserver_internal_error if an error occurred; no_error otherwise) and optionally a non-zero rejection code if the webhook rejects the request with an HTTP status code (honored by the apiserver when the code is greater or equal to 400). Codes greater than 600 are truncated to 600, to keep the metrics cardinality bounded.",
            StabilityLevel: metrics.ALPHA,
        },
        []string{"name", "type", "operation", "error_type", "rejection_code"})

    step.mustRegister()
    controller.mustRegister()
    webhook.mustRegister()
    legacyregistry.MustRegister(webhookRejection)
    return &AdmissionMetrics{step: step, controller: controller, webhook: webhook, webhookRejection: webhookRejection}
}

func newMetricSet(name string, labels []string, helpTemplate string, hasSummary bool) *metricSet {
    var summary *metrics.SummaryVec
    if hasSummary {
        summary = metrics.NewSummaryVec(
            &metrics.SummaryOpts{
                Namespace:      namespace,
                Subsystem:      subsystem,
                Name:           fmt.Sprintf("%s_admission_duration_seconds_summary", name),
                Help:           fmt.Sprintf(helpTemplate, "latency summary in seconds"),
                MaxAge:         latencySummaryMaxAge,
                StabilityLevel: metrics.ALPHA,
            },
            labels,
        )
    }

    return &metricSet{
        latencies: metrics.NewHistogramVec(
            &metrics.HistogramOpts{
                Namespace:      namespace,
                Subsystem:      subsystem,
                Name:           fmt.Sprintf("%s_admission_duration_seconds", name),
                Help:           fmt.Sprintf(helpTemplate, "latency histogram in seconds"),
                Buckets:        latencyBuckets,
                StabilityLevel: metrics.ALPHA,
            },
            labels,
        ),

        latenciesSummary: summary,
    }
}
```