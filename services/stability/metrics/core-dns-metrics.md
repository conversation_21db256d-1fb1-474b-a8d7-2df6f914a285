# CoreDNS Metrics

## Metrics

```bash
# TYPE coredns_build_info gauge
# TYPE coredns_plugin_enabled gauge

# TYPE coredns_dns_do_requests_total counter
# TYPE coredns_dns_requests_total counter
# TYPE coredns_dns_responses_total counter

# TYPE coredns_dns_request_size_bytes histogram
# TYPE coredns_dns_response_size_bytes histogram
# TYPE coredns_dns_request_duration_seconds histogram


# TYPE coredns_forward_requests_total counter
# TYPE coredns_forward_responses_total counter
# TYPE coredns_forward_conn_cache_hits_total counter
# TYPE coredns_forward_conn_cache_misses_total counter
# TYPE coredns_forward_request_duration_seconds histogram
# TYPE coredns_forward_max_concurrent_rejects_total counter


# TYPE coredns_forward_healthcheck_broken_total counter
# TYPE coredns_health_request_duration_seconds histogram

# TYPE coredns_cache_entries gauge
# TYPE coredns_cache_misses_total counter

# TYPE coredns_panics_total counter
# TYPE coredns_reload_failed_total counter
# TYPE coredns_hosts_reload_timestamp_seconds gauge
```

## TYPE

```bash
# TYPE coredns_build_info gauge
# TYPE coredns_cache_entries gauge
# TYPE coredns_cache_misses_total counter
# TYPE coredns_dns_do_requests_total counter
# TYPE coredns_dns_request_duration_seconds histogram
# TYPE coredns_dns_request_size_bytes histogram
# TYPE coredns_dns_requests_total counter
# TYPE coredns_dns_response_size_bytes histogram
# TYPE coredns_dns_responses_total counter
# TYPE coredns_forward_conn_cache_hits_total counter
# TYPE coredns_forward_conn_cache_misses_total counter
# TYPE coredns_forward_healthcheck_broken_total counter
# TYPE coredns_forward_max_concurrent_rejects_total counter
# TYPE coredns_forward_request_duration_seconds histogram
# TYPE coredns_forward_requests_total counter
# TYPE coredns_forward_responses_total counter
# TYPE coredns_health_request_duration_seconds histogram
# TYPE coredns_hosts_reload_timestamp_seconds gauge
# TYPE coredns_panics_total counter
# TYPE coredns_plugin_enabled gauge
# TYPE coredns_reload_failed_total counter

# TYPE go_gc_duration_seconds summary
# TYPE go_goroutines gauge
# TYPE go_info gauge
# TYPE go_memstats_alloc_bytes gauge
# TYPE go_memstats_alloc_bytes_total counter
# TYPE go_memstats_buck_hash_sys_bytes gauge
# TYPE go_memstats_frees_total counter
# TYPE go_memstats_gc_cpu_fraction gauge
# TYPE go_memstats_gc_sys_bytes gauge
# TYPE go_memstats_heap_alloc_bytes gauge
# TYPE go_memstats_heap_idle_bytes gauge
# TYPE go_memstats_heap_inuse_bytes gauge
# TYPE go_memstats_heap_objects gauge
# TYPE go_memstats_heap_released_bytes gauge
# TYPE go_memstats_heap_sys_bytes gauge
# TYPE go_memstats_last_gc_time_seconds gauge
# TYPE go_memstats_lookups_total counter
# TYPE go_memstats_mallocs_total counter
# TYPE go_memstats_mcache_inuse_bytes gauge
# TYPE go_memstats_mcache_sys_bytes gauge
# TYPE go_memstats_mspan_inuse_bytes gauge
# TYPE go_memstats_mspan_sys_bytes gauge
# TYPE go_memstats_next_gc_bytes gauge
# TYPE go_memstats_other_sys_bytes gauge
# TYPE go_memstats_stack_inuse_bytes gauge
# TYPE go_memstats_stack_sys_bytes gauge
# TYPE go_memstats_sys_bytes gauge
# TYPE go_threads gauge
# TYPE process_cpu_seconds_total counter
# TYPE process_max_fds gauge
# TYPE process_open_fds gauge
# TYPE process_resident_memory_bytes gauge
# TYPE process_start_time_seconds gauge
# TYPE process_virtual_memory_bytes gauge
# TYPE process_virtual_memory_max_bytes gauge
```

## HELP

```bash
# HELP coredns_build_info A metric with a constant '1' value labeled by version, revision, and goversion from which CoreDNS was built.
# HELP coredns_cache_entries The number of elements in the cache.
# HELP coredns_cache_misses_total The count of cache misses.
# HELP coredns_dns_do_requests_total Counter of DNS requests with DO bit set per zone.
# HELP coredns_dns_request_duration_seconds Histogram of the time (in seconds) each request took.
# HELP coredns_dns_request_size_bytes Size of the EDNS0 UDP buffer in bytes (64K for TCP).
# HELP coredns_dns_requests_total Counter of DNS requests made per zone, protocol and family.
# HELP coredns_dns_response_size_bytes Size of the returned response in bytes.
# HELP coredns_dns_responses_total Counter of response status codes.
# HELP coredns_forward_conn_cache_hits_total Counter of connection cache hits per upstream and protocol.
# HELP coredns_forward_conn_cache_misses_total Counter of connection cache misses per upstream and protocol.
# HELP coredns_forward_healthcheck_broken_total Counter of the number of complete failures of the healthchecks.
# HELP coredns_forward_max_concurrent_rejects_total Counter of the number of queries rejected because the concurrent queries were at maximum.
# HELP coredns_forward_request_duration_seconds Histogram of the time each request took.
# HELP coredns_forward_requests_total Counter of requests made per upstream.
# HELP coredns_forward_responses_total Counter of responses received per upstream.
# HELP coredns_health_request_duration_seconds Histogram of the time (in seconds) each request took.
# HELP coredns_hosts_reload_timestamp_seconds The timestamp of the last reload of hosts file.
# HELP coredns_panics_total A metrics that counts the number of panics.
# HELP coredns_plugin_enabled A metric that indicates whether a plugin is enabled on per server and zone basis.
# HELP coredns_reload_failed_total Counter of the number of failed reload attempts.
# HELP go_gc_duration_seconds A summary of the pause duration of garbage collection cycles.
# HELP go_goroutines Number of goroutines that currently exist.
# HELP go_info Information about the Go environment.
# HELP go_memstats_alloc_bytes Number of bytes allocated and still in use.
# HELP go_memstats_alloc_bytes_total Total number of bytes allocated, even if freed.
# HELP go_memstats_buck_hash_sys_bytes Number of bytes used by the profiling bucket hash table.
# HELP go_memstats_frees_total Total number of frees.
# HELP go_memstats_gc_cpu_fraction The fraction of this program's available CPU time used by the GC since the program started.
# HELP go_memstats_gc_sys_bytes Number of bytes used for garbage collection system metadata.
# HELP go_memstats_heap_alloc_bytes Number of heap bytes allocated and still in use.
# HELP go_memstats_heap_idle_bytes Number of heap bytes waiting to be used.
# HELP go_memstats_heap_inuse_bytes Number of heap bytes that are in use.
# HELP go_memstats_heap_objects Number of allocated objects.
# HELP go_memstats_heap_released_bytes Number of heap bytes released to OS.
# HELP go_memstats_heap_sys_bytes Number of heap bytes obtained from system.
# HELP go_memstats_last_gc_time_seconds Number of seconds since 1970 of last garbage collection.
# HELP go_memstats_lookups_total Total number of pointer lookups.
# HELP go_memstats_mallocs_total Total number of mallocs.
# HELP go_memstats_mcache_inuse_bytes Number of bytes in use by mcache structures.
# HELP go_memstats_mcache_sys_bytes Number of bytes used for mcache structures obtained from system.
# HELP go_memstats_mspan_inuse_bytes Number of bytes in use by mspan structures.
# HELP go_memstats_mspan_sys_bytes Number of bytes used for mspan structures obtained from system.
# HELP go_memstats_next_gc_bytes Number of heap bytes when next garbage collection will take place.
# HELP go_memstats_other_sys_bytes Number of bytes used for other system allocations.
# HELP go_memstats_stack_inuse_bytes Number of bytes in use by the stack allocator.
# HELP go_memstats_stack_sys_bytes Number of bytes obtained from system for stack allocator.
# HELP go_memstats_sys_bytes Number of bytes obtained from system.
# HELP go_threads Number of OS threads created.
# HELP process_cpu_seconds_total Total user and system CPU time spent in seconds.
# HELP process_max_fds Maximum number of open file descriptors.
# HELP process_open_fds Number of open file descriptors.
# HELP process_resident_memory_bytes Resident memory size in bytes.
# HELP process_start_time_seconds Start time of the process since unix epoch in seconds.
# HELP process_virtual_memory_bytes Virtual memory size in bytes.
# HELP process_virtual_memory_max_bytes Maximum amount of virtual memory available in bytes.
```