{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 11, "iteration": 1655446662774, "links": [], "panels": [{"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 124, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 1}, "id": 119, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^git_version$/", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "kubernetes_build_info{region=\"$region\", clusterID=\"$clusterID\"}", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Version", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 1}, "id": 120, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "count(procstat_num_threads{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"} > 0)", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Up", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "displayName", "value": "count"}]}]}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 1}, "id": 121, "links": [], "options": {"showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "MAX(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource=~\"pods|nodes|services\"}) by (resource)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Resource Count", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["resource", "Value"]}}}], "type": "table"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 1}, "id": 122, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "API Objects QPS", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "noValue": "0%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 0, "y": 6}, "id": 129, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", code=~\"5..\"}) / SUM(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "5XX Request Rate", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 6, "y": 6}, "id": 130, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "histogram_quantile (\n          0.99,\n          sum by (le)(\n          irate(apiserver_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", verb!~\"CONNECT|WATCH\"}[1m]))\n        )", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Request Latency P99", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 12, "y": 6}, "id": 141, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "MAX(procstat_cpu_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}, {"datasource": "${datasource}", "exemplar": true, "expr": "", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Max(APIServer top CPU%)", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 5, "w": 6, "x": 18, "y": 6}, "id": 142, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "MAX(procstat_memory_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "MAX(APIServer top %MEM)", "transformations": [], "type": "stat"}], "title": "Overview", "type": "row"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 126, "panels": [], "title": "SLO", "type": "row"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 1}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 0, "y": 2}, "id": 175, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", code=~\"5..\"}) / SUM(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Request Error Rate", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 7, "x": 4, "y": 2}, "hiddenSeries": false, "id": 127, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "total", "refId": "E"}, {"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{job=\"master-metrics\",region=\"$region\",clusterID=\"$clusterID\", instance=~\"$instance\", code=~\"2..\"}[1m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "2xx", "refId": "A"}, {"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{job=\"master-metrics\",region=\"$region\", instance=~\"$instance\",code=~\"3..\",clusterID=\"$clusterID\"}[1m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "3xx", "refId": "B"}, {"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{job=\"master-metrics\",region=\"$region\", instance=~\"$instance\",code=~\"4..\",clusterID=\"$clusterID\"}[1m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "4xx", "refId": "C"}, {"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{job=\"master-metrics\",region=\"$region\", instance=~\"$instance\",code=~\"5..\",clusterID=\"$clusterID\"}[1m]))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "5xx", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "API Objects QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 7, "x": 11, "y": 2}, "hiddenSeries": false, "id": 134, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_filter_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (filter)", "hide": false, "interval": "", "legendFormat": "{{filter}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Filter<PERSON>hain QPS (>=1.20)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 2}, "hiddenSeries": false, "id": 102, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_terminations_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "termination in self-defense", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Request termination in self-defense QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 0, "y": 8}, "id": 176, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "histogram_quantile (\n          0.99,\n          sum by (le)(\n          rate(apiserver_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", verb!~\"CONNECT|WATCH\"}[5m]))\n        )", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Request Latency P99", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Mutating calls for single objects Latency P99", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 7, "x": 4, "y": 8}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb)(\n          rate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb=~\"PUT|POST|PATCH|DELETE\"}[5m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mutating Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Read-only API calls for objects Latency P99", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 7, "x": 11, "y": 8}, "hiddenSeries": false, "id": 133, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb)(\n          irate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb=~\"LIST\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "List Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 8}, "hiddenSeries": false, "id": 131, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb)(\n          irate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb=~\"GET\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Get Latency  P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 14}, "id": 138, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 33, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", code=~\"5..\"}[1m])) by (verb, scope, version, resource)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}  {{scope}}/{{version}}/{{resource}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_request_total 5xx", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 177, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_filter_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (filter)", "hide": false, "interval": "", "legendFormat": "{{filter}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Filter<PERSON>hain QPS (>= 1.20)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 179, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_terminations_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (verb, resource)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}} {{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_request_terminations_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 22}, "hiddenSeries": false, "id": 108, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_dropped_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", requestKind!~\"\"}[1m])) by (requestKind)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{requestKind}}", "refId": "E"}, {"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_dropped_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", request_kind!~\"\"}[1m])) by (request_kind)", "hide": false, "interval": "", "legendFormat": "{{request_kind}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_dropped_requests_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 29}, "hiddenSeries": false, "id": 196, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "aggregator_unavailable_apiservice{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"} == 1", "hide": false, "interval": "", "legendFormat": "{{name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "aggregator_unavailable_apiservice", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 29}, "hiddenSeries": false, "id": 101, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_tls_handshake_errors_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "tls_handshake_errors", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_tls_handshake_errors_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 29}, "hiddenSeries": false, "id": 103, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_aborts_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "aborts", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_request_aborts_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Request Error", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 148, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 4}, "hiddenSeries": false, "id": 158, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb, group, version, resource, scope)(\n          irate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb=~\"LIST\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}} {{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "List Latency in APIServer P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 4}, "hiddenSeries": false, "id": 150, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, operation, type)(\n          irate(etcd_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", operation=~\"list|listWithCount|get\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "List  Latency in Etcd P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 11}, "hiddenSeries": false, "id": 223, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, filter)(\n          irate(apiserver_request_filter_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{filter}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Filter Chan Latency P99 (>=1.20)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 11}, "hiddenSeries": false, "id": 156, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "AVG(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource!=\"events\"}) by (resource)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Etcd Object Counts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 166, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", verb=~\"LIST|WATCH\"}[1m])) by (verb, resource)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}} {{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "List/Watch QPS in APIServer", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 162, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(etcd_request_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", operation=~\"list|listwithcount\"}[1m])) by (operation, type)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "List QPS in Etcd", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 25}, "id": 155, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Latency"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb, group, version, resource, scope)(\n          irate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb=~\"LIST\"}[1m]))\n        )", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{resource}}", "refId": "E"}], "title": "List  Latency P99 in APIServer", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["group", "resource", "scope", "version", "Value", "verb"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 5, "group": 1, "resource": 3, "scope": 4, "verb": 0, "version": 2}, "renameByName": {"Value": "Latency", "group": ""}}}], "type": "table"}], "title": "List Latency", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 140, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 5}, "hiddenSeries": false, "id": 170, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb, group, version, resource, scope)(\n          irate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb!~\"LIST|GET|WATCH|CONNECT\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}} {{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mutating Latency in APIServer P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 5}, "hiddenSeries": false, "id": 171, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, operation, type)(\n          irate(etcd_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", operation!~\"get|list|listWithCount\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mutating Latency in Etcd P99 (Max=60s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 194, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, filter)(\n          irate(apiserver_request_filter_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{filter}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Filter Chan Latency P99 (>=1.20)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 193, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name)(\n         irate(apiserver_admission_controller_admission_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{filter}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Latency P99 (Max=2.5s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 19}, "hiddenSeries": false, "id": 172, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", verb!~\"LIST|GET|WATCH|CONNECT\"}[1m])) by (verb, resource)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}} {{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mutating QPS in APIServer", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 19}, "hiddenSeries": false, "id": 173, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(etcd_request_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", operation!~\"list|listwithcount\"}[1m])) by (operation, type)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mutating QPS in Etcd", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 26}, "id": 169, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Latency"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, verb, group, version, resource, scope)(\n          irate(apiserver_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", verb!~\"LIST|GET|WATCH|CONNECT\"}[1m]))\n        )", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "{{resource}}", "refId": "E"}], "title": "Mutating Each Objects Latency P99 in APIServer", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["group", "resource", "scope", "version", "Value", "verb"]}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {"Value": 5, "group": 1, "resource": 3, "scope": 4, "verb": 0, "version": 2}, "renameByName": {"Value": "Latency", "group": ""}}}], "type": "table"}], "title": "Mutating Latency", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 136, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 6}, "id": 207, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(system_n_cpus{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "SUM(Master CPU Cores)", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 6}, "hiddenSeries": false, "id": 203, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "cpu_usage_active{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", cpu=\"cpu-total\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "cpu_usage_active (Same as top %CPU)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 6}, "hiddenSeries": false, "id": 211, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "sort": "current", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "cpu_usage_iowait{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", cpu=\"cpu-total\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "cpu_usage_iowait", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 12}, "id": 208, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(mem_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "SUM(Master MEM Bytes)", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 12}, "hiddenSeries": false, "id": 212, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "mem_used_percent{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "mem_used_percent  (Same as top %MEM)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 12}, "hiddenSeries": false, "id": 210, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "mem_used{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "mem_used", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 215, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(procstat_cpu_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (process_name)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{process_name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_cpu_usage (Same as top %CPU)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 216, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(procstat_memory_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (process_name)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{process_name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_memory_usage (Same as top %MEM)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 24}, "hiddenSeries": false, "id": 240, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "procstat_num_fds{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}-{{process_name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_num_fds (Same as lsof)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Master Load", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 243, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 17}, "id": 200, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "MAX(procstat_cpu_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "MAX(APServer top CPU%)", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 17}, "hiddenSeries": false, "id": 180, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "procstat_cpu_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_cpu_usage (Same as top CPU%)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 17}, "hiddenSeries": false, "id": 220, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "procstat_cpu_time_iowait{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_cpu_time_iowait", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0%", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 23}, "id": 204, "links": [], "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "MAX(procstat_memory_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "MAX(APIServer top MEM%)", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 23}, "hiddenSeries": false, "id": 181, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "procstat_memory_usage{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_memory_usage (Same as  top %MEM)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percent", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 23}, "hiddenSeries": false, "id": 221, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "procstat_memory_rss{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"apiserver\"}", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_memory_rss", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 29}, "hiddenSeries": false, "id": 217, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "AVG(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource!=\"events\"}) by (resource)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Etcd Object Counts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 29}, "hiddenSeries": false, "id": 218, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (verb)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "API Objects QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 29}, "hiddenSeries": false, "id": 219, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_response_sizes_sum{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[3m])) by (verb)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_response_sizes_sum", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "APIServer Load", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 190, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 8}, "hiddenSeries": false, "id": 241, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(MAX(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource!=\"events\"}) by (resource))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "sum", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "SUM(Etcd Object Counts)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 191, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "AVG(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource!=\"events\"}) by (resource)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{resource}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Etcd Object Counts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 15}, "id": 192, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Value"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "MAX(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource!=\"events\"}) by (resource)", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "title": "Etcd Object Counts", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["resource", "Value"]}}}], "type": "table"}], "title": "Etcd Object Count", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 226, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 231, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "Total", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total QPS in APIServer", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 232, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_request_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (verb, scope, group, version, resource)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}  {{group}}/{{version}}/{{resource}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Object QPS in APIServer", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 230, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(etcd_request_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "Etcd QPS", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total QPS in Etcd", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 227, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(etcd_request_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (operation, type)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Object QPS in Etcd", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 228, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, operation, type)(\n          irate(etcd_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", operation=~\"list|listWithCount|get\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "List  Latency in Etcd P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 23}, "hiddenSeries": false, "id": 229, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, operation, type)(\n          irate(etcd_request_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", operation!~\"get|list|listWithCount\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{operation}} {{type}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Mutating Latency in Etcd P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "APIServer To ETCD Request", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["master", "apiserver"], "templating": {"list": [{"current": {"selected": false, "text": "prometheus-cce-87et03rc", "value": "prometheus-cce-87et03rc"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bd", "value": "bd"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(apiserver_audit_level_total, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-87et03rc", "value": "cce-87et03rc"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\",clusterID=\"$clusterID\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\",clusterID=\"$clusterID\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Master / APIServer / Request", "uid": "sLeWHE87z-apiserver-slo", "version": 1}