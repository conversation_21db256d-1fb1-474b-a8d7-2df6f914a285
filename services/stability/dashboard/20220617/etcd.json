{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "description": "Etcd Dashboard", "editable": true, "gnetId": 3070, "graphTooltip": 0, "id": 16, "iteration": 1655447056768, "links": [], "panels": [{"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 137, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "string"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 0, "y": 1}, "id": 187, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^cluster_version$/", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "etcd_cluster_version{region=\"$region\", clusterID=\"$clusterID\"}", "format": "table", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Version", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 3, "y": 1}, "id": 46, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(etcd_server_id{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Up", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"from": "", "id": 1, "text": "YES", "to": "", "type": 1, "value": "1"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 6, "y": 1}, "id": 156, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "MAX(etcd_server_has_leader{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Has Leader", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 9, "y": 1}, "id": 79, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "MAX(etcd_debugging_mvcc_keys_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Keys", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 12, "y": 1}, "id": 169, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "AVG(system_n_cpus{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"})", "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "CPU", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 15, "y": 1}, "id": 171, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "AVG(mem_total{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"})", "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "MEM", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 18, "y": 1}, "id": 205, "options": {"frameIndex": 0, "showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "etcd_server_id{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ETCD Server ID", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instance", "server_id"]}}}], "type": "table"}], "title": "Overview", "type": "row"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 94, "panels": [], "title": "SLO", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"0": {"text": "NO"}, "1": {"text": "YES"}}, "text": "YES", "type": 1, "value": "1"}], "thresholds": {"mode": "absolute", "steps": [{"color": "rgba(245, 54, 54, 0.9)", "value": null}, {"color": "rgba(237, 129, 40, 0.89)", "value": 0}, {"color": "rgba(50, 172, 45, 0.97)", "value": 1}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 2}, "id": 44, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "max(etcd_server_has_leader{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "A", "step": 600}], "title": "has_leader", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 6, "x": 6, "y": 2}, "hiddenSeries": false, "id": 78, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus", "uid": "HofE91I7z"}, "exemplar": true, "expr": "etcd_server_is_leader{clusterID=\"$clusterID\"}", "hide": false, "interval": "", "legendFormat": "{{instance}} ", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Leader", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 2}, "hiddenSeries": false, "id": 185, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, instance, To)(\n          irate(etcd_network_peer_round_trip_time_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}->{{To}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_round_trip_time_seconds P99 (MAX=3.28s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 9}, "hiddenSeries": false, "id": 209, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_slow_apply_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}} ", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_slow_apply_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 9}, "hiddenSeries": false, "id": 107, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, instance)(\n          irate(etcd_disk_backend_commit_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "db_fsync  {{instance}} ", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_backend_commit_duration_seconds P99 (<10ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 9}, "hiddenSeries": false, "id": 148, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, instance)(\n          irate(etcd_disk_wal_fsync_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "wal_fsync {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_wal_fsync_duration_seconds P99 (MAX=8.19s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 15}, "id": 158, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 3}, "hiddenSeries": false, "id": 161, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_server_is_leader{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_is_leader", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 3}, "hiddenSeries": false, "id": 162, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"exemplar": true, "expr": "irate(etcd_server_leader_changes_seen_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_leader_changes_seen_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 3}, "hiddenSeries": false, "id": 163, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_heartbeat_send_failures_total{region=\"$region\", clusterID=\"$clusterID\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_heartbeat_send_failures_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 9}, "id": 232, "options": {"frameIndex": 0, "showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "etcd_server_id{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ETCD Server ID", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instance", "server_id"]}}}], "type": "table"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "--heartbeat-interval 通常是 round_trip_time 的 0.5~1.5 倍\n----election-timeout 通常是 round_trip_time 的 10 倍", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 16, "x": 8, "y": 9}, "hiddenSeries": false, "id": 231, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, instance, To)(\n          irate(etcd_network_peer_round_trip_time_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}->{{To}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_round_trip_time_seconds P99 (MAX=3.28s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 16}, "hiddenSeries": false, "id": 202, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_network_active_peers{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{Local}} -> {{Remote}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_active_peers", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 204, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_disconnected_peers_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{Local}} -> {{Remote}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_disconnected_peers_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Raft Leader Election", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 230, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Follow 比 Leader 的 commited 数少很多，而且差距越来越大，证明有问题", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 234, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_proposals_committed_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_proposals_committed_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "Commited 和 Applied 的数量不会差很多，大概几千之类合理。二者的差距越来越大，表示负载有问题。", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 23}, "hiddenSeries": false, "id": 166, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_proposals_applied_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_proposals_applied_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 29}, "hiddenSeries": false, "id": 167, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_server_proposals_pending{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_proposals_pending", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 29}, "hiddenSeries": false, "id": 168, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_proposals_failed_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_proposals_failed_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Raft Replicate Log", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 122, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 5}, "hiddenSeries": false, "id": 111, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_debugging_mvcc_keys_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}} keys", "refId": "A", "step": 30}, {"exemplar": true, "expr": "sum(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance }} objects", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_debugging_mvcc_keys_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 5}, "hiddenSeries": false, "id": 113, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (resource)", "hide": false, "interval": "", "legendFormat": "{{resource}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_object_counts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 227, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_lease_object_counts_sum{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_lease_object_counts_sum", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 228, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_debugging_server_lease_expired_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_debugging_server_lease_expired_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Keys", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 96, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 25}, "hiddenSeries": false, "id": 150, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_debugging_mvcc_keys_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}} keys", "refId": "A", "step": 30}, {"exemplar": true, "expr": "sum(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance }} objects", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_debugging_mvcc_keys_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 25}, "hiddenSeries": false, "id": 151, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (resource)", "hide": false, "interval": "", "legendFormat": "{{resource}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_object_counts", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 31}, "hiddenSeries": false, "id": 97, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, instance)(\n          irate(etcd_disk_wal_fsync_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "wal_fsync {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_wal_fsync_duration_seconds P99 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 31}, "hiddenSeries": false, "id": 235, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_disk_wal_fsync_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "wal_fsync {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_wal_fsync_duration_seconds P100 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 37}, "hiddenSeries": false, "id": 153, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_write_bytes{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}-write", "refId": "A", "step": 30}, {"exemplar": true, "expr": "max(irate(diskio_read_bytes{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "hide": true, "interval": "", "legendFormat": "{{name}}-read", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_write_bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 37}, "hiddenSeries": false, "id": 236, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_write_bytes{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "format": "time_series", "hide": true, "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}-write", "refId": "A", "step": 30}, {"exemplar": true, "expr": "max(irate(diskio_read_bytes{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "hide": false, "interval": "", "legendFormat": "{{name}}-read", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_read_bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 43}, "hiddenSeries": false, "id": 108, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_disk_wal_fsync_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_wal_fsync_duration_seconds_count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "percent"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 43}, "hiddenSeries": false, "id": 154, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "cpu_usage_iowait{region=~\"$region\", cpu=\"cpu-total\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "cpu_usage_iowait", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "WAL", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 104, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 149, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_disk_backend_commit_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}} DB fsync", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_backend_commit_duration_seconds P99 (<10ms)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "db_total_size 和 total_size_in_use 的差值可能是碎片导致，可以通过 defragmentation 优化", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 206, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_debugging_mvcc_db_total_size_in_bytes{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - allocated", "metric": "", "refId": "A", "step": 120}, {"exemplar": true, "expr": "etcd_mvcc_db_total_size_in_use_in_bytes{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}} - used", "refId": "B"}, {"exemplar": true, "expr": "etcd_server_quota_backend_bytes{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}} - quota", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_debugging_mvcc_db_total_size_in_bytes", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 27}, "hiddenSeries": false, "id": 123, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_debugging_disk_backend_commit_rebalance_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "disk_backend_commit_rebalance_duration_seconds P100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 27}, "hiddenSeries": false, "id": 125, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_debugging_disk_backend_commit_spill_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "disk_backend_commit_spill_duration_seconds P100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 27}, "hiddenSeries": false, "id": 126, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_debugging_disk_backend_commit_write_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "disk_backend_commit_write_duration_seconds P100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "BoltDB", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 173, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 8}, "id": 237, "options": {"frameIndex": 0, "showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "etcd_server_id{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "format": "table", "instant": true, "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "ETCD Server ID", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["instance", "server_id"]}}}], "type": "table"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "--heartbeat-interval 通常是 round_trip_time 的 0.5~1.5 倍\n----election-timeout 通常是 round_trip_time 的 10 倍", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 18, "x": 6, "y": 8}, "hiddenSeries": false, "id": 186, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, instance, To)(\n          irate(etcd_network_peer_round_trip_time_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}->{{To}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_round_trip_time_seconds P99 (MAX=3.28s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 15}, "hiddenSeries": false, "id": 179, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_client_grpc_sent_bytes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "", "refId": "A", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_client_grpc_sent_bytes_total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 15}, "hiddenSeries": false, "id": 182, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_client_grpc_received_bytes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "metric": "", "refId": "A", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_client_grpc_received_bytes_total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 21}, "hiddenSeries": false, "id": 174, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_peer_sent_bytes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - >{{To}}", "metric": "", "refId": "A", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_sent_bytes_total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2349", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:2350", "format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 21}, "hiddenSeries": false, "id": 176, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_peer_received_bytes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": " {{From}} -> {{instance}} ", "metric": "", "refId": "A", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_received_bytes_total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 245, "interval": "", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(irate(net_bytes_sent{region=~\"$region\",clusterID=~\"$clusterID\",instance=~\"$instance\"}[$__rate_interval])) by (interface)", "hide": false, "interval": "", "legendFormat": "{{interface}}", "refId": "H"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "net_bytes_sent", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2302", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:2303", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 243, "interval": "", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(irate(net_bytes_recv{region=\"$region\",clusterID=\"$clusterID\",instance=~\"$instance\"}[$__rate_interval])) by (interface)", "hide": false, "instant": false, "interval": "", "legendFormat": "{{interface}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "net_bytes_recv", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2302", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:2303", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 247, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "netstat_tcp_established{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "Established", "refId": "A"}, {"exemplar": true, "expr": "netstat_tcp_listen{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "listen", "refId": "H"}, {"exemplar": true, "expr": "netstat_tcp_close{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "close", "refId": "B"}, {"exemplar": true, "expr": "netstat_tcp_close_wait{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "close_wait", "refId": "C"}, {"exemplar": true, "expr": "netstat_tcp_closing{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "closing", "refId": "D"}, {"exemplar": true, "expr": "netstat_tcp_fin_wait1{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "fin_wait1", "refId": "E"}, {"exemplar": true, "expr": "netstat_tcp_fin_wait2{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "fin_wait2", "refId": "F"}, {"exemplar": true, "expr": "netstat_tcp_last_ack{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "last_ack", "refId": "G"}, {"exemplar": true, "expr": "netstat_tcp_time_wait{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "time_wait", "refId": "I"}, {"exemplar": true, "expr": "netstat_tcp_syn_recv{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "SyncRecv", "refId": "J"}, {"exemplar": true, "expr": "netstat_tcp_syn_sent{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "SyncSent", "refId": "K"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TCP Conn Stats", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:979", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:980", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 249, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "net_drop_in{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "drop_in", "refId": "B"}, {"exemplar": true, "expr": "net_drop_out{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "drop_out", "refId": "C"}, {"exemplar": true, "expr": "net_err_in{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "err_in", "refId": "D"}, {"exemplar": true, "expr": "net_err_out{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "err_out", "refId": "E"}, {"exemplar": true, "expr": "net_udp_rcvbuferrors{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "udp_rcvbuferrors", "refId": "I"}, {"exemplar": true, "expr": "net_udp_sndbuferrors{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "udp_sndbuferrors", "refId": "J"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Error", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 39}, "hiddenSeries": false, "id": 177, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_peer_sent_failures_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}} - >{{To}}", "metric": "", "refId": "A", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_sent_failures_total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "editable": true, "error": false, "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 0, "fillGradient": 0, "grid": {}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 39}, "hiddenSeries": false, "id": 178, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_peer_received_failures_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": " {{From}} -> {{instance}} ", "metric": "", "refId": "A", "step": 120}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_peer_received_failures_total", "tooltip": {"msResolution": false, "shared": true, "sort": 0, "value_type": "cumulative"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": false}], "yaxis": {"align": false}}], "title": "Network", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 189, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 9}, "hiddenSeries": false, "id": 192, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_snap_fsync_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "snap_db_fsync {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_snap_fsync_duration_second P100 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 9}, "hiddenSeries": false, "id": 190, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_snap_db_fsync_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "snap_db_fsync {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_snap_db_fsync_duration_seconds P100 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 9}, "hiddenSeries": false, "id": 191, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_snap_db_save_total_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "snap_db_fsync {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_snap_db_save_total_duration_seconds P100 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 15}, "hiddenSeries": false, "id": 193, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_snapshot_send_success{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])\n", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_snapshot_send_success", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 15}, "hiddenSeries": false, "id": 194, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_snapshot_send_inflights_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_snapshot_send_inflights_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 15}, "hiddenSeries": false, "id": 196, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_network_snapshot_send_total_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_snapshot_send_total_duration_seconds P100 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 21}, "hiddenSeries": false, "id": 197, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_snapshot_receive_success{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])\n", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_snapshot_receive_success", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 21}, "hiddenSeries": false, "id": 198, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_network_snapshot_receive_inflights_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_snapshot_send_inflights_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 21}, "hiddenSeries": false, "id": 199, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_network_snapshot_receive_total_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_network_snapshot_receive_total_duration_seconds P100 （MAX=8.19s）", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Snapshot", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 211, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 10}, "hiddenSeries": false, "id": 212, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_disk_backend_defrag_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}} DB fsync", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_disk_backend_defrag_duration_seconds P100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Defragmentation", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 214, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 11}, "hiddenSeries": false, "id": 215, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_mvcc_hash_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_mvcc_hash_duration_seconds P100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 11}, "hiddenSeries": false, "id": 216, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          1,\n          sum by (le, instance)(\n          irate(etcd_mvcc_hash_rev_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))\n        )", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": " {{instance}}", "refId": "A", "step": 30}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_mvcc_hash_rev_duration_seconds P100", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:115", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:116", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Slow Disk", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 218, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 12}, "hiddenSeries": false, "id": 233, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_server_id{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Up", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 219, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_health_success{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_health_success", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 220, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_health_failures{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_health_failures", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 24}, "hiddenSeries": false, "id": 221, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_read_indexes_failed_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": true, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}, {"exemplar": true, "expr": "etcd_server_read_indexes_failed_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_read_indexes_failed_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 222, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(etcd_server_slow_read_indexes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": true, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}, {"exemplar": true, "expr": "etcd_server_slow_read_indexes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_slow_read_indexes_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Server", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 224, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 13}, "hiddenSeries": false, "id": 225, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_server_is_learner{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_is_learner", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 13}, "hiddenSeries": false, "id": 226, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "etcd_server_learner_promote_successes{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "etcd_server_learner_promote_successes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:314", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:315", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "<PERSON><PERSON>", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["master", "etcd"], "templating": {"list": [{"current": {"selected": false, "text": "prometheus-cce-87et03rc", "value": "prometheus-cce-87et03rc"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bd", "value": "bd"}, "datasource": "${datasource}", "definition": "label_values(etcd_server_has_leader, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(etcd_server_has_leader, region)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-87et03rc", "value": "cce-87et03rc"}, "datasource": "${datasource}", "definition": "label_values(etcd_server_has_leader{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "ClusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(etcd_server_has_leader{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "${datasource}", "definition": "label_values(etcd_server_has_leader{region=\"$region\", clusterID=\"$clusterID\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(etcd_server_has_leader{region=\"$region\", clusterID=\"$clusterID\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "browser", "title": "Master / ETCD", "uid": "QwGQHxI7z-etcd", "version": 1}