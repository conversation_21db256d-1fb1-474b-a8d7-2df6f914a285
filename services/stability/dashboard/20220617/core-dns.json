{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "A dashboard for the CoreDNS DNS server with updated metrics for version 1.7.0+.  Based on the CoreDNS 1.7.0+ dashboard by <PERSON><PERSON><PERSON>", "editable": true, "gnetId": 14981, "graphTooltip": 0, "id": 164, "iteration": 1655874156409, "links": [{"$$hashKey": "object:94", "icon": "external link", "tags": [], "targetBlank": true, "title": "CoreDNS.io", "type": "link", "url": "https://coredns.io"}], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 44, "panels": [], "title": "Overview", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 0, "y": 1}, "id": 39, "links": [], "options": {"displayLabels": ["percent"], "legend": {"displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_dns_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{instance}}", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Requests (by instance)", "type": "piechart"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 6, "y": 1}, "id": 167, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_forward_max_concurrent_rejects_total{region=\"$region\", clusterID=\"$clusterID\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Upstream Rejected Queries", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 11, "y": 1}, "id": 35, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_forward_healthcheck_broken_total{region=\"$region\", clusterID=\"$clusterID\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Upstream Health Check Fails", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 16, "y": 1}, "id": 81, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_panics_total{region=\"$region\", clusterID=\"$clusterID\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Panics", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 20, "y": 1}, "id": 92, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_reload_failed_total{region=\"$region\", clusterID=\"$clusterID\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Failed Reloads", "type": "stat"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 41, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "super-light-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 9}, "id": 152, "links": [], "maxPerRow": 2, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "name"}, "pluginVersion": "7.5.0", "repeat": null, "repeatDirection": "v", "scopedVars": {"instance": {"selected": true, "text": "********:9153", "value": "********:9153"}}, "targets": [{"exemplar": true, "expr": "coredns_build_info{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "{{version}}", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Version", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 6, "y": 9}, "id": 36, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "scopedVars": {"instance": {"selected": true, "text": "********:9153", "value": "********:9153"}}, "targets": [{"exemplar": true, "expr": "sum(irate(coredns_forward_max_concurrent_rejects_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "title": "Upstream Rejected Queries", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 5, "x": 11, "y": 9}, "id": 165, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "scopedVars": {"instance": {"selected": true, "text": "********:9153", "value": "********:9153"}}, "targets": [{"exemplar": true, "expr": "sum(irate(coredns_forward_healthcheck_broken_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Upstream Health Check Fails", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 16, "y": 9}, "id": 168, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "scopedVars": {"instance": {"selected": true, "text": "********:9153", "value": "********:9153"}}, "targets": [{"exemplar": true, "expr": "sum(irate(coredns_panics_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Panics", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 4, "x": 20, "y": 9}, "id": 169, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "scopedVars": {"instance": {"selected": true, "text": "********:9153", "value": "********:9153"}}, "targets": [{"exemplar": true, "expr": "sum(irate(coredns_reload_failed_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))", "interval": "", "legendFormat": "{{instance}}", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Failed Reloads", "type": "stat"}], "repeat": "instance", "scopedVars": {"instance": {"selected": true, "text": "********:9153", "value": "********:9153"}}, "title": "Health: $instance", "type": "row"}, {"collapsed": true, "datasource": "cce-vm-cluster", "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 26, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 10}, "id": 2, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_dns_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (server)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{server}}", "refId": "A", "step": 60}, {"exemplar": true, "expr": "sum(irate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))", "hide": false, "interval": "", "legendFormat": "cache", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Requests (total)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 10}, "id": 6, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_dns_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (zone)", "interval": "", "intervalFactor": 2, "legendFormat": "{{zone}}", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Requests (by zone)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 17}, "id": 32, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(irate(coredns_dns_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.90, sum(irate(coredns_dns_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "90%", "refId": "B", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.50, sum(irate(coredns_dns_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "50%", "refId": "C", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Responses (latency, internet zone)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 17}, "id": 4, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_dns_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (type)", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Requests (by type)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 24}, "id": 24, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=\"success\"}[$__rate_interval])) / sum(irate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "hits: success", "refId": "A", "step": 40}, {"exemplar": true, "expr": "sum(rate(coredns_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=\"denial\"}[5m])) / sum(rate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\",instance=~\"$instance\"}[5m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "hits: denial", "refId": "B", "step": 40}, {"exemplar": true, "expr": "(sum(rate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) - sum(rate(coredns_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\",instance=~\"$instance\", type=\"success\"}[5m]))) / sum(rate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))", "hide": false, "interval": "", "legendFormat": "misses", "refId": "C"}, {"exemplar": true, "expr": "sum(rate(coredns_dnssec_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) / sum(rate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))", "hide": false, "interval": "", "legendFormat": "hits: DNSSEC", "refId": "D"}, {"exemplar": true, "expr": "(sum(rate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) - sum(rate(coredns_dnssec_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))) / sum(rate(coredns_cache_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))", "hide": false, "interval": "", "legendFormat": "misses: DNSSEC", "refId": "E"}], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON> (hitrate)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 24}, "id": 8, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_dns_do_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (zone)", "interval": "", "intervalFactor": 2, "legendFormat": "{{zone}}", "refId": "A", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Requests (DNSSEC by zone)", "type": "timeseries"}, {"cacheTimeout": null, "datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 31}, "id": 14, "interval": null, "links": [], "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "values": ["value", "percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(irate(coredns_dns_responses_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (rcode)", "interval": "", "intervalFactor": 2, "legendFormat": "{{rcode}}", "refId": "A", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Responses (by code)", "type": "piechart"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 31}, "id": 18, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(irate(coredns_dns_request_size_bytes_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "interval": "", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.90, sum(irate(coredns_dns_request_size_bytes_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "90%", "refId": "B", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.50, sum(irate(coredns_dns_request_size_bytes_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "50%", "metric": "", "refId": "C", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Requests (size, internet zone)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "decbytes"}, "overrides": []}, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 31}, "id": 33, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(irate(coredns_dns_response_size_bytes_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "interval": "", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.90, sum(rate(coredns_dns_response_size_bytes_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[5m])) by (le))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "90%", "refId": "B", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.50, sum(irate(coredns_dns_response_size_bytes_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", zone=\".\"}[$__rate_interval])) by (le))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "50%", "metric": "", "refId": "C", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Responses (size, internet zone)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 38}, "id": 22, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(coredns_cache_entries{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}) by (type)", "interval": "", "intervalFactor": 2, "legendFormat": "{{type}}", "refId": "A", "step": 40}, {"exemplar": true, "expr": "sum(coredns_dnssec_cache_entries{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "hide": false, "interval": "", "legendFormat": "DNSSEC", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Cache (size)", "type": "stat"}], "repeat": null, "title": "Local", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 63, "panels": [{"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "reqps"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 11}, "id": 72, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(rate(coredns_forward_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "upstream", "refId": "A", "step": 60}], "timeFrom": null, "timeShift": null, "title": "Requests (total)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 11}, "id": 38, "links": [], "maxPerRow": 6, "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "repeat": null, "repeatDirection": "h", "targets": [{"exemplar": true, "expr": "sum(rate(coredns_forward_conn_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) / sum(rate(coredns_forward_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "hits", "refId": "A", "step": 40}, {"exemplar": true, "expr": "(sum(rate(coredns_forward_requests_total{region=\"$region\", clusterID=\"$clusterID\",  instance=~\"$instance\"}[5m])) - sum(rate(coredns_forward_conn_cache_hits_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))) / sum(rate(coredns_forward_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m]))", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "misses", "refId": "B", "step": 40}], "timeFrom": null, "timeShift": null, "title": "<PERSON><PERSON> (hitrate)", "type": "timeseries"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"graph": false, "legend": false, "tooltip": false}, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 18}, "id": 37, "links": [], "options": {"graph": {}, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltipOptions": {"mode": "multi"}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99, sum(rate(coredns_forward_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "99%", "refId": "A", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.90, sum(rate(coredns_forward_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "90%", "refId": "B", "step": 40}, {"exemplar": true, "expr": "histogram_quantile(0.50, sum(rate(coredns_forward_request_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) by (le))", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "50%", "refId": "C", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Responses (latency)", "type": "timeseries"}, {"cacheTimeout": null, "datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 12, "y": 18}, "id": 105, "interval": null, "links": [], "options": {"displayLabels": ["percent"], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "values": ["value"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(rate(coredns_forward_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) by (to)", "interval": "", "intervalFactor": 2, "legendFormat": "{{to}}", "refId": "A", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Requests (by upstream)", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["Time", "1.0.0.1:853", "1.1.1.1:853", "8.8.4.4:853", "8.8.8.8:853"]}}}], "type": "piechart"}, {"cacheTimeout": null, "datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 6, "x": 18, "y": 18}, "id": 53, "interval": null, "links": [], "options": {"displayLabels": [], "legend": {"calcs": [], "displayMode": "table", "placement": "right", "values": ["value", "percent"]}, "pieType": "pie", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}}, "pluginVersion": "7.5.6", "targets": [{"exemplar": true, "expr": "sum(rate(coredns_forward_responses_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[5m])) by (rcode)", "interval": "", "intervalFactor": 2, "legendFormat": "{{rcode}}", "refId": "A", "step": 40}], "timeFrom": null, "timeShift": null, "title": "Responses (by code)", "type": "piechart"}], "title": "Upstream", "type": "row"}], "refresh": "5s", "schemaVersion": 27, "style": "dark", "tags": ["dns", "coredns"], "templating": {"list": [{"current": {"selected": true, "text": "gztest-cce-bdcc9r2t", "value": "gztest-cce-bdcc9r2t"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": ".*", "current": {"selected": false, "text": "gztest", "value": "gztest"}, "datasource": "${datasource}", "definition": "label_values(coredns_build_info, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(coredns_build_info, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 3, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-bdcc9r2t", "value": "cce-bdcc9r2t"}, "datasource": "${datasource}", "definition": "label_values(coredns_build_info{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [{"selected": true, "text": "cce-bdcc9r2t", "value": "cce-bdcc9r2t"}], "query": {"query": "label_values(coredns_build_info{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "text": "********:9153", "value": "********:9153"}, "datasource": "${datasource}", "definition": "label_values(coredns_build_info{region=\"$region\", clusterID=\"$clusterID\"},  instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "instance", "multi": false, "name": "instance", "options": [{"selected": false, "text": "All", "value": "$__all"}, {"selected": true, "text": "********:9153", "value": "********:9153"}, {"selected": false, "text": "********:9153", "value": "********:9153"}, {"selected": false, "text": "********:9153", "value": "********:9153"}], "query": {"query": "label_values(coredns_build_info{region=\"$region\", clusterID=\"$clusterID\"},  instance)", "refId": "StandardVariableQuery"}, "refresh": 0, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Plugin / CoreDNS", "uid": "wY4blRMGz", "version": 16}