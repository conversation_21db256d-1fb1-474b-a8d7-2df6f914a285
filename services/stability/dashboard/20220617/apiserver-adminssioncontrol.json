{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 13, "iteration": 1655447100468, "links": [], "panels": [{"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 249, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 1}, "id": 286, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "COUNT(SUM(apiserver_admission_controller_admission_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}) by (name))", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Enabled Admission Controller", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 1}, "id": 285, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\", rejected=\"true\"}[1m]))", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Admission Controller Rejected", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 1}, "id": 259, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "MAX(histogram_quantile (\n          0.99,\n          sum by (le, name)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))\n        ))", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "MAX(Admission Controller Latency)", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 0, "y": 7}, "id": 260, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_admission_webhook_rejection_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>ok Rejected Count", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 8, "y": 7}, "id": 261, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "MAX(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource=~\"mutatingwebhookconfigurations.*\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "MutatingWebhookConfig", "transformations": [], "type": "stat"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 8, "x": 16, "y": 7}, "id": 262, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "MAX(etcd_object_counts{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", resource=~\"validatingwebhookconfigurations.*\"})", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "ValidatingWebhookConfig", "transformations": [], "type": "stat"}], "title": "Overview", "type": "row"}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 297, "panels": [], "title": "SLO", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 2}, "hiddenSeries": false, "id": 298, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", rejected=\"true\"}[1m])) by (rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "rejected={{rejected}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers Rejected", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 2}, "hiddenSeries": false, "id": 303, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le,name)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers Latency P99 (Max=2.5s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 302, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 3}, "hiddenSeries": false, "id": 304, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\", rejected=\"true\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers Rejected", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 3}, "hiddenSeries": false, "id": 305, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_webhook_admission_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", rejected=\"true\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_webhook Rejected", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Admission Controller Rejected", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 10}, "id": 307, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 4}, "hiddenSeries": false, "id": 308, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le,name)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers Latency P99 (Max=2.5s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 4}, "hiddenSeries": false, "id": 288, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejected)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\",type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_controller  Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 4}, "hiddenSeries": false, "id": 310, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejetcd)(\n          irate(apiserver_admission_webhook_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_webhook Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Admission Controller Latency", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 11}, "id": 277, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 14, "w": 5, "x": 0, "y": 5}, "id": 295, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "QPS"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(apiserver_admission_controller_admission_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}) by (name)", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}, {"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (name)", "format": "table", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Enabled Admission Controller", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["name"]}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 10, "x": 5, "y": 5}, "hiddenSeries": false, "id": 235, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m])) by (name)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 15, "y": 5}, "hiddenSeries": false, "id": 224, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le,name)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{name}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 10, "x": 5, "y": 12}, "hiddenSeries": false, "id": 270, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers QPS Detail", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 9, "x": 15, "y": 12}, "hiddenSeries": false, "id": 280, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejected)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Admission Controllers Latency P99 Detail", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "QPS"}]}]}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 19}, "id": 282, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "rejected"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m])) by (name, type, operation, rejected)", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}, {"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (name)", "format": "table", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Admission Controller QPS", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["name", "Value #E", "operation", "rejected", "type"]}}}], "type": "table"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 19}, "id": 283, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejected)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=~\"$type\"}[1m]))\n        )", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}, {"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (name)", "format": "table", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "Admission Controller Latency", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["Value #E", "name", "operation", "rejected", "type"]}}}], "type": "table"}], "title": "Admission Controllers", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 12}, "id": 279, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "QPS"}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 13}, "id": 313, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "rejected"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "kube_mutatingwebhookconfiguration_info{region=\"$region\", clusterID=\"$clusterID\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "title": "kube_mutatingwebhookconfiguration_info", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["clusterID", "mutatingwebhookconfiguration"]}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 287, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", name=\"MutatingAdmissionWebhook\", type=~\"$type\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_controller  QPS (name=MutatingAdmissionWebhook)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 309, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejected)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", name=\"MutatingAdmissionWebhook\", type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_controller  Latency P99 (name=MutatingAdmissionWebhook)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 236, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_webhook_admission_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=\"admit\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_webhook QPS (type=mutating)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 233, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejetcd)(\n          irate(apiserver_admission_webhook_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=\"admit\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_webhook Latency P99 (type=mutating)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Mutating Admission Webhook", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 290, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "transparent", "value": null}]}, "unit": "ops"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #E"}, "properties": [{"id": "displayName", "value": "QPS"}]}]}, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 14}, "id": 314, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "rejected"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "kube_validatingwebhookconfiguration_info{region=\"$region\", clusterID=\"$clusterID\"}", "format": "table", "hide": false, "instant": true, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "title": "kube_validatingwebhookconfiguration_info", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["clusterID", "validatingwebhookconfiguration"]}}}], "type": "table"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 291, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\", name=\"ValidatingAdmissionWebhook\", type=~\"$type\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_controller  QPS (name=ValidatingAdmissionWebhook)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 20}, "hiddenSeries": false, "id": 292, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejected)(\n          irate(apiserver_admission_controller_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", name=\"ValidatingAdmissionWebhook\", type=~\"$type\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_controller  Latency P99 (name=ValidatingAdmissionWebhook)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 293, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(irate(apiserver_admission_webhook_admission_duration_seconds_count{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", type=\"validating\"}[1m])) by (name, type, operation, rejected)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_webhook QPS (type=validating)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 294, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, name, type, operation, rejetcd)(\n          irate(apiserver_admission_webhook_admission_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", type=\"validating\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "admission_webhook Latency P99 (type=validating)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Validating Admission Webhook", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["master", "apiserver"], "templating": {"list": [{"current": {"selected": false, "text": "prometheus-cce-87et03rc", "value": "prometheus-cce-87et03rc"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bd", "value": "bd"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(apiserver_audit_level_total, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-87et03rc", "value": "cce-87et03rc"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\",clusterID=\"$clusterID\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\",clusterID=\"$clusterID\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "admit", "value": "admit"}, "datasource": "${datasource}", "definition": "label_values(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\"}, type)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "type", "multi": false, "name": "type", "options": [], "query": {"query": "label_values(apiserver_admission_controller_admission_duration_seconds_count{clusterID=\"$clusterID\", instance=~\"$instance\"}, type)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Master / APIServer / AdmissionControl", "uid": "sLeWHE87z-apiserver-admission-control", "version": 1}