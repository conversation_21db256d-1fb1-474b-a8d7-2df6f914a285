{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 106, "iteration": 1654673696438, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 126, "panels": [], "title": "SLO", "type": "row"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 1}, "id": 121, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["last"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "sum(irate(apiserver_flowcontrol_rejected_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "FlowControl Rejected Request", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 18, "x": 6, "y": 1}, "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_flowcontrol_rejected_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))", "hide": false, "interval": "", "legendFormat": "rejected", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_flowcontrol_rejected_requests_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 6, "w": 6, "x": 0, "y": 7}, "id": 245, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "histogram_quantile (\n          0.99,\n          sum by (le)(\n          irate(apiserver_request_filter_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", filter=\"priorityandfairness\"}[1m]))\n        )", "format": "table", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "title": "PriorityAndFairness Latency", "transformations": [], "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 18, "x": 6, "y": 7}, "hiddenSeries": false, "id": 133, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, filter)(\n          irate(apiserver_request_filter_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", filter=\"priorityandfairness\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PriorityAndFairness Latency P99 (Max=5.0s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 13}, "id": 138, "panels": [], "title": "Request Rejected", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 246, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_flowcontrol_rejected_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (priority_level, flow_schema, reason)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_flowcontrol_rejected_requests_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 247, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_flowcontrol_dispatched_requests_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (priority_level, flow_schema)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_flowcontrol_dispatched_requests_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 21}, "hiddenSeries": false, "id": 250, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(apiserver_current_inflight_requests{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]) by ( request_kind)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_current_inflight_requests (Version <= 1.18)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 21}, "hiddenSeries": false, "id": 248, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_current_inqueue_requests{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by ( request_kind)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_current_inqueue_requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 21}, "hiddenSeries": false, "id": 253, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "sum(irate(apiserver_flowcontrol_current_inqueue_requests{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[1m])) by (priority_level, flow_schema)", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_flowcontrol_current_inqueue_requests", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 28}, "id": 252, "panels": [], "title": "FlowControl Latency", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 29}, "hiddenSeries": false, "id": 254, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, filter)(\n          irate(apiserver_request_filter_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\", filter=\"priorityandfairness\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "PriorityAndFairness Latency P99 (Max=5.0s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 35}, "hiddenSeries": false, "id": 255, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, priority_level, flow_schema)(\n          irate(apiserver_flowcontrol_request_wait_duration_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "APF request_wait_duration Latency P99 (Max=5.0s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 35}, "hiddenSeries": false, "id": 257, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, priority_level, flow_schema)(\n          irate(apiserver_flowcontrol_request_execution_seconds_bucket{clusterID=\"$clusterID\", instance=~\"$instance\"}[1m]))\n        )", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{verb}}", "refId": "E"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "APF request_execution_duration Latency P99 (Max=5.0s)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 42}, "id": 74, "panels": [], "title": "Concurrency Limit", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "displayName", "value": "Concurrency"}]}]}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 43}, "id": 93, "links": [], "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Concurrency"}]}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": false, "expr": "SUM(apiserver_flowcontrol_request_concurrency_limit{region=\"$region\", clusterID=\"$clusterID\"}) by (priority_level)", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "{{priority_level}}", "refId": "A"}], "title": "apiserver_flowcontrol_request_concurrency_limit", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["priority_level", "Value"]}}}], "type": "table"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["master", "apiserver"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bj", "value": "bj"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(apiserver_audit_level_total, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-b5plp7ss", "value": "cce-b5plp7ss"}, "datasource": "${datasource}", "definition": "label_values(apiserver_flowcontrol_request_concurrency_limit{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(apiserver_flowcontrol_request_concurrency_limit{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "${datasource}", "definition": "label_values(apiserver_flowcontrol_request_concurrency_limit{region=\"$region\", clusterID=\"$clusterID\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(apiserver_flowcontrol_request_concurrency_limit{region=\"$region\", clusterID=\"$clusterID\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-15m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Master / APIServer / FlowControl (>=1.20)", "uid": "sLeWHE87z-apiserver-apf-v2", "version": 13}