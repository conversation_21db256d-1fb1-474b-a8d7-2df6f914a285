{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 121, "iteration": 1654673747644, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 26, "panels": [], "title": "SLO", "type": "row"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 1}, "id": 59, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) ", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Node NotReady", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 4, "y": 1}, "hiddenSeries": false, "id": 55, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"}) by (node))", "hide": false, "instant": false, "interval": "", "legendFormat": "total", "refId": "A"}, {"exemplar": true, "expr": "count(count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 1) by (node)) ", "hide": false, "interval": "", "legendFormat": "ready", "refId": "B"}, {"exemplar": true, "expr": "count(count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) by (node)) ", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 14, "y": 1}, "hiddenSeries": false, "id": 48, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) by (node)", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node NotReady", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 6}, "id": 60, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 0)", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Pod NotReady", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 4, "y": 6}, "hiddenSeries": false, "id": 56, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "total", "refId": "A"}, {"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 1)", "hide": false, "interval": "", "legendFormat": "ready", "refId": "C"}, {"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 0)", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pod", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 14, "y": 6}, "hiddenSeries": false, "id": 97, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 0) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pod NotReady", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 11}, "id": 61, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\"}[$__rate_interval])) by (le)\n)", "hide": false, "instant": false, "interval": "", "legendFormat": "", "refId": "A"}], "timeFrom": null, "timeShift": null, "title": "Start Pod Latency P99", "type": "stat"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 4, "y": 11}, "hiddenSeries": false, "id": 54, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\"}[$__rate_interval])) by (le)\n)\n", "hide": false, "interval": "", "legendFormat": "pod_start_duration", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 14, "y": 11}, "hiddenSeries": false, "id": 98, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 35, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 17}, "hiddenSeries": false, "id": 77, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) by (node)", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node NotReady", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 23}, "hiddenSeries": false, "id": 63, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\", status=\"true\"}", "hide": false, "interval": "", "legendFormat": "{{condition}} ", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Conditions: $node", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [{"from": "", "id": 1, "text": "false", "to": "", "type": 1, "value": "0"}, {"from": "", "id": 2, "text": "true", "to": "", "type": 1, "value": "1"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "true"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 23}, "id": 62, "links": [], "maxDataPoints": 100, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "true"}]}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\", status=\"true\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Node Conditions: $node", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["condition", "node", "status", "Value #C"]}}}], "type": "table"}], "title": "Node Conditons", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 17}, "id": 65, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 69, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\", condition=\"Ready\", status=\"true\"}", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Ready", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 68, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_info{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\"}) by (node)", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pods on Node", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "custom.width", "value": 209}, {"id": "unit", "value": "dateTimeAsLocal"}, {"id": "displayName", "value": "create_time"}, {"id": "custom.filterable", "value": true}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 24}, "id": 31, "links": [], "maxDataPoints": 100, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "create_time"}]}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kube_pod_created{region=\"$region\", clusterID=\"$clusterID\"} * on(namespace, pod) group_left(node) kube_pod_info{region=\"$region\", clusterID=\"$clusterID\", node=~\"$node\"} * 1000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Pods  create_time on Node: $node", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["namespace", "node", "pod", "Value #B"]}}}], "type": "table"}], "title": "Node NotReady by <PERSON>ad", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 67, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 19}, "hiddenSeries": false, "id": 95, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pleg_relist_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pleg_relist_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 19}, "hiddenSeries": false, "id": 96, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pleg_relist_interval_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pleg_relist_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Node NotReady by PLEG", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 58, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 20}, "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 26}, "hiddenSeries": false, "id": 90, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_worker_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_worker_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 26}, "hiddenSeries": false, "id": 94, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(storage_operation_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "storage_operation_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 32}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_worker_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_worker_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 32}, "hiddenSeries": false, "id": 79, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_cgroup_manager_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=~\"create|update\"}[$__rate_interval])) by (le, instance,operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kube<PERSON>_cgroup_manager_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 38}, "hiddenSeries": false, "id": 81, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"run_podsandbox\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_runtime_operations_duration_seconds P99 run_podsandbox", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 44}, "hiddenSeries": false, "id": 86, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_network_plugin_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"set_up_pod\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_network_plugin_operations_duration_seconds P99 set_up_pod", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 50}, "hiddenSeries": false, "id": 92, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"pull_image\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " kubelet_runtime_operations_duration_seconds P99 pull_image", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 50}, "hiddenSeries": false, "id": 84, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"pull_image\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_docker_operations_duration_seconds P99 pull_image", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 57}, "hiddenSeries": false, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"create_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " kubelet_runtime_operations_duration_seconds P99 create_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 57}, "hiddenSeries": false, "id": 93, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"create_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_docker_operations_duration_seconds P99 create_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 64}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"start_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " kubelet_runtime_operations_duration_seconds P99 start_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 64}, "hiddenSeries": false, "id": 85, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"start_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_docker_operations_duration_seconds P99 start_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Pod Create Latency", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["node", "kubelet"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bd", "value": "bd"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(apiserver_audit_level_total, region)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-4ju1wo3n", "value": "cce-4ju1wo3n"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "************", "value": "************"}, "datasource": "${datasource}", "definition": "label_values(kube_node_status_condition{region=\"$region\",  clusterID=\"$clusterID\"}, node)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "node", "multi": false, "name": "node", "options": [], "query": {"query": "label_values(kube_node_status_condition{region=\"$region\",  clusterID=\"$clusterID\"}, node)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Node / Kubelet", "uid": "sLeWHE87zbbb", "version": 2}