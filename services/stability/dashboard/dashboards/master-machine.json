{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 37, "iteration": 1654673763141, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 37, "panels": [], "title": "Overview", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [{"options": {"": {"text": ""}}, "type": "value"}, {"options": {"match": "null", "result": {"text": "N/A"}}, "type": "special"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 0, "y": 1}, "id": 12, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "system_uptime{region=~\"$region\", clusterID=~\"$clusterID\",instance=~\"$instance\"}", "format": "time_series", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Uptime", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 4, "y": 1}, "id": 9, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "system_n_cpus{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "CPU", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 8, "y": 1}, "id": 33, "links": [], "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "SUM(mem_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "MEM", "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "", "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "dark-red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 12, "y": 1}, "id": 23, "links": [], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "100 - cpu_usage_idle{region=~\"$region\", cpu=\"cpu-total\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "CPU Usage", "type": "gauge"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "", "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "dark-red", "value": 80}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 16, "y": 1}, "id": 29, "links": [], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.0", "targets": [{"datasource": "${datasource}", "exemplar": true, "expr": "mem_used_percent{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "gauge"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "displayName": "", "mappings": [], "max": 100, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "#EAB839", "value": 70}, {"color": "dark-red", "value": 90}]}, "unit": "percent"}, "overrides": []}, "gridPos": {"h": 4, "w": 4, "x": 20, "y": 1}, "id": 10, "links": [], "options": {"orientation": "auto", "reduceOptions": {"calcs": ["mean"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "text": {}}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(disk_used_percent{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\",path=\"/\"})", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Disk usage", "type": "gauge"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 5}, "id": 39, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "id": 26, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "100 - cpu_usage_idle{region=~\"$region\", cpu=\"cpu-total\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "total", "refId": "A"}, {"exemplar": true, "expr": "cpu_usage_iowait{region=~\"$region\", cpu=\"cpu-total\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "iowait", "refId": "B"}, {"exemplar": true, "expr": "cpu_usage_user{region=~\"$region\", cpu=\"cpu-total\",clusterID=\"$clusterID\", instance=\"$instance\"}", "interval": "", "legendFormat": "usr", "refId": "C"}, {"exemplar": true, "expr": "cpu_usage_system{region=~\"$region\", cpu=\"cpu-total\",clusterID=\"$clusterID\", instance=\"$instance\"}", "interval": "", "legendFormat": "sys", "refId": "D"}, {"exemplar": true, "expr": "cpu_usage_softirq{region=~\"$region\", cpu=\"cpu-total\",clusterID=\"$clusterID\", instance=\"$instance\"}", "interval": "", "legendFormat": "softirq", "refId": "E"}, {"exemplar": true, "expr": "cpu_usage_irq{region=~\"$region\", cpu=\"cpu-total\",clusterID=\"$clusterID\", instance=\"$instance\"}", "interval": "", "legendFormat": "irq", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:146", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:147", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "hiddenSeries": false, "id": 25, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "procstat_cpu_usage{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "{{process_name}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_cpu_usage", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:479", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:480", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "CPU", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 6}, "id": 41, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "mem_total{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "legendFormat": "Total", "refId": "A"}, {"expr": "mem_free{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "legendFormat": "Free", "refId": "H"}, {"expr": "mem_available{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "legendFormat": "Available", "refId": "B"}, {"expr": "mem_buffered{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "legendFormat": "Buffered", "refId": "C"}, {"expr": "mem_cached{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "legendFormat": "<PERSON><PERSON><PERSON>", "refId": "D"}, {"expr": "mem_shared{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "legendFormat": "Shared", "refId": "E"}, {"expr": "mem_slab{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "legendFormat": "Slab", "refId": "F"}, {"expr": "mem_used{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "legendFormat": "Used", "refId": "G"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Mem", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:94", "format": "decbytes", "logBase": 1, "show": true}, {"$$hashKey": "object:95", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 14}, "hiddenSeries": false, "id": 22, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(procstat_memory_usage{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}) by (instance, process_name)", "hide": false, "interval": "", "legendFormat": "{{process_name}}", "refId": "G"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "procstat_memory_usage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:532", "format": "percent", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:533", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Memory", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 7}, "id": 43, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 22}, "hiddenSeries": false, "id": 48, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "cpu_usage_iowait{region=~\"$region\", cpu=\"cpu-total\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "iowait", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "cpu_usage_iowait", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:146", "format": "percent", "logBase": 1, "show": true}, {"$$hashKey": "object:147", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 22}, "hiddenSeries": false, "id": 47, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_io_time{region=~\"$region\",clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])/1000) by (name)", "interval": "", "legendFormat": "{{name}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_io_time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 28}, "hiddenSeries": false, "id": 50, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_write_bytes{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "interval": "", "legendFormat": "{{name}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_write_bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 28}, "hiddenSeries": false, "id": 49, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_read_bytes{region=~\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "interval": "", "legendFormat": "{{name}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_read_bytes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "binBps", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 34}, "hiddenSeries": false, "id": 56, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_writes{region=~\"$region\",clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "interval": "", "legendFormat": "{{name}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_writes", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 34}, "hiddenSeries": false, "id": 57, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max(irate(diskio_reads{region=~\"$region\",clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (name)", "interval": "", "legendFormat": "{{name}}", "refId": "F"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "diskio_reads", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "ops", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Disk IO", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 45, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 41}, "hiddenSeries": false, "id": 52, "interval": "", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(irate(net_bytes_recv{region=\"$region\",clusterID=\"$clusterID\",instance=~\"$instance\"}[$__rate_interval])) by (interface)", "hide": false, "instant": false, "interval": "", "legendFormat": "{{interface}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "net_bytes_recv", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2302", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:2303", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": [], "unit": "binBps"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 41}, "hiddenSeries": false, "id": 53, "interval": "", "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(irate(net_bytes_sent{region=~\"$region\",clusterID=~\"$clusterID\",instance=~\"$instance\"}[$__rate_interval])) by (interface)", "hide": false, "interval": "", "legendFormat": "{{interface}}", "refId": "H"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "net_bytes_sent", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:2302", "format": "binBps", "logBase": 1, "show": true}, {"$$hashKey": "object:2303", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 48}, "hiddenSeries": false, "id": 51, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "netstat_tcp_established{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "legendFormat": "Established", "refId": "A"}, {"exemplar": true, "expr": "netstat_tcp_listen{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "listen", "refId": "H"}, {"exemplar": true, "expr": "netstat_tcp_close{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "close", "refId": "B"}, {"exemplar": true, "expr": "netstat_tcp_close_wait{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "close_wait", "refId": "C"}, {"exemplar": true, "expr": "netstat_tcp_closing{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "closing", "refId": "D"}, {"exemplar": true, "expr": "netstat_tcp_fin_wait1{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "fin_wait1", "refId": "E"}, {"exemplar": true, "expr": "netstat_tcp_fin_wait2{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "fin_wait2", "refId": "F"}, {"exemplar": true, "expr": "netstat_tcp_last_ack{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "last_ack", "refId": "G"}, {"exemplar": true, "expr": "netstat_tcp_time_wait{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "time_wait", "refId": "I"}, {"exemplar": true, "expr": "netstat_tcp_syn_recv{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "SyncRecv", "refId": "J"}, {"exemplar": true, "expr": "netstat_tcp_syn_sent{region=~\"$region\", clusterID=\"$clusterID\", instance=\"$instance\"}", "hide": false, "interval": "", "legendFormat": "SyncSent", "refId": "K"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "TCP Conn Stats", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:979", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:980", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 48}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "net_drop_in{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "drop_in", "refId": "B"}, {"exemplar": true, "expr": "net_drop_out{region=~\"$region\",clusterID=~\"$clusterID\", instance=\"$instance\"}", "interval": "", "legendFormat": "drop_out", "refId": "C"}, {"exemplar": true, "expr": "net_err_in{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "err_in", "refId": "D"}, {"exemplar": true, "expr": "net_err_out{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "err_out", "refId": "E"}, {"exemplar": true, "expr": "net_udp_rcvbuferrors{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "udp_rcvbuferrors", "refId": "I"}, {"exemplar": true, "expr": "net_udp_sndbuferrors{region=~\"$region\",clusterID=~\"$clusterID\", instance=~\"$instance\"}", "interval": "", "legendFormat": "udp_sndbuferrors", "refId": "J"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Network Error", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Network", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 9}, "id": 55, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {"unit": "bytes"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 56}, "hiddenSeries": false, "id": 30, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(disk_total{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"})by(instance, device)", "interval": "", "legendFormat": "disk_total", "refId": "A"}, {"exemplar": true, "expr": "sum(disk_used{region=~\"$region\", clusterID=~\"$clusterID\", instance=~\"$instance\"})by(instance, device)", "hide": false, "interval": "", "legendFormat": "disk_used", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Disk Size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Disk Size", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["master", "machine"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": "datasource", "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bj", "value": "bj"}, "datasource": "${datasource}", "definition": "label_values(system_uptime, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(system_uptime, region)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "cce-zyxcnrtt", "value": "cce-zyxcnrtt"}, "datasource": "${datasource}", "definition": "label_values(system_uptime{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(system_uptime{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": ".*", "current": {"selected": false, "text": "************:9005", "value": "************:9005"}, "datasource": "${datasource}", "definition": "label_values(system_uptime{region=~\"$region\",clusterID =~\"$clusterID\",instance=~\".*9005\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(system_uptime{region=~\"$region\",clusterID =~\"$clusterID\",instance=~\".*9005\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "Master / Machine", "uid": "lE_aFES7z", "version": 20}