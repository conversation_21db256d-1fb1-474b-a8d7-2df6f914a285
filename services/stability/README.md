# CCE 稳定性建设

## 概述

CCE 服务包含两个对象:

1. B 区 Meta K8S 集群, 和集群中 CCE 后端服务;
2. A 区 用户 K8S 集群, 和集群中 CCE K8S 插件。

CCE 至今没出现过由于自身代码和变更等导致重大故障, 有两点关键因素:

1. CCE 所有变更不涉及已有 K8S 集群;
2. CCE 功能和代码上尽量避免变更已有集群, 万不得已时有简单清晰的判断条件, 非预期一律报错处理, 不做自动化。

ps: 关于第二点举个例子: 比如 CCE 完全没有重启机器的代码, 所以用户节点重启和我们肯定是没关系的。

重大故障没有, 但 CCE 值班问题特别多, 总结下来由三个特点决定:

1. K8S 比较复杂, 先不说用户, 我们自己定位许多 K8S 相关问题都非常困难;
2. K8S 是云一套 API, CCE 关联几乎所有计算网络存储产品, 三方异常都会通过 CCE 对外暴露;
3. 不做已有集群变更, 好处是不影响已有服务, 缺点是没办法收敛已知问题, 其中主要是网络和存储相关插件。

2022 4月初整理近 4 个月值班问题, 共计 340 个, 问题频次和认知基本一致。其中 Top5 集群管理/容器网络/K8S使用/流量接入/容器存储等受限于上述三个特点, 没有很好的解决办法, 监控日志/节点组目前主要因为功能不够完善, 相对而言还好。

```md
* 集群管理: 66
* 容器网络: 65
* K8S使用: 62
* 流量接入: 40
* 容器存储: 19
* 监控日志: 18
* 节点组/自动扩缩容: 17
* GPU/AI: 9
* 权限问题: 2
* Serverless: 3
* 使用咨询: 15
* 其他未知: 24
```

**减少值班问题本质是解决用户问题, 是指导产品优化方向核心依据**。问题的收敛最早从 2020 年开始规划, 目前三大基石基本完成:

1. 2020 年完成集群管理 V2 升级, 提供声明式语义, 简化集群操作异常恢复;
2. 2021 年规划托管 Promtheus, 解决成本问题, 为 K8S 可观测性提供生产可用底座;
3. 2022 年 Q1 上线集群升级 Workflow, 提供集群变更能力抽象, 为集群存量问题 K8S 插件收敛提供基础。

尝试通过声明式集群管理, 云原生监控, Workflow 为集群管理, K8S 使用, 容器网络, 流量接入等 Top4 问题提供长期有效地解决方法。

## 目标

目标: 提供产品化解决方案, 让用户可以自助处理和恢复异常

完整描述: 提供框架或方法, 能够为已知和未知的问题, 提供优雅地产品化解决方案, 实现用户可自助处理和恢复异常。不是问题从 100 个变 30 个, 也不是以前 100 min 现在 30 min, 因为产品不是静态的, 而是在不断发展, 所以某个阶段的值下降不代表这个问题就结束了, 我们要做的是为值班问题提供产品化解决方案, 方向正确的话, 前面的结果会自然而然达成。

### 为什么目标是用户能自助处理

就终极目标而言, 消灭问题肯定是不可能的, 旧的问题解决, 新的问题还会出现, 即使 CCE 没问题, IaaS 迭代也会产生新的问题。

由于问题一定会出现, 所以剩下的就是出现问题后谁来处理, 也就三个角色:

1. 我们
2. TAM
3. 用户

目前模式是"我们 > TAM > 用户", 如果还是这个模式, 仅期望通过迭代优化来解决已知问题, 但由于整个产品和云也在不断发展, 历史经验已经证明不可能达到目的。

* 比如 BCC ENI 我们很多坑, 马上 BBC ENI 要出来, 未来新增 ARM 机型, 自研智能卡...

我个人理解应该是"用户 > TAM > 我们", 最高级的方式是不要让用户认为是异常, 而是通过产品去引导用户理解我们的逻辑, 能够去自助的解决。怕的不是出问题, 而是出了问题后得不到足够的指引, 只能提交工单和漫长等待。让用户在使用 CCE 过程中有参与感和掌控感, 可以逐渐建立对使用 CCE 的信心, 我们的目标就达到了。

所以就解决值班问题的目标而言, 我觉得产品上引导用户自助能解决是比较实现的, 当然该修的问题也得修。

### 为什么是通过产品化的解决方案

产品化的另一面是仅管理员可见的系统, 如果一个问题只有通过查看管理员才能看到的系统才能定位, 那用户自助的目标永远达不到。

另一方面, 我们做过很多类似的 cce-thanos/运维平台/showx 报警, 现在都处于半维护状态, 面向用户更能做到高标准要求, 才可能真正持久有效, 才能形成闭环。

所以我们尽力的做成产品功能, 而不去做后台地/一次性地/人肉维护的各种东西。

## 2022 Q2 稳定性 TODO

2022 Q2 重点解决 top4 方向中的重点问题, 分三类:

* 集群管理
* K8S 使用/容器网络
* 流量接入

### 集群管理

CCE Cluster/Instance/InstanceGroup/Workflow 都是 K8S 中 CRD, 支持最终一致语义, 管理方法上和 K8S Pod 没本质区别, 异常恢复做个类比:

```md
K8S Pod 创建失败基本有两种原因:

1. 自身问题: Pod YAML 写得有问题;
2. 依赖问题: Node 或 Pod 依赖有问题。

恢复方法:

1. 自身问题: 修改为正确 YAML, 靠 K8S 重试即可;
2. 依赖问题: 修复依赖问题, K8S 自动重试即可恢复。

CCE CRD 和 Pod 主要的不同是我们限制最大重试次数。
```

长期目标: 丰富 cloudshell 或 client 工具, 用户可以像管理操作 K8S 原生资源一样, 管理 CCE K8S 集群。

当前目标: 前置检查减少已知问题; 提供重试, 用户可自助解决依赖问题。

产品功能规划:

* Cluster/Instance/InstanceGroup/Workflow 创建的前置校验;
* Cluster/Instance/InstanceGroup/Workflow 异常后, 展示异常详情及恢复方法;
* Cluster/Instance/InstanceGroup/Workflow 异常后, 提供重试恢复功能;
* Cluster/Instance/InstanceGroup/Workflow 提供 YAML 更新方式, 作为表单更新的补充, 类似 Deployment YAML 更新;
* Cluster/Instance/InstanceGroup/Workflow 后端实现和前端样式尽量能统一复用。

ps: Cluster/Instance/InstanceGroup/Workflow 和用户 K8S 的 Deployment 没本质区别, **YAML 管理方式能释放集群操作的所有能力**。

### 流量接入

流量接入主要是 lb-controller/ingress-controller 问题, 包括 cni/csi 等自研插件类似。

* 增量: 插件 BUG 或待优化项修复完上线至新集群;
* 存量: 已有集群旧版本插件是本次要解决的重点。

长期目标: 线上集群 K8S 插件版本具备打平的能力(后台自动升级没必要, 产品上引导用户手动升级, 控制风险范围)

功能规划:

* ingress-controller/lb-controller/csi/cni 丰富 event/metrics, 辅助我们和用户白屏定位问题。
* ingress-controller/lb-controller/csi/cni 新增插件升级 Workflow, 遇到已知问题, 引导用户自助升级;
* 如果涉及主机进程或配置变更, 依赖 cce-agent 能力(霖皇目前在做的);

进阶: 将集群运维及自动化变更能力做成一个功能卖点

* 产品上将 Workflow 和 Instance/InstanceGroup 放到平行位置, 统一展示, 后续提供 Master 升配等通用工作流。

ps: ingress-controller/lb-controller 高优, csi/cni 低优。

### K8S 使用/容器网络

用户使用 K8S 遇到各类问题是值班过程中最难处理的部分, 云原生可观测性作为一个和 K8S 同等体量的方向是有道理的, 所以没有银弹, TODO 需要聚焦。

#### 原理说明

我把线上问题分成两类(遇到报障通常可以先问用户是下面哪种情况):

1. 用户第一次就遇到问题, 从未正常运行过;
2. 用户之前一直都运行正常, 突然出现问题。

第一类通常在测试联调阶段, 严重程度不高, 可以让用户配合复现, 相对好解决。产生重大影响的通常是第二类, 对值班同学压力更大, 需要及时定位和止损, 是需要重点解决的。

我解决第二类问题的思路是基于一个假设: **异常通常只是结果, 某些因素的变化才是导致异常的原因**, 这里包含两类数据:

1. SLI A 能反映服务发生异常;
2. SLI B 导致服务 SLI A 异常。

理论上如果不考虑成本和技术实现, 采集服务所有相关数据, 在时序上 B 一定和 A 存在先后关系, 一些例子:

1. 创建 BCI 实例异常, Neutron 刚好在异常前在做发布;
2. 用户 15:00 更新服务, 15:05 服务异常, 几乎能百分百确定是发布导致;
3. 2022-03-28 车和家苏州和保定集群 Node NotReady, 节点一年多前创建一直正常, 近两周问题频发, 用户刚好最近两周升级 node-exporter, 删除后再没出现。

2022 Q2 CCE 可观测性建设, 聚焦解决上述这类问题。

#### 目标方法

目标:

* 长期目标是消灭用户提问题, 而不仅仅只是减少我们值班的工作量;
* 但不出问题不可能, 把经验转化成产品, 引导用户自助发现和定位问题。

原则: 提供产品化的可观测性报表, 管理员和用户统一视角

* 比如一个问题只能我们, 而且必须看内部系统才能定位出来, 用户永远没办法自助定位, 永远不可能彻底消灭我们值班工作;
* 所以 Prometheus/Grafana 等相关建设, 肯定需要管理员和用户看到的东西一模一样, 只有我们能够通过 Dashboard 一眼看出问题, 用户才有可能。

TODO:

* ingress-controller/lb-controller/csi/cni 丰富 event/metrics, 用户能通过 event 和 dashboard 自助定位;
* 丰富 prometheus 采集和 grafana dashboard, dashboard 将"结果和原因"展示在一起, 定期发布和更新采集和展示。

#### 长期目标

CCE 会谨慎的变更已有集群, 所以用户 K8S 出问题最大两种可能:

1. IaaS 出问题;
2. 用户使用不当导致问题, 比如超集群负载使用。

问题 2 核心是可预期, 可预期最理想的表达方式是基于云原生可观测性产品发布 CCE SLO, SLO 含义如下:

```md
用户在保证正常使用 CCE K8S 情况下, 我们能提供什么样的 SLI。
```

包含三层含义:

* CCE 提供什么样的 SLI 能表示服务正常
* 对正常使用 CCE K8S 的定义, 哪些指标会影响服务正常(用户资源规模, 虚机各种监控等)
* 量化 SLI A 和 SLI B 的关联关系

例子: APIServer 响应超时, 进一步发现 etcd 磁盘延迟高, 延迟高既可能是原因, 也可能是结果:

1. CDS 集群异常导致 IO 延迟大, 导致 etcd 性能受影响;
2. 其实是用户请求 APIServer 突增导致 etcd 响应慢。

如果可以将这 4 个数据放在一起展示, 找到量化关系, 理论上大部分 APIServer 响应延迟的问题都是可以白屏解决。

