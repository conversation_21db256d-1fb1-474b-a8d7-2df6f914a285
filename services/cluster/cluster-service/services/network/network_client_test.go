package network

import (
	"context"
	"errors"
	"fmt"
	"math"
	"net"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"

	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	vpcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
)

func TestService_calculateMaxNodeNum(t *testing.T) {
	type args struct {
		cidr       string
		maxNodePod int
	}

	tests := []struct {
		name           string
		args           args
		wantErr        bool
		wantMaxNodeNum int
	}{
		{
			name: "mask=16，maxNodePod=32",
			args: args{
				cidr:       "***********/16",
				maxNodePod: int(math.Pow(2, 5)),
			},
			wantErr:        false,
			wantMaxNodeNum: int(math.Pow(2, 11)),
		},

		{
			name: "mask=16，maxNodePod=64",
			args: args{
				cidr:       "***********/16",
				maxNodePod: int(math.Pow(2, 6)),
			},
			wantErr:        false,
			wantMaxNodeNum: int(math.Pow(2, 10)),
		},

		{
			name: "mask=16，maxNodePod=128",
			args: args{
				cidr:       "***********/16",
				maxNodePod: int(math.Pow(2, 7)),
			},
			wantErr:        false,
			wantMaxNodeNum: int(math.Pow(2, 9)),
		},

		{
			name: "mask=16，maxNodePod=256",
			args: args{
				cidr:       "***********/16",
				maxNodePod: int(math.Pow(2, 8)),
			},
			wantErr:        false,
			wantMaxNodeNum: int(math.Pow(2, 8)),
		},

		{
			name: "mask=16，maxNodePod=16",
			args: args{
				cidr:       "***********/16",
				maxNodePod: 16,
			},
			wantMaxNodeNum: 4096,
		},

		{
			name: "mask=17，maxNodePod=256",
			args: args{
				cidr:       "***********/17",
				maxNodePod: int(math.Pow(2, 8)),
			},
			wantErr:        false,
			wantMaxNodeNum: int(math.Pow(2, 7)),
		},

		{
			name: "mask=30，maxNodePod=256",
			args: args{
				cidr:       "***********/30",
				maxNodePod: int(math.Pow(2, 8)),
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			maxNodeNum, err := calculateMaxNodeNum(tt.args.cidr, tt.args.maxNodePod)

			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("calculateMaxNodeNum() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("calculateMaxNodeNum() failed, wantErr=true got=false")
				return
			}

			if maxNodeNum != tt.wantMaxNodeNum {
				t.Errorf("calculateMaxNodeNum() failed, wantMaxNodeNum=%v got=%v", tt.wantMaxNodeNum, maxNodeNum)
				return
			}
			t.Logf("calculateMaxNodeNum() succeded, wantMaxNodeNum=%v got=%v", tt.wantMaxNodeNum, maxNodeNum)
		})
	}
}

func TestService_checkNetRange(t *testing.T) {
	type args struct {
		cidr string
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "10.0.0.0/8/1 invalid",
			args: args{
				cidr: "10.0.0.0/8/1",
			},
			wantErr: true,
		},

		{
			name: "10.0.0.0.1/8 invalid",
			args: args{
				cidr: "10.0.0.0.1/8",
			},
			wantErr: true,
		},

		{
			name: "10.0.0.0/8 valid",
			args: args{
				cidr: "10.0.0.0/8",
			},
			wantErr: false,
		},

		{
			name: "********/15 invalid",
			args: args{
				cidr: "********/15",
			},
			wantErr: true,
		},

		{
			name: "**********/15 valid",
			args: args{
				cidr: "**********/15",
			},
			wantErr: false,
		},

		{
			name: "**********/16 valid",
			args: args{
				cidr: "**********/16",
			},
			wantErr: false,
		},

		{
			name: "**********/16 valid",
			args: args{
				cidr: "**********/16",
			},
			wantErr: false,
		},

		{
			name: "***********/17 valid",
			args: args{
				cidr: "***********/17",
			},
			wantErr: false,
		},

		{
			name: "***********/17 invalid",
			args: args{
				cidr: "***********/17",
			},
			wantErr: true,
		},

		{
			name: "192.16.0.0/16 invalid",
			args: args{
				cidr: "192.16.0.0/16",
			},
			wantErr: true,
		},

		{
			name: "***********/14 invalid",
			args: args{
				cidr: "***********/14",
			},
			wantErr: true,
		},

		{
			name: "191.168.0.0/17 invalid",
			args: args{
				cidr: "191.168.0.0/17",
			},
			wantErr: true,
		},

		//{
		//	name: "***********/20 invalid",
		//	args: args{
		//		cidr: "***********/20",
		//	},
		//	wantErr: true,
		//},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := checkNetRange(tt.args.cidr)

			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("checkNetRange() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("checkNetRange() failed, wantErr=true got=false")
				return
			}
		})
	}
}

func TestContainerCIDR_IsConflictWithNodeCIDR(t *testing.T) {
	type args struct {
		ctx      context.Context
		nodeCIDR string
	}
	tests := []struct {
		name    string
		cidr    ContainerCIDR
		args    args
		want    *ccesdk.NetworkConflictInfo
		wantErr bool
	}{
		{
			cidr:    "***********/16",
			args:    args{nodeCIDR: "**********/24"},
			want:    nil,
			wantErr: false,
		},
		{
			cidr:    "**********/16",
			args:    args{nodeCIDR: "**********/24"},
			want:    nil,
			wantErr: false,
		},
		{
			cidr: "***********/16",
			args: args{nodeCIDR: "192.168.8.0/24"},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with node cidr[%s]", "***********/16", "192.168.8.0/24"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType:     ccesdk.ContainerCIDRAndNodeCIDRConflict,
					ConflictNodeCIDR: &ccesdk.ConflictNodeCIDR{NodeCIDR: "192.168.8.0/24"},
				},
			},
			wantErr: false,
		},
		{
			cidr:    "fc00:8888::/56",
			args:    args{nodeCIDR: "240c:4082:0:d100::/56"},
			want:    nil,
			wantErr: false,
		},
		{
			cidr: "fc00:8888:777::/56",
			args: args{nodeCIDR: "fc00:8888::/32"},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with node cidr[%s]", "fc00:8888:777::/56", "fc00:8888::/32"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType:     ccesdk.ContainerCIDRAndNodeCIDRConflict,
					ConflictNodeCIDR: &ccesdk.ConflictNodeCIDR{NodeCIDR: "fc00:8888::/32"},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.cidr.isConflictWithVPCCIDR(tt.args.ctx, tt.args.nodeCIDR)
			if (err != nil) != tt.wantErr {
				t.Errorf("isConflictWithVPCCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("isConflictWithVPCCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestContainerCIDR_IsConflictWithExistedClusters(t *testing.T) {
	type args struct {
		ctx       context.Context
		ipVersion ccetypes.ContainerNetworkIPType
		clusters  []*models.Cluster
	}
	tests := []struct {
		name    string
		cidr    ContainerCIDR
		args    args
		want    *ccesdk.NetworkConflictInfo
		wantErr bool
	}{
		{
			name:    "同VPC集群为空",
			cidr:    "***********/16",
			args:    args{clusters: []*models.Cluster{}},
			want:    nil,
			wantErr: false,
		},
		{
			name: "同VPC集群冲突",
			cidr: "**********/24",
			args: args{clusters: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-pCgco50A",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-H7yQ0tIh",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-24gtLtyT",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
			}},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with cluster %s container cidr[%s]", "**********/24", "c-H7yQ0tIh", "**********/16"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndExistedClusterContainerCIDRConflict,
					ConflictCluster: &ccesdk.ConflictCluster{
						ClusterID:     "c-H7yQ0tIh",
						ContainerCIDR: "**********/16",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "同VPC集群冲突",
			cidr: "**********/13",
			args: args{clusters: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-24gtLtyT",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "172.299.0.0/16"}, // invalid CIDR
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-pCgco50A",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-H7yQ0tIh",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
			}},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with cluster %s container cidr[%s]", "**********/13", "c-pCgco50A", "**********/16"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndExistedClusterContainerCIDRConflict,
					ConflictCluster: &ccesdk.ConflictCluster{
						ClusterID:     "c-pCgco50A",
						ContainerCIDR: "**********/16",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "同VPC集群不冲突",
			cidr: "**********/16",
			args: args{clusters: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-pCgco50A",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-H7yQ0tIh",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-24gtLtyT",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
			}},
			want:    nil,
			wantErr: false,
		},
		{
			name: "同VPC集群不冲突",
			cidr: "***********/16",
			args: args{clusters: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-pCgco50A",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-H7yQ0tIh",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-24gtLtyT",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDR: "**********/16"},
					},
				},
			}},
			want:    nil,
			wantErr: false,
		},
		{
			name:    "同VPC集群为空",
			cidr:    "fd00::/8",
			args:    args{clusters: []*models.Cluster{}, ipVersion: ccetypes.ContainerNetworkIPTypeIPv6},
			want:    nil,
			wantErr: false,
		},
		{
			name: "同VPC集群冲突",
			cidr: "fd00::/8",
			args: args{clusters: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-H7yQ0tIh",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDRIPv6: "2001:da8:d800:1472/64"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-pCgco50A",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDRIPv6: "fd00:1234::/120"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-24gtLtyT",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDRIPv6: "2000:da8:d800:1472/64"},
					},
				},
			}, ipVersion: ccetypes.ContainerNetworkIPTypeIPv6},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with cluster %s container cidr[%s]", "fd00::/8", "c-pCgco50A", "fd00:1234::/120"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndExistedClusterContainerCIDRConflict,
					ConflictCluster: &ccesdk.ConflictCluster{
						ClusterID:     "c-pCgco50A",
						ContainerCIDR: "fd00:1234::/120",
					},
				},
			},
			wantErr: false,
		},
		{
			name: "同VPC集群不冲突",
			cidr: "fd00::/8",
			args: args{clusters: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-H7yQ0tIh",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDRIPv6: "2001:da8:d800:1472/64"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-pCgco50A",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDRIPv6: "fe00:1234::/120"},
					},
				},
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:              "c-24gtLtyT",
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{ClusterPodCIDRIPv6: "2000:da8:d800:1472/64"},
					},
				},
			}, ipVersion: ccetypes.ContainerNetworkIPTypeIPv6},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.cidr.isConflictWithExistedClusters(tt.args.ctx, tt.args.ipVersion, tt.args.clusters)
			if (err != nil) != tt.wantErr {
				t.Errorf("isConflictWithExistedClusters() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("isConflictWithExistedClusters() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func TestContainerCIDR_IsConflictWithVPCRoutes(t *testing.T) {
	type args struct {
		ctx        context.Context
		vpcCIDR    string
		routeRules []vpc.RouteRule
	}
	tests := []struct {
		name    string
		cidr    ContainerCIDR
		args    args
		want    *ccesdk.NetworkConflictInfo
		wantErr bool
	}{
		{
			name: "RouteRules为空",
			cidr: "***********/16",
			args: args{
				routeRules: []vpc.RouteRule{},
			},
			want:    nil,
			wantErr: false,
		},

		{
			name: "Container与Route目的地址不冲突",
			cidr: "***********/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********.0/16", // 目的解析失败
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/16",
					},
				},
			},
			want:    nil,
			wantErr: false,
		},

		{
			name: "Route目的地址有0.0.0.0/0, Container与Route目的地址不冲突",
			cidr: "***********/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "**********/16",
						DestinationAddress: "0.0.0.0/0",
					},
				},
			},
			want:    nil,
			wantErr: false,
		},

		{
			name: "Container与Route目的地址冲突",
			cidr: "***********/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "**********/16",
						DestinationAddress: "0.0.0.0/0",
					},
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "**********/16",
						DestinationAddress: "***********/24",
					},
				},
			},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflict with VPC route with destination address [%s]", "***********/16", "***********/24"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndVPCRouteConflict,
					ConflictVPCRoute: &ccesdk.ConflictVPCRoute{
						RouteRule: vpc.RouteRule{
							RouteRuleID:        "RouteRule-1",
							SourceAddress:      "**********/16",
							DestinationAddress: "***********/24",
						},
					},
				},
			},
			wantErr: false,
		},

		{
			name: "Container与Route目的地址不冲突",
			cidr: "**********/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "**********/16",
						DestinationAddress: "0.0.0.0/0",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "**********/16",
						DestinationAddress: "0.0.0.0/0",
					},
					{
						RouteRuleID:        "RouteRule-4",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Container与Route目的地址冲突",
			cidr: "**********/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "**********/16",
						DestinationAddress: "0.0.0.0/0",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "**********/16",
						DestinationAddress: "0.0.0.0/0",
					},
					{
						RouteRuleID:        "RouteRule-4",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
				},
			},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflict with VPC route with destination address [%s]", "**********/16", "**********/24"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndVPCRouteConflict,
					ConflictVPCRoute: &ccesdk.ConflictVPCRoute{
						RouteRule: vpc.RouteRule{
							RouteRuleID:        "RouteRule-4",
							SourceAddress:      "0.0.0.0/0",
							DestinationAddress: "**********/24",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "Container与Route目的地址不冲突",
			cidr: "**********/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "1240c:4082:0:d104::/64",
						DestinationAddress: "::/0",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "::/0",
						DestinationAddress: "fc00::/24",
					},
					{
						RouteRuleID:        "RouteRule-4",
						SourceAddress:      "::/0",
						DestinationAddress: "fc00:100::/24",
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Container与Route目的地址不冲突",
			cidr: "fd00::/24",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "1240c:4082:0:d104::/64",
						DestinationAddress: "::/0",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "::/0",
						DestinationAddress: "fc00::/24",
					},
					{
						RouteRuleID:        "RouteRule-4",
						SourceAddress:      "::/0",
						DestinationAddress: "fc00:100::/24",
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "Container与Route目的地址冲突",
			cidr: "fc00::/16",
			args: args{
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-2",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "1240c:4082:0:d104::/64",
						DestinationAddress: "::/0",
					},
					{
						RouteRuleID:        "RouteRule-3",
						SourceAddress:      "::/0",
						DestinationAddress: "fc00::/24",
					},
					{
						RouteRuleID:        "RouteRule-4",
						SourceAddress:      "::/0",
						DestinationAddress: "fc00:100::/24",
					},
				},
			},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflict with VPC route with destination address [%s]", "fc00::/16", "fc00::/24"),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndVPCRouteConflict,
					ConflictVPCRoute: &ccesdk.ConflictVPCRoute{
						RouteRule: vpc.RouteRule{
							RouteRuleID:        "RouteRule-3",
							SourceAddress:      "::/0",
							DestinationAddress: "fc00::/24",
						},
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.cidr.isConflictWithVPCRoutes(tt.args.ctx, tt.args.vpcCIDR, tt.args.routeRules)
			if (err != nil) != tt.wantErr {
				t.Errorf("isConflictWithVPCRoutes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("isConflictWithVPCRoutes() got = %+v, want %+v", got, tt.want)
			}
		})
	}
}

func Test_sortContainerCIDRCandidates(t *testing.T) {
	type args struct {
		privateNetCIDRCandidates []ccesdk.PrivateNetString
		vpcCIDR                  string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{},
				vpcCIDR:                  "10.0.0.0/16",
			},
			want: []string{},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8"},
				vpcCIDR:                  "10.0.0.0/16",
			},
			want: []string{"10.0.0.0/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"**********/12"},
				vpcCIDR:                  "10.0.0.0/16",
			},
			want: []string{"**********/12"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8", "**********/12", "***********/16"},
				vpcCIDR:                  "10.0.0.0/16",
			},
			want: []string{"**********/12", "***********/16", "10.0.0.0/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8", "**********/12", "***********/16"},
				vpcCIDR:                  "**********/16",
			},
			want: []string{"10.0.0.0/8", "***********/16", "**********/12"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8", "**********/12"},
				vpcCIDR:                  "***********/16",
			},
			want: []string{"10.0.0.0/8", "**********/12"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"fd00::/8"},
				vpcCIDR:                  "240c:4082:0:d100::/56",
			},
			want: []string{"fd00::/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"fd00::/8"},
				vpcCIDR:                  "fd00::/8",
			},
			want: []string{"fd00::/8"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := sortContainerCIDRCandidates(tt.args.privateNetCIDRCandidates, tt.args.vpcCIDR); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("sortContainerCIDRCandidates() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_recommendContainerCIDR(t *testing.T) {
	type fields struct {
		accountID string
		clients   *clients.Clients
		models    models.Interface
	}
	type args struct {
		ctx                  context.Context
		ipVersion            ccetypes.ContainerNetworkIPType
		sortedCandidateCIDRs []string
		maskSize             int
		vpcCIDR              string
		clusters             []*models.Cluster
		routeRules           []vpc.RouteRule
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []string
	}{
		{
			name: "掩码为负",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"***********/16"},
				vpcCIDR:              "***********/16",
				maskSize:             0,
			},
			want: []string{},
		},
		{
			name: "节点网段与候选网段相同，无法推荐",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"***********/16"},
				vpcCIDR:              "***********/16",
			},
			want: []string{},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"***********/16", "10.0.0.0/8"},
				maskSize:             24,
				vpcCIDR:              "10.0.0.0/16",
			},
			want: []string{"***********/24", "***********/24", "***********/24", "***********/24", "***********/24"},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"***********/16", "10.0.0.0/8"},
				maskSize:             18,
				vpcCIDR:              "10.0.0.0/16",
			},
			want: []string{"***********/18", "************/18", "*************/18", "*************/18", "********/18"},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"***********/16", "10.0.0.0/8"},
				maskSize:             16,
				vpcCIDR:              "********/16",
			},
			want: []string{"***********/16", "10.0.0.0/16", "********/16", "********/16", "********/16"},
		},
		{
			name: "节点网段与候选网段不同，掩码大于候选网段",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"***********/16", "10.0.0.0/8"},
				maskSize:             9,
				vpcCIDR:              "********/16",
			},
			want: []string{"**********/9"},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"**********/12", "***********/16", "10.0.0.0/8"},
				maskSize:             16,
				vpcCIDR:              "********/16",
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/24",
					},
				},
			},
			want: []string{"**********/16", "**********/16", "**********/16", "**********/16", "**********/16"},
		},
		{
			name: "大掩码和路由冲突，没有符合要求的",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv4,
				sortedCandidateCIDRs: []string{"**********/12", "***********/16", "10.0.0.0/8"},
				maskSize:             9,
				vpcCIDR:              "********/16",
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "0.0.0.0/0",
						DestinationAddress: "**********/16",
					},
				},
			},
			want: []string{},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv6,
				sortedCandidateCIDRs: []string{"fd00::/8"},
				maskSize:             9,
				vpcCIDR:              "240c:4082:0:d100::/56",
			},
			want: []string{"fd00::/9", "fd80::/9", "fd00::/9", "fd80::/9"},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv6,
				sortedCandidateCIDRs: []string{"fd00::/8"},
				maskSize:             12,
				vpcCIDR:              "240c:4082:0:d100::/56",
			},
			want: []string{"fc00::/12", "fc10::/12", "fc20::/12", "fc30::/12", "fc40::/12"},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv6,
				sortedCandidateCIDRs: []string{"fd00::/8"},
				maskSize:             12,
				vpcCIDR:              "240c:4082:0:d100::/56",
			},
			want: []string{"fd00::/12", "fd10::/12", "fd20::/12", "fd30::/12", "fd40::/12"},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				ipVersion:            ccetypes.ContainerNetworkIPTypeIPv6,
				sortedCandidateCIDRs: []string{"fd00::/8"},
				maskSize:             16,
				vpcCIDR:              "240c:4082:0:d100::/56",
				routeRules: []vpc.RouteRule{
					{
						RouteRuleID:        "RouteRule-1",
						SourceAddress:      "::/0",
						DestinationAddress: "fd00::/24",
					},
				},
			},
			want: []string{"fd01::/16", "fd02::/16", "fd03::/16", "fd04::/16", "fd05::/16"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			got := c.recommendContainerCIDR(tt.args.ctx, tt.args.ipVersion, tt.args.sortedCandidateCIDRs, tt.args.maskSize, tt.args.vpcCIDR, tt.args.clusters, tt.args.routeRules)
			for index := range got {
				ip, _, err := net.ParseCIDR(got[index])
				if err != nil {
					t.Errorf("parse cidr error %s - %v", got[index], err)
				}
				if ip.To4() != nil && got[index] != tt.want[index] {
					t.Errorf("recommendContainerCIDR() = %v, want %v", got, tt.want)
				}
				// ipv6 addr was generated from random string
			}
		})
	}
}

func Test_sortClusterIPCIDRCandidates(t *testing.T) {
	type args struct {
		privateNetCIDRCandidates []ccesdk.PrivateNetString
		vpcCIDR                  string
		containerCIDR            string
	}
	tests := []struct {
		name string
		args args
		want []string
	}{
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{},
				vpcCIDR:                  "10.0.0.0/16",
				containerCIDR:            "********/16",
			},
			want: []string{},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8"},
				vpcCIDR:                  "10.0.0.0/16",
				containerCIDR:            "********/16",
			},
			want: []string{"10.0.0.0/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8"},
				vpcCIDR:                  "10.0.0.0/16",
				containerCIDR:            "***********/16",
			},
			want: []string{"10.0.0.0/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"**********/12"},
				vpcCIDR:                  "10.0.0.0/16",
				containerCIDR:            "***********/16",
			},
			want: []string{"**********/12"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8", "**********/12", "***********/16"},
				vpcCIDR:                  "***********/24",
				containerCIDR:            "********/16",
			},
			want: []string{"**********/12", "***********/16", "10.0.0.0/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8", "**********/12", "***********/16"},
				vpcCIDR:                  "**********/16",
				containerCIDR:            "172.16.2.0/16",
			},
			want: []string{"***********/16", "10.0.0.0/8", "**********/12"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"10.0.0.0/8", "**********/12"},
				vpcCIDR:                  "***********/16",
				containerCIDR:            "172.16.2.0/16",
			},
			want: []string{"10.0.0.0/8", "**********/12"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"fd00::/8"},
				vpcCIDR:                  "240c:4082:0:d100::/56",
				containerCIDR:            "fd00::/8",
			},
			want: []string{"fd00::/8"},
		},
		{
			args: args{
				privateNetCIDRCandidates: []ccesdk.PrivateNetString{"fd00::/8"},
				vpcCIDR:                  "fc00:ffff::/56",
				containerCIDR:            "fd00::/8",
			},
			want: []string{"fd00::/8"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := sortClusterIPCIDRCandidates(tt.args.privateNetCIDRCandidates, tt.args.vpcCIDR, tt.args.containerCIDR)
			for index := range got {
				ip, _, err := net.ParseCIDR(got[index])
				if err != nil {
					t.Errorf("parse cidr error %s - %v", got[index], err)
				}
				if ip.To4() != nil && got[index] != tt.want[index] {
					t.Errorf("recommendClusterIP() = %v, want %v", got, tt.want)
				}
				// ipv6 addr was generated from random string
			}
		})
	}
}

func TestClient_recommendClusterIPCIDR(t *testing.T) {
	type fields struct {
		accountID string
		clients   *clients.Clients
		models    models.Interface
	}
	type args struct {
		ctx                  context.Context
		ipVersion            ccetypes.ContainerNetworkIPType
		sortedCandidateCIDRs []string
		maskSize             int
		vpcCIDR              string
		containerCIDR        string
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   []string
	}{
		{
			name: "掩码为负",
			args: args{
				sortedCandidateCIDRs: []string{"***********/16"},
				vpcCIDR:              "***********/16",
				containerCIDR:        "10.0.0.0/16",
			},
			want: []string{},
		},
		{
			name: "节点网段与候选网段相同，无法推荐",
			args: args{
				sortedCandidateCIDRs: []string{"***********/16"},
				vpcCIDR:              "***********/16",
				containerCIDR:        "10.0.0.0/16",
			},
			want: []string{},
		},
		{
			name: "节点网段与候选网段不同",
			args: args{
				sortedCandidateCIDRs: []string{"***********/16", "10.0.0.0/8"},
				maskSize:             16,
				vpcCIDR:              "10.0.0.0/16",
				containerCIDR:        "**********/16",
			},
			want: []string{"***********/16", "**********/16", "10.253.0.0/16", "10.252.0.0/16", "10.251.0.0/16"},
		},
		{
			name: "172段足够",
			args: args{
				sortedCandidateCIDRs: []string{"**********/12", "***********/16", "10.0.0.0/8"},
				maskSize:             16,
				vpcCIDR:              "***********/24",
				containerCIDR:        "********/16",
			},
			want: []string{"**********/16", "172.30.0.0/16", "172.29.0.0/16", "172.28.0.0/16", "172.27.0.0/16"},
		},
		{
			name: "掩码过大，无法推荐",
			args: args{
				sortedCandidateCIDRs: []string{"**********/12", "***********/16", "10.0.0.0/8"},
				maskSize:             8,
				vpcCIDR:              "***********/24",
				containerCIDR:        "********/16",
			},
			want: []string{},
		},
		{
			name: "10段能够推荐",
			args: args{
				sortedCandidateCIDRs: []string{"**********/12", "***********/16", "10.0.0.0/8"},
				maskSize:             10,
				vpcCIDR:              "***********/24",
				containerCIDR:        "********/16",
			},
			want: []string{"**********/10", "**********/10", "10.64.0.0/10"},
		},
		{
			name: "10段能够推荐",
			args: args{
				sortedCandidateCIDRs: []string{"**********/12", "***********/16", "10.0.0.0/8"},
				maskSize:             12,
				vpcCIDR:              "**********/24",
				containerCIDR:        "********/16",
			},
			want: []string{"10.240.0.0/12", "10.224.0.0/12", "10.208.0.0/12", "**********/12", "10.176.0.0/12"},
		},
		{
			name: "fd00::/8 段能够推荐",
			args: args{
				sortedCandidateCIDRs: []string{"fd00::/8"},
				maskSize:             118,
				vpcCIDR:              "240c:4082:0:d100::/56",
				containerCIDR:        "fd00::/16",
			},
			want: []string{
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:fc00/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:f800/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:f400/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:f000/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:ec00/118"},
		},
		{
			name: "fd00::/8 段能够推荐",
			args: args{
				sortedCandidateCIDRs: []string{"fd00::/8"},
				maskSize:             118,
				vpcCIDR:              "240c:4082:0:d100::/56",
				containerCIDR:        "fdf3::/15",
			},
			want: []string{
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:fc00/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:f800/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:f400/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:f000/118",
				"fdff:ffff:ffff:ffff:ffff:ffff:ffff:ec00/118"},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			got := c.recommendClusterIPCIDR(tt.args.ctx, tt.args.sortedCandidateCIDRs, tt.args.maskSize, tt.args.vpcCIDR, tt.args.containerCIDR)
			for index := range got {
				ip, _, err := net.ParseCIDR(got[index])
				if err != nil {
					t.Errorf("parse cidr error %s - %v", got[index], err)
				}
				if ip.To4() != nil && got[index] != tt.want[index] {
					t.Errorf("recommendClusterIP() = %v, want %v", got, tt.want)
				}
				// ipv6 addr was generated from random string
			}
		})
	}
}

func Test_calculateClusterCIDRMaskSize1(t *testing.T) {
	type args struct {
		maxPodsPerNode    int
		clusterMaxNodeNum int
		k8sVersion        ccetypes.K8SVersion
	}
	tests := []struct {
		name    string
		args    args
		want    int
		want1   int
		wantErr bool
	}{
		{
			name: "case 1",
			args: args{
				maxPodsPerNode:    256,
				clusterMaxNodeNum: 256,
				k8sVersion:        ccetypes.K8S_1_16_8,
			},
			want:  16,
			want1: 112,
		},
		{
			name: "case 2",
			args: args{
				maxPodsPerNode:    256,
				clusterMaxNodeNum: 256,
				k8sVersion:        ccetypes.K8SVersion("1.18.2"),
			},
			want:  16,
			want1: 112,
		},
		{
			name: "case 3",
			args: args{
				maxPodsPerNode:    128,
				clusterMaxNodeNum: 200,
				k8sVersion:        ccetypes.K8S_1_16_8,
			},
			want:  17,
			want1: 112,
		},
		{
			name: "case 4",
			args: args{
				maxPodsPerNode:    128,
				clusterMaxNodeNum: 9223372036854775807, // math.MaxInt64
				k8sVersion:        ccetypes.K8S_1_16_8,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, got1, err := calculateClusterCIDRMaskSize(tt.args.maxPodsPerNode, tt.args.clusterMaxNodeNum, tt.args.k8sVersion)
			if (err != nil) != tt.wantErr {
				t.Errorf("calculateClusterCIDRMaskSize() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("calculateClusterCIDRMaskSize() got = %v, want %v", got, tt.want)
			}
			if got1 != tt.want1 {
				t.Errorf("calculateClusterCIDRMaskSize() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_checkClusterIPCIDR(t *testing.T) {
	type args struct {
		ctx           context.Context
		clusterIPCIDR ClusterIPCIDR
		vpcCIDR       string
	}
	tests := []struct {
		name    string
		args    args
		want    *ccesdk.NetworkConflictInfo
		wantErr bool
	}{
		{
			name: "异常：IPv4, ClusterIP CIDR 格式错误",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "***********",
				vpcCIDR:       "**********/16",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "异常：IPv4, ClusterIP CIDR 非私有网段",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "***********/16",
				vpcCIDR:       "**********/16",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "正常：IPv4, ClusterIP 与 VPC 不冲突",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "*********/12",
				vpcCIDR:       "**********/16",
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "异常：IPv4, ClusterIP 与 VPC 冲突",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "***********/16",
				vpcCIDR:       "***********/24",
			},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict:            true,
				ErrMsg:                "clusterip cidr [***********/16] conflicts with node cidr[***********/24]",
				ContainerCIDRConflict: nil,
				ClusterIPCIDRConflict: &ccesdk.ClusterIPCIDRConflict{
					ConflictType: ccesdk.ClusterIPCIDRAndNodeCIDRConflict,
					ConflictNodeCIDR: &ccesdk.ConflictNodeCIDR{
						NodeCIDR: "***********/24",
					},
					ConflictContainerCIDR: nil,
				},
			},
			wantErr: false,
		},
		{
			name: "正常：IPv6, ClusterIP 与 VPC 不冲突",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "fd00::/8",
				vpcCIDR:       "fe00:1234::/120",
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "异常：IPv6, ClusterIP 与 VPC 冲突",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "fd00::/8",
				vpcCIDR:       "fd00:1234::/120",
			},
			want: &ccesdk.NetworkConflictInfo{
				IsConflict:            true,
				ErrMsg:                "clusterip cidr [fd00::/8] conflicts with node cidr[fd00:1234::/120]",
				ContainerCIDRConflict: nil,
				ClusterIPCIDRConflict: &ccesdk.ClusterIPCIDRConflict{
					ConflictType: ccesdk.ClusterIPCIDRAndNodeCIDRConflict,
					ConflictNodeCIDR: &ccesdk.ConflictNodeCIDR{
						NodeCIDR: "fd00:1234::/120",
					},
					ConflictContainerCIDR: nil,
				},
			},
			wantErr: false,
		},
		{
			name: "异常：IPv6, ClusterIP CIDR 格式错误",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "2001:db8::",
				vpcCIDR:       "240c:4082:0:d100::/56",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "异常：IPv6, ClusterIP CIDR 非私有网段",
			args: args{
				ctx:           context.TODO(),
				clusterIPCIDR: "2001:db8::/32",
				vpcCIDR:       "240c:4082:0:d100::/56",
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := checkClusterIPCIDR(tt.args.ctx, tt.args.clusterIPCIDR, tt.args.vpcCIDR)
			if (err != nil) != tt.wantErr {
				t.Errorf("checkClusterIPCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("checkClusterIPCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_CheckClusterIPCIDR(t *testing.T) {
	type fields struct {
		accountID string
		clients   *clients.Clients
		models    models.Interface
	}
	type args struct {
		ctx context.Context
		req *ccesdk.CheckClusterIPCIDRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.CheckClusterIPCIDRResponse
		wantErr bool
	}{
		{
			name:   "正常：VPCID 为空",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "",
					VPCCIDR:           "**********/16",
					VPCCIDRIPv6:       "",
					ClusterIPCIDR:     "***********/16",
					ClusterIPCIDRIPv6: "",
					IPVersion:         ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:   "正常：IPVersion 为空",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "**********/16",
					VPCCIDRIPv6:       "",
					ClusterIPCIDR:     "***********/16",
					ClusterIPCIDRIPv6: "",
					IPVersion:         "",
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name:   "正常：IPv4, ClusterIP CIDR 不冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "**********/16",
					VPCCIDRIPv6:       "",
					ClusterIPCIDR:     "***********/16",
					ClusterIPCIDRIPv6: "",
					IPVersion:         ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: false,
				ErrMsg:     "",
				RequestID:  "",
			},
			wantErr: false,
		},
		{
			name:   "正常：IPv4, ClusterIP CIDR 冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "***********/24",
					VPCCIDRIPv6:       "",
					ClusterIPCIDR:     "***********/16",
					ClusterIPCIDRIPv6: "",
					IPVersion:         ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: true,
				ErrMsg:     "clusterip cidr [***********/16] conflicts with node cidr[***********/24]",
				RequestID:  "",
			},
			wantErr: false,
		},
		{
			name:   "正常：IPv6, ClusterIP CIDR 不冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "",
					VPCCIDRIPv6:       "fe00:1234::/120",
					ClusterIPCIDR:     "",
					ClusterIPCIDRIPv6: "fd00::/110",
					IPVersion:         ccetypes.ContainerNetworkIPTypeIPv6,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: false,
				ErrMsg:     "",
				RequestID:  "",
			},
			wantErr: false,
		},
		{
			name:   "正常：IPv6, ClusterIP CIDR 冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "",
					VPCCIDRIPv6:       "fd00:1234::/120",
					ClusterIPCIDR:     "",
					ClusterIPCIDRIPv6: "fd00:1234::/110",
					IPVersion:         ccetypes.ContainerNetworkIPTypeIPv6,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: true,
				ErrMsg:     "clusterip cidr [fd00:1234::/110] conflicts with node cidr[fd00:1234::/120]",
				RequestID:  "",
			},
			wantErr: false,
		},
		{
			name:   "正常：DualStack, ClusterIP CIDR 不冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "***********/16",
					VPCCIDRIPv6:       "fe00:1234::/120",
					ClusterIPCIDR:     "**********/16",
					ClusterIPCIDRIPv6: "fd00::/110",
					IPVersion:         ccetypes.ContainerNetworkIPTypeDualStack,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: false,
				ErrMsg:     "",
				RequestID:  "",
			},
			wantErr: false,
		},
		{
			name:   "正常：DualStack, ClusterIP CIDR, IPv4冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "***********/24",
					VPCCIDRIPv6:       "fe00:1234::/120",
					ClusterIPCIDR:     "***********/16",
					ClusterIPCIDRIPv6: "fd00::/110",
					IPVersion:         ccetypes.ContainerNetworkIPTypeDualStack,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: true,
				ErrMsg:     "clusterip cidr [***********/16] conflicts with node cidr[***********/24]",
				RequestID:  "",
			},
			wantErr: false,
		},
		{
			name:   "正常：DualStack, ClusterIP CIDR, IPv6冲突",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckClusterIPCIDRequest{
					VPCID:             "VPCID",
					VPCCIDR:           "***********/16",
					VPCCIDRIPv6:       "fd00:1234::/120",
					ClusterIPCIDR:     "**********/16",
					ClusterIPCIDRIPv6: "fd00:1234::/110",
					IPVersion:         ccetypes.ContainerNetworkIPTypeDualStack,
				},
			},
			want: &ccesdk.CheckClusterIPCIDRResponse{
				IsConflict: true,
				ErrMsg:     "clusterip cidr [fd00:1234::/110] conflicts with node cidr[fd00:1234::/120]",
				RequestID:  "",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			got, err := c.CheckClusterIPCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckClusterIPCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckClusterIPCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_isValidClusterIP(t *testing.T) {
	tests := []struct {
		name    string
		cidr    string
		wantErr bool
		errMsg  string
	}{
		// 测试私有网段 IPv4 - 应该返回 nil (无错误)
		{
			name:    "IPv4 private net 10.0.0.0/8",
			cidr:    "10.0.0.0/8",
			wantErr: false,
		},
		{
			name:    "IPv4 private net *********/12",
			cidr:    "*********/12",
			wantErr: false,
		},
		{
			name:    "IPv4 private net **********/12",
			cidr:    "**********/12",
			wantErr: false,
		},
		{
			name:    "IPv4 private net **********/16",
			cidr:    "**********/16",
			wantErr: false,
		},
		{
			name:    "IPv4 private net ***********/16",
			cidr:    "***********/16",
			wantErr: false,
		},
		{
			name:    "IPv4 private net ***********/24",
			cidr:    "***********/24",
			wantErr: false,
		},
		// 测试扩展私有网段
		{
			name:    "IPv4 extended private net *******/8",
			cidr:    "*******/8",
			wantErr: false,
		},
		{
			name:    "IPv4 extended private net *******/8",
			cidr:    "*******/8",
			wantErr: false,
		},
		{
			name:    "IPv4 extended private net *******/8",
			cidr:    "*******/8",
			wantErr: false,
		},
		// 测试保留的 ClusterIP 网段
		{
			name:    "Reserved ClusterIP ********/8",
			cidr:    "********/8",
			wantErr: false,
		},
		{
			name:    "Reserved ClusterIP *********/12",
			cidr:    "*********/12",
			wantErr: false,
		},
		// 测试 IPv6 私有网段
		{
			name:    "IPv6 private net fd00::/8",
			cidr:    "fd00::/8",
			wantErr: false,
		},
		{
			name:    "IPv6 private net fd00:1234::/32",
			cidr:    "fd00:1234::/32",
			wantErr: false,
		},
		{
			name:    "IPv6 private net fd00:1234:5678::/48",
			cidr:    "fd00:1234:5678::/48",
			wantErr: false,
		},
		// 测试公网 IPv4 地址 - 应该返回错误
		{
			name:    "Public IPv4 *******/24",
			cidr:    "*******/24",
			wantErr: true,
			errMsg:  "illegal CIDR: *******/24, must be private addresses",
		},
		{
			name:    "Public IPv4 *************/24",
			cidr:    "*************/24",
			wantErr: true,
			errMsg:  "illegal CIDR: *************/24, must be private addresses",
		},
		{
			name:    "Public IPv4 *************/24",
			cidr:    "*************/24",
			wantErr: true,
			errMsg:  "illegal CIDR: *************/24, must be private addresses",
		},
		{
			name:    "Public IPv4 *******/24",
			cidr:    "*******/24",
			wantErr: true,
			errMsg:  "illegal CIDR: *******/24, must be private addresses",
		},
		// 测试公网 IPv6 地址 - 应该返回错误
		{
			name:    "Public IPv6 2001:db8::/32",
			cidr:    "2001:db8::/32",
			wantErr: true,
			errMsg:  "illegal CIDR: 2001:db8::/32, must be private addresses",
		},
		{
			name:    "Public IPv6 2400:3200::/32",
			cidr:    "2400:3200::/32",
			wantErr: true,
			errMsg:  "illegal CIDR: 2400:3200::/32, must be private addresses",
		},
		{
			name:    "Public IPv6 fe80::/10",
			cidr:    "fe80::/10",
			wantErr: true,
			errMsg:  "illegal CIDR: fe80::/10, must be private addresses",
		},
		// 测试边界情况 - 注意：无效的CIDR格式会在util.IsPrivateNetCIDRString中导致panic
		// 所以我们只测试格式正确但不是私有网段的情况
		// 测试私有网段的边界情况
		{
			name:    "IPv4 private net boundary ************/24",
			cidr:    "************/24",
			wantErr: false,
		},
		{
			name:    "IPv4 private net boundary **********/16",
			cidr:    "**********/16",
			wantErr: false,
		},
		{
			name:    "IPv4 private net boundary ************/24",
			cidr:    "************/24",
			wantErr: false,
		},
		{
			name:    "IPv4 private net boundary *************/24",
			cidr:    "*************/24",
			wantErr: false,
		},
		// 测试非私有网段的边界情况
		{
			name:    "Non-private IPv4 *******/8",
			cidr:    "*******/8",
			wantErr: true,
			errMsg:  "illegal CIDR: *******/8, must be private addresses",
		},
		{
			name:    "Non-private IPv4 **********/16",
			cidr:    "**********/16",
			wantErr: true,
			errMsg:  "illegal CIDR: **********/16, must be private addresses",
		},
		{
			name:    "Non-private IPv4 **********/16",
			cidr:    "**********/16",
			wantErr: true,
			errMsg:  "illegal CIDR: **********/16, must be private addresses",
		},
		{
			name:    "Non-private IPv4 ***********/16",
			cidr:    "***********/16",
			wantErr: true,
			errMsg:  "illegal CIDR: ***********/16, must be private addresses",
		},
		{
			name:    "Non-private IPv4 ***********/16",
			cidr:    "***********/16",
			wantErr: true,
			errMsg:  "illegal CIDR: ***********/16, must be private addresses",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := isValidClusterIP(tt.cidr)

			if tt.wantErr {
				if err == nil {
					t.Errorf("isValidClusterIP() expected error but got nil")
					return
				}
				if err.Error() != tt.errMsg {
					t.Errorf("isValidClusterIP() error = %v, want %v", err.Error(), tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("isValidClusterIP() unexpected error = %v", err)
				}
			}
		})
	}
}

func TestClient_RecommendContainerNetworkCIDR(t *testing.T) {
	type fields struct {
		ctl       *gomock.Controller
		accountID string
		clients   *clients.Clients
		models    models.Interface
	}
	type args struct {
		ctx context.Context
		req *ccesdk.RecommendContainerNetworkCIDRRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.RecommendContainerNetworkCIDRResponse
		wantErr bool
	}{
		{
			name: "failed to list cluster by vpc should return error",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("connection time out")),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),

			args: args{
				ctx: context.TODO(),
				req: &ccesdk.RecommendContainerNetworkCIDRRequest{
					K8SVersion:               "1.16.8",
					VPCID:                    "vpc-xxx",
					VPCCIDR:                  "10.0.0.0/8",
					ClusterMaxNodeNum:        256,
					MaxPodsPerNode:           256,
					ContainerPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net172, ccesdk.PrivateIPv4Net192},
					ClusterMaxServiceNum:     1024,
					ClusterIPPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net172, ccesdk.PrivateIPv4Net192},
				},
			},
			wantErr: true,
		},

		{
			name: "no container cidr to recommend",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),

			args: args{
				ctx: context.TODO(),
				req: &ccesdk.RecommendContainerNetworkCIDRRequest{
					K8SVersion:               "1.16.8",
					VPCID:                    "vpc-xxx",
					VPCCIDR:                  "10.0.0.0/8",
					ClusterMaxNodeNum:        256,
					MaxPodsPerNode:           256,
					ContainerPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10},
					ClusterMaxServiceNum:     1024,
					ClusterIPPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net172, ccesdk.PrivateIPv4Net192},
				},
			},
			want: &ccesdk.RecommendContainerNetworkCIDRResponse{
				IsSuccess: false,
				ErrMsg:    "container cidr: no available subnet to recommend",
			},
		},

		{
			name: "no clusterip cidr to recommend",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),

			args: args{
				ctx: context.TODO(),
				req: &ccesdk.RecommendContainerNetworkCIDRRequest{
					K8SVersion:               "1.16.8",
					VPCID:                    "vpc-xxx",
					VPCCIDR:                  "10.0.0.0/8",
					ClusterMaxNodeNum:        256,
					MaxPodsPerNode:           256,
					ContainerPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net192},
					ClusterMaxServiceNum:     65536,
					ClusterIPPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net192},
				},
			},
			want: &ccesdk.RecommendContainerNetworkCIDRResponse{
				IsSuccess: false,
				ErrMsg:    "clusterip cidr: no available subnet to recommend",
			},
		},

		{
			name: "normal case 1",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),

			args: args{
				ctx: context.TODO(),
				req: &ccesdk.RecommendContainerNetworkCIDRRequest{
					K8SVersion:               "1.16.8",
					VPCID:                    "vpc-xxx",
					VPCCIDR:                  "10.0.0.0/8",
					ClusterMaxNodeNum:        256,
					MaxPodsPerNode:           256,
					ContainerPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net172, ccesdk.PrivateIPv4Net192},
					ClusterMaxServiceNum:     1024,
					ClusterIPPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net172, ccesdk.PrivateIPv4Net192},
				},
			},
			want: &ccesdk.RecommendContainerNetworkCIDRResponse{
				RecommendedContainerCIDR: "**********/16",
				RecommendedClusterIPCIDR: "*************/22",
				IsSuccess:                true,
			},
		},

		{
			name: "normal case 2",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),

			args: args{
				ctx: context.TODO(),
				req: &ccesdk.RecommendContainerNetworkCIDRRequest{
					K8SVersion:               "1.16.8",
					VPCID:                    "vpc-xxx",
					VPCCIDR:                  "10.0.0.0/8",
					ClusterMaxNodeNum:        256,
					MaxPodsPerNode:           512,
					ContainerPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net172, ccesdk.PrivateIPv4Net192},
					ClusterMaxServiceNum:     65536,
					ClusterIPPrivateNetCIDRs: []ccesdk.PrivateNetString{ccesdk.PrivateIPv4Net10, ccesdk.PrivateIPv4Net172},
				},
			},
			want: &ccesdk.RecommendContainerNetworkCIDRResponse{
				RecommendedContainerCIDR: "**********/15",
				RecommendedClusterIPCIDR: "**********/16",
				IsSuccess:                true,
			},
		},

		{
			name: "normal case 3",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),

			args: args{
				ctx: context.TODO(),
				req: &ccesdk.RecommendContainerNetworkCIDRRequest{
					K8SVersion:                   "1.16.8",
					IPVersion:                    ccetypes.ContainerNetworkIPTypeIPv6,
					VPCID:                        "vpc-xxx",
					VPCCIDRIPv6:                  "240c:4082:0:d100::/56",
					ClusterMaxNodeNum:            256,
					MaxPodsPerNode:               512,
					ContainerPrivateNetCIDRIPv6s: []ccesdk.PrivateNetString{"fd00::/8"},
					ClusterMaxServiceNum:         65536,
					ClusterIPPrivateNetCIDRIPv6s: []ccesdk.PrivateNetString{"fd00::/8"},
				},
			},
			want: &ccesdk.RecommendContainerNetworkCIDRResponse{
				RecommendedContainerCIDRIPv6: "fd00::/15",
				RecommendedClusterIPCIDRIPv6: "fdff:ffff:ffff:ffff:ffff:ffff:ffff:0/112",
				IsSuccess:                    true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctl != nil {
				defer tt.fields.ctl.Finish()
			}

			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			got, err := c.RecommendContainerNetworkCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("RecommendContainerNetworkCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if tt.want != nil {
				if tt.want.RecommendedClusterIPCIDRIPv6 != "" {
					if got.RecommendedClusterIPCIDRIPv6 == "" {
						t.Errorf("fault in ipv6 ClusterIP got %v, want %v", got.RecommendedClusterIPCIDRIPv6, tt.want.RecommendedClusterIPCIDRIPv6)
					}
					got.RecommendedClusterIPCIDRIPv6 = tt.want.RecommendedClusterIPCIDRIPv6
				}
				if tt.want.RecommendedContainerCIDRIPv6 != "" {
					if got.RecommendedContainerCIDRIPv6 == "" {
						t.Errorf("fault in IPv6 container got %v, want %v", got.RecommendedContainerCIDRIPv6, tt.want.RecommendedContainerCIDRIPv6)
					}
					got.RecommendedContainerCIDRIPv6 = tt.want.RecommendedContainerCIDRIPv6
				}
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("RecommendContainerNetworkCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_CheckContainerNetworkCIDR(t *testing.T) {
	type fields struct {
		ctl       *gomock.Controller
		accountID string
		clients   *clients.Clients
		models    models.Interface
	}
	type args struct {
		ctx context.Context
		req *ccesdk.CheckContainerNetworkCIDRRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.CheckContainerNetworkCIDRResponse
		wantErr bool
	}{
		{
			name: "normal case 1",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckContainerNetworkCIDRRequest{
					VPCID:                  "vpc-xxxx",
					VPCCIDR:                "10.0.0.0/8",
					ContainerCIDR:          "**********/16",
					ClusterIPCIDR:          "**********/16",
					MaxPodsPerNode:         256,
					SkipContainerCIDRCheck: false,
				},
			},
			want: &ccesdk.CheckContainerNetworkCIDRResponse{
				MaxNodeNum:          256,
				NetworkConflictInfo: ccesdk.NetworkConflictInfo{},
			},
			wantErr: false,
		},
		{
			name: "normal case 2",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Cluster{&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-fake",
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								ClusterPodCIDR: "**********/16",
							},
						},
						Status: nil,
					}}, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckContainerNetworkCIDRRequest{
					VPCID:                  "vpc-xxxx",
					VPCCIDR:                "10.0.0.0/8",
					ContainerCIDR:          "**********/16",
					ClusterIPCIDR:          "**********/16",
					MaxPodsPerNode:         256,
					SkipContainerCIDRCheck: false,
				},
			},
			want: &ccesdk.CheckContainerNetworkCIDRResponse{
				MaxNodeNum: 256,
				NetworkConflictInfo: ccesdk.NetworkConflictInfo{
					IsConflict: true,
					ErrMsg:     "container cidr [**********/16] conflicts with cluster cce-fake container cidr[**********/16]",
					ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
						ConflictType: ccesdk.ContainerCIDRAndExistedClusterContainerCIDRConflict,
						ConflictCluster: &ccesdk.ConflictCluster{
							ClusterID:     "cce-fake",
							ContainerCIDR: "**********/16",
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "skip case 1",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckContainerNetworkCIDRRequest{
					VPCID:                  "vpc-xxxx",
					VPCCIDR:                "10.0.0.0/8",
					ContainerCIDR:          "**********/16",
					ClusterIPCIDR:          "**********/16",
					MaxPodsPerNode:         256,
					SkipContainerCIDRCheck: true,
				},
			},
			want: &ccesdk.CheckContainerNetworkCIDRResponse{
				MaxNodeNum:          256,
				NetworkConflictInfo: ccesdk.NetworkConflictInfo{},
			},
			wantErr: false,
		},
		{
			name: "normal case 2",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsClient := stsmock.NewMockInterface(ctl)
				vpcClient := vpcmock.NewMockInterface(ctl)
				modelClient := models.NewMockInterface(ctl)

				gomock.InOrder(
					modelClient.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Cluster{&models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-fake",
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								ClusterPodCIDR: "**********/16",
							},
						},
						Status: nil,
					}}, nil),
					stsClient.EXPECT().NewSignOption(gomock.Any(), gomock.Any()).Return(nil),
					vpcClient.EXPECT().ListRouteTable(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:     ctl,
					clients: &clients.Clients{VPCClient: vpcClient, STSClient: stsClient},
					models:  modelClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckContainerNetworkCIDRRequest{
					VPCID:                  "vpc-xxxx",
					VPCCIDR:                "10.0.0.0/8",
					ContainerCIDR:          "**********/16",
					ClusterIPCIDR:          "**********/16",
					MaxPodsPerNode:         256,
					SkipContainerCIDRCheck: true,
				},
			},
			want: &ccesdk.CheckContainerNetworkCIDRResponse{
				MaxNodeNum:          256,
				NetworkConflictInfo: ccesdk.NetworkConflictInfo{},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			got, err := c.CheckContainerNetworkCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckContainerNetworkCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckContainerNetworkCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestClient_CheckAuxiliaryIPCIDR(t *testing.T) {
	type fields struct {
		accountID string
		clients   *clients.Clients
		models    models.Interface
	}
	type args struct {
		ctx context.Context
		req *ccesdk.CheckAuxiliaryIPCIDRRequest
	}
	//不冲突的测试用例
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.CheckAuxiliaryIPCIDRResponse
		wantErr bool
	}{
		{
			name: "无req",
			args: args{
				ctx: context.TODO(),
				req: nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "ipv4正常流程，非辅助网段，非ebpf",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "***********/16",
					IsEBPF:        false,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckAuxiliaryIPCIDRResponse{
				IsConflict:  false,
				ErrMsg:      "",
				IsAuxiliary: false,
			},
			wantErr: false,
		},
		{
			name: "ipv4正常流程，辅助网段，非ebpf",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "**********/16",
					IsEBPF:        false,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckAuxiliaryIPCIDRResponse{
				IsConflict:  false,
				ErrMsg:      "",
				IsAuxiliary: true,
			},
			wantErr: false,
		},
		{
			name: "ipv4正常流程，辅助网段，ebpf",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "**********/16",
					IsEBPF:        true,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckAuxiliaryIPCIDRResponse{
				IsConflict:  false,
				ErrMsg:      "",
				IsAuxiliary: true,
			},
			wantErr: false,
		},
		{
			name: "ipv4正常流程，非辅助网段，ebpf",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "***********/16",
					IsEBPF:        true,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckAuxiliaryIPCIDRResponse{
				IsConflict:  false,
				ErrMsg:      "",
				IsAuxiliary: false,
			},
			wantErr: false,
		},
		{
			name: "异常：req格式不合法，缺少vpccidr",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/16",
				},
			},
			want:    nil,
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			ctrl := gomock.NewController(t)

			model := models.NewMockInterface(ctrl)
			model.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Cluster{}, nil)
			c.models = model

			got, err := c.CheckAuxiliaryIPCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAuxiliaryIPCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckAuxiliaryIPCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
	//有冲突的测试用例
	tests2 := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.CheckAuxiliaryIPCIDRResponse
		wantErr bool
	}{
		{
			name: "与已有vpc路由冲突，ipv4，辅助网段，非ebpf",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "**********/16",
					IsEBPF:        false,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckAuxiliaryIPCIDRResponse{
				IsConflict:  true,
				ErrMsg:      "auxiliary cidr [***********/24] conflicts with existed vpc-route cluster cce-123456 container cidr[***********/24]",
				IsAuxiliary: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests2 {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			ctrl := gomock.NewController(t)

			model := models.NewMockInterface(ctrl)
			model.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:           ccetypes.ContainerNetworkModeVPCRoute,
							ClusterPodCIDR: "***********/24",
						},
						ClusterID: "cce-123456",
					},
				},
			}, nil)
			c.models = model

			got, err := c.CheckAuxiliaryIPCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAuxiliaryIPCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckAuxiliaryIPCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
	// 外部依赖函数有报错的异常情况下的测试用例
	tests3 := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.CheckAuxiliaryIPCIDRResponse
		wantErr bool
	}{
		{
			name: "异常：与已有vpc路由冲突，ipv4，辅助网段，非ebpf， GetClustersByVPC函数报错",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "**********/16",
					IsEBPF:        false,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: nil,

			wantErr: true,
		},
	}
	for _, tt := range tests3 {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			ctrl := gomock.NewController(t)

			model := models.NewMockInterface(ctrl)
			model.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("mock error"))
			c.models = model

			got, err := c.CheckAuxiliaryIPCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAuxiliaryIPCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckAuxiliaryIPCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
	//已有集群为vpceni模式的测试用例
	tests4 := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.CheckAuxiliaryIPCIDRResponse
		wantErr bool
	}{
		{
			name: "有vpceni集群用了相同网段，ipv4，辅助网段，非ebpf",
			args: args{
				ctx: context.TODO(),
				req: &ccesdk.CheckAuxiliaryIPCIDRRequest{
					ContainerCIDR: "***********/24",
					VPCID:         "vpc-xxxxx",
					VPCCIDR:       "**********/16",
					IsEBPF:        false,
					IPVersion:     ccetypes.ContainerNetworkIPTypeIPv4,
				},
			},
			want: &ccesdk.CheckAuxiliaryIPCIDRResponse{
				IsConflict:  false,
				ErrMsg:      "",
				IsAuxiliary: true,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests4 {
		t.Run(tt.name, func(t *testing.T) {
			c := &Client{
				accountID: tt.fields.accountID,
				clients:   tt.fields.clients,
				models:    tt.fields.models,
			}
			ctrl := gomock.NewController(t)

			model := models.NewMockInterface(ctrl)
			model.EXPECT().GetClustersByVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return([]*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:           ccetypes.ContainerNetworkModeVPCENI,
							ClusterPodCIDR: "***********/24",
						},
						ClusterID: "cce-123456",
					},
				},
			}, nil)
			c.models = model

			got, err := c.CheckAuxiliaryIPCIDR(tt.args.ctx, tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("CheckAuxiliaryIPCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("CheckAuxiliaryIPCIDR() got = %v, want %v", got, tt.want)
			}
		})
	}
}
