package network

import (
	"context"
	"errors"
	"fmt"
	"math"
	"net"
	"sort"

	gocidr "github.com/apparentlymart/go-cidr/cidr"
	netutil "k8s.io/utils/net"

	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/util"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
)

const (
	// maxRecommendedCIDRNum 推荐网段的最大数量
	maxRecommendedCIDRNum = 5
)

// Client 是 quota.Interface 的实现
type Client struct {
	accountID string
	clients   *clients.Clients
	models    models.Interface
}

// NewClient 初始化 quota.Client
func NewClient(ctx context.Context, accountID string, clients *clients.Clients, models models.Interface) (Interface, error) {
	if accountID == "" {
		return nil, fmt.Errorf("accountID is empty")
	}

	if clients == nil {
		return nil, fmt.Errorf("clients is nil")
	}

	if models == nil {
		return nil, fmt.Errorf("models is nil")
	}

	return &Client{
		accountID: accountID,
		clients:   clients,
		models:    models,
	}, nil
}

// CheckContainerNetworkCIDR 检查容器网络是否有效
// TODO: 根据冲突类型推荐可选的网段
//
// 主要的步骤：
// 1.计算最大节点数
// 2.检查容器网段
// 3.检查 ClusterIP 网段
func (c *Client) CheckContainerNetworkCIDR(ctx context.Context, req *ccesdk.CheckContainerNetworkCIDRRequest) (*ccesdk.CheckContainerNetworkCIDRResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request is nil")
	}

	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 计算最大节点数, 根据 IPv4 地址空间计算
	maxNodeNum, err := calculateMaxNodeNum(req.ContainerCIDR, req.MaxPodsPerNode)
	if err != nil {
		msg := fmt.Sprintf("calculate max node num error: %v", err)
		logger.Errorf(ctx, msg)
		return nil, err
	}

	logger.Infof(ctx, "CheckContainerNetworkCIDR MaxNodeNum: %d", maxNodeNum)

	// 容器网络检查待返回的结果
	resp := &ccesdk.CheckContainerNetworkCIDRResponse{
		MaxNodeNum: maxNodeNum,
	}

	// 获取 VPC 集群列表（实际上是为了获取 ContainerCIDR）
	clusters, err := c.models.GetClustersByVPC(ctx, c.accountID, req.VPCID)
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and existed clusters: GetClustersByVPC failed: %v", err)
		logger.Errorf(ctx, msg)
		return nil, err
	}

	// 获取 VPC 路由表
	args := &vpc.ListRouteArgs{
		VpcID: req.VPCID,
	}
	routeRules, err := c.clients.VPCClient.ListRouteTable(ctx, args, c.clients.STSClient.NewSignOption(ctx, c.accountID))
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and vpc routes: ListRouteTable in VPC=%v failed: %v", req.VPCID, err)
		logger.Errorf(ctx, msg)
		return nil, err
	}

	logger.Infof(ctx, "CheckContainerNetworkCIDR RouteList: %s", utils.ToJSON(routeRules))

	var conflictInfo *ccesdk.NetworkConflictInfo

	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		conflictInfo, err = c.checkContainerNetworkCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv4,
			ContainerCIDR(req.ContainerCIDR), ClusterIPCIDR(req.ClusterIPCIDR), req.VPCCIDR, clusters, routeRules, req.SkipContainerCIDRCheck)
	case ccetypes.ContainerNetworkIPTypeIPv6:
		conflictInfo, err = c.checkContainerNetworkCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv6,
			ContainerCIDR(req.ContainerCIDRIPv6), ClusterIPCIDR(req.ClusterIPCIDRIPv6), req.VPCCIDRIPv6, clusters, routeRules, req.SkipContainerCIDRCheck)
	case ccetypes.ContainerNetworkIPTypeDualStack:
		// 首先检查 IPv4
		conflictInfo, err = c.checkContainerNetworkCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv4,
			ContainerCIDR(req.ContainerCIDR), ClusterIPCIDR(req.ClusterIPCIDR), req.VPCCIDR, clusters, routeRules, req.SkipContainerCIDRCheck)
		if err != nil || conflictInfo != nil {
			goto ErrorOrConflict
		}

		// 检查完 IPv4 没有冲突，检查 IPv6
		conflictInfo, err = c.checkContainerNetworkCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv6,
			ContainerCIDR(req.ContainerCIDRIPv6), ClusterIPCIDR(req.ClusterIPCIDRIPv6), req.VPCCIDRIPv6, clusters, routeRules, req.SkipContainerCIDRCheck)
	}
ErrorOrConflict:
	if err != nil {
		return nil, err
	}
	if conflictInfo != nil {
		logger.Infof(ctx, "checkContainerNetworkCIDRByVersion %v conflicts: %+v", req.IPVersion, utils.ToJSON(conflictInfo))
		resp.NetworkConflictInfo = *conflictInfo
		return resp, nil
	}

	return resp, nil
}

// RecommendContainerNetworkCIDR 给出一组推荐的容器网段与 ClusterIP 网段
// Step 1. 推荐容器网段
// Step 2. 推荐 ClusterIP 网段
func (c *Client) RecommendContainerNetworkCIDR(ctx context.Context, req *ccesdk.RecommendContainerNetworkCIDRRequest) (*ccesdk.RecommendContainerNetworkCIDRResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request is nil")
	}

	// 校验请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 最终的推荐结果
	var containerCIDR, clusterIPCIDR string
	var containerCIDRIPv6, clusterIPCIDRIPv6 string

	// 推荐容器网段
	containerCIDRReq := &ccesdk.RecommendContainerCIDRRequest{
		VPCID:               req.VPCID,
		VPCCIDR:             req.VPCCIDR,
		VPCCIDRIPv6:         req.VPCCIDRIPv6,
		ClusterMaxNodeNum:   req.ClusterMaxNodeNum,
		MaxPodsPerNode:      req.MaxPodsPerNode,
		PrivateNetCIDRs:     req.ContainerPrivateNetCIDRs,
		PrivateNetCIDRIPv6s: req.ContainerPrivateNetCIDRIPv6s,
		K8SVersion:          req.K8SVersion,
		IPVersion:           req.IPVersion,
	}

	containerCIDRResp, err := c.RecommendContainerCIDR(ctx, containerCIDRReq)
	if err != nil {
		logger.Errorf(ctx, "RecommendContainerNetworkCIDR: failed to recommend container cidr: %v", err)
		return nil, err
	}

	if !containerCIDRResp.IsSuccess {
		goto FailToRecommendContainerCIDR
	}

	// 推荐 ClusterIP 网段
	// v4/v6分开，依次尝试每个 ContainerCIDR，找到一个可推荐的就返回
	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		containerCIDR, clusterIPCIDR, err = c.recommendContainerNetworkByVersion(ctx, req.IPVersion, req.VPCCIDR, containerCIDRResp.RecommendedContainerCIDRs, req.ClusterMaxServiceNum, req.ClusterIPPrivateNetCIDRs)
		if err != nil {
			goto FailToRecommendClusterIPCIDR
		}
	case ccetypes.ContainerNetworkIPTypeIPv6:
		containerCIDRIPv6, clusterIPCIDRIPv6, err = c.recommendContainerNetworkByVersion(ctx, req.IPVersion, req.VPCCIDRIPv6, containerCIDRResp.RecommendedContainerCIDRIPv6s, req.ClusterMaxServiceNum, req.ClusterIPPrivateNetCIDRIPv6s)
		if err != nil {
			goto FailToRecommendClusterIPCIDR
		}
	case ccetypes.ContainerNetworkIPTypeDualStack:
		containerCIDR, clusterIPCIDR, err = c.recommendContainerNetworkByVersion(ctx, req.IPVersion, req.VPCCIDR, containerCIDRResp.RecommendedContainerCIDRs, req.ClusterMaxServiceNum, req.ClusterIPPrivateNetCIDRs)
		if err != nil {
			goto FailToRecommendClusterIPCIDR
		}
		containerCIDRIPv6, clusterIPCIDRIPv6, err = c.recommendContainerNetworkByVersion(ctx, req.IPVersion, req.VPCCIDRIPv6, containerCIDRResp.RecommendedContainerCIDRIPv6s, req.ClusterMaxServiceNum, req.ClusterIPPrivateNetCIDRIPv6s)
		if err != nil {
			goto FailToRecommendClusterIPCIDR
		}
	}

	// no error here
	return &ccesdk.RecommendContainerNetworkCIDRResponse{
		RecommendedContainerCIDR:     containerCIDR,
		RecommendedContainerCIDRIPv6: containerCIDRIPv6,
		RecommendedClusterIPCIDR:     clusterIPCIDR,
		RecommendedClusterIPCIDRIPv6: clusterIPCIDRIPv6,
		IsSuccess:                    true,
	}, nil

FailToRecommendContainerCIDR:
	// 没有找到可以推荐的网段
	logger.Warnf(ctx, "RecommendContainerNetworkCIDR: fail to recommend %v container cidr: no available subnet", req.IPVersion)
	return &ccesdk.RecommendContainerNetworkCIDRResponse{
		IsSuccess: false,
		ErrMsg:    "container cidr: " + containerCIDRResp.ErrMsg,
	}, nil

FailToRecommendClusterIPCIDR:
	// 没有找到可以推荐的网段
	logger.Warnf(ctx, "RecommendClusterIPCIDR: fail to recommend clusterip cidr: no available subnet")
	return &ccesdk.RecommendContainerNetworkCIDRResponse{
		IsSuccess: false,
		ErrMsg:    "clusterip cidr: " + err.Error(),
	}, nil
}

// RecommendContainerCIDR 推荐容器网段
func (c *Client) RecommendContainerCIDR(ctx context.Context, req *ccesdk.RecommendContainerCIDRRequest) (*ccesdk.RecommendContainerCIDRResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request is nil")
	}

	// 校验请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	// 计算 IPv4/IPv6 子网掩码大小
	podMaskSize, podMaskSizeIPv6, err := calculateClusterCIDRMaskSize(req.MaxPodsPerNode, req.ClusterMaxNodeNum, req.K8SVersion)
	if err != nil {
		return nil, err
	}
	logger.Infof(ctx, "RecommendContainerCIDR: cluster max pod num is %d, IPv4/IPv6 cidr mask size is %v/%v", req.MaxPodsPerNode*req.ClusterMaxNodeNum, podMaskSize, podMaskSizeIPv6)

	// 获取 VPC 集群列表（实际上是为了获取 ContainerCIDR）
	clusters, err := c.models.GetClustersByVPC(ctx, c.accountID, req.VPCID)
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and existed clusters: GetClustersByVPC failed: %v", err)
		logger.Errorf(ctx, msg)
		return nil, err
	}

	// 获取 VPC 路由表
	args := &vpc.ListRouteArgs{
		VpcID: req.VPCID,
	}
	routeRules, err := c.clients.VPCClient.ListRouteTable(ctx, args, c.clients.STSClient.NewSignOption(ctx, c.accountID))
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and vpc routes: ListRouteTable in VPC=%v failed: %v", req.VPCID, err)
		logger.Errorf(ctx, msg)
		return nil, err
	}

	var recommendedCIDRs, recommendedCIDRIPv6s []string
	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		recommendedCIDRs = c.recommendContainerCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv4, req.VPCCIDR, req.PrivateNetCIDRs, podMaskSize, clusters, routeRules)
		if len(recommendedCIDRs) == 0 {
			goto FailToRecommend
		}
	case ccetypes.ContainerNetworkIPTypeIPv6:
		recommendedCIDRIPv6s = c.recommendContainerCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv6, req.VPCCIDRIPv6, req.PrivateNetCIDRIPv6s, podMaskSizeIPv6, clusters, routeRules)
		if len(recommendedCIDRIPv6s) == 0 {
			goto FailToRecommend
		}
	case ccetypes.ContainerNetworkIPTypeDualStack:
		recommendedCIDRs = c.recommendContainerCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv4, req.VPCCIDR, req.PrivateNetCIDRs, podMaskSize, clusters, routeRules)
		if len(recommendedCIDRs) == 0 {
			goto FailToRecommend
		}
		recommendedCIDRIPv6s = c.recommendContainerCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv6, req.VPCCIDRIPv6, req.PrivateNetCIDRIPv6s, podMaskSizeIPv6, clusters, routeRules)
		if len(recommendedCIDRIPv6s) == 0 {
			goto FailToRecommend
		}
	}

	return &ccesdk.RecommendContainerCIDRResponse{
		RecommendedContainerCIDRs:     recommendedCIDRs,
		RecommendedContainerCIDRIPv6s: recommendedCIDRIPv6s,
		IsSuccess:                     true,
	}, nil

FailToRecommend:
	// 没有找到可以推荐的网段
	logger.Warnf(ctx, "RecommendContainerCIDR: fail to recommend %v container cidr: no available subnet", req.IPVersion)
	return &ccesdk.RecommendContainerCIDRResponse{
		IsSuccess: false,
		ErrMsg:    "no available subnet to recommend",
	}, nil
}

// RecommendClusterIPCIDR 推荐 ClusterIP 网段
func (c *Client) RecommendClusterIPCIDR(ctx context.Context, req *ccesdk.RecommendClusterIPCIDRRequest) (*ccesdk.RecommendClusterIPCIDRResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("request is nil")
	}
	// 校验请求
	if err := req.Validate(); err != nil {
		return nil, err
	}

	var recommendedCIDRs, recommendedCIDRIPv6s []string
	switch req.IPVersion {
	case ccetypes.ContainerNetworkIPTypeIPv4:
		recommendedCIDRs = c.recommendClusterIPCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv4, req.VPCCIDR, req.ContainerCIDR, req.ClusterMaxServiceNum, req.PrivateNetCIDRs)
		if len(recommendedCIDRs) == 0 {
			goto FailToRecommend
		}
	case ccetypes.ContainerNetworkIPTypeIPv6:
		recommendedCIDRIPv6s = c.recommendClusterIPCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv6, req.VPCCIDRIPv6, req.ContainerCIDRIPv6, req.ClusterMaxServiceNum, req.PrivateNetCIDRIPv6s)
		if len(recommendedCIDRIPv6s) == 0 {
			goto FailToRecommend
		}
	case ccetypes.ContainerNetworkIPTypeDualStack:
		recommendedCIDRs = c.recommendClusterIPCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv4, req.VPCCIDR, req.ContainerCIDR, req.ClusterMaxServiceNum, req.PrivateNetCIDRs)
		if len(recommendedCIDRs) == 0 {
			goto FailToRecommend
		}
		recommendedCIDRIPv6s = c.recommendClusterIPCIDRByVersion(ctx, ccetypes.ContainerNetworkIPTypeIPv6, req.VPCCIDRIPv6, req.ContainerCIDRIPv6, req.ClusterMaxServiceNum, req.PrivateNetCIDRIPv6s)
		if len(recommendedCIDRIPv6s) == 0 {
			goto FailToRecommend
		}
	}

	return &ccesdk.RecommendClusterIPCIDRResponse{
		RecommendedClusterIPCIDRs:     recommendedCIDRs,
		RecommendedClusterIPCIDRIPv6s: recommendedCIDRIPv6s,
		IsSuccess:                     true,
	}, nil

FailToRecommend:
	// 没有找到可以推荐的网段
	logger.Warnf(ctx, "RecommendClusterIPCIDR: fail to recommend clusterip cidr: no available subnet")
	return &ccesdk.RecommendClusterIPCIDRResponse{
		IsSuccess: false,
		ErrMsg:    "no available subnet to recommend",
	}, nil
}

// recommendContainerNetworkByVersion 推荐一组容器网段与 ClusterIP 网段
func (c *Client) recommendContainerNetworkByVersion(
	ctx context.Context,
	ipVersion ccetypes.ContainerNetworkIPType,
	VPCCIDR string,
	containerCIDRs []string,
	clusterMaxServiceNum int,
	privateNetCIDRCandidates []ccesdk.PrivateNetString,
) (string, string, error) {
	// loop for every container cidr
	for _, containerCIDR := range containerCIDRs {
		recommendedCIDRs := c.recommendClusterIPCIDRByVersion(ctx, ipVersion, VPCCIDR, containerCIDR, clusterMaxServiceNum, privateNetCIDRCandidates)
		// found one pair
		if len(recommendedCIDRs) != 0 {
			return containerCIDR, recommendedCIDRs[0], nil
		}
	}

	return "", "", fmt.Errorf("no available subnet to recommend")
}

func (c *Client) checkContainerNetworkCIDRByVersion(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType,
	containerCIDR ContainerCIDR, clusterIPCIDR ClusterIPCIDR, VPCCIDR string,
	clusters []*models.Cluster, routeRules []vpc.RouteRule, skipContainerCIDRCheck bool) (*ccesdk.NetworkConflictInfo, error) {
	// step 1. 检查容器网段冲突
	if !skipContainerCIDRCheck {
		conflictInfo, err := c.checkContainerCIDR(ctx, ipVersion, containerCIDR, VPCCIDR, clusters, routeRules)
		if err != nil {
			return nil, err
		}
		if conflictInfo != nil {
			logger.Infof(ctx, "checkContainerCIDR conflicts: %+v", *conflictInfo)
			return conflictInfo, nil
		}
	}

	// step 2. 检查 ClusterIP 网段冲突
	conflictInfo, err := c.checkClusterIPCIDR(ctx, clusterIPCIDR, VPCCIDR, string(containerCIDR))
	if err != nil {
		return nil, err
	}
	if conflictInfo != nil {
		logger.Infof(ctx, "checkClusterIPCIDR conflicts: %+v", *conflictInfo)
		return conflictInfo, nil
	}

	return nil, nil
}

func (c *Client) recommendContainerCIDRByVersion(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType,
	VPCCIDR string, privateNetCIDRCandidates []ccesdk.PrivateNetString, maskSize int,
	clusters []*models.Cluster, routeRules []vpc.RouteRule) []string {
	// 用户提供的私有网段排序，确定搜索顺序
	sortedCandidateCIDRs := sortContainerCIDRCandidates(privateNetCIDRCandidates, VPCCIDR)
	logger.Infof(ctx, "RecommendContainerCIDR: sortContainerCIDRCandidates got %v", sortedCandidateCIDRs)

	recommendedCIDRs := c.recommendContainerCIDR(ctx, ipVersion, sortedCandidateCIDRs, maskSize, VPCCIDR, clusters, routeRules)
	return recommendedCIDRs
}

func (c *Client) recommendClusterIPCIDRByVersion(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType,
	VPCCIDR, containerCIDR string, clusterMaxServiceNum int, privateNetCIDRCandidates []ccesdk.PrivateNetString) []string {
	var (
		ipv4CandidateCIDRs []string
		ipv6CandidateCIDRs []string
		recommendedCIDRs   []string
	)
	sortedCandidateCIDRs := sortClusterIPCIDRCandidates(privateNetCIDRCandidates, VPCCIDR, containerCIDR)
	logger.Infof(ctx, "RecommendClusterIPCIDR: sortClusterIPCIDRCandidates got %v", sortedCandidateCIDRs)

	for _, candidateCIDR := range sortedCandidateCIDRs {
		ip, _, _ := net.ParseCIDR(candidateCIDR)
		if ip.To4() != nil && ipVersion == ccetypes.ContainerNetworkIPTypeIPv4 || ipVersion == ccetypes.ContainerNetworkIPTypeDualStack {
			ipv4CandidateCIDRs = append(ipv4CandidateCIDRs, candidateCIDR)
		} else if ipVersion == ccetypes.ContainerNetworkIPTypeIPv6 || ipVersion == ccetypes.ContainerNetworkIPTypeDualStack {
			ipv6CandidateCIDRs = append(ipv6CandidateCIDRs, candidateCIDR)
		}
	}
	if len(ipv4CandidateCIDRs) > 0 {
		maskSize := 32 - int(math.Ceil(math.Log2(float64(clusterMaxServiceNum))))
		recommendedCIDRs = append(recommendedCIDRs, c.recommendClusterIPCIDR(ctx, sortedCandidateCIDRs, maskSize, VPCCIDR, containerCIDR)...)
	}
	if len(ipv6CandidateCIDRs) > 0 {
		maskSize := 128 - int(math.Ceil(math.Log2(float64(clusterMaxServiceNum))))
		recommendedCIDRs = append(recommendedCIDRs, c.recommendClusterIPCIDR(ctx, sortedCandidateCIDRs, maskSize, VPCCIDR, containerCIDR)...)
	}
	return recommendedCIDRs
}

// checkContainerCIDR 检查容器网段
// 1.检查容器网段范围合法性
// 2.检查容器网络是否与节点网络冲突
// 3.检查容器网络是否与用户同 VPC 下集群的容器网络冲突
// 4.检查容器网络是否和 VPC 路由中的目的地址网络冲突
func (c *Client) checkContainerCIDR(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType, containerCIDR ContainerCIDR, vpcCIDR string, clusters []*models.Cluster, routeRules []vpc.RouteRule) (*ccesdk.NetworkConflictInfo, error) {
	// 1. 检查容器网络 CIDR 范围
	if err := checkNetRange(string(containerCIDR)); err != nil {
		msg := fmt.Sprintf("error checking container cidr: %v", err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}

	// 2. 检查容器网络是否与节点网段冲突
	conflictInfo, err := containerCIDR.isConflictWithVPCCIDR(ctx, vpcCIDR)
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and node cidr: %v", err)
		logger.Errorf(ctx, msg)
		return nil, err
	}
	if conflictInfo != nil {
		return conflictInfo, nil
	}

	// 3. 检查容器网络是否和已有集群容器网络冲突
	conflictInfo, err = containerCIDR.isConflictWithExistedClusters(ctx, ipVersion, clusters)
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and existed clusters: %v", err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}
	if conflictInfo != nil {
		return conflictInfo, nil
	}

	// 4. 检查容器网络是否和 VPC 路由中的目的地址网络冲突
	conflictInfo, err = containerCIDR.isConflictWithVPCRoutes(ctx, vpcCIDR, routeRules)
	if err != nil {
		msg := fmt.Sprintf("error checking container cidr and vpc routes: %v", err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}
	if conflictInfo != nil {
		return conflictInfo, nil
	}

	return nil, nil
}

// checkClusterIPCIDR 检查ClusterIP网段是否和集群容器、节点网段冲突
func (c *Client) checkClusterIPCIDR(ctx context.Context, clusterIPCIDR ClusterIPCIDR, vpcCIDR string, containerCIDR string) (*ccesdk.NetworkConflictInfo, error) {
	// 1. 检查 ClusterIP CIDR 范围
	if err := checkClusterIPRange(string(clusterIPCIDR)); err != nil {
		msg := fmt.Sprintf("error checking ClusterIP cidr: %v", err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}

	// 2. 检查 ClusterIP 网段是否与节点网段冲突
	conflictInfo, err := clusterIPCIDR.isConflictWithNodeCIDR(ctx, vpcCIDR)
	if err != nil {
		msg := fmt.Sprintf("error checking clusterip cidr and node cidr: %v", err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}
	if conflictInfo != nil {
		return conflictInfo, nil
	}

	// 3. 检查 ClusterIP 网段是否与容器网段冲突
	conflictInfo, err = clusterIPCIDR.isConflictWitContainerCIDR(ctx, containerCIDR)
	if err != nil {
		msg := fmt.Sprintf("error checking clusterip cidr and container cidr: %v", err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}
	if conflictInfo != nil {
		return conflictInfo, nil
	}

	return nil, nil
}

// calculateMaxNodeNum 计算最大节点数
func calculateMaxNodeNum(containerCIDR string, maxPodsPerNode int) (int, error) {
	_, ipnet, err := net.ParseCIDR(containerCIDR)
	if err != nil {
		return 0, fmt.Errorf("invalid CIDR format: %s", containerCIDR)
	}

	ones, bits := ipnet.Mask.Size()
	containerIPNum := uint64(0x01) << (bits - ones)
	if containerIPNum < uint64(maxPodsPerNode) {
		return 0, fmt.Errorf("container cidr [%s] too small, cannot allocate node", containerCIDR)
	}
	// TODO: overflow if IPv6
	return int(containerIPNum / uint64(maxPodsPerNode)), nil
}

// checkClusterIPRange 检查 clusterIP 的范围是否符合规范
func checkClusterIPRange(cidr string) error {
	// 合法
	if !util.IsValidCIDR(cidr) {
		return fmt.Errorf("invalid CIDR format: %s", cidr)
	}

	return isValidClusterIP(cidr)
}

func isValidClusterIP(cidr string) error {
	if util.IsPrivateNetCIDRString(cidr) {
		// 私有网段
		return nil
	} else if util.IsPreserveClusterIP(cidr) {
		// 保留的 cluster ip
		return nil
	}

	return fmt.Errorf("illegal CIDR: %s, must be private addresses", cidr)
}

// checkNetRange 检查容器网络的范围是否符合规范
func checkNetRange(cidr string) error {
	// 合法
	if !util.IsValidCIDR(cidr) {
		return fmt.Errorf("invalid CIDR format: %s", cidr)
	}

	// 私有网段
	if !util.IsPrivateNetCIDRString(cidr) {
		return fmt.Errorf("illegal CIDR: %s, must be private addresses", cidr)
	}

	return nil
}

// calculateClusterCIDRMaskSize 计算 IPv4/IPv6 的容器网段掩码
// k8s v1.16 对于 IPv4/IPv6 DualStack 只有 --node-cidr-mask-size 一个共同参数
// k8s v1.17 针对 IPv6 新增 --node-cidr-mask-size-ipv4|--node-cidr-mask-size-ipv6 flag
// The subnet mask size cannot be greater than 16 more than the cluster mask size
// https://github.com/kubernetes/kubernetes/issues/44918
// 只要节点数不超过 65536	就满足 node-cidr-mask-size - cluster-cidr-mask-size <= 16
// Note: 考虑如下场景，对于一个租户如果存在 1.16 的 IPv6 的集群，cluster-cidr 可能为 fc00::00/16 (空间很大)
// 在创建 v1.17 的集群时候，如果按照 maxPods 推算，推荐的 cluster-cidr-mask-size 可能为 112, 这样就会导致在 fc00::00/16 空间里搜寻超时，
// 所以针对 v1.17+ 的集群依然采用 v1.16 的策略 —— 每个节点上 node-cidr-mask-size 小（ max-pods 大）。
// 对于每一个租户，如果集群规模最大节点数 256，每个节点容器数 256，容器网段掩码为16，fd00::/8 的空间能够支持 2^9 个集群是足够的。
// IPv6下地址资源非常充足，所以我们为每个节点最小分配一个112位的子网，即占用 2^16 个地址空间
func calculateClusterCIDRMaskSize(maxPodsPerNode, clusterMaxNodeNum int, k8sVersion ccetypes.K8SVersion) (int, int, error) {
	var clusterCIDRMaskSize, clusterCIDRMaskSizeIPv6 int

	// maxPodsPerNode 取值范围: [32, 64, 128, 256, 512]，已经被校验过
	// 为防止用户提交的 clusterMaxNodeNum 过大导致 maxPodNum 溢出，这里进行校验
	if math.MaxInt64/clusterMaxNodeNum < maxPodsPerNode {
		return 0, 0, fmt.Errorf("cluster max node num %v is too large", clusterMaxNodeNum)
	}
	maxPodNum := maxPodsPerNode * clusterMaxNodeNum
	maskSize := int(math.Ceil(math.Log2(float64(maxPodNum))))
	// IPv4
	clusterCIDRMaskSize = 32 - maskSize

	// IPv6 最小保留112位的子网
	clusterCIDRMaskSizeIPv6 = int(math.Min(112, float64(128-maskSize)))
	return clusterCIDRMaskSize, clusterCIDRMaskSizeIPv6, nil
}

// isConflictWithVPCCIDR 检查容器网段是否与VPC网段(节点网段)冲突
func (cidr ContainerCIDR) isConflictWithVPCCIDR(ctx context.Context, nodeCIDR string) (*ccesdk.NetworkConflictInfo, error) {
	if util.IsConflictCIDRString(string(cidr), nodeCIDR) {
		return &ccesdk.NetworkConflictInfo{
			IsConflict: true,
			ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with node cidr[%s]", string(cidr), nodeCIDR),
			ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
				ConflictType:     ccesdk.ContainerCIDRAndNodeCIDRConflict,
				ConflictNodeCIDR: &ccesdk.ConflictNodeCIDR{NodeCIDR: nodeCIDR},
			},
		}, nil
	}
	return nil, nil
}

// isConflictWithExistedClusters 检查容器网络是否与同 VPC 已有集群容器网络冲突
func (cidr ContainerCIDR) isConflictWithExistedClusters(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType, clusters []*models.Cluster) (*ccesdk.NetworkConflictInfo, error) {
	if len(clusters) == 0 {
		return nil, nil
	}

	for _, cluster := range clusters {
		clusterID := cluster.Spec.ClusterID
		clusterPodCIDR := cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR
		if ipVersion == ccetypes.ContainerNetworkIPTypeIPv6 {
			clusterPodCIDR = cluster.Spec.ContainerNetworkConfig.ClusterPodCIDRIPv6
		}

		// 防止其他集群的容器网段有问题
		if !util.IsValidCIDR(clusterPodCIDR) {
			logger.Warnf(ctx, "error parsing pod cidr %s of cluster %s", clusterPodCIDR, clusterID)
			continue
		}

		// 容器网段与其他集群容器网段冲突
		if util.IsConflictCIDRString(string(cidr), clusterPodCIDR) {
			return &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("container cidr [%s] conflicts with cluster %s container cidr[%s]", string(cidr), clusterID, clusterPodCIDR),
				ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
					ConflictType: ccesdk.ContainerCIDRAndExistedClusterContainerCIDRConflict,
					ConflictCluster: &ccesdk.ConflictCluster{
						ClusterID:     clusterID,
						ContainerCIDR: clusterPodCIDR,
					},
				},
			}, nil
		}
	}

	return nil, nil
}

// isConflictWithVPCRoutes 检查容器网络是否与 VPC 路由冲突
func (cidr ContainerCIDR) isConflictWithVPCRoutes(ctx context.Context, vpcCIDR string, routeRules []vpc.RouteRule) (*ccesdk.NetworkConflictInfo, error) {
	if len(routeRules) == 0 {
		return nil, nil
	}

	for _, route := range routeRules {
		if !util.IsValidCIDR(route.DestinationAddress) {
			logger.Warnf(ctx, "error parsing destination address %s of route rule %s", route.DestinationAddress, route.RouteRuleID)
			continue
		}

		// 目的地址不冲突，则肯定不冲突
		if !util.IsConflictCIDRString(string(cidr), route.DestinationAddress) {
			continue
		}
		// VPC路由目的地址 0.0.0.0/0 导致冲突，不判定为冲突(出公网路由)
		if route.DestinationAddress == "0.0.0.0/0" || route.DestinationAddress == "::/0" {
			continue
		}

		return &ccesdk.NetworkConflictInfo{
			IsConflict: true,
			ErrMsg:     fmt.Sprintf("container cidr [%s] conflict with VPC route with destination address [%s]", string(cidr), route.DestinationAddress),
			ContainerCIDRConflict: &ccesdk.ContainerCIDRConflict{
				ConflictType:     ccesdk.ContainerCIDRAndVPCRouteConflict,
				ConflictVPCRoute: &ccesdk.ConflictVPCRoute{RouteRule: route},
			},
		}, nil
	}

	return nil, nil
}

// isConflictWithVPCCIDR 检查 ClusterIP 网段是否与节点网段冲突
func (cidr ClusterIPCIDR) isConflictWithNodeCIDR(ctx context.Context, nodeCIDR string) (*ccesdk.NetworkConflictInfo, error) {
	if util.IsConflictCIDRString(string(cidr), nodeCIDR) {
		return &ccesdk.NetworkConflictInfo{
			IsConflict: true,
			ErrMsg:     fmt.Sprintf("clusterip cidr [%s] conflicts with node cidr[%s]", string(cidr), nodeCIDR),
			ClusterIPCIDRConflict: &ccesdk.ClusterIPCIDRConflict{
				ConflictType:     ccesdk.ClusterIPCIDRAndNodeCIDRConflict,
				ConflictNodeCIDR: &ccesdk.ConflictNodeCIDR{NodeCIDR: nodeCIDR},
			},
		}, nil
	}
	return nil, nil
}

// isConflictWitContainerCIDR 检查 ClusterIP 网段是否与容器网段冲突
func (cidr ClusterIPCIDR) isConflictWitContainerCIDR(ctx context.Context, containerCIDR string) (*ccesdk.NetworkConflictInfo, error) {
	if util.IsConflictCIDRString(string(cidr), containerCIDR) {
		return &ccesdk.NetworkConflictInfo{
			IsConflict: true,
			ErrMsg:     fmt.Sprintf("clusterip cidr [%s] conflicts with container cidr[%s]", string(cidr), containerCIDR),
			ClusterIPCIDRConflict: &ccesdk.ClusterIPCIDRConflict{
				ConflictType:          ccesdk.ClusterIPCIDRAndContainerCIDRConflict,
				ConflictContainerCIDR: &ccesdk.ConflictContainerCIDR{ContainerCIDR: containerCIDR},
			},
		}, nil
	}
	return nil, nil
}

// sortContainerCIDRCandidates 根据用户传递的候选网段排序
// IPv4 排序的规则：
// 1. 按照 "10.0.0.0/8", "**********/12", "***********/16" 的次序(地址空间递减)
// 2. 节点网段放在最后
// IPv6 排序的规则：
// 1. 只有 fd00::/8 无需排序
func sortContainerCIDRCandidates(privateNetCIDRCandidates []ccesdk.PrivateNetString, vpcCIDR string) []string {
	sortedCIDRs := []string{}
	lastCIDRToRecommend := ""
	for _, cidr := range privateNetCIDRCandidates {
		if !util.IsConflictCIDRString(vpcCIDR, string(cidr)) {
			sortedCIDRs = append(sortedCIDRs, string(cidr))
		} else {
			lastCIDRToRecommend = string(cidr)
		}
	}
	// 根据字典序排序
	sort.Strings(sortedCIDRs)
	if lastCIDRToRecommend != "" {
		sortedCIDRs = append(sortedCIDRs, lastCIDRToRecommend)
	}
	return sortedCIDRs
}

func (c *Client) recommendContainerCIDR(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType,
	sortedCandidateCIDRs []string, maskSize int, vpcCIDR string,
	clusters []*models.Cluster, routeRules []vpc.RouteRule) []string {
	recommendedCIDRs := []string{}
	if maskSize <= 0 {
		return recommendedCIDRs
	}

	// 检查CIDR是否有冲突
	checkCIDR := func(ctx context.Context, ipVersion ccetypes.ContainerNetworkIPType, subnet *net.IPNet, vpcCIDR string, clusters []*models.Cluster, routeRules []vpc.RouteRule) {
		conflict, err := c.checkContainerCIDR(ctx, ipVersion, ContainerCIDR(subnet.String()), vpcCIDR, clusters, routeRules)
		logger.Infof(ctx, "recommendContainerCIDR: check subnet %v with conflict: %v, error: %v", subnet, conflict, err)
		// 出错或冲突就下一个子网
		if err != nil || conflict != nil {
			return
		}
		recommendedCIDRs = append(recommendedCIDRs, subnet.String())
	}

	// IPv4: 依次从["10.0.0.0/8", "**********/12", "***********/16"]三个大网段中推荐
	// IPv6: 从 fd00::/8 中推荐
	for _, cidr := range sortedCandidateCIDRs {
		logger.Infof(ctx, "recommendContainerCIDR: start to search subnet in cidr %v", cidr)
		_, ipnet, _ := net.ParseCIDR(cidr)
		mask := net.CIDRMask(maskSize, 8*len(ipnet.IP))

		if netutil.IsIPv4CIDR(ipnet) {
			subnet := &net.IPNet{IP: ipnet.IP.Mask(mask), Mask: mask}
			for ; util.IsPrivateNetCIDR(subnet); subnet, _ = gocidr.NextSubnet(subnet, maskSize) {
				checkCIDR(ctx, ipVersion, subnet, vpcCIDR, clusters, routeRules)
				// 超出最大推荐数量
				if len(recommendedCIDRs) >= maxRecommendedCIDRNum {
					break
				}
			}
		} else {
			for i := 0; i < maxRecommendedCIDRNum; i++ {
				checkCIDR(ctx, ipVersion, util.GenerateRandomIPv6SubCIDR(ipnet, maskSize), vpcCIDR, clusters, routeRules)
			}
		}
		// 超出最大推荐数量
		if len(recommendedCIDRs) >= maxRecommendedCIDRNum {
			break
		}
	}
	return recommendedCIDRs
}

// sortClusterIPCIDRCandidates 根据用户传递的候选网段排序
// IPv4 排序的规则：
// 1. 按照 "***********/16", "**********/12", "10.0.0.0/8" 的次序(地址空间递增)
// 2. 节点网段、容器网段滞后
// IPv6 排序的规则：
// 1. 只有 fd00::/8 无需排序
func sortClusterIPCIDRCandidates(privateNetCIDRCandidates []ccesdk.PrivateNetString, vpcCIDR, containerCIDR string) []string {
	sortedCIDRs := []string{}
	conflictCIDRs := []string{}
	for _, cidr := range privateNetCIDRCandidates {
		if util.IsConflictCIDRString(string(cidr), vpcCIDR) || util.IsConflictCIDRString(string(cidr), containerCIDR) {
			conflictCIDRs = append(conflictCIDRs, string(cidr))
		} else {
			sortedCIDRs = append(sortedCIDRs, string(cidr))
		}
	}
	sort.Sort(sort.Reverse(sort.StringSlice(sortedCIDRs)))
	sort.Sort(sort.Reverse(sort.StringSlice(conflictCIDRs)))
	sortedCIDRs = append(sortedCIDRs, conflictCIDRs...)
	return sortedCIDRs
}

func (c *Client) recommendClusterIPCIDR(ctx context.Context, sortedCandidateCIDRs []string, maskSize int, vpcCIDR, containerCIDR string) []string {
	recommendedCIDRs := []string{}
	if maskSize <= 0 {
		return recommendedCIDRs
	}

	// 检查CIDR是否有冲突
	checkCIDR := func(ctx context.Context, subnet *net.IPNet, vpcCIDR string) bool {
		// 超出最大推荐数量
		if len(recommendedCIDRs) >= maxRecommendedCIDRNum {
			return false
		}
		conflict, err := c.checkClusterIPCIDR(ctx, ClusterIPCIDR(subnet.String()), vpcCIDR, containerCIDR)
		logger.Infof(ctx, "recommendClusterIPCIDR: check subnet %v with conflict: %v, error: %v", subnet, conflict, err)
		// 出错或冲突就下一个子网
		if err != nil || conflict != nil {
			return true
		}
		recommendedCIDRs = append(recommendedCIDRs, subnet.String())
		return true
	}

	// IPv4: 依次从["***********/16", "**********/12", "10.0.0.0/8"]三个大网段中推荐
	// IPv6: 从 fd00::/8 中推荐
	// IPv6 空间很大，随机分配
	for _, cidr := range sortedCandidateCIDRs {
		logger.Infof(ctx, "recommendClusterIPCIDR: start to search subnet in cidr %v", cidr)
		_, ipnet, _ := net.ParseCIDR(cidr)
		_, lastIP := gocidr.AddressRange(ipnet)
		mask := net.CIDRMask(maskSize, 8*len(ipnet.IP))

		if netutil.IsIPv4CIDR(ipnet) {
			subnet := &net.IPNet{IP: lastIP.Mask(mask), Mask: mask}
			for ; util.IsPrivateNetCIDR(subnet); subnet, _ = gocidr.PreviousSubnet(subnet, maskSize) {
				if !checkCIDR(ctx, subnet, vpcCIDR) {
					break
				}
			}
		} else {
			for i := 0; i < maxRecommendedCIDRNum; i++ {
				if !checkCIDR(ctx, util.GenerateRandomIPv6SubCIDR(ipnet, maskSize), vpcCIDR) {
					break
				}
			}
		}
		// 超出最大推荐数量
		if len(recommendedCIDRs) >= maxRecommendedCIDRNum {
			break
		}
	}
	return recommendedCIDRs
}

// CheckClusterIPCIDR - 检查 VPC-CNI 模式 ClusterIP CIDR
func (c *Client) CheckClusterIPCIDR(ctx context.Context, req *ccesdk.CheckClusterIPCIDRequest) (*ccesdk.CheckClusterIPCIDRResponse, error) {
	if req == nil {
		return nil, fmt.Errorf("CheckClusterIPCIDRequest is nil")
	}

	// 检查 ClusterIP CIDR 合法性
	err := req.Validate()
	if err != nil {
		logger.Errorf(ctx, "CheckClusterIPCIDRequest Validate failed: %v", err)
		return nil, err
	}

	// 检查 ClusterIP CIDR 是否与 VPC CIDR 冲突
	hasIPv4 := req.IPVersion == ccetypes.ContainerNetworkIPTypeIPv4 || req.IPVersion == ccetypes.ContainerNetworkIPTypeDualStack
	hasIPv6 := req.IPVersion == ccetypes.ContainerNetworkIPTypeIPv6 || req.IPVersion == ccetypes.ContainerNetworkIPTypeDualStack

	resp := &ccesdk.CheckClusterIPCIDRResponse{
		IsConflict: false,
		ErrMsg:     "",
	}

	if hasIPv4 == true {
		conflictInfo, err := checkClusterIPCIDR(ctx, ClusterIPCIDR(req.ClusterIPCIDR), req.VPCCIDR)
		if err != nil {
			logger.Errorf(ctx, "check IPv4 ClusterIPCIDR failed: %v", err)
			return nil, err
		}
		if conflictInfo != nil && conflictInfo.IsConflict {
			resp.IsConflict = true
			resp.ErrMsg = conflictInfo.ErrMsg
			return resp, nil
		}
	}

	if hasIPv6 == true {
		conflictInfo, err := checkClusterIPCIDR(ctx, ClusterIPCIDR(req.ClusterIPCIDRIPv6), req.VPCCIDRIPv6)
		if err != nil {
			logger.Errorf(ctx, "check IPv6 ClusterIPCIDR failed: %v", err)
			return nil, err
		}
		if conflictInfo != nil && conflictInfo.IsConflict {
			resp.IsConflict = true
			resp.ErrMsg = conflictInfo.ErrMsg
			return resp, nil
		}
	}

	return resp, nil
}

// checkClusterIPCIDR - 检查 ClusterIP CIDR 是否与 VPC CIDR 冲突
func checkClusterIPCIDR(ctx context.Context, clusterIPCIDR ClusterIPCIDR, vpcCIDR string) (*ccesdk.NetworkConflictInfo, error) {
	// 检查 ClusterIP CIDR 范围
	if err := checkClusterIPRange(string(clusterIPCIDR)); err != nil {
		msg := fmt.Sprintf("checkNetRange of clusterIPCIDR %s failed: %v", clusterIPCIDR, err)
		logger.Errorf(ctx, msg)
		return nil, fmt.Errorf(msg)
	}

	// 检查 ClusterIP 网段是否与节点网段冲突
	conflictInfo, err := clusterIPCIDR.isConflictWithNodeCIDR(ctx, vpcCIDR)
	if err != nil {
		logger.Errorf(ctx, "clusterIPCIDR.isConflictWithNodeCIDR failed: %v", err)
		return nil, err
	}

	return conflictInfo, nil
}

// CheckAuxiliaryCIDR - 检查容器辅助网段 CIDR 是否与 VPC 路由冲突
func (c *Client) CheckAuxiliaryIPCIDR(ctx context.Context, req *ccesdk.CheckAuxiliaryIPCIDRRequest) (*ccesdk.CheckAuxiliaryIPCIDRResponse, error) {
	if req == nil {
		return nil, errors.New("CheckAuxiliaryCIDRRequest is nil")
	}

	// 检查 ClusterIP CIDR 合法性
	err := req.Validate()
	if err != nil {
		logger.Errorf(ctx, "CheckAuxiliaryIPCIDRequest Validate failed: %v", err)
		return nil, err
	}

	resp := &ccesdk.CheckAuxiliaryIPCIDRResponse{
		IsConflict:  false,
		ErrMsg:      "",
		IsAuxiliary: true,
	}

	if util.IsSubnet(req.ContainerCIDR, req.VPCCIDR) {
		resp.IsAuxiliary = false
	}

	// 在开启ebpf加速时候，目前对所有的辅助网段保持禁用，后续会验证改进放开
	// 故结合上面resp的初始值：如果是辅助网段，则不需要做冲突检查，直接返回即可；
	// 如果不是辅助网段，则一定不会与VPC路由冲突，直接返回不冲突即可。
	if req.IsEBPF {
		return resp, nil
	}

	// 只有辅助网段才有可能与现存vpc路由冲突
	if resp.IsAuxiliary {
		clusters, err := c.models.GetClustersByVPC(ctx, c.accountID, req.VPCID)
		if err != nil {
			logger.Errorf(ctx, "GetClustersByVPC failed: %v", err)
			return nil, err
		}

		conflictInfo := checkAuxiliaryIPCIDR(ctx, req, clusters)

		if conflictInfo != nil && conflictInfo.IsConflict {
			resp.IsConflict = true
			resp.ErrMsg = conflictInfo.ErrMsg
		}
	}

	return resp, nil
}

func checkAuxiliaryIPCIDR(ctx context.Context, req *ccesdk.CheckAuxiliaryIPCIDRRequest, clusters []*models.Cluster) (conflictInfo *ccesdk.NetworkConflictInfo) {
	hasIPv4 := req.IPVersion == ccetypes.ContainerNetworkIPTypeIPv4 || req.IPVersion == ccetypes.ContainerNetworkIPTypeDualStack
	hasIPv6 := req.IPVersion == ccetypes.ContainerNetworkIPTypeIPv6 || req.IPVersion == ccetypes.ContainerNetworkIPTypeDualStack

	if hasIPv4 {
		conflictInfo = isConflictWithExistedClusters(ctx, req.ContainerCIDR, clusters, ccetypes.ContainerNetworkIPTypeIPv4)
	}
	if conflictInfo != nil && conflictInfo.IsConflict {
		return conflictInfo
	}

	if hasIPv6 {
		conflictInfo = isConflictWithExistedClusters(ctx, req.ContainerCIDRIPv6, clusters, ccetypes.ContainerNetworkIPTypeIPv6)
	}

	return conflictInfo
}

func isConflictWithExistedClusters(ctx context.Context, cidr string, clusters []*models.Cluster, ipVersion ccetypes.ContainerNetworkIPType) *ccesdk.NetworkConflictInfo {
	if len(clusters) == 0 {
		return nil
	}

	for _, cluster := range clusters {
		// 只需要检查vpc-route模式的集群
		if cluster.Spec.ContainerNetworkConfig.Mode != ccetypes.ContainerNetworkModeVPCRoute &&
			cluster.Spec.ContainerNetworkConfig.Mode != ccetypes.ContainerNetworkModeVPCRouteVeth &&
			cluster.Spec.ContainerNetworkConfig.Mode != ccetypes.ContainerNetworkModeVPCRouteIPVlan &&
			cluster.Spec.ContainerNetworkConfig.Mode != ccetypes.ContainerNetworkModeVPCRouteAutoDetect {
			continue
		}
		clusterID := cluster.Spec.ClusterID
		var existedClusterPodCIDR string
		if ipVersion == ccetypes.ContainerNetworkIPTypeIPv4 {
			existedClusterPodCIDR = cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR
		} else {
			existedClusterPodCIDR = cluster.Spec.ContainerNetworkConfig.ClusterPodCIDRIPv6
		}

		// 容器网段与其他集群容器网段冲突
		if util.IsConflictCIDRString(cidr, existedClusterPodCIDR) {
			return &ccesdk.NetworkConflictInfo{
				IsConflict: true,
				ErrMsg:     fmt.Sprintf("auxiliary cidr [%s] conflicts with existed vpc-route cluster %s container cidr[%s]", cidr, clusterID, existedClusterPodCIDR),
			}
		}
	}

	return nil
}
