package cluster

import (
	"context"
	_ "embed"
	"errors"
	"fmt"
	"net"
	"reflect"
	"strings"
	"testing"

	"github.com/golang/mock/gomock"
	pluginclients "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	appsv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/dynamic"
	dynamicfake "k8s.io/client-go/dynamic/fake"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	appsv1Client "k8s.io/client-go/kubernetes/typed/apps/v1"
	"k8s.io/client-go/rest"
	"sigs.k8s.io/controller-runtime/pkg/scheme"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	vpcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc/mock"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/addon"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"
)

var (
	sampleKubeConfigFile = `
apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVWm5TK3pDbUxhcnFwNHQ4RWNnb0RDOUZvcDlBd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdIaGNOTWpJd09ERXdNRGt4TlRBd1doY05Nekl3T0RBM01Ea3hOVEF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ05WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFKMkhpdHdZdml6a2RiSlAKVXlZUDNiN210K3ZoYjlLUEVIQkc3WVFJRjZsUG9aQUlHVWtoMWNrQU9vbFlnRFBHVGFiRzZ5b21BMVZDZzMvMAoxcm9xN1hBcU55bEIyWDdjU0hPTEowd2VXcVJEY3duWUVRTlB4bFBaYjdraGt6WFc0ZXF0N0xMUHU4VWc3VExwCmhjUzJQNk8wU3UzNFE1Y3RDYmwraDcxc0kxdkJGYWFrNW9pVHRmMWxPRlZxaFFrVjY2UUMxYjdMbWdIdU42eGQKNlhacHY3N045V2VHWWlGV1lRdW5DT3QxSW9tZDZ4Nzd6dzVmSHQ3QzdLeTkzMjd6MldMNTAzaUs2bUswaTRlWgoyV1JZeGtaRTlRMWxPNVIxMFlnZjFnWTlkcTBlKzRYaVU5dDBrbFBRWlVXZmNCbXp5a2NQYmd4TGY1cWZtQkF6Cmd1VzRZNEVDQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHcKSFFZRFZSME9CQllFRk1zcUt1Rk9aN2RPUmowMUV0cW5ZVitkWGFyc01BMEdDU3FHU0liM0RRRUJDd1VBQTRJQgpBUUNTRnpXOGg1TW44QlFJWE13aTdySDVCQlpKU285YkMxZTJlbUQ1WFpYVjFtdlAyZUtXUVprOUlEa1Z5VUJrCkNXOVhKVG1zYmFJMW9tYXdHc2hPanlzNWRRUml2NXViREpqdzdyUmxtOWpsTDhobGJaNFFUbVlYUGdQRXMvaTgKVGxwWEFBdGora3VZTzJoSDV2RkNnbWYyTitmdVJHd1R5c0RCTWFGeVJNR2doMWhndzcrU2xwdCs1S2xmdHlXUQpEWEhNTm9CLzdsOEJKTVBodGZlTEV0TUthbHNwOW4wbW1DTWpYc2EzYW5ORThTbnJtWjhqcFI3bW5HSEZrcFZCCk9RdFU1M242dnFwQm5pVGN0amJMQUZnTHE5TVNzRTdDdG82clU2cDJnaTlhTm40TWlQb3lYUS96djdXdHA3Z2kKQzBaZjF2Y0t6VklIS0dMQ1hKdTgxalZCCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    server: https://***********:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: eca97e148cb74e9683d7b7240829d1ffff
  name: eca97e148cb74e9683d7b7240829d1ff@kubernetes
current-context: eca97e148cb74e9683d7b7240829d1ff@kubernetes
kind: Config
preferences: {}
users:
- name: eca97e148cb74e9683d7b7240829d1ff
  user:
    client-certificate-data: 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
    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
`

	sampleAgentConfigFile = `cniMode: vpc-secondary-ip-auto-detect
resyncPeriod: 60s
workers: 1
cniConfig: {}
cce:
  region: bj
  clusterID: cce-xxxxx
  vpcID: vpc-xxxxxx
  routeController:
    enableVPCRoute: false
    enableStaticRoute: false
  eniController:
    eniSubnetList:
      - EniSubA
      - EniSubB
    routeTableOffset: 127
    eniSyncPeriod: 180s
  podSubnetController:
    subnetList:
      - EniSubB`

	sampleAgentConfigFileWithoutEniSubnet = `cniMode: vpc-secondary-ip-auto-detect
resyncPeriod: 60s
workers: 1
cniConfig: {}
cce:
  region: bj
  clusterID: cce-xxxxx
  vpcID: vpc-xxxxxx
  podSubnetController:
    subnetList:
      - EniSubB`

	//go:embed mock/mock-vpc-eni-config.yaml
	simpleVPCENIConfig []byte
)

func Test_service_GetClusterExtraInfo(t *testing.T) {
	type fields struct {
		accountID          string
		clients            *clients.Clients
		models             models.Interface
		adminKubeClient    kubernetes.Interface
		adminDynamicClient dynamic.Interface
	}
	type args struct {
		ctx       context.Context
		clusterID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccesdk.ClusterExtraInfo
		wantErr bool
	}{
		{
			name: "正常情况：托管集群",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         true,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					models: model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want: &ccesdk.ClusterExtraInfo{
				LBServiceSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				Subnets: []ccesdk.Subnet{
					{
						SubnetID:      "SubnetID",
						SubnetName:    "Name",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneA,
					},
				},
			},
			wantErr: false,
		},

		{
			name: "正常情况：独立部署集群",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
							MasterType:            ccetypes.MasterTypeCustom,
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					models: model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want: &ccesdk.ClusterExtraInfo{
				MasterBLBSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				LBServiceSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				Subnets: []ccesdk.Subnet{
					{
						SubnetID:      "SubnetID",
						SubnetName:    "Name",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneA,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "正常情况：独立部署集群,容器化部署 Master",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
							MasterType:            ccetypes.MasterTypeContainerizedCustom,
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					models: model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want: &ccesdk.ClusterExtraInfo{
				MasterBLBSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				LBServiceSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				Subnets: []ccesdk.Subnet{
					{
						SubnetID:      "SubnetID",
						SubnetName:    "Name",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneA,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "正常情况：独立部署 VPC 路由集群",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeVPCRouteVeth,
							LBServiceVPCSubnetID: "SubnetID",
							// ENIVPCSubnetIDs: map[internalvpc.AvailableZone][]string{
							// 	internalvpc.ZoneA: []string{"ENISubnetID"},
							// },
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "ENISubnetID",
								Name:       "ENIName",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					models: model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want: &ccesdk.ClusterExtraInfo{
				MasterBLBSubnet: nil,
				LBServiceSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				Subnets: []ccesdk.Subnet{
					{
						SubnetID:      "SubnetID",
						SubnetName:    "Name",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneA,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "正常情况：serverless集群",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				metaclient := metamock.NewMockInterface(ctrl)

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterType: ccetypes.ClusterTypeServerless,
						VPCID:       "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
							ServerlessMasterOption: ccetypes.ServerlessMasterOption{
								VKSubnets: []ccetypes.VKSubnetType{
									{
										AvailableZone: internalvpc.ZoneA,
										SubnetID:      "SubnetID",
									},
									{
										AvailableZone: internalvpc.ZoneB,
										SubnetID:      "SubnetID-2",
									},
								},
							},
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeVPCCNI,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "SubnetID-2",
								Name:       "Name-2",
								ZoneName:   "cn-bj-b",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}
				subnet2 := &vpc.Subnet{
					SubnetID:   "SubnetID-2",
					Name:       "Name-2",
					ZoneName:   "cn-bj-b",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					metaclient.EXPECT().GetCluster(gomock.Any(), models.DefaultNamespace, "ClusterID", &metav1.GetOptions{}).Return(&ccev1.Cluster{
						Spec: *(cluster.Spec),
					}, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID-2", nil).Return(subnet2, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient:     vpcclient,
						STSClient:     stsclient,
						MetaK8SClient: metaclient,
					},
					models: model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want: &ccesdk.ClusterExtraInfo{
				MasterBLBSubnet: nil,
				LBServiceSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				Subnets: []ccesdk.Subnet{
					{
						SubnetID:      "SubnetID",
						SubnetName:    "Name",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneA,
					},
					{
						SubnetID:      "SubnetID-2",
						SubnetName:    "Name-2",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneB,
					},
				},
			},
			wantErr: false,
		},
		{
			name: "独立部署 VPC-Hybrid 集群, agent configmap not found",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				adminKubeClient := fake.NewSimpleClientset()

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubA",
								Name:       "ENIName",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubB",
								Name:       "ENIName",
								ZoneName:   "cn-bj-b",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubC",
								Name:       "ENIName",
								ZoneName:   "cn-bj-c",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					adminKubeClient: adminKubeClient,
					models:          model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "独立部署 VPC-Hybrid 集群, configmap 没有 key config",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				adminKubeClient := fake.NewSimpleClientset()
				adminDynamicClient, err := newFakeClientWithSubnet([]Subnet{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "EniSubC",
							Namespace: "default",
						},
					},
				})
				if err != nil {
					t.Errorf("newFakeClientWithSubnet: %v", err)
				}

				_, err = adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"configgggg": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubA",
								Name:       "ENIName",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubB",
								Name:       "ENIName",
								ZoneName:   "cn-bj-b",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubC",
								Name:       "ENIName",
								ZoneName:   "cn-bj-c",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
					models:             model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			wantErr: true,
		},
		{
			name: "独立部署 VPC-Hybrid 集群, configmap 没有 key config",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				adminKubeClient := fake.NewSimpleClientset()
				adminDynamicClient, err := newFakeClientWithSubnet([]Subnet{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "EniSubC",
							Namespace: "default",
						},
					},
				})
				if err != nil {
					t.Errorf("newFakeClientWithSubnet: %v", err)
				}

				_, err = adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": "this is not a yaml",
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubA",
								Name:       "ENIName",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubB",
								Name:       "ENIName",
								ZoneName:   "cn-bj-b",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubC",
								Name:       "ENIName",
								ZoneName:   "cn-bj-c",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
					models:             model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			wantErr: true,
		},
		{
			name: "独立部署 VPC-Hybrid 集群, 正常流程",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctrl)
				vpcclient := vpcmock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				adminKubeClient := fake.NewSimpleClientset()
				adminDynamicClient, err := newFakeClientWithSubnet([]Subnet{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "EniSubC",
							Namespace: "default",
						},
					},
				})
				if err != nil {
					t.Errorf("newFakeClientWithSubnet: %v", err)
				}

				_, err = adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				vpcResp := &vpc.DescribeVPCResponse{
					ShowVPCModel: &vpc.VPC{
						VPCID: "VPCID",
						Subnets: []*vpc.Subnet{
							{
								SubnetID:   "SubnetID",
								Name:       "Name",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubA",
								Name:       "ENIName",
								ZoneName:   "cn-bj-a",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubB",
								Name:       "ENIName",
								ZoneName:   "cn-bj-b",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
							{
								SubnetID:   "EniSubC",
								Name:       "ENIName",
								ZoneName:   "cn-bj-c",
								CIDR:       "CIDR",
								VPCID:      "VPCID",
								SubnetType: "SubnetType",
							},
						},
					},
				}

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								VPCSubnetID:   "SubnetID",
								AvailableZone: internalvpc.ZoneA,
							},
						},
					},
				}

				subnet := &vpc.Subnet{
					SubnetID:   "SubnetID",
					Name:       "Name",
					ZoneName:   "cn-bj-a",
					CIDR:       "CIDR",
					SubnetType: "SubnetType",
				}

				gomock.InOrder(
					model.EXPECT().GetCluster(ctx, "ClusterID", "AccountID").Return(cluster, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeVPC(ctx, "VPCID", nil).Return(vpcResp, nil),

					model.EXPECT().GetClusterSubnets(ctx, "AccountID", "ClusterID").Return(instances, nil),

					stsclient.EXPECT().NewSignOption(ctx, "AccountID").Return(nil),
					vpcclient.EXPECT().DescribeSubnet(ctx, "SubnetID", nil).Return(subnet, nil),
				)

				return fields{
					accountID: "AccountID",
					clients: &clients.Clients{
						VPCClient: vpcclient,
						STSClient: stsclient,
					},
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
					models:             model,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "ClusterID",
			},
			want: &ccesdk.ClusterExtraInfo{
				MasterBLBSubnet: nil,
				LBServiceSubnet: &ccesdk.Subnet{
					SubnetID:      "SubnetID",
					SubnetName:    "Name",
					SubnetType:    "SubnetType",
					SubnetCIDR:    "CIDR",
					AvailableZone: internalvpc.ZoneA,
				},
				Subnets: []ccesdk.Subnet{
					{
						SubnetID:      "SubnetID",
						SubnetName:    "Name",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: internalvpc.ZoneA,
					},
				},
				ENISubnets: []ccesdk.Subnet{
					{
						SubnetID:      "EniSubA",
						SubnetName:    "ENIName",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: "zoneA",
					},
					{
						SubnetID:      "EniSubB",
						SubnetName:    "ENIName",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: "zoneB",
					},
					{
						SubnetID:      "EniSubC",
						SubnetName:    "ENIName",
						SubnetType:    "SubnetType",
						SubnetCIDR:    "CIDR",
						AvailableZone: "zoneC",
					},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}
			got, err := s.GetClusterExtraInfo(tt.args.ctx, tt.args.clusterID)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetClusterExtraInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetClusterExtraInfo() got = %v, want %v", got, tt.want)
			}
		})
	}
}

type Subnet struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   SubnetSpec   `json:"spec,omitempty"`
	Status SubnetStatus `json:"status,omitempty"`
}

type SubnetSpec struct {
	ID               string `json:"id,omitempty"`
	Name             string `json:"name,omitempty"`
	AvailabilityZone string `json:"availabilityZone,omitempty"`
	CIDR             string `json:"cidr,omitempty"`
	Exclusive        bool   `json:"exclusive,omitempty"`
}

type SubnetStatus struct {
	AvailableIPNum int    `json:"availableIPNum,omitempty"`
	Enable         bool   `json:"enable,omitempty"`
	HasNoMoreIP    bool   `json:"hasNoMoreIP,omitempty"`
	Reason         string `json:"reason,omitempty"`
}

func newFakeClientWithSubnet(subnets []Subnet) (dynamic.Interface, error) {
	var (
		GroupVersion  = schema.GroupVersion{Group: "cce.io", Version: "v1alpha1"}
		SchemeBuilder = &scheme.Builder{GroupVersion: GroupVersion}
	)

	scheme, err := SchemeBuilder.Build()
	if err != nil {
		return nil, err
	}

	objs := []runtime.Object{}
	for _, d := range subnets {
		u, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&d)
		if err != nil {
			return nil, err
		}
		utd := &unstructured.Unstructured{Object: u}
		utd.SetGroupVersionKind(schema.GroupVersionKind{
			Group: "cce.io", Version: "v1alpha1", Kind: "Subnet",
		})
		objs = append(objs, utd)
	}
	dc := dynamicfake.NewSimpleDynamicClient(scheme, objs...)
	return dc, nil
}

// mockDynamicClientWithError 创建一个会返回特定错误的mock dynamic client
type mockDynamicClientWithError struct {
	dynamic.Interface
	err error
}

type mockResourceInterface struct {
	dynamic.ResourceInterface
	err error
}

type mockNamespaceableResourceInterface struct {
	dynamic.NamespaceableResourceInterface
	err error
}

func (m *mockDynamicClientWithError) Resource(resource schema.GroupVersionResource) dynamic.NamespaceableResourceInterface {
	return &mockNamespaceableResourceInterface{err: m.err}
}

func (m *mockNamespaceableResourceInterface) Namespace(namespace string) dynamic.ResourceInterface {
	return &mockResourceInterface{err: m.err}
}

func (m *mockResourceInterface) List(ctx context.Context, opts metav1.ListOptions) (*unstructured.UnstructuredList, error) {
	return nil, m.err
}

func (m *mockResourceInterface) Get(ctx context.Context, name string, options metav1.GetOptions, subresources ...string) (*unstructured.Unstructured, error) {
	return nil, m.err
}

func (m *mockResourceInterface) Create(ctx context.Context, obj *unstructured.Unstructured, options metav1.CreateOptions, subresources ...string) (*unstructured.Unstructured, error) {
	return nil, m.err
}

func (m *mockResourceInterface) Update(ctx context.Context, obj *unstructured.Unstructured, options metav1.UpdateOptions, subresources ...string) (*unstructured.Unstructured, error) {
	return nil, m.err
}

func (m *mockResourceInterface) UpdateStatus(ctx context.Context, obj *unstructured.Unstructured, options metav1.UpdateOptions) (*unstructured.Unstructured, error) {
	return nil, m.err
}

func (m *mockResourceInterface) Delete(ctx context.Context, name string, options metav1.DeleteOptions, subresources ...string) error {
	return m.err
}

func (m *mockResourceInterface) DeleteCollection(ctx context.Context, options metav1.DeleteOptions, listOptions metav1.ListOptions) error {
	return m.err
}

func (m *mockResourceInterface) Watch(ctx context.Context, opts metav1.ListOptions) (watch.Interface, error) {
	return nil, m.err
}

func (m *mockResourceInterface) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, options metav1.PatchOptions, subresources ...string) (*unstructured.Unstructured, error) {
	return nil, m.err
}

func newMockDynamicClientWithError(err error) dynamic.Interface {
	return &mockDynamicClientWithError{err: err}
}

func Test_service_UpdatePodEniSubnets(t *testing.T) {
	type fields struct {
		accountID          string
		clients            *clients.Clients
		models             models.Interface
		config             *configuration.Config
		services           services.Interface
		adminKubeClient    kubernetes.Interface
		adminDynamicClient dynamic.Interface
	}
	type args struct {
		ctx       context.Context
		clusterID string
		req       *ccesdk.UpdatePodEniSubnetsRequest
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
						"cced":   string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: false,
		},
		{
			name: "configmap no cced key",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "no cluster id",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
						"cced":   string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "configMap not found",
			fields: func() fields {
				adminKubeClient := fake.NewSimpleClientset()

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "configMap not correct",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"confiffg": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "configMap not correct",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": "haha",
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "configMap not include eniSubnetList",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFileWithoutEniSubnet,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		}, {
			name: "update cce-network-v2 subnet",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				model := models.NewMockInterface(ctrl)
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      addon.CCENetworkV2ConfigMapName,
						Namespace: addon.AddonCCENetworkV2Namespace,
					},
					Data: map[string]string{
						"cced": string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				cluster := &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeVPCENI,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}

				model.EXPECT().GetCluster(ctx, "cce-xxx", "AccountID").Return(cluster, nil).AnyTimes()

				return fields{
					accountID: "AccountID",
					clients:   &clients.Clients{},
					models:    model,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaClient := metamock.NewMockInterface(ctrl)

			mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaClient
			s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(&vpc.DescribeVPCResponse{
				ShowVPCModel: &vpc.VPC{
					Subnets: []*vpc.Subnet{
						{
							SubnetID: "sbn-xxx",
							ZoneName: "x-x-a",
						},
					},
				},
			}, nil).AnyTimes()
			metaClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Cluster{}, nil)
			mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			cluster := &models.Cluster{
				Spec: &ccetypes.ClusterSpec{
					VPCID: "VPCID",
					MasterConfig: ccetypes.MasterConfig{
						ExposedPublic:         false,
						ClusterBLBVPCSubnetID: "SubnetID",
					},
					ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
						Mode:                 ccetypes.ContainerNetworkModeVPCENI,
						LBServiceVPCSubnetID: "SubnetID",
					},
				},
				Status: nil,
			}
			mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(cluster, nil).AnyTimes()
			mockmodel.EXPECT().UpdateClusterSpecDBAndCRD(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	tests2 := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "func updateclusterspecandcrd fail",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
						"cced":   string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "func updateclusterspecandcrd fail v1",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests2 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaClient := metamock.NewMockInterface(ctrl)

			mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaClient
			s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(&vpc.DescribeVPCResponse{
				ShowVPCModel: &vpc.VPC{
					Subnets: []*vpc.Subnet{
						{
							SubnetID: "sbn-xxx",
							ZoneName: "x-x-a",
						},
					},
				},
			}, nil).AnyTimes()
			metaClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Cluster{}, nil)
			mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("fail"))
			var cluster *models.Cluster
			if strings.Contains(tt.name, "v1") {
				cluster = &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}
			} else {
				cluster = &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeVPCENI,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}
			}
			mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(cluster, nil).AnyTimes()
			mockmodel.EXPECT().UpdateClusterSpecDBAndCRD(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("fail")).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	tests3 := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "func describeVPC fail",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
						"cced":   string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "func describeVPC fail for v1",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests3 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaclient := metamock.NewMockInterface(ctrl)

			mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaclient
			s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("vpc client error")).AnyTimes()
			metaclient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Cluster{}, nil)
			mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			var cluster *models.Cluster
			if strings.Contains(tt.name, "v1") {
				cluster = &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}
			} else {
				cluster = &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeVPCENI,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}
			}
			mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(cluster, nil).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	tests4 := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "func metaclient.getcluster fail",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
						"cced":   string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
		{
			name: "func metaclient.getcluster fail v1",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests4 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaClient := metamock.NewMockInterface(ctrl)

			mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaClient
			s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(&vpc.DescribeVPCResponse{
				ShowVPCModel: &vpc.VPC{
					Subnets: []*vpc.Subnet{
						{
							SubnetID: "sbn-xxx",
							ZoneName: "x-x-a",
						},
					},
				},
			}, nil).AnyTimes()
			metaClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("metaclient getcluster fail"))
			mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			var cluster *models.Cluster
			if strings.Contains(tt.name, "v1") {
				cluster = &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}
			} else {
				cluster = &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						VPCID: "VPCID",
						MasterConfig: ccetypes.MasterConfig{
							ExposedPublic:         false,
							ClusterBLBVPCSubnetID: "SubnetID",
						},
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							Mode:                 ccetypes.ContainerNetworkModeVPCENI,
							LBServiceVPCSubnetID: "SubnetID",
						},
					},
					Status: nil,
				}
			}
			mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(cluster, nil).AnyTimes()
			mockmodel.EXPECT().UpdateClusterSpecDBAndCRD(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("fail")).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	tests5 := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "models == nil for v1",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests5 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaClient := metamock.NewMockInterface(ctrl)

			// mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaClient
			// s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(&vpc.DescribeVPCResponse{
				ShowVPCModel: &vpc.VPC{
					Subnets: []*vpc.Subnet{
						{
							SubnetID: "sbn-xxx",
							ZoneName: "x-x-a",
						},
					},
				},
			}, nil).AnyTimes()
			metaClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Cluster{}, nil)
			// mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
			// var cluster *models.Cluster
			// if strings.Contains(tt.name, "v1") {
			// 	cluster = &models.Cluster{
			// 		Spec: &ccetypes.ClusterSpec{
			// 			VPCID: "VPCID",
			// 			MasterConfig: ccetypes.MasterConfig{
			// 				ExposedPublic:         false,
			// 				ClusterBLBVPCSubnetID: "SubnetID",
			// 			},
			// 			ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
			// 				Mode:                 ccetypes.ContainerNetworkModeBBCSecondaryIPVeth,
			// 				LBServiceVPCSubnetID: "SubnetID",
			// 			},
			// 		},
			// 		Status: nil,
			// 	}
			// } else {
			// 	cluster = &models.Cluster{
			// 		Spec: &ccetypes.ClusterSpec{
			// 			VPCID: "VPCID",
			// 			MasterConfig: ccetypes.MasterConfig{
			// 				ExposedPublic:         false,
			// 				ClusterBLBVPCSubnetID: "SubnetID",
			// 			},
			// 			ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
			// 				Mode:                 ccetypes.ContainerNetworkModeVPCENI,
			// 				LBServiceVPCSubnetID: "SubnetID",
			// 			},
			// 		},
			// 		Status: nil,
			// 	}
			// }
			// mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(cluster, nil).AnyTimes()
			// mockmodel.EXPECT().UpdateClusterSpecDBAndCRD(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("fail")).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	tests6 := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "models.getcluster returns nil for v1",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests6 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaClient := metamock.NewMockInterface(ctrl)

			mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaClient
			s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(&vpc.DescribeVPCResponse{
				ShowVPCModel: &vpc.VPC{
					Subnets: []*vpc.Subnet{
						{
							SubnetID: "sbn-xxx",
							ZoneName: "x-x-a",
						},
					},
				},
			}, nil).AnyTimes()
			metaClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Cluster{}, nil)
			mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(nil, nil).AnyTimes()
			mockmodel.EXPECT().UpdateClusterSpecDBAndCRD(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("fail")).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
	tests7 := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "models.getcluster failed ",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})
				if err != nil {
					t.Errorf("create configmap: %v", err)
				}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-xxx",
				req: &ccesdk.UpdatePodEniSubnetsRequest{
					Subnets: []string{"sbn-xxx"},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests7 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			//s.accountID = "accountID"
			ctrl := gomock.NewController(t)
			vpcclient := vpcmock.NewMockInterface(ctrl)
			stsclient := stsmock.NewMockInterface(ctrl)
			metaClient := metamock.NewMockInterface(ctrl)

			mockmodel := models.NewMockInterface(ctrl)
			s.clients.VPCClient = vpcclient
			s.clients.STSClient = stsclient
			s.clients.MetaK8SClient = metaClient
			s.models = mockmodel

			stsclient.EXPECT().NewSignOption(gomock.Any(), tt.fields.accountID).Return(&bce.SignOption{}).AnyTimes()
			vpcclient.EXPECT().DescribeVPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(&vpc.DescribeVPCResponse{
				ShowVPCModel: &vpc.VPC{
					Subnets: []*vpc.Subnet{
						{
							SubnetID: "sbn-xxx",
							ZoneName: "x-x-a",
						},
					},
				},
			}, nil).AnyTimes()
			metaClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Cluster{}, nil)
			mockmodel.EXPECT().UpdatePartClusterStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

			mockmodel.EXPECT().GetCluster(tt.args.ctx, "cce-xxx", tt.fields.accountID).Return(nil, errors.New("fail")).AnyTimes()
			mockmodel.EXPECT().UpdateClusterSpecDBAndCRD(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(errors.New("fail")).AnyTimes()

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			if err := s.UpdatePodEniSubnets(tt.args.ctx, tt.args.clusterID, tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("service.UpdatePodEniSubnets() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}

}

func TestNewService(t *testing.T) {
	type args struct {
		ctrl      *gomock.Controller
		ctx       context.Context
		accountID string
		clusterID string
		config    *configuration.Config
		services  services.Interface
		clients   *clients.Clients
		model     models.Interface
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		{
			name: "accountID empty",
			args: args{
				ctx:       context.TODO(),
				accountID: "",
				clusterID: "",
				config: &configuration.Config{
					Region: "bj",
				},
				services: nil,
				clients:  &clients.Clients{},
				model:    nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "clients nil",
			args: args{
				ctx:       context.TODO(),
				accountID: "xxx",
				clusterID: "",
				config: &configuration.Config{
					Region: "bj",
				},
				services: nil,
				clients:  nil,
				model:    nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "model nil",
			args: args{
				ctx:       context.TODO(),
				accountID: "xxx",
				clusterID: "",
				config: &configuration.Config{
					Region: "bj",
				},
				services: nil,
				clients:  &clients.Clients{},
				model:    nil,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "GetAdminKubeConfigCompatibility failed",
			args: func() args {
				ctrl := gomock.NewController(t)

				mockmodel := models.NewMockInterface(ctrl)

				gomock.InOrder(
					mockmodel.EXPECT().GetAdminKubeConfigCompatibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, net.ErrClosed),
				)

				return args{
					ctrl:      ctrl,
					ctx:       context.TODO(),
					accountID: "xxx",
					clusterID: "cce-xxx",
					config: &configuration.Config{
						Region: "bj",
					},
					services: nil,
					clients:  &clients.Clients{},
					model:    mockmodel,
				}
			}(),
			want:    nil,
			wantErr: true,
		},
		{
			name: "NewK8SClient failed",
			args: func() args {
				ctrl := gomock.NewController(t)

				mockmodel := models.NewMockInterface(ctrl)

				gomock.InOrder(
					mockmodel.EXPECT().GetAdminKubeConfigCompatibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.KubeConfig{
						KubeConfigFile: "empty",
					}, nil),
				)

				return args{
					ctrl:      ctrl,
					ctx:       context.TODO(),
					accountID: "xxx",
					clusterID: "cce-xxx",
					config: &configuration.Config{
						Region: "bj",
					},
					services: nil,
					clients:  &clients.Clients{},
					model:    mockmodel,
				}
			}(),
			want:    nil,
			wantErr: true,
		},
		{
			name: "normal case",
			args: func() args {
				ctrl := gomock.NewController(t)

				mockmodel := models.NewMockInterface(ctrl)

				gomock.InOrder(
					mockmodel.EXPECT().GetAdminKubeConfigCompatibility(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.KubeConfig{
						KubeConfigFile: sampleKubeConfigFile,
					}, nil),
				)

				return args{
					ctrl:      ctrl,
					ctx:       context.TODO(),
					accountID: "xxx",
					clusterID: "cce-xxx",
					config: &configuration.Config{
						Region: "bj",
					},
					services: nil,
					clients:  &clients.Clients{},
					model:    mockmodel,
				}
			}(),
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.args.ctrl != nil {
				defer tt.args.ctrl.Finish()
			}

			got, err := NewService(tt.args.ctx, tt.args.accountID, tt.args.clusterID, tt.args.config, tt.args.services, tt.args.clients, tt.args.model)
			if (err != nil) != tt.wantErr {
				t.Errorf("NewService() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if got == nil && !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewService() = %v, want %v", got, tt.want)
			}
		})
	}

}

func Test_tryToGetAvailableZoneIgnoreError(t *testing.T) {
	type args struct {
		ctx      context.Context
		zoneName string
	}
	tests := []struct {
		name string
		args args
		want internalvpc.AvailableZone
	}{
		{
			name: "正常流程",
			args: args{
				ctx:      context.TODO(),
				zoneName: "cn-bj-c",
			},
			want: "zoneC",
		},
		{
			name: "zone 不存在",
			args: args{
				ctx:      context.TODO(),
				zoneName: "cn-bj-z",
			},
			want: "cn-bj-z",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tryToGetAvailableZoneIgnoreError(tt.args.ctx, tt.args.zoneName); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("tryToGetAvailableZoneIgnoreError() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_service_FilterClusterListByPermission(t *testing.T) {
	type fields struct {
		accountID          string
		clients            *clients.Clients
		models             models.Interface
		config             *configuration.Config
		services           services.Interface
		adminKubeClient    kubernetes.Interface
		adminDynamicClient dynamic.Interface
	}
	type args struct {
		ctx              context.Context
		accountID        string
		userID           string
		signatureHeaders map[string]string
		originList       []*models.Cluster
		permissions      []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*models.Cluster
		want1   []iam.VerifyResult
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "主用户",
			fields: fields{},
			args: args{
				ctx:       context.TODO(),
				accountID: "accountID",
				userID:    "accountID",
				originList: []*models.Cluster{
					{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-aaaaa",
						},
					},
				},
			},
			want: []*models.Cluster{
				{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-aaaaa",
					},
				},
			},
			wantErr: false,
		},
		{
			name:   "子用户，cluster list empty",
			fields: fields{},
			args: args{
				ctx:        context.TODO(),
				accountID:  "accountID",
				userID:     "userID",
				originList: []*models.Cluster{},
			},
			want:    []*models.Cluster{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}
			got, got1, err := s.FilterClusterListByPermission(tt.args.ctx, tt.args.accountID, tt.args.userID, tt.args.signatureHeaders, tt.args.originList, tt.args.permissions)
			if (err != nil) != tt.wantErr {
				t.Errorf("service.FilterClusterListByPermission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("service.FilterClusterListByPermission() got = %v, want %v", got, tt.want)
			}
			if !reflect.DeepEqual(got1, tt.want1) {
				t.Errorf("service.FilterClusterListByPermission() got1 = %v, want %v", got1, tt.want1)
			}
		})
	}
}

func Test_service_updateClusterRoleCCEAdmin(t *testing.T) {
	type fields struct {
		accountID          string
		clients            *clients.Clients
		models             models.Interface
		config             *configuration.Config
		services           services.Interface
		adminKubeClient    kubernetes.Interface
		adminDynamicClient dynamic.Interface
		adminExtendClient  clientset.Interface
		adminRESTClient    rest.Interface
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常",
			fields: func() fields {
				//ctx := context.TODO()
				role := rbacv1.ClusterRole{
					TypeMeta: metav1.TypeMeta{
						Kind:       "ClusterRole",
						APIVersion: "rbac.authorization.k8s.io/v1",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "cce:admin",
					},
					Rules: []rbacv1.PolicyRule{
						{
							Verbs:           nil,
							APIGroups:       nil,
							Resources:       nil,
							ResourceNames:   nil,
							NonResourceURLs: nil,
						},
					},
					AggregationRule: nil,
				}
				adminKubeClient := fake.NewSimpleClientset(&role)

				//_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-clusterrole", metav1.GetOptions{})
				//if err != nil {
				//	t.Errorf("create configmap: %v", err)
				//}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{ctx: context.TODO()},
		},
		{
			name: "return err",
			fields: func() fields {
				////ctx := context.TODO()
				//role := rbacv1.ClusterRole{
				//	TypeMeta: metav1.TypeMeta{
				//		Kind:       "ClusterRole",
				//		APIVersion: "rbac.authorization.k8s.io/v1",
				//	},
				//	ObjectMeta: metav1.ObjectMeta{
				//		Name: "cce:admin",
				//	},
				//	Rules: []rbacv1.PolicyRule{
				//		{
				//			Verbs:           nil,
				//			APIGroups:       nil,
				//			Resources:       nil,
				//			ResourceNames:   nil,
				//			NonResourceURLs: nil,
				//		},
				//	},
				//	AggregationRule: nil,
				//}
				adminKubeClient := fake.NewSimpleClientset()

				//_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-clusterrole", metav1.GetOptions{})
				//if err != nil {
				//	t.Errorf("create configmap: %v", err)
				//}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args:    args{ctx: context.TODO()},
			wantErr: true,
		},
		{
			name: "return err",
			fields: func() fields {
				////ctx := context.TODO()
				//role := rbacv1.ClusterRole{
				//	TypeMeta: metav1.TypeMeta{
				//		Kind:       "ClusterRole",
				//		APIVersion: "rbac.authorization.k8s.io/v1",
				//	},
				//	ObjectMeta: metav1.ObjectMeta{
				//		Name: "cce:admin",
				//	},
				//	Rules: []rbacv1.PolicyRule{
				//		{
				//			Verbs:           nil,
				//			APIGroups:       nil,
				//			Resources:       nil,
				//			ResourceNames:   nil,
				//			NonResourceURLs: nil,
				//		},
				//	},
				//	AggregationRule: nil,
				//}
				adminKubeClient := fake.NewSimpleClientset(
					&rbacv1.ClusterRole{
						TypeMeta: metav1.TypeMeta{},
						ObjectMeta: metav1.ObjectMeta{
							Name: ClusterRoleCCEAdmin,
						},
						Rules: []rbacv1.PolicyRule{
							{
								Verbs:           nil,
								APIGroups:       nil,
								Resources:       nil,
								ResourceNames:   nil,
								NonResourceURLs: nil,
							},
							{
								Verbs:           nil,
								APIGroups:       nil,
								Resources:       nil,
								ResourceNames:   nil,
								NonResourceURLs: []string{"nil"},
							},
						},
						AggregationRule: nil,
					},
				)

				//_, err := adminKubeClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "cce-clusterrole", metav1.GetOptions{})
				//if err != nil {
				//	t.Errorf("create configmap: %v", err)
				//}

				return fields{
					accountID: "",
					clients:   &clients.Clients{},
					models:    nil,
					config: &configuration.Config{
						Region: "bj",
					},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args:    args{ctx: context.TODO()},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
				adminExtendClient:  tt.fields.adminExtendClient,
				adminRESTClient:    tt.fields.adminRESTClient,
			}
			if err := s.updateClusterRoleCCEAdmin(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("updateClusterRoleCCEAdmin() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

//func Test_service_updateClusterRoleCCEAdmin(t *testing.T) {
//    type fields struct {
//        adminKubeClient kubernetes.Interface
//    }
//    type args struct {
//        ctx context.Context
//    }
//    tests := []struct {
//        name    string
//        fields  fields
//        args    args
//        wantErr bool
//    }{
//        {
//            name: "Update ClusterRole successfully",
//            fields: fields{
//                adminKubeClient: newFakeKubeClient(&rbacv1.ClusterRole{
//                    ObjectMeta: metav1.ObjectMeta{
//                        Name: "cce:admin",
//                    },
//                }),
//            },
//            args: args{
//                ctx: context.Background(),
//            },
//            wantErr: false,
//        },
//        {
//            name: "Fail to get ClusterRole",
//            fields: fields{
//                adminKubeClient: newFakeKubeClient(&rbacv1.ClusterRole{
//                    ObjectMeta: metav1.ObjectMeta{
//                        Name: "non-existent", // Different name to simulate non-existent ClusterRole
//                    },
//                }),
//                // Alternatively, you can use a reactor to simulate the error
//                // adminKubeClient: newFakeKubeClientWithReactor("get", "clusterroles", func(action clientTesting.Action) (handled bool, ret runtime.Object, err error) {
//                //     return true, nil, fmt.Errorf("clusterrole not found")
//                // }),
//            },
//            args: args{
//                ctx: context.Background(),
//            },
//            wantErr: true,
//        },
//    }
//    for _, tt := range tests {
//        t.Run(tt.name, func(t *testing.T) {
//            s := &service{
//                adminKubeClient: tt.fields.adminKubeClient,
//            }
//            if err := s.updateClusterRoleCCEAdmin(tt.args.ctx); (err != nil) != tt.wantErr {
//                t.Errorf("updateClusterRoleCCEAdmin() error = %v, wantErr %v", err, tt.wantErr)
//            }
//        })
//    }
//}

func Test_ClusterScaleInfo(t *testing.T) {
	ctx := context.TODO()
	clusterID := "cce-5gtirrnf"
	accountID := "eca97e148cb74e9683d7b7240829d1ff"
	mysqlConn := "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true"
	masterFlavor := "l50"

	models, err := models.NewClient(ctx, mysqlConn)
	if err != nil {
		logger.Errorf(ctx, "NewModel failed: %v", err)
		return
	}

	cluster, err := models.GetCluster(ctx, clusterID, accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %v", clusterID, err)
		return
	}

	logger.Infof(ctx, " %s is : %v", clusterID, utils.ToJSON(cluster))

	adminKubeconfig, err := models.GetAdminKubeConfigCompatibility(ctx, clusterID, "internal")
	if err != nil {
		logger.Errorf(ctx, "fail to get admin kubeconfig of cluster %s: %w", clusterID, err)
		return
	}
	adminK8SClient, err := utils.NewK8SClient(ctx, adminKubeconfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "fail to get admin kubeconfig of cluster %s: %w", clusterID, err)
		return
	}

	service := &service{
		accountID: "",
		clients:   nil,
		models:    models,
		config: &configuration.Config{
			PluginConfig: &pluginclients.Config{
				FlavorConfig: &pluginclients.FlavorConfig{
					FlavorConfigMap: map[string]pluginclients.FlavorDeployConfig{
						masterFlavor: pluginclients.FlavorDeployConfig{
							ResourceLimit: pluginclients.ResourceLimit{},
						},
					},
				},
			},
		},
		services:           nil,
		adminKubeClient:    adminK8SClient,
		adminDynamicClient: nil,
		adminExtendClient:  nil,
		adminRESTClient:    nil,
	}

	clusterScaleInfo, err := service.GetClusterScaleInfo(ctx, cluster)
	if err != nil {
		logger.Errorf(ctx, "GetClusterScaleInfo %s failed: %v", clusterID, err)
	}

	logger.Infof(ctx, "clusterScaleInfo is %v", clusterScaleInfo)
}

func Test_service_GetAllExistedSubnets(t *testing.T) {
	type fields struct {
		accountID          string
		clients            *clients.Clients
		models             models.Interface
		config             *configuration.Config
		services           services.Interface
		adminKubeClient    kubernetes.Interface
		adminDynamicClient dynamic.Interface
	}
	type args struct {
		ctx         context.Context
		clusterID   string
		isV1Network bool
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    sets.String
		wantErr bool
	}{
		{
			name: "正常流程",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
						"cced":   string(simpleVPCENIConfig),
					},
				}, metav1.CreateOptions{})

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: false,
			},
			want:    sets.NewString("sbn-psupdhtdc0nv"),
			wantErr: false,
		},
		{
			name: "configmap not found ",
			fields: func() fields {
				//ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: false,
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "configmap not correct, no cced in data ",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-network-v2-config",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: nil,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: false,
			},
			want:    nil,
			wantErr: true,
		},

		{
			name: "V1 network - ConfigMap not found",
			fields: func() fields {
				adminKubeClient := fake.NewSimpleClientset()
				// 不创建ConfigMap，模拟ConfigMap不存在的情况

				// 创建正常的dynamic client
				adminDynamicClient, _ := newFakeClientWithSubnet([]Subnet{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "EniSubC",
							Namespace: "default",
						},
					},
				})

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: true,
			},
			want:    sets.NewString("EniSubC"), // 应该从CRD获取到子网，ConfigMap错误被兼容处理
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			s.adminDynamicClient, _ = newFakeClientWithSubnet([]Subnet{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "EniSubC",
						Namespace: "default",
					},
				},
			})

			got, err := s.GetAllExistedSubnets(tt.args.ctx, tt.args.clusterID, tt.args.isV1Network)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllExistedSubnets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllExistedSubnets() got = %v, want %v", got, tt.want)
			}
		})
	}

	tests2 := []struct {
		name    string
		fields  fields
		args    args
		want    sets.String
		wantErr bool
	}{
		{
			name: "V1 network - CRD no matches for kind error",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				// 创建V1网络模式需要的ConfigMap
				adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})

				// 创建会返回"no matches for kind"错误的mock dynamic client
				noMatchesErr := errors.New("failed to list cce.io/v1alpha1, Resource=subnets: no matches for kind \"Subnet\" in version \"cce.io/v1alpha1\"")
				adminDynamicClient := newMockDynamicClientWithError(noMatchesErr)

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: true,
			},
			want:    sets.NewString("EniSubA", "EniSubB"), // 应该从ConfigMap获取到子网
			wantErr: false,
		},
		{
			name: "V1 network - CRD server could not find resource error",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				// 创建V1网络模式需要的ConfigMap
				adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})

				// 创建会返回"server could not find resource"错误的mock dynamic client
				serverErr := errors.New("failed to list cce.io/v1alpha1, Resource=subnets: the server could not find the requested resource")
				adminDynamicClient := newMockDynamicClientWithError(serverErr)

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: true,
			},
			want:    sets.NewString("EniSubA", "EniSubB"), // 应该从ConfigMap获取到子网
			wantErr: false,
		},
		{
			name: "V1 network - CRD could not find resource error",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				// 创建V1网络模式需要的ConfigMap
				adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})

				// 创建会返回"could not find resource"错误的mock dynamic client
				couldNotFindErr := errors.New("failed to list cce.io/v1alpha1, Resource=subnets: could not find the requested resource")
				adminDynamicClient := newMockDynamicClientWithError(couldNotFindErr)

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: true,
			},
			want:    sets.NewString("EniSubA", "EniSubB"), // 应该从ConfigMap获取到子网
			wantErr: false,
		},
		{
			name: "V1 network - CRD real error (not compatibility case)",
			fields: func() fields {
				ctx := context.TODO()
				adminKubeClient := fake.NewSimpleClientset()

				// 创建V1网络模式需要的ConfigMap
				adminKubeClient.CoreV1().ConfigMaps("kube-system").Create(ctx, &v1.ConfigMap{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cce-cni-node-agent",
						Namespace: "kube-system",
					},
					Data: map[string]string{
						"config": sampleAgentConfigFile,
					},
				}, metav1.CreateOptions{})

				// 创建会返回真实错误（非兼容性错误）的mock dynamic client
				realErr := errors.New("failed to list cce.io/v1alpha1, Resource=subnets: network connection failed")
				adminDynamicClient := newMockDynamicClientWithError(realErr)

				return fields{
					accountID:          "123456",
					clients:            &clients.Clients{},
					models:             &models.MockInterface{},
					config:             &configuration.Config{},
					services:           nil,
					adminKubeClient:    adminKubeClient,
					adminDynamicClient: adminDynamicClient,
				}
			}(),
			args: args{
				ctx:         context.TODO(),
				clusterID:   "test-cluster",
				isV1Network: true,
			},
			want:    nil,
			wantErr: true, // 真实错误应该返回错误
		},
	}
	for _, tt := range tests2 {
		t.Run(tt.name, func(t *testing.T) {
			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			got, err := s.GetAllExistedSubnets(tt.args.ctx, tt.args.clusterID, tt.args.isV1Network)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetAllExistedSubnets() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetAllExistedSubnets() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_service_CNIDataPathV2Enabled(t *testing.T) {
	type fields struct {
		accountID          string
		clients            *clients.Clients
		models             models.Interface
		config             *configuration.Config
		services           services.Interface
		adminKubeClient    kubernetes.Interface
		adminDynamicClient dynamic.Interface
	}
	type args struct {
		ctx       context.Context
		clusterID string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		prepare func(f *fields)
		want    bool
		wantErr bool
	}{
		{
			name: "获取集群信息失败",
			fields: fields{
				accountID: "test-account",
			},
			args: args{
				ctx:       context.Background(),
				clusterID: "test-cluster",
			},
			prepare: func(f *fields) {
				mockCtrl := gomock.NewController(t)
				metaclient := metamock.NewMockInterface(mockCtrl)
				metaclient.EXPECT().GetCluster(gomock.Any(), consts.MetaClusterDefaultNamespace, "test-cluster", gomock.Any()).Return(nil, fmt.Errorf("get cluster error"))
				f.clients = &clients.Clients{
					MetaK8SClient: metaclient,
				}
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "集群不存在",
			fields: fields{
				accountID: "test-account",
			},
			args: args{
				ctx:       context.Background(),
				clusterID: "test-cluster",
			},
			prepare: func(f *fields) {
				mockCtrl := gomock.NewController(t)
				metaclient := metamock.NewMockInterface(mockCtrl)
				metaclient.EXPECT().GetCluster(gomock.Any(), consts.MetaClusterDefaultNamespace, "test-cluster", gomock.Any()).Return(nil, nil)
				f.clients = &clients.Clients{
					MetaK8SClient: metaclient,
				}
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "EBPF未启用",
			fields: fields{
				accountID: "test-account",
			},
			args: args{
				ctx:       context.Background(),
				clusterID: "test-cluster",
			},
			prepare: func(f *fields) {
				mockCtrl := gomock.NewController(t)

				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							EBPFConfig: ccetypes.EBPFConfiuration{
								Enabled: false,
							},
						},
					},
				}
				metaclient := metamock.NewMockInterface(mockCtrl)
				metaclient.EXPECT().GetCluster(gomock.Any(), consts.MetaClusterDefaultNamespace, "test-cluster", gomock.Any()).Return(cluster, nil)
				f.clients = &clients.Clients{
					MetaK8SClient: metaclient,
				}
			},
			want:    false,
			wantErr: false,
		},
		{
			name: "获取Deployment失败",
			fields: fields{
				accountID: "test-account",
			},
			args: args{
				ctx:       context.Background(),
				clusterID: "test-cluster",
			},
			prepare: func(f *fields) {
				mockCtrl := gomock.NewController(t)
				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							EBPFConfig: ccetypes.EBPFConfiuration{
								Enabled: true,
							},
						},
					},
				}
				metaclient := metamock.NewMockInterface(mockCtrl)
				metaclient.EXPECT().GetCluster(gomock.Any(), consts.MetaClusterDefaultNamespace, "test-cluster", gomock.Any()).Return(cluster, nil)
				f.clients = &clients.Clients{
					MetaK8SClient: metaclient,
				}

				// 创建一个返回error的fake client
				mockKubeClient := fake.NewSimpleClientset()
				// 使用monkey patch模拟AppsV1().Deployments().Get()调用返回错误
				f.adminKubeClient = &fakeKubeClientForCNI{
					Interface:          mockKubeClient,
					getDeploymentError: fmt.Errorf("get deployment error"),
				}
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "CNI Image Tag 不合法",
			fields: fields{
				accountID: "test-account",
			},
			args: args{
				ctx:       context.Background(),
				clusterID: "test-cluster",
			},
			prepare: func(f *fields) {
				mockCtrl := gomock.NewController(t)
				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							EBPFConfig: ccetypes.EBPFConfiuration{
								Enabled: true,
							},
						},
					},
				}
				metaclient := metamock.NewMockInterface(mockCtrl)
				metaclient.EXPECT().GetCluster(gomock.Any(), consts.MetaClusterDefaultNamespace, "test-cluster", gomock.Any()).Return(cluster, nil)
				f.clients = &clients.Clients{
					MetaK8SClient: metaclient,
				}

				// 创建fake client并添加cilium-operator deployment
				deployment := &appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name:      DeploymentCiliumOperator,
						Namespace: NamespaceKubeSystem,
					},
					Spec: appsv1.DeploymentSpec{
						Template: v1.PodTemplateSpec{
							Spec: v1.PodSpec{
								Containers: []v1.Container{
									{
										Name:  "init",
										Image: "registry.baidubce.com/cce-plugin-dev/nginx:latest",
									},
									{
										Name:  ContainerNameCiliumOperator,
										Image: "registry.baidubce.com/cce-plugin-dev/cilium/operator-generic",
									},
								},
							},
						},
					},
				}
				mockKubeClient := fake.NewSimpleClientset(deployment)
				f.adminKubeClient = mockKubeClient
			},
			want:    false,
			wantErr: true,
		},
		{
			name: "CNI版本大于1.15.0返回true",
			fields: fields{
				accountID: "test-account",
			},
			args: args{
				ctx:       context.Background(),
				clusterID: "test-cluster",
			},
			prepare: func(f *fields) {
				mockCtrl := gomock.NewController(t)
				cluster := &ccev1.Cluster{
					Spec: ccetypes.ClusterSpec{
						ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
							EBPFConfig: ccetypes.EBPFConfiuration{
								Enabled: true,
							},
						},
					},
				}
				metaclient := metamock.NewMockInterface(mockCtrl)
				metaclient.EXPECT().GetCluster(gomock.Any(), consts.MetaClusterDefaultNamespace, "test-cluster", gomock.Any()).Return(cluster, nil)
				f.clients = &clients.Clients{
					MetaK8SClient: metaclient,
				}

				// 创建fake client并添加cilium-operator deployment
				deployment := &appsv1.Deployment{
					ObjectMeta: metav1.ObjectMeta{
						Name:      DeploymentCiliumOperator,
						Namespace: NamespaceKubeSystem,
					},
					Spec: appsv1.DeploymentSpec{
						Template: v1.PodTemplateSpec{
							Spec: v1.PodSpec{
								Containers: []v1.Container{
									{
										Name:  "init",
										Image: "registry.baidubce.com/cce-plugin-dev/nginx:latest",
									},
									{
										Name:  ContainerNameCiliumOperator,
										Image: "registry.baidubce.com/cce-plugin-dev/cilium/operator-generic:1.15.6-baidu-240726",
									},
								},
							},
						},
					},
				}
				mockKubeClient := fake.NewSimpleClientset(deployment)
				f.adminKubeClient = mockKubeClient
			},
			want:    true,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.prepare != nil {
				tt.prepare(&tt.fields)
			}

			s := &service{
				accountID:          tt.fields.accountID,
				clients:            tt.fields.clients,
				models:             tt.fields.models,
				config:             tt.fields.config,
				services:           tt.fields.services,
				adminKubeClient:    tt.fields.adminKubeClient,
				adminDynamicClient: tt.fields.adminDynamicClient,
			}

			got, err := s.CNIDataPathV2Enabled(tt.args.ctx, tt.args.clusterID)
			if (err != nil) != tt.wantErr {
				t.Errorf("service.CNIDataPathV2Enabled() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("service.CNIDataPathV2Enabled() = %v, want %v", got, tt.want)
			}
		})
	}
}

// 自定义fake client用于测试获取Deployment时出错的情况
type fakeKubeClientForCNI struct {
	kubernetes.Interface
	getDeploymentError error
}

func (f *fakeKubeClientForCNI) AppsV1() appsv1Client.AppsV1Interface {
	return &fakeAppsV1InterfaceForCNI{
		AppsV1Interface:    f.Interface.AppsV1(),
		getDeploymentError: f.getDeploymentError,
	}
}

type fakeAppsV1InterfaceForCNI struct {
	appsv1Client.AppsV1Interface
	getDeploymentError error
}

func (f *fakeAppsV1InterfaceForCNI) Deployments(namespace string) appsv1Client.DeploymentInterface {
	return &fakeDeploymentInterfaceForCNI{
		DeploymentInterface: f.AppsV1Interface.Deployments(namespace),
		getDeploymentError:  f.getDeploymentError,
	}
}

type fakeDeploymentInterfaceForCNI struct {
	appsv1Client.DeploymentInterface
	getDeploymentError error
}

func (f *fakeDeploymentInterfaceForCNI) Get(ctx context.Context, name string, options metav1.GetOptions) (*appsv1.Deployment, error) {
	if f.getDeploymentError != nil {
		return nil, f.getDeploymentError
	}
	return f.DeploymentInterface.Get(ctx, name, options)
}
