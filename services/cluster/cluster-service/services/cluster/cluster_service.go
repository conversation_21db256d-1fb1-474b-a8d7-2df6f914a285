package cluster

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	v1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	extendclient "k8s.io/apiextensions-apiserver/pkg/client/clientset/clientset"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	restclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/auth"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/addon"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients/authorization"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"
)

const (
	CniNodeAgentConfigMapName      = "cce-cni-node-agent"
	CniNodeAgentConfigMapNamespace = "kube-system"
	eniSubnetListMarker            = "eniSubnetList:"

	ApiServerStorageObjects        = "apiserver_storage_objects"
	ETCDObjectCounts               = "etcd_object_counts"
	ResourceNode                   = "nodes"
	ResourcePods                   = "pods"
	ResourceServices               = "services"
	ResourceConfigMaps             = "configmaps"
	ResourcePersistentVolumes      = "persistentvolumes"
	ResourcePersistentVolumeClaims = "persistentvolumeclaims"
	ResourceSecrets                = "secrets"
	ClusterRoleCCEAdmin            = "cce:admin"

	K8SVersion1220 = "v1.22.0"

	APIServeMetricsEndpoint = "/metrics"

	ResourceMetricRegex = `{resource="(.+?)"} (\d+)`
	BaiduCCECRDSuffix   = "cce.baidubce.com"
)

var (
	// Deprecated: cce network v1
	SubnetV1Alpha1GVR = schema.GroupVersionResource{
		Group:    "cce.io",
		Version:  "v1alpha1",
		Resource: "subnets",
	}

	// 容器网络v2对象
	SubnetV1GVR = schema.GroupVersionResource{
		Group:    "cce.baidubce.com",
		Version:  "v1",
		Resource: "subnets",
	}
)

type service struct {
	accountID          string
	clients            *clients.Clients
	models             models.Interface
	config             *configuration.Config
	services           services.Interface
	adminKubeClient    kubernetes.Interface
	adminDynamicClient dynamic.Interface
	adminExtendClient  extendclient.Interface
	adminRESTClient    restclient.Interface
}

// NewService - 初始化 cluster.service
func NewService(
	ctx context.Context,
	accountID string,
	clusterID string,
	config *configuration.Config,
	services services.Interface,
	clients *clients.Clients,
	model models.Interface,
) (Interface, error) {
	if accountID == "" {
		return nil, errors.New("accountID is empty")
	}

	if clients == nil {
		return nil, errors.New("clients is nil")
	}

	if model == nil {
		return nil, errors.New("models is nil")
	}

	var (
		adminK8SClient     kubernetes.Interface
		adminDynamicClient dynamic.Interface
		adminExtendClient  extendclient.Interface
		restConfig         restclient.Interface
	)

	if clusterID != "" {
		// fetch admin kubeconfig
		adminKubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
		if err != nil {
			return nil, fmt.Errorf("fail to get admin kubeconfig of cluster %s: %w", clusterID, err)
		}

		// new typed client
		adminK8SClient, err = utils.NewK8SClient(ctx, adminKubeconfig.KubeConfigFile)
		if err != nil {
			return nil, err
		}

		restConfig, err := clientcmd.RESTConfigFromKubeConfig([]byte(adminKubeconfig.KubeConfigFile))
		if err != nil {
			return nil, fmt.Errorf("RESTConfigFromKubeConfig for admin failed: %w", err)
		}

		// new dynamic client
		adminDynamicClient, err = dynamic.NewForConfig(restConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to create admin dynamic client: %w", err)
		}

		adminExtendClient, err = extendclient.NewForConfig(restConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to create admin extend client: %w", err)
		}
	}

	return &service{
		accountID:          accountID,
		clients:            clients,
		models:             model,
		config:             config,
		services:           services,
		adminRESTClient:    restConfig,
		adminKubeClient:    adminK8SClient,
		adminDynamicClient: adminDynamicClient,
		adminExtendClient:  adminExtendClient,
	}, nil
}

func (s *service) GetClusterExtraInfo(ctx context.Context, clusterID string) (*ccesdk.ClusterExtraInfo, error) {
	if clusterID == "" {
		return nil, errors.New("clusterID is empty")
	}

	// 查询集群所在 VPC 中所有子网
	cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		return nil, err
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		return nil, fmt.Errorf("cluster %s not exist", clusterID)
	}

	describeVPCResp, err := s.clients.VPCClient.DescribeVPC(ctx, cluster.Spec.VPCID, s.clients.STSClient.NewSignOption(ctx, s.accountID))
	if err != nil {
		logger.Errorf(ctx, "DescribeVPC %s failed: %v", clusterID, err)
		return nil, err
	}

	// 1. 查询集群所有 Nodes 所在的子网
	var subnets []ccesdk.Subnet
	switch cluster.Spec.ClusterType {
	case ccetypes.ClusterTypeServerless:
		// serverless集群，取 vkSubnets 作为集群实例 subnets
		// TODO(yezichao): serveless master option入库后从 model 读取 vkSubnets
		v1Cluster, err := s.clients.MetaK8SClient.GetCluster(ctx, models.DefaultNamespace, clusterID, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "GetCluster failed: %v", err)
			return nil, err
		}
		vkSubnets := v1Cluster.Spec.MasterConfig.ServerlessMasterOption.VKSubnets
		subnets = make([]ccesdk.Subnet, 0, len(vkSubnets))
		for _, vkSubnet := range vkSubnets {
			subnets = append(subnets, ccesdk.Subnet{
				SubnetID:      vkSubnet.SubnetID,
				AvailableZone: vkSubnet.AvailableZone,
			})
		}
	default:
		instances, err := s.models.GetClusterSubnets(ctx, s.accountID, clusterID)
		if err != nil {
			logger.Errorf(ctx, "GetClusterSubnets failed: %v", err)
			return nil, err
		}

		subnets = make([]ccesdk.Subnet, 0, len(instances))
		for _, instance := range instances {
			subnets = append(subnets, ccesdk.Subnet{
				SubnetID:      instance.Spec.VPCSubnetID,
				AvailableZone: instance.Spec.AvailableZone,
			})
		}
	}

	// 补全subnets信息
	for i := range subnets {
		for _, vpcSubnet := range describeVPCResp.ShowVPCModel.Subnets {
			if vpcSubnet.SubnetID == subnets[i].SubnetID {
				subnets[i].SubnetName = vpcSubnet.Name
				subnets[i].SubnetType = vpcSubnet.SubnetType
				subnets[i].SubnetCIDR = vpcSubnet.CIDR
				break
			}
		}
	}

	// 2. 查询 MasterBLB 子网详情
	var masterBLBSubnet *ccesdk.Subnet
	if cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeCustom ||
		cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeContainerizedCustom ||
		cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeContainerizedEdge {
		subnetID := cluster.Spec.MasterConfig.ClusterBLBVPCSubnetID
		if subnetID != "" {
			masterBLBSubnet, err = s.getSubnet(ctx, subnetID)
			if err != nil {
				logger.Errorf(ctx, "getSubnet %s failed: %v", subnetID, err)
				return nil, err
			}
		}
	}

	// 3. 查询 LB service 子网详情
	lbServiceSubnet, err := s.getSubnet(ctx, cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID)
	if err != nil {
		logger.Errorf(ctx, "getSubnet failed: %v", err)
		return nil, err
	}

	// 4. 查询 ENI 子网详情
	var (
		eniSubnets []ccesdk.Subnet
	)
	// Deprecated: cce network v1
	isV1ENINetwork := utils.IsVPCSecondaryIPCNIMode(cluster.Spec.ContainerNetworkConfig.Mode) ||
		utils.IsBBCSecondaryIPCNIMode(cluster.Spec.ContainerNetworkConfig.Mode)

	// for cce network v2
	if cluster.Spec.ContainerNetworkConfig.Mode == ccetypes.ContainerNetworkModeVPCENI ||
		cluster.Spec.ContainerNetworkConfig.Mode == ccetypes.ContainerNetworkModeVPCExclusiveENI {
		// first data source from CRD cce.baidubce.com/v1 Subnet
		sbns, err := s.getSubnetsFromCCEV1SubnetCR(ctx)
		if err != nil {
			logger.Errorf(ctx, "getSubnetsFromCCEV1SubnetCR failed: %v", err)
			return nil, err
		}

		// second data source from configmap kube-system/cce-network-v2-config
		cm, err := s.adminKubeClient.CoreV1().ConfigMaps(addon.AddonCCENetworkV2Namespace).Get(ctx, addon.CCENetworkV2ConfigMapName, metav1.GetOptions{})
		if err == nil {
			_, agentSubnets, err := addon.GetV2ENISubnets(cm.Data)
			if err == nil {
				sbns = sbns.Union(agentSubnets)
			}
		}
		for _, item := range sbns.List() {
			eniSubnets = append(eniSubnets, ccesdk.Subnet{
				SubnetID: item,
			})
		}
	} else if !isV1ENINetwork || cluster.Spec.ClusterType == ccetypes.ClusterTypeServerless {
		return &ccesdk.ClusterExtraInfo{
			MasterBLBSubnet: masterBLBSubnet,
			LBServiceSubnet: lbServiceSubnet,
			Subnets:         subnets,
		}, nil
	} else {
		// Deprecated: cce network v1
		// 从 cni 配置文件中获取
		s1, err := s.getEniSubnetsFromCniAgentConfig(ctx)
		if err != nil {
			logger.Errorf(ctx, "getEniSubnetsFromCniAgentConfig failed: %v", err)
			return nil, err
		}

		// 从 Subnet CR 中获取
		s2, err := s.getEniSubnetsFromSubnetV1Alpha1CR(ctx)
		if err != nil {
			logger.Errorf(ctx, "getEniSubnetsFromSubnetV1Alpha1CR failed: %v", err)
			return nil, err
		}

		// 汇聚结果，并集
		aggregate := s1.Union(s2)

		// 构建 []ccesdk.Subnet
		for _, item := range aggregate.List() {
			eniSubnets = append(eniSubnets, ccesdk.Subnet{
				SubnetID: item,
			})
		}
	}

	// 从 DescribeVPC 返回中匹配子网，补充信息
	for i := range eniSubnets {
		for _, vpcSubnet := range describeVPCResp.ShowVPCModel.Subnets {
			if vpcSubnet.SubnetID == eniSubnets[i].SubnetID {
				eniSubnets[i].SubnetName = vpcSubnet.Name
				eniSubnets[i].SubnetType = vpcSubnet.SubnetType
				eniSubnets[i].SubnetCIDR = vpcSubnet.CIDR
				eniSubnets[i].AvailableZone = tryToGetAvailableZoneIgnoreError(ctx, vpcSubnet.ZoneName)
				break
			}
		}
	}

	return &ccesdk.ClusterExtraInfo{
		MasterBLBSubnet: masterBLBSubnet,
		LBServiceSubnet: lbServiceSubnet,
		Subnets:         subnets,
		ENISubnets:      eniSubnets,
	}, nil
}

func tryToGetAvailableZoneIgnoreError(ctx context.Context, zoneName string) internalvpc.AvailableZone {
	availableZone, err := ccetypes.TransZoneNameToAvailableZone(ctx, zoneName)
	if err != nil {
		logger.Errorf(ctx, "TransZoneNameToAvailableZone failed: %v", err)
		// TODO: 暂时不堵塞
		availableZone = internalvpc.AvailableZone(zoneName)
	}

	return availableZone
}

func (s *service) getSubnet(ctx context.Context, subnetID string) (*ccesdk.Subnet, error) {
	subnet, err := s.clients.VPCClient.DescribeSubnet(ctx, subnetID, s.clients.STSClient.NewSignOption(ctx, s.accountID))
	if err != nil {
		logger.Errorf(ctx, "DescribeSubnet %s failed: %v", subnetID, err)
		return nil, err
	}

	availableZone := tryToGetAvailableZoneIgnoreError(ctx, subnet.ZoneName)

	return &ccesdk.Subnet{
		SubnetID:      subnet.SubnetID,
		SubnetName:    subnet.Name,
		SubnetType:    subnet.SubnetType,
		SubnetCIDR:    subnet.CIDR,
		AvailableZone: availableZone,
	}, nil
}

// Deprecated: cce network v1
func (s *service) getEniSubnetsFromCniAgentConfig(ctx context.Context) (sets.String, error) {
	var (
		set         = sets.NewString()
		agentConfig NodeAgentConfiguration
	)

	cm, err := s.adminKubeClient.CoreV1().ConfigMaps(CniNodeAgentConfigMapNamespace).Get(ctx, CniNodeAgentConfigMapName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get ConfigMap (%s/%s): %v", CniNodeAgentConfigMapNamespace, CniNodeAgentConfigMapName, err)
	}

	if _, ok := cm.Data["config"]; !ok {
		return nil, fmt.Errorf("ConfigMap (%s/%s) doesn't have key `config`", CniNodeAgentConfigMapNamespace, CniNodeAgentConfigMapName)
	}

	j, err := yaml.ToJSON([]byte(cm.Data["config"]))
	if err != nil {
		return nil, fmt.Errorf("failed to parse yaml to json: %v", err)
	}

	err = json.Unmarshal(j, &agentConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal json: %v", err)
	}

	logger.Infof(ctx, "bcc eni subnets: %v", agentConfig.CCE.ENIController.ENISubnetList)
	logger.Infof(ctx, "bbc pod subnets: %v", agentConfig.CCE.PodSubnetController.SubnetList)

	set.Insert(agentConfig.CCE.ENIController.ENISubnetList...)
	set.Insert(agentConfig.CCE.PodSubnetController.SubnetList...)

	return set, nil
}

// Deprecated: cce network v1
func (s *service) getEniSubnetsFromSubnetV1Alpha1CR(ctx context.Context) (sets.String, error) {
	var (
		set = sets.NewString()
	)

	subnetList, err := s.adminDynamicClient.Resource(SubnetV1Alpha1GVR).Namespace("default").List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list %s: %v", SubnetV1Alpha1GVR, err)
	}

	for _, item := range subnetList.Items {
		set.Insert(item.GetName())
	}

	logger.Infof(ctx, "subnet cr: %v", set.List())

	return set, nil
}

// isCRDNotFoundError 检查错误是否为CRD不存在或未定义的错误
func isCRDNotFoundError(err error) bool {

	// 使用k8s标准错误检查
	if kerrors.IsNotFound(err) {
		return true
	}

	// 检查CRD相关的错误信息
	errMsg := err.Error()
	return strings.Contains(errMsg, "no matches for kind") ||
		strings.Contains(errMsg, "the server could not find the requested resource") ||
		strings.Contains(errMsg, "could not find the requested resource")
}

// isConfigMapNotFoundError 检查错误是否为ConfigMap不存在的错误
func isConfigMapNotFoundError(err error) bool {

	// 首先尝试直接检查k8s错误
	if kerrors.IsNotFound(err) {
		return true
	}

	// 处理被fmt.Errorf包装后的错误，检查错误字符串中是否包含NotFound的特征
	errMsg := err.Error()
	return strings.Contains(errMsg, "not found")
}

// 获取容器网路 v2中的子网
// v2中所有子网对象都是用于容器网络的子网
func (s *service) getSubnetsFromCCEV1SubnetCR(ctx context.Context) (sets.String, error) {
	var (
		set = sets.NewString()
	)

	subnetList, err := s.adminDynamicClient.Resource(SubnetV1GVR).List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to list %s: %v", SubnetV1GVR, err)
	}

	for _, item := range subnetList.Items {
		set.Insert(item.GetName())
	}

	logger.Infof(ctx, "subnet v1 cr: %v", set.List())

	return set, nil
}

func (s *service) getEniSubnetsAndCniAgentConfig(ctx context.Context) (sets.String, *corev1.ConfigMap, error) {
	var (
		set         = sets.NewString()
		agentConfig NodeAgentConfiguration
	)

	cm, err := s.adminKubeClient.CoreV1().ConfigMaps(CniNodeAgentConfigMapNamespace).Get(ctx, CniNodeAgentConfigMapName, metav1.GetOptions{})
	if err != nil {
		return nil, nil, fmt.Errorf("failed to get ConfigMap (%s/%s): %v", CniNodeAgentConfigMapNamespace, CniNodeAgentConfigMapName, err)
	}

	if _, ok := cm.Data["config"]; !ok {
		return nil, nil, fmt.Errorf("ConfigMap (%s/%s) doesn't have key `config`", CniNodeAgentConfigMapNamespace, CniNodeAgentConfigMapName)
	}

	j, err := yaml.ToJSON([]byte(cm.Data["config"]))
	if err != nil {
		return nil, nil, fmt.Errorf("failed to parse yaml to json: %v", err)
	}

	err = json.Unmarshal(j, &agentConfig)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to unmarshal json: %v", err)
	}

	set.Insert(agentConfig.CCE.ENIController.ENISubnetList...)

	return set, cm, nil
}

// Deprecated: cce network v1
func (s *service) UpdatePodEniSubnets(ctx context.Context, clusterID string, req *ccesdk.UpdatePodEniSubnetsRequest) error {
	var (
		toBeAdded sets.String = sets.NewString()
	)
	if s.models != nil {
		// 查询集群所在 VPC 中所有子网
		if clusterID == "" {
			return errors.New("clusterID is empty")
		}
		cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
		if err != nil {
			logger.Errorf(ctx, "GetCluster failed: %v", err)
			return err
		}
		if cluster == nil {
			logger.Errorf(ctx, "cluster %s not exist", clusterID)
			return fmt.Errorf("cluster %s not exist", clusterID)
		}

		// 1. 为cce-network-v2增加新的eni子网
		if cluster.Spec != nil &&
			(cluster.Spec.ContainerNetworkConfig.Mode == ccetypes.ContainerNetworkModeVPCENI ||
				cluster.Spec.ContainerNetworkConfig.Mode == ccetypes.ContainerNetworkModeVPCExclusiveENI) {
			err = s.addCCENetworkV2Subnet(ctx, clusterID, req.Subnets)
			if err != nil {
				logger.Errorf(ctx, "addCCENetworkV2Subnet failed: %v", err)
				return err
			}

			// get subnets information from vpc to fetch az for each subnet
			// then convert data into a format that can be written to the database
			// and update data to database and crd cluster spec
			// ATTENTION: here we get all subnets for this cluster including psts subnets!!!
			//            so as to ensure all subnets info is recorded in database
			var (
				toUpdateSubnets []string
			)
			sbns, err := s.GetAllExistedSubnets(ctx, clusterID, false)
			if err != nil {
				logger.Errorf(ctx, "getAllExistedSubnets failed: %v", err)
				return err
			}
			for _, subnet := range req.Subnets {
				sbns.Insert(subnet)
			}
			toUpdateSubnets = append(toUpdateSubnets, sbns.List()...)

			describeVPCResp, err := s.clients.VPCClient.DescribeVPC(ctx, cluster.Spec.VPCID, s.clients.STSClient.NewSignOption(ctx, s.accountID))
			if err != nil {
				logger.Errorf(ctx, "DescribeVPC %s failed: %v", clusterID, err)
				return err
			}
			mapVPCSubnet2AvailableZone := make(map[string]internalvpc.AvailableZone)
			for _, vpcSubnet := range describeVPCResp.ShowVPCModel.Subnets {
				az := tryToGetAvailableZoneIgnoreError(ctx, vpcSubnet.ZoneName)
				mapVPCSubnet2AvailableZone[vpcSubnet.SubnetID] = az
			}
			// 按可用区组织子网数据
			subnetsInModelFormat := make(map[internalvpc.AvailableZone][]string)
			for _, subnetID := range toUpdateSubnets {
				az := mapVPCSubnet2AvailableZone[subnetID]
				subnetsInModelFormat[az] = append(subnetsInModelFormat[az], subnetID)
			}
			// 获取当前集群CRD
			clusterCRD, err := s.clients.MetaK8SClient.GetCluster(ctx, models.DefaultNamespace, clusterID, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "failed to get cluster CRD: %v", err)
				return err
			}

			// 更新CRD的spec字段
			clusterCRD.Spec.ContainerNetworkConfig.ENIVPCSubnetIDs = subnetsInModelFormat

			// 同时更新数据库和CRD
			if err := s.models.UpdateClusterSpecDBAndCRD(ctx, clusterID, s.accountID, &clusterCRD.Spec, &clusterCRD.ObjectMeta, nil, false, s.clients.MetaK8SClient.UpdateClusterSpec); err != nil {
				logger.Errorf(ctx, "UpdateClusterSpecDBAndCRD failed: %v", err)
				return fmt.Errorf("UpdateClusterSpecDBAndCRD failed: %v", err)
			}
			return nil
		}
	}

	// 2. 为cce-network-v1增加新的cni子网
	// 从 configMap 中获取已存在的子网和配置内容
	existed, cm, err := s.getEniSubnetsAndCniAgentConfig(ctx)
	if err != nil {
		logger.Errorf(ctx, "getEniSubnetsAndCniAgentConfig failed: %v", err)
		return err
	}

	// 从请求中添加子网
	for _, sbn := range req.Subnets {
		if !existed.Has(sbn) {
			toBeAdded.Insert(sbn)
		}
	}

	logger.Infof(ctx, "existed subnets: %v", existed.List())
	logger.Infof(ctx, "try to add eni subnets: %v", toBeAdded.List())

	// 生成新的配置文件
	var (
		content = cm.Data["config"]
	)

	for _, sbn := range toBeAdded.List() {
		idx := strings.Index(content, eniSubnetListMarker)
		if idx == -1 {
			return fmt.Errorf("failed to find eniSubnetList entry in configMap(%s/%s)", CniNodeAgentConfigMapNamespace,
				CniNodeAgentConfigMapName)
		}
		idx = idx + len(eniSubnetListMarker)
		str := fmt.Sprintf("\n      - %s", sbn)
		content = content[:idx] + str + content[idx:]
	}

	cm.Data["config"] = content

	// 更新 ConfigMap
	_, err = s.adminKubeClient.CoreV1().ConfigMaps(CniNodeAgentConfigMapNamespace).Update(ctx, cm, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "failed to update configMap(%s/%s): %v", CniNodeAgentConfigMapNamespace,
			CniNodeAgentConfigMapName, err)
		return err
	}

	// 重启 cni node agent
	cniAgentPodListOptions := metav1.ListOptions{
		LabelSelector: "name=cce-cni-node-agent",
	}
	err = s.adminKubeClient.CoreV1().Pods(CniNodeAgentConfigMapNamespace).DeleteCollection(ctx, *metav1.NewDeleteOptions(0), cniAgentPodListOptions)
	if err != nil {
		logger.Errorf(ctx, "failed to delete cni node agent pods: %v", err)
		return err
	}

	// 继续为cce-network-v1中使用ENI模式的集群更新子网到数据库和CRD
	// 只有当models不为nil且有新增子网时才尝试更新数据库（这里是为了与v2的检查models的逻辑一致防止出现遗留问题）
	if s.models != nil && len(toBeAdded) > 0 {
		cluster, err := s.models.GetCluster(ctx, clusterID, s.accountID)
		if err != nil {
			logger.Errorf(ctx, "GetCluster for v1 database update failed: %v", err)
			return err
		}

		if cluster == nil {
			logger.Errorf(ctx, "cluster %s not exist", clusterID)
			return fmt.Errorf("cluster %s not exist", clusterID)
		}

		// 只有特定的v1网络模式才需要更新数据库和CRD
		if cluster.Spec != nil && utils.IsVpcEniV1(cluster.Spec.ContainerNetworkConfig.Mode) {

			// 获取所有现有子网（包括新添加的，包括psts的，让数据库是全量)
			sbns, err := s.GetAllExistedSubnets(ctx, clusterID, true)
			if err != nil {
				logger.Errorf(ctx, "getAllExistedSubnets failed: %v", err)
				return err
			}
			var toUpdateSubnets []string
			toUpdateSubnets = append(toUpdateSubnets, sbns.List()...)

			// 查询VPC信息获取子网的可用区并记录到map中
			describeVPCResp, err := s.clients.VPCClient.DescribeVPC(ctx, cluster.Spec.VPCID, s.clients.STSClient.NewSignOption(ctx, s.accountID))
			if err != nil {
				logger.Errorf(ctx, "DescribeVPC %s failed: %v", clusterID, err)
				return err
			}
			mapVPCSubnet2AvailableZone := make(map[string]internalvpc.AvailableZone)
			for _, vpcSubnet := range describeVPCResp.ShowVPCModel.Subnets {
				az := tryToGetAvailableZoneIgnoreError(ctx, vpcSubnet.ZoneName)
				mapVPCSubnet2AvailableZone[vpcSubnet.SubnetID] = az
			}

			// 按可用区组织子网数据
			subnetsInModelFormat := make(map[internalvpc.AvailableZone][]string)
			for _, subnetID := range toUpdateSubnets {
				az := mapVPCSubnet2AvailableZone[subnetID]
				subnetsInModelFormat[az] = append(subnetsInModelFormat[az], subnetID)
			}

			// 获取当前集群CRD
			clusterCRD, err := s.clients.MetaK8SClient.GetCluster(ctx, models.DefaultNamespace, clusterID, &metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "failed to get cluster CRD for v1 network: %v", err)
				return err
			}

			// 更新CRD的spec字段
			clusterCRD.Spec.ContainerNetworkConfig.ENIVPCSubnetIDs = subnetsInModelFormat

			// 同时更新数据库和CRD
			if err := s.models.UpdateClusterSpecDBAndCRD(ctx, clusterID, s.accountID, &clusterCRD.Spec, &clusterCRD.ObjectMeta, nil, false, s.clients.MetaK8SClient.UpdateClusterSpec); err != nil {
				logger.Errorf(ctx, "UpdateClusterSpecDBAndCRD for v1 network failed: %v", err)
				return fmt.Errorf("UpdateClusterSpecDBAndCRD for v1 network failed: %v", err)
			}

			logger.Infof(ctx, "Updated database and CRD with subnets for v1 network mode %s: %v", cluster.Spec.ContainerNetworkConfig.Mode, subnetsInModelFormat)
		}
	} else if s.models == nil {
		logger.Infof(ctx, "Models is nil, maybe old cluster deployed without models, skipping database update for v1 network (ConfigMap update succeeded)")
	}

	return nil
}

func (s *service) GetAllExistedSubnets(ctx context.Context, clusterID string, isV1Network bool) (sets.String, error) {
	var (
		sbns sets.String
		err  error
	)
	if !isV1Network { //v2容器网络模式
		//first data source from cce-network-v2 subnet cr
		sbns, err = s.getSubnetsFromCCEV1SubnetCR(ctx)
		if err != nil {
			return nil, err
		}
		logger.Infof(ctx, "V2 network get Subnets From CR success: %v", sbns.List())

		// second data source from configmap kube-system/cce-network-v2-config
		cm, err := s.adminKubeClient.CoreV1().ConfigMaps(addon.AddonCCENetworkV2Namespace).Get(ctx, addon.CCENetworkV2ConfigMapName, metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "Get ConfigMap (%s/%s) failed: %v", addon.AddonCCENetworkV2Namespace, addon.CCENetworkV2ConfigMapName, err)
			return nil, err
		}
		_, cmSubnets, err := addon.GetV2ENISubnets(cm.Data)
		if err != nil {
			logger.Errorf(ctx, "GetV2ENISubnets failed: %v", err)
			return nil, err
		}
		logger.Infof(ctx, "Get Subnets from configmap success: %v", cmSubnets.List())

		sbns = sbns.Union(cmSubnets)
		logger.Infof(ctx, "V2 network get all existed subnets success, result is: %v", sbns.List())
	} else { // V1网络模式其subnet cr的api组、 configmap的名字和字段都与v2的不同
		// first data source from cce-network-v1 subnet cr
		sbns, err = s.getEniSubnetsFromSubnetV1Alpha1CR(ctx)
		if err != nil {
			// 兼容老旧集群：当CRD不存在或未定义时才打印警告，其他错误仍然返回
			if isCRDNotFoundError(err) {
				logger.Warnf(ctx, "V1 network get Subnets from CR failed (old cluster without CRD): %v", err)
				sbns = sets.NewString() // 初始化为空集合
			} else {
				return nil, err
			}
		} else {
			logger.Infof(ctx, "V1 network get Subnets from CR success, result is: %v", sbns.List())
		}

		// second data source from configmap kube-system/cce-cni-node-agent
		cmSubnets, _, err := s.getEniSubnetsAndCniAgentConfig(ctx)
		if err != nil {
			// 兼容老旧集群：只有当ConfigMap不存在时才打印警告，其他错误仍然返回
			if isConfigMapNotFoundError(err) {
				logger.Warnf(ctx, "V1 network get Subnets from ConfigMap failed (old cluster without ConfigMap): %v", err)
				cmSubnets = sets.NewString() // 初始化为空集合
			} else {
				logger.Errorf(ctx, "getEniSubnetsAndCniAgentConfig failed: %v", err)
				return nil, err
			}
		} else {
			logger.Infof(ctx, "V1 network get Subnets from ConfigMap success, result is: %v", cmSubnets.List())
		}

		sbns = sbns.Union(cmSubnets)
		logger.Infof(ctx, "V1 network get all existed subnets success, result is: %v", sbns.List())
	}

	return sbns, nil
}

// addCCENetworkV2Subnet 为cce-network-v2 增加新的子网
func (s *service) addCCENetworkV2Subnet(ctx context.Context, clusterID string, reqSubnet []string) error {
	//
	cm, err := s.adminKubeClient.CoreV1().ConfigMaps(addon.AddonCCENetworkV2Namespace).Get(ctx, addon.CCENetworkV2ConfigMapName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get ConfigMap (%s/%s): %v", addon.AddonCCENetworkV2Namespace, addon.CCENetworkV2ConfigMapName, err)
	}
	err = addon.InsertV2ENISubnet(cm.Data, reqSubnet)
	if err != nil {
		logger.Errorf(ctx, "failed to insert subnet to configMap(%s/%s): %v", addon.AddonCCENetworkV2Namespace, addon.CCENetworkV2ConfigMapName)
	}
	// 更新 ConfigMap
	_, err = s.adminKubeClient.CoreV1().ConfigMaps(CniNodeAgentConfigMapNamespace).Update(ctx, cm, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "failed to update configMap(%s/%s): %v", addon.AddonCCENetworkV2Namespace, addon.CCENetworkV2ConfigMapName, err)
		return err
	}

	// 删除 cce-network-operator
	cniAgentPodListOptions := metav1.ListOptions{
		LabelSelector: fmt.Sprintf("%s=%s", addon.CCENetworkV2AppLabelName, addon.CCENetworkV2AppLabelAgent),
	}
	err = s.adminKubeClient.CoreV1().Pods(addon.AddonCCENetworkV2Namespace).DeleteCollection(ctx, *metav1.NewDeleteOptions(0), cniAgentPodListOptions)
	if err != nil {
		logger.Errorf(ctx, "failed to delete cce-network-operator pods: %v", err)
		return err
	}

	logger.Infof(ctx, "add subnet [%+v] to cce-network-operator success", reqSubnet)
	return nil
}

// 按 IAM 权限过滤，用于具体集群的鉴权
// clusters 只用到 clusterID字段，如果使用其他字段，注意传入字段是否满足
func (s *service) FilterClusterListByPermission(ctx context.Context, accountID, userID string, signatureHeaders map[string]string, originList []*models.Cluster, permissions []string) ([]*models.Cluster, []iam.VerifyResult, error) {
	// 主用户，不过滤
	if accountID == userID {
		return originList, nil, nil
	}
	if len(originList) == 0 {
		return originList, nil, nil
	}
	var filteredClusters []*models.Cluster

	var verifyList []iam.VerifyEntity
	for i := range originList {
		requestCtx := iam.RequestContext{
			Variables: map[string]string{},
		}
		for _, tag := range originList[i].Spec.Tags {
			requestCtx.Variables[tag.TagKey] = tag.TagValue
		}
		verifyEntity := iam.VerifyEntity{
			Region:  string(s.config.ClientConfig.Region), // iam自定义策略会校验region的真实性，这里使用实际的iaas region
			Service: "bce:cce",
			Resource: []string{
				"CCE_Cluster/" + originList[i].Spec.ClusterID,
			},
			ResourceOwner:  accountID,
			Permission:     permissions,
			RequestContext: requestCtx,
		}

		verifyList = append(verifyList, verifyEntity)
	}

	verifyRequest := &iam.VerifyRequest{
		VerifyList:    verifyList,
		SecurityToken: signatureHeaders["x-bce-security-token"],
	}

	s.clients.IAMClient.SetDebug(true)

	// 请求 IAM 鉴权
	verifyResponse, err := s.clients.IAMClient.BatchVerifyPermissionByToken(ctx, verifyRequest, userID, s.config.ClientConfig.ServiceName, s.config.ClientConfig.ServicePassword,
		bce.NewSignOptionWithSessionToken(ctx, signatureHeaders["x-bce-security-token"]))
	if err != nil {
		logger.Errorf(ctx, "BatchVerifyPermissionByToken failed: %v", err)
		return nil, nil, err
	}

	if verifyResponse == nil {
		return nil, nil, errors.New("response is nil")
	}

	// 这里 iam 保证返回的ID跟请求的ID顺序一致
	for i, results := range verifyResponse.VerifyResults {
		if iam.EFFECT_ALLOW == results.Result[0].Effect {
			filteredClusters = append(filteredClusters, originList[i])
		}
	}

	return filteredClusters, verifyResponse.VerifyResults, nil
}

func (s *service) GetClusterScaleInfo(ctx context.Context, cluster *models.Cluster) (*ccesdk.ClusterScaleInfo, error) {
	if cluster == nil {
		return nil, errors.New("cluster is nil")
	}

	clusterScaleInfo := &ccesdk.ClusterScaleInfo{}

	// 1. 检查集群规格配置
	masterFlavor := cluster.Spec.MasterConfig.MasterFlavor
	flavorDeployConfig, ok := s.config.PluginConfig.FlavorConfig.FlavorConfigMap[string(masterFlavor)]
	if !ok {
		logger.Infof(ctx, "pluginConfig FlavorConfigMap not contain %s", string(masterFlavor))
		return clusterScaleInfo, nil
	}

	clusterScaleInfo.ClusterFlavor = masterFlavor
	clusterScaleInfo.ResourceLimit = flavorDeployConfig.ResourceLimit
	logger.Infof(ctx, "Get instances num from db")
	instancesNum, err := s.models.GetInstanceNum(ctx, cluster.Spec.AccountID, cluster.Spec.ClusterID, models.InstanceKeywordTypeDefault, "", false)
	if err != nil {
		logger.Errorf(ctx, "get instances failed: %v", err)
		return clusterScaleInfo, err
	}

	existedResourceNum := &ccesdk.ExistedResourceNum{
		InstanceNum: instancesNum,
	}

	var metricsErr, crdsErr error
	var storageObjects map[string]int
	var systemPodNum int32
	var crds *v1.CustomResourceDefinitionList

	wg := sync.WaitGroup{}

	logger.Infof(ctx, "Get metrics and crds")
	// 收集指标数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		storageObjects, metricsErr = s.collectMetrics(ctx)
	}()

	// 收集CRD数据
	wg.Add(1)
	go func() {
		defer wg.Done()
		startTime := time.Now()
		crds, crdsErr = s.adminExtendClient.ApiextensionsV1().CustomResourceDefinitions().List(ctx, metav1.ListOptions{})
		if crdsErr != nil {
			logger.Errorf(ctx, "get cluster crds failed: %v", err)
		}
		elapsed := time.Since(startTime)
		logger.Infof(ctx, "get cluster crds cost time: %v", elapsed)
	}()

	// 收集kube-system下面的pod数量
	wg.Add(1)
	go func() {
		defer wg.Done()
		startTime := time.Now()
		systemPodNum = s.getSystemPodNum(ctx)
		elapsed := time.Since(startTime)
		logger.Infof(ctx, "get system pod num cost time: %v", elapsed)
	}()

	// 等待所有goroutine完成
	wg.Wait()

	// 检查错误
	if metricsErr != nil {
		return nil, fmt.Errorf("failed to collect metrics: %v", metricsErr)
	}

	logger.Infof(ctx, "begin to process existed resource")
	// 处理指标数据
	if len(storageObjects) != 0 {
		existedResourceNum.NodeNum = storageObjects[ResourceNode]
		existedResourceNum.ServiceNum = storageObjects[ResourceServices]
		existedResourceNum.ConfigMapNum = storageObjects[ResourceConfigMaps]
		existedResourceNum.PVNum += storageObjects[ResourcePersistentVolumes]
		existedResourceNum.PVCNum += storageObjects[ResourcePersistentVolumeClaims]
		existedResourceNum.SecretNum += storageObjects[ResourceSecrets]

		existedResourceNum.PodNum = storageObjects[ResourcePods] - int(systemPodNum)
		// Metrics Server 默认一分钟进行一次监控数据同步，更新storageObjects[ResourcePods]中的数量，而systemPodNum的查询结果是实时的
		// Metrics Server 监控数据同步间隔期间扩容节点，会有概率出现PodNum为负数的情况，这里做容错处理
		if existedResourceNum.PodNum < 0 {
			existedResourceNum.PodNum = 0
		}
	}

	// 处理CRD数据
	if crds != nil && crdsErr == nil {
		for _, crd := range crds.Items {
			if strings.HasSuffix(crd.Name, BaiduCCECRDSuffix) {
				continue // 过滤系统组件创建的CR
			}
			existedResourceNum.CRDNum += storageObjects[crd.Name]
		}
	}

	clusterScaleInfo.ExistedResourceNum = existedResourceNum

	logger.Infof(ctx, "process existed resource success")
	return clusterScaleInfo, nil
}

// 将指标收集逻辑抽取为单独的方法
func (s *service) collectMetrics(ctx context.Context) (map[string]int, error) {
	storageObjects := make(map[string]int)

	logger.Infof(ctx, "get metrics from kube-apiserver")
	resp, err := s.adminKubeClient.CoreV1().RESTClient().Get().AbsPath(APIServeMetricsEndpoint).DoRaw(ctx) // 使用传入的ctx
	if err != nil {
		logger.Errorf(ctx, "kubeClient get --raw=/metrics failed: %v", err)

		if updateErr := s.updateClusterRoleCCEAdmin(ctx); updateErr != nil {
			logger.Errorf(ctx, "update clusterrole cce:admin failed: %v", updateErr)
			return nil, fmt.Errorf("failed to update admin role: %v", updateErr)
		}

		resp, err = s.adminKubeClient.CoreV1().RESTClient().Get().AbsPath(APIServeMetricsEndpoint).DoRaw(ctx)
		if err != nil {
			return nil, fmt.Errorf("failed to get metrics after role update: %v", err)
		}
	}

	// 确定过滤参数
	logger.Infof(ctx, "check k8s version")
	isHigherK8sVersion, err := utils.IsHigherSpecifiedK8sVersion(s.adminKubeClient, K8SVersion1220)
	if err != nil {
		return nil, fmt.Errorf("failed to check k8s version: %v", err)
	}

	filterParameter := ApiServerStorageObjects
	if !isHigherK8sVersion {
		filterParameter = ETCDObjectCounts
	}

	// 使用正则表达式解析指标
	re := regexp.MustCompile(filterParameter + ResourceMetricRegex)
	metrics := string(resp)

	logger.Infof(ctx, "parse metrics")
	for _, line := range strings.Split(metrics, "\n") {
		if strings.Contains(line, filterParameter) {
			matches := re.FindStringSubmatch(line)
			if len(matches) == 3 {
				resource := matches[1]
				value, err := strconv.Atoi(matches[2])
				if err == nil {
					storageObjects[resource] = value
				}
			}
		}
	}

	logger.Infof(ctx, "collect metrics success")
	return storageObjects, nil
}

// 获取 kube-system 下面的pod 数量
func (s *service) getSystemPodNum(ctx context.Context) int32 {
	var wg sync.WaitGroup

	wg.Add(1)
	var deployPodNum int32
	go func(deployPodNUm *int32) {
		defer wg.Done()
		deployments, err := s.adminKubeClient.AppsV1().Deployments("kube-system").List(ctx, metav1.ListOptions{})
		if err != nil {
			logger.Errorf(ctx, "list deployments in kube-system fail: %s", err)
		}

		for _, deploy := range deployments.Items {
			if _, ok := ccetypes.CCESystemPlugins[ccetypes.CCESystemPlugin(deploy.Name)]; ok {
				*deployPodNUm += deploy.Status.Replicas
			}
		}
	}(&deployPodNum)

	wg.Add(1)
	var stsPodNum int32
	go func(stsPodNUm *int32) {
		defer wg.Done()
		sts, err := s.adminKubeClient.AppsV1().StatefulSets("kube-system").List(ctx, metav1.ListOptions{})
		if err != nil {
			logger.Errorf(ctx, "list statefulSets in kube-system fail: %s", err)
		}
		for _, s := range sts.Items {
			if _, ok := ccetypes.CCESystemPlugins[ccetypes.CCESystemPlugin(s.Name)]; ok {
				*stsPodNUm += s.Status.Replicas
			}
		}
	}(&stsPodNum)

	wg.Add(1)
	var dsPodNum int32
	go func(dsPodNUm *int32) {
		defer wg.Done()
		ds, err := s.adminKubeClient.AppsV1().DaemonSets("kube-system").List(ctx, metav1.ListOptions{})
		if err != nil {
			logger.Errorf(ctx, "list daemonSets in kube-system fail: %s", err)
		}

		for _, d := range ds.Items {
			if _, ok := ccetypes.CCESystemPlugins[ccetypes.CCESystemPlugin(d.Name)]; ok {
				*dsPodNUm += d.Status.CurrentNumberScheduled
			}
		}
	}(&dsPodNum)

	wg.Wait()

	return deployPodNum + stsPodNum + dsPodNum

}

func (s *service) updateClusterRoleCCEAdmin(ctx context.Context) error {

	// 获取ClusterRole的引用
	clusterRole, err := s.adminKubeClient.RbacV1().ClusterRoles().Get(context.TODO(), ClusterRoleCCEAdmin, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get clusterrole cce:admin fail : %v ", err)
		return fmt.Errorf("get clusterrole cce:admin fail : %v ", err)
	}

	// 如果 cce:admin clusterRole.Rules 存在 NonResourceURLs，说明进行了专门的设置，那这里就不需要再去增加nonResourceURLs
	for _, rule := range clusterRole.Rules {
		if len(rule.NonResourceURLs) != 0 {
			logger.Infof(ctx, "cce:admin rule has nonResourceURLs, skip add nonResourceURLs")
			return nil
		}
	}

	// 添加nonResourceURLs规则
	newRule := rbacv1.PolicyRule{
		NonResourceURLs: []string{"*"},
		Verbs:           []string{"*"},
	}

	// 更新规则列表
	clusterRole.Rules = append(clusterRole.Rules, newRule)

	// 更新ClusterRole
	updatedClusterRole, err := s.adminKubeClient.RbacV1().ClusterRoles().Update(context.TODO(), clusterRole, metav1.UpdateOptions{})
	if err != nil {
		logger.Errorf(ctx, "update clusterrole cce:admin fail : %v ", err)
		return fmt.Errorf("update clusterrole cce:admin fail : %v ", err)
	}

	logger.Infof(ctx, "update clusterrole cce:admin success : %v", utils.ToJSON(updatedClusterRole))

	return nil
}

func (s *service) CNIDataPathV2Enabled(ctx context.Context, clusterID string) (bool, error) {
	cluster, err := s.clients.MetaK8SClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetCluster failed: %v", err)
		return false, err
	}
	if cluster == nil {
		logger.Errorf(ctx, "cluster %s not exist", clusterID)
		return false, fmt.Errorf("cluster %s not exist", clusterID)
	}

	if !cluster.Spec.ContainerNetworkConfig.EBPFConfig.Enabled {
		logger.Errorf(ctx, "EBPF is disabled for cluster %s, datapathv2 disabled ", clusterID)
		return false, nil
	}

	// 为了兼容存量开启 EBPF 加速的集群。
	// 如果 Cilium 版本小于 1.15，那么认为该集群不支持 datapathv2，前端显示：开启 EBPF 加速
	// 如果 Cilium 版本大于 1.15，那么认为该集群支持 datapathv2，前端显示：开启 DataPathV2
	ciliumOperator, err := s.adminKubeClient.AppsV1().Deployments(NamespaceKubeSystem).
		Get(ctx, DeploymentCiliumOperator, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "get cilium-operator fail : %v", err)
		return false, err
	}

	var ciliumImage string
	for i := range ciliumOperator.Spec.Template.Spec.Containers {
		container := ciliumOperator.Spec.Template.Spec.Containers[i]
		if container.Name != ContainerNameCiliumOperator {
			continue
		}

		ciliumImage = container.Image
		break
	}

	enabled, err := s.cniVersionGreaterThan(ctx, ciliumImage, CNIVersion115)
	if err != nil {
		logger.Errorf(ctx, "cniVersionGreaterThan fail : %v", err)
		return false, err
	}

	return enabled, nil
}

func (s *service) cniVersionGreaterThan(ctx context.Context, imageTag string, targetVersion string) (bool, error) {
	pos := strings.LastIndexByte(imageTag, ':')
	if pos == -1 || pos == len(imageTag)-1 {
		return false, fmt.Errorf("image %q has no tag", imageTag)
	}
	tag := imageTag[pos+1:]

	cmp, err := utils.CompareVersion(tag, targetVersion)
	return cmp == 1, err
}

func (s *service) FilterV1ClusterListByPermission(ctx context.Context, request *http.Request, accountID, userID string, originList []ccesdk.SimpleClusterVO) ([]ccesdk.SimpleClusterVO, error) {
	var filteredSimpleClusterVOList []ccesdk.SimpleClusterVO

	// 权限验证：批量验证集群访问权限（使用现有的权限验证逻辑）
	requestForm, err := auth.NewAuthenticationArgs(ctx, request)
	if err != nil {
		logger.Errorf(ctx, "NewAuthenticationArgs failed : %v", err)
		return filteredSimpleClusterVOList, err
	}

	permissionClusters := []*models.Cluster{}
	for _, simpleClusterVO := range originList {
		permissionClusters = append(permissionClusters, &models.Cluster{
			// 仅传入clusterUuid 进行鉴权，v1集群不涉及tag鉴权
			Spec: &ccetypes.ClusterSpec{
				ClusterID: simpleClusterVO.ClusterUuid,
			},
		})
	}

	logger.Infof(ctx, "permissionClusters num : %v", len(permissionClusters))

	// // 根据权限（权限包含了项目）过滤
	filteredClusters, _, err := s.FilterClusterListByPermission(ctx, accountID, userID, requestForm.SignatureHeaders, permissionClusters, []string{
		string(authorization.IAMClusterList),
		string(authorization.IAMClusterRead),
		string(authorization.IAMOperate),
		string(authorization.IAMClusterV1List),
	})
	if err != nil {
		logger.Errorf(ctx, "FilterClusterListByPermission failed : %v", err)
		return filteredSimpleClusterVOList, err
	}
	logger.Infof(ctx, "filteredClusters num : %v", len(filteredClusters))

	if len(filteredClusters) == 0 {
		logger.Infof(ctx, "vertify result null")
		// 返回空的集群列表
		return filteredSimpleClusterVOList, nil
	}

	// 筛选出通过鉴权的cluster
	filteredClusterMap := make(map[string]bool)
	for _, filteredCluster := range filteredClusters {
		filteredClusterMap[filteredCluster.Spec.ClusterID] = true
	}

	for _, simpleClusterVO := range originList {
		if _, ok := filteredClusterMap[simpleClusterVO.ClusterUuid]; ok {
			filteredSimpleClusterVOList = append(filteredSimpleClusterVOList, simpleClusterVO)
		}
	}

	return filteredSimpleClusterVOList, nil
}
