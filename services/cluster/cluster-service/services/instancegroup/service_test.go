/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  service_test
 * @Version: 1.0.0
 * @Date: 2020/6/29 3:47 下午
 */
package instancegroup

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"reflect"
	"sync"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"gotest.tools/assert"
	corev1 "k8s.io/api/core/v1"
	k8serr "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"

	pluginclients "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	mock2 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	ccesdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccev2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalvpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	mockClientset "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset/mock"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/task/k8s"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	modelcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	mock3 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec"
	fillclients "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec/clients"
	fillclientsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec/clients/mock"
	instancefillermock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/fillspec/instance/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/quota"
	quotamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/quota/mock"
	userscriptmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-service/services/userscript/mock"
)

type syncSets struct {
	lock sync.Mutex
	data map[string]struct{}
}

func newSyncSets() *syncSets {
	return &syncSets{
		lock: sync.Mutex{},
		data: make(map[string]struct{}),
	}
}

func (ss *syncSets) add(key string) {
	ss.lock.Lock()
	defer ss.lock.Unlock()
	ss.data[key] = struct{}{}
}

func (ss *syncSets) del(key string) {
	ss.lock.Lock()
	defer ss.lock.Unlock()
	delete(ss.data, key)
}

func (ss *syncSets) len() int {
	ss.lock.Lock()
	defer ss.lock.Unlock()
	return len(ss.data)
}

func (ss *syncSets) has(key string) bool {
	ss.lock.Lock()
	defer ss.lock.Unlock()
	_, ok := ss.data[key]
	return ok
}

func (ss *syncSets) equal(other *syncSets) bool {
	return ss.len() == other.len()
}

func Test_scaleUpExistNodeNotInCluster(t *testing.T) {
	s := &InstanceGroupService{
		K8SClient: &meta.Client{},
		model:     &models.Client{},
	}
	patches := gomonkey.ApplyFuncReturn((*services.Service).EnsureInstanceSets, nil)
	patches = gomonkey.ApplyFuncReturn((*services.Service).InstanceSetToModel, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*models.Client).CreateInstances, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*InstanceGroupService).createInstances, []*ccetypes.InstancesToJoin{{}}, fmt.Errorf("test"))
	defer patches.Reset()
	_, err := s.scaleUpExistNodeNotInCluster(context.TODO(), &services.Service{}, &ccev1.Cluster{}, &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					InstanceChargingType: bcc.PaymentTimingPrepaid,
				},
			},
		},
	}, true, []*ccesdk.InstanceSet{{
		InstanceSpec: ccetypes.InstanceSpec{
			InstanceChargingType: bcc.PaymentTimingPostpaid,
		},
	}}, false)
	assert.Equal(t, err != nil, true)
}

func Test_scaleUpExistNodeNotInCluster_UseIGRuntime(t *testing.T) {
	s := &InstanceGroupService{
		K8SClient: &meta.Client{},
		model:     &models.Client{},
	}
	patches := gomonkey.ApplyFuncReturn((*services.Service).EnsureInstanceSets, nil)
	patches = gomonkey.ApplyFuncReturn((*services.Service).InstanceSetToModel, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*models.Client).CreateInstances, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*InstanceGroupService).createInstances, []*ccetypes.InstancesToJoin{{}}, fmt.Errorf("test"))
	defer patches.Reset()
	_, err := s.scaleUpExistNodeNotInCluster(context.TODO(), &services.Service{}, &ccev1.Cluster{}, &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					RuntimeType:    ccetypes.RuntimeTypeContainerd,
					RuntimeVersion: "1.6.36",
				},
			},
		},
	}, true, []*ccesdk.InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				InstanceResource: ccetypes.InstanceResource{
					CDSList:           nil,
					EphemeralDiskList: nil,
				},
			},
			Count: 2,
		},
	}, false)
	assert.Equal(t, models.ErrPartialSuccess.Is(err), true)
}

func Test_scaleUpExistNodeNotInCluster_UseClusterRuntime(t *testing.T) {
	s := &InstanceGroupService{
		K8SClient: &meta.Client{},
		model:     &models.Client{},
	}
	patches := gomonkey.ApplyFuncReturn((*services.Service).EnsureInstanceSets, nil)
	patches = gomonkey.ApplyFuncReturn((*services.Service).InstanceSetToModel, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*models.Client).CreateInstances, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*InstanceGroupService).createInstances, nil, fmt.Errorf("test"))
	defer patches.Reset()
	_, err := s.scaleUpExistNodeNotInCluster(context.TODO(), &services.Service{}, &ccev1.Cluster{
		Spec: ccetypes.ClusterSpec{
			RuntimeType:    ccetypes.RuntimeTypeContainerd,
			RuntimeVersion: "1.6.36",
		},
	}, &ccev1.InstanceGroup{}, true, []*ccesdk.InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				InstanceResource: ccetypes.InstanceResource{
					CDSList:           nil,
					EphemeralDiskList: nil,
				},
			},
			Count: 2,
		},
	}, false)
	assert.Equal(t, models.ErrPartialSuccess.Is(err), false)
}

func Test_scaleUpExistNodeNotInCluster_EmptyRuntime(t *testing.T) {
	s := &InstanceGroupService{
		K8SClient: &meta.Client{},
		model:     &models.Client{},
	}
	patches := gomonkey.ApplyFuncReturn((*services.Service).EnsureInstanceSets, nil)
	patches = gomonkey.ApplyFuncReturn((*services.Service).InstanceSetToModel, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*models.Client).CreateInstances, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*InstanceGroupService).createInstances, nil, fmt.Errorf("test"))
	defer patches.Reset()
	_, err := s.scaleUpExistNodeNotInCluster(context.TODO(), &services.Service{}, &ccev1.Cluster{}, &ccev1.InstanceGroup{}, true, []*ccesdk.InstanceSet{
		{
			InstanceSpec: ccetypes.InstanceSpec{
				InstanceResource: ccetypes.InstanceResource{
					CDSList:           nil,
					EphemeralDiskList: nil,
				},
			},
			Count: 2,
		},
	}, false)
	assert.Equal(t, models.ErrPartialSuccess.Is(err), false)
}

func Test_scaleUpExistNodeNotInCluster_2(t *testing.T) {
	s := &InstanceGroupService{
		K8SClient: &meta.Client{},
		model:     &models.Client{},
	}
	patches := gomonkey.ApplyFuncReturn((*services.Service).EnsureInstanceSets, nil)
	patches = gomonkey.ApplyFuncReturn((*services.Service).InstanceSetToModel, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*models.Client).CreateInstances, nil, nil)
	patches = gomonkey.ApplyFuncReturn((*InstanceGroupService).createInstances, []*ccetypes.InstancesToJoin{{}}, fmt.Errorf("test"))
	defer patches.Reset()
	_, err := s.scaleUpExistNodeNotInCluster(context.TODO(), &services.Service{}, &ccev1.Cluster{}, &ccev1.InstanceGroup{}, true, nil, false)
	assert.Equal(t, models.ErrPartialSuccess.Is(err), true)
}

func Test_createInstances(t *testing.T) {
	mockDB := newSyncSets()
	mockCluster := newSyncSets()

	type fields struct {
		instances []*ccev1.Instance
	}
	tests := []struct {
		name   string
		fields fields
	}{
		{
			name: "正常",
			fields: func() fields {
				instances := make([]*ccev1.Instance, 0)
				for i := 0; i < 100; i++ {
					instances = append(instances, &ccev1.Instance{
						ObjectMeta: metav1.ObjectMeta{
							Name: fmt.Sprintf("instance-%d", i),
						},
					})
					mockDB.add(fmt.Sprintf("instance-%d", i))
				}
				return fields{
					instances: instances,
				}
			}(),
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			patches := gomonkey.ApplyFunc((*meta.Client).CreateInstance, func(self *meta.Client, ctx context.Context, ns string, instance *ccev1.Instance) (*ccev1.Instance, error) {
				if rand.Float64() < 0.5 {
					return nil, fmt.Errorf("create %v crd failed", instance.Name)
				}
				mockCluster.add(instance.Name)
				return nil, nil
			})
			patches = gomonkey.ApplyFunc((*models.Client).UpdateInstancePhase, func(self *models.Client, ctx context.Context, cceInstanceID string, accountID string, phase ccetypes.InstancePhase) error {
				if !mockDB.has(cceInstanceID) {
					return models.ErrNotExist.New(ctx, cceInstanceID)
				}
				mockDB.del(cceInstanceID)
				return nil
			})
			defer patches.Reset()
			s := &InstanceGroupService{
				K8SClient: &meta.Client{},
				model:     &models.Client{},
			}
			instances, err := s.createInstances(context.TODO(), tt.fields.instances, 3)
			fmt.Println(err)
			fmt.Println(len(instances))
			assert.Equal(t, len(instances), mockCluster.len())
			assert.Equal(t, len(instances), mockDB.len())
		})
	}
}

func TestInstanceGroupService_verifyAndPadDefault(t *testing.T) {
	type fields struct {
		ctl          *gomock.Controller
		filler       *fillspec.BaseFiller
		clients      fillclients.Clients
		clusterSpec  *ccetypes.ClusterSpec
		instanceSpec *ccetypes.InstanceSpec
		quotaClient  quota.Interface
	}
	type args struct {
		ctx               context.Context
		clusterSpec       *ccetypes.ClusterSpec
		instanceGroupSpec *ccetypes.InstanceGroupSpec
	}

	tests := []struct {
		name        string
		fields      fields
		args        args
		expectedErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				instanceFiller := instancefillermock.NewMockInterface(ctl)
				clients := fillclientsmock.NewMockClients(ctl)
				quotaClient := quotamock.NewMockInterface(ctl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "user-id",
					AccountID: "account-id",
					Config: &configuration.Config{
						Handler: "xxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				}

				instance := &ccetypes.InstanceSpec{
					ClusterRole: ccetypes.ClusterRoleNode,
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instanceFiller, nil).AnyTimes()
				instanceFiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.InstanceTemplateID = "test"
					instance.Handler = "xxx"
					instance.ClusterID = "cluster-id"
					instance.ClusterRole = ccetypes.ClusterRoleNode
					instance.AccountID = "account-id"
					instance.UserID = "user-id"
					instance.RuntimeType = ccetypes.RuntimeTypeDocker
					instance.RuntimeVersion = "1.18.0"
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil).AnyTimes()
				instanceFiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil).AnyTimes()
				instanceFiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil).AnyTimes()
				instanceFiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil).AnyTimes()
				instanceFiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "os-arch",
					OSBuild:   "os-build",
				}, nil).AnyTimes()
				instanceFiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil).AnyTimes()
				instanceFiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil).AnyTimes()
				instanceFiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil).AnyTimes()
				instanceFiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil).AnyTimes()
				instanceFiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{
					KubeReserved:   k8s.GenKubeletReserved(ctx, instance.CPU, instance.MEM, instance.DeployCustomConfig.KubeReserved),
					SystemReserved: k8s.GenKubeletReserved(ctx, instance.CPU, instance.MEM, instance.DeployCustomConfig.SystemReserved),
				}, nil).AnyTimes()
				instanceFiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil).AnyTimes()
				instanceFiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil).AnyTimes()
				//)

				return fields{
					ctl:          ctl,
					filler:       baseFiller,
					clients:      clients,
					clusterSpec:  clusterSpec,
					instanceSpec: instance,
					quotaClient:  quotaClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				clusterSpec: &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				},
				instanceGroupSpec: &ccetypes.InstanceGroupSpec{
					Handler:            "",
					CCEInstanceGroupID: "",
					InstanceGroupName:  "xxx",
					ClusterID:          "",
					ClusterRole:        ccetypes.ClusterRoleNode,
					UserID:             "",
					AccountID:          "",
					Selector:           nil,
					ShrinkPolicy:       "",
					UpdatePolicy:       "",
					CleanPolicy:        "",
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							ClusterRole:        ccetypes.ClusterRoleNode,
							Handler:            "xxx",
							ClusterID:          "cluster-id",
							AccountID:          "account-id",
							UserID:             "user-id",
							RuntimeType:        ccetypes.RuntimeTypeDocker,
							RuntimeVersion:     "1.18.0",
						},
					},
					Replicas:              0,
					ClusterAutoscalerSpec: nil,
				},
			},
			expectedErr: false,
		},
		{
			name: "正常流程-instanceTemplates",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				instancefiller := instancefillermock.NewMockInterface(ctl)
				clients := fillclientsmock.NewMockClients(ctl)
				quotaClient := quotamock.NewMockInterface(ctl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "user-id",
					AccountID: "account-id",
					Config: &configuration.Config{
						Handler: "xxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				}

				instance := &ccetypes.InstanceSpec{
					ClusterRole: ccetypes.ClusterRoleNode,
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.InstanceTemplateID = "test"
					instance.Handler = "xxx"
					instance.ClusterID = "cluster-id"
					instance.ClusterRole = ccetypes.ClusterRoleNode
					instance.AccountID = "account-id"
					instance.UserID = "user-id"
					instance.RuntimeType = ccetypes.RuntimeTypeDocker
					instance.RuntimeVersion = "1.18.0"
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil)
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "os-arch",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)
				//)

				return fields{
					ctl:          ctl,
					filler:       baseFiller,
					clients:      clients,
					clusterSpec:  clusterSpec,
					instanceSpec: instance,
					quotaClient:  quotaClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				clusterSpec: &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				},
				instanceGroupSpec: &ccetypes.InstanceGroupSpec{
					Handler:            "",
					CCEInstanceGroupID: "",
					InstanceGroupName:  "xxx",
					ClusterID:          "",
					ClusterRole:        ccetypes.ClusterRoleNode,
					UserID:             "",
					AccountID:          "",
					Selector:           nil,
					ShrinkPolicy:       "",
					UpdatePolicy:       "",
					CleanPolicy:        "",
					InstanceTemplates: []ccetypes.InstanceTemplate{
						{
							ccetypes.InstanceSpec{
								InstanceTemplateID: "test",
								ClusterRole:        ccetypes.ClusterRoleNode,
								Handler:            "xxx",
								ClusterID:          "cluster-id",
								AccountID:          "account-id",
								UserID:             "user-id",
								RuntimeType:        ccetypes.RuntimeTypeDocker,
								RuntimeVersion:     "1.18.0",
							},
						},
					},
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							ClusterRole:        ccetypes.ClusterRoleNode,
							Handler:            "xxx",
							ClusterID:          "cluster-id",
							AccountID:          "account-id",
							UserID:             "user-id",
							RuntimeType:        ccetypes.RuntimeTypeDocker,
							RuntimeVersion:     "1.18.0",
						},
					},
					Replicas:              0,
					ClusterAutoscalerSpec: nil,
				},
			},
			expectedErr: false,
		},
		{
			name: "异常流程-instanceTemplates-预付费-开启自动扩缩容",
			fields: func() fields {
				//ctx := context.TODO()
				ctl := gomock.NewController(t)

				//instancefiller := instancefillermock.NewMockInterface(ctl)
				clients := fillclientsmock.NewMockClients(ctl)
				quotaClient := quotamock.NewMockInterface(ctl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "user-id",
					AccountID: "account-id",
					Config: &configuration.Config{
						Handler: "xxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				}

				instance := &ccetypes.InstanceSpec{
					ClusterRole: ccetypes.ClusterRoleNode,
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				return fields{
					ctl:          ctl,
					filler:       baseFiller,
					clients:      clients,
					clusterSpec:  clusterSpec,
					instanceSpec: instance,
					quotaClient:  quotaClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				clusterSpec: &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				},
				instanceGroupSpec: &ccetypes.InstanceGroupSpec{
					Handler:            "",
					CCEInstanceGroupID: "",
					InstanceGroupName:  "xxx",
					ClusterID:          "",
					ClusterRole:        ccetypes.ClusterRoleNode,
					UserID:             "",
					AccountID:          "",
					Selector:           nil,
					ShrinkPolicy:       "",
					UpdatePolicy:       "",
					CleanPolicy:        "",
					InstanceTemplates: []ccetypes.InstanceTemplate{
						{
							ccetypes.InstanceSpec{
								InstanceChargingType: bcc.PaymentTimingPrepaid,
								InstanceTemplateID:   "test",
								ClusterRole:          ccetypes.ClusterRoleNode,
								Handler:              "xxx",
								ClusterID:            "cluster-id",
								AccountID:            "account-id",
								UserID:               "user-id",
								RuntimeType:          ccetypes.RuntimeTypeDocker,
								RuntimeVersion:       "1.18.0",
							},
						},
					},
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							ClusterRole:        ccetypes.ClusterRoleNode,
							Handler:            "xxx",
							ClusterID:          "cluster-id",
							AccountID:          "account-id",
							UserID:             "user-id",
							RuntimeType:        ccetypes.RuntimeTypeDocker,
							RuntimeVersion:     "1.18.0",
						},
					},
					Replicas: 0,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled: true,
					},
				},
			},
			expectedErr: true,
		},
		{
			name: "异常流程-instanceTemplates-预付费-开启自动扩缩容",
			fields: func() fields {
				//ctx := context.TODO()
				ctl := gomock.NewController(t)

				//instancefiller := instancefillermock.NewMockInterface(ctl)
				clients := fillclientsmock.NewMockClients(ctl)
				quotaClient := quotamock.NewMockInterface(ctl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "user-id",
					AccountID: "account-id",
					Config: &configuration.Config{
						Handler: "xxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				}

				instance := &ccetypes.InstanceSpec{
					ClusterRole: ccetypes.ClusterRoleNode,
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				return fields{
					ctl:          ctl,
					filler:       baseFiller,
					clients:      clients,
					clusterSpec:  clusterSpec,
					instanceSpec: instance,
					quotaClient:  quotaClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				clusterSpec: &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				},
				instanceGroupSpec: &ccetypes.InstanceGroupSpec{
					Handler:            "",
					CCEInstanceGroupID: "",
					InstanceGroupName:  "xxx",
					ClusterID:          "",
					ClusterRole:        ccetypes.ClusterRoleNode,
					UserID:             "",
					AccountID:          "",
					Selector:           nil,
					ShrinkPolicy:       "",
					UpdatePolicy:       "",
					CleanPolicy:        "",
					InstanceTemplates: []ccetypes.InstanceTemplate{
						{
							ccetypes.InstanceSpec{
								InstanceChargingType: bcc.PaymentTimingPrepaid,
								InstanceTemplateID:   "test",
								ClusterRole:          ccetypes.ClusterRoleNode,
								Handler:              "xxx",
								ClusterID:            "cluster-id",
								AccountID:            "account-id",
								UserID:               "user-id",
								RuntimeType:          ccetypes.RuntimeTypeDocker,
								RuntimeVersion:       "1.18.0",
							},
						},
					},
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							ClusterRole:        ccetypes.ClusterRoleNode,
							Handler:            "xxx",
							ClusterID:          "cluster-id",
							AccountID:          "account-id",
							UserID:             "user-id",
							RuntimeType:        ccetypes.RuntimeTypeDocker,
							RuntimeVersion:     "1.18.0",
						},
					},
					Replicas: 0,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled: true,
					},
				},
			},
			expectedErr: true,
		},
		{
			name: "异常流程-instanceTemplates-预付费-开启自动扩缩容",
			fields: func() fields {
				//ctx := context.TODO()
				ctl := gomock.NewController(t)

				//instancefiller := instancefillermock.NewMockInterface(ctl)
				clients := fillclientsmock.NewMockClients(ctl)
				quotaClient := quotamock.NewMockInterface(ctl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "user-id",
					AccountID: "account-id",
					Config: &configuration.Config{
						Handler: "xxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				}

				instance := &ccetypes.InstanceSpec{
					ClusterRole: ccetypes.ClusterRoleNode,
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				return fields{
					ctl:          ctl,
					filler:       baseFiller,
					clients:      clients,
					clusterSpec:  clusterSpec,
					instanceSpec: instance,
					quotaClient:  quotaClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				clusterSpec: &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				},
				instanceGroupSpec: &ccetypes.InstanceGroupSpec{
					Handler:            "",
					CCEInstanceGroupID: "",
					InstanceGroupName:  "xxx",
					ClusterID:          "",
					ClusterRole:        ccetypes.ClusterRoleNode,
					UserID:             "",
					AccountID:          "",
					Selector:           nil,
					ShrinkPolicy:       "",
					UpdatePolicy:       "",
					CleanPolicy:        "",
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							InstanceTemplateID:   "test",
							ClusterRole:          ccetypes.ClusterRoleNode,
							Handler:              "xxx",
							ClusterID:            "cluster-id",
							AccountID:            "account-id",
							UserID:               "user-id",
							RuntimeType:          ccetypes.RuntimeTypeDocker,
							RuntimeVersion:       "1.18.0",
						},
					},
					Replicas: 0,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled: true,
					},
				},
			},
			expectedErr: true,
		},
		{
			name: "预付费参数校验失败",
			fields: func() fields {
				//ctx := context.TODO()
				ctl := gomock.NewController(t)

				//instancefiller := instancefillermock.NewMockInterface(ctl)
				clients := fillclientsmock.NewMockClients(ctl)
				quotaClient := quotamock.NewMockInterface(ctl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "user-id",
					AccountID: "account-id",
					Config: &configuration.Config{
						Handler: "xxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				}

				instance := &ccetypes.InstanceSpec{
					ClusterRole: ccetypes.ClusterRoleNode,
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				return fields{
					ctl:          ctl,
					filler:       baseFiller,
					clients:      clients,
					clusterSpec:  clusterSpec,
					instanceSpec: instance,
					quotaClient:  quotaClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				clusterSpec: &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "account-id",
					UserID:         "user-id",
				},
				instanceGroupSpec: &ccetypes.InstanceGroupSpec{
					Handler:            "",
					CCEInstanceGroupID: "",
					InstanceGroupName:  "xxx",
					ClusterID:          "",
					ClusterRole:        ccetypes.ClusterRoleNode,
					UserID:             "",
					AccountID:          "",
					Selector:           nil,
					ShrinkPolicy:       "",
					UpdatePolicy:       "",
					CleanPolicy:        "",
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceChargingType: bcc.PaymentTimingPrepaid,
							InstancePreChargingOption: ccetypes.InstancePreChargingOption{
								AutoRenew:         true,
								AutoRenewTime:     10,
								AutoRenewTimeUnit: "month",
								PurchaseTimeUnit:  "month",
								PurchaseTime:      10,
							},
							InstanceTemplateID: "test",
							ClusterRole:        ccetypes.ClusterRoleNode,
							Handler:            "xxx",
							ClusterID:          "cluster-id",
							AccountID:          "account-id",
							UserID:             "user-id",
							RuntimeType:        ccetypes.RuntimeTypeDocker,
							RuntimeVersion:     "1.18.0",
						},
					},
					Replicas: 0,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled: false,
					},
				},
			},
			expectedErr: true,
		},
	}

	for _, tc := range tests {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.fields.ctl.Finish()

			s := &InstanceGroupService{
				log:         logger.WithValues("name", "test"),
				filler:      tc.fields.filler,
				fillClients: tc.fields.clients,
				quotaClient: tc.fields.quotaClient,
			}

			err := s.VerifyAndPadDefault(tc.args.ctx, tc.args.clusterSpec, tc.args.instanceGroupSpec, "")
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

func TestInstanceGroupService_Create(t *testing.T) {
	type mocks struct {
		ctrl        *gomock.Controller
		model       models.Interface
		K8SClient   meta.Interface
		quotaClient quota.Interface
		filler      *fillspec.BaseFiller
		clients     fillclients.Clients
	}

	instance := ccetypes.InstanceSpec{
		InstanceTemplateID: "test",
		Handler:            "xxxx",
		CCEInstanceID:      "iiii",
		AdminPassword:      "pppp",
		ClusterID:          "cluster-id",
		ClusterRole:        "node",
		AccountID:          "ffff",
		UserID:             "ffff",
		RuntimeType:        ccetypes.RuntimeTypeDocker,
		RuntimeVersion:     "1.18.0",
		ScaleDownDisabled:  true,
		VPCConfig: ccetypes.VPCConfig{
			VPCID: "dddd",
		},
		DeployCustomConfig: ccetypes.DeployCustomConfig{
			KubeletRootDir: "/var/lib/kubelet",
			DockerConfig: ccetypes.DockerConfig{
				DockerDataRoot: "/var/lib/docker",
			},
			PreUserScript:  "cce-us-xxxxxx01",
			PostUserScript: "cce-us-xxxxxx01",
			EnableCordon:   true,
		},
		Tags: ccetypes.TagList{
			{
				TagKey:   "k",
				TagValue: "v",
			},
		},
		Labels: ccetypes.InstanceLabels{
			"lk": "lv",
		},
		Taints: ccetypes.InstanceTaints{
			{
				Key:    "tk",
				Value:  "tv",
				Effect: corev1.TaintEffectNoExecute,
			},
		},
	}
	instanceA := instance.DeepCopy()

	testCases := []struct {
		name        string
		mocks       *mocks
		ig          *ccetypes.InstanceGroupSpec
		expectedErr bool
	}{
		{
			name: "create field with different cpu",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clu-xxx",
						AccountID: "acc-xxx",
					},
					Status: nil,
				}, nil)

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("", nil)

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

				k8sClient := mock.NewMockInterface(ctrl)

				quotaClient := quotamock.NewMockInterface(ctrl)
				quotaClient.EXPECT().WithCache(gomock.Any()).Return(quotaClient).AnyTimes()
				quotaClient.EXPECT().GetNodeQuotaWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clu-xxx",
						AccountID: "acc-xxx",
					},
					Status: nil,
				}, nil)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					filler:      baseFiller,
				}
			}(),

			ig: &ccetypes.InstanceGroupSpec{
				Replicas: 0,
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  "BCC",
						InstanceType: "N3",
						InstanceName: "",
						// InstanceOS: ccetypes.InstanceOS{
						// 	OSArch: "os-arch",
						// },
					},
				},
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							MachineType:        "BCC",
							InstanceType:       "N3",
							InstanceName:       "",
							ImageID:            "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
							InstanceOS: ccetypes.InstanceOS{
								ImageType: "System",
							},
							InstanceResource: ccetypes.InstanceResource{
								CPU:          4,
								MEM:          8,
								RootDiskType: "enhanced_ssd_pl1",
								RootDiskSize: 100,
								GPUType:      "",
								GPUCount:     1,
								SpecID:       "g3",
								MachineSpec:  "bcc.g3.c4m8",
							},
							VPCConfig: ccetypes.VPCConfig{

								VPCSubnetID:       "sbn-uhj41zevmcty",
								SecurityGroupType: "normal",
								SecurityGroup:     ccetypes.SecurityGroup{},
							},
						},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							MachineType:        "BCC",
							InstanceType:       "N5",
							InstanceName:       "",
							ImageID:            "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
							InstanceOS: ccetypes.InstanceOS{
								ImageType: "System",
							},
							InstanceResource: ccetypes.InstanceResource{
								CPU:          2,
								MEM:          8,
								RootDiskType: "enhanced_ssd_pl1",
								RootDiskSize: 100,
								GPUType:      "",
								GPUCount:     1,
								SpecID:       "g4",
								MachineSpec:  "bcc.g4.c2m8",
							},
							VPCConfig: ccetypes.VPCConfig{

								VPCSubnetID:       "sbn-jsva4jyn8287",
								SecurityGroupType: "normal",
								SecurityGroup:     ccetypes.SecurityGroup{},
							},
						},
					},
				},
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: false,
				},
			},

			expectedErr: true,
		},
		{
			name: "create err with userID | accountID is nil",
			mocks: func() *mocks {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clu-xxx",
						AccountID: "acc-xxx",
					},
					Status: nil,
				}, nil)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clu-xxx",
						AccountID: "acc-xxx",
					},
					Status: nil,
				}, nil)

				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				instance := ccetypes.InstanceSpec{
					InstanceTemplateID: "test",
					Handler:            "xxxx",
					CCEInstanceID:      "iiii",
					AdminPassword:      "pppp",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					AccountID:          "ffff",
					UserID:             "ffff",
					RuntimeType:        ccetypes.RuntimeTypeDocker,
					RuntimeVersion:     "1.18.0",

					VPCConfig: ccetypes.VPCConfig{
						VPCID: "dddd",
					},
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeletRootDir: "/var/lib/kubelet",
						DockerConfig: ccetypes.DockerConfig{
							DockerDataRoot: "/var/lib/docker",
						},
						PreUserScript:  "cce-us-xxxxxx01",
						PostUserScript: "cce-us-xxxxxx01",
						EnableCordon:   true,
					},
					Tags: ccetypes.TagList{
						{
							TagKey:   "k",
							TagValue: "v",
						},
					},
					Labels: ccetypes.InstanceLabels{
						"lk": "lv",
					},
					Taints: ccetypes.InstanceTaints{
						{
							Key:    "tk",
							Value:  "tv",
							Effect: corev1.TaintEffectNoExecute,
						},
					},
				}

				gomock.InOrder(
					quotaClient.EXPECT().WithCache(gomock.Any()).Return(quotaClient).AnyTimes(),
					quotaClient.EXPECT().GetNodeQuotaWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
						Quota: 10,
						Used:  0,
					}, nil).AnyTimes(),
					clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil).Times(2),
					instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						return instance.DeepCopy()
					}()).Return(false, ccetypes.ExistedOption{}, nil),
					instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.Existed = false
						return instance.DeepCopy()
					}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil),
					instancefiller.EXPECT().VPC(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.MachineType = ccetypes.MachineTypeBCC
						instance.InstanceType = bcc.InstanceTypeN3
						return instance.DeepCopy()
					}()).Return(ccetypes.VPCConfig{
						VPCID:             "vpc-id",
						VPCSubnetID:       "vpc-subnet-id",
						SecurityGroupID:   "security-group-id",
						VPCUUID:           "vpc-uuid",
						VPCSubnetUUID:     "vpc-subnet-uuid",
						SecurityGroupUUID: "security-group-uuid",
						VPCSubnetType:     vpc.SubnetTypeBCC,
						VPCSubnetCIDR:     "vpc-subnet-cidr",
						VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
						AvailableZone:     internalvpc.ZoneA,
					}, nil),
					instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.VPCConfig = ccetypes.VPCConfig{
							VPCID:             "vpc-id",
							VPCSubnetID:       "vpc-subnet-id",
							SecurityGroupID:   "security-group-id",
							VPCUUID:           "vpc-uuid",
							VPCSubnetUUID:     "vpc-subnet-uuid",
							SecurityGroupUUID: "security-group-uuid",
							VPCSubnetType:     vpc.SubnetTypeBCC,
							VPCSubnetCIDR:     "vpc-subnet-cidr",
							VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
							AvailableZone:     internalvpc.ZoneA,
						}
						return instance.DeepCopy()
					}()).Return(ccetypes.InstanceResource{
						CPU:           4,
						MEM:           8,
						RootDiskType:  bcc.StorageTypeHP1,
						RootDiskSize:  100,
						LocalDiskSize: 10000,
						CDSList: []ccetypes.CDSConfig{
							{
								Path:        "/home/<USER>",
								StorageType: bcc.StorageTypeHP1,
							},
						},
						GPUType:  bcc.GPUTypeP40,
						GPUCount: 8,
					}, nil),
					instancefiller.EXPECT().Image(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.InstanceResource = ccetypes.InstanceResource{
							CPU:           4,
							MEM:           8,
							RootDiskType:  bcc.StorageTypeHP1,
							RootDiskSize:  100,
							LocalDiskSize: 10000,
							CDSList: []ccetypes.CDSConfig{
								{
									Path:        "/home/<USER>",
									StorageType: bcc.StorageTypeHP1,
								},
							},
							GPUType:  bcc.GPUTypeP40,
							GPUCount: 8,
						}
						return instance.DeepCopy()
					}()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
						ImageType: bccimage.ImageTypeSystem,
						ImageName: "image-name",
						OSType:    bccimage.OSTypeLinux,
						OSName:    bccimage.OSNameCentOS,
						OSVersion: "os-version",
						OSArch:    "",
						OSBuild:   "os-build",
					}, nil),
					instancefiller.EXPECT().EIP(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.ImageID = "image-id"
						instance.ImageUUID = "image-uuid"
						instance.InstanceOS = ccetypes.InstanceOS{
							ImageType: bccimage.ImageTypeSystem,
							ImageName: "image-name",
							OSType:    bccimage.OSTypeLinux,
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "os-version",
							OSArch:    "",
							OSBuild:   "os-build",
						}
						return instance.DeepCopy()
					}()).Return(true, ccetypes.EIPOption{
						EIPName:         "eip-name",
						EIPChargingType: eip.BillingMethodByBandwidth,
						EIPBandwidth:    10,
					}, nil),
					instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.NeedEIP = true
						instance.EIPOption = ccetypes.EIPOption{
							EIPName:         "eip-name",
							EIPChargingType: eip.BillingMethodByBandwidth,
							EIPBandwidth:    10,
						}
						return instance.DeepCopy()
					}()).Return("admin-password", "ssh-key-id", nil),
					instancefiller.EXPECT().ChargingType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.AdminPassword = "admin-password"
						instance.SSHKeyID = "ssh-key-id"
						return instance.DeepCopy()
					}()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil),
					instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.InstanceChargingType = bcc.PaymentTimingPostpaid
						instance.InstancePreChargingOption = ccetypes.InstancePreChargingOption{}
						return instance.DeepCopy()
					}()).Return(ccetypes.DeleteOption{
						MoveOut:           true,
						DeleteResource:    true,
						DeleteCDSSnapshot: false,
					}, nil),
					instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.DeleteOption = &ccetypes.DeleteOption{
							MoveOut:           true,
							DeleteResource:    true,
							DeleteCDSSnapshot: false,
						}
						return instance.DeepCopy()
					}()).Return(ccetypes.DeployCustomConfig{}, nil),
					instancefiller.EXPECT().Tags(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.DeleteOption = &ccetypes.DeleteOption{
							MoveOut:           true,
							DeleteResource:    true,
							DeleteCDSSnapshot: false,
						}
						return instance.DeepCopy()
					}()).Return([]ccetypes.Tag{
						{
							TagKey:   "key",
							TagValue: "value",
						},
					}, nil),
					instancefiller.EXPECT().Labels(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
						instance.Tags = ccetypes.TagList{
							{
								TagKey:   "key",
								TagValue: "value",
							},
						}
						return instance.DeepCopy()
					}()).Return(ccetypes.InstanceLabels{}, nil),
				)

				k8sClient := mock.NewMockInterface(ctrl)

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					filler:      baseFiller,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					clients:     clients,
				}
			}(),

			ig: &ccetypes.InstanceGroupSpec{
				ClusterRole: ccetypes.ClusterRoleNode,
				Replicas:    0,
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType:  "BCC",
						InstanceType: "N3",
						InstanceName: "",
						// InstanceOS: ccetypes.InstanceOS{
						// 	OSArch: "os-arch",
						// },
					},
				},
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:  "BCC",
							InstanceType: "N3",
							InstanceName: "",
							ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
							InstanceOS: ccetypes.InstanceOS{
								ImageType: "System",
							},
							InstanceResource: ccetypes.InstanceResource{
								CPU:          2,
								MEM:          8,
								RootDiskType: "enhanced_ssd_pl1",
								RootDiskSize: 100,
								GPUType:      "",
								GPUCount:     1,
								SpecID:       "g3",
								MachineSpec:  "bcc.g3.c2m8",
							},
							VPCConfig: ccetypes.VPCConfig{

								VPCSubnetID:       "sbn-uhj41zevmcty",
								SecurityGroupType: "normal",
								SecurityGroup:     ccetypes.SecurityGroup{},
							},
						},
					},
					{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:  "BCC",
							InstanceType: "N5",
							InstanceName: "",
							ImageID:      "50ebea36-bf40-453b-8a61-829a8a2c0bbd",
							InstanceOS: ccetypes.InstanceOS{
								ImageType: "System",
							},
							InstanceResource: ccetypes.InstanceResource{
								CPU:          2,
								MEM:          8,
								RootDiskType: "enhanced_ssd_pl1",
								RootDiskSize: 100,
								GPUType:      "",
								GPUCount:     1,
								SpecID:       "g4",
								MachineSpec:  "bcc.g4.c2m8",
							},
							VPCConfig: ccetypes.VPCConfig{

								VPCSubnetID:       "sbn-jsva4jyn8287",
								SecurityGroupType: "normal",
								SecurityGroup:     ccetypes.SecurityGroup{},
							},
						},
					},
				},
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: false,
				},
			},

			expectedErr: true,
		},
		{
			name: "create normal",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				ctx := context.TODO()

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec:      clusterSpec,
					Status:    nil,
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"aaa"}, nil)

				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				quotaClient.EXPECT().WithCache(gomock.Any()).Return(quotaClient).AnyTimes()
				quotaClient.EXPECT().GetNodeQuotaWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().CreateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				return &mocks{
					ctrl:        ctrl,
					model:       model,
					filler:      baseFiller,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					clients:     clients,
				}
			}(),

			ig: &ccetypes.InstanceGroupSpec{
				InstanceGroupName: "test",
				AccountID:         "ffff",
				UserID:            "ffff",
				ClusterRole:       ccetypes.ClusterRoleNode,
				Replicas:          0,
				InstanceTemplate:  ccetypes.InstanceTemplate{},
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{

						InstanceSpec: *instanceA,
					},
					//{
					//
					//	InstanceSpec: *instanceA,
					//},
				},
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: false,
				},
			},

			expectedErr: false,
		},

		{
			name: "create normal 2",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				ctx := context.TODO()

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec:      clusterSpec,
					Status:    nil,
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"aaa"}, nil)

				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				quotaClient.EXPECT().WithCache(gomock.Any()).Return(quotaClient).AnyTimes()
				quotaClient.EXPECT().GetNodeQuotaWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().CreateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				return &mocks{
					ctrl:        ctrl,
					model:       model,
					filler:      baseFiller,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					clients:     clients,
				}
			}(),

			ig: &ccetypes.InstanceGroupSpec{
				InstanceGroupName: "test",
				AccountID:         "ffff",
				UserID:            "ffff",
				ClusterRole:       ccetypes.ClusterRoleNode,
				Replicas:          0,
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: *instanceA,
				},
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: false,
				},
			},

			expectedErr: false,
		},
		{
			name: "invalid annotation",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				ctx := context.TODO()

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec:      clusterSpec,
					Status:    nil,
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"aaa"}, nil).AnyTimes()

				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				quotaClient.EXPECT().WithCache(gomock.Any()).Return(quotaClient).AnyTimes()
				quotaClient.EXPECT().GetNodeQuotaWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, ccetypes.ExistedOption{}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceType(gomock.Any(), gomock.Any(), gomock.Any()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil).AnyTimes()
				instancefiller.EXPECT().VPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().CreateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				return &mocks{
					ctrl:        ctrl,
					model:       model,
					filler:      baseFiller,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					clients:     clients,
				}
			}(),

			ig: &ccetypes.InstanceGroupSpec{
				InstanceGroupName: "test",
				AccountID:         "ffff",
				UserID:            "ffff",
				ClusterRole:       ccetypes.ClusterRoleNode,
				Replicas:          0,
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "xxxx",
						CCEInstanceID:      "iiii",
						Annotations: map[string]string{
							"cluster-autoscaler.kubernetes.io/scale-down-disabled": "false",
						},
						AdminPassword:     "pppp",
						ClusterID:         "cluster-id",
						ClusterRole:       "node",
						AccountID:         "ffff",
						UserID:            "ffff",
						RuntimeType:       ccetypes.RuntimeTypeDocker,
						RuntimeVersion:    "1.18.0",
						ScaleDownDisabled: true,
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: false,
				},
			},

			expectedErr: true,
		},

		{
			name: "invalid annotation 2",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				ctx := context.TODO()

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec:      clusterSpec,
					Status:    nil,
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil)

				model.EXPECT().CreateInstanceGroups(gomock.Any(), gomock.Any()).Return([]string{"aaa"}, nil).AnyTimes()

				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				quotaClient.EXPECT().WithCache(gomock.Any()).Return(quotaClient).AnyTimes()
				quotaClient.EXPECT().GetNodeQuotaWithCache(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(gomock.Any(), gomock.Any(), gomock.Any()).Return(false, ccetypes.ExistedOption{}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceType(gomock.Any(), gomock.Any(), gomock.Any()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil).AnyTimes()
				instancefiller.EXPECT().VPC(gomock.Any(), gomock.Any(), gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().CreateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				return &mocks{
					ctrl:        ctrl,
					model:       model,
					filler:      baseFiller,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					clients:     clients,
				}
			}(),

			ig: &ccetypes.InstanceGroupSpec{
				InstanceGroupName: "test",
				AccountID:         "ffff",
				UserID:            "ffff",
				ClusterRole:       ccetypes.ClusterRoleNode,
				Replicas:          0,
				InstanceTemplate:  ccetypes.InstanceTemplate{},
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							Handler:            "xxxx",
							CCEInstanceID:      "iiii",
							Annotations: map[string]string{
								"cluster-autoscaler.kubernetes.io/scale-down-disabled": "false",
							},
							AdminPassword:     "pppp",
							ClusterID:         "cluster-id",
							ClusterRole:       "node",
							AccountID:         "ffff",
							UserID:            "ffff",
							RuntimeType:       ccetypes.RuntimeTypeDocker,
							RuntimeVersion:    "1.18.0",
							ScaleDownDisabled: true,
							VPCConfig: ccetypes.VPCConfig{
								VPCID: "dddd",
							},
							DeployCustomConfig: ccetypes.DeployCustomConfig{
								KubeletRootDir: "/var/lib/kubelet",
								DockerConfig: ccetypes.DockerConfig{
									DockerDataRoot: "/var/lib/docker",
								},
								PreUserScript:  "cce-us-xxxxxx01",
								PostUserScript: "cce-us-xxxxxx01",
								EnableCordon:   true,
							},
							Tags: ccetypes.TagList{
								{
									TagKey:   "k",
									TagValue: "v",
								},
							},
							Labels: ccetypes.InstanceLabels{
								"lk": "lv",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key:    "tk",
									Value:  "tv",
									Effect: corev1.TaintEffectNoExecute,
								},
							},
						},
					},
				},
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled: false,
				},
			},

			expectedErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			//defer tc.mocks.ctrl.Finish()

			s := &InstanceGroupService{
				log:         logger.WithValues("name", "test"),
				model:       tc.mocks.model,
				K8SClient:   tc.mocks.K8SClient,
				quotaClient: tc.mocks.quotaClient,
				filler:      tc.mocks.filler,
				fillClients: tc.mocks.clients,
			}
			_, err := s.Create(context.TODO(), tc.ig)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

func TestInstanceGroupService_Update(t *testing.T) {
	type mocks struct {
		ctrl        *gomock.Controller
		model       models.Interface
		K8SClient   meta.Interface
		quotaClient quota.Interface
		filler      *fillspec.BaseFiller
		clients     fillclients.Clients
	}

	testCases := []struct {
		name                         string
		mocks                        *mocks
		ig                           *ccetypes.InstanceGroupSpec
		opts                         UpdateOptions
		expectedErr                  bool
		skipUpdateInstanceGroupModel bool
	}{
		{
			name: "empty update field",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				return &mocks{
					ctrl: ctrl,
				}
			}(),
			ig:          &ccetypes.InstanceGroupSpec{},
			opts:        UpdateOptions{},
			expectedErr: true,
		},
		{
			name: "scale replicas succeed, no instance imported",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec:   &ccetypes.InstanceGroupSpec{},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil).AnyTimes()
				model.EXPECT().UpdateInstanceGroupStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)

				quotaClient := quotamock.NewMockInterface(ctrl)
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Replicas:           10,
				CCEInstanceGroupID: "igig",
			},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldReplicas},
			},
			expectedErr: false,
		},
		{
			name: "shrink replicas succeed, no instance imported",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						Replicas: 20,
					},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)
				model.EXPECT().UpdateInstanceGroupStatus(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							Spec: ccetypes.InstanceSpec{
								CCEInstanceID:     "i-1",
								ScaleDownDisabled: true,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						Replicas: 20,
					},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Replicas:           10,
				CCEInstanceGroupID: "igig",
				ClusterID:          "clu-1",
			},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldReplicas},
			},
			expectedErr: false,
		},
		{
			name: "scale replicas, import instance failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
					},
				}, nil)
				quotaClient := quotamock.NewMockInterface(ctrl)
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Replicas:           10,
				CCEInstanceGroupID: "igig",
			},
			opts: UpdateOptions{
				Fields:                 []string{UpdateFieldReplicas},
				ScaleShrinkInstanceIDs: []string{"iiii"},
			},
			expectedErr: true,
		},
		{
			name: "shrink replicas, mark instance failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test")).AnyTimes()

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							Spec: ccetypes.InstanceSpec{
								CCEInstanceID:     "i-1",
								ScaleDownDisabled: true,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						Replicas: 20,
					},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Replicas:           10,
				CCEInstanceGroupID: "igig",
				ClusterID:          "clu-1",
			},
			opts: UpdateOptions{
				Fields:                 []string{UpdateFieldReplicas},
				ScaleShrinkInstanceIDs: []string{"iiii"},
			},
			expectedErr: true,
		},
		{
			name: "update autoscaler spec, validate spec failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						Replicas: 0,
					},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				CCEInstanceGroupID: "igig",
				Replicas:           20,
				ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
					Enabled:              true,
					MinReplicas:          2,
					MaxReplicas:          1,
					ScalingGroupPriority: 1,
				},
			},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldAutoscalerSpec},
			},
			expectedErr: true,
		},
		{
			name: "get instanceGroup from db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							Spec: ccetypes.InstanceSpec{
								CCEInstanceID: "i-1",
								//ScaleDownDisabled: true,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						Replicas: 20,
					},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldReplicas},
			},
			expectedErr: true,
		},
		{
			name: "update db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						Replicas: 20,
					},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))

				k8sClient := mock.NewMockInterface(ctrl)

				k8sClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							Spec: ccetypes.InstanceSpec{
								CCEInstanceID: "i-1",
							},
						},
					},
				}, nil)

				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						Replicas: 20,
					},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldReplicas},
			},
			expectedErr: true,
		},
		{
			name: "get instanceGroup from meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldReplicas},
			},
			expectedErr: true,
		},
		{
			name: "update instanceGroup to meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldReplicas},
			},
			expectedErr: true,
		},
		{
			name: "update instanceGroup instance template",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Eq(&ccetypes.InstanceGroupSpec{
					CCEInstanceGroupID: "ig-id",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					Replicas:           20,
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							DeployCustomConfig: ccetypes.DeployCustomConfig{
								KubeletRootDir: "/var/lib/kubelet",
								DockerConfig: ccetypes.DockerConfig{
									DockerDataRoot: "/var/lib/docker",
								},
								PreUserScript:  "cce-us-xxxxxx01",
								PostUserScript: "cce-us-xxxxxx01",
								EnableCordon:   true,
							},
							Tags: ccetypes.TagList{
								{
									TagKey:   "k",
									TagValue: "v",
								},
							},
							Labels: ccetypes.InstanceLabels{
								"lk":                             "lv",
								ccetypes.ClusterIDLabelKey:       "cluster-id",
								ccetypes.InstanceGroupIDLabelKey: "ig-id",
								ccetypes.ClusterRoleLabelKey:     "node",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key:    "tk",
									Value:  "tv",
									Effect: corev1.TaintEffectNoExecute,
								},
							},
						},
					},
				})).Return(nil).AnyTimes()

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Eq(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								DeployCustomConfig: ccetypes.DeployCustomConfig{
									KubeletRootDir: "/var/lib/kubelet",
									DockerConfig: ccetypes.DockerConfig{
										DockerDataRoot: "/var/lib/docker",
									},
									PreUserScript:  "cce-us-xxxxxx01",
									PostUserScript: "cce-us-xxxxxx01",
									EnableCordon:   true,
								},
								Tags: ccetypes.TagList{
									{
										TagKey:   "k",
										TagValue: "v",
									},
								},
								Labels: ccetypes.InstanceLabels{
									"lk":                             "lv",
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
								Taints: ccetypes.InstanceTaints{
									{
										Key:    "tk",
										Value:  "tv",
										Effect: corev1.TaintEffectNoExecute,
									},
								},
							},
						},
					},
				})).Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "dddd",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "ccccc",
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "dddd",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldInstanceTemplate},
			},
			expectedErr: false,
		},

		{
			name: "instance group exit conflict workflow",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)

				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
				}, nil)

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil).AnyTimes()
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Eq(&ccetypes.InstanceGroupSpec{
					CCEInstanceGroupID: "ig-id",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					Replicas:           20,
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							DeployCustomConfig: ccetypes.DeployCustomConfig{
								KubeletRootDir: "/var/lib/kubelet",
								DockerConfig: ccetypes.DockerConfig{
									DockerDataRoot: "/var/lib/docker",
								},
								PreUserScript:  "cce-us-xxxxxx01",
								PostUserScript: "cce-us-xxxxxx01",
								EnableCordon:   true,
							},
							Tags: ccetypes.TagList{
								{
									TagKey:   "k",
									TagValue: "v",
								},
							},
							Labels: ccetypes.InstanceLabels{
								"lk":                             "lv",
								ccetypes.ClusterIDLabelKey:       "cluster-id",
								ccetypes.InstanceGroupIDLabelKey: "ig-id",
								ccetypes.ClusterRoleLabelKey:     "node",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key:    "tk",
									Value:  "tv",
									Effect: corev1.TaintEffectNoExecute,
								},
							},
						},
					},
				})).Return(nil).AnyTimes()

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeKubeletConfig,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhasePaused,
							},
						},
					},
				}, nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "dddd",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "ccccc",
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "dddd",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			opts: UpdateOptions{
				Fields: []string{UpdateFieldInstanceTemplate},
			},
			expectedErr: true,
		},

		{
			name: "update instanceGroup instance configure",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:      "cluster-id",
						AccountID:      "ffff",
						UserID:         "ffff",
						RuntimeType:    ccetypes.RuntimeTypeDocker,
						RuntimeVersion: "1.18.0",
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				ctx := context.TODO()
				instanceFiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
					K8SCustomConfig: ccetypes.K8SCustomConfig{
						EnableHostname: true,
					},
				}

				instance := ccetypes.InstanceSpec{
					InstanceTemplateID: "test",
					Handler:            "xxxx",
					CCEInstanceID:      "iiii",
					AdminPassword:      "pppp",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					AccountID:          "ffff",
					UserID:             "ffff",
					RuntimeType:        ccetypes.RuntimeTypeDocker,
					RuntimeVersion:     "1.18.0",

					VPCConfig: ccetypes.VPCConfig{
						VPCID: "dddd",
					},
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeletRootDir: "/var/lib/kubelet",
						DockerConfig: ccetypes.DockerConfig{
							DockerDataRoot: "/var/lib/docker",
						},
						PreUserScript:  "cce-us-xxxxxx01",
						PostUserScript: "cce-us-xxxxxx01",
						EnableCordon:   true,
					},
					Tags: ccetypes.TagList{
						{
							TagKey:   "k",
							TagValue: "v",
						},
					},
					Labels: ccetypes.InstanceLabels{
						"lk": "lv",
					},
					Taints: ccetypes.InstanceTaints{
						{
							Key:    "tk",
							Value:  "tv",
							Effect: corev1.TaintEffectNoExecute,
						},
					},
				}

				//gomock.InOrder(
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil).AnyTimes()
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instanceFiller, nil)
				instanceFiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instanceFiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instanceFiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil)
				instanceFiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instanceFiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instanceFiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instanceFiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instanceFiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instanceFiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)

				instanceFiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)

				instanceFiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instanceFiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)
				//)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)

				k8sClient.EXPECT().UpdateInstanceGroup(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					filler:      baseFiller,
					clients:     clients,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "xxxx",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "cluster-id",
				InstanceGroupName:  "test",
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "xxxx",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			skipUpdateInstanceGroupModel: true,
			opts: UpdateOptions{
				Fields: []string{UpdateFieldConfigure},
			},
			expectedErr: false,
		},
		{
			name: "update instanceGroup instance configure instanceTemplates",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID:      "cluster-id",
						AccountID:      "ffff",
						UserID:         "ffff",
						RuntimeType:    ccetypes.RuntimeTypeDocker,
						RuntimeVersion: "1.18.0",
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: true,
						},
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				ctx := context.TODO()
				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
					K8SCustomConfig: ccetypes.K8SCustomConfig{
						EnableHostname: true,
					},
				}

				instance := ccetypes.InstanceSpec{
					InstanceTemplateID: "test",
					Handler:            "xxxx",
					CCEInstanceID:      "iiii",
					AdminPassword:      "pppp",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					AccountID:          "ffff",
					UserID:             "ffff",
					RuntimeType:        ccetypes.RuntimeTypeDocker,
					RuntimeVersion:     "1.18.0",

					VPCConfig: ccetypes.VPCConfig{
						VPCID: "dddd",
					},
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeletRootDir: "/var/lib/kubelet",
						DockerConfig: ccetypes.DockerConfig{
							DockerDataRoot: "/var/lib/docker",
						},
						PreUserScript:  "cce-us-xxxxxx01",
						PostUserScript: "cce-us-xxxxxx01",
						EnableCordon:   true,
					},
					Tags: ccetypes.TagList{
						{
							TagKey:   "k",
							TagValue: "v",
						},
					},
					Labels: ccetypes.InstanceLabels{
						"lk": "lv",
					},
					Taints: ccetypes.InstanceTaints{
						{
							Key:    "tk",
							Value:  "tv",
							Effect: corev1.TaintEffectNoExecute,
						},
					},
				}

				//gomock.InOrder(
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil).AnyTimes()
				//quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
				//	Quota: 10,
				//	Used:  0,
				//}, nil),
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil)
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)
				//)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)

				k8sClient.EXPECT().UpdateInstanceGroup(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					filler:      baseFiller,
					clients:     clients,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "xxxx",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "cluster-id",
				InstanceGroupName:  "test",
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							Handler:            "xxxx",
							CCEInstanceID:      "iiii",
							AdminPassword:      "pppp",
							VPCConfig: ccetypes.VPCConfig{
								VPCID: "dddd",
							},
							DeployCustomConfig: ccetypes.DeployCustomConfig{
								KubeletRootDir: "/var/lib/kubelet",
								DockerConfig: ccetypes.DockerConfig{
									DockerDataRoot: "/var/lib/docker",
								},
								PreUserScript:  "cce-us-xxxxxx01",
								PostUserScript: "cce-us-xxxxxx01",
								EnableCordon:   true,
							},
							Tags: ccetypes.TagList{
								{
									TagKey:   "k",
									TagValue: "v",
								},
							},
							Labels: ccetypes.InstanceLabels{
								"lk": "lv",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key:    "tk",
									Value:  "tv",
									Effect: corev1.TaintEffectNoExecute,
								},
							},
						},
					},
				},
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "xxxx",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			skipUpdateInstanceGroupModel: true,
			opts: UpdateOptions{
				Fields: []string{UpdateFieldConfigure},
			},
			expectedErr: false,
		},
		{
			name: "update instanceGroup instance configure instanceTemplates machine",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec:   &ccetypes.ClusterSpec{ClusterID: "cluster-id", AccountID: "ffff", UserID: "ffff", RuntimeType: ccetypes.RuntimeTypeDocker, RuntimeVersion: "1.18.0"},
					Status: &ccetypes.ClusterStatus{},
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				ctx := context.TODO()
				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				instance := ccetypes.InstanceSpec{
					InstanceTemplateID: "test",
					Handler:            "xxxx",
					CCEInstanceID:      "iiii",
					AdminPassword:      "pppp",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					AccountID:          "ffff",
					UserID:             "ffff",
					RuntimeType:        ccetypes.RuntimeTypeDocker,
					RuntimeVersion:     "1.18.0",

					VPCConfig: ccetypes.VPCConfig{
						VPCID: "dddd",
					},
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeletRootDir: "/var/lib/kubelet",
						DockerConfig: ccetypes.DockerConfig{
							DockerDataRoot: "/var/lib/docker",
						},
						PreUserScript:  "cce-us-xxxxxx01",
						PostUserScript: "cce-us-xxxxxx01",
						EnableCordon:   true,
					},
					Tags: ccetypes.TagList{
						{
							TagKey:   "k",
							TagValue: "v",
						},
					},
					Labels: ccetypes.InstanceLabels{
						"lk": "lv",
					},
					Taints: ccetypes.InstanceTaints{
						{
							Key:    "tk",
							Value:  "tv",
							Effect: corev1.TaintEffectNoExecute,
						},
					},
				}

				//gomock.InOrder(
				//quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
				//	Quota: 10,
				//	Used:  0,
				//}, nil),
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil)
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)
				//)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
						MachinesToJoin: map[string]ccetypes.ScalingItemSpec{
							"aa": {},
						},
						InstancesToBeRemoved: map[string]ccetypes.ScalingItemSpec{
							"bb": {},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)

				k8sClient.EXPECT().UpdateInstanceGroup(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					filler:      baseFiller,
					clients:     clients,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "xxxx",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "cluster-id",
				InstanceGroupName:  "test",
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							Handler:            "xxxx",
							CCEInstanceID:      "iiii",
							AdminPassword:      "pppp",
							VPCConfig: ccetypes.VPCConfig{
								VPCID: "dddd",
							},
							DeployCustomConfig: ccetypes.DeployCustomConfig{
								KubeletRootDir: "/var/lib/kubelet",
								DockerConfig: ccetypes.DockerConfig{
									DockerDataRoot: "/var/lib/docker",
								},
								PreUserScript:  "cce-us-xxxxxx01",
								PostUserScript: "cce-us-xxxxxx01",
								EnableCordon:   true,
							},
							Tags: ccetypes.TagList{
								{
									TagKey:   "k",
									TagValue: "v",
								},
							},
							Labels: ccetypes.InstanceLabels{
								"lk": "lv",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key:    "tk",
									Value:  "tv",
									Effect: corev1.TaintEffectNoExecute,
								},
							},
						},
					},
				},
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "xxxx",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			skipUpdateInstanceGroupModel: true,
			opts: UpdateOptions{
				Fields: []string{UpdateFieldConfigure},
			},
			expectedErr: false,
		},
		{
			name: "update instanceGroup instance configure instanceTemplates machine",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec:   &ccetypes.ClusterSpec{ClusterID: "cluster-id", AccountID: "ffff", UserID: "ffff", RuntimeType: ccetypes.RuntimeTypeDocker, RuntimeVersion: "1.18.0"},
					Status: &ccetypes.ClusterStatus{},
				}, nil).AnyTimes()

				model.EXPECT().CreateUserScript(gomock.Any(), gomock.Any()).Return("cce-us-xxxxxx01", nil).AnyTimes()

				userScript := userscriptmock.NewMockInterface(ctrl)
				userScript.EXPECT().Create(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return("", nil).AnyTimes()

				ctx := context.TODO()
				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				instance := ccetypes.InstanceSpec{
					InstanceTemplateID: "test",
					Handler:            "xxxx",
					CCEInstanceID:      "iiii",
					AdminPassword:      "pppp",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					AccountID:          "ffff",
					UserID:             "ffff",
					RuntimeType:        ccetypes.RuntimeTypeDocker,
					RuntimeVersion:     "1.18.0",

					VPCConfig: ccetypes.VPCConfig{
						VPCID: "dddd",
					},
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeletRootDir: "/var/lib/kubelet",
						DockerConfig: ccetypes.DockerConfig{
							DockerDataRoot: "/var/lib/docker",
						},
						PreUserScript:  "cce-us-xxxxxx01",
						PostUserScript: "cce-us-xxxxxx01",
						EnableCordon:   true,
					},
					Tags: ccetypes.TagList{
						{
							TagKey:   "k",
							TagValue: "v",
						},
					},
					Labels: ccetypes.InstanceLabels{
						"lk": "lv",
					},
					Taints: ccetypes.InstanceTaints{
						{
							Key:    "tk",
							Value:  "tv",
							Effect: corev1.TaintEffectNoExecute,
						},
					},
				}

				//gomock.InOrder(
				//quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
				//	Quota: 10,
				//	Used:  0,
				//}, nil),
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil)
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil)
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)
				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, nil)
				instancefiller.EXPECT().Tags(ctx, clusterSpec, gomock.Any()).Return([]ccetypes.Tag{
					{
						TagKey:   "key",
						TagValue: "value",
					},
				}, nil)
				instancefiller.EXPECT().Labels(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceLabels{}, nil)
				//)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
						MachinesToJoin: map[string]ccetypes.ScalingItemSpec{
							"aa": {},
						},
						InstancesToBeRemoved: map[string]ccetypes.ScalingItemSpec{
							"bb": {},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)

				k8sClient.EXPECT().UpdateInstanceGroup(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					filler:      baseFiller,
					clients:     clients,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "xxxx",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "cluster-id",
				InstanceGroupName:  "test",
				MachinesToJoin: map[string]ccetypes.ScalingItemSpec{
					"cc": {},
				},
				InstancesToBeRemoved: map[string]ccetypes.ScalingItemSpec{
					"dd": {},
				},
				InstanceTemplates: []ccetypes.InstanceTemplate{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							InstanceTemplateID: "test",
							Handler:            "xxxx",
							CCEInstanceID:      "iiii",
							AdminPassword:      "pppp",
							VPCConfig: ccetypes.VPCConfig{
								VPCID: "dddd",
							},
							DeployCustomConfig: ccetypes.DeployCustomConfig{
								KubeletRootDir: "/var/lib/kubelet",
								DockerConfig: ccetypes.DockerConfig{
									DockerDataRoot: "/var/lib/docker",
								},
								PreUserScript:  "cce-us-xxxxxx01",
								PostUserScript: "cce-us-xxxxxx01",
								EnableCordon:   true,
							},
							Tags: ccetypes.TagList{
								{
									TagKey:   "k",
									TagValue: "v",
								},
							},
							Labels: ccetypes.InstanceLabels{
								"lk": "lv",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key:    "tk",
									Value:  "tv",
									Effect: corev1.TaintEffectNoExecute,
								},
							},
						},
					},
				},
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "xxxx",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			skipUpdateInstanceGroupModel: true,
			opts: UpdateOptions{
				Fields: []string{UpdateFieldConfigure},
			},
			expectedErr: false,
		},
		{
			name: "update instanceGroup instance configure with wrong custom deploy config",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec:   &ccetypes.ClusterSpec{ClusterID: "cluster-id", AccountID: "ffff", UserID: "ffff", RuntimeType: ccetypes.RuntimeTypeDocker, RuntimeVersion: "1.18.0"},
					Status: &ccetypes.ClusterStatus{},
				}, nil).AnyTimes()

				ctx := context.TODO()
				instancefiller := instancefillermock.NewMockInterface(ctrl)
				clients := fillclientsmock.NewMockClients(ctrl)
				quotaClient := quotamock.NewMockInterface(ctrl)

				baseFiller := &fillspec.BaseFiller{
					UserID:    "ffff",
					AccountID: "ffff",
					Config: &configuration.Config{
						Handler: "xxxx",
					},
				}

				clusterSpec := &ccetypes.ClusterSpec{
					ClusterID:      "cluster-id",
					RuntimeType:    ccetypes.RuntimeTypeDocker,
					RuntimeVersion: "1.18.0",
					AccountID:      "ffff",
					UserID:         "ffff",
				}

				instance := ccetypes.InstanceSpec{
					InstanceTemplateID: "test",
					Handler:            "xxxx",
					CCEInstanceID:      "iiii",
					AdminPassword:      "pppp",
					ClusterID:          "cluster-id",
					ClusterRole:        "node",
					AccountID:          "ffff",
					UserID:             "ffff",
					RuntimeType:        ccetypes.RuntimeTypeDocker,
					RuntimeVersion:     "1.18.0",

					VPCConfig: ccetypes.VPCConfig{
						VPCID: "dddd",
					},
					DeployCustomConfig: ccetypes.DeployCustomConfig{
						KubeletRootDir: "/var/lib/kubelet",
						DockerConfig: ccetypes.DockerConfig{
							DockerDataRoot: "/var/lib/docker",
						},
						PreUserScript:  "cce-us-xxxxxx01",
						PostUserScript: "cce-us-xxxxxx01",
						EnableCordon:   true,
					},
					Tags: ccetypes.TagList{
						{
							TagKey:   "k",
							TagValue: "v",
						},
					},
					Labels: ccetypes.InstanceLabels{
						"lk": "lv",
					},
					Taints: ccetypes.InstanceTaints{
						{
							Key:    "tk",
							Value:  "tv",
							Effect: corev1.TaintEffectNoExecute,
						},
					},
				}

				//gomock.InOrder(
				//quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
				//	Quota: 10,
				//	Used:  0,
				//}, nil),
				clients.EXPECT().NewInstanceFiller(gomock.Any(), gomock.Any(), ccetypes.ClusterRoleNode, gomock.Any(), gomock.Any()).Return(instancefiller, nil)
				instancefiller.EXPECT().ExistedOption(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					return instance.DeepCopy()
				}()).Return(false, ccetypes.ExistedOption{}, nil)
				instancefiller.EXPECT().InstanceType(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
					instance.Existed = false
					return instance.DeepCopy()
				}()).Return(ccetypes.MachineTypeBCC, bcc.InstanceTypeN3, ccetypes.BBCOption{}, nil)
				instancefiller.EXPECT().VPC(ctx, clusterSpec, gomock.Any()).Return(ccetypes.VPCConfig{
					VPCID:             "vpc-id",
					VPCSubnetID:       "vpc-subnet-id",
					SecurityGroupID:   "security-group-id",
					VPCUUID:           "vpc-uuid",
					VPCSubnetUUID:     "vpc-subnet-uuid",
					SecurityGroupUUID: "security-group-uuid",
					VPCSubnetType:     vpc.SubnetTypeBCC,
					VPCSubnetCIDR:     "vpc-subnet-cidr",
					VPCSubnetCIDRIPv6: "vpc-subnet-cidr-ipv6",
					AvailableZone:     internalvpc.ZoneA,
				}, nil).AnyTimes()
				instancefiller.EXPECT().InstanceResource(ctx, clusterSpec, gomock.Any()).Return(ccetypes.InstanceResource{
					CPU:           4,
					MEM:           8,
					RootDiskType:  bcc.StorageTypeHP1,
					RootDiskSize:  100,
					LocalDiskSize: 10000,
					CDSList: []ccetypes.CDSConfig{
						{
							Path:        "/home/<USER>",
							StorageType: bcc.StorageTypeHP1,
						},
					},
					GPUType:  bcc.GPUTypeP40,
					GPUCount: 8,
				}, nil)
				instancefiller.EXPECT().Image(ctx, clusterSpec, gomock.Any()).Return("image-id", "image-uuid", ccetypes.InstanceOS{
					ImageType: bccimage.ImageTypeSystem,
					ImageName: "image-name",
					OSType:    bccimage.OSTypeLinux,
					OSName:    bccimage.OSNameCentOS,
					OSVersion: "os-version",
					OSArch:    "",
					OSBuild:   "os-build",
				}, nil).AnyTimes()
				instancefiller.EXPECT().EIP(ctx, clusterSpec, gomock.Any()).Return(true, ccetypes.EIPOption{
					EIPName:         "eip-name",
					EIPChargingType: eip.BillingMethodByBandwidth,
					EIPBandwidth:    10,
				}, nil)
				instancefiller.EXPECT().AdminPasswd(ctx, clusterSpec, gomock.Any()).Return("admin-password", "ssh-key-id", nil)
				instancefiller.EXPECT().ChargingType(ctx, clusterSpec, gomock.Any()).Return(bcc.PaymentTimingPostpaid, ccetypes.InstancePreChargingOption{}, nil)
				instancefiller.EXPECT().DeleteOption(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeleteOption{
					MoveOut:           true,
					DeleteResource:    true,
					DeleteCDSSnapshot: false,
				}, nil)

				instancefiller.EXPECT().DeployCustomConfig(ctx, clusterSpec, gomock.Any()).Return(ccetypes.DeployCustomConfig{}, fmt.Errorf("registryPullQPS must be greater than 0"))

				// instancefiller.EXPECT().Tags(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
				// 	instance.DeleteOption = &ccetypes.DeleteOption{
				// 		MoveOut:           true,
				// 		DeleteResource:    true,
				// 		DeleteCDSSnapshot: false,
				// 	}
				// 	return instance.DeepCopy()
				// }()).Return([]ccetypes.Tag{
				// 	{
				// 		TagKey:   "key",
				// 		TagValue: "value",
				// 	},
				// }, nil),
				// instancefiller.EXPECT().Labels(ctx, clusterSpec, func() *ccetypes.InstanceSpec {
				// 	instance.Tags = ccetypes.TagList{
				// 		{
				// 			TagKey:   "key",
				// 			TagValue: "value",
				// 		},
				// 	}
				// 	return instance.DeepCopy()
				// }()).Return(ccetypes.InstanceLabels{}, nil),
				//)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					ObjectMeta: metav1.ObjectMeta{
						Labels: ccetypes.InstanceLabels{
							ccetypes.ClusterIDLabelKey:       "cluster-id",
							ccetypes.InstanceGroupIDLabelKey: "ig-id",
							ccetypes.ClusterRoleLabelKey:     "node",
						},
					},
					Spec: ccetypes.InstanceGroupSpec{
						CCEInstanceGroupID: "ig-id",
						ClusterID:          "cluster-id",
						ClusterRole:        "node",
						Replicas:           20,
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								Labels: ccetypes.InstanceLabels{
									ccetypes.ClusterIDLabelKey:       "cluster-id",
									ccetypes.InstanceGroupIDLabelKey: "ig-id",
									ccetypes.ClusterRoleLabelKey:     "node",
								},
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				}, nil)

				// k8sClient.EXPECT().UpdateInstanceGroup(ctx, gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:        ctrl,
					model:       model,
					K8SClient:   k8sClient,
					quotaClient: quotaClient,
					filler:      baseFiller,
					clients:     clients,
				}
			}(),
			ig: &ccetypes.InstanceGroupSpec{
				Handler:            "xxxx",
				CCEInstanceGroupID: "xxxx",
				AccountID:          "ffff",
				UserID:             "ffff",
				ClusterID:          "cluster-id",
				InstanceGroupName:  "test",
				InstanceTemplate: ccetypes.InstanceTemplate{
					InstanceSpec: ccetypes.InstanceSpec{
						InstanceTemplateID: "test",
						Handler:            "xxxx",
						CCEInstanceID:      "iiii",
						AdminPassword:      "pppp",
						VPCConfig: ccetypes.VPCConfig{
							VPCID: "dddd",
						},
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
							DockerConfig: ccetypes.DockerConfig{
								DockerDataRoot: "/var/lib/docker",
							},
							PreUserScript:  "cce-us-xxxxxx01",
							PostUserScript: "cce-us-xxxxxx01",
							EnableCordon:   true,
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "k",
								TagValue: "v",
							},
						},
						Labels: ccetypes.InstanceLabels{
							"lk": "lv",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key:    "tk",
								Value:  "tv",
								Effect: corev1.TaintEffectNoExecute,
							},
						},
					},
				},
			},
			skipUpdateInstanceGroupModel: true,
			opts: UpdateOptions{
				Fields: []string{UpdateFieldConfigure},
			},
			expectedErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()

			s := &InstanceGroupService{
				log:                          logger.WithValues("name", "test"),
				model:                        tc.mocks.model,
				K8SClient:                    tc.mocks.K8SClient,
				quotaClient:                  tc.mocks.quotaClient,
				filler:                       tc.mocks.filler,
				fillClients:                  tc.mocks.clients,
				skipUpdateInstanceGroupModel: tc.skipUpdateInstanceGroupModel,
			}
			err := s.Update(context.TODO(), tc.ig, tc.opts)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

// TestInstanceGroupService_Delete 测试实例组服务的删除方法
func TestInstanceGroupService_Delete(t *testing.T) {
	type mocks struct {
		ctrl      *gomock.Controller
		model     models.Interface
		K8SClient meta.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceGroupID string
		opt             DeleteOption
		expectedErr     bool
	}{
		{
			name: "delete succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				// model.EXPECT().DeleteInstanceGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().DeleteInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			expectedErr:     false,
		},
		{
			name: "instance delete option is set, delete succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				// model.EXPECT().DeleteInstanceGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Eq(&metav1.ListOptions{
					LabelSelector: fmt.Sprintf("%s=%s", ccetypes.InstanceGroupIDLabelKey, "dddd"),
				})).Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							ObjectMeta: metav1.ObjectMeta{
								Name:      "iiii",
								Namespace: "default",
							},
							Spec: ccetypes.InstanceSpec{
								CCEInstanceID: "iiii",
							},
						},
					},
				}, nil)
				k8sClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Eq(consts.MetaClusterDefaultNamespace), "iiii", gomock.Eq(&ccev1.Instance{
					TypeMeta: metav1.TypeMeta{},
					ObjectMeta: metav1.ObjectMeta{
						Name:      "iiii",
						Namespace: "default",
						Annotations: map[string]string{
							ccetypes.InstanceGroupSetInstanceDeleteOptionAnnotationKey: `{"deleteResource":true,"deleteCDSSnapshot":true}`,
						},
					},
					Spec: ccetypes.InstanceSpec{
						CCEInstanceID: "iiii",
					},
					Status: ccetypes.InstanceStatus{},
				})).Return(nil)
				k8sClient.EXPECT().DeleteInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			opt: DeleteOption{
				CleanPolicy: "",
				InstanceDeleteOption: &ccetypes.DeleteOption{
					MoveOut:           false,
					DeleteResource:    true,
					DeleteCDSSnapshot: true,
				},
			},
			expectedErr: false,
		},
		/*{
			 name: "delete from db failed",
			 mocks: func() *mocks {
				 ctrl := gomock.NewController(t)
				 model := models.NewMockInterface(ctrl)
				 model.EXPECT().DeleteInstanceGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))

				 return &mocks{
					 ctrl:      ctrl,
					 model:     model,
					 K8SClient: nil,
				 }
			 }(),
			 accountID:       "iiii",
			 instanceGroupID: "dddd",
			 expectedErr:     true,
		 },

		*/
		{
			name: " failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				// model.EXPECT().DeleteInstanceGroups(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)

				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().DeleteInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			expectedErr:     true,
		},
		{
			name: "clean policy set, but get instanceGroup from db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			opt:             DeleteOption{CleanPolicy: ccetypes.DeleteCleanPolicy},
			expectedErr:     true,
		},
		{
			name: "clean policy set, but update instanceGroup to db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						CleanPolicy: ccetypes.RemainCleanPolicy,
					},
					Status:  nil,
					Deleted: false,
				}, nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))
				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			opt:             DeleteOption{CleanPolicy: ccetypes.DeleteCleanPolicy},
			expectedErr:     true,
		},
		{
			name: "clean policy set, but get instanceGroup from meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						CleanPolicy: ccetypes.RemainCleanPolicy,
					},
					Status:  nil,
					Deleted: false,
				}, nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			opt:             DeleteOption{CleanPolicy: ccetypes.DeleteCleanPolicy},
			expectedErr:     true,
		},
		{
			name: "clean policy set, but update instanceGroup to meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec: &ccetypes.InstanceGroupSpec{
						CleanPolicy: ccetypes.RemainCleanPolicy,
					},
					Status:  nil,
					Deleted: false,
				}, nil)
				model.EXPECT().UpdateInstanceGroupSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				k8sClient.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "iiii",
			instanceGroupID: "dddd",
			opt:             DeleteOption{CleanPolicy: ccetypes.DeleteCleanPolicy},
			expectedErr:     true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()

			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				model:     tc.mocks.model,
				K8SClient: tc.mocks.K8SClient,
			}
			err := s.Delete(context.TODO(), tc.accountID, tc.instanceGroupID, tc.opt)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

// TestInstanceGroupService_Get tests the Get function of InstanceGroupService
func TestInstanceGroupService_Get(t *testing.T) {
	type mocks struct {
		ctrl       *gomock.Controller
		model      models.Interface
		metaClient meta.Interface
		K8SClient  meta.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceGroupID string
		expectedErr     bool
	}{
		{
			name: "get succeed, deleted",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec:   &ccetypes.InstanceGroupSpec{},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, k8serr.NewNotFound(schema.GroupResource{}, ""))

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
		},
		{
			name: "get from db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     true,
		},
		{
			name: "get from meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec:   &ccetypes.InstanceGroupSpec{},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()

			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				model:     tc.mocks.model,
				K8SClient: tc.mocks.metaClient,
			}
			_, err := s.Get(context.TODO(), tc.accountID, tc.instanceGroupID)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

func TestGetUpgradeComponentVersions(t *testing.T) {
	type mocks struct {
		ctrl       *gomock.Controller
		model      models.Interface
		metaClient meta.Interface
		K8SClient  meta.Interface
		clientSet  *clientset.ClientSet
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		clusterID       string
		instanceGroupID string
		expectedErr     bool
		want            *UpgradeComponents
	}{
		{
			name: "get succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									ccetypes.ClusterIDLabelKey:       "xxx",
									ccetypes.InstanceGroupIDLabelKey: "xxx",
								},
								Name: "node1",
							},
							Spec: ccetypes.InstanceSpec{
								InstanceType: bcc.InstanceTypeG1,
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
								RuntimeType:    ccetypes.RuntimeTypeDocker,
								RuntimeVersion: "20.10.24",
							},
							Status: ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP: "*******",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				_, _ = fakeK8SClient.CoreV1().Nodes().Create(context.Background(), &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							ccetypes.ClusterIDLabelKey:       "xxx",
							ccetypes.InstanceGroupIDLabelKey: "xxx",
						},
						Name: "node1",
					},
					Status: corev1.NodeStatus{
						Addresses: []corev1.NodeAddress{
							{
								Type:    corev1.NodeInternalIP,
								Address: "*******",
							},
						},
						NodeInfo: corev1.NodeSystemInfo{
							KubeletVersion:          "v1.20.8",
							ContainerRuntimeVersion: "docker://20.10.24",
						},
					},
				}, metav1.CreateOptions{})
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.20.8",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: true,
						},
						{
							TargetVersion: "containerd://1.6.36",
							NeedDrainNode: true,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.3.0",
					ComponentVersions: []ComponentVersion{{
						TargetVersion: "1.14.3",
						NeedDrainNode: true,
					}},
				},
			},
		},
		{
			name: "get succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_18_9,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									ccetypes.ClusterIDLabelKey:       "xxx",
									ccetypes.InstanceGroupIDLabelKey: "xxx",
								},
								Name: "node1",
							},
							Spec: ccetypes.InstanceSpec{
								InstanceType: bcc.InstanceTypeG1,
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
								RuntimeType:    ccetypes.RuntimeTypeDocker,
								RuntimeVersion: "20.10.24",
							},
							Status: ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP: "*******",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				_, _ = fakeK8SClient.CoreV1().Nodes().Create(context.Background(), &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							ccetypes.ClusterIDLabelKey:       "xxx",
							ccetypes.InstanceGroupIDLabelKey: "xxx",
						},
						Name: "node1",
					},
					Status: corev1.NodeStatus{
						Addresses: []corev1.NodeAddress{
							{
								Type:    corev1.NodeInternalIP,
								Address: "*******",
							},
						},
						NodeInfo: corev1.NodeSystemInfo{
							KubeletVersion:          "v1.18.9",
							ContainerRuntimeVersion: "docker://20.10.24",
						},
					},
				}, metav1.CreateOptions{})
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.18.9",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.18.9",
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.5.4",
							NeedDrainNode: true,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.3.0",
					ComponentVersions: []ComponentVersion{{
						TargetVersion: "1.14.3",
						NeedDrainNode: true,
					}},
				},
			},
		},
		{
			name: "get succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_18_9,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{
						{
							ObjectMeta: metav1.ObjectMeta{
								Labels: map[string]string{
									ccetypes.ClusterIDLabelKey:       "xxx",
									ccetypes.InstanceGroupIDLabelKey: "xxx",
								},
								Name: "node1",
							},
							Spec: ccetypes.InstanceSpec{
								InstanceType: bcc.InstanceTypeG1,
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
								RuntimeType:    ccetypes.RuntimeTypeContainerd,
								RuntimeVersion: "1.5.4",
							},
							Status: ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									VPCIP: "*******",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				_, _ = fakeK8SClient.CoreV1().Nodes().Create(context.Background(), &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{
							ccetypes.ClusterIDLabelKey:       "xxx",
							ccetypes.InstanceGroupIDLabelKey: "xxx",
						},
						Name: "node1",
					},
					Status: corev1.NodeStatus{
						Addresses: []corev1.NodeAddress{
							{
								Type:    corev1.NodeInternalIP,
								Address: "*******",
							},
						},
						NodeInfo: corev1.NodeSystemInfo{
							KubeletVersion:          "v1.18.9",
							ContainerRuntimeVersion: "containerd://1.5.4",
						},
					},
				}, metav1.CreateOptions{})
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.18.9",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.18.9",
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "containerd://1.5.4",
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.3.0",
					ComponentVersions: []ComponentVersion{{
						TargetVersion: "1.14.3",
						NeedDrainNode: true,
					}},
				},
			},
		},
		{
			name: "no node - basic case",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return a basic instance group
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:   bcc.InstanceTypeN3,
								RuntimeType:    ccetypes.RuntimeTypeDocker,
								RuntimeVersion: "20.10.24",
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: true,
						},
						{
							TargetVersion: "containerd://1.6.36",
							NeedDrainNode: true,
						},
					},
				},
			},
		},
		{
			name: "no node - GPU G1 type with nvidia toolkit",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return a GPU G1 instance group
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:                  bcc.InstanceTypeG1,
								RuntimeType:                   ccetypes.RuntimeTypeDocker,
								RuntimeVersion:                "20.10.24",
								NvidiaContainerToolkitVersion: "1.3.0",
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: true,
						},
						{
							TargetVersion: "containerd://1.6.36",
							NeedDrainNode: true,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.3.0",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.14.3",
							NeedDrainNode: true,
						},
					},
				},
			},
		},
		{
			name: "no node - GPU BBC_GPU type with nvidia toolkit",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return a GPU BBC_GPU instance group
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:                  bcc.InstanceTypeBBCGPU,
								RuntimeType:                   ccetypes.RuntimeTypeContainerd,
								RuntimeVersion:                "1.6.36",
								NvidiaContainerToolkitVersion: "1.9.0",
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameUbuntu,
									OSVersion: "18.04",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "containerd://1.6.36",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: false,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.9.0",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.14.3",
							NeedDrainNode: true,
						},
					},
				},
			},
		},
		{
			name: "no node - with InstanceTemplates (uses first template)",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return instance group with InstanceTemplates
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:   bcc.InstanceTypeN3,
								RuntimeType:    ccetypes.RuntimeTypeDocker,
								RuntimeVersion: "20.10.24",
							},
						},
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									InstanceType:                  bcc.InstanceTypeHPAS,
									RuntimeType:                   ccetypes.RuntimeTypeContainerd,
									RuntimeVersion:                "1.7.25",
									NvidiaContainerToolkitVersion: "1.14.3",
									InstanceOS: ccetypes.InstanceOS{
										OSName:    bccimage.OSNameCentOS,
										OSVersion: "7.9",
									},
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "containerd://1.7.25",
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.14.3",
				},
			},
		},
		{
			name: "no node - GetInstanceGroup error",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return error
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(nil, fmt.Errorf("instance group not found"))

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     true,
			want:            nil,
		},
		{
			name: "no node - NeedGPU flag set",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return instance group with NeedGPU flag
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:                  bcc.InstanceTypeN3,
								RuntimeType:                   ccetypes.RuntimeTypeDocker,
								RuntimeVersion:                "20.10.24",
								NeedGPU:                       true,
								NvidiaContainerToolkitVersion: "1.3.0",
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: true,
						},
						{
							TargetVersion: "containerd://1.6.36",
							NeedDrainNode: true,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.3.0",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.14.3",
							NeedDrainNode: true,
						},
					},
				},
			},
		},
		{
			name: "no node - NeedGPU flag set, no nvidia-toolkit组件",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return instance group with NeedGPU flag
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:   bcc.InstanceTypeN3,
								RuntimeType:    ccetypes.RuntimeTypeDocker,
								RuntimeVersion: "20.10.24",
								NeedGPU:        true,
								//NvidiaContainerToolkitVersion: "1.3.0",
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: true,
						},
						{
							TargetVersion: "containerd://1.6.36",
							NeedDrainNode: true,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion: "1.3.0",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.14.3",
							NeedDrainNode: true,
						},
					},
				},
			},
		},

		{
			name: "no node - GPU with higher current toolkit version (no upgrades available)",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return GPU instance group with higher toolkit version
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:                  bcc.InstanceTypeG1,
								RuntimeType:                   ccetypes.RuntimeTypeDocker,
								RuntimeVersion:                "20.10.24",
								NvidiaContainerToolkitVersion: "2.0.0", // Higher than available upgrades
								InstanceOS: ccetypes.InstanceOS{
									OSName:    bccimage.OSNameCentOS,
									OSVersion: "7.5",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     false,
			want: &UpgradeComponents{
				Kubelet: UpgradeVersionList{
					CurrentVersion: "1.21.14",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "1.21.14",
							NeedDrainNode: false,
						},
					},
				},
				ContainerRuntime: UpgradeVersionList{
					CurrentVersion: "docker://20.10.24",
					ComponentVersions: []ComponentVersion{
						{
							TargetVersion: "containerd://1.7.25",
							NeedDrainNode: true,
						},
						{
							TargetVersion: "containerd://1.6.36",
							NeedDrainNode: true,
						},
					},
				},
				NvidiaContainerToolkit: UpgradeVersionList{
					CurrentVersion:    "2.0.0",
					ComponentVersions: nil, // No upgrades available
				},
			},
		},
		{
			name: "no node - GPU with unsupported OS causing GetSupportUpgradeToolkitVersion error",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetClusterByClusterID(gomock.Any(), gomock.Any()).Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						K8SVersion: ccetypes.K8S_1_21_14,
					},
					Status: &ccetypes.ClusterStatus{},
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.InstanceList{
					Items: []ccev1.Instance{},
				}, nil)

				// Mock GetInstanceGroup to return GPU instance group with unsupported OS
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), "default", "xxx", gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								InstanceType:                  bcc.InstanceTypeG1,
								RuntimeType:                   ccetypes.RuntimeTypeDocker,
								RuntimeVersion:                "20.10.24",
								NvidiaContainerToolkitVersion: "1.3.0",
								InstanceOS: ccetypes.InstanceOS{
									OSName:    "unsupported-os", // This will cause GetSupportUpgradeToolkitVersion to fail
									OSVersion: "1.0",
								},
							},
						},
					},
				}, nil)

				fakeK8SClient := fake.NewSimpleClientset()
				mockClientSetFactory := mockClientset.NewMockFactory(ctrl)
				mockClientSetFactory.EXPECT().NewK8SClientWithClusterID(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(fakeK8SClient, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
					clientSet: &clientset.ClientSet{
						Factory: mockClientSetFactory,
						Clients: &clientset.Clients{
							MetaClient: metaClient,
						},
					},
				}
			}(),
			accountID:       "xxx",
			clusterID:       "xxx",
			instanceGroupID: "xxx",
			expectedErr:     true, // Expect error due to GetSupportUpgradeToolkitVersion failure
			want:            nil,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()

			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				model:     tc.mocks.model,
				K8SClient: tc.mocks.metaClient,
				clientSet: tc.mocks.clientSet,
			}
			res, err := s.GetUpgradeComponentVersions(context.TODO(), tc.accountID, tc.clusterID, tc.instanceGroupID)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
			if !cmp.Equal(res, tc.want) {
				t.Errorf("GetUpgradeComponentVersions() Diff = %v", cmp.Diff(res, tc.want))
			}
		})
	}
}

// TestGetUpgradeComponentVersions is a unit test for the InstanceGroupService's List method

func TestInstanceGroupService_List(t *testing.T) {
	type mocks struct {
		ctrl       *gomock.Controller
		model      models.Interface
		metaClient meta.Interface
	}

	testCases := []struct {
		name        string
		mocks       *mocks
		opts        ListOptions
		expectedErr bool
	}{
		{
			name: "list succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupsEx(gomock.Any(), gomock.Any()).Return(&models.InstanceGroupList{
					Items: []*models.InstanceGroup{
						{
							Spec:   &ccetypes.InstanceGroupSpec{},
							Status: &ccetypes.InstanceGroupStatus{},
						},
					},
					TotalCount: 1,
				}, nil)

				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)

				return &mocks{
					ctrl:       ctrl,
					model:      model,
					metaClient: metaClient,
				}
			}(),
			opts: ListOptions{
				AccountID: "xxx",
				ClusterID: "xxx",
				Role:      "",
				OrderBy:   "",
				Order:     "ASC",
			},
			expectedErr: false,
		},
		{
			name: "list failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupsEx(gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))

				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			opts: ListOptions{
				AccountID: "xxx",
				ClusterID: "xxx",
				Role:      "",
			},
			expectedErr: true,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()

			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				model:     tc.mocks.model,
				K8SClient: tc.mocks.metaClient,
			}
			_, err := s.List(context.TODO(), tc.opts)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

// Test_instanceGroupService_ScaleDown 测试函数
func Test_instanceGroupService_ScaleDown(t *testing.T) {
	type mocks struct {
		ctrl        *gomock.Controller
		metaClient  meta.Interface
		modelClient models.Interface
	}
	testCases := []struct {
		name                 string
		mocks                *mocks
		accountID            string
		clusterID            string
		instanceGroupID      string
		instancesToBeRemoved []string
		k8sNodesToBeRemoved  []string
		opts                 ScaleDownOption
		expectedErr          bool
		expectedName         string
	}{
		{
			name: "scaledown",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						AccountID: "xxx",
					},
				}, nil)
				metaClient.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(&ccev1.Task{
					ObjectMeta: metav1.ObjectMeta{
						Name: "task-mock",
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:       ctrl,
					metaClient: metaClient,
				}
			}(),
			accountID:            "xxx",
			instanceGroupID:      "xxx",
			instancesToBeRemoved: []string{"i1", "i2"},
			expectedErr:          false,
			expectedName:         "task-mock",
		},
		{
			name: "scaledown-wrong-accountid",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						AccountID: "xxx",
					},
				}, nil)
				return &mocks{
					ctrl:       ctrl,
					metaClient: metaClient,
				}
			}(),
			accountID:            "yyy",
			instanceGroupID:      "xxx",
			instancesToBeRemoved: []string{"i1", "i2"},
			expectedErr:          true,
			expectedName:         "task-mock",
		},
		{
			name: "dup-instance-id",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				return &mocks{
					ctrl:       ctrl,
					metaClient: metaClient,
				}
			}(),
			accountID:            "xxx",
			instanceGroupID:      "xxx",
			instancesToBeRemoved: []string{"i1", "i2", "i1"},
			expectedErr:          true,
			expectedName:         "task-mock",
		},
		{
			name: "dup-k8s-nodename",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				return &mocks{
					ctrl:       ctrl,
					metaClient: metaClient,
				}
			}(),
			accountID:           "xxx",
			instanceGroupID:     "xxx",
			k8sNodesToBeRemoved: []string{"i1", "i2", "i1"},
			expectedErr:         true,
			expectedName:        "task-mock",
		},
		{
			name: "normal-k8s-nodename",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				modelClient := modelcmock.NewMockInterface(ctrl)

				// ig, err := s.K8SClient.GetInstanceGroup(ctx, consts.MetaClusterDefaultNamespace, instanceGroupID, &metav1.GetOptions{})
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						ClusterID: "cce-cluster",
						AccountID: "xxx",
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				modelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clu-xxx",
						AccountID: "acc-xxx",
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: false,
						},
					},
					Status: nil,
				}, nil)
				modelClient.EXPECT().GetInstancesByInstanceGroupID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), "", "", "", "", 100000, 1).Return(
					&models.InstanceList{
						Items: []*models.Instance{
							{
								Spec: &ccetypes.InstanceSpec{
									InstanceName: "*********",
								},
								Status: &ccetypes.InstanceStatus{
									Machine: ccetypes.Machine{
										VPCIP:    "*********",
										Hostname: "*********",
									},
								},
							},
							{
								Spec: &ccetypes.InstanceSpec{
									InstanceName: "*********",
								},
								Status: &ccetypes.InstanceStatus{
									Machine: ccetypes.Machine{
										VPCIP:    "*********",
										Hostname: "*********",
									},
								},
							},
						},
						TotalCount: 2,
					}, nil)

				return &mocks{
					ctrl:        ctrl,
					metaClient:  metaClient,
					modelClient: modelClient,
				}
			}(),
			accountID:           "xxx",
			instanceGroupID:     "ig-1",
			k8sNodesToBeRemoved: []string{"*********", "*********", "*********"},
			expectedErr:         true,
			expectedName:        "task-mock",
		},
		{
			name: "normal-k8s-nodename",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				modelClient := modelcmock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						ClusterID: "cce-cluster",
						AccountID: "xxx",
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				modelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Cluster{
					BaseModel: models.BaseModel{},
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "clu-xxx",
						AccountID: "acc-xxx",
						K8SCustomConfig: ccetypes.K8SCustomConfig{
							EnableHostname: false,
						},
					},
					Status: nil,
				}, nil)
				modelClient.EXPECT().GetInstancesByInstanceGroupID(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), "", "", "", "", 100000, 1).Return(
					&models.InstanceList{
						Items: []*models.Instance{
							{
								Spec: &ccetypes.InstanceSpec{
									InstanceName: "*********",
								},
								Status: &ccetypes.InstanceStatus{
									Machine: ccetypes.Machine{
										VPCIP:    "*********",
										Hostname: "*********",
									},
								},
							},
							{
								Spec: &ccetypes.InstanceSpec{
									InstanceName: "*********",
								},
								Status: &ccetypes.InstanceStatus{
									Machine: ccetypes.Machine{
										VPCIP:    "*********",
										Hostname: "*********",
									},
								},
							},
						},
						TotalCount: 2,
					}, nil)
				metaClient.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(&ccev1.Task{
					ObjectMeta: metav1.ObjectMeta{
						Name: "task-mock",
					},
				}, nil)

				return &mocks{
					ctrl:        ctrl,
					metaClient:  metaClient,
					modelClient: modelClient,
				}
			}(),
			accountID:           "xxx",
			instanceGroupID:     "ig-1",
			k8sNodesToBeRemoved: []string{"*********", "*********"},
			expectedErr:         false,
			expectedName:        "task-mock",
		},
		{
			name: "normal-k8s-nodename",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				modelClient := modelcmock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						ClusterID: "cce-cluster",
						AccountID: "xxx",
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				modelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

				return &mocks{
					ctrl:        ctrl,
					metaClient:  metaClient,
					modelClient: modelClient,
				}
			}(),
			accountID:           "xxx",
			instanceGroupID:     "ig-1",
			k8sNodesToBeRemoved: []string{"*********", "*********"},
			expectedErr:         true,
			expectedName:        "task-mock",
		},
		{
			name: "normal-k8s-nodename",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				modelClient := modelcmock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						ClusterID: "cce-cluster",
						AccountID: "xxx",
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				modelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("test"))

				return &mocks{
					ctrl:        ctrl,
					metaClient:  metaClient,
					modelClient: modelClient,
				}
			}(),
			accountID:           "xxx",
			instanceGroupID:     "ig-1",
			k8sNodesToBeRemoved: []string{"*********", "*********"},
			expectedErr:         true,
			expectedName:        "task-mock",
		},
		{
			name: "instance is progress",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)
				modelClient := modelcmock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						ClusterID: "cce-cluster",
						AccountID: "xxx",
						InstancesToBeRemoved: map[string]ccetypes.ScalingItemSpec{
							"aaa": ccetypes.ScalingItemSpec{},
						},
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				//modelClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("test"))

				return &mocks{
					ctrl:        ctrl,
					metaClient:  metaClient,
					modelClient: modelClient,
				}
			}(),
			accountID:            "xxx",
			instanceGroupID:      "ig-1",
			instancesToBeRemoved: []string{"aaa"},
			expectedErr:          true,
			expectedName:         "task-mock",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				K8SClient: tc.mocks.metaClient,
				rand:      func(i int) string { return "mock" },
				model:     tc.mocks.modelClient,
			}
			gotName, err := s.ScaleDown(context.TODO(), tc.accountID, tc.instanceGroupID, tc.instancesToBeRemoved, tc.k8sNodesToBeRemoved, tc.opts)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
			if err == nil && gotName != tc.expectedName {
				t.Errorf("expected name: %s, got: %s", tc.expectedName, gotName)
			}
		})
	}
}

// TestInstanceGroupService_markInstanceAsCandidate
// 测试实例组服务的标记实例为候选者函数
func TestInstanceGroupService_markInstanceAsCandidate(t *testing.T) {
	type mocks struct {
		ctrl      *gomock.Controller
		model     models.Interface
		K8SClient meta.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceID      string
		instanceGroupID string
		expectedErr     bool
	}{
		{
			name: "get instance from db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))
				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "instance belong to other instanceGroup",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						InstanceGroupID: "kkkk",
					},
				}, nil)
				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "instance joining other instanceGroup",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						Labels: map[string]string{
							ccetypes.JoiningInstanceGroupIDLabelKey: "kkkk",
						},
					},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
					},
				}, nil)
				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "instance is not running",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseProvisioned,
					},
				}, nil)

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: nil,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "update instance spec to db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
					},
				}, nil)
				model.EXPECT().UpdatePartInstanceSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))

				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: nil,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "get instance from meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
					},
				}, nil)
				model.EXPECT().UpdatePartInstanceSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "update instance to meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
					},
				}, nil)
				model.EXPECT().UpdatePartInstanceSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Instance{}, nil)
				k8sClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     true,
		},
		{
			name: "succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{},
					Status: &ccetypes.InstanceStatus{
						InstancePhase: ccetypes.InstancePhaseRunning,
					},
				}, nil)
				model.EXPECT().UpdatePartInstanceSpec(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Instance{}, nil)
				k8sClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			expectedErr:     false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				model:     tc.mocks.model,
				K8SClient: tc.mocks.K8SClient,
			}
			err := s.markInstanceAsCandidate(context.TODO(), tc.accountID, tc.instanceID, tc.instanceGroupID)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

// TestInstanceGroupService_markInstanceToBeRemoved 测试实例组服务的标记实例到删除状态的方法
func TestInstanceGroupService_markInstanceToBeRemoved(t *testing.T) {
	type mocks struct {
		ctrl      *gomock.Controller
		model     models.Interface
		K8SClient meta.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceID      string
		instanceGroupID string
		cleanPolicy     ccetypes.CleanPolicy
		deleteOption    *ccetypes.DeleteOption
		expectedErr     bool
	}{
		{
			name: "get instance from db failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))
				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			cleanPolicy:     "",
			expectedErr:     true,
		},
		{
			name: "instance belong to other instanceGroup",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						Labels: map[string]string{
							ccetypes.InstanceGroupIDLabelKey: "dddd",
						},
					},
					Status: nil,
				}, nil)
				return &mocks{
					ctrl:  ctrl,
					model: model,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			cleanPolicy:     "",
			expectedErr:     true,
		},
		{
			name: "get instance from meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						Labels: map[string]string{
							ccetypes.InstanceGroupIDLabelKey: "igig",
						},
						InstanceGroupID: "igig",
					},
					Status: nil,
				}, nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("test"))
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			cleanPolicy:     "",
			expectedErr:     true,
		},
		{
			name: "update instance to meta cluster failed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						Labels: map[string]string{
							ccetypes.InstanceGroupIDLabelKey: "igig",
						},
						InstanceGroupID: "igig",
					},
					Status: nil,
				}, nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Instance{}, nil)
				k8sClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(fmt.Errorf("test"))
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			cleanPolicy:     ccetypes.RemainCleanPolicy,
			expectedErr:     true,
		},
		{
			name: "succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.Instance{
					Spec: &ccetypes.InstanceSpec{
						Labels: map[string]string{
							ccetypes.InstanceGroupIDLabelKey: "igig",
						},
						InstanceGroupID: "igig",
					},
					Status: nil,
				}, nil)
				k8sClient := mock.NewMockInterface(ctrl)
				k8sClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Instance{}, nil)
				k8sClient.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil)
				return &mocks{
					ctrl:      ctrl,
					model:     model,
					K8SClient: k8sClient,
				}
			}(),
			accountID:       "aaaa",
			instanceID:      "iiii",
			instanceGroupID: "igig",
			cleanPolicy:     ccetypes.RemainCleanPolicy,
			deleteOption: &ccetypes.DeleteOption{
				MoveOut:           false,
				DeleteResource:    true,
				DeleteCDSSnapshot: true,
			},
			expectedErr: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			s := &InstanceGroupService{
				log:       logger.WithValues("name", "test"),
				model:     tc.mocks.model,
				K8SClient: tc.mocks.K8SClient,
			}
			err := s.markInstanceToBeRemoved(context.TODO(), tc.accountID, tc.instanceID, tc.instanceGroupID, tc.cleanPolicy, tc.deleteOption)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

func Test_isReplicaUpN(t *testing.T) {
	type args struct {
		UpToReplicas int
		UpReplicas   int
	}
	tests := []struct {
		name       string
		args       args
		wantResult bool
		wantErr    bool
	}{
		{
			name: "all zero",
			args: args{
				UpToReplicas: 0,
				UpReplicas:   0,
			},
			wantResult: false,
			wantErr:    true,
		},
		{
			name: "all negative",
			args: args{
				UpToReplicas: -1,
				UpReplicas:   -1,
			},
			wantResult: false,
			wantErr:    true,
		},
		{
			name: "one negative",
			args: args{
				UpToReplicas: -1,
				UpReplicas:   0,
			},
			wantResult: false,
			wantErr:    true,
		},
		{
			name: "all bigger than 0",
			args: args{
				UpToReplicas: 3,
				UpReplicas:   3,
			},
			wantResult: false,
			wantErr:    true,
		},
		{
			name: "normal up to N",
			args: args{
				UpToReplicas: 3,
				UpReplicas:   0,
			},
			wantResult: false,
			wantErr:    false,
		},
		{
			name: "normal up N",
			args: args{
				UpToReplicas: 0,
				UpReplicas:   3,
			},
			wantResult: true,
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if result, err := isReplicaUpN(tt.args.UpToReplicas, tt.args.UpReplicas); result != tt.wantResult || (err != nil) != tt.wantErr {
				t.Errorf("isReplicaUpN() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_buildCleanPolicyAndDeleteOption(t *testing.T) {
	type args struct {
		ig   *ccev1.InstanceGroup
		opts ScaleDownOption
	}
	tests := []struct {
		name             string
		args             args
		wantCleanPolicy  ccetypes.CleanPolicy
		wantDeleteOption *ccetypes.DeleteOption
	}{
		{
			name: "remain-with-nil-delete-option",
			args: args{
				ig: &ccev1.InstanceGroup{},
				opts: ScaleDownOption{
					CleanPolicy:          ccetypes.RemainCleanPolicy,
					InstanceDeleteOption: nil,
				},
			},
			wantCleanPolicy:  ccetypes.RemainCleanPolicy,
			wantDeleteOption: nil,
		},
		{
			name: "remain-with-delete-option",
			args: args{
				ig: &ccev1.InstanceGroup{},
				opts: ScaleDownOption{
					CleanPolicy: ccetypes.RemainCleanPolicy,
					InstanceDeleteOption: &ccetypes.DeleteOption{
						MoveOut:           true,
						DeleteResource:    false,
						DeleteCDSSnapshot: false,
					},
				},
			},
			wantCleanPolicy:  ccetypes.RemainCleanPolicy,
			wantDeleteOption: nil,
		},
		{
			name: "delete-with-nil-delete-option",
			args: args{
				ig: &ccev1.InstanceGroup{},
				opts: ScaleDownOption{
					CleanPolicy:          ccetypes.DeleteCleanPolicy,
					InstanceDeleteOption: nil,
				},
			},
			wantCleanPolicy:  ccetypes.DeleteCleanPolicy,
			wantDeleteOption: nil,
		},
		{
			name: "delete-with-delete-option",
			args: args{
				ig: &ccev1.InstanceGroup{},
				opts: ScaleDownOption{
					CleanPolicy: ccetypes.DeleteCleanPolicy,
					InstanceDeleteOption: &ccetypes.DeleteOption{
						MoveOut:           false,
						DeleteResource:    true,
						DeleteCDSSnapshot: true,
					},
				},
			},
			wantCleanPolicy: ccetypes.DeleteCleanPolicy,
			wantDeleteOption: &ccetypes.DeleteOption{
				MoveOut:           false,
				DeleteResource:    true,
				DeleteCDSSnapshot: true,
			},
		},
		{
			name: "no-opts-use-ig-default-remain",
			args: args{
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						CleanPolicy: ccetypes.RemainCleanPolicy,
					},
				},
				opts: ScaleDownOption{
					CleanPolicy:          "",
					InstanceDeleteOption: nil,
				},
			},
			wantCleanPolicy:  ccetypes.RemainCleanPolicy,
			wantDeleteOption: nil,
		},
		{
			name: "no-opts-use-ig-default-delete",
			args: args{
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						CleanPolicy: ccetypes.DeleteCleanPolicy,
					},
				},
				opts: ScaleDownOption{
					CleanPolicy:          "",
					InstanceDeleteOption: nil,
				},
			},
			wantCleanPolicy:  ccetypes.DeleteCleanPolicy,
			wantDeleteOption: nil,
		},
		{
			name: "invalid-opts-use-ig-default-delete",
			args: args{
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						CleanPolicy: ccetypes.DeleteCleanPolicy,
					},
				},
				opts: ScaleDownOption{
					CleanPolicy:          "xxx",
					InstanceDeleteOption: nil,
				},
			},
			wantCleanPolicy:  ccetypes.DeleteCleanPolicy,
			wantDeleteOption: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cleanPolicy, deleteOption := buildCleanPolicyAndDeleteOption(tt.args.ig, tt.args.opts)
			if !reflect.DeepEqual(cleanPolicy, tt.wantCleanPolicy) {
				t.Errorf("buildCleanPolicyAndDeleteOption() got = %v, want %v", cleanPolicy, tt.wantCleanPolicy)
			}
			if !reflect.DeepEqual(deleteOption, tt.wantDeleteOption) {
				t.Errorf("buildCleanPolicyAndDeleteOption() got1 = %v, want %v", deleteOption, tt.wantDeleteOption)
			}
		})
	}
}

// Test_instanceGroupService_ScaleUp is a unit test function for the instanceGroupService.ScaleUp method
func Test_instanceGroupService_ScaleUp(t *testing.T) {
	type mocks struct {
		ctrl        *gomock.Controller
		metaClient  meta.Interface
		quotaClient quota.Interface
		model       models.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceGroupID string
		replicas        int
		upToReplicas    int
		expectedErr     bool
		expectedName    string
		quotaClient     quota.Interface
		model           models.Interface
	}{
		{
			name: "scaleup",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						AccountID: "xxx",
						ClusterID: "xxx",
					},
				}, nil)
				metaClient.EXPECT().ListWorkflows(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.WorkflowList{
					Items: []ccev1.Workflow{
						{
							Spec: ccetypes.WorkflowSpec{
								WorkflowType: ccetypes.WorkflowTypeUpgradeNodes,
							},
							Status: ccetypes.WorkflowStatus{
								WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				}, nil)
				metaClient.EXPECT().CreateTask(gomock.Any(), gomock.Any()).Return(&ccev1.Task{
					ObjectMeta: metav1.ObjectMeta{
						Name: "task-mock",
					},
				}, nil)
				quotaClient := quotamock.NewMockInterface(ctrl)
				quotaClient.EXPECT().GetNodeQuota(gomock.Any(), gomock.Any()).Return(&ccetypes.Quota{
					Quota: 10,
					Used:  0,
				}, nil)

				modelsClient := modelcmock.NewMockInterface(ctrl)
				modelsClient.EXPECT().GetCluster(gomock.Any(), gomock.Any(), gomock.Any()).Return(
					&models.Cluster{Spec: &ccetypes.ClusterSpec{
						MasterConfig: ccetypes.MasterConfig{
							ManagedClusterMasterOption: ccetypes.ManagedClusterMasterOption{
								MasterFlavor: "medium"},
						},
					}}, nil)
				return &mocks{
					ctrl:        ctrl,
					metaClient:  metaClient,
					quotaClient: quotaClient,
					model:       modelsClient,
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "xxx",
			replicas:        0,
			upToReplicas:    2,
			expectedErr:     false,
			expectedName:    "task-mock",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			s := &InstanceGroupService{
				log:         logger.WithValues("name", "test"),
				K8SClient:   tc.mocks.metaClient,
				rand:        func(i int) string { return "mock" },
				quotaClient: tc.mocks.quotaClient,
				model:       tc.mocks.model,
			}
			gotName, err := s.ScaleUp(context.TODO(), tc.accountID, tc.instanceGroupID, tc.replicas, tc.upToReplicas)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
			if err == nil && gotName != tc.expectedName {
				t.Errorf("expected name: %s, got: %s", tc.expectedName, gotName)
			}
		})
	}
}

func Test_instanceGroupService_ScaleUpHpas(t *testing.T) {
	type mocks struct {
		ctrl        *gomock.Controller
		metaClient  meta.Interface
		quotaClient quota.Interface
		model       models.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceGroupID string
		replicas        int
		upToReplicas    int
		expectedErr     bool
		expectedName    string
		quotaClient     quota.Interface
		model           models.Interface
	}{
		{
			name: "scaleup",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				metaClient := mock.NewMockInterface(ctrl)

				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						AccountID: "xxx",
						ClusterID: "xxx",
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeHPAS,
							},
						},
					},
				}, nil)
				return &mocks{
					ctrl:       ctrl,
					metaClient: metaClient,
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "xxx",
			replicas:        0,
			upToReplicas:    2,
			expectedErr:     true,
			expectedName:    "task-mock",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			defer tc.mocks.ctrl.Finish()
			s := &InstanceGroupService{
				log:         logger.WithValues("name", "test"),
				K8SClient:   tc.mocks.metaClient,
				rand:        func(i int) string { return "mock" },
				quotaClient: tc.mocks.quotaClient,
				model:       tc.mocks.model,
			}
			gotName, err := s.ScaleUp(context.TODO(), tc.accountID, tc.instanceGroupID, tc.replicas, tc.upToReplicas)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
			if err == nil && gotName != tc.expectedName {
				t.Errorf("expected name: %s, got: %s", tc.expectedName, gotName)
			}
		})
	}
}

// TestInstanceGroupService_GetInstanceGroupStatus is a unit test function for testing InstanceGroupService's GetInstanceGroupStatus method
func TestInstanceGroupService_GetInstanceGroupStatus(t *testing.T) {
	type mocks struct {
		ctrl      *gomock.Controller
		k8sclient kubernetes.Interface
	}

	testCases := []struct {
		name            string
		mocks           *mocks
		accountID       string
		instanceGroupID string
		expectedErr     bool
	}{
		{
			name: "get succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec:   &ccetypes.InstanceGroupSpec{},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)
				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				node1 := &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "node1",
						Labels: map[string]string{"instance-group-id": "ig-1"},
					},
				}
				node2 := &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "node2",
						Labels: map[string]string{"instance-group-id": "ig-1"},
					},
				}
				return &mocks{
					ctrl:      ctrl,
					k8sclient: fake.NewSimpleClientset(node1, node2),
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "ig-1",
			expectedErr:     false,
		},
		{
			name: "get err",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec:   &ccetypes.InstanceGroupSpec{},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)
				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				return &mocks{
					ctrl: ctrl,
					//k8sclient: fake.NewSimpleClientset(node1, node2),
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "ig-1",
			expectedErr:     true,
		},
		{
			name: "get succeed",
			mocks: func() *mocks {
				ctrl := gomock.NewController(t)
				model := models.NewMockInterface(ctrl)
				model.EXPECT().GetInstanceGroupByCCEID(gomock.Any(), gomock.Any(), gomock.Any()).Return(&models.InstanceGroup{
					Spec:   &ccetypes.InstanceGroupSpec{},
					Status: &ccetypes.InstanceGroupStatus{},
				}, nil)
				metaClient := mock.NewMockInterface(ctrl)
				metaClient.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroup{}, nil)
				node1 := &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "node1",
						Labels: map[string]string{"instance-group-id": "ig-1"},
					},
					Status: corev1.NodeStatus{
						Conditions: []corev1.NodeCondition{
							{
								Type:   corev1.NodeReady,
								Status: corev1.ConditionTrue,
							},
							{
								Type:   corev1.NodeNetworkUnavailable,
								Status: corev1.ConditionFalse,
							},
						},
					},
				}
				node2 := &corev1.Node{
					ObjectMeta: metav1.ObjectMeta{
						Name:   "node2",
						Labels: map[string]string{"instance-group-id": "ig-1"},
					},
					Status: corev1.NodeStatus{
						Addresses: []corev1.NodeAddress{
							{
								Type:    corev1.NodeInternalIP,
								Address: "aa",
							},
						},
						Conditions: []corev1.NodeCondition{
							{
								Type:   corev1.NodeReady,
								Status: corev1.ConditionTrue,
							},
							{
								Type:   corev1.NodeNetworkUnavailable,
								Status: corev1.ConditionFalse,
							},
						},
					},
				}

				return &mocks{
					ctrl:      ctrl,
					k8sclient: fake.NewSimpleClientset(node1, node2),
				}
			}(),
			accountID:       "xxx",
			instanceGroupID: "ig-1",
			expectedErr:     false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			_, err := GetInstanceGroupStatus(context.TODO(), tc.mocks.k8sclient, tc.instanceGroupID)
			if (tc.expectedErr && err == nil) || (!tc.expectedErr && err != nil) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
			}
		})
	}
}

func Test_instanceGroupService_checkCCENodeNumLimitAndReplicas(t *testing.T) {
	cluster := models.Cluster{
		Spec: &ccetypes.ClusterSpec{
			AccountID: "123",
			ClusterID: "cce-123",
			MasterConfig: ccetypes.MasterConfig{
				MasterType:            ccetypes.MasterTypeManagedPro,
				ClusterHA:             0,
				ExposedPublic:         false,
				ClusterBLBVPCSubnetID: "",
				ManagedClusterMasterOption: ccetypes.ManagedClusterMasterOption{
					MasterFlavor: "l50",
				},
				ServerlessMasterOption: ccetypes.ServerlessMasterOption{},
				EdgeMasterOption:       ccetypes.EdgeMasterOption{},
			},
		},
	}
	type fields struct {
		log                          logger.InterfaceEx
		model                        modelcmock.Interface
		K8SClient                    meta.Interface
		filler                       *fillspec.BaseFiller
		fillClients                  fillclients.Clients
		quotaClient                  quota.Interface
		handler                      string
		rand                         utils.RandStringFunc
		skipUpdateInstanceGroupModel bool
		config                       *configuration.Config
	}
	type args struct {
		ctx       context.Context
		clusterID string
		accountID string
		addNum    int
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常",
			fields: func() fields {

				ctl := gomock.NewController(t)
				modelsClient := models.NewMockInterface(ctl)

				config := &configuration.Config{
					PluginConfig: &pluginclients.Config{
						FlavorConfig: &pluginclients.FlavorConfig{
							FlavorConfigMap: map[string]pluginclients.FlavorDeployConfig{
								"l50": pluginclients.FlavorDeployConfig{
									MasterPluginConfigList: nil,
									PluginConfigMap:        nil,
									RequiredNodes:          nil,
									ResourceLimit: pluginclients.ResourceLimit{
										NodeNum:      50,
										PodNum:       0,
										ServiceNum:   0,
										ConfigMapNum: 0,
										SecretNum:    0,
										PVNum:        0,
										PVCNum:       0,
										CRDNum:       0,
									},
								},
							},
						},
					},
				}

				ctx := context.TODO()

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
				}
				gomock.InOrder(
					modelsClient.EXPECT().GetCluster(ctx, "cce-123", "123").Return(&cluster, nil),
					modelsClient.EXPECT().GetInstances(ctx, "123", "cce-123").Return(instances, nil),
				)

				return fields{
					log:                          nil,
					model:                        modelsClient,
					K8SClient:                    nil,
					filler:                       nil,
					fillClients:                  nil,
					quotaClient:                  nil,
					handler:                      "",
					rand:                         nil,
					skipUpdateInstanceGroupModel: false,
					config:                       config,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-123",
				accountID: "123",
				addNum:    0,
			},
			wantErr: false,
		},
		{
			name: " ! check ",
			fields: func() fields {

				ctl := gomock.NewController(t)
				modelsClient := models.NewMockInterface(ctl)

				config := &configuration.Config{
					PluginConfig: &pluginclients.Config{
						FlavorConfig: &pluginclients.FlavorConfig{
							FlavorConfigMap: map[string]pluginclients.FlavorDeployConfig{
								"l50": pluginclients.FlavorDeployConfig{
									MasterPluginConfigList: nil,
									PluginConfigMap:        nil,
									RequiredNodes:          nil,
									ResourceLimit: pluginclients.ResourceLimit{
										NodeNum:      30,
										PodNum:       0,
										ServiceNum:   0,
										ConfigMapNum: 0,
										SecretNum:    0,
										PVNum:        0,
										PVCNum:       0,
										CRDNum:       0,
									},
								},
							},
						},
					},
				}

				ctx := context.TODO()

				instances := []*models.Instance{
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
					{
						Spec: &ccetypes.InstanceSpec{
							InstanceName: "cce-instance",
						},
					},
				}
				gomock.InOrder(
					modelsClient.EXPECT().GetCluster(ctx, "cce-123", "123").Return(&cluster, nil),
					modelsClient.EXPECT().GetInstances(ctx, "123", "cce-123").Return(instances, nil),
				)

				return fields{
					log:                          nil,
					model:                        modelsClient,
					K8SClient:                    nil,
					filler:                       nil,
					fillClients:                  nil,
					quotaClient:                  nil,
					handler:                      "",
					rand:                         nil,
					skipUpdateInstanceGroupModel: false,
					config:                       config,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				clusterID: "cce-123",
				accountID: "123",
				addNum:    30,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &InstanceGroupService{
				log:                          tt.fields.log,
				model:                        tt.fields.model,
				K8SClient:                    tt.fields.K8SClient,
				filler:                       tt.fields.filler,
				fillClients:                  tt.fields.fillClients,
				quotaClient:                  tt.fields.quotaClient,
				handler:                      tt.fields.handler,
				rand:                         tt.fields.rand,
				skipUpdateInstanceGroupModel: tt.fields.skipUpdateInstanceGroupModel,
				config:                       tt.fields.config,
			}
			if err := s.checkCCENodeNumLimitAndReplicas(tt.args.ctx, tt.args.clusterID, tt.args.accountID, tt.args.addNum); (err != nil) != tt.wantErr {
				t.Errorf("checkCCENodeNumLimitAndReplicas() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_CheckCASpecOkAfterDeleteInstances(t *testing.T) {
	ctx := context.TODO()
	ctrl := gomock.NewController(t)

	k8sClient := mock.NewMockInterface(ctrl)
	s := InstanceGroupService{
		K8SClient: k8sClient,
	}
	k8sClient.EXPECT().ListInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.InstanceGroupList{
		Items: []ccev1.InstanceGroup{
			{
				Spec: ccetypes.InstanceGroupSpec{
					CCEInstanceGroupID: "ig1",
					ClusterID:          "clusterID",
					Replicas:           5,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled:     true,
						MinReplicas: 3,
					},
				},
			},
			{
				Spec: ccetypes.InstanceGroupSpec{
					CCEInstanceGroupID: "ig2",
					ClusterID:          "clusterID",
					Replicas:           5,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled:     false,
						MinReplicas: 3,
					},
				},
			},
			{
				Spec: ccetypes.InstanceGroupSpec{
					CCEInstanceGroupID: "ig3",
					ClusterID:          "clusterID",
					Replicas:           5,
					ClusterAutoscalerSpec: &ccetypes.ClusterAutoscalerSpec{
						Enabled:     false,
						MinReplicas: 3,
					},
				},
			},
		},
	}, nil)
	igs, err := s.CheckCASpecOkAfterDeleteInstances(ctx, "clusterID", map[string]int{
		"ig1": 3,
		"ig2": 3,
		"ig3": 2,
	})
	assert.Assert(t, err == nil)
	assert.Assert(t, len(igs) == 1)
}

func TestInstanceGroupService_bindInstanceIAMRole(t *testing.T) {
	ctx := context.Background()
	type fields struct {
		log                          logger.InterfaceEx
		model                        modelcmock.Interface
		K8SClient                    meta.Interface
		filler                       *fillspec.BaseFiller
		fillClients                  fillclients.Clients
		quotaClient                  quota.Interface
		handler                      string
		rand                         utils.RandStringFunc
		skipUpdateInstanceGroupModel bool
		config                       *configuration.Config
		clientSet                    *clientset.ClientSet
	}
	type args struct {
		ctx                 context.Context
		ig                  *ccev1.InstanceGroup
		newJoinInstanceInfo []*ccetypes.InstancesToJoin
		log                 logger.InterfaceEx
		joinInstanceInfo    []*ccetypes.InstancesToJoin
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []string
		wantErr bool
	}{
		{
			name: "bind error",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bccClient := mock2.NewMockInterface(ctrl)
				bccClient.EXPECT().BindInstanceRole(ctx, "test-iam", []string{"i-1", "i-2"}, nil).Return(
					nil, fmt.Errorf("failed to bind instance role"))

				stsClient := mock3.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(ctx, "account-id").Return(nil)

				return fields{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							OpenBCCClient: bccClient,
							STSClient:     stsClient,
						},
					},
				}
			}(),
			args: args{
				ctx: ctx,
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						IAMRole:   &ccetypes.IAMRole{RoleName: "test-iam"},
						AccountID: "account-id",
					},
				},
				newJoinInstanceInfo: []*ccetypes.InstancesToJoin{
					{
						ExistedInstanceID: "i-1",
					},
					{
						ExistedInstanceID: "i-2",
					},
				},
			},
			want:    nil,
			wantErr: true,
		},
		{
			name: "success",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bccClient := mock2.NewMockInterface(ctrl)
				bccClient.EXPECT().BindInstanceRole(ctx, "test-iam", []string{"i-1", "i-2"}, nil).Return(
					&bccapi.BindInstanceRoleResult{
						InstanceRoleAssociations: []bccapi.InstanceRoleAssociations{
							{
								InstanceID: "i-1",
							},
							{
								InstanceID: "i-2",
							},
						},
					}, nil)

				stsClient := mock3.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(ctx, "account-id").Return(nil)

				return fields{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							OpenBCCClient: bccClient,
							STSClient:     stsClient,
						},
					},
				}
			}(),
			args: args{
				ctx: ctx,
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						IAMRole:   &ccetypes.IAMRole{RoleName: "test-iam"},
						AccountID: "account-id",
					},
				},
				newJoinInstanceInfo: []*ccetypes.InstancesToJoin{
					{
						ExistedInstanceID: "i-1",
					},
					{
						ExistedInstanceID: "i-2",
					},
				},
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "success",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				bccClient := mock2.NewMockInterface(ctrl)
				bccClient.EXPECT().BindInstanceRole(ctx, "test-iam", []string{"i-1", "i-2"}, nil).Return(
					&bccapi.BindInstanceRoleResult{
						InstanceRoleAssociations: []bccapi.InstanceRoleAssociations{
							{
								InstanceID: "i-1",
							},
						},
						FailInstances: []bccapi.FailInstances{
							{
								InstanceId:  "i-2",
								FailMessage: "failed to bind instance role",
							},
						},
					}, nil)

				stsClient := mock3.NewMockInterface(ctrl)
				stsClient.EXPECT().NewSignOption(ctx, "account-id").Return(nil)

				return fields{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							OpenBCCClient: bccClient,
							STSClient:     stsClient,
						},
					},
				}
			}(),
			args: args{
				ctx: ctx,
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						IAMRole:   &ccetypes.IAMRole{RoleName: "test-iam"},
						AccountID: "account-id",
					},
				},
				newJoinInstanceInfo: []*ccetypes.InstancesToJoin{
					{
						ExistedInstanceID: "i-1",
					},
					{
						ExistedInstanceID: "i-2",
					},
				},
			},
			want:    []string{"i-2"},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &InstanceGroupService{
				log:                          logger.WithValues("service", tt.name),
				model:                        tt.fields.model,
				K8SClient:                    tt.fields.K8SClient,
				filler:                       tt.fields.filler,
				fillClients:                  tt.fields.fillClients,
				quotaClient:                  tt.fields.quotaClient,
				handler:                      tt.fields.handler,
				rand:                         tt.fields.rand,
				skipUpdateInstanceGroupModel: tt.fields.skipUpdateInstanceGroupModel,
				config:                       tt.fields.config,
				clientSet:                    tt.fields.clientSet,
			}
			_, got, err := s.syncInstanceRole(tt.args.ctx, tt.args.ig, tt.args.newJoinInstanceInfo, s.log)
			if (err != nil) != tt.wantErr {
				t.Errorf("syncInstanceRole() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("syncInstanceRole() got = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestScaleUpExistNodeInCluster(t *testing.T) {
	testInfos := []struct {
		name         string
		ig           *ccev1.InstanceGroup
		param        ccesdk.ScaleUpExistInstanceGroupOption
		instanceList *ccev1.InstanceList
		wantErr      bool
	}{
		{
			name: "success",
			ig: &ccev1.InstanceGroup{
				Spec: ccetypes.InstanceGroupSpec{
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:          ccetypes.MachineTypeBCC,
							InstanceChargingType: bcc.PaymentTimingPostpaid,
							Labels: map[string]string{
								ccetypes.GPUSharePluginLabelKey: ccetypes.GPUSharePluginLabelValueDisable,
								ccetypes.NPUSharePluginLabelKey: ccetypes.GPUSharePluginLabelValueEnable,
							},
							ScaleDownDisabled: true,
						},
					},
				},
			},
			instanceList: &ccev1.InstanceList{
				Items: []ccev1.Instance{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name: "ins-1",
						},
						Spec: ccetypes.InstanceSpec{
							MachineType:          ccetypes.MachineTypeBCC,
							InstanceChargingType: bcc.PaymentTimingPostpaid,
						},
						Status: ccetypes.InstanceStatus{
							Machine: ccetypes.Machine{
								InstanceID: "i-1",
							},

							InstancePhase: InstancePhaseRunning,
						},
					},
				},
			},
			param: ccesdk.ScaleUpExistInstanceGroupOption{
				ExistedInstancesInCluster: []*ccesdk.ExistedInstanceInCluster{
					{
						ExistedInstanceID: "i-1",
					},
				},
				UseInstanceGroupConfig: true,
			},

			wantErr: false,
		},
		{
			name: "error",
			ig: &ccev1.InstanceGroup{
				Spec: ccetypes.InstanceGroupSpec{
					InstanceTemplate: ccetypes.InstanceTemplate{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType:          ccetypes.MachineTypeBCC,
							InstanceChargingType: bcc.PaymentTimingPostpaid,
							Labels: map[string]string{
								ccetypes.GPUSharePluginLabelKey: ccetypes.GPUSharePluginLabelValueDisable,
								ccetypes.NPUSharePluginLabelKey: ccetypes.GPUSharePluginLabelValueEnable,
							},
						},
					},
				},
			},
			instanceList: &ccev1.InstanceList{
				Items: []ccev1.Instance{
					{
						ObjectMeta: metav1.ObjectMeta{
							Name: "ins-1",
						},
						Spec: ccetypes.InstanceSpec{
							MachineType:          ccetypes.MachineTypeBCC,
							InstanceChargingType: bcc.PaymentTimingPrepaid,
						},
						Status: ccetypes.InstanceStatus{
							Machine: ccetypes.Machine{
								InstanceID: "i-1",
							},

							InstancePhase: InstancePhaseRunning,
						},
					},
				},
			},
			param: ccesdk.ScaleUpExistInstanceGroupOption{
				ExistedInstancesInCluster: []*ccesdk.ExistedInstanceInCluster{
					{
						ExistedInstanceID: "i-1",
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)

			metaMock := mock.NewMockInterface(ctrl)
			metaMock.EXPECT().ListInstances(gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(tt.instanceList, nil)
			metaMock.EXPECT().UpdateInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(nil)

			s := &InstanceGroupService{
				K8SClient: metaMock,
			}

			_, err := s.scaleUpExistNodeInCluster(context.TODO(), tt.ig, tt.param)
			assert.Equal(t, err != nil, tt.wantErr)
		})
	}
}

func TestInstanceGroupService_setUserData(t *testing.T) {
	type fields struct {
		log                          logger.InterfaceEx
		model                        modelcmock.Interface
		K8SClient                    meta.Interface
		filler                       *fillspec.BaseFiller
		fillClients                  fillclients.Clients
		quotaClient                  quota.Interface
		handler                      string
		rand                         utils.RandStringFunc
		skipUpdateInstanceGroupModel bool
		config                       *configuration.Config
		clientSet                    *clientset.ClientSet
	}
	type args struct {
		installGpuDriver bool
		ins              *ccesdk.InstanceSet
		ig               *ccev1.InstanceGroup
	}
	tests := []struct {
		name   string
		fields fields
		args   args
	}{
		{
			name: "len instance template is zero",
			args: args{
				installGpuDriver: true,
				ins: &ccesdk.InstanceSet{
					InstanceSpec: ccetypes.InstanceSpec{},
				},
				ig: &ccev1.InstanceGroup{
					TypeMeta:   metav1.TypeMeta{},
					ObjectMeta: metav1.ObjectMeta{},
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								UserData: "this is user data",
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				},
			},
		},
		{
			name: "len instance template more than zero",
			args: args{
				installGpuDriver: true,
				ins: &ccesdk.InstanceSet{
					InstanceSpec: ccetypes.InstanceSpec{},
				},
				ig: &ccev1.InstanceGroup{
					TypeMeta:   metav1.TypeMeta{},
					ObjectMeta: metav1.ObjectMeta{},
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplates: []ccetypes.InstanceTemplate{
							{
								InstanceSpec: ccetypes.InstanceSpec{
									UserData: "this is user data",
								},
							},
						},
					},
					Status: ccetypes.InstanceGroupStatus{},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &InstanceGroupService{
				log:                          tt.fields.log,
				model:                        tt.fields.model,
				K8SClient:                    tt.fields.K8SClient,
				filler:                       tt.fields.filler,
				fillClients:                  tt.fields.fillClients,
				quotaClient:                  tt.fields.quotaClient,
				handler:                      tt.fields.handler,
				rand:                         tt.fields.rand,
				skipUpdateInstanceGroupModel: tt.fields.skipUpdateInstanceGroupModel,
				config:                       tt.fields.config,
				clientSet:                    tt.fields.clientSet,
			}
			s.setUserData(true, tt.args.ins, tt.args.ig)
			if tt.args.ins.InstanceSpec.UserData == "" {
				t.Errorf("instance spec user data must not be null")
			}
		})
	}
}

func Test_verifyAndUpdateInstanceGroupSpec(t *testing.T) {
	s := &InstanceGroupService{
		log:   logger.WithValues("test", "test"),
		model: &models.Client{},
	}
	patches := gomonkey.ApplyFuncReturn((*models.Client).GetCluster, &models.Cluster{}, nil)
	patches.ApplyFuncReturn((*InstanceGroupService).VerifyAndPadDefault, fmt.Errorf("test"))
	defer patches.Reset()
	_, err := s.verifyAndUpdateInstanceGroupSpec(context.TODO(), &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplate: ccetypes.InstanceTemplate{
				InstanceSpec: ccetypes.InstanceSpec{
					RuntimeType:    ccetypes.RuntimeTypeContainerd,
					RuntimeVersion: "1.6.36",
				},
			},
		},
	}, &ccetypes.InstanceGroupSpec{
		InstanceTemplate: ccetypes.InstanceTemplate{},
	})
	assert.Equal(t, err == nil, false)

	_, err = s.verifyAndUpdateInstanceGroupSpec(context.TODO(), &ccev1.InstanceGroup{
		Spec: ccetypes.InstanceGroupSpec{
			InstanceTemplates: []ccetypes.InstanceTemplate{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						RuntimeType:    ccetypes.RuntimeTypeContainerd,
						RuntimeVersion: "1.6.36",
					},
				},
			},
		},
	}, &ccetypes.InstanceGroupSpec{
		InstanceTemplates: []ccetypes.InstanceTemplate{
			{},
		},
	})
	assert.Equal(t, err == nil, false)
}

func TestInstanceGroupService_overrideInstancesByInstanceGroup1(t *testing.T) {
	type args struct {
		ctx                                context.Context
		ig                                 *ccev1.InstanceGroup
		instances                          []*ccesdk.InstanceSet
		useInstanceGroupConfigWithDiskInfo bool
	}

	tests := []struct {
		name string
		args args
		want []*ccesdk.InstanceSet
	}{
		{
			name: "Override instance set with instance group config - without disk info",
			args: args{
				ctx: context.TODO(),
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								VPCConfig: ccetypes.VPCConfig{
									SecurityGroup: ccetypes.SecurityGroup{
										EnableCCERequiredSecurityGroup: true,
										EnableCCEOptionalSecurityGroup: true,
									},
									SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
								},
								EhcClusterID: "test-ehc-cluster",
								DeployCustomConfig: ccetypes.DeployCustomConfig{
									KubeletRootDir: "/var/lib/kubelet",
								},
								Tags: []ccetypes.Tag{
									{TagKey: "key1", TagValue: "value1"},
								},
								RelationTag:                   true,
								CheckGPUDriver:                true,
								NvidiaContainerToolkitVersion: "1.13.5",
							},
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								ID:   "sg-id-1",
								Name: "sg-name-1",
								Type: ccetypes.SecurityGroupTypeNormal,
							},
						},
						IAMRole: &ccetypes.IAMRole{
							RoleName: "test-role",
						},
					},
				},
				instances: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
							},
							InstanceResource: ccetypes.InstanceResource{},
						},
					},
				},
				useInstanceGroupConfigWithDiskInfo: false,
			},
			want: []*ccesdk.InstanceSet{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						VPCConfig: ccetypes.VPCConfig{
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: true,
								EnableCCEOptionalSecurityGroup: true,
							},
							SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
							SecurityGroups: []ccetypes.SecurityGroupV2{
								{
									ID:   "sg-id-1",
									Name: "sg-name-1",
									Type: ccetypes.SecurityGroupTypeNormal,
								},
							},
						},
						EhcClusterID: "test-ehc-cluster",
						DeployCustomConfig: ccetypes.DeployCustomConfig{
							KubeletRootDir: "/var/lib/kubelet",
						},
						Tags: []ccetypes.Tag{
							{TagKey: "key1", TagValue: "value1"},
						},
						RelationTag:                   true,
						CheckGPUDriver:                false, // GPU驱动以实际传入为准，沿用节点组操作系统配置是前端逻辑，后端没有该字段
						NvidiaContainerToolkitVersion: "1.13.5",
						IAMRole: &ccetypes.IAMRole{
							RoleName: "test-role",
						},

						InstanceResource: ccetypes.InstanceResource{},
					},
				},
			},
		},
		{
			name: "Override instance set with instance group config - with disk info",
			args: args{
				ctx: context.TODO(),
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								VPCConfig: ccetypes.VPCConfig{
									SecurityGroup: ccetypes.SecurityGroup{
										EnableCCERequiredSecurityGroup: true,
									},
									SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
								},
								NvidiaContainerToolkitVersion: "1.13.5",
							},
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								ID:   "sg-id-1",
								Name: "sg-name-1",
								Type: ccetypes.SecurityGroupTypeNormal,
							},
						},
					},
				},
				instances: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
							},
							InstanceResource: ccetypes.InstanceResource{
								CDSList: []ccetypes.CDSConfig{
									{
										StorageType: "hp1",
										CDSSize:     100,
									},
								},
								EphemeralDiskList: []ccetypes.EphemeralDiskConfig{
									{
										SizeInGB: 200,
									},
								},
							},
						},
					},
				},
				useInstanceGroupConfigWithDiskInfo: true,
			},
			want: []*ccesdk.InstanceSet{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						VPCConfig: ccetypes.VPCConfig{
							SecurityGroup: ccetypes.SecurityGroup{
								EnableCCERequiredSecurityGroup: true,
							},
							SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
							SecurityGroups: []ccetypes.SecurityGroupV2{
								{
									ID:   "sg-id-1",
									Name: "sg-name-1",
									Type: ccetypes.SecurityGroupTypeNormal,
								},
							},
						},
						NvidiaContainerToolkitVersion: "1.13.5",
						InstanceResource: ccetypes.InstanceResource{
							CDSList: []ccetypes.CDSConfig{
								{
									StorageType: "hp1",
									CDSSize:     100,
								},
							},
							EphemeralDiskList: []ccetypes.EphemeralDiskConfig{
								{
									SizeInGB: 200,
								},
							},
						},
					},
				},
			},
		},
		{
			name: "Override instance set with different security group type",
			args: args{
				ctx: context.TODO(),
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								VPCConfig: ccetypes.VPCConfig{
									SecurityGroupType: ccetypes.SecurityGroupTypeEnterprise,
									SecurityGroups: []ccetypes.SecurityGroupV2{
										{
											ID:   "sg-vpc-1",
											Name: "sg-vpc-1",
											Type: ccetypes.SecurityGroupTypeEnterprise,
										},
									},
								},
							},
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								ID:   "sg-id-1",
								Name: "sg-name-1",
								Type: ccetypes.SecurityGroupTypeEnterprise,
							},
						},
					},
				},
				instances: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
								SecurityGroups: []ccetypes.SecurityGroupV2{
									{
										ID:   "sg-instance-1",
										Name: "sg-instance-1",
										Type: ccetypes.SecurityGroupTypeNormal,
									},
								},
							},
						},
					},
				},
				useInstanceGroupConfigWithDiskInfo: false,
			},
			want: []*ccesdk.InstanceSet{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						VPCConfig: ccetypes.VPCConfig{
							SecurityGroup:     ccetypes.SecurityGroup{},
							SecurityGroupType: ccetypes.SecurityGroupTypeEnterprise,
							SecurityGroups: []ccetypes.SecurityGroupV2{
								{
									ID:   "sg-id-1",
									Name: "sg-name-1",
									Type: ccetypes.SecurityGroupTypeEnterprise,
								},
							},
						},
						InstanceResource: ccetypes.InstanceResource{},
					},
				},
			},
		},

		{
			name: "Override instance label by instancegroup label",
			args: args{
				ctx: context.TODO(),
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								VPCConfig: ccetypes.VPCConfig{
									SecurityGroupType: ccetypes.SecurityGroupTypeEnterprise,
									SecurityGroups: []ccetypes.SecurityGroupV2{
										{
											ID:   "sg-vpc-1",
											Name: "sg-vpc-1",
											Type: ccetypes.SecurityGroupTypeEnterprise,
										},
									},
								},
								Labels: map[string]string{
									"a": "a",
								},
								Annotations: map[string]string{
									"a": "a",
								},
								Taints: ccetypes.InstanceTaints{
									{
										Key: "taint-1",
									},
								},
							},
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								ID:   "sg-id-1",
								Name: "sg-name-1",
								Type: ccetypes.SecurityGroupTypeEnterprise,
							},
						},
					},
				},
				instances: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							VPCConfig: ccetypes.VPCConfig{
								SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
								SecurityGroups: []ccetypes.SecurityGroupV2{
									{
										ID:   "sg-instance-1",
										Name: "sg-instance-1",
										Type: ccetypes.SecurityGroupTypeNormal,
									},
								},
							},
							Labels: map[string]string{
								"b": "b",
							},
							Annotations: map[string]string{
								"a": "b",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key: "taint-2",
								},
							},
						},
					},
				},
				useInstanceGroupConfigWithDiskInfo: false,
			},
			want: []*ccesdk.InstanceSet{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						VPCConfig: ccetypes.VPCConfig{
							SecurityGroup:     ccetypes.SecurityGroup{},
							SecurityGroupType: ccetypes.SecurityGroupTypeEnterprise,
							SecurityGroups: []ccetypes.SecurityGroupV2{
								{
									ID:   "sg-id-1",
									Name: "sg-name-1",
									Type: ccetypes.SecurityGroupTypeEnterprise,
								},
							},
						},
						Labels: map[string]string{
							"b": "b",
							"a": "a",
						},
						Annotations: map[string]string{
							"a": "a",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key: "taint-2",
							},
							{
								Key: "taint-1",
							},
						},
						InstanceResource: ccetypes.InstanceResource{},
					},
				},
			},
		},
		{
			name: "Override instance label by instancegroup label",
			args: args{
				ctx: context.TODO(),
				ig: &ccev1.InstanceGroup{
					Spec: ccetypes.InstanceGroupSpec{
						InstanceTemplate: ccetypes.InstanceTemplate{
							InstanceSpec: ccetypes.InstanceSpec{
								MachineType: ccetypes.MachineTypeHPAS,
								VPCConfig: ccetypes.VPCConfig{
									SecurityGroupType: ccetypes.SecurityGroupTypeEnterprise,
									SecurityGroups: []ccetypes.SecurityGroupV2{
										{
											ID:   "sg-vpc-1",
											Name: "sg-vpc-1",
											Type: ccetypes.SecurityGroupTypeEnterprise,
										},
									},
								},
								InstanceResource: ccetypes.InstanceResource{
									EphemeralDiskList: []ccetypes.EphemeralDiskConfig{
										{
											Path:       "/home/<USER>",
											DataDevice: "nvme",
										},
									},
								},
								Labels: map[string]string{
									"a": "a",
								},
								Annotations: map[string]string{
									"a": "a",
								},
								Taints: ccetypes.InstanceTaints{
									{
										Key: "taint-1",
									},
								},
							},
						},
						DefaultSecurityGroups: []ccetypes.SecurityGroupV2{
							{
								ID:   "sg-id-1",
								Name: "sg-name-1",
								Type: ccetypes.SecurityGroupTypeEnterprise,
							},
						},
					},
				},
				instances: []*ccesdk.InstanceSet{
					{
						InstanceSpec: ccetypes.InstanceSpec{
							MachineType: ccetypes.MachineTypeHPAS,
							VPCConfig: ccetypes.VPCConfig{
								SecurityGroupType: ccetypes.SecurityGroupTypeNormal,
								SecurityGroups: []ccetypes.SecurityGroupV2{
									{
										ID:   "sg-instance-1",
										Name: "sg-instance-1",
										Type: ccetypes.SecurityGroupTypeNormal,
									},
								},
							},
							Labels: map[string]string{
								"b": "b",
							},
							Annotations: map[string]string{
								"a": "b",
							},
							Taints: ccetypes.InstanceTaints{
								{
									Key: "taint-2",
								},
							},
						},
					},
				},
				useInstanceGroupConfigWithDiskInfo: true,
			},
			want: []*ccesdk.InstanceSet{
				{
					InstanceSpec: ccetypes.InstanceSpec{
						MachineType: ccetypes.MachineTypeHPAS,
						VPCConfig: ccetypes.VPCConfig{
							SecurityGroup:     ccetypes.SecurityGroup{},
							SecurityGroupType: ccetypes.SecurityGroupTypeEnterprise,
							SecurityGroups: []ccetypes.SecurityGroupV2{
								{
									ID:   "sg-id-1",
									Name: "sg-name-1",
									Type: ccetypes.SecurityGroupTypeEnterprise,
								},
							},
						},
						Labels: map[string]string{
							"b": "b",
							"a": "a",
						},
						Annotations: map[string]string{
							"a": "a",
						},
						Taints: ccetypes.InstanceTaints{
							{
								Key: "taint-2",
							},
							{
								Key: "taint-1",
							},
						},
						InstanceResource: ccetypes.InstanceResource{
							EphemeralDiskList: []ccetypes.EphemeralDiskConfig{
								{
									Path:       "/home/<USER>",
									DataDevice: "nvme",
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := &InstanceGroupService{}
			got := s.overrideInstancesByInstanceGroup(tt.args.ctx, tt.args.ig, tt.args.instances, tt.args.useInstanceGroupConfigWithDiskInfo)

			if diff := cmp.Diff(tt.want, got); diff != "" {
				t.Errorf("overrideInstancesByInstanceGroup() mismatch (-want +got):\n%s", diff)
			}
		})
	}
}

func Test_mergeSecurityGroups(t *testing.T) {
	s := &InstanceGroupService{
		K8SClient: &meta.Client{},
		filler: &fillspec.BaseFiller{
			SDKClients: &clients.Clients{},
		},
	}
	p := gomonkey.ApplyFuncReturn((*meta.Client).GetCluster, &ccev1.Cluster{}, nil)
	p.ApplyFuncReturn((*InstanceGroupService).getSecurityGroupsInClusterVPC, nil, nil, fmt.Errorf("test"))
	err := s.mergeSecurityGroups(context.TODO(), &models.InstanceGroup{
		Spec: &ccetypes.InstanceGroupSpec{},
	}, &ccev1.InstanceGroup{})
	assert.Equal(t, err != nil, true)

	err = s.checkInstanceSecurityGroupCount(context.TODO(), nil, &ccev1.Cluster{}, &ccev1.InstanceGroup{})
	assert.NilError(t, err)
}
