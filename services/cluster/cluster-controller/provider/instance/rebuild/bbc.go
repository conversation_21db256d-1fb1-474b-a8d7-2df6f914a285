package rebuild

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	bbcapi "github.com/baidubce/bce-sdk-go/services/bbc"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bbc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/retrypolicy"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/provider"
)

type BBCClient struct {
	stsclient      sts.Interface
	logicBbcClient logicbcc.Interface
	openBbcClient  bbc.Interface

	existedBCCClient *BCCClient
	region           string
	config           *provider.Config
}

var _ Interface = &BBCClient{}

func NewBBCClient(ctx context.Context, config *provider.Config) (*BBCClient, error) {
	if config == nil {
		return nil, fmt.Errorf("NewBBCClient failed: config is nil")
	}

	if config.STSEndpoint == "" || config.IAMEndpoint == "" || config.BCCLogicEndpoint == "" ||
		config.Region == "" || config.Timeout == 0 {
		str, _ := json.Marshal(config)
		return nil, fmt.Errorf("NewExistedLogicBBCClient failed: config %v not matched", string(str))
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, config.MaxRetryTime, config.RetryIntervalSeconds)

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint:    config.STSEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, &bce.Config{
		Endpoint:    config.IAMEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
		// IAM Disaster Tolerance Config
		IAMAgentEndpoint:            config.IAMAgentEndpoint,
		IAMDisasterToleranceEnabled: config.IAMDisasterToleranceEnabled,
	}, config.ServiceRoleName, config.ServiceName, config.ServicePassword)

	bbcclient := logicbcc.NewClient(&bce.Config{
		Endpoint:    config.BCCLogicEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	})

	// 初始化 bbc client
	openBBCClient := bbc.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.BBCEndpoint,
		RetryPolicy: retryPolicy,
	})

	existedBCCClient, err := NewBCCClient(ctx, config)
	if err != nil {
		logger.Errorf(ctx, "NewBCCClient failed: %v", err)
		return nil, err
	}

	return &BBCClient{
		stsclient:        stsclient,
		logicBbcClient:   bbcclient,
		openBbcClient:    openBBCClient,
		existedBCCClient: existedBCCClient,
		region:           config.Region,
		config:           config,
	}, nil
}

func (c *BBCClient) GetMachine(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance) (bool, *MachineAbstract, error) {
	return c.existedBCCClient.GetMachine(ctx, cluster, instance)
}

func (c *BBCClient) RebuildMachine(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance) error {
	if instance == nil {
		return fmt.Errorf("instance is nil")
	}

	accountID := instance.Spec.AccountID
	if accountID == "" {
		return fmt.Errorf("instance.Spec.AccountID is empty")
	}

	instanceID := instance.Status.Machine.InstanceID
	if instanceID == "" {
		return fmt.Errorf("instance.Status.Machine.InstanceID is empty")
	}

	imageID := instance.Spec.ImageID
	if imageID == "" {
		return fmt.Errorf("instance.Spec.ImageID is empty")
	}

	raidId := instance.Spec.BBCOption.RaidID
	sysRootSize := instance.Spec.InstanceResource.RootDiskSize

	// 因后续bbc不再迭代，bbc团队不支持接口返回现有的raidID信息，所以针对鉴智客户，与tianpengwei确认，采用特殊处理。
	// 针对 GPU-GTX3090-04S套餐、账户ID为 4058c10b1da14a558d8a5adbb30c9744 和 2da0b4dda06f4903a96a2aa5b03a3b4a 这两个账号
	// raidID固定为raid-e9bgHKD2，SysRootSize固定为100G
	if strings.EqualFold(string(instance.Spec.BBCOption.Flavor), "GPU-GTX3090-04S") &&
		(accountID == "4058c10b1da14a558d8a5adbb30c9744" || accountID == "2da0b4dda06f4903a96a2aa5b03a3b4a") {
		logger.Infof(ctx, "for jianzhi")
		raidId = "raid-e9bgHKD2"
		sysRootSize = 100
	}
	
	if raidId == "" {
		return fmt.Errorf("instance.Spec.BBCOption.RaidID is empty")
	}

	if sysRootSize <= 0 {
		return fmt.Errorf("instance.Spec.InstanceResource.RootDiskSize is empty")
	}

	if instance.Spec.AdminPassword == "" {
		return fmt.Errorf("instance.Spec.AdminPassword is empty")
	}

	// 解密
	adminPassword, err := utils.AESCFBDecrypt(instance.Spec.AdminPassword)
	if err != nil {
		return fmt.Errorf("AESCFBDecrypt failed: %v", err)
	}

	encrpyPassword, signOption, err := c.getPasswordAndSignOption(ctx, accountID, adminPassword)
	if err != nil {
		logger.Errorf(ctx, "getPasswordAndSignOption failed: %s", err)
		return err
	}

	args := &bbcapi.RebuildBatchInstanceArgs{
		InstanceIds: []string{
			instanceID,
		},
		ImageId:   instance.Spec.ImageID,
		AdminPass: encrpyPassword,
		// ReserveData目前没保存，所以这里都为false
		// TODO 产品是否需要设计
		IsPreserveData: instance.Spec.BBCOption.ReserveData,
		RaidId:         raidId,
		// bbc的系统盘大小目前保存在这里面
		SysRootSize: sysRootSize,
	}

	logger.Infof(ctx, "BatchRebuildInstances: %s", utils.ToJSON(args))

	if _, err := c.openBbcClient.BatchRebuildInstances(ctx, args, signOption); err != nil {
		logger.Errorf(ctx, "BatchRebuildInstances %s failed: %s", instanceID, err)
		return err
	}

	return nil
}

func (c *BBCClient) getPasswordAndSignOption(ctx context.Context, userID string, password string) (string, *bce.SignOption, error) {
	cred, err := c.stsclient.GetCredential(ctx, userID)
	if err != nil {
		logger.Errorf(ctx, "GetCredential failed: %v", err)
		return "", nil, err
	}

	if cred == nil {
		logger.Errorf(ctx, "GetCredential failed: cred is nil")
		return "", nil, fmt.Errorf("GetCredential return nil")
	}

	// Password 加密
	if c.config.ServiceAccessKeySecret == "" {
		return "", nil, fmt.Errorf("ServiceAccessKeySecret is empty")
	}
	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey(c.config.ServiceAccessKeySecret, password)
	if err != nil {
		logger.Errorf(ctx, "Aes128EncryptUseSecreteKey failed: %s", err)
		return "", nil, err
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", cred.Token.ID)
	option.AddHeader("X-Bce-Security-Token", cred.SessionToken)
	option.Credentials = bce.NewCredentials(cred.AccessKeyID, cred.SecretAccessKey)

	// 传入服务账号 AK, 用于 BCC 查询 SK 解码 AdminPass
	if c.config.ServiceAccessKeyID == "" {
		return "", nil, fmt.Errorf("ServiceAccessKeyID is empty")
	}
	option.AddHeader("encrypted-key", c.config.ServiceAccessKeyID)

	option.AddHeadersToSign("host")

	return encrpyPassword, option, nil
}
