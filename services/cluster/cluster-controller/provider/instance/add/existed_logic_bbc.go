// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2019/11/25 13:27:00, by <EMAIL>, create
*/
/*
DESCRIPTION
调用 BBC NovaAPI 实现 provider/instance.Interface 接口:
* EnsureMachine: 确保对应 BBC 重装 OS 成功, 保证接口幂等;
*/

package add

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	bbcapi "github.com/baidubce/bce-sdk-go/services/bbc"
	bccapi "github.com/baidubce/bce-sdk-go/services/bcc/api"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/tag"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bbc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logictag"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/retrypolicy"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/provider"
)

var _ Interface = &ExistedLogicBBCClient{}

// ExistedLogicBBCClient 调用 BBC ConsoleAPI, 实现机器的重装 OS
type ExistedLogicBBCClient struct {
	stsclient        sts.Interface
	bbcclient        logicbcc.Interface
	openApiBBCClient bbc.Interface
	logictagClient   logictag.Interface

	existedBCCClient *ExistedLogicBCCClient
	region           string
	config           *provider.Config
}

func (c *ExistedLogicBBCClient) UnbindInstanceTags(ctx context.Context, tags []tag.Tag, accountID string, instanceID string) error {
	return nil
}

func (c *ExistedLogicBBCClient) BindInstanceTags(ctx context.Context, tags []tag.Tag, accountID, instanceID string) error {
	return nil
}

func (c *ExistedLogicBBCClient) BindInstanceRole(ctx context.Context, roleName, accountID string, instanceIDs ...string) error {
	return nil
}

func (c *ExistedLogicBBCClient) GetStockBySpecAndEhcClusterID(ctx context.Context, instance *ccev1.Instance, delta int) error {
	return nil
}

// GetServersByOrderId - 获取 instance
func (c *ExistedLogicBBCClient) GetServersByOrderId(ctx context.Context, orderID string, cluster *ccev1.Cluster, instance *ccev1.Instance, config *types.Config) ([]bcc.OrderInstance, error) {
	return []bcc.OrderInstance{}, nil
}

// NewExistedLogicBBCClient 返回 &ExistedLogicBBCClient
func NewExistedLogicBBCClient(ctx context.Context, config *provider.Config) (Interface, error) {
	if config == nil {
		return nil, errors.New("NewExistedLogicBBCClient failed: config is nil")
	}

	if config.STSEndpoint == "" || config.IAMEndpoint == "" || config.BCCLogicEndpoint == "" ||
		config.Region == "" || config.Timeout == 0 {
		str, _ := json.Marshal(config)
		return nil, fmt.Errorf("NewExistedLogicBBCClient failed: config %v not matched", string(str))
	}

	retryPolicy := retrypolicy.NewIntervalRetryPolicy(ctx, config.MaxRetryTime, config.RetryIntervalSeconds)

	// 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint:    config.STSEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	}, &bce.Config{
		Endpoint:    config.IAMEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
		// IAM Disaster Tolerance Config
		IAMAgentEndpoint:            config.IAMAgentEndpoint,
		IAMDisasterToleranceEnabled: config.IAMDisasterToleranceEnabled,
	}, config.ServiceRoleName, config.ServiceName, config.ServicePassword)

	bbcclient := logicbcc.NewClient(&bce.Config{
		Endpoint:    config.BCCLogicEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	})

	// 初始化 bbc client
	openBBCClient := bbc.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		Endpoint:    config.BBCEndpoint,
		RetryPolicy: retryPolicy,
	})

	logictagClient := logictag.NewClient(&bce.Config{
		Endpoint:    config.LogicTagEndpoint,
		Checksum:    true,
		Timeout:     config.Timeout * time.Second,
		Region:      config.Region,
		RetryPolicy: retryPolicy,
	})
	logictagClient.SetDebug(true)

	existedLogicBCCClient, err := NewExistedLogicBCCClient(ctx, config)
	if err != nil {
		logger.Errorf(ctx, "NewExistedLogicBCCClient failed: %v", err)
		return nil, err
	}

	region := config.Region

	return &ExistedLogicBBCClient{
		stsclient:        stsclient,
		bbcclient:        bbcclient,
		existedBCCClient: existedLogicBCCClient,
		openApiBBCClient: openBBCClient,
		logictagClient:   logictagClient,
		region:           region,
		config:           config,
	}, nil
}

// GetMachine - 获取机器状态
//
// PARAMS:
//   - ctx: The context to trace request
//   - instance: *ccev1.Instance
//
// RETURNS:
//
//	bool: 移入节点是否已经发起重装系统
//	MachineAbstract: MachineAbstract.Ready, 节点是否已经完成系统重装
//	error: nil if succeed, error if fail
func (c *ExistedLogicBBCClient) GetMachine(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance) (bool, *MachineAbstract, error) {
	return c.existedBCCClient.GetMachine(ctx, cluster, instance)
}

func (c *ExistedLogicBBCClient) ListMachines(ctx context.Context, options ListOptions) ([]*MachineAbstract, error) {
	return c.existedBCCClient.ListMachines(ctx, options)
}

// CreateMachine - 移入节点重装系统
//
// PARAMS:
//   - ctx: The context to trace request
//   - cluster: *ccev1.Cluster
//   - instance: *ccev1.Instance
//
// RETURNS:
//
//	string: OrderID, 订单 ID
//	string: InstanceID, 虚机短 ID
//	string: InstanceUUID, 虚机长 ID
//	error: nil if succeed, error if fail
func (c *ExistedLogicBBCClient) CreateMachine(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance, clientToken string) (string, string, string, error) {
	if instance == nil {
		return "", "", "", errors.New(" instance is nil")
	}

	if instance.Spec.Existed == false {
		return "", "", "", errors.New("instance.Spec.Existed is false")
	}

	accountID := instance.Spec.AccountID
	if accountID == "" {
		return "", "", "", errors.New("accountID is empty")
	}

	instanceID := instance.Spec.ExistedOption.ExistedInstanceID
	if instanceID == "" {
		return "", "", "", errors.New("instance.Spec.existedOption.ExistedInstanceID is empty")
	}

	// 复用 Existed BCC 重装前检查
	rebuild, err := needRebuild(ctx, instance)
	if err != nil {
		logger.Errorf(ctx, "checkBeforRebuild failed: %s", err)
		return "", "", "", err
	}

	if rebuild == true {
		raidId := instance.Spec.BBCOption.RaidID
		sysRootSize := instance.Spec.InstanceResource.RootDiskSize

		// 因后续bbc不再迭代，bbc团队不支持接口返回现有的raidID信息，所以针对鉴智客户，与tianpengwei确认，采用特殊处理。
		// 针对 GPU-GTX3090-04S套餐、账户ID为 4058c10b1da14a558d8a5adbb30c9744 和 2da0b4dda06f4903a96a2aa5b03a3b4a 这两个账号
		// raidID固定为raid-e9bgHKD2，SysRootSize固定为100G
		if strings.EqualFold(string(instance.Spec.BBCOption.Flavor), "GPU-GTX3090-04S") &&
			(accountID == "4058c10b1da14a558d8a5adbb30c9744" || accountID == "2da0b4dda06f4903a96a2aa5b03a3b4a") {
			logger.Infof(ctx, "for jianzhi")
			raidId = "raid-e9bgHKD2"
			sysRootSize = 100
		}
		if raidId == "" {
			return "", "", "", errors.New("instance.Spec.BBCOption.RaidID is empty")
		}

		if sysRootSize <= 0 {
			return "", "", "", errors.New("instance.Spec.InstanceResource.RootDiskSize is empty")
		}

		// 检查 BBCOption
		if instance.Spec.AdminPassword == "" {
			return "", "", "", errors.New("instance.Spec.AdminPassword is empty")
		}

		// 解密
		adminPassword, err := utils.AESCFBDecrypt(instance.Spec.AdminPassword)
		if err != nil {
			return "", "", "", fmt.Errorf("AESCFBDecrypt failed: %v", err)
		}

		encrpyPassword, signOption, err := c.getPasswordAndSignOption(ctx, accountID, adminPassword)
		if err != nil {
			logger.Errorf(ctx, "getPasswordAndSignOption failed: %s", err)
		}

		// 重装系统
		args := &bbcapi.RebuildBatchInstanceArgs{
			InstanceIds: []string{
				instanceID,
			},
			ImageId:   instance.Spec.ImageID,
			AdminPass: encrpyPassword,
			// TODO 待产品设计，目前全部为false
			IsPreserveData: instance.Spec.BBCOption.ReserveData,
			RaidId:         raidId,
			// bbc的系统盘大小目前保存在这里面
			SysRootSize: sysRootSize,
		}

		logger.Infof(ctx, "BatchRebuildInstances: %s", utils.ToJSON(args))

		if _, err := c.openApiBBCClient.BatchRebuildInstances(ctx, args, signOption); err != nil {
			logger.Errorf(ctx, "BatchRebuildInstances %s failed: %s", instanceID, err)
			return "", instanceID, "", err
		}
	}

	// 设置重装系统标志
	instance.Status.ReinstallOSAlready = true

	return "", instanceID, "", nil
}

func (c *ExistedLogicBBCClient) getPasswordAndSignOption(ctx context.Context, userID string, password string) (string, *bce.SignOption, error) {
	cred, err := c.stsclient.GetCredential(ctx, userID)
	if err != nil {
		logger.Errorf(ctx, "GetCredential failed: %v", err)
		return "", nil, err
	}

	if cred == nil {
		logger.Errorf(ctx, "GetCredential failed: cred is nil")
		return "", nil, errors.New("GetCredential return nil")
	}

	// Password 加密
	if c.config.ServiceAccessKeySecret == "" {
		return "", nil, errors.New("ServiceAccessKeySecret is empty")
	}
	encrpyPassword, err := bccapi.Aes128EncryptUseSecreteKey(c.config.ServiceAccessKeySecret, password)
	if err != nil {
		logger.Errorf(ctx, "Aes128EncryptUseSecreteKey failed: %s", err)
		return "", nil, err
	}

	// 构建 SignOption
	option := &bce.SignOption{}

	option.AddHeader("X-Bce-Request-Id", logger.GetUUID())
	option.AddHeader("X-Auth-Token", cred.Token.ID)
	option.AddHeader("X-Bce-Security-Token", cred.SessionToken)
	option.Credentials = bce.NewCredentials(cred.AccessKeyID, cred.SecretAccessKey)

	// 传入服务账号 AK, 用于 BCC 查询 SK 解码 AdminPass
	if c.config.ServiceAccessKeyID == "" {
		return "", nil, errors.New("ServiceAccessKeyID is empty")
	}
	option.AddHeader("encrypted-key", c.config.ServiceAccessKeyID)

	option.AddHeadersToSign("host")

	return encrpyPassword, option, nil
}

// CreateMachines - 批量移入节点重装
//
// PARAMS:
//   - ctx: The context to trace request
//   - cluster: *ccev1.Cluster
//   - instance: *ccev1.Instance template
//   - replicas: instances replicas
//
// RETURNS:
//
//	string: OrderID, 订单ID
//	[]string: InstanceID, 虚机短ID列表
//	[]string: InstanceUUID, 虚机长ID列表
//	error: nil if succeed, error if fail
//	注意: instance长短ID列表长度一定要等长
func (c *ExistedLogicBBCClient) CreateMachines(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance, replicas int, clientToken string) (string, []string, []string, error) {
	return "", nil, nil, errors.New("not implement")
}

// SetHostname - 设置 instance hostname
func (c *ExistedLogicBBCClient) SetHostname(ctx context.Context, cluster *ccev1.Cluster, instance *ccev1.Instance, config *types.Config) error {
	return c.existedBCCClient.setHostname(ctx, cluster, instance, config)
}
