package add

import (
	"context"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bbc"
	bbcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bbc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	bccmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	logicbccmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logictag"
	logictagmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logictag/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/order"
	bcests "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/tag"
	tagmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/tag/mock"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	stsmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts/mock"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/provider"
)

func TestLogicBCCClient_GetMachine(t *testing.T) {
	type fields struct {
		ctl              *gomock.Controller
		stsclient        sts.Interface
		bbcclient        logicbcc.Interface
		cluster          *ccev1.Cluster
		instance         *ccev1.Instance
		tagclient        tag.Interface
		openApiBBCClient bbc.Interface
		logictagClient   logictag.Interface
		openApiBCCClient bcc.Interface
	}

	type args struct {
		ctx context.Context
	}

	tests := []struct {
		name         string
		fields       fields
		args         args
		wantExist    bool
		wantAbstract *MachineAbstract
		wantErr      bool
	}{
		{
			name: "机器存在, 已被重装, 状态正常",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bccclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				tagclient := tagmock.NewMockInterface(ctl)
				logicclient := logictagmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := true
				cluster := &ccev1.Cluster{}
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						AccountID: "account-id",
						UserID:    "user-id",
						Existed:   true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
						Tags: ccetypes.TagList{
							{
								TagKey:   "source",
								TagValue: "CCE",
							},
						},
					},
					Status: ccetypes.InstanceStatus{
						ReinstallOSAlready: true,
					},
				}

				tags := []tag.Tag{
					{
						TagKey:   "source",
						TagValue: "CCE",
					},
				}

				gomock.InOrder(
					stsclient.EXPECT().NewSignOption(ctx, "account-id").Return(nil),
					bccclient.EXPECT().GetServerByID(ctx, "instance-id", nil).Return(&logicbcc.Server{
						InstanceID:   "instance-id",
						InstanceUUID: "instance-uuid",
						OrderID:      "order-id",
						InternalIP:   "vpc-ip",
						FloatingIP:   "floating-ip",
						Status:       logicbcc.ServerStatusActive,
					}, nil),
					stsclient.EXPECT().NewSignOption(ctx, "account-id").Return(nil),
					bccclient.EXPECT().BatchUpdateApplication(ctx, &logicbcc.BatchUpdateApplicationArgs{
						InstanceIDs: []string{
							"instance-id",
						},
						Application: logicbcc.ApplicationCCE,
					}, nil).Return(nil),
					stsclient.EXPECT().NewSignOption(ctx, "account-id").Return(nil),
					openApiBCCClient.EXPECT().BindTags(ctx, "instance-id", tags, nil).Return(nil),
				)

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bccclient,
					cluster:          cluster,
					instance:         instance,
					tagclient:        tagclient,
					openApiBBCClient: openApiBBCClient,
					logictagClient:   logicclient,
					openApiBCCClient: openApiBCCClient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantExist: true,
			wantAbstract: &MachineAbstract{
				Ready: true,
				Machine: ccetypes.Machine{
					InstanceID:    "instance-id",
					InstanceUUID:  "instance-uuid",
					OrderID:       "order-id",
					VPCIP:         "vpc-ip",
					FloatingIP:    "floating-ip",
					MachineStatus: logicbcc.ServerStatusActive,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			defer tt.fields.ctl.Finish()

			c := &ExistedLogicBBCClient{
				stsclient:        tt.fields.stsclient,
				bbcclient:        tt.fields.bbcclient,
				openApiBBCClient: tt.fields.openApiBBCClient,
				logictagClient:   tt.fields.logictagClient,
				existedBCCClient: &ExistedLogicBCCClient{
					stsclient:        tt.fields.stsclient,
					bccclient:        tt.fields.bbcclient,
					tagclient:        tt.fields.tagclient,
					openApiBCCClient: tt.fields.openApiBCCClient,
				},
			}

			gotExist, gotAbstract, err := c.GetMachine(tt.args.ctx, tt.fields.cluster, tt.fields.instance)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("GetMachine() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("GetMachine() failed, wantErr=true got=false")
				return
			}

			if gotExist != tt.wantExist {
				t.Errorf("GetMachine() got = %v, want %v", gotExist, tt.wantExist)
			}

			if !reflect.DeepEqual(gotAbstract, tt.wantAbstract) {
				t.Errorf("Got: %v", utils.ToJSON(gotAbstract))
				t.Errorf("Want: %v", utils.ToJSON(tt.wantAbstract))
				t.Errorf("GetMachine() gotAbstract = %v, wantAbstract = %v", gotAbstract, tt.wantAbstract)
			}
		})
	}
}

func genBBCInstance(accountID, instanceID string) *ccev1.Instance {
	instance := ccev1.Instance{
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID: "instance-uuid",
			InstanceName:  "instance-name",
			InstanceResource: ccetypes.InstanceResource{
				CPU:           4,
				MEM:           8,
				RootDiskSize:  40,
				LocalDiskSize: 0,
			},
			AdminPassword: "admin-password",
			ImageUUID:     "image-uuid",
			VPCConfig: ccetypes.VPCConfig{
				AvailableZone:     "zoneA",
				VPCSubnetUUID:     "subnet-uuid",
				SecurityGroupUUID: "securityGroup-uuid",
			},
			AccountID: accountID,
		},
		Status: ccetypes.InstanceStatus{
			Machine: ccetypes.Machine{
				OrderID:    "orderID",
				InstanceID: instanceID,
			},
		},
	}
	return &instance
}

func TestNewExistedLogicBBCClient(t *testing.T) {
	type args struct {
		ctx    context.Context
		config *provider.Config
	}
	tests := []struct {
		name    string
		args    args
		want    Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常初始化",
			args: args{
				ctx: context.TODO(),
				config: &provider.Config{
					STSEndpoint:      "sts endpoint",
					IAMEndpoint:      "iam endpoint",
					BCCLogicEndpoint: "bcc logic endpoint",
					Region:           "region",
					Timeout:          10,
				},
			},
			wantErr: false,
		},
		{
			name: "初始化异常",
			args: args{
				ctx: context.TODO(),
				config: &provider.Config{
					STSEndpoint:      "sts endpoint",
					BCCLogicEndpoint: "bcc logic endpoint",
					Region:           "region",
					Timeout:          10,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := gomonkey.ApplyFuncReturn(order.NewOrderClient, &order.Client{})
			defer p.Reset()
			_, err := NewExistedLogicBBCClient(tt.args.ctx, tt.args.config)
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("NewExistedLogicBBCClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("NewExistedLogicBBCClient() failed, wantErr=true got=false")
				return
			}

		})
	}
}

func TestExistedLogicBBCClient_CreateMachine(t *testing.T) {
	type fields struct {
		ctl              *gomock.Controller
		stsclient        sts.Interface
		bbcclient        logicbcc.Interface
		openApiBBCClient bbc.Interface
		openApiBCCClient bcc.Interface
		cluster          *ccev1.Cluster
		instance         *ccev1.Instance
	}
	type args struct {
		ctx context.Context
	}

	tests := []struct {
		name                   string
		fields                 fields
		args                   args
		wantOrderID            string
		wantInstanceID         string
		wantInstanceUUID       string
		wantReinstallOSAlready bool
		wantErr                bool
	}{
		{
			name: "BBC机器需要重装且成功",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bbcclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := true
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ImageID:       "image-id",
						AdminPassword: "Jr56cWp9nGKw8jFsHpy6iuexrzeEaK9CGtyc395v9Lo=",
						AccountID:     "account-id",
						Existed:       true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
						BBCOption: ccetypes.BBCOption{
							ReserveData: false,
							RaidID:      "raid-1",
						},
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
						},
					},
				}

				gomock.InOrder(
					stsclient.EXPECT().GetCredential(ctx, "account-id").Return(&bcests.Credential{
						AccessKeyID:     "ak",
						SecretAccessKey: "sk",
						SessionToken:    "token",
						Token: bcests.Token{
							ID: "token-id",
						},
					}, nil),
					openApiBBCClient.EXPECT().BatchRebuildInstances(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bbcclient,
					openApiBBCClient: openApiBBCClient,
					openApiBCCClient: openApiBCCClient,
					instance:         instance,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantOrderID:            "",
			wantInstanceID:         "instance-id",
			wantInstanceUUID:       "",
			wantReinstallOSAlready: true,
			wantErr:                false,
		},
		{
			name: "BBC机器需要重装且成功",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bbcclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := true
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ImageID:       "image-id",
						AdminPassword: "Jr56cWp9nGKw8jFsHpy6iuexrzeEaK9CGtyc395v9Lo=",
						AccountID:     "4058c10b1da14a558d8a5adbb30c9744",
						Existed:       true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
						BBCOption: ccetypes.BBCOption{
							ReserveData: false,
							Flavor:      "GPU-GTX3090-04S",
							RaidID:      "raid-1",
						},
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
						},
					},
				}

				gomock.InOrder(
					stsclient.EXPECT().GetCredential(ctx, gomock.Any()).Return(&bcests.Credential{
						AccessKeyID:     "ak",
						SecretAccessKey: "sk",
						SessionToken:    "token",
						Token: bcests.Token{
							ID: "token-id",
						},
					}, nil),
					openApiBBCClient.EXPECT().BatchRebuildInstances(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bbcclient,
					openApiBBCClient: openApiBBCClient,
					openApiBCCClient: openApiBCCClient,
					instance:         instance,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantOrderID:            "",
			wantInstanceID:         "instance-id",
			wantInstanceUUID:       "",
			wantReinstallOSAlready: true,
			wantErr:                false,
		},

		{
			name: "BBC机器需要重装且但缺少RaidID字段",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bbcclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := true
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ImageID:       "image-id",
						AdminPassword: "Jr56cWp9nGKw8jFsHpy6iuexrzeEaK9CGtyc395v9Lo=",
						AccountID:     "account-id",
						Existed:       true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
						BBCOption: ccetypes.BBCOption{
							ReserveData: false,
						},
						InstanceResource: ccetypes.InstanceResource{
							RootDiskSize: 100,
						},
					},
				}

				gomock.InOrder(
					stsclient.EXPECT().GetCredential(ctx, "account-id").Return(&bcests.Credential{
						AccessKeyID:     "ak",
						SecretAccessKey: "sk",
						SessionToken:    "token",
						Token: bcests.Token{
							ID: "token-id",
						},
					}, nil),
					openApiBBCClient.EXPECT().BatchRebuildInstances(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bbcclient,
					openApiBBCClient: openApiBBCClient,
					openApiBCCClient: openApiBCCClient,
					instance:         instance,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantOrderID:            "",
			wantInstanceID:         "instance-id",
			wantInstanceUUID:       "",
			wantReinstallOSAlready: true,
			wantErr:                true,
		},
		{
			name: "BBC机器需要重装且但缺少RootDiskSize字段",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bbcclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := true
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ImageID:       "image-id",
						AdminPassword: "Jr56cWp9nGKw8jFsHpy6iuexrzeEaK9CGtyc395v9Lo=",
						AccountID:     "account-id",
						Existed:       true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
						BBCOption: ccetypes.BBCOption{
							ReserveData: false,
							RaidID:      "raid-1",
						},
						InstanceResource: ccetypes.InstanceResource{},
					},
				}

				gomock.InOrder(
					stsclient.EXPECT().GetCredential(ctx, "account-id").Return(&bcests.Credential{
						AccessKeyID:     "ak",
						SecretAccessKey: "sk",
						SessionToken:    "token",
						Token: bcests.Token{
							ID: "token-id",
						},
					}, nil),
					openApiBBCClient.EXPECT().BatchRebuildInstances(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil),
				)

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bbcclient,
					openApiBBCClient: openApiBBCClient,
					openApiBCCClient: openApiBCCClient,
					instance:         instance,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantOrderID:            "",
			wantInstanceID:         "instance-id",
			wantInstanceUUID:       "",
			wantReinstallOSAlready: true,
			wantErr:                true,
		},

		{
			name: "机器已重装, 不发起重装",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bccclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := true
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ImageUUID:     "image-uuid",
						AdminPassword: "Jr56cWp9nGKw8jFsHpy6iuexrzeEaK9CGtyc395v9Lo=",
						AccountID:     "account-id",
						Existed:       true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
					},
					Status: ccetypes.InstanceStatus{
						ReinstallOSAlready: true,
					},
				}

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bccclient,
					openApiBBCClient: openApiBBCClient,
					openApiBCCClient: openApiBCCClient,
					instance:         instance,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantOrderID:            "",
			wantInstanceID:         "instance-id",
			wantInstanceUUID:       "",
			wantReinstallOSAlready: true,
			wantErr:                false,
		},
		{
			name: "机器不需要重装, 不发起重装",
			fields: func() fields {
				ctl := gomock.NewController(t)

				stsclient := stsmock.NewMockInterface(ctl)
				bccclient := logicbccmock.NewMockInterface(ctl)
				openApiBBCClient := bbcmock.NewMockInterface(ctl)
				openApiBCCClient := bccmock.NewMockInterface(ctl)

				rebuild := false
				instance := &ccev1.Instance{
					Spec: ccetypes.InstanceSpec{
						ImageUUID:     "image-uuid",
						AdminPassword: "Jr56cWp9nGKw8jFsHpy6iuexrzeEaK9CGtyc395v9Lo=",
						AccountID:     "account-id",
						Existed:       true,
						ExistedOption: ccetypes.ExistedOption{
							Rebuild:           &(rebuild),
							ExistedInstanceID: "instance-id",
						},
					},
				}

				return fields{
					ctl:              ctl,
					stsclient:        stsclient,
					bbcclient:        bccclient,
					openApiBBCClient: openApiBBCClient,
					openApiBCCClient: openApiBCCClient,
					instance:         instance,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantOrderID:            "",
			wantInstanceID:         "instance-id",
			wantInstanceUUID:       "",
			wantReinstallOSAlready: true,
			wantErr:                false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ExistedLogicBBCClient{
				stsclient:        tt.fields.stsclient,
				bbcclient:        tt.fields.bbcclient,
				openApiBBCClient: tt.fields.openApiBBCClient,
				existedBCCClient: &ExistedLogicBCCClient{
					stsclient:        tt.fields.stsclient,
					bccclient:        tt.fields.bbcclient,
					openApiBCCClient: tt.fields.openApiBCCClient,
				},
				config: &provider.Config{
					ServiceAccessKeySecret: "xxx-xx",
				},
			}

			gotOrderID, gotInstanceID, gotInstanceUUID, err := c.CreateMachine(tt.args.ctx, tt.fields.cluster, tt.fields.instance, "")
			if err != nil {
				if tt.wantErr == true {
					return
				}

				t.Errorf("ExistedLogicBBCClient.CreateMachine() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.wantErr == true {
				t.Errorf("ExistedLogicBBCClient.CreateMachine() failed, wantErr=true got=false")
				return
			}

			if gotOrderID != tt.wantOrderID {
				t.Errorf("ExistedLogicBBCClient.CreateMachine() gotOrderID = %v, wantOrderID %v",
					gotOrderID, tt.wantOrderID)
			}

			if gotInstanceID != tt.wantInstanceID {
				t.Errorf("ExistedLogicBBCClient.CreateMachine() gotInstanceID = %v, wantInstanceID %v",
					gotInstanceID, tt.wantInstanceID)
			}

			if gotInstanceUUID != tt.wantInstanceUUID {
				t.Errorf("ExistedLogicBBCClient.CreateMachine() gotInstanceUUID = %v, wantInstanceUUID %v",
					gotInstanceUUID, tt.wantInstanceUUID)
			}

			if tt.fields.instance.Status.ReinstallOSAlready != tt.wantReinstallOSAlready {
				t.Errorf("ExistedLogicBBCClient.CreateMachine() Status.InfrastructureReady = %v, wantReinstallOSAlready %v",
					tt.fields.instance.Status.InfrastructureReady, tt.wantReinstallOSAlready)
			}
		})
	}
}

func TestExistedLogicBBCClient_BindInstanceRole(t *testing.T) {
	type fields struct {
		stsclient        sts.Interface
		bbcclient        logicbcc.Interface
		openApiBBCClient bbc.Interface
		logictagClient   logictag.Interface
		existedBCCClient *ExistedLogicBCCClient
		region           string
		config           *provider.Config
	}
	type args struct {
		ctx         context.Context
		roleName    string
		accountID   string
		instanceIDs []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "test",
			fields:  fields{},
			args:    args{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ExistedLogicBBCClient{
				stsclient:        tt.fields.stsclient,
				bbcclient:        tt.fields.bbcclient,
				openApiBBCClient: tt.fields.openApiBBCClient,
				logictagClient:   tt.fields.logictagClient,
				existedBCCClient: tt.fields.existedBCCClient,
				region:           tt.fields.region,
				config:           tt.fields.config,
			}
			if err := c.BindInstanceRole(tt.args.ctx, tt.args.roleName, tt.args.accountID, tt.args.instanceIDs...); (err != nil) != tt.wantErr {
				t.Errorf("BindInstanceRole() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestExistedLogicBBCClient_BindInstanceRole1(t *testing.T) {
	type fields struct {
		stsclient        sts.Interface
		bbcclient        logicbcc.Interface
		openApiBBCClient bbc.Interface
		logictagClient   logictag.Interface
		existedBCCClient *ExistedLogicBCCClient
		region           string
		config           *provider.Config
	}
	type args struct {
		ctx         context.Context
		roleName    string
		accountID   string
		instanceIDs []string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name:    "test",
			fields:  fields{},
			args:    args{},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &ExistedLogicBBCClient{
				stsclient:        tt.fields.stsclient,
				bbcclient:        tt.fields.bbcclient,
				openApiBBCClient: tt.fields.openApiBBCClient,
				logictagClient:   tt.fields.logictagClient,
				existedBCCClient: tt.fields.existedBCCClient,
				region:           tt.fields.region,
				config:           tt.fields.config,
			}
			if err := c.BindInstanceRole(tt.args.ctx, tt.args.roleName, tt.args.accountID, tt.args.instanceIDs...); (err != nil) != tt.wantErr {
				t.Errorf("BindInstanceRole() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_ExistedLogicBBCClient_tags(t *testing.T) {
	c := &ExistedLogicBBCClient{}
	err := c.BindInstanceTags(context.Background(), []tag.Tag{}, "123", "123")
	assert.Nil(t, err)

	err = c.UnbindInstanceTags(context.Background(), []tag.Tag{}, "123", "123")
	assert.Nil(t, err)
}
