package main

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/spf13/cobra"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/uuid"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	v1core "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/client-go/tools/record"
	"k8s.io/component-base/cli/globalflag"
	"k8s.io/component-base/logs"
	"k8s.io/klog"
	"k8s.io/kubernetes/pkg/api/legacyscheme"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/controller"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/metrics"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/options"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/signals"
	_ "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/util/logger"
)

func main() {
	// 初始化日志配置
	logs.InitLogs()
	defer logs.FlushLogs()

	// 初始化默认 App 启动参数
	lbControllerOption, err := options.NewLBControllerOptions()
	if err != nil {
		klog.Fatalf("Failed to initialize command options: %v", err)
	}

	// 初始化 Signal 处理函数
	stopCh := signals.SetupSignalHandler()

	command := &cobra.Command{
		Use:  "cce-lb-controller",
		Long: `cce-lb-controller manages LoadBalancer Services in Kubernestes Cluster.`,
		Run: func(cmd *cobra.Command, args []string) {
			// 校验 App 启动参数并将其转化成 Controller 所需的配置
			lbControllerConfig, err := lbControllerOption.Config()
			if err != nil {
				fmt.Fprintf(os.Stderr, "Config LB Controller Config Fail: %v\n", err)
				os.Exit(1)
			}

			// 初始化监控指标收集
			reg := prometheus.NewRegistry()
			mc := metrics.NewDummyCollector()
			if lbControllerConfig.EnableMetrics {
				mc, err = metrics.NewMetricsCollector(reg,
					lbControllerConfig.SharedInformers.Core().V1().Services().Lister(),
					lbControllerConfig.MetricsCollectGapInSeconds)
				if err != nil {
					klog.Fatalf("Error creating prometheus collector:  %v", err)
				}
			}
			mc.Start()

			// 启动Metrics Server
			mux := http.NewServeMux()
			registerMetrics(reg, mux)
			go startHTTPServer(lbControllerConfig.MetricsPort, mux)

			// 启动 Controller
			if err := Run(lbControllerConfig, stopCh, mc); err != nil {
				fmt.Fprintf(os.Stderr, "Run LB Controller Fail: %v\n", err)
				os.Exit(1)
			}
		},
	}

	// 为 App 设置启动参数列表
	namedFlagSets := lbControllerOption.Flags()
	globalflag.AddGlobalFlags(namedFlagSets.FlagSet("global"), command.Name())
	for _, f := range namedFlagSets.FlagSets {
		command.Flags().AddFlagSet(f)
	}

	// 启动 App
	if err := command.Execute(); err != nil {
		fmt.Fprintf(os.Stderr, "LB Controller execute error: %v\n", err)
		os.Exit(1)
	}

	fmt.Fprintf(os.Stderr, "LB Controller execute complete")
}

func Run(config *config.LBControllerConfig, stopCh <-chan struct{}, metricsCollector metrics.MetricsCollector) error {
	config.Cloud.BceCloud.SetMetricsCollector(metricsCollector)
	// 设置 LB Controller 相关参数并初始化
	lbController, err := controller.NewLBController(
		config,
		metricsCollector,
	)
	if err != nil {
		klog.Fatalf("InitAppFail: Error NewLBController: %s", err.Error())
		return err
	}

	// 获取当前集群的 KubeClient 作为 LeaderElectionClient
	leaderElectionClient, err := getLeaderElectionKubeClient()
	if err != nil {
		klog.Fatalf("InitAppFail: Error NewForConfig: %s", err.Error())
		return err
	}

	// 获取当前组件 Leader Election ID
	id, err := getLeaderElectionID()
	if err != nil {
		return err
	}

	// 获取 Event Record
	leaderEventRecorder := getLeaderElectionEventRecorder(leaderElectionClient)

	// Lock required for leader election
	rl, err := resourcelock.New(resourcelock.ConfigMapsResourceLock,
		"kube-system",
		"cce-lb-controller",
		leaderElectionClient.CoreV1(),
		leaderElectionClient.CoordinationV1(),
		resourcelock.ResourceLockConfig{
			Identity:      id,
			EventRecorder: leaderEventRecorder,
		})
	if err != nil {
		klog.Fatalf("error creating lock: %v", err)
	}

	// Try and become the leader and start cloud controller manager loops
	leaderelection.RunOrDie(context.TODO(), leaderelection.LeaderElectionConfig{
		Lock:          rl,
		LeaseDuration: 15 * time.Second,
		RenewDeadline: 10 * time.Second,
		RetryPeriod:   2 * time.Second,
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				// 启动 Informers
				// Informers 的 Start 和 WaitForCacheSync 是非阻塞式的，需要在 Controller Run 之前执行
				config.SharedInformers.Start(stopCh)
				config.SharedInformers.WaitForCacheSync(wait.NeverStop)

				// 启动 LB Controller
				klog.Infof("AppStart: CCE LB Controller Start")
				if err = lbController.Run(stopCh); err != nil {
					klog.Fatalf("AppFail: Error running controller: %s", err.Error())
				}
			},
			OnStoppedLeading: func() {
				klog.Fatalf("leader election lost")
			},
		},
		Name: "cce-lb-controller",
	})

	return fmt.Errorf("unreachable")
}

func startHTTPServer(port int, mux *http.ServeMux) {
	klog.Infof("Start Metrics")
	server := &http.Server{
		Addr:              fmt.Sprintf(":%v", port),
		Handler:           mux,
		ReadTimeout:       10 * time.Second,
		ReadHeaderTimeout: 10 * time.Second,
		WriteTimeout:      300 * time.Second,
		IdleTimeout:       120 * time.Second,
	}
	klog.Fatal(server.ListenAndServe())
}

func registerMetrics(reg *prometheus.Registry, mux *http.ServeMux) {
	mux.Handle(
		"/metrics",
		promhttp.InstrumentMetricHandler(
			reg,
			promhttp.HandlerFor(reg, promhttp.HandlerOpts{}),
		),
	)
}

func getLeaderElectionEventRecorder(leaderElectionClient kubernetes.Interface) record.EventRecorder {
	eventBroadcaster := record.NewBroadcaster()
	eventBroadcaster.StartLogging(klog.Infof)
	eventBroadcaster.StartRecordingToSink(&v1core.EventSinkImpl{Interface: leaderElectionClient.CoreV1().Events("")})
	leaderEventRecorder := eventBroadcaster.NewRecorder(legacyscheme.Scheme, v1.EventSource{Component: "cce-lb-controller"})

	return leaderEventRecorder
}

func getLeaderElectionID() (string, error) {
	id, err := os.Hostname()
	if err != nil {
		return "", err
	}
	id = id + "_" + string(uuid.NewUUID())

	return id, nil
}

func getLeaderElectionKubeClient() (kubernetes.Interface, error) {
	cfg, err := clientcmd.BuildConfigFromFlags("", "")
	if err != nil {
		klog.Fatalf("InitAppFail: Error BuildConfigFromFlags: %s", err.Error())
		return nil, err
	}
	kubeClient, err := kubernetes.NewForConfig(cfg)
	if err != nil {
		klog.Fatalf("InitAppFail: Error NewForConfig: %s", err.Error())
		return nil, err
	}

	return kubeClient, nil
}
