apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-cni-node-agent
  namespace: kube-system
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cce-cni-node-agent
  namespace: kube-system
rules:
  - apiGroups: [""]
    resources:
      ["pods", "nodes", "namespaces", "configmaps", "serviceaccounts", "events", "pods/status", "nodes/status"]
    verbs: ["get", "watch", "list", "update", "create", "patch"]
  - apiGroups: ["apps"]
    resources:
      ["statefulsets", "deployments", "replicasets"]
    verbs: ["get", "watch", "list", "update", "create", "patch"]
  - apiGroups: ["cce.io"]
    resources:
      ["workloadendpoints", "ippools", "ippools/status"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cce-cni-node-agent
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-cni-node-agent
subjects:
  - kind: ServiceAccount
    name: cce-cni-node-agent
    namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cce-cni-node-agent
  namespace: kube-system
  labels:
    k8s-app: cce-cni-node-agent
spec:
  selector:
    matchLabels:
      name: cce-cni-node-agent
  template:
    metadata:
      labels:
        name: cce-cni-node-agent
      annotations:
        scheduler.alpha.kubernetes.io/critical-pod: ""
    spec:
      nodeSelector:
        beta.kubernetes.io/arch: amd64
      tolerations:
        - operator: "Exists"
      serviceAccountName: cce-cni-node-agent
      hostNetwork: true
      priorityClassName: system-node-critical
      initContainers:
        - name: install-cce-cni-binary
          image: registry.baidubce.com/jpaas-public/cce-vpc-route-cni:test
          imagePullPolicy: Always
          volumeMounts:
            - name: cni-bin-dir
              mountPath: /host/opt/cni/bin
      containers:
        - name: cce-cni-node-agent
          image: registry.baidubce.com/jpaas-public/cce-cni-node-agent:test
          command:
            - /bin/cni-node-agent
            - --cni-mode=vpc-cni
            - --cluster-id=c-EjHcOaKs
            - --eni-subnet-list=sbn-sceqbzr0m5e6,sbn-gpb9hv6jiyrb
            - --eni-security-group-list=g-8tam8t4udqec
            - --logtostderr=false
            - --alsologtostderr=true
            - --log-file=/var/log/cce/cce-cni-node-agent.log
            - --resync-period=15s
          imagePullPolicy: Always
          securityContext:
            privileged: true
          volumeMounts:
            - name: log-dir
              mountPath: /var/log/
            - name: cce-plugin-token
              mountPath: /var/run/secrets/cce/cce-plugin-token
              readOnly: true
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
      terminationGracePeriodSeconds: 0
      volumes:
        - name: cni-bin-dir
          hostPath:
            path: /opt/cni/bin
        - name: cni-net-dir
          hostPath:
            path: /etc/cni/net.d
        - name: log-dir
          hostPath:
            path: /var/log/
            type: "Directory"
        - name: cce-plugin-token
          secret:
            defaultMode: 0400
            secretName: cce-plugin-token
        - name: lib-modules
          hostPath:
            path: /lib/modules/

