#!/bin/bash

if [ $# -ne 1 ]; then
    echo "USAGE: ${0} tag"
    exit -1
fi

tag=${1}


CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/cce-ipam ../cmd/eni-ipam && \
docker build -t registry.baidubce.com/cce-plugin-pro/cce-vpc-cni-ipam:${tag} -f Dockerfile.eniipam . && \
docker push registry.baidubce.com/cce-plugin-pro/cce-vpc-cni-ipam:${tag}

CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/cni-node-agent ../cmd/node-agent && \
docker build -t registry.baidubce.com/cce-plugin-pro/cce-cni-node-agent:${tag} -f Dockerfile.nodeagent . && \
docker push registry.baidubce.com/cce-plugin-pro/cce-cni-node-agent:${tag}