apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-vpc-cni
  namespace: kube-system
---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cce-vpc-cni
  namespace: kube-system
rules:
  - apiGroups: [""]
    resources:
      ["pods", "nodes", "namespaces", "configmaps", "serviceaccounts", "events", "pods/status", "nodes/status"]
    verbs: ["get", "watch", "list", "update", "create", "patch"]
  - apiGroups: ["apps"]
    resources:
      ["statefulsets", "deployments", "replicasets"]
    verbs: ["get", "watch", "list", "update", "create", "patch"]
  - apiGroups: ["cce.io"]
    resources:
      ["workloadendpoints"]
    verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cce-vpc-cni-binding
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-vpc-cni
subjects:
  - kind: ServiceAccount
    name: cce-vpc-cni
    namespace: kube-system
---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
  name: cce-ip-masq-agent
  namespace: kube-system
data:
  config: |
    nonMasqueradeCIDRs:
      - 10.0.0.0/8
      - **********/12
      - ***********/16
    masqOutBound: false
    masqOutBoundIPv6: false
    masqLinkLocal: false
    masqLinkLocalIPv6: false
    resyncInterval: 60s
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: cce-vpc-cni
  namespace: kube-system
  labels:
    k8s-app: cce-vpc-cni
spec:
  selector:
    matchLabels:
      name: cce-vpc-cni
  template:
    metadata:
      labels:
        name: cce-vpc-cni
      annotations:
        scheduler.alpha.kubernetes.io/critical-pod: ""
    spec:
      nodeSelector:
        beta.kubernetes.io/arch: amd64
      tolerations:
        - operator: "Exists"
      serviceAccountName: cce-vpc-cni
      hostNetwork: true
      priorityClassName: system-node-critical
      initContainers:
        - name: install-cce-cni-binary
          image: registry.baidubce.com/jpaas-public/cce-vpc-route-cni:test
          imagePullPolicy: Always
          volumeMounts:
            - name: cni-bin-dir
              mountPath: /host/opt/cni/bin
      containers:
        - name: cce-ip-masq-agent
          image: registry.baidubce.com/jpaas-public/cce-ip-masq-agent:v1.0.0
          imagePullPolicy: Always
          securityContext:
            privileged: false
            capabilities:
              add: ["NET_ADMIN", "NET_RAW"]
          volumeMounts:
            - name: cce-ip-masq-agent-config
              mountPath: /etc/config
            - name: cni-net-dir
              mountPath: /etc/cni/net.d
        #  install cni.conf
        - name: install-cce-cni-conf
          image: registry.baidubce.com/jpaas-public/cce-vpc-cni-install-conf:test
          imagePullPolicy: Always
          env:
            - name: EndPoint
              value: "**************"          # cce-eni-ipam svc + port
          volumeMounts:
            - name: cni-net-dir
              mountPath: /etc/cni/net.d
      terminationGracePeriodSeconds: 30
      volumes:
        - name: cni-bin-dir
          hostPath:
            path: /opt/cni/bin
        - name: cni-net-dir
          hostPath:
            path: /etc/cni/net.d
        - name: cce-vpc-cni-log-dir
          hostPath:
            path: /var/log/
            type: "Directory"
        - name: cce-ip-masq-agent-config
          configMap:
            # Note this ConfigMap must be created in the same namespace as the daemon pods - this spec uses kube-system
            name: cce-ip-masq-agent
            optional: true
            items:
              # The daemon looks for its config in a YAML file at /etc/config/ip-masq-agent
              - key: config
                path: ip-masq-agent
        - name: cloud-config
          hostPath:
            path: /etc/kubernetes/cloud.config
        - name: lib-modules
          hostPath:
            path: /lib/modules/
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: cce-eni-ipam
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cce-eni-ipam
  template:
    metadata:
      labels:
        app: cce-eni-ipam
    spec:
      priorityClassName: system-node-critical
      nodeSelector:
        cce.io/eni-ipam: "true"
      hostNetwork: true
      serviceAccountName: cce-vpc-cni
      containers:
        - name: cce-eni-ipam
          image: registry.baidubce.com/jpaas-public/cce-vpc-cni-ipam:test
          imagePullPolicy: Always
          securityContext:
            privileged: true
          volumeMounts:
            - name: log-dir
              mountPath: /var/log/
            - name: cce-plugin-token
              mountPath: /var/run/secrets/cce/cce-plugin-token
              readOnly: true
          # MODIFY ME:
          command:
            - /bin/cce-ipam
            - --cluster-id=c-4CSRFNH6
            - --region=fwh
            - --vpcid=vpc-vqgcn7jcdvc0
            - --eni-sync-period=20s
            - --sts-gc-period=20s
            - --http-port=9999
            - --logtostderr=false
            - --alsologtostderr=true
            - --log-file=/var/log/cce/cce-eni-ipam.log
          ports:
            - name: http
              containerPort: 9999
      volumes:
        - name: log-dir
          hostPath:
            path: /var/log/
            type: "Directory"
        - name: cce-plugin-token
          secret:
            defaultMode: 0400
            secretName: cce-plugin-token
---
kind: Service
apiVersion: v1
metadata:
  name: cce-eni-ipam
  namespace: kube-system
spec:
  type: ClusterIP
  selector:
    app: cce-eni-ipam
  ports:
    - port: 80
      targetPort: 9999