#!/bin/sh


CNI_FILE=/etc/cni/net.d/00-vpc-cni.conflist

while true
do
  if [ ! -f $CNI_FILE ]; then
    echo "File $CNI_FILE not exists."

    cat > $CNI_FILE << EOF
{
    "name":"cce-vpc-route-cni",
    "cniVersion":"0.3.1",
    "plugins":[
        {
            "type":"ptp",
            "enableARPProxy":true,
            "vethPrefix":"veth",
            "ipam":{
                "type":"eni-ipam",
                "endpoint": "##ENDPOINT##",
                "deleteENIScopeLinkRoute": true
            }
        }
    ]
}
EOF
    sed -i "s/##ENDPOINT##/${EndPoint}/" $CNI_FILE
  fi

  sleep 10
done
