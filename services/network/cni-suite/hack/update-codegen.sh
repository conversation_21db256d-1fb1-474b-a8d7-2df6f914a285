set -o errexit
set -o nounset
set -o pipefail

# generate the code with:
# --output-base    because this script should also be able to run inside the vendor dir of
#                  k8s.io/kubernetes. The output-base is needed for the generators to output into the vendor dir
#                  instead of the $GOPATH directly. For normal projects this can be dropped.
#
 
#run "go mod vendor && chmod -R 777 vendor" in cce-stack
#run "cd hack && ./update-codegen.sh"
bash ../../../../vendor/k8s.io/code-generator/generate-groups.sh "deepcopy,client,informer,lister" \
icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated \
icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis \
"networking:v1alpha1" \
--go-header-file $(pwd)/boilerplate.go.txt