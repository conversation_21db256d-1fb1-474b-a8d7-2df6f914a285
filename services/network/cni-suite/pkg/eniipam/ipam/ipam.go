package ipam

import (
	"context"
	goerrors "errors"
	"fmt"
	"time"

	"github.com/juju/ratelimit"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	v1core "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/util/retry"

	enisdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/util"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	crdinformers "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/informers/externalversions"
	nodeagentutil "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/nodeagent/util"
	k8sutil "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/k8s"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

const (
	// minPrivateIPLifeTime is the life time of a private ip (from allocation to release), aim to trade off db slave delay
	minPrivateIPLifeTime = 3 * time.Second

	rateLimitErrorSleepPeriod  = time.Millisecond * 800
	rateLimitErrorJitterFactor = 5
)

func (ipam *IPAM) Allocate(ctx context.Context, name, namespace, containerID string) (*v1alpha1.WorkloadEndpoint, error) {
	log.Infof(ctx, "[Allocate] allocating IP for pod (%v %v) starts", namespace, name)
	defer log.Infof(ctx, "[Allocate] allocating IP for pod (%v %v) ends", namespace, name)

	if !ipam.Ready(ctx) {
		log.Warningf(ctx, "ipam has not synced cache yet")
		return nil, fmt.Errorf("ipam has not synced cache yet")
	}

	var ipResult = ""
	pod, err := ipam.ifactory.Core().V1().Pods().Lister().Pods(namespace).Get(name)
	if err != nil {
		return nil, err
	}

	log.Infof(ctx, "pod (%v %v) with containerID %v is scheduled to node %v", namespace, name, containerID, pod.Spec.NodeName)

	// 测试结果：
	// 全局的 lock：批量起 100 个 pod 需要 140s；批量起 50 个 pod 需要 75s；
	// 细粒度 lock：批量起 100 个 pod 需要 80s； 批量起 50 个 pod 需要 45s；

	// choose which eni to bind
	eni, err := ipam.findSuitableENI(ctx, pod)
	if err != nil {
		log.Errorf(ctx, "failed to find a suitable eni for pod (%v %v): %v", namespace, name, err)
		return nil, err
	}

	wep, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().WorkloadEndpoints(namespace).Get(name)
	if err == nil {
		ipToAllocate := wep.Spec.IP
		if !isFixIPStatefulSetPod(pod) {
			log.Warningf(ctx, "pod (%v %v) still has wep, but is not a fix-ip sts pod", namespace, name)
			ipToAllocate = ""
		}
		log.Infof(ctx, "try to reuse fix IP %v for pod (%v %v)", ipToAllocate, namespace, name)
		// Note: here DeletePrivateIP and AddPrivateIP should be atomic. we leverage a lock to do this
		// ensure private ip not attached to eni
		log.Infof(ctx, "try to delete IP %v from %v", wep.Spec.IP, wep.Spec.ENIID)
		ipam.lock.Lock()
		ipam.bucket.Wait(1)
		if err := ipam.cloud.DeletePrivateIP(ctx, wep.Spec.IP, wep.Spec.ENIID); err != nil && !cloud.IsErrorENIPrivateIPNotFound(err) {
			log.Errorf(ctx, "error delete private IP %v for pod (%v %v): %v", wep.Spec.IP, namespace, name, err)
			if cloud.IsErrorRateLimit(err) {
				time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
			}
		}
		// TODO:
		allocIPMaxTry := 3
		for i := 0; i < allocIPMaxTry; i++ {
			ipam.bucket.Wait(1)
			log.Infof(ctx, "try to add IP %v to %v for pod (%v %v)", ipToAllocate, eni.ENIID, namespace, name)
			ipResult, err = ipam.cloud.AddPrivateIP(ctx, ipToAllocate, eni.ENIID)
			if err != nil {
				log.Errorf(ctx, "error add private IP %v for pod (%v %v): %v", ipToAllocate, namespace, name, err)
				if cloud.IsErrorRateLimit(err) {
					time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
				}
				if cloud.IsErrorPrivateIPInUse(err) {
					log.Warningf(ctx, "fix ip %v has been mistakenly allocated to somewhere else for pod (%v %v)", ipToAllocate, namespace, name)
					ipToAllocate = ""
					continue
				}
				ipam.lock.Unlock()
				return nil, err
			} else if err == nil {
				ipam.lock.Unlock()
				break
			}
		}
		log.Infof(ctx, "add private IP %v for pod (%v %v) successfully", ipResult, namespace, name)
		ipam.lock.Lock()
		ipam.privateIPNumCache[eni.ENIID]++
		ipam.lock.Unlock()

		if k8sutil.IsStatefulSetPod(pod) {
			if wep.Labels == nil {
				wep.Labels = make(map[string]string)
			}
			wep.Labels[OwnerKey] = util.GetStsName(wep)
		}
		wep.Spec.ENIID = eni.ENIID
		wep.Spec.IP = ipResult
		wep.Spec.Mac = eni.MacAddress
		wep.Spec.Node = pod.Spec.NodeName
		wep.Spec.PodUID = string(pod.UID)
		wep.Spec.ContainerID = containerID
		wep.Spec.SubnetID = eni.SubnetID
		wep.Spec.UpdateAt = metav1.Now()
		if pod.Annotations != nil {
			wep.Spec.EnableFixIP = pod.Annotations[StsPodAnnotationEnableFixIP]
			wep.Spec.FixIPDeletePolicy = pod.Annotations[StsPodAnnotationFixIPDeletePolicy]
		}
		_, err = ipam.crdClient.CceV1alpha1().WorkloadEndpoints(namespace).Update(ctx, wep, metav1.UpdateOptions{})
		if err != nil {
			log.Errorf(ctx, "failed to update wep for pod (%v %v): %v", namespace, name, err)
			time.Sleep(minPrivateIPLifeTime)
			if delErr := ipam.cloud.DeletePrivateIP(ctx, ipResult, eni.ENIID); delErr != nil {
				log.Errorf(ctx, "rollback: error deleting private IP %v for pod (%v %v): %v", ipResult, namespace, name, err)
			}
			return nil, err
		}
		log.Infof(ctx, "update wep with spec %+v for pod (%v %v) successfully", wep.Spec, namespace, name)
		ipam.lock.Lock()
		ipam.allocated[ipResult] = wep
		ipam.lock.Unlock()
		return wep, nil
	} else {
		if !errors.IsNotFound(err) {
			log.Errorf(ctx, "failed to get wep of pod (%v %v): %v", pod.Namespace, pod.Name, err)
			return nil, err
		}
	}

	// allocate ip for non-fixip pod
	wep = &v1alpha1.WorkloadEndpoint{}
	wep.Name = pod.Name
	wep.Namespace = pod.Namespace
	wep.Spec.Node = pod.Spec.NodeName
	wep.Spec.Type = PodType
	if k8sutil.IsStatefulSetPod(pod) {
		wep.Spec.Type = StsType
		if wep.Labels == nil {
			wep.Labels = make(map[string]string)
		}
		wep.Labels[OwnerKey] = util.GetStsName(wep)
	}
	wep.Spec.UpdateAt = metav1.Now()
	wep.Spec.ENIID = eni.ENIID
	wep.Spec.Mac = eni.MacAddress
	wep.Spec.PodUID = string(pod.UID)
	wep.Spec.ContainerID = containerID
	wep.Spec.SubnetID = eni.SubnetID
	if pod.Annotations != nil {
		wep.Spec.EnableFixIP = pod.Annotations[StsPodAnnotationEnableFixIP]
		wep.Spec.FixIPDeletePolicy = pod.Annotations[StsPodAnnotationFixIPDeletePolicy]
	}

	log.Infof(ctx, "try to allocate IP and create wep for pod (%v %v)", pod.Namespace, pod.Name)

	allocIPMaxTry := 10
	for i := 0; i < allocIPMaxTry; i++ {
		ipam.bucket.Wait(1)
		ipResult, err = ipam.cloud.AddPrivateIP(ctx, ipResult, eni.ENIID)
		if err != nil {
			log.Errorf(ctx, "failed to add private IP for pod (%v %v): %v", namespace, name, err)
			if cloud.IsErrorRateLimit(err) {
				time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
			}
			return nil, err
		}
		ipam.lock.RLock()
		wep, ok := ipam.allocated[ipResult]
		ipam.lock.RUnlock()
		if !ok {
			break
		}
		log.Warningf(ctx, "IP %s has been allocated to pod (%v %v), will try next...", ipResult, wep.Namespace, wep.Name)
		ipResult = ""
	}

	if ipResult == "" {
		msg := fmt.Sprintf("failed to add private IP for pod (%v %v) after retrying %d times", namespace, name, allocIPMaxTry)
		log.Error(ctx, msg)
		return nil, goerrors.New(msg)
	}

	log.Infof(ctx, "assign private IP %v for pod (%v %v) successfully", ipResult, namespace, name)
	ipam.lock.Lock()
	ipam.privateIPNumCache[eni.ENIID]++
	ipam.lock.Unlock()

	wep.Spec.IP = ipResult
	_, err = ipam.crdClient.CceV1alpha1().WorkloadEndpoints(namespace).Create(ctx, wep, metav1.CreateOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to create wep for pod (%v %v): %v", namespace, name, err)
		ipam.bucket.Wait(1)
		if delErr := ipam.cloud.DeletePrivateIP(ctx, ipResult, eni.ENIID); delErr != nil {
			log.Errorf(ctx, "rollback: error deleting private IP %v for pod (%v %v): %v", ipResult, namespace, name, err)
			if cloud.IsErrorRateLimit(err) {
				time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
			}
		}
		ipam.lock.Lock()
		ipam.privateIPNumCache[eni.ENIID]--
		ipam.lock.Unlock()
		return nil, err
	}
	log.Infof(ctx, "create wep with spec %+v for pod (%v %v) successfully", wep.Spec, namespace, name)

	ipam.lock.Lock()
	ipam.allocated[ipResult] = wep
	ipam.lock.Unlock()
	return wep, nil
}

func (ipam *IPAM) Release(ctx context.Context, name, namespace, containerID string) (*v1alpha1.WorkloadEndpoint, error) {
	log.Infof(ctx, "[Release] releasing IP for pod (%v %v) starts", namespace, name)
	defer log.Infof(ctx, "[Release] releasing IP for pod (%v %v) ends", namespace, name)

	if !ipam.Ready(ctx) {
		log.Warningf(ctx, "ipam has not synced cache yet")
		return nil, fmt.Errorf("ipam has not synced cache yet")
	}

	tmpWep, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().WorkloadEndpoints(namespace).Get(name)
	if err != nil {
		if errors.IsNotFound(err) {
			log.Infof(ctx, "wep of pod (%v %v) not found", namespace, name)
			return nil, nil
		}
		log.Errorf(ctx, "failed to get wep of pod (%v %v): %v", namespace, name, err)
		return nil, err
	}

	// new a wep, avoid data racing
	wep := tmpWep.DeepCopy()

	log.Infof(ctx, "wep (%v %v) containerID: %v, received containerID: %v", namespace, name, wep.Spec.ContainerID, containerID)

	// this may be due to a pod migrate to another node
	if wep.Spec.ContainerID != "" && wep.Spec.ContainerID != containerID {
		log.Warningf(ctx, "pod (%v %v) may have switched to another node, ignore old cleanup", name, namespace)
		return nil, nil
	}

	if wep.Spec.ContainerID == "" {
		log.Warningf(ctx, "containerID of wep (%v %v) is empty, maybe due to old version ipam", name, namespace)
	}

	if isFixIPStatefulSetPodWep(wep) {
		log.Infof(ctx, "release: sts pod (%v %v) will update wep but private IP won't release", namespace, name)
		wep.Spec.UpdateAt = metav1.Now()
		_, err = ipam.crdClient.CceV1alpha1().WorkloadEndpoints(namespace).Update(ctx, wep, metav1.UpdateOptions{})
		if err != nil {
			log.Errorf(ctx, "failed to update sts pod (%v %v) status: %v", namespace, name, err)
		}
		log.Infof(ctx, "release: update wep for sts pod (%v %v) successfully", namespace, name)
		return wep, nil
	}

	// not sts pod, delete eni ip, delete fip crd
	log.Infof(ctx, "try to release private IP %v and wep for non-sts pod (%v %v)", wep.Spec.IP, namespace, name)
	ipam.bucket.Wait(1)
	err = ipam.cloud.DeletePrivateIP(ctx, wep.Spec.IP, wep.Spec.ENIID)
	if err != nil {
		log.Errorf(ctx, "release: error deleting private IP %v for pod (%v %v): %v", wep.Spec.IP, namespace, name, err)
		if cloud.IsErrorRateLimit(err) {
			time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
		}
	} else {
		// ip on eni and delete successfully
		ipam.lock.Lock()
		ipam.privateIPNumCache[wep.Spec.ENIID]--
		ipam.lock.Unlock()
	}
	if err != nil && !ipam.isErrorENIPrivateIPNotFound(err, wep) {
		return nil, err
	}
	log.Infof(ctx, "release private IP for pod (%v %v) successfully", namespace, name)

	// ipam may receive allocate request before release request.
	// For sts pod, wep name will not change.
	// double check to ensure we don't mistakenly delete wep.
	if wep.Spec.ContainerID == "" || wep.Spec.ContainerID == containerID {
		err = ipam.crdClient.CceV1alpha1().WorkloadEndpoints(namespace).Delete(ctx, name, *metav1.NewDeleteOptions(0))
		if err != nil {
			log.Errorf(ctx, "failed to delete wep for pod (%v %v): %v", namespace, name, err)
			return nil, err
		}
		log.Infof(ctx, "release wep for pod (%v %v) successfully", namespace, name)
	}

	ipam.lock.Lock()
	delete(ipam.allocated, wep.Spec.IP)
	ipam.lock.Unlock()
	return wep, nil
}

func (ipam *IPAM) Ready(ctx context.Context) bool {
	return ipam.cacheHasSynced
}

func NewIPAM(
	kubeClient kubernetes.Interface,
	crdClient versioned.Interface,
	bceClient cloud.Interface,
	vpcID string,
	clusterID string,
	defaultResync time.Duration,
	eniSyncPeriod time.Duration,
	stsGCPeriod time.Duration,
	ipMutatingRate float64,
	ipMutatingBurst int64,
) (*IPAM, error) {
	log.Infof(context.TODO(), "limit ip mutating rate to %v, burst to %v", ipMutatingRate, ipMutatingBurst)

	eventBroadcaster := record.NewBroadcaster()
	eventBroadcaster.StartRecordingToSink(&v1core.EventSinkImpl{
		Interface: kubeClient.CoreV1().Events(""),
	})
	recorder := eventBroadcaster.NewRecorder(scheme.Scheme, v1.EventSource{Component: "cce-ipam"})

	ifactory := informers.NewSharedInformerFactory(kubeClient, defaultResync)
	crdInformer := crdinformers.NewSharedInformerFactory(crdClient, defaultResync)

	ipam := &IPAM{
		eventBroadcaster: eventBroadcaster,
		eventRecorder:    recorder,
		ifactory:         ifactory,
		kubeClient:       kubeClient,
		crdInformer:      crdInformer,
		crdClient:        crdClient,
		cloud:            bceClient,
		vpcID:            vpcID,
		clusterID:        clusterID,
		eniSyncPeriod:    eniSyncPeriod,
		stsGCPeriod:      stsGCPeriod,
		eniCache:         make(map[string][]*enisdk.ENI),
		allocated:        make(map[string]*v1alpha1.WorkloadEndpoint),
		bucket:           ratelimit.NewBucketWithRate(ipMutatingRate, ipMutatingBurst),
		cacheHasSynced:   false,
	}
	return ipam, nil
}

func (ipam *IPAM) Run(ctx context.Context, stopCh <-chan struct{}) error {
	defer func() {
		runtime.HandleCrash()
	}()

	log.Info(ctx, "Starting cce ipam controller")
	defer log.Info(ctx, "Shutting down cce ipam controller")

	nodeInformer := ipam.ifactory.Core().V1().Nodes().Informer()
	podInformer := ipam.ifactory.Core().V1().Pods().Informer()
	stsInformer := ipam.ifactory.Apps().V1().StatefulSets().Informer()
	wepInformer := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Informer()
	ippoolInformer := ipam.crdInformer.Cce().V1alpha1().IPPools().Informer()

	ipam.ifactory.Start(stopCh)
	ipam.crdInformer.Start(stopCh)

	if !cache.WaitForNamedCacheSync(
		"cce-ipam",
		stopCh,
		nodeInformer.HasSynced,
		podInformer.HasSynced,
		stsInformer.HasSynced,
		wepInformer.HasSynced,
		ippoolInformer.HasSynced,
	) {
		log.Warning(ctx, "failed WaitForCacheSync, timeout")
		return nil
	} else {
		log.Info(ctx, "WaitForCacheSync done")
	}

	err := ipam.buildAllocatedCache()
	if err != nil {
		return err
	}

	go func() {
		if err := ipam.syncENIInfo(stopCh); err != nil {
			log.Errorf(ctx, "failed to sync eni info: %v", err)
		}
	}()

	ipam.cacheHasSynced = true

	go func() {
		if err := ipam.gc(stopCh); err != nil {
			log.Errorf(ctx, "failed to start statefulset gc: %v", err)
		}
	}()

	<-stopCh
	return nil
}

// findSuitableENI finds suitable eni for pod
func (ipam *IPAM) findSuitableENI(ctx context.Context, pod *v1.Pod) (*enisdk.ENI, error) {
	log.Infof(ctx, "start to find suitable eni for pod (%v/%v)", pod.Namespace, pod.Name)

	ipam.lock.RLock()
	defer ipam.lock.RUnlock()

	nodeName := pod.Spec.NodeName
	ippoolName := nodeagentutil.GetIPPoolName(nodeName)

	enis, ok := ipam.eniCache[nodeName]
	if !ok || len(enis) == 0 {
		return nil, fmt.Errorf("no eni binded to node %s", nodeName)
	}

	// only one eni
	if len(enis) == 1 {
		return enis[0], nil
	}

	ippool, err := ipam.crdInformer.Cce().V1alpha1().IPPools().Lister().IPPools(v1.NamespaceDefault).Get(ippoolName)
	if err != nil {
		log.Errorf(ctx, "failed to get ippool %v: %v", ippoolName, err)
		return nil, err
	}

	candidateENI := enis[0]
	maxIPPerENI := ippool.Spec.ENI.MaxIPPerENI
	// for fix IP pod
	if isFixIPStatefulSetPod(pod) {
		// filter eni in the same subnet
		wep, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().WorkloadEndpoints(pod.Namespace).Get(pod.Name)
		if err == nil {
			// reuse fix ip
			oldSubnet := wep.Spec.SubnetID
			log.Infof(ctx, "pod (%v/%v) was in subnet %v before, will choose an eni in this subnet", pod.Namespace, pod.Name, oldSubnet)
			enis = listENIBySubnet(enis, oldSubnet)
			if len(enis) == 0 {
				log.Errorf(ctx, "schedule error: node %v has no eni in subnet %v", nodeName, oldSubnet)
				return nil, fmt.Errorf("schedule error: node %v has no eni in subnet %v", nodeName, oldSubnet)
			}
			log.Infof(ctx, "list enis in subnet %v: %+v", oldSubnet, enis)
		} else {
			if !errors.IsNotFound(err) {
				log.Errorf(ctx, "failed to get wep of pod (%v %v): %v", pod.Namespace, pod.Name, err)
				return nil, err
			}
		}
	}

	// find the eni with least ip
	candidateENI = ipam.findENIWithLeastIP(enis)
	log.Infof(ctx, "find least ip eni: %v", candidateENI.ENIID)

	// final check the chosen eni
	if ipam.privateIPNumCache[candidateENI.ENIID] >= maxIPPerENI {
		log.Errorf(ctx, "schedule error: eni %v already has %d IP, exceed max ip limit %v", candidateENI.ENIID, ipam.privateIPNumCache[candidateENI.ENIID], maxIPPerENI)
		return nil, fmt.Errorf("schedule error: eni %v cannot add more ip", candidateENI.ENIID)
	}

	log.Infof(ctx, "find suitable eni %v for pod (%v/%v) successfully", candidateENI.ENIID, pod.Namespace, pod.Name)

	return candidateENI, nil
}

func (ipam *IPAM) findENIWithLeastIP(enis []*enisdk.ENI) *enisdk.ENI {
	result := enis[0]

	for _, eni := range enis {
		if ipam.privateIPNumCache[eni.ENIID] < ipam.privateIPNumCache[result.ENIID] {
			result = eni
		}
	}

	return result
}

func listENIBySubnet(enis []*enisdk.ENI, subnetID string) []*enisdk.ENI {
	result := []*enisdk.ENI{}

	for _, eni := range enis {
		if eni.SubnetID == subnetID {
			result = append(result, eni)
		}
	}

	return result
}

func (ipam *IPAM) buildAllocatedCache() error {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	ctx := log.NewContext()
	wepList, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().List(labels.Everything())
	if err != nil {
		return err
	}
	for _, wep := range wepList {
		nwep := wep.DeepCopy()
		ipam.allocated[wep.Spec.IP] = nwep
		log.Infof(ctx, "build cache: found IP %v assigned to pod (%v %v)", wep.Spec.IP, wep.Namespace, wep.Name)
	}
	return nil
}

func (ipam *IPAM) syncENIInfo(stopCh <-chan struct{}) error {
	err := wait.PollImmediateUntil(ipam.eniSyncPeriod, func() (bool, error) {
		ctx := log.NewContext()

		// list vpc enis
		enis, err := ipam.cloud.ListENIs(ctx, ipam.vpcID)
		if err != nil {
			log.Errorf(ctx, "failed to list enis when syncing eni info: %v", err)
			return false, nil
		}

		// list all nodes
		nodes, err := ipam.ifactory.Core().V1().Nodes().Lister().List(labels.Everything())
		if err != nil {
			log.Errorf(ctx, "failed to list nodes when syncing eni info: %v", err)
			return false, nil
		}

		// build eni cache
		err = ipam.buildENICache(ctx, nodes, enis)
		if err != nil {
			log.Errorf(ctx, "failed to build eni cache: %v", err)
			return false, nil
		}

		// update ippool status
		for _, node := range nodes {
			err = ipam.updateIPPoolStatus(ctx, node, enis)
			if err != nil {
				log.Errorf(ctx, "failed to update ippool status for node %v: %v", node.Name, err)
			}
		}

		return false, nil
	}, stopCh)
	if err != nil {
		return err
	}

	return nil
}

func (ipam *IPAM) buildENICache(ctx context.Context, nodes []*v1.Node, enis []*enisdk.ENI) error {
	ipam.lock.Lock()
	defer ipam.lock.Unlock()

	// build eni cache
	ipam.eniCache = make(map[string][]*enisdk.ENI)
	ipam.privateIPNumCache = make(map[string]int)

	instanceIdToNodeNameMap := map[string]string{}
	for _, n := range nodes {
		instanceId := util.GetInstanceID(n)
		if instanceId == "" {
			log.Warningf(ctx, "warning: cannot get instanceID of node %v", n.Name)
			continue
		}
		instanceIdToNodeNameMap[instanceId] = n.Name
		ipam.eniCache[n.Name] = make([]*enisdk.ENI, 0)
	}

	for _, eni := range enis {
		if eni.Status != enisdk.ENIStatusInuse || !nodeagentutil.ENICreatedByCCE(eni) {
			continue
		}

		if nodeName, ok := instanceIdToNodeNameMap[eni.InstanceID]; ok {
			ipam.eniCache[nodeName] = append(ipam.eniCache[nodeName], eni)
		}

		ipam.privateIPNumCache[eni.ENIID] = len(eni.PrivateIPSet)
	}

	return nil
}

func (ipam *IPAM) updateIPPoolStatus(ctx context.Context, node *v1.Node, enis []*enisdk.ENI) error {
	eniStatus := map[string]v1alpha1.ENI{}
	instanceID := util.GetInstanceID(node)
	ippoolName := nodeagentutil.GetIPPoolName(node.Name)
	for _, eni := range enis {
		if !nodeagentutil.ENIOwnedByNode(eni, ipam.clusterID, instanceID) {
			continue
		}

		ippool, err := ipam.crdInformer.Cce().V1alpha1().IPPools().Lister().IPPools(v1.NamespaceDefault).Get(ippoolName)
		if err != nil {
			log.Errorf(ctx, "failed to get ippool %v: %v", ippoolName, err)
			return err
		}

		// interfaceIndex cannot be fetched by eni-ipam, node-agent will update it
		// we get it from origin crd and store it
		var linkIndex int
		if _, ok := ippool.Status.ENI.ENIs[eni.ENIID]; ok {
			linkIndex = ippool.Status.ENI.ENIs[eni.ENIID].InterfaceIndex
		} else {
			linkIndex = -1
		}

		eniStatus[eni.ENIID] = v1alpha1.ENI{
			ID:               eni.ENIID,
			MAC:              eni.MacAddress,
			AvailabilityZone: eni.ZoneName,
			Description:      eni.Description,
			InterfaceIndex:   linkIndex,
			Subnet:           eni.SubnetID,
			PrivateIPSet:     nodeagentutil.GetPrivateIPSet(eni),
			VPC:              eni.VPCID,
		}
	}

	retryErr := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		result, err := ipam.crdClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Get(ctx, ippoolName, metav1.GetOptions{})
		if err != nil {
			log.Errorf(ctx, "failed to get ippool %v: %v", ippoolName, err)
			return err
		}
		result.Status.ENI.ENIs = eniStatus

		_, updateErr := ipam.crdClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Update(ctx, result, metav1.UpdateOptions{})
		if updateErr != nil {
			log.Errorf(ctx, "error updating ippool %v status: %v", ippoolName, updateErr)
			return updateErr
		}
		return nil
	})

	if retryErr != nil {
		log.Errorf(ctx, "retry: error updating ippool %v status: %v", ippoolName, retryErr)
		return retryErr
	}

	return nil
}

func (ipam *IPAM) gc(stopCh <-chan struct{}) error {
	err := wait.PollImmediateUntil(ipam.stsGCPeriod, func() (bool, error) {
		ctx := log.NewContext()
		stsList, err := ipam.ifactory.Apps().V1().StatefulSets().Lister().List(labels.Everything())
		if err != nil {
			log.Errorf(ctx, "gc: error list sts in cluster: %v", err)
			return false, nil
		}
		wepList, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().List(labels.Everything())
		if err != nil {
			log.Errorf(ctx, "gc: error list wep in cluster: %v", err)
			return false, nil
		}

		// release wep if sts is deleted
		for _, wep := range wepList {
			// only delete ip if sts requires fix ip
			if !isFixIPStatefulSetPodWep(wep) {
				continue
			}
			// don't delete ip if policy is Never
			if wep.Spec.FixIPDeletePolicy == FixIPDeletePolicyNever {
				continue
			}
			stsName := util.GetStsName(wep)
			_, err := ipam.ifactory.Apps().V1().StatefulSets().Lister().StatefulSets(wep.Namespace).Get(stsName)
			if err != nil {
				if errors.IsNotFound(err) {
					log.Infof(ctx, "gc: sts (%v %v) has been deleted, will release private IP and clean up orphaned wep ", wep.Namespace, stsName)

					ipam.bucket.Wait(1)
					err := ipam.cloud.DeletePrivateIP(context.Background(), wep.Spec.IP, wep.Spec.ENIID)
					if err != nil {
						log.Errorf(ctx, "gc: failed to delete private IP %v on %v for orphaned pod (%v %v): %v", wep.Spec.IP, wep.Spec.ENIID, wep.Namespace, wep.Name, err)
						if cloud.IsErrorRateLimit(err) {
							time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
						}
					} else {
						log.Infof(ctx, "gc: delete private IP %v on %v for orphaned pod (%v %v) successfully", wep.Spec.IP, wep.Spec.ENIID, wep.Namespace, wep.Name)
					}
					if err != nil && !(ipam.isErrorENIPrivateIPNotFound(err, wep) || cloud.IsErrorENINotFound(err)) {
						log.Errorf(ctx, "gc: stop delete wep for orphaned pod (%v %v), try next round", wep.Namespace, wep.Name)
						// we cannot continue to delete wep, otherwise this IP will not gc in the next round, thus leaked
						continue
					}

					if err := ipam.crdClient.CceV1alpha1().WorkloadEndpoints(wep.Namespace).Delete(ctx, wep.Name, *metav1.NewDeleteOptions(0)); err != nil {
						log.Errorf(ctx, "gc: failed to delete wep for orphaned pod (%v %v): %v", wep.Namespace, wep.Name, err)
					} else {
						log.Infof(ctx, "gc: delete wep for orphaned pod (%v %v) successfully", wep.Namespace, wep.Name)
					}
					ipam.lock.Lock()
					delete(ipam.allocated, wep.Spec.IP)
					ipam.lock.Unlock()
				} else {
					log.Errorf(ctx, "gc: failed to get sts (%v %v): %v", wep.Namespace, stsName, err)
				}
			}
		}

		// release wep if sts scale down
		for _, sts := range stsList {
			replicas := int(*sts.Spec.Replicas)
			requirement, err := labels.NewRequirement(OwnerKey, selection.Equals, []string{sts.Name})
			if err != nil {
				log.Errorf(ctx, "gc: error parsing requirement: %v", err)
				return false, nil
			}
			selector := labels.NewSelector().Add(*requirement)
			weps, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().WorkloadEndpoints(sts.Namespace).List(selector)
			if err != nil {
				log.Errorf(ctx, "gc: failed to list wep with selector: %v: %v", selector.String(), err)
				continue
			}
			if replicas < len(weps) {
				podTemplateAnnotations := sts.Spec.Template.ObjectMeta.Annotations
				if podTemplateAnnotations == nil || podTemplateAnnotations[StsPodAnnotationFixIPDeletePolicy] != FixIPDeletePolicyNever {
					log.Infof(ctx, "gc: sts (%v %v) has scaled down from %v to %v, will release private IP and clean up orphaned wep", sts.Namespace, sts.Name, len(weps), replicas)
				}
				for _, wep := range weps {
					// only delete ip if sts requires fix ip
					if !isFixIPStatefulSetPodWep(wep) {
						continue
					}
					// don't delete ip if policy is Never
					if wep.Spec.FixIPDeletePolicy == FixIPDeletePolicyNever {
						continue
					}
					index := util.GetStsPodIndex(wep)
					if index < 0 || index < replicas {
						continue
					}
					stsPodName := fmt.Sprintf("%s-%d", sts.Name, index)
					log.Infof(ctx, "gc: try to release orphaned wep (%v %v)", sts.Namespace, stsPodName)
					wep, err := ipam.crdInformer.Cce().V1alpha1().WorkloadEndpoints().Lister().WorkloadEndpoints(sts.Namespace).Get(stsPodName)
					if err != nil {
						log.Errorf(ctx, "gc: failed to get wep (%v %v): %v", sts.Namespace, stsPodName, err)
						continue
					}
					ipam.bucket.Wait(1)
					err = ipam.cloud.DeletePrivateIP(context.Background(), wep.Spec.IP, wep.Spec.ENIID)
					if err != nil {
						log.Errorf(ctx, "gc: failed to delete private IP %v on %v for orphaned pod (%v %v): %v", wep.Spec.IP, wep.Spec.ENIID, wep.Namespace, wep.Name, err)
						if cloud.IsErrorRateLimit(err) {
							time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
						}
					} else {
						log.Infof(ctx, "gc: delete private IP %v on %v for orphaned pod (%v %v) successfully", wep.Spec.IP, wep.Spec.ENIID, wep.Namespace, wep.Name)
					}
					if err != nil && !(ipam.isErrorENIPrivateIPNotFound(err, wep) || cloud.IsErrorENINotFound(err)) {
						log.Errorf(ctx, "gc: stop delete wep for orphaned pod (%v %v), try next round", wep.Namespace, wep.Name)
						// we cannot continue to delete wep, otherwise this IP will not gc in the next round, thus leaked
						continue
					}
					err = ipam.crdClient.CceV1alpha1().WorkloadEndpoints(sts.Namespace).Delete(ctx, stsPodName, *metav1.NewDeleteOptions(0))
					if err != nil {
						log.Errorf(ctx, "gc: failed to delete wep for orphaned pod (%v %v): %v", wep.Namespace, wep.Name, err)
					} else {
						log.Infof(ctx, "gc: delete wep for orphaned pod (%v %v) successfully", wep.Namespace, wep.Name)
					}
					ipam.lock.Lock()
					delete(ipam.allocated, wep.Spec.IP)
					ipam.lock.Unlock()
				}
			}
		}

		// release non-sts wep if pod not found
		for _, wep := range wepList {
			if wep.Spec.Type != PodType {
				continue
			}
			_, err := ipam.ifactory.Core().V1().Pods().Lister().Pods(wep.Namespace).Get(wep.Name)
			if err != nil {
				if errors.IsNotFound(err) {
					log.Infof(ctx, "gc: try to release leaked wep (%v %v)", wep.Namespace, wep.Name)
					ipam.bucket.Wait(1)
					err = ipam.cloud.DeletePrivateIP(context.Background(), wep.Spec.IP, wep.Spec.ENIID)
					if err != nil {
						log.Errorf(ctx, "gc: failed to delete private IP %v on %v for leaked pod (%v %v): %v", wep.Spec.IP, wep.Spec.ENIID, wep.Namespace, wep.Name, err)
						if cloud.IsErrorRateLimit(err) {
							time.Sleep(wait.Jitter(rateLimitErrorSleepPeriod, rateLimitErrorJitterFactor))
						}
					} else {
						log.Infof(ctx, "gc: delete private IP %v on %v for leaked pod (%v %v) successfully", wep.Spec.IP, wep.Spec.ENIID, wep.Namespace, wep.Name)
					}
					if err != nil && !(ipam.isErrorENIPrivateIPNotFound(err, wep) || cloud.IsErrorENINotFound(err)) {
						log.Errorf(ctx, "gc: stop delete wep for leaked pod (%v %v), try next round", wep.Namespace, wep.Name)
						// we cannot continue to delete wep, otherwise this IP will not gc in the next round, thus leaked
						continue
					}
					err = ipam.crdClient.CceV1alpha1().WorkloadEndpoints(wep.Namespace).Delete(ctx, wep.Name, *metav1.NewDeleteOptions(0))
					if err != nil {
						log.Errorf(ctx, "gc: failed to delete wep for leaked pod (%v %v): %v", wep.Namespace, wep.Name, err)
					} else {
						log.Infof(ctx, "gc: delete wep for leaked pod (%v %v) successfully", wep.Namespace, wep.Name)
					}
					ipam.lock.Lock()
					delete(ipam.allocated, wep.Spec.IP)
					ipam.lock.Unlock()
				} else {
					log.Errorf(ctx, "gc: failed to get pod (%v %v): %v", wep.Namespace, wep.Name)
				}
			}
		}

		return false, nil
	}, stopCh)

	if err != nil {
		return err
	}
	return nil
}

func (ipam *IPAM) isErrorENIPrivateIPNotFound(err error, wep *v1alpha1.WorkloadEndpoint) bool {
	return cloud.IsErrorENIPrivateIPNotFound(err) && time.Since(wep.Spec.UpdateAt.Time) >= minPrivateIPLifeTime
}

func isFixIPStatefulSetPodWep(wep *v1alpha1.WorkloadEndpoint) bool {
	return wep.Spec.Type == StsType && wep.Spec.EnableFixIP == EnableFixIPTrue
}

func isFixIPStatefulSetPod(pod *v1.Pod) bool {
	if pod.Annotations == nil || !k8sutil.IsStatefulSetPod(pod) {
		return false
	}
	return pod.Annotations[StsPodAnnotationEnableFixIP] == EnableFixIPTrue
}
