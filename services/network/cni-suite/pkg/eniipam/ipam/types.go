package ipam

import (
	"context"
	"sync"
	"time"

	"github.com/juju/ratelimit"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/record"

	enisdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	crdinformers "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/informers/externalversions"
)

const (
	StsType  = "StatefulSet"
	PodType  = "Pod"
	OwnerKey = "cce.io/owner"

	StsPodAnnotationEnableFixIP       = "cce.io/sts-enable-fix-ip"
	EnableFixIPTrue                   = "True"
	StsPodAnnotationFixIPDeletePolicy = "cce.io/sts-pod-fix-ip-delete-policy"
	FixIPDeletePolicyNever            = "Never"
)

type IPAM struct {
	lock sync.RWMutex
	// key is node name, value is list of enis attached
	eniCache map[string][]*enisdk.ENI
	// privateIPNumCache stores allocated IP num of each eni. key is eni id.
	privateIPNumCache map[string]int
	cacheHasSynced    bool
	// key is ip, value is wep
	allocated map[string]*v1alpha1.WorkloadEndpoint
	bucket    *ratelimit.Bucket

	eventBroadcaster record.EventBroadcaster
	eventRecorder    record.EventRecorder

	ifactory   informers.SharedInformerFactory
	kubeClient kubernetes.Interface

	crdInformer crdinformers.SharedInformerFactory
	crdClient   versioned.Interface

	cloud         cloud.Interface
	vpcID         string
	clusterID     string
	eniSyncPeriod time.Duration
	stsGCPeriod   time.Duration
}

type Interface interface {
	Allocate(ctx context.Context, name, namespace, containerID string) (*v1alpha1.WorkloadEndpoint, error)
	Release(ctx context.Context, name, namespace, containerID string) (*v1alpha1.WorkloadEndpoint, error)
	Ready(ctx context.Context) bool
}

var _ Interface = &IPAM{}
