package reconcile

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/pflag"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	cmdget "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/cmd/get"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/ipam"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	k8sutil "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/k8s"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

type Options struct {
	cmdget.Options
}

func (o *Options) addFlags(fs *pflag.FlagSet) {
	o.CommonOptions.AddFlags(fs)
	fs.StringVar(&o.VPCID, "vpc-id", o.VPCID, "Cluster VPC ID")
	fs.StringVar(&o.IPInfoSource, "ip-info-src", o.IPInfoSource, `From which source to get IP info, "k8s" or "cloud" are supported`)
}

func NewReconcileCommand() *cobra.Command {
	o := Options{}

	cmd := &cobra.Command{
		Use:   "reconcile",
		Short: "reconcile tries to delete unassigned ENI IPs and ensure wep of non-hostnetwork pod",
		Run: func(cmd *cobra.Command, args []string) {
			ctx := log.NewContext()

			config, err := k8sutil.BuildConfig(o.KubeConfig)
			if err != nil {
				log.Fatalf(ctx, "failed to create k8s client config: %v", err)
			}
			kubeClient := kubernetes.NewForConfigOrDie(config)
			wepClient := versioned.NewForConfigOrDie(config)
			bceClient, err := cloud.New(o.Region, o.ClusterID, o.AccessKeyID, o.SecretAccessKey, true)
			if err != nil {
				log.Fatalf(ctx, "failed to create cloud client: %v", err)
			}

			podResult, err := cmdget.GetIPFromK8S(ctx, kubeClient)
			if err != nil {
				log.Fatal(ctx, err)
			}

			eniResult, err := cmdget.GetIPFromCloud(ctx, kubeClient, bceClient, o.VPCID)
			if err != nil {
				log.Fatal(ctx, err)
			}

			log.Infof(ctx, "Step 1: clean up unassigned private IP starts...")
			deleteUnassignedIP(ctx, bceClient, wepClient, podResult, eniResult)

			log.Infof(ctx, "Step 2: create missing wep starts...")
			ensureWep(ctx, kubeClient, wepClient, podResult, eniResult)
		},
	}

	o.addFlags(cmd.Flags())

	return cmd
}

func deleteUnassignedIP(ctx context.Context, bceClient cloud.Interface, wepClient versioned.Interface, podResult map[string]*cmdget.PodIPStatus, eniResult map[string]*cmdget.PrivateIPStatus) {
	var unassignedIP []*cmdget.PrivateIPStatus

	// find IP that is never deleted
	neverDeleteIPSet := sets.NewString()
	wepList, err := wepClient.CceV1alpha1().WorkloadEndpoints("").List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Fatal(ctx, err)
	}
	for _, wep := range wepList.Items {
		if wep.Spec.FixIPDeletePolicy == ipam.FixIPDeletePolicyNever && wep.Spec.EnableFixIP == ipam.EnableFixIPTrue {
			neverDeleteIPSet.Insert(wep.Spec.IP)
		}
	}

	if neverDeleteIPSet.Len() != 0 {
		log.Infof(ctx, "List FixIPDeletePolicyNever IP:")
		for _, ip := range neverDeleteIPSet.List() {
			fmt.Println(ip)
		}
	}

	for ip, status := range eniResult {
		// step 1. find IP not never delete
		if ok := neverDeleteIPSet.Has(ip); ok {
			continue
		}
		// step 2. find IP not assigned to pod
		if _, ok := podResult[ip]; ok {
			continue
		}
		unassignedIP = append(unassignedIP, status)
	}

	if len(unassignedIP) == 0 {
		log.Infof(ctx, "good news: no unassigned primary IP found")
		return
	}

	log.Infof(ctx, "Delete Unassigned Private IP:\n")
	for _, ip := range unassignedIP {
		fmt.Println(ip.IP)
	}
	prompt()

	for _, ip := range unassignedIP {
		if err := bceClient.DeletePrivateIP(context.TODO(), ip.IP, ip.ENIID); err != nil {
			log.Errorf(ctx, "error delete private IP %v of ENI %v: %v", ip.IP, ip.ENIID, err)
		} else {
			log.Infof(ctx, "delete private IP %v of ENI %v successfully", ip.IP, ip.ENIID)
		}
		time.Sleep(time.Millisecond * 100)
	}
}

func ensureWep(ctx context.Context, kubeClient kubernetes.Interface, wepClient versioned.Interface, podResult map[string]*cmdget.PodIPStatus, eniResult map[string]*cmdget.PrivateIPStatus) {
	var missingWep []*v1alpha1.WorkloadEndpoint

	for ip, status := range podResult {
		namespace := status.Namespace
		name := status.Name
		_, err := wepClient.CceV1alpha1().WorkloadEndpoints(namespace).Get(ctx, name, metav1.GetOptions{})
		if err == nil {
			continue
		}
		if errors.IsNotFound(err) {
			eniStatus, ok := eniResult[ip]
			if !ok {
				log.Warningf(ctx, "failed to find eni info for IP %v of pod (%v %v)", ip, namespace, name)
				continue
			}

			pod, err := kubeClient.CoreV1().Pods(namespace).Get(ctx, name, metav1.GetOptions{})
			if err != nil {
				log.Errorf(ctx, "error get pod (%v %v): %v", namespace, name, err)
				continue
			}
			podType := ipam.PodType
			if k8sutil.IsStatefulSetPod(pod) {
				podType = ipam.StsType
			}

			wep := &v1alpha1.WorkloadEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Name:      name,
					Namespace: namespace,
				},
				Spec: v1alpha1.WorkloadEndpointSpec{
					IP:                ip,
					Type:              podType,
					Mac:               eniStatus.Mac,
					SubnetID:          eniStatus.SubnetID,
					EnableFixIP:       status.EnableFixIP,
					ENIID:             eniStatus.ENIID,
					Node:              status.NodeName,
					FixIPDeletePolicy: status.FixIPDeletePolicy,
					UpdateAt:          metav1.Now(),
				},
			}

			missingWep = append(missingWep, wep)
		} else {
			log.Errorf(ctx, "error get wep (%v %v): %v")
		}
	}

	if len(missingWep) == 0 {
		log.Infof(ctx, "good news: no missing wep found")
		return
	}

	log.Infof(ctx, "Create Missing Wep:\n")
	for _, wep := range missingWep {
		fmt.Println(wep.Namespace, wep.Name)
	}
	prompt()

	for _, wep := range missingWep {
		_, err := wepClient.CceV1alpha1().WorkloadEndpoints(wep.Namespace).Create(ctx, wep, metav1.CreateOptions{})
		if err != nil {
			log.Errorf(ctx, "error create wep of pod (%v %v): %v", wep.Namespace, wep.Name, err)
		} else {
			log.Infof(ctx, "create wep of pod (%v %v) successfully\n", wep.Namespace, wep.Name)
		}
	}
}

func prompt() {
	fmt.Printf("-> Press Return key to continue.")
	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		break
	}
	if err := scanner.Err(); err != nil {
		panic(err)
	}
	fmt.Println()
}
