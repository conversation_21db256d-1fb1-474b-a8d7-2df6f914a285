package ip

import (
	"context"

	"github.com/spf13/cobra"
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/cmd"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

type Options struct {
	cmd.CommonOptions
	ENIID string
	IP    string
}

func (o *Options) addFlags(fs *pflag.FlagSet) {
	o.CommonOptions.AddFlags(fs)
	fs.StringVar(&o.ENIID, "eni-id", o.ENIID, "ENI ID")
	fs.StringVar(&o.IP, "ip", o.IP, "Private IP")
}

func NewIPCommand() *cobra.Command {
	o := Options{}

	cmd := &cobra.Command{
		Use: "ip add/del",
		Run: func(cmd *cobra.Command, args []string) {
			ctx := log.NewContext()

			bceClient, err := cloud.New(o.Region, o.ClusterID, o.AccessKeyID, o.SecretAccessKey, true)
			if err != nil {
				log.Fatalf(ctx, "failed to create cloud client: %v", err)
			}

			if len(args) == 0 {
				log.Fatal(ctx, "You must specify the type of operation: add or del.")
			}

			if args[0] == "add" {
				ip, err := bceClient.AddPrivateIP(context.TODO(), o.IP, o.ENIID)
				if err != nil {
					log.Fatal(ctx, "error add IP %v to ENI %v: %v", o.IP, o.ENIID, err)
				} else {
					log.Infof(ctx, "add IP %v to ENI %v successfully", ip, o.ENIID)
					return
				}
			}

			if args[0] == "del" {
				err := bceClient.DeletePrivateIP(context.TODO(), o.IP, o.ENIID)
				if err != nil {
					log.Fatal(ctx, "error del IP %v to ENI %v: %v\n", o.IP, o.ENIID, err)
				} else {
					log.Infof(ctx, "del IP %v to ENI %v successfully\n", o.IP, o.ENIID)
					return
				}
			}

			log.Fatal(ctx, "unknown operation: %v", args[0])
		},
	}

	o.addFlags(cmd.Flags())

	return cmd
}
