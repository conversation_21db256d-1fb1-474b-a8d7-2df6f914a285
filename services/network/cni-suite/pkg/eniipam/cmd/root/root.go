package cmd

import (
	"context"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/pflag"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/cmd/get"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/cmd/ip"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/cmd/reconcile"
	httpserver "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/http"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/ipam"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/k8s"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

func NewRootCommand() *cobra.Command {
	o := Options{
		stopCh:          make(chan struct{}),
		ResyncPeriod:    15 * time.Second,
		ENISyncPeriod:   5 * time.Second,
		StsGCPeriod:     60 * time.Second,
		HTTPPort:        9999,
		IPMutatingBurst: 5,
		IPMutatingRate:  10,
		MaxWorkerNum:    20,
		Debug:           false,
	}

	ctx := log.NewContext()

	cmd := &cobra.Command{
		Use: "cce-ipam",
		Run: func(cmd *cobra.Command, args []string) {
			log.Info(ctx, "cce-ipam starts...")
			printFlags(cmd.Flags())

			config, err := k8s.BuildConfig(o.KubeConfig)
			if err != nil {
				log.Fatalf(ctx, "failed to create k8s client config: %v", err)
			}
			kubeClient := kubernetes.NewForConfigOrDie(config)
			crdClient := versioned.NewForConfigOrDie(config)
			bceClient, err := cloud.New(o.Region, o.ClusterID, o.AccessKeyID, o.SecretAccessKey, o.Debug)
			if err != nil {
				log.Fatalf(ctx, "failed to create cloud client: %v", err)
			}
			ipamd, err := ipam.NewIPAM(
				kubeClient,
				crdClient,
				bceClient,
				o.VPCID,
				o.ClusterID,
				o.ResyncPeriod,
				o.ENISyncPeriod,
				o.StsGCPeriod,
				o.IPMutatingRate,
				o.IPMutatingBurst,
			)
			if err != nil {
				log.Fatalf(ctx, "failed to create ipamd: %v", err)
			}
			go func() {
				if err := ipamd.Run(ctx, o.stopCh); err != nil {
					log.Fatalf(ctx, "ipamd failed to run: %v", err)
				}
			}()

			httpserver.Run(ipamd, o.HTTPPort, o.MaxWorkerNum)
		},
	}

	o.AddFlags(cmd.Flags())

	// add sub command
	cmd.AddCommand(get.NewGetCommand())
	cmd.AddCommand(reconcile.NewReconcileCommand())
	cmd.AddCommand(ip.NewIPCommand())

	return cmd
}

func (o *Options) AddFlags(fs *pflag.FlagSet) {
	// Global
	fs.StringVar(&o.KubeConfig, "kubeconfig", o.KubeConfig, "Path to kubeconfig file with authorization information or empty if in cluster")
	fs.DurationVar(&o.ResyncPeriod, "resync-period", o.ResyncPeriod, "How often configuration from the apiserver is refreshed.  Must be greater than 0")
	// BCE
	fs.StringVar(&o.AccessKeyID, "ak", o.AccessKeyID, "BCE OpenApi AccessKeyID")
	fs.StringVar(&o.SecretAccessKey, "sk", o.AccessKeyID, "BCE OpenApi SecretAccessKey")
	fs.StringVar(&o.Region, "region", o.Region, "BCE OpenApi Region")
	fs.StringVar(&o.VPCID, "vpc-id", o.VPCID, "Cluster VPC ID")
	fs.StringVar(&o.ClusterID, "cluster-id", o.ClusterID, "CCE Cluster ID")
	// IPAM
	fs.DurationVar(&o.ENISyncPeriod, "eni-sync-period", o.ENISyncPeriod, "How often to rebuild ENI cache")
	fs.DurationVar(&o.StsGCPeriod, "sts-gc-period", o.StsGCPeriod, "How often to gc orphaned sts pod IP")
	fs.IntVar(&o.HTTPPort, "http-port", o.HTTPPort, "Http server listen port")
	fs.Float64Var(&o.IPMutatingRate, "ip-mutating-rate", o.IPMutatingRate, "Private IP Mutating Rate")
	fs.Int64Var(&o.IPMutatingBurst, "ip-mutating-burst", o.IPMutatingBurst, "Private IP Mutating Burst")
	fs.IntVar(&o.MaxWorkerNum, "max-worker-num", o.MaxWorkerNum, "Max Worker Num of IPAM")
	fs.BoolVar(&o.Debug, "debug", o.Debug, "Debug mode")
}

// printFlags logs the flags in the flagset
func printFlags(flags *pflag.FlagSet) {
	flags.VisitAll(func(flag *pflag.Flag) {
		log.Infof(context.TODO(), "FLAG: --%s=%q", flag.Name, flag.Value)
	})
}
