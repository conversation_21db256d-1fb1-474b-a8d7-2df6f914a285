package cmd

import (
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/cmd"
)

type Options struct {
	cmd.CommonOptions
	VPCID           string
	ENISyncPeriod   time.Duration
	StsGCPeriod     time.Duration
	HTTPPort        int
	ResyncPeriod    time.Duration
	IPMutatingRate  float64
	IPMutatingBurst int64
	MaxWorkerNum    int
	Debug           bool
	stopCh          chan struct{}
}
