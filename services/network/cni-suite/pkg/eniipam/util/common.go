package util

import (
	"strconv"
	"strings"

	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
)

func GetInstanceID(node *v1.Node) string {
	providerID := node.Spec.ProviderID
	if !strings.HasPrefix(providerID, "cce://") {
		providerID = "cce://" + providerID
	}
	splitted := strings.Split(providerID, "//")
	if len(splitted) != 2 {
		return ""
	}
	return splitted[1]
}

func GetStsName(wep *v1alpha1.WorkloadEndpoint) string {
	return wep.Name[:strings.LastIndex(wep.Name, "-")]
}

func GetStsPodIndex(wep *v1alpha1.WorkloadEndpoint) int {
	indexStr := wep.Name[strings.LastIndex(wep.Name, "-")+1:]
	index, err := strconv.ParseInt(indexStr, 10, 32)
	if err != nil {
		return -1
	}
	return int(index)
}
