package server

import "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"

type AllocateIPResponse struct {
	IsSuccess   bool                           `json:"isSuccess,omitempty"`
	ErrMsg      string                         `json:"errMsg,omitempty"`
	NetworkInfo *v1alpha1.WorkloadEndpointSpec `json:"networkInfo,omitempty"`
}

type ReleaseIPResponse struct {
	IsSuccess   bool                           `json:"isSuccess,omitempty"`
	ErrMsg      string                         `json:"errMsg,omitempty"`
	NetworkInfo *v1alpha1.WorkloadEndpointSpec `json:"networkInfo,omitempty"`
}
