package server

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/astaxie/beego"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/ipam"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

var (
	ipamd        ipam.Interface
	maxWorkerNum int = 20
	workers      int = 0
	mutex        sync.Mutex
)

type IPAMController struct {
	beego.Controller
}

func Run(ipam ipam.Interface, listenPort int, maxWorkers int) {
	ipamd = ipam
	maxWorkerNum = maxWorkers

	ctrl := &IPAMController{}
	beego.Router("/", ctrl, "get:Hello")
	beego.Router("/v1alpha/allocate", ctrl, "get:Allocate")
	beego.Router("/v1alpha/release", ctrl, "get:Release")
	beego.Run(fmt.Sprintf(":%d", listenPort))
}

func (c *IPAMController) Hello() {
	c.Ctx.Output.Body([]byte("CCE IPAM"))
}

func (c *IPAMController) Allocate() {
	var allocResponse AllocateIPResponse
	var wep *v1alpha1.WorkloadEndpoint
	var err error

	ctx := newContext()

	name := c.Input().Get("name")
	namespace := c.Input().Get("namespace")
	containerID := c.Input().Get("containerID")

	log.Infof(ctx, "====> allocate request for pod (%v %v) recv <====", namespace, name)
	defer func() {
		if data, err := json.Marshal(allocResponse); err == nil {
			log.Infof(ctx, "alloc resp: %v", string(data))
		}
		log.Infof(ctx, "====> allocate response for pod (%v %v) sent <====", namespace, name)
	}()

	// request comes in
	c.incWorker()

	// 后端申请 IP 可能较慢，在请求入口限制住同时申请 IP 的并发度
	log.Infof(ctx, "ipam worker num: %v", c.getWorker())
	if c.getWorker() > maxWorkerNum {
		allocResponse.IsSuccess = false
		allocResponse.ErrMsg = "QPS exceed limit, wait another try"
		// request rejected
		c.decWorker()
		goto sendResponse
	}

	wep, err = ipamd.Allocate(ctx, name, namespace, containerID)
	// request completes
	c.decWorker()

	if err != nil {
		allocResponse.IsSuccess = false
		allocResponse.ErrMsg = err.Error()
	} else {
		allocResponse.IsSuccess = true
		allocResponse.NetworkInfo = &wep.Spec
	}

sendResponse:
	c.Data["json"] = allocResponse
	c.ServeJSON()
}

func (c *IPAMController) Release() {
	var releaseResponse ReleaseIPResponse

	ctx := newContext()

	name := c.Input().Get("name")
	namespace := c.Input().Get("namespace")
	containerID := c.Input().Get("containerID")

	log.Infof(ctx, "====> release request for pod (%v %v) recv <====", namespace, name)
	defer func() {
		if data, err := json.Marshal(releaseResponse); err == nil {
			log.Infof(ctx, "release resp: %v", string(data))
		}
		log.Infof(ctx, "====> release response for pod (%v %v) sent <====", namespace, name)
	}()

	wep, err := ipamd.Release(ctx, name, namespace, containerID)
	if err != nil {
		releaseResponse.IsSuccess = false
		releaseResponse.ErrMsg = err.Error()
	} else {
		releaseResponse.IsSuccess = true
		if wep != nil {
			releaseResponse.NetworkInfo = &wep.Spec
		}
	}
	c.Data["json"] = releaseResponse
	c.ServeJSON()
}

func newContext() context.Context {
	requestID := logger.GetUUID()
	return context.WithValue(context.TODO(), logger.RequestID, requestID)
}

func (c *IPAMController) incWorker() {
	mutex.Lock()
	defer mutex.Unlock()
	workers++
}
func (c *IPAMController) decWorker() {
	mutex.Lock()
	defer mutex.Unlock()
	workers--
}
func (c *IPAMController) getWorker() int {
	mutex.Lock()
	defer mutex.Unlock()
	return workers
}
