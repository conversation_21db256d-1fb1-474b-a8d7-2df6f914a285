package cloud

import (
	"context"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

const (
	secretVolume = "/var/run/secrets/cce/cce-plugin-token"
)

func New(region, clusterID, ak, sk string, debug bool) (Interface, error) {
	var bceConfig = &bce.Config{
		Checksum: true,
		Region:   region,
	}

	var bceSignOption *bce.SignOption
	var helper ccegateway.Helper

	if ak != "" && sk != "" {
		bceSignOption = &bce.SignOption{
			Credentials: &bce.Credentials{
				AccessKeyID:     ak,
				SecretAccessKey: sk,
			},
		}
	} else {
		// set cce-gateway proxy
		helper = ccegateway.NewHelper(region, clusterID)
		bceConfig.ProxyHost, bceConfig.ProxyPort = helper.GetHostAndPort()
	}

	// create bce clients
	vpcClient := vpc.NewClient(bceConfig)
	vpcClient.SetDebug(debug)

	bccClient := bcc.NewClient(bceConfig)
	bccClient.SetDebug(debug)

	eniClient := eni.NewClient(eni.NewConfig(bceConfig))
	eniClient.SetDebug(debug)

	c := &Client{
		vpcClient:        vpcClient,
		bccClient:        bccClient,
		eniClient:        eniClient,
		signOption:       bceSignOption,
		ccegatewayHelper: helper,
	}
	return c, nil
}

func (c *Client) ListENIs(ctx context.Context, vpcID string) ([]*eni.ENI, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return nil, err
	}

	enis := []*eni.ENI{}

	isTruncated := true
	nextMarker := ""

	for isTruncated {
		listArgs := &eni.ListENIsArgs{
			VPCID:  vpcID,
			Marker: nextMarker,
		}

		res, err := c.eniClient.ListENIs(ctx, listArgs, bceSignOption)
		if err != nil {
			return nil, err
		}

		enis = append(enis, res.ENIs...)

		nextMarker = res.NextMarker
		isTruncated = res.IsTruncated
	}

	return enis, nil
}

func (c *Client) AddPrivateIP(ctx context.Context, privateIP string, eniID string) (string, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return "", err
	}

	t := time.Now()

	defer func(startTime time.Time) {
		log.Infof(ctx, "AddPrivateIP elapsed: %v", time.Since(t))
	}(t)

	return c.eniClient.AddPrivateIP(ctx, &eni.AddPrivateIPArgs{
		ENIID:     eniID,
		PrivateIP: privateIP,
	}, bceSignOption)
}

func (c *Client) DeletePrivateIP(ctx context.Context, privateIP string, eniID string) error {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return err
	}

	t := time.Now()

	defer func(startTime time.Time) {
		log.Infof(ctx, "DeletePrivateIP elapsed: %v", time.Since(t))
	}(t)

	return c.eniClient.DeletePrivateIP(ctx, &eni.DeletePrivateIPArgs{
		ENIID:     eniID,
		PrivateIP: privateIP,
	}, bceSignOption)
}

func (c *Client) CreateENI(ctx context.Context, args *eni.CreateENIArgs) (string, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return "", err
	}

	return c.eniClient.CreateENI(ctx, args, bceSignOption)
}

func (c *Client) DeleteENI(ctx context.Context, eniID string) error {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return err
	}

	return c.eniClient.DeleteENI(ctx, eniID, bceSignOption)
}

func (c *Client) AttachENI(ctx context.Context, args *eni.AttachENIArgs) error {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return err
	}

	return c.eniClient.AttachENI(ctx, args, bceSignOption)
}

func (c *Client) DetachENI(ctx context.Context, args *eni.DetachENIArgs) error {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return err
	}
	return c.eniClient.DetachENI(ctx, args, bceSignOption)
}

func (c *Client) StatENI(ctx context.Context, eniID string) (*eni.StatENIResponse, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return nil, err
	}

	return c.eniClient.StatENI(ctx, eniID, bceSignOption)
}

func (c *Client) ListRouteTable(ctx context.Context, args *vpc.ListRouteArgs) ([]vpc.RouteRule, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return nil, err
	}

	return c.vpcClient.ListRouteTable(ctx, args, bceSignOption)
}

func (c *Client) CreateRouteRule(ctx context.Context, args *vpc.CreateRouteRuleArgs) (string, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return "", err
	}

	return c.vpcClient.CreateRouteRule(ctx, args, bceSignOption)
}

func (c *Client) DeleteRoute(ctx context.Context, routeID string) error {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return err
	}

	return c.vpcClient.DeleteRoute(ctx, routeID, bceSignOption)
}

func (c *Client) DescribeSubnet(ctx context.Context, subnetID string) (*vpc.Subnet, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return nil, err
	}

	return c.vpcClient.DescribeSubnet(ctx, subnetID, bceSignOption)
}

func (c *Client) DescribeInstance(ctx context.Context, instanceID string) (*bcc.Instance, error) {
	bceSignOption, err := c.auth(ctx)
	if err != nil {
		return nil, err
	}

	return c.bccClient.DescribeInstance(ctx, instanceID, bceSignOption)
}

// auth uses ccegateway or ak/sk to auth
func (c *Client) auth(ctx context.Context) (*bce.SignOption, error) {
	var bceSignOption *bce.SignOption
	var err error
	if c.ccegatewayHelper != nil {
		bceSignOption, err = c.ccegatewayHelper.NewSignOptionFromVolume(ctx, secretVolume)
	} else {
		bceSignOption = c.signOption
	}

	return bceSignOption, err
}
