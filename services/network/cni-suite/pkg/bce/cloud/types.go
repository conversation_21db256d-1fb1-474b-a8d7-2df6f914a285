package cloud

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eni"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
)

type Interface interface {
	ListENIs(ctx context.Context, vpcID string) ([]*eni.ENI, error)
	AddPrivateIP(ctx context.Context, privateIP string, eniID string) (string, error)
	DeletePrivateIP(ctx context.Context, privateIP string, eniID string) error
	CreateENI(ctx context.Context, args *eni.CreateENIArgs) (string, error)
	DeleteENI(ctx context.Context, eniID string) error
	AttachENI(ctx context.Context, args *eni.AttachENIArgs) error
	DetachENI(ctx context.Context, args *eni.DetachENIArgs) error
	StatENI(ctx context.Context, eniID string) (*eni.StatENIResponse, error)

	ListRouteTable(ctx context.Context, args *vpc.ListRouteArgs) ([]vpc.RouteRule, error)
	CreateRouteRule(ctx context.Context, args *vpc.CreateRouteRuleArgs) (string, error)
	DeleteRoute(ctx context.Context, routeID string) error

	DescribeSubnet(ctx context.Context, subnetID string) (*vpc.Subnet, error)

	DescribeInstance(ctx context.Context, instanceID string) (*bcc.Instance, error)
}

type Client struct {
	bccClient bcc.Interface
	eniClient eni.Interface
	vpcClient vpc.Interface

	signOption       *bce.SignOption
	ccegatewayHelper ccegateway.Helper
}

var _ Interface = &Client{}
