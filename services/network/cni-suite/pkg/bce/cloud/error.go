package cloud

import (
	"strings"
)

type ErrorReason string

const (
	ErrorReasonUnknown                    ErrorReason = "Unknown"
	ErrorReasonENIPrivateIPNotFound       ErrorReason = "ENIPrivateIPNotFound"
	ErrorReasonENINotFound                ErrorReason = "ENINotFound"
	ErrorReasonSubnetHasNoMoreIP          ErrorReason = "SubnetHasNoMoreIP"
	ErrorReasonRateLimit                  ErrorReason = "RateLimit"
	ErrorReasonPrivateIPInUse             ErrorReason = "PrivateIPInUse"
	ErrorReasonBBCENIPrivateIPNotFound    ErrorReason = "BBCENIPrivateIPNotFound"
	ErrorReasonBBCENIPrivateIPExceedLimit ErrorReason = "BBCENIPrivateIPExceedLimit"
)

func ReasonForError(err error) ErrorReason {
	if err != nil {
		errMsg := err.Error()
		switch {
		case caseInsensitiveContains(errMsg, "PrivateIPNotExistException"):
			return ErrorReasonENIPrivateIPNotFound
		case caseInsensitiveContains(errMsg, "EniIdException"):
			return ErrorReasonENINotFound
		case caseInsensitiveContains(errMsg, "SubnetHasNoMoreIpException"):
			return ErrorReasonSubnetHasNoMoreIP
		case caseInsensitiveContains(errMsg, "RateLimit"):
			return ErrorReasonRateLimit
		case caseInsensitiveContains(errMsg, "BadRequest") && caseInsensitiveContains(errMsg, "is invalid"):
			return ErrorReasonBBCENIPrivateIPNotFound
		case caseInsensitiveContains(errMsg, "ExceedLimitException"):
			return ErrorReasonBBCENIPrivateIPExceedLimit
		case caseInsensitiveContains(errMsg, "PrivateIpInUseException"):
			return ErrorReasonPrivateIPInUse
		}
	}
	return ErrorReasonUnknown
}

// IsErrorENIPrivateIPNotFound 判定删除辅助 IP 的 err 是否是因为 IP 不属于弹性网卡
func IsErrorENIPrivateIPNotFound(err error) bool {
	return ReasonForError(err) == ErrorReasonENIPrivateIPNotFound
}

func IsErrorENINotFound(err error) bool {
	return ReasonForError(err) == ErrorReasonENINotFound
}

func IsErrorSubnetHasNoMoreIP(err error) bool {
	return ReasonForError(err) == ErrorReasonSubnetHasNoMoreIP
}

func IsErrorRateLimit(err error) bool {
	return ReasonForError(err) == ErrorReasonRateLimit
}

func IsErrorPrivateIPInUse(err error) bool {
	return ReasonForError(err) == ErrorReasonPrivateIPInUse
}

func IsErrorBBCENIPrivateIPNotFound(err error) bool {
	return ReasonForError(err) == ErrorReasonBBCENIPrivateIPNotFound
}

func IsErrorBBCENIPrivateIPExceedLimit(err error) bool {
	return ReasonForError(err) == ErrorReasonBBCENIPrivateIPExceedLimit
}

func caseInsensitiveContains(s, substr string) bool {
	s, substr = strings.ToLower(s), strings.ToLower(substr)
	return strings.Contains(s, substr)
}
