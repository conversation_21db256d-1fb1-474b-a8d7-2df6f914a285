package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// +genclient
// +genclient:noStatus
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type WorkloadEndpoint struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec WorkloadEndpointSpec `json:"spec,omitempty"`
}

type WorkloadEndpointSpec struct {
	IP                string      `json:"ip"`
	Type              string      `json:"type"`
	Mac               string      `json:"mac"`
	Gw                string      `json:"gw"`
	ENIID             string      `json:"eniID"`
	Node              string      `json:"node"`
	SubnetID          string      `json:"subnetID"`
	EnableFixIP       string      `json:"enableFixIP"`
	FixIPDeletePolicy string      `json:"fixIPDeletePolicy"`
	PodUID            string      `json:"podUID"`
	ContainerID       string      `json:"containerID"`
	UpdateAt          metav1.Time `json:"updateAt"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type WorkloadEndpointList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []WorkloadEndpoint `json:"items"`
}

// +genclient
// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type IPPool struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   IPPoolSpec   `json:"spec"`
	Status IPPoolStatus `json:"status"`
}

type IPPoolSpec struct {
	// Deprecated: IPPool bind to these node
	Nodes []string `json:"nodes"`

	// NodeSelector allows IPPool to allocate for a specific node by label selector
	NodeSelector string `json:"nodeSelector"`

	// Priority is the priority value of IPPool
	Priority int32 `json:"priority"`

	// ranges is for host-local allocator
	IPv4Ranges []Range `json:"ipv4Ranges,omitempty"`
	IPv6Ranges []Range `json:"ipv6Ranges,omitempty"`

	// eni spec, subnet/security group etc
	// eni is for eni allocator
	ENI ENISpec `json:"eni,omitempty"`
}

type IPPoolStatus struct {
	ENI ENIStatus `json:"eni,omitempty"`
}

// +k8s:deepcopy-gen:interfaces=k8s.io/apimachinery/pkg/runtime.Object

type IPPoolList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []IPPool `json:"items"`
}

type ENISpec struct {
	InstanceID       string   `json:"instanceID"`
	InstanceType     string   `json:"instanceType"`
	AvailabilityZone string   `json:"availabilityZone"`
	VPCID            string   `json:"vpcID"`
	Subnets          []Subnet `json:"subnets"`
	SecurityGroups   []string `json:"securityGroups"`
	MaxENINum        int      `json:"maxENINum"`
	MaxIPPerENI      int      `json:"maxIPPerENI"`
}

type Subnet struct {
	ID     string `json:"id"`
	Enable bool   `json:"enable"`
}

type ENIStatus struct {
	ENIs map[string]ENI `json:"enis,omitempty"`
}

type ENI struct {
	ID               string      `json:"id,omitempty"`
	MAC              string      `json:"mac,omitempty"`
	AvailabilityZone string      `json:"availabilityZone,omitempty"`
	Description      string      `json:"description,omitempty"`
	InterfaceIndex   int         `json:"interfaceIndex,omitempty"`
	Subnet           string      `json:"subnet,omitempty"`
	VPC              string      `json:"vpc,omitempty"`
	PrivateIPSet     []PrivateIP `json:"addresses,omitempty"`
	SecurityGroups   []string    `json:"securityGroups,omitempty"`
}

type PrivateIP struct {
	IsPrimary        bool   `json:"isPrimary"`
	PublicIPAddress  string `json:"publicIPAddress,omitempty"`
	PrivateIPAddress string `json:"privateIPAddress"`
}

type Range struct {
	// Version is ip version: 4 or 6
	Version    int      `json:"version"`
	CIDR       string   `json:"cidr"`
	RangeStart string   `json:"rangeStart"`
	RangeEnd   string   `json:"rangeEnd"`
	Excludes   []string `json:"excludes"`
	Gateway    string   `json:"gateway"`
	Routes     []Route  `json:"routes"`
	VlanID     int      `json:"vlanID"`
}

type Route struct {
	Dst string `json:"dst"`
	Gw  string `json:"gw"`
}
