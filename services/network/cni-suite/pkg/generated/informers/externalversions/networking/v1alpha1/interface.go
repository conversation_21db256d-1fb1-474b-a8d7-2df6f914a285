// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	internalinterfaces "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// IPPools returns a IPPoolInformer.
	IPPools() IPPoolInformer
	// WorkloadEndpoints returns a WorkloadEndpointInformer.
	WorkloadEndpoints() WorkloadEndpointInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// IPPools returns a IPPoolInformer.
func (v *version) IPPools() IPPoolInformer {
	return &iPPoolInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// WorkloadEndpoints returns a WorkloadEndpointInformer.
func (v *version) WorkloadEndpoints() WorkloadEndpointInformer {
	return &workloadEndpointInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}
