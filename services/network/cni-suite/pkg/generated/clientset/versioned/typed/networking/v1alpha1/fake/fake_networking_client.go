// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned/typed/networking/v1alpha1"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeCceV1alpha1 struct {
	*testing.Fake
}

func (c *FakeCceV1alpha1) IPPools(namespace string) v1alpha1.IPPoolInterface {
	return &FakeIPPools{c, namespace}
}

func (c *FakeCceV1alpha1) WorkloadEndpoints(namespace string) v1alpha1.WorkloadEndpointInterface {
	return &FakeWorkloadEndpoints{c, namespace}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeCceV1alpha1) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
