// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned/scheme"
	rest "k8s.io/client-go/rest"
)

type CceV1alpha1Interface interface {
	RESTClient() rest.Interface
	IPPoolsGetter
	WorkloadEndpointsGetter
}

// CceV1alpha1Client is used to interact with features provided by the cce.io group.
type CceV1alpha1Client struct {
	restClient rest.Interface
}

func (c *CceV1alpha1Client) IPPools(namespace string) IPPoolInterface {
	return newIPPools(c, namespace)
}

func (c *CceV1alpha1Client) WorkloadEndpoints(namespace string) WorkloadEndpointInterface {
	return newWorkloadEndpoints(c, namespace)
}

// NewForConfig creates a new CceV1alpha1Client for the given config.
func NewForConfig(c *rest.Config) (*CceV1alpha1Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientFor(&config)
	if err != nil {
		return nil, err
	}
	return &CceV1alpha1Client{client}, nil
}

// NewForConfigOrDie creates a new CceV1alpha1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *CceV1alpha1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new CceV1alpha1Client for the given RESTClient.
func New(c rest.Interface) *CceV1alpha1Client {
	return &CceV1alpha1Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1alpha1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *CceV1alpha1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
