package k8s

import (
	"context"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	clientretry "k8s.io/client-go/util/retry"
	cloudproviderhelpers "k8s.io/cloud-provider/node/helpers"

	clientset "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

func BuildConfig(kubeconfig string) (*rest.Config, error) {
	if kubeconfig != "" {
		cfg, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
		if err != nil {
			return nil, err
		}
		return cfg, nil
	}

	cfg, err := rest.InClusterConfig()
	if err != nil {
		return nil, err
	}
	return cfg, nil
}

// NewKubeClient creates a k8s client
func NewKubeClient(kubeconfig string) (kubernetes.Interface, error) {
	config, err := BuildConfig(kubeconfig)
	if err != nil {
		return nil, err
	}
	return kubernetes.NewForConfig(config)
}

// NewIPPoolClient creates IPPool CR client
func NewIPPoolClient(kubeconfig string) (clientset.Interface, error) {
	config, err := BuildConfig(kubeconfig)
	if err != nil {
		return nil, err
	}
	return clientset.NewForConfig(config)
}

func IsStatefulSetPod(pod *v1.Pod) bool {
	controllerRef := metav1.GetControllerOf(pod)
	if controllerRef != nil {
		if controllerRef.Kind == "StatefulSet" {
			return true
		}
	}
	return false
}

func UpdateNetworkingCondition(
	ctx context.Context,
	kubeClient kubernetes.Interface,
	nodeName string,
	networkReady bool,
	readyReason string,
	unReadyReason string,
	readyMsg string,
	unReadyMsg string,
) error {
	node, err := kubeClient.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	_, condition := cloudproviderhelpers.GetNodeCondition(&(node.Status), v1.NodeNetworkUnavailable)
	if networkReady && condition != nil && condition.Status == v1.ConditionFalse {
		log.Infof(ctx, "set node %v with NodeNetworkUnavailable=false was canceled because it is already set", node.Name)
		return nil
	}

	if !networkReady && condition != nil && condition.Status == v1.ConditionTrue {
		log.Infof(ctx, "set node %v with NodeNetworkUnavailable=true was canceled because it is already set", node.Name)
		return nil
	}

	log.Infof(ctx, "patching node %v NetworkUnavailable status with %v, previous condition was:%+v", node.Name, !networkReady, condition)

	// either condition is not there, or has a value != to what we need
	// start setting it
	err = clientretry.RetryOnConflict(clientretry.DefaultRetry, func() error {
		var err error
		currentTime := metav1.Now()

		if networkReady {
			err = cloudproviderhelpers.SetNodeCondition(kubeClient, types.NodeName(node.Name), v1.NodeCondition{
				Type:               v1.NodeNetworkUnavailable,
				Status:             v1.ConditionFalse,
				Reason:             readyReason,
				Message:            readyMsg,
				LastTransitionTime: currentTime,
			})
		} else {
			err = cloudproviderhelpers.SetNodeCondition(kubeClient, types.NodeName(node.Name), v1.NodeCondition{
				Type:               v1.NodeNetworkUnavailable,
				Status:             v1.ConditionTrue,
				Reason:             unReadyReason,
				Message:            unReadyMsg,
				LastTransitionTime: currentTime,
			})
		}
		if err != nil {
			log.Errorf(ctx, "error updating node %s, retrying: %v", types.NodeName(node.Name), err)
		}
		return err
	})

	if err != nil {
		log.Errorf(ctx, "error updating node %s, retrying: %v", types.NodeName(node.Name), err)
		return err
	}

	return nil
}
