package logger

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

func init() {
	// init log in bce-sdk-go
	logger.SetLogger(logAdapter{})
}

// LogAdapter adapts our logger to bce-sdk-go logger
type logAdapter struct{}

func (logAdapter) Infof(ctx context.Context, format string, a ...interface{}) {
	Infof(ctx, format, a...)
}

// Warn log
func (logAdapter) Warnf(ctx context.Context, format string, a ...interface{}) {
	Warningf(ctx, format, a...)
}

// Error log
func (logAdapter) Errorf(ctx context.Context, format string, a ...interface{}) {
	Errorf(ctx, format, a...)
}
