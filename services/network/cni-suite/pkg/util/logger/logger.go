package logger

import (
	"context"
	"fmt"

	"k8s.io/klog/v2"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

func Info(ctx context.Context, args ...interface{}) {
	prefix := buildFormat(ctx, "")
	klog.InfoDepth(1, prefix+fmt.Sprint(args...))
}

func Infof(ctx context.Context, format string, args ...interface{}) {
	format = buildFormat(ctx, format)
	klog.InfoDepth(1, fmt.Sprintf(format, args...))
}

func Warning(ctx context.Context, args ...interface{}) {
	prefix := buildFormat(ctx, "")
	klog.WarningDepth(1, prefix+fmt.Sprint(args...))
}

func Warningf(ctx context.Context, format string, args ...interface{}) {
	format = buildFormat(ctx, format)
	klog.WarningDepth(1, fmt.Sprintf(format, args...))
}

func Error(ctx context.Context, args ...interface{}) {
	prefix := buildFormat(ctx, "")
	klog.ErrorDepth(1, prefix+fmt.Sprint(args...))
}

func Errorf(ctx context.Context, format string, args ...interface{}) {
	format = buildFormat(ctx, format)
	klog.ErrorDepth(1, fmt.Sprintf(format, args...))
}

func Fatal(ctx context.Context, args ...interface{}) {
	prefix := buildFormat(ctx, "")
	klog.FatalDepth(1, prefix+fmt.Sprint(args...))
}

func Fatalf(ctx context.Context, format string, args ...interface{}) {
	format = buildFormat(ctx, format)
	klog.FatalDepth(1, fmt.Sprintf(format, args...))
}

func buildFormat(ctx context.Context, format string) string {
	if ctx != nil {
		if requestID := ctx.Value(logger.RequestID); requestID != nil {
			format = "[" + (string)(logger.RequestID) + ": " + ctx.Value(logger.RequestID).(string) + "] " + format
		}
	}
	return format
}

func NewContext() context.Context {
	requestID := logger.GetUUID()
	ctx := context.WithValue(context.TODO(), logger.RequestID, requestID)
	return ctx
}
