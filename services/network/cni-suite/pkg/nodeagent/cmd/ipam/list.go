package ipam

import (
	"fmt"
	"github.com/spf13/cobra"
)

func newListCommand() *cobra.Command {
	cmd := &cobra.Command{
		Use:   "list",
		Short: "list IP status on this node",
		RunE: func(cmd *cobra.Command, args []string) error {
			fmt.Printf("list ipam runs...")
			return nil
		},
	}

	cmd.Flags().StringP("type", "", "secondary-ip", "type of IPAM, 'host-local' or 'secondary-ip'")

	return cmd
}
