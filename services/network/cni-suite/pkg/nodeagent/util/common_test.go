package util

import "testing"

func TestGetIPPoolName(t *testing.T) {
	type args struct {
		nodeName string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		{
			args: args{nodeName: "**********"},
			want: "ippool-10-0-3-187",
		},
		{
			args: args{nodeName: "kube135"},
			want: "ippool-kube135",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := GetIPPoolName(tt.args.nodeName); got != tt.want {
				t.Errorf("GetIPPoolName() = %v, want %v", got, tt.want)
			}
		})
	}
}
