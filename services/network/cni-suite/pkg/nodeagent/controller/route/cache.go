package route

import (
	"sync"
)

type cachedStaticRoute struct {
	Dst     string
	Gateway string
}

type StaticRouteCache struct {
	lock     sync.Mutex
	routeMap map[string]*cachedStaticRoute
}

func (c *StaticRouteCache) get(key string) (*cachedStaticRoute, bool) {
	c.lock.Lock()
	defer c.lock.Unlock()

	rt, ok := c.routeMap[key]
	return rt, ok
}

func (c *StaticRouteCache) set(key string, rt *cachedStaticRoute) {
	c.lock.Lock()
	defer c.lock.Unlock()

	c.routeMap[key] = rt
}

func (c *StaticRouteCache) delete(key string) {
	c.lock.Lock()
	defer c.lock.Unlock()

	delete(c.routeMap, key)
}
