package eni

import (
	"context"
	"fmt"
	"net"
	"regexp"
	"sort"
	"strconv"
	"time"

	"github.com/vishvananda/netlink"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	utilerrors "k8s.io/apimachinery/pkg/util/errors"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/util/retry"

	enisdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eni"
	meta_data "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/meta-data"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	clientset "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/nodeagent/util"
	k8sutil "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/k8s"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
	netlinkwrapper "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/netlink"
)

const (
	eniAttachTimeout  int           = 50
	eniAttachMaxRetry int           = 10
	eniReadyTime      time.Duration = 5 * time.Second

	fromENIPrimaryIPRulePriority = 1024
)

var (
	eniNameMatcher = regexp.MustCompile(`eth(\d+)`)
)

// Controller manages ENI at node level
type Controller struct {
	cloudClient   cloud.Interface
	metaClient    meta_data.Interface
	kubeClient    kubernetes.Interface
	ipPoolClient  clientset.Interface
	netlink       netlinkwrapper.Interface
	clusterID     string
	nodeName      string
	ipPoolName    string
	instanceID    string
	vpcID         string
	rtTableOffset int
	eniSyncPeriod time.Duration
}

// New creates ENI Controller
func New(
	cloudClient cloud.Interface,
	metaClient meta_data.Interface,
	kubeClient kubernetes.Interface,
	ippoolClient clientset.Interface,
	clusterID string,
	nodeName string,
	ippoolName string,
	instanceID string,
	vpcID string,
	rtTableOffset int,
	eniSyncPeriod time.Duration,
) *Controller {
	c := &Controller{
		cloudClient:   cloudClient,
		metaClient:    metaClient,
		kubeClient:    kubeClient,
		ipPoolClient:  ippoolClient,
		netlink:       netlinkwrapper.New(),
		clusterID:     clusterID,
		nodeName:      nodeName,
		ipPoolName:    ippoolName,
		instanceID:    instanceID,
		vpcID:         vpcID,
		rtTableOffset: rtTableOffset,
		eniSyncPeriod: eniSyncPeriod,
	}

	return c
}

func (c *Controller) ReconcileENIs() {
	err := wait.PollImmediateInfinite(c.eniSyncPeriod, func() (bool, error) {
		ctx := log.NewContext()

		err := c.reconcileENIs(ctx)
		if err != nil {
			log.Errorf(ctx, "error reconciling enis: %v", err)
		}

		// only update when eni ready, never taint node
		if err == nil {
			patchErr := k8sutil.UpdateNetworkingCondition(
				ctx,
				c.kubeClient,
				c.nodeName,
				true,
				"AllENIReady",
				"NotAllENIReady",
				"CCE ENI Controller reconciles ENI",
				"CCE ENI Controller failed to reconcile ENI",
			)

			if patchErr != nil {
				log.Errorf(ctx, "eni: update networking condition for node %v error: %v", c.nodeName, patchErr)
			}
		}

		return false, nil
	})
	if err != nil {
		log.Errorf(context.TODO(), "failed to sync reconcile enis: %v", err)
	}
}

func (c *Controller) reconcileENIs(ctx context.Context) error {
	log.Infof(ctx, "reconcile enis for %v begins...", c.nodeName)
	defer log.Infof(ctx, "reconcile enis for %v ends...", c.nodeName)

	ippool, err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Get(ctx, c.ipPoolName, metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get ippool %v: %v", c.ipPoolName, err)
		return err
	}

	err = c.waitForUnstableENIWithTimeout(ctx)
	if err != nil {
		return err
	}

	// list all enis in vpc
	enis, err := c.cloudClient.ListENIs(ctx, c.vpcID)
	if err != nil {
		log.Errorf(ctx, "failed to list enis in vpc %s: %v", c.vpcID, err)
		return err
	}

	attachedENIs, err := c.listAttachedENIs(ctx, enis)
	if err != nil {
		log.Warningf(ctx, "failed to list attached ENIs: %v", err)
		return nil
	}
	reusableENIs := c.listReusableENIs(ctx, enis)
	log.Infof(ctx, "node %v has %d attached enis, %d reusable enis before creating new enis", c.nodeName, len(attachedENIs), len(reusableENIs))

	eniShort := ippool.Spec.ENI.MaxENINum - len(attachedENIs)
	log.Infof(ctx, "node %v can have up to %d enis due to spec, thus the shortage of enis is %d before reuse", c.nodeName, ippool.Spec.ENI.MaxENINum, eniShort)
	if eniShort < 0 {
		err := fmt.Errorf("decreasing eni num from %d to %d is not permitted", len(attachedENIs), ippool.Spec.ENI.MaxENINum)
		log.Error(ctx, err.Error())
	}

	enisToCreate, err := c.reuseAvailableENI(ctx, eniShort, reusableENIs)
	if err != nil {
		log.Errorf(ctx, "failed to reuse available enis: %v", err)
		return err
	}
	log.Infof(ctx, "reuse available enis successfully, will create %d new enis after reuse", enisToCreate)

	// create eni
	for i := 0; i < enisToCreate; i++ {
		log.Infof(ctx, "progress(%d/%d): allocate eni starts...", i+1, enisToCreate)
		// list all enis in vpc
		enis, err = c.cloudClient.ListENIs(ctx, c.vpcID)
		if err != nil {
			log.Errorf(ctx, "failed to list enis in vpc %s: %v", c.vpcID, err)
			return err
		}
		// list pools to find candidate subnets
		subnets, err := c.findAllCandidateSubnetsOfNode(ctx)
		if err != nil {
			log.Errorf(ctx, "failed to find all candidates subnets: %v", err)
			return err
		}

		// evenly spread enis to all subnets
		subnet, err := c.findSubnetWithLeastENI(ctx, subnets, enis)
		if err != nil {
			log.Errorf(ctx, "failed to find subnet to create eni: %v", err)
			return err
		}
		log.Infof(ctx, "subnet %v is chosen to create a new eni", subnet)
		eniID, err := c.allocateENI(ctx, subnet, ippool.Spec.ENI.SecurityGroups)
		if err != nil {
			log.Errorf(ctx, "failed to allocate(create and attach) new eni: %v", err)
			return err
		}
		log.Infof(ctx, "progress(%d/%d): allocate(create and attach) eni %v successfully", i+1, enisToCreate, eniID)
	}

	// list all enis in vpc
	enis, err = c.cloudClient.ListENIs(ctx, c.vpcID)
	if err != nil {
		log.Errorf(ctx, "failed to list enis in vpc %s: %v", c.vpcID, err)
		return err
	}

	// list available enis
	availableENIs := c.listReusableENIs(ctx, enis)
	if len(availableENIs) != 0 {
		log.Infof(ctx, "node %v has %d available enis", c.nodeName, len(availableENIs))
		// free leaked available enis after sleep
		err = c.freeLeakedAvailableENIs(ctx, availableENIs)
		if err != nil {
			log.Errorf(ctx, "failed to free all available enis: %v", err)
		}
	}

	// update cr status and ip link up and add addr
	err = c.updateIPPoolStatus(ctx, enis)
	if err != nil {
		log.Errorf(ctx, "failed to update ippool %v status: %v", c.ipPoolName, err)
		return err
	}

	return nil
}

// findAllCandidateSubnetsOfNode find largest priority pools to support dynamic config
func (c *Controller) findAllCandidateSubnetsOfNode(ctx context.Context) ([]v1alpha1.Subnet, error) {
	// list all pools
	pools, err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to list all pools: %v", err)
		return nil, err
	}

	node, err := c.kubeClient.CoreV1().Nodes().Get(ctx, c.nodeName, metav1.GetOptions{})
	if err != nil {
		log.Errorf(ctx, "failed to get node %v: %v", c.nodeName, err)
		return nil, err
	}

	// find all matched pools
	matchedPools, err := util.GetMatchedIPPoolsByNode(node, pools.Items)
	if err != nil {
		return nil, err
	}
	log.Infof(ctx, "list all matched pools: %v", poolNameList(matchedPools))

	// sort matched pools by priority
	sort.Slice(matchedPools, func(i, j int) bool {
		return matchedPools[i].Spec.Priority > matchedPools[j].Spec.Priority
	})

	// find matched pools with largest priority.
	// if multiple pools has the same priority, they are all selected out.
	priorPools := findLargestPriorityPools(matchedPools)
	if len(priorPools) > 0 {
		log.Infof(ctx, "list matched pools with largest priority %v: %v", priorPools[0].Spec.Priority, poolNameList(priorPools))
	}

	var result []v1alpha1.Subnet

	// aggregate subnets
	for _, pool := range priorPools {
		for _, s := range pool.Spec.ENI.Subnets {
			result = append(result, s)
		}
	}

	return result, nil
}

func poolNameList(pools []v1alpha1.IPPool) []string {
	var result []string
	for _, pool := range pools {
		result = append(result, pool.Name)
	}
	return result
}

// TODO: this can be optimized by binary search
func findLargestPriorityPools(pools []v1alpha1.IPPool) []v1alpha1.IPPool {
	if len(pools) == 0 {
		return nil
	}

	// pools have been sorted, search from tail to head
	maxPriority := pools[0].Spec.Priority
	for i := len(pools) - 1; i >= 0; i-- {
		if pools[i].Spec.Priority == maxPriority {
			return pools[:i+1]
		}
	}

	return []v1alpha1.IPPool{pools[0]}
}

// freeLeakedAvailableENIs frees ENIs that are available and owned by this node
func (c *Controller) freeLeakedAvailableENIs(ctx context.Context, enis []*enisdk.ENI) error {
	var errs []error
	for _, eni := range enis {
		// check eni status before delete
		resp, err := c.cloudClient.StatENI(ctx, eni.ENIID)
		if err != nil {
			log.Errorf(ctx, "failed to stat eni %v: %v", eni.ENIID, err)
			continue
		}
		if resp.Status != string(enisdk.ENIStatusAvailable) {
			continue
		}

		err = c.cloudClient.DeleteENI(ctx, eni.ENIID)
		if err != nil {
			log.Errorf(ctx, "failed to delete eni %v: %v", eni.ENIID, err)
			errs = append(errs, err)
		} else {
			log.Infof(ctx, "free leaked eni %v successfully", eni.ENIID)
		}
	}

	return utilerrors.NewAggregate(errs)
}

func (c *Controller) waitForUnstableENIWithTimeout(ctx context.Context) error {
	sleepTime := time.Duration(eniAttachTimeout/eniAttachMaxRetry) * time.Second

	for i := 0; i < eniAttachMaxRetry; i++ {
		// list all enis in vpc
		enis, err := c.cloudClient.ListENIs(ctx, c.vpcID)
		if err != nil {
			log.Errorf(ctx, "waitForUnstableENIWithTimeout tries %d time: failed to list enis in vpc %s: %v", i, c.vpcID, err)
			time.Sleep(sleepTime)
			continue
		}

		allENIStable := true

		for _, eni := range enis {
			// if eni not owned by local node, just ignore
			if !util.ENIOwnedByNode(eni, c.clusterID, c.instanceID) {
				continue
			}

			if eni.Status == enisdk.ENIStatusAttaching || eni.Status == enisdk.ENIStatusDetaching {
				allENIStable = false
				log.Infof(ctx, "waitForUnstableENIWithTimeout tries %d time: eni %s is in status %s", i, eni.ENIID, eni.Status)
			}
		}

		if allENIStable {
			log.Info(ctx, "waitForUnstableENIWithTimeout: all enis are stable")
			return nil
		}

		time.Sleep(sleepTime)
	}

	// attach timeout
	log.Errorf(ctx, "waitForUnstableENIWithTimeout: eni in attaching/detaching status too long")
	return fmt.Errorf("eni in attaching/detaching status too long")
}

func (c *Controller) listAttachedENIs(ctx context.Context, enis []*enisdk.ENI) ([]*enisdk.ENI, error) {
	attachedENIs := []*enisdk.ENI{}

	// find out attached enis
	for _, eni := range enis {
		// eni openapi may return with Status inuse but InstanceID empty.
		// we cannot handle this kind of situation
		if eni.Status == enisdk.ENIStatusInuse && eni.InstanceID == "" {
			return nil, fmt.Errorf("invalid eni response: instanceID is empty")
		}
		if eni.Status == enisdk.ENIStatusInuse && eni.InstanceID == c.instanceID {
			attachedENIs = append(attachedENIs, eni)
		}
	}

	return attachedENIs, nil
}

func (c *Controller) listReusableENIs(ctx context.Context, enis []*enisdk.ENI) []*enisdk.ENI {
	reusableENIs := []*enisdk.ENI{}

	// find out available enis
	for _, eni := range enis {
		if eni.Status == enisdk.ENIStatusAvailable && util.ENIOwnedByNode(eni, c.clusterID, c.instanceID) {
			reusableENIs = append(reusableENIs, eni)
		}
	}

	return reusableENIs
}

// reuseAvailableENI attaches reusable enis or delete redundant enis
func (c *Controller) reuseAvailableENI(ctx context.Context, eniShort int, reusableENIs []*enisdk.ENI) (int, error) {
	var newENIsToCreate int
	// still needs to create new enis besides reusable enis
	if eniShort >= len(reusableENIs) {
		newENIsToCreate = eniShort - len(reusableENIs)
		for _, eni := range reusableENIs {
			err := c.attachENIWithTimeout(ctx, eni.ENIID)
			if err != nil {
				log.Errorf(ctx, "reuse: failed to attach eni %v: %v", eni.ENIID, err)
				return 0, err
			} else {
				log.Infof(ctx, "reuse: attach eni %v successfully", eni.ENIID)
			}
		}
		return newENIsToCreate, nil
	}

	if eniShort >= 0 {
		// many available enis, will delete some
		for _, eni := range reusableENIs[eniShort:] {
			// try to delete useless eni
			err := c.cloudClient.DeleteENI(ctx, eni.ENIID)
			if err != nil {
				log.Errorf(ctx, "reuse: failed to delete eni %v: %v", eni.ENIID, err)
			} else {
				log.Infof(ctx, "reuse: delete eni %v successfully", eni.ENIID)
			}
		}

		for _, eni := range reusableENIs[:eniShort] {
			err := c.attachENIWithTimeout(ctx, eni.ENIID)
			if err != nil {
				log.Errorf(ctx, "reuse: failed to attach eni %v: %v", eni.ENIID, err)
				return 0, err
			} else {
				log.Infof(ctx, "reuse: attach eni %v successfully", eni.ENIID)
			}
		}
	}

	return 0, nil
}

func (c *Controller) attachENIWithTimeout(ctx context.Context, eniID string) error {
	start := time.Now()
	log.Infof(ctx, "attach eni %v to instance %v begins...", eniID, c.instanceID)
	defer log.Infof(ctx, "attach eni %v to instance %v ends...", eniID, c.instanceID)

	err := c.cloudClient.AttachENI(ctx, &enisdk.AttachENIArgs{
		InstanceID: c.instanceID,
		ENIID:      eniID,
	})
	if err != nil {
		log.Errorf(ctx, "failed to attach eni %v to instance %v: %v", eniID, c.instanceID, err)
		_ = c.rollbackFailedENI(ctx, eniID)
		return err
	}
	log.Infof(ctx, "request of attaching eni %v to instance %v is sent successfully", eniID, c.instanceID)

	// stat eni synchronously to check status
	sleepTime := time.Duration(eniAttachTimeout/eniAttachMaxRetry) * time.Second

	for i := 0; i < eniAttachMaxRetry; i++ {
		statResp, err := c.cloudClient.StatENI(ctx, eniID)
		if err != nil {
			log.Errorf(ctx, "failed to stat eni %v while attaching: %v", eniID, err)
			time.Sleep(sleepTime)
			continue
		}

		log.Infof(ctx, "attempt(%d/%d) to stat eni %v, current status: %v, time consuming: %f s", i+1, eniAttachMaxRetry, eniID, statResp.Status, time.Since(start).Seconds())

		if statResp.Status == string(enisdk.ENIStatusInuse) {
			goto success
		}
		if statResp.Status == string(enisdk.ENIStatusAvailable) || statResp.Status == string(enisdk.ENIStatusDetaching) {
			log.Errorf(ctx, "failed to attach eni %v while attaching: %v", eniID, err)
			_ = c.rollbackFailedENI(ctx, eniID)
			return err
		}
		time.Sleep(sleepTime)
	}
	// attach eni timeout
	log.Errorf(ctx, "failed to attach eni %v due to timeout", eniID)
	_ = c.rollbackFailedENI(ctx, eniID)
	return fmt.Errorf("failed to attach eni %v due to timeout", eniID)

success:
	log.Infof(ctx, "attach eni %v to instance %v successfully", eniID, c.instanceID)
	return nil
}

func (c *Controller) rollbackFailedENI(ctx context.Context, eniID string) error {
	log.Infof(ctx, "rollback: delete failed eni %v begins...", eniID)
	defer log.Infof(ctx, "rollback: delete failed eni %v ends...", eniID)

	err := c.cloudClient.DeleteENI(ctx, eniID)
	if err != nil {
		log.Errorf(ctx, "resource may leak, failed to delete eni %v: %v", eniID, err)
		return err
	}
	return nil
}

func (c *Controller) allocateENI(ctx context.Context, subnetID string, SecurityGroupIDs []string) (string, error) {
	start := time.Now()
	log.Infof(ctx, "allocate eni for node %v begins...", c.nodeName)
	defer log.Infof(ctx, "allocate eni for node %v ends...", c.nodeName)

	eniName := util.CreateNameForENI(c.clusterID, c.instanceID, c.nodeName)
	createENIArgs := &enisdk.CreateENIArgs{
		Name:             eniName,
		SubnetID:         subnetID,
		SecurityGroupIDs: SecurityGroupIDs,
		PrivateIPSet: []*enisdk.PrivateIP{{
			Primary:          true,
			PrivateIPAddress: "",
		}},
		Description: "auto created by cce-cni, do not modify",
	}

	eniID, err := c.cloudClient.CreateENI(ctx, createENIArgs)
	if err != nil {
		log.Errorf(ctx, "failed to create eni: %v", err)
		return "", err
	}
	log.Infof(ctx, "create eni %v for node %v successfully", eniID, c.nodeName)
	// wait for eni ready, or attach may fail
	time.Sleep(eniReadyTime)

	err = c.attachENIWithTimeout(ctx, eniID)
	if err != nil {
		log.Errorf(ctx, "failed to attach eni %v: %v", eniID, err)
		return eniID, err
	}

	log.Infof(ctx, "allocate eni %v for node %v successfully, takes %f s to finish", eniID, c.nodeName, time.Since(start).Seconds())
	return eniID, nil
}

func (c *Controller) findSubnetWithLeastENI(ctx context.Context, subnets []v1alpha1.Subnet, enis []*enisdk.ENI) (string, error) {
	if len(subnets) == 0 {
		return "", fmt.Errorf("eni subnets cannot be empty")
	}

	// filter out enabled subnets
	var enabledSubnets []string
	for _, subnet := range subnets {
		if subnet.Enable {
			enabledSubnets = append(enabledSubnets, subnet.ID)
		}
	}
	if len(enabledSubnets) == 0 {
		return "", fmt.Errorf("enabled eni subnets cannot be empty")
	}

	// start to find
	candidate := enabledSubnets[0]
	spreadResult := map[string]int{}

	for _, subnet := range enabledSubnets {
		spreadResult[subnet] = 0
	}

	for _, eni := range enis {
		if !util.ENIOwnedByNode(eni, c.clusterID, c.instanceID) {
			continue
		}
		_, ok := spreadResult[eni.SubnetID]
		if ok {
			spreadResult[eni.SubnetID]++
		} else {
			log.Warningf(ctx, "eni %v in subnet %v is not in candidate subnets: %v", eni.ENIID, eni.SubnetID, subnets)
		}
	}

	log.Infof(ctx, "eni spread result of node %v: %+v", c.nodeName, spreadResult)

	// find the least one
	for subnet, cnt := range spreadResult {
		if cnt < spreadResult[candidate] {
			candidate = subnet
		}
	}

	return candidate, nil
}

func (c *Controller) updateIPPoolStatus(ctx context.Context, enis []*enisdk.ENI) error {
	eniStatus := map[string]v1alpha1.ENI{}

	for _, eni := range enis {
		if !util.ENIOwnedByNode(eni, c.clusterID, c.instanceID) || eni.Status != enisdk.ENIStatusInuse {
			continue
		}

		linkIndex, err := c.setupENILink(ctx, eni)
		if err != nil {
			log.Errorf(ctx, "failed to ensure eni %v ready: %v", eni.ENIID, err)
		}
		eniStatus[eni.ENIID] = v1alpha1.ENI{
			ID:               eni.ENIID,
			MAC:              eni.MacAddress,
			AvailabilityZone: eni.ZoneName,
			Description:      eni.Description,
			InterfaceIndex:   linkIndex,
			Subnet:           eni.SubnetID,
			PrivateIPSet:     util.GetPrivateIPSet(eni),
			VPC:              eni.VPCID,
		}
	}

	retryErr := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		result, err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Get(ctx, c.ipPoolName, metav1.GetOptions{})
		if err != nil {
			log.Errorf(ctx, "failed to get ippool %v: %v", c.ipPoolName, err)
			return err
		}
		result.Status.ENI.ENIs = eniStatus

		// 兼容存量 IPPool 没有添加 NodeSelector 字段的情况
		if result.Spec.NodeSelector == "" {
			result.Spec.NodeSelector = fmt.Sprintf("kubernetes.io/hostname=%s", c.nodeName)
		}

		_, updateErr := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Update(ctx, result, metav1.UpdateOptions{})
		if updateErr != nil {
			log.Errorf(ctx, "error updating ippool %v status: %v", c.ipPoolName, updateErr)
			return updateErr
		}
		return nil
	})

	if retryErr != nil {
		log.Errorf(ctx, "retry: error updating ippool %v status: %v", c.ipPoolName, retryErr)
		return retryErr
	}

	return nil
}

func (c *Controller) setupENILink(ctx context.Context, eni *enisdk.ENI) (int, error) {
	eniIntf, err := c.findENILinkByMac(ctx, eni)
	if err != nil {
		return -1, err
	}
	eniIndex := eniIntf.Attrs().Index

	err = c.setLinkUP(ctx, eniIntf)
	if err != nil {
		return eniIndex, err
	}

	err = c.addPrimaryIP(ctx, eniIntf, getPrimaryIP(eni))
	if err != nil {
		return eniIndex, err
	}

	err = c.delScopeLinkRoute(ctx, eniIntf)
	if err != nil {
		return eniIndex, err
	}

	err = c.addFromENIRule(ctx, eni, eniIntf)
	if err != nil {
		log.Errorf(ctx, "failed to add from eni primary ip rule: %v", err)
		return eniIndex, err
	}

	return eniIndex, nil
}

func (c *Controller) setLinkUP(ctx context.Context, intf netlink.Link) error {
	if intf.Attrs().Flags&net.FlagUp != 0 {
		return nil
	}
	// if link is down, set link up
	log.Warningf(ctx, "link %v is down, will bring it up", intf.Attrs().Name)
	err := c.netlink.LinkSetUp(intf)
	if err != nil {
		return err
	}

	return nil
}

func (c *Controller) delScopeLinkRoute(ctx context.Context, intf netlink.Link) error {
	addrs, err := netlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		return err
	}

	for _, addr := range addrs {
		dst := net.IPNet{
			IP:   addr.IP.Mask(addr.Mask),
			Mask: addr.Mask,
		}
		err = netlink.RouteDel(&netlink.Route{
			Dst:       &dst,
			Scope:     netlink.SCOPE_LINK,
			LinkIndex: intf.Attrs().Index,
		})
		if err != nil && !netlinkwrapper.IsNotExistsError(err) {
			return err
		}
	}

	return nil
}

func (c *Controller) findENILinkByMac(ctx context.Context, eni *enisdk.ENI) (netlink.Link, error) {
	var eniIntf netlink.Link
	// list all interfaces, and find ENI by mac address
	interfaces, err := c.netlink.LinkList()
	if err != nil {
		return nil, fmt.Errorf("failed to list interfaces: %v", interfaces)
	}

	for _, intf := range interfaces {
		if intf.Attrs().HardwareAddr.String() == eni.MacAddress {
			eniIntf = intf
			break
		}
	}

	if eniIntf == nil {
		return nil, fmt.Errorf("eni with mac address %v not found", eni.MacAddress)
	}

	return eniIntf, nil
}

func (c *Controller) addFromENIRule(ctx context.Context, eni *enisdk.ENI, intf netlink.Link) error {
	primaryIP := getPrimaryIP(eni)
	if primaryIP == "" {
		return fmt.Errorf("no primary ip found")
	}

	matches := eniNameMatcher.FindStringSubmatch(intf.Attrs().Name)
	if len(matches) != 2 {
		return fmt.Errorf("invalid eni name: %v", intf.Attrs().Name)
	}
	ethID, err := strconv.ParseInt(matches[1], 10, 32)
	if err != nil {
		return fmt.Errorf("error parsing eni link index: %v", err)
	}
	rtTable := c.rtTableOffset + int(ethID)

	// ip rule add from {primaryIP} lookup {rtTable} prio 1024
	rule := c.netlink.NewRule()
	rule.Src = &net.IPNet{
		IP:   net.ParseIP(primaryIP),
		Mask: net.CIDRMask(32, 32),
	}
	rule.Table = rtTable
	rule.Priority = fromENIPrimaryIPRulePriority
	existed, err := c.isRuleExisted(rule)
	if err != nil {
		return err
	}
	if !existed {
		if err := c.netlink.RuleAdd(rule); err != nil && !netlinkwrapper.IsExistsError(err) {
			log.Errorf(ctx, "failed to add rule %+v: %v", *rule, err)
			return err
		}
	}

	var gateway net.IP
	defautRt, err := c.findLinkDefaultRoute(ctx, intf)
	if err != nil {
		log.Errorf(ctx, "failed to get gateway of eni %v from link default route: %v", eni.ENIID, err)
		log.Infof(ctx, "fall back to get gateway of eni %v from meta-data", eni.ENIID)
		// fallback to meta-data api
		gw, err := c.getLinkGateway(ctx, eni)
		if err != nil {
			log.Errorf(ctx, "failed to get gateway of eni %v from meta-data: %v", eni.ENIID, err)
			return err
		}
		gateway = gw
	} else {
		gateway = defautRt.Gw
	}

	log.Infof(ctx, "eni %v with primary IP %v has gateway: %v", eni.ENIID, primaryIP, gateway)
	_, dstNet, _ := net.ParseCIDR("0.0.0.0/0")
	// ip route add default via {eniGW} dev ethX table {rtTable} onlink
	rt := &netlink.Route{
		LinkIndex: intf.Attrs().Index,
		Dst:       dstNet,
		Table:     rtTable,
		Gw:        gateway,
	}
	rt.SetFlag(netlink.FLAG_ONLINK)

	err = c.netlink.RouteReplace(rt)
	if err != nil {
		return fmt.Errorf("failed to replace default route %+v in table %v: %v", *rt, rtTable, err)
	}

	return nil
}

// addPrimaryIP add eni primary IP to
func (c *Controller) addPrimaryIP(ctx context.Context, intf netlink.Link, primaryIP string) error {
	addrs, err := c.netlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		log.Errorf(ctx, "failed to list addresses of link %v: %v", intf.Attrs().Name, err)
		return err
	}

	for _, addr := range addrs {
		// primary IP already on link
		if addr.IP.String() == primaryIP {
			return nil
		}
	}

	log.Infof(ctx, "start to add primary IP %v to link %v", primaryIP, intf.Attrs().Name)
	// mask is in format like *************
	mask, err := c.metaClient.GetLinkMask(intf.Attrs().HardwareAddr.String(), primaryIP)
	if err != nil {
		log.Errorf(ctx, "failed to get link %v mask: %v", intf.Attrs().Name, err)
		return err
	}
	addr := &netlink.Addr{
		IPNet: &net.IPNet{
			IP:   net.ParseIP(primaryIP),
			Mask: net.IPMask(net.ParseIP(mask).To4()),
		},
	}

	err = c.netlink.AddrAdd(intf, addr)
	if err != nil && !netlinkwrapper.IsExistsError(err) {
		log.Errorf(ctx, "failed to add primary IP %v to link %v", addr.String(), intf.Attrs().Name)
		return err
	}

	log.Infof(ctx, "add primary IP %v to link %v successfully", addr.String(), intf.Attrs().Name)
	return nil
}

// findLinkDefaultRoute equals ip r show dev {dev}
func (c *Controller) findLinkDefaultRoute(ctx context.Context, intf netlink.Link) (*netlink.Route, error) {
	// ip route show dev ethX
	eniRoutes, err := c.netlink.RouteList(intf, netlink.FAMILY_V4)
	if err != nil {
		return nil, fmt.Errorf("failed to list dev %v routes: %v", intf.Attrs().Name, err)
	}
	// find eni default route
	for _, r := range eniRoutes {
		if r.Dst == nil || r.Dst.String() == "0.0.0.0/0" {
			return &r, nil
		}
	}

	return nil, fmt.Errorf("default route not found")
}

func (c *Controller) isRuleExisted(rule *netlink.Rule) (bool, error) {
	rules, err := c.netlink.RuleList(netlink.FAMILY_V4)
	if err != nil {
		return false, err
	}

	for _, r := range rules {
		if r.Src != nil && rule.Src != nil {
			if r.Priority == rule.Priority && r.Table == rule.Table && r.Src.String() == rule.Src.String() {
				return true, nil
			}
		}
	}

	return false, nil
}

func (c *Controller) getLinkGateway(ctx context.Context, eni *enisdk.ENI) (net.IP, error) {
	gateway, err := c.metaClient.GetLinkGateway(eni.MacAddress, getPrimaryIP(eni))
	if err != nil {
		return nil, err
	}

	gw := net.ParseIP(gateway)
	if gw == nil {
		return nil, fmt.Errorf("error parsing gateway IP address: %v", gateway)
	}
	return gw, nil
}

func getPrimaryIP(eni *enisdk.ENI) string {
	for _, ip := range eni.PrivateIPSet {
		if ip.Primary {
			return ip.PrivateIPAddress
		}
	}
	return ""
}
