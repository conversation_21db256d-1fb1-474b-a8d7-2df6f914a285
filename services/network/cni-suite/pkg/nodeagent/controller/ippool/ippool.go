package ippool

import (
	"context"
	"fmt"
	"strings"

	v1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	corelisters "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/util/retry"

	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/bce/cloud"
	clientset "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/nodeagent/util"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

// Controller manipulates IPPool CRDs
type Controller struct {
	// Common
	kubeClient   kubernetes.Interface
	ipPoolClient clientset.Interface
	ipPoolName   string
	cniMode      ccetypes.ContainerNetworkMode
	instanceID   string
	nodeName     string
	// ENI
	cloudClient      cloud.Interface
	availabilityZone string // node azone
	maxENI           int
	maxIPPerENI      int
	subnets          []string // cluster-level candidate subnets
	securityGroups   []string
	// Range
}

func New(
	kubeClient kubernetes.Interface,
	cloudClient cloud.Interface,
	ippoolClient clientset.Interface,
	cniMode ccetypes.ContainerNetworkMode,
	nodeName string,
	instanceID string,
	subnetList string,
	securityGroupList string,
	maxENI int,
	maxIPPerENI int,
) *Controller {
	ctx := log.NewContext()
	c := &Controller{
		kubeClient:   kubeClient,
		cloudClient:  cloudClient,
		ipPoolClient: ippoolClient,
		cniMode:      cniMode,
		nodeName:     nodeName,
		ipPoolName:   util.GetIPPoolName(nodeName),
		instanceID:   instanceID,
		maxENI:       maxENI,
		maxIPPerENI:  maxIPPerENI,
	}

	c.subnets = splitSubnets(subnetList)
	log.Infof(ctx, "cluster-level eni candidate subnets are: %v", c.subnets)

	c.securityGroups = splitSecurityGroups(securityGroupList)
	log.Infof(ctx, "security groups bound to eni are: %v", c.securityGroups)

	return c
}

func (c *Controller) SyncNode(nodeKey string, nodeLister corelisters.NodeLister) error {
	ctx := log.NewContext()

	isLocalNode := nodeKey == c.nodeName

	if isLocalNode {
		node, err := nodeLister.Get(nodeKey)
		if err != nil && !kerrors.IsNotFound(err) {
			return err
		}

		if node.Status.Phase == v1.NodeTerminated {
			log.Infof(ctx, "node %v is terminated", node.Name)
			return nil
		}

		// node exists, then ensure pool exists
		if err := c.createIPPool(ctx); err != nil {
			log.Errorf(ctx, "failed to create ippool %v: %v", c.ipPoolName, err)
			return err
		}

		switch {
		case utils.IsVPCSecondaryIPCNIMode(c.cniMode):
			return c.syncENISpec(ctx, nodeKey, nodeLister)
		case utils.IsVPCRouteCNIMode(c.cniMode):
			return c.syncRangeSpec(ctx, nodeKey, nodeLister)
		default:
			return fmt.Errorf("unknown cni mode: %v", c.cniMode)
		}
	}

	if !isLocalNode {
		// if node is deleted, then delete default pool
		_, err := nodeLister.Get(nodeKey)
		if kerrors.IsNotFound(err) {
			// clean up pool of deleted node
			poolName := util.GetIPPoolName(nodeKey)
			log.Errorf(ctx, "node %v is deleted, delete default ippool %v", nodeKey, poolName)
			if err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Delete(ctx, poolName, *metav1.NewDeleteOptions(0)); err != nil && !kerrors.IsNotFound(err) {
				log.Errorf(ctx, "failed to delete ippool %v: %v", poolName, err)
			}
		}
	}

	return nil
}

func (c *Controller) syncENISpec(ctx context.Context, nodeName string, nodeLister corelisters.NodeLister) error {
	log.Infof(ctx, "syncing eni spec of node %v begins...", nodeName)
	defer log.Infof(ctx, "syncing eni spec of node %v ends...", nodeName)

	instance, err := c.cloudClient.DescribeInstance(ctx, c.instanceID)
	if err != nil {
		log.Errorf(ctx, "failed to describe instance %v: %v", c.instanceID, err)
		return err
	}
	c.availabilityZone = instance.ZoneName

	retryErr := retry.RetryOnConflict(retry.DefaultRetry, func() error {
		result, err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Get(ctx, c.ipPoolName, metav1.GetOptions{})
		if err != nil {
			log.Errorf(ctx, "failed to get ippool %v: %v", c.ipPoolName, err)
			return err
		}

		result.Spec.ENI.InstanceID = c.instanceID
		result.Spec.ENI.InstanceType = string(instance.InstanceType)
		result.Spec.ENI.AvailabilityZone = instance.ZoneName
		result.Spec.ENI.VPCID = instance.VPCID
		// just ensure subnets are in the IPPool CRD, user can specify other subnets.
		result.Spec.ENI.Subnets = getNodeENISubnets(result.Spec.ENI.Subnets, c.pickSameZoneSubnets(ctx, c.subnets))
		result.Spec.ENI.SecurityGroups = c.securityGroups
		if c.maxENI != 0 {
			result.Spec.ENI.MaxENINum = c.maxENI
		} else {
			result.Spec.ENI.MaxENINum = util.GetMaxENIPerNode(instance.CPUCount)
		}

		if c.maxIPPerENI != 0 {
			result.Spec.ENI.MaxIPPerENI = c.maxIPPerENI
		} else {
			result.Spec.ENI.MaxIPPerENI = util.GetMaxIPPerENI(instance.MemoryCapacityInGB)
		}

		// 兼容存量 IPPool 没有添加 NodeSelector 字段的情况
		if result.Spec.NodeSelector == "" {
			result.Spec.NodeSelector = fmt.Sprintf("kubernetes.io/hostname=%s", c.nodeName)
		}

		_, updateErr := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Update(ctx, result, metav1.UpdateOptions{})
		if updateErr != nil {
			log.Errorf(ctx, "error updating ippool %v spec: %v", c.ipPoolName, updateErr)
			return updateErr
		}

		// validate
		if len(result.Spec.ENI.Subnets) == 0 {
			return fmt.Errorf("node-level eni candidate subnets cannot be empty")
		}

		if len(result.Spec.ENI.SecurityGroups) == 0 {
			return fmt.Errorf("security groups bound to eni cannot be empty")
		}

		return nil
	})

	if retryErr != nil {
		log.Errorf(ctx, "retry: error updating ippool %v spec: %v", c.ipPoolName, retryErr)
		return retryErr
	}

	log.Infof(ctx, "update ippool %v spec successfully", c.ipPoolName)
	return nil
}

// TODO:
func (c *Controller) syncRangeSpec(ctx context.Context, nodeName string, nodeLister corelisters.NodeLister) error {
	return nil
}

// createIPPool creates node-level IPPool CR
func (c *Controller) createIPPool(ctx context.Context) error {
	ippoolName := c.ipPoolName
	_, err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Get(ctx, ippoolName, metav1.GetOptions{})
	if err != nil {
		if !kerrors.IsNotFound(err) {
			return err
		}
		log.Infof(ctx, "ippool %s is not found, will create", ippoolName)

		ippool := &v1alpha1.IPPool{
			ObjectMeta: metav1.ObjectMeta{
				Name: ippoolName,
			},
			Spec: v1alpha1.IPPoolSpec{
				Nodes:        []string{c.nodeName},
				NodeSelector: fmt.Sprintf("kubernetes.io/hostname=%s", c.nodeName),
			},
		}
		if _, err := c.ipPoolClient.CceV1alpha1().IPPools(v1.NamespaceDefault).Create(ctx, ippool, metav1.CreateOptions{}); err != nil {
			return err
		}
		log.Infof(ctx, "ippool %s is created successfully", ippoolName)
	}
	log.Infof(ctx, "ippool %s is already existed, skip creating", ippoolName)
	return nil
}

// getNodeENISubnets 获取 node 的 eni 子网
func getNodeENISubnets(crdSubnets []v1alpha1.Subnet, subnets []string) []v1alpha1.Subnet {
	subnetMap := map[string]bool{}

	for _, s := range crdSubnets {
		subnetMap[s.ID] = s.Enable
	}

	for _, s := range subnets {
		_, ok := subnetMap[s]
		if !ok {
			subnetMap[s] = true
		}
	}

	// sum up
	var res []v1alpha1.Subnet
	for k, v := range subnetMap {
		res = append(res, v1alpha1.Subnet{
			ID:     k,
			Enable: v,
		})
	}
	return res
}

// pickSameZoneSubnets 从 cluster-level 的子网挑选和 node 同可用区的子网
func (c *Controller) pickSameZoneSubnets(ctx context.Context, subnets []string) []string {
	filteredSubnets := []string{}
	for _, s := range subnets {
		resp, err := c.cloudClient.DescribeSubnet(ctx, s)
		if err != nil {
			log.Errorf(ctx, "pickSameZoneSubnets: skip subnet %v due to describe error: %v", s, err)
			continue
		}
		if resp.ZoneName == c.availabilityZone {
			filteredSubnets = append(filteredSubnets, s)
			log.Infof(ctx, "add subnet %v at zone %v as node-level candidate", s, resp.ZoneName)
		}
	}

	return filteredSubnets
}

// splitSubnets is a helper function that works on a comma separated subnets and
// returns a list of unique subnets
func splitSubnets(subnetList string) []string {
	if subnetList == "" {
		return nil
	}
	subnetsSplit := strings.Split(strings.TrimSpace(subnetList), ",")
	return subnetsSplit
}

// splitSecurityGroups is a helper function that works on a comma separated securityGroups and
// returns a list of unique securityGroup
func splitSecurityGroups(securityGroupList string) []string {
	if securityGroupList == "" {
		return nil
	}
	securityGroupSplit := strings.Split(strings.TrimSpace(securityGroupList), ",")
	return securityGroupSplit
}
