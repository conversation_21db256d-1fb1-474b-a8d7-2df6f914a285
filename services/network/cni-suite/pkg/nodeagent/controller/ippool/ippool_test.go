package ippool

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
)

func Test_getNodeENISubnets(t *testing.T) {
	type args struct {
		crdSubnets []v1alpha1.Subnet
		subnets    []string
	}
	tests := []struct {
		name string
		args args
		want []v1alpha1.Subnet
	}{
		{
			args: args{
				crdSubnets: nil,
				subnets:    []string{"a"},
			},
			want: []v1alpha1.Subnet{
				{
					ID:     "a",
					Enable: true,
				},
			},
		},

		{
			args: args{
				crdSubnets: []v1alpha1.Subnet{
					{
						ID:     "b",
						Enable: false,
					},
				},
				subnets: []string{"a"},
			},
			want: []v1alpha1.Subnet{
				{
					ID:     "b",
					Enable: false,
				},
				{
					ID:     "a",
					Enable: true,
				},
			},
		},

		{
			args: args{
				crdSubnets: []v1alpha1.Subnet{
					{
						ID:     "a",
						Enable: false,
					},
					{
						ID:     "b",
						Enable: true,
					},
				},
				subnets: []string{"a"},
			},
			want: []v1alpha1.Subnet{
				{
					ID:     "a",
					Enable: false,
				},
				{
					ID:     "b",
					Enable: true,
				},
			},
		},

		{
			args: args{
				crdSubnets: []v1alpha1.Subnet{
					{
						ID:     "a",
						Enable: true,
					},
					{
						ID:     "b",
						Enable: true,
					},
				},
				subnets: []string{"a"},
			},
			want: []v1alpha1.Subnet{
				{
					ID:     "a",
					Enable: true,
				},
				{
					ID:     "b",
					Enable: true,
				},
			},
		},

		{
			args: args{
				crdSubnets: []v1alpha1.Subnet{
					{
						ID:     "a",
						Enable: false,
					},
					{
						ID:     "b",
						Enable: true,
					},
				},
				subnets: nil,
			},
			want: []v1alpha1.Subnet{
				{
					ID:     "a",
					Enable: false,
				},
				{
					ID:     "b",
					Enable: true,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getNodeENISubnets(tt.args.crdSubnets, tt.args.subnets)
			assert.ElementsMatch(t, got, tt.want)
		})
	}
}
