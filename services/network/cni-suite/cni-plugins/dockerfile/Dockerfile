FROM registry.baidubce.com/jpaas-public/debian-iptables-amd64:v12.0.1

LABEL maintainer="<PERSON><<EMAIL>>"

# ensure cni bin from https://github.com/containernetworking/plugins/releases/download/v0.8.5/cni-plugins-linux-amd64-v0.8.5.tgz
RUN curl -L -k --retry 5 https://github.com/containernetworking/plugins/releases/download/v0.8.5/cni-plugins-linux-amd64-v0.8.5.tgz | tar -xz -C / ./loopback ./host-local ./portmap

COPY ./install-cce-vpc-route-cni.sh /install-cce-vpc-route-cni.sh
COPY output/unnumbered-ptp /unnumbered-ptp
COPY output/ipvlan /ipvlan
COPY output/ptp /ptp
COPY output/eni-ipam /eni-ipam
COPY output/sysctl /sysctl


CMD ["/bin/sh", "/install-cce-vpc-route-cni.sh"]
