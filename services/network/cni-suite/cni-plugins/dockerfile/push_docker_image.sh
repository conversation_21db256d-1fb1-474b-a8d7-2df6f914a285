#!/bin/bash

if [ $# -ne 1 ]; then
    echo "USAGE: ${0} tag"
    exit -1
fi

tag=${1}


CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/unnumbered-ptp ../unnumbered-ptp && \
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/ipvlan ../ipvlan && \
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/ptp ../ptp && \
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/eni-ipam ../eni-ipam && \
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ./output/sysctl ../sysctl && \
docker build -t registry.baidubce.com/cce-plugin-pro/cce-vpc-route-cni:${tag} ./ && \
docker push registry.baidubce.com/cce-plugin-pro/cce-vpc-route-cni:${tag}
