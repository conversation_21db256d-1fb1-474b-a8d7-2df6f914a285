package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"os"
	"strings"

	"github.com/containernetworking/cni/pkg/skel"
	"github.com/containernetworking/cni/pkg/types"
	"github.com/containernetworking/cni/pkg/version"
	"github.com/containernetworking/plugins/pkg/ns"
	bv "github.com/containernetworking/plugins/pkg/utils/buildversion"
	"github.com/containernetworking/plugins/pkg/utils/sysctl"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/klog/v2"
)

const (
	logFile = "/var/log/cce/cni-sysctl.log"

	DefaultSysCtlAnnotationPrefix = "net.sysctl.cce.io"
)

// SysCtlConf represents the network sysctl configuration.
type SysCtlConf struct {
	types.NetConf

	SysCtl                 map[string]string `json:"sysctl"`
	KubeConfig             string            `json:"kubeconfig"`
	SysCtlAnnotationPrefix string            `json:"sysctlAnnotationPrefix"`
}

// K8SArgs k8s pod args
type K8SArgs struct {
	types.CommonArgs `json:"commonArgs"`
	// IP is pod's ip address
	IP net.IP `json:"ip"`
	// K8S_POD_NAME is pod's name
	K8S_POD_NAME types.UnmarshallableString `json:"k8s_pod_name"`
	// K8S_POD_NAMESPACE is pod's namespace
	K8S_POD_NAMESPACE types.UnmarshallableString `json:"k8s_pod_namespace"`
	// K8S_POD_INFRA_CONTAINER_ID is pod's container ID
	K8S_POD_INFRA_CONTAINER_ID types.UnmarshallableString `json:"k8s_pod_infra_container_id"`
}

func loadConf(data []byte) (*SysCtlConf, error) {
	conf := SysCtlConf{}
	if err := json.Unmarshal(data, &conf); err != nil {
		return nil, fmt.Errorf("failed to load netconf: %v", err)
	}

	if conf.SysCtlAnnotationPrefix == "" {
		conf.SysCtlAnnotationPrefix = DefaultSysCtlAnnotationPrefix
	}

	return &conf, nil
}

func loadK8SArgs(envArgs string) (*K8SArgs, error) {
	k8sArgs := K8SArgs{}
	if envArgs != "" {
		err := types.LoadArgs(envArgs, &k8sArgs)
		if err != nil {
			return nil, err
		}
	}
	return &k8sArgs, nil
}

func cmdAdd(args *skel.CmdArgs) error {
	klog.Infof("-----------cmdAdd begins----------")
	defer klog.Infof("-----------cmdAdd ends----------")
	klog.Infof("***** cmdAdd: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)
	klog.Infof("***** cmdAdd: stdinData: %v", string(args.StdinData))

	sysctlConf, err := loadConf(args.StdinData)
	if err != nil {
		return err
	}

	k8sArgs, err := loadK8SArgs(args.Args)
	if err != nil {
		return err
	}

	// parse previous result.
	if sysctlConf.RawPrevResult == nil {
		return fmt.Errorf("required prevResult is missing")
	}

	if err := version.ParsePrevResult(&sysctlConf.NetConf); err != nil {
		return err
	}

	// Users can pass sysctl params with cni conf and pod annotations if kubeconfig not empty.
	// Pod annotations will override params in cni conf.
	sysctlResult := map[string]string{}
	for k, v := range sysctlConf.SysCtl {
		sysctlResult[k] = v
	}

	name, namespace := string(k8sArgs.K8S_POD_NAME), string(k8sArgs.K8S_POD_NAMESPACE)
	if sysctlConf.KubeConfig != "" {
		kubeClient, err := newKubeClient(sysctlConf.KubeConfig)
		if err != nil {
			return fmt.Errorf("failed to create k8s client with kubeconfig %v: %v", sysctlConf.KubeConfig, err)
		}

		pod, err := kubeClient.CoreV1().Pods(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("failed to get pod (%v/%v): %v", namespace, name, err)
		}

		annotations := pod.Annotations
		klog.Infof("pod (%v/%v) has annotations: %+v", namespace, name, annotations)
		for k, v := range annotations {
			if isSysCtlKey(k, sysctlConf.SysCtlAnnotationPrefix) {
				sk := extractSysCtlKey(k, sysctlConf.SysCtlAnnotationPrefix)
				// update sysctlResult
				sysctlResult[sk] = v
			}
		}
	}

	klog.Infof("start to set pod (%v/%v) with sysctl: %+v", namespace, name, sysctlResult)

	// The directory /proc/sys/net is per network namespace. Enter in the
	// network namespace before writing on it.
	err = ns.WithNetNSPath(args.Netns, func(_ ns.NetNS) error {
		for key, value := range sysctlResult {
			// refuse to modify sysctl parameters that don't belong to the network subsystem.
			if !strings.HasPrefix(key, "net") {
				return fmt.Errorf("invalid net sysctl key: %v", key)
			}
			_, err := sysctl.Sysctl(key, value)
			if err != nil {
				return err
			}
		}

		return nil
	})
	if err != nil {
		return err
	}

	klog.Infof("write sysctl %+v for pod (%s/%s) successfully", sysctlResult, namespace, name)

	return types.PrintResult(sysctlConf.PrevResult, sysctlConf.CNIVersion)
}

func cmdDel(args *skel.CmdArgs) error {
	return nil
}

func cmdCheck(args *skel.CmdArgs) error {
	return nil
}

func buildConfig(kubeconfig string) (*rest.Config, error) {
	cfg, err := clientcmd.BuildConfigFromFlags("", kubeconfig)
	if err != nil {
		return nil, err
	}
	return cfg, nil
}

// newKubeClient creates a k8s client
func newKubeClient(kubeconfig string) (kubernetes.Interface, error) {
	config, err := buildConfig(kubeconfig)
	if err != nil {
		return nil, err
	}
	return kubernetes.NewForConfig(config)
}

func isSysCtlKey(key string, prefix string) bool {
	return strings.HasPrefix(key, prefix)
}

func extractSysCtlKey(key string, prefix string) string {
	return strings.TrimPrefix(key, prefix+"/")
}

func main() {
	klog.InitFlags(nil)
	flag.Set("logtostderr", "false")
	flag.Set("log_file", logFile)
	flag.Parse()
	defer klog.Flush()
	if e := skel.PluginMainWithError(cmdAdd, cmdCheck, cmdDel, version.All, bv.BuildString("sysctl")); e != nil {
		klog.Flush()
		if err := e.Print(); err != nil {
			klog.Errorf("Error writing error JSON to stdout: %v", err)
		}
		os.Exit(1)
	}
}
