package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"regexp"
	"strconv"
	"syscall"
	"time"

	"github.com/containernetworking/cni/pkg/skel"
	"github.com/containernetworking/cni/pkg/types"
	"github.com/containernetworking/cni/pkg/types/current"
	"github.com/containernetworking/cni/pkg/version"
	"github.com/containernetworking/plugins/pkg/ns"
	bv "github.com/containernetworking/plugins/pkg/utils/buildversion"
	"github.com/parnurzeal/gorequest"
	"github.com/vishvananda/netlink"
	"k8s.io/klog/v2"

	server "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/eniipam/http"
)

const (
	logFile = "/var/log/cce/cni-eni-ipam.log"

	toContainerRulePriority   = 512
	fromContainerRulePriority = 1536

	mainRouteTableID          = 254
	defaultRouteTableIDOffset = 127

	// httpTimeout is set to be slightly less than runtime timeout
	// xref: https://github.com/kubernetes/kubernetes/pull/71653/
	httpTimeout = time.Second * 210
)

var (
	eniNameMatcher = regexp.MustCompile(`eth(\d+)`)
)

type NetConf struct {
	CNIVersion   string          `json:"cniVersion,omitempty"`
	Name         string          `json:"name,omitempty"`
	Type         string          `json:"type,omitempty"`
	Capabilities map[string]bool `json:"capabilities,omitempty"`
	IPAM         *IPAMConf       `json:"ipam,omitempty"`
}

type IPAMConf struct {
	Type                    string `json:"type,omitempty"`
	Endpoint                string `json:"endpoint"`
	DeleteENIScopeLinkRoute bool   `json:"deleteENIScopeLinkRoute"`
	RouteTableIDOffset      int    `json:"routeTableIDOffset"` // default 127, eth1 use table 128, eth2 use table 129 etc.
}

// K8SArgs k8s pod args
type K8SArgs struct {
	types.CommonArgs `json:"commonArgs"`
	// IP is pod's ip address
	IP net.IP `json:"ip"`
	// K8S_POD_NAME is pod's name
	K8S_POD_NAME types.UnmarshallableString `json:"k8s_pod_name"`
	// K8S_POD_NAMESPACE is pod's namespace
	K8S_POD_NAMESPACE types.UnmarshallableString `json:"k8s_pod_namespace"`
	// K8S_POD_INFRA_CONTAINER_ID is pod's container ID
	K8S_POD_INFRA_CONTAINER_ID types.UnmarshallableString `json:"k8s_pod_infra_container_id"`
}

// loadIPAMConf
func loadIPAMConf(stdinData []byte) (*IPAMConf, string, error) {
	n := NetConf{}
	if err := json.Unmarshal(stdinData, &n); err != nil {
		return nil, "", err
	}

	if n.IPAM == nil {
		return nil, "", fmt.Errorf("IPAM config missing 'ipam' key")
	}

	if n.IPAM.RouteTableIDOffset == 0 {
		n.IPAM.RouteTableIDOffset = defaultRouteTableIDOffset
	}

	return n.IPAM, n.CNIVersion, nil
}

func loadK8SArgs(envArgs string) (*K8SArgs, error) {
	k8sArgs := K8SArgs{}
	if envArgs != "" {
		err := types.LoadArgs(envArgs, &k8sArgs)
		if err != nil {
			return nil, err
		}
	}
	return &k8sArgs, nil
}

func addToOrFromContainerRule(isToContainer bool, addr *net.IPNet, priority int, rtTable int) error {
	rule := netlink.NewRule()
	rule.Table = rtTable
	rule.Priority = priority

	if isToContainer {
		rule.Dst = addr // ip rule add from all to `addr` lookup `table` prio `xxx`
	} else {
		rule.Src = addr // ip rule add from `addr` lookup `table` prio `xxx`
	}

	err := netlink.RuleDel(rule)
	if err != nil && !isNotExist(err) {
		return err
	}

	if err := netlink.RuleAdd(rule); err != nil {
		return err
	}
	return nil
}

func delToOrFromContainerRule(isToContainer bool, addr *net.IPNet) error {
	rule := netlink.NewRule()

	if isToContainer {
		rule.Dst = addr // ip rule add from all to `addr` lookup `table` prio `xxx`
	} else {
		rule.Src = addr // ip rule add from `addr` lookup `table` prio `xxx`
	}

	err := netlink.RuleDel(rule)
	if err != nil && !isNotExist(err) {
		return err
	}

	return nil
}

func main() {
	klog.InitFlags(nil)
	flag.Set("logtostderr", "false")
	flag.Set("log_file", logFile)
	flag.Parse()
	defer klog.Flush()
	if e := skel.PluginMainWithError(cmdAdd, cmdCheck, cmdDel, version.All, bv.BuildString("eni-ipam")); e != nil {
		klog.Flush()
		if err := e.Print(); err != nil {
			klog.Errorf("Error writing error JSON to stdout: %v", err)
		}
		os.Exit(1)
	}
}

func cmdAdd(args *skel.CmdArgs) error {
	klog.Infof("-----------cmdAdd begins----------")
	defer klog.Infof("-----------cmdAdd ends----------")
	klog.Infof("[Add Args]: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)
	klog.Infof("[Add Args]: stdinData: %v", string(args.StdinData))

	result := &current.Result{}

	ipamConf, cniVersion, err := loadIPAMConf(args.StdinData)
	if err != nil {
		return err
	}

	k8sArgs, err := loadK8SArgs(args.Args)
	if err != nil {
		return err
	}

	name, namespace := string(k8sArgs.K8S_POD_NAME), string(k8sArgs.K8S_POD_NAMESPACE)
	// alloc IP
	url := fmt.Sprintf("http://%s/v1alpha/allocate?namespace=%s&name=%s&containerID=%s", ipamConf.Endpoint, namespace, name, args.ContainerID)
	resp, body, errs := gorequest.New().Timeout(httpTimeout).Get(url).End()
	if len(errs) != 0 {
		klog.Errorf("visit %s got error: %v", url, errs)
		return errs[0]
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return fmt.Errorf("visit %s got bad body: %v", url, body)
	}

	klog.Infof("cmdAdd got body: %v", body)

	var allocResp server.AllocateIPResponse
	if err := json.Unmarshal([]byte(body), &allocResp); err != nil {
		return err
	}

	if !allocResp.IsSuccess {
		return fmt.Errorf("ipam server alloc IP error: %v", allocResp.ErrMsg)
	}

	defer func() {
		if err != nil {
			// release IP
			url := fmt.Sprintf("http://%s/v1alpha/release?namespace=%s&name=%s&containerID=%s", ipamConf.Endpoint, namespace, name, args.ContainerID)
			_, _, delErrs := gorequest.New().Timeout(httpTimeout).Get(url).End()
			if len(delErrs) != 0 {
				klog.Errorf("rollback: failed to delete IP for pod (%v %v): %v", namespace, name, delErrs[0])
			}
		}
	}()

	allocIP := net.ParseIP(allocResp.NetworkInfo.IP)
	if allocIP == nil {
		return fmt.Errorf("alloc IP %v format error", allocResp.NetworkInfo.IP)
	}
	version := "4"
	addrBits := 32
	if allocIP.To4() == nil {
		version = "6"
		addrBits = 128
	}

	result.IPs = []*current.IPConfig{
		{
			Version: version,
			Address: net.IPNet{IP: allocIP, Mask: net.CIDRMask(addrBits, addrBits)},
		},
	}

	// ensure default route for each eni table
	var eniIntf netlink.Link
	// list all interfaces, and find ENI by mac address
	interfaces, err := netlink.LinkList()
	if err != nil {
		return fmt.Errorf("failed to list interfaces: %v", interfaces)
	}

	for _, intf := range interfaces {
		if intf.Attrs().HardwareAddr.String() == allocResp.NetworkInfo.Mac {
			eniIntf = intf
			break
		}
	}

	if eniIntf == nil {
		return fmt.Errorf("eni with mac address %v not found", allocResp.NetworkInfo.Mac)
	}

	matches := eniNameMatcher.FindStringSubmatch(eniIntf.Attrs().Name)
	if len(matches) != 2 {
		return fmt.Errorf("invalid eni name: %v", eniIntf.Attrs().Name)
	}
	ethID, err := strconv.ParseInt(matches[1], 10, 32)
	if err != nil {
		return fmt.Errorf("error parsing eni interface index: %v", err)
	}

	rtTable := defaultRouteTableIDOffset + int(ethID)

	// add to/from ip rule
	// ip rule add from all to <SIP> table main
	// ip rule add from <SIP> table XXX

	allocIPNet := &net.IPNet{IP: allocIP, Mask: net.CIDRMask(addrBits, addrBits)}
	// clean up old rule
	_ = delToOrFromContainerRule(true, allocIPNet)
	_ = delToOrFromContainerRule(false, allocIPNet)

	err = addToOrFromContainerRule(true, allocIPNet, toContainerRulePriority, mainRouteTableID)
	if err != nil {
		return fmt.Errorf("failed to add to-container rule: %v", err)
	}

	err = addToOrFromContainerRule(false, allocIPNet, fromContainerRulePriority, rtTable)
	if err != nil {
		return fmt.Errorf("failed to add from-container rule: %v", err)
	}

	// del eni scope link route
	if ipamConf.DeleteENIScopeLinkRoute {
		_ = delScopeLinkRoute(eniIntf)
	}

	return types.PrintResult(result, cniVersion)
}

func cmdDel(args *skel.CmdArgs) error {
	klog.Infof("-----------cmdDel begins----------")
	defer klog.Infof("-----------cmdDel ends----------")
	klog.Infof("[Del Args]: containerID: %v, netns: %v, ifName: %v, args: %v, path: %v",
		args.ContainerID, args.Netns, args.IfName, args.Args, args.Path)
	klog.Infof("[Del Args]: stdinData: %v", string(args.StdinData))

	ipamConf, _, err := loadIPAMConf(args.StdinData)
	if err != nil {
		return err
	}

	k8sArgs, err := loadK8SArgs(args.Args)
	if err != nil {
		return err
	}

	// we cannot rely on NetworkInfo to clean up rule since ipam server will gc leaked pod
	if args.Netns != "" {
		podIP, _ := getIPFromPodNetNS(args.Netns, "eth0")
		if podIP != nil {
			addrBits := 32
			if podIP.To4() == nil {
				addrBits = 128
			}
			podIPNet := &net.IPNet{IP: podIP, Mask: net.CIDRMask(addrBits, addrBits)}
			_ = delToOrFromContainerRule(true, podIPNet)
			_ = delToOrFromContainerRule(false, podIPNet)
		}
	}

	name, namespace := string(k8sArgs.K8S_POD_NAME), string(k8sArgs.K8S_POD_NAMESPACE)
	// release IP
	url := fmt.Sprintf("http://%s/v1alpha/release?namespace=%s&name=%s&containerID=%s", ipamConf.Endpoint, namespace, name, args.ContainerID)
	resp, body, errs := gorequest.New().Timeout(httpTimeout).Get(url).End()
	if len(errs) != 0 {
		klog.Errorf("visit %s got error: %v", url, errs)
		return errs[0]
	}

	if resp.StatusCode >= http.StatusBadRequest {
		return fmt.Errorf("visit %s got bad body: %v", url, body)
	}

	klog.Infof("cmdDel got body: %v", body)

	var releaseResp server.ReleaseIPResponse
	if err := json.Unmarshal([]byte(body), &releaseResp); err != nil {
		return err
	}

	if !releaseResp.IsSuccess {
		return fmt.Errorf("ipam server release IP error: %v", releaseResp.ErrMsg)
	}

	if releaseResp.NetworkInfo == nil {
		return nil
	}

	releaseIP := net.ParseIP(releaseResp.NetworkInfo.IP)
	if releaseIP == nil {
		return fmt.Errorf("alloc IP %v format error", releaseResp.NetworkInfo.IP)
	}
	addrBits := 32
	if releaseIP.To4() == nil {
		addrBits = 128
	}
	releaseIPNet := &net.IPNet{IP: releaseIP, Mask: net.CIDRMask(addrBits, addrBits)}

	err = delToOrFromContainerRule(true, releaseIPNet)
	if err != nil && !isNotExist(err) {
		return fmt.Errorf("failed to del to-container rule: %v", err)
	}

	err = delToOrFromContainerRule(false, releaseIPNet)
	if err != nil && !isNotExist(err) {
		return fmt.Errorf("failed to del from-container rule: %v", err)
	}

	return nil
}

func cmdCheck(args *skel.CmdArgs) error {
	return nil
}

func isNotExist(err error) bool {
	errno, ok := err.(syscall.Errno)
	if ok {
		return errno == syscall.ENOENT || errno == syscall.ESRCH
	}
	return false
}

func getIPFromPodNetNS(podNSPath string, ifName string) (net.IP, error) {
	var ip net.IP

	netns, err := ns.GetNS(podNSPath)
	if err != nil {
		return nil, err
	}
	defer netns.Close()

	err = netns.Do(func(hostNS ns.NetNS) error {
		intf, err := netlink.LinkByName(ifName)
		if err != nil {
			return err
		}
		addrs, err := netlink.AddrList(intf, netlink.FAMILY_V4)
		if err != nil {
			return err
		}
		if len(addrs) == 0 {
			return fmt.Errorf("no IP found on %v", ifName)
		}
		ip = addrs[0].IP
		return nil
	})
	if err != nil {
		return nil, err
	}

	return ip, nil
}

func delScopeLinkRoute(intf netlink.Link) error {
	addrs, err := netlink.AddrList(intf, netlink.FAMILY_V4)
	if err != nil {
		return err
	}

	for _, addr := range addrs {
		dst := net.IPNet{
			IP:   addr.IP.Mask(addr.Mask),
			Mask: addr.Mask,
		}
		err = netlink.RouteDel(&netlink.Route{
			Dst:       &dst,
			Scope:     netlink.SCOPE_LINK,
			LinkIndex: intf.Attrs().Index,
		})
		if err != nil && !isNotExist(err) {
			return err
		}
	}

	return nil
}
