// Copyright 2015 CNI authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

/*
modification history
--------------------
2020/03/17, <NAME_EMAIL>
from https://github.com/containernetworking/plugins/blob/master/plugins/main/ipvlan/ipvlan.go @ 47a9fd80c8258a288db0bb39f68516a4770012a4
2020/03/17, <NAME_EMAIL>
*/

package main

import (
	"crypto/sha1"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net"
	"os"
	"runtime"
	"syscall"
	"time"

	"github.com/containernetworking/cni/pkg/skel"
	"github.com/containernetworking/cni/pkg/types"
	"github.com/containernetworking/cni/pkg/types/current"
	"github.com/containernetworking/cni/pkg/version"
	"github.com/containernetworking/plugins/pkg/ip"
	"github.com/containernetworking/plugins/pkg/ipam"
	"github.com/containernetworking/plugins/pkg/ns"
	"github.com/containernetworking/plugins/pkg/utils"
	bv "github.com/containernetworking/plugins/pkg/utils/buildversion"
	"github.com/containernetworking/plugins/pkg/utils/sysctl"
	"github.com/j-keck/arping"
	"github.com/vishvananda/netlink"
)

const (
	ARPProxyTemplate    = "net.ipv4.conf.%s.proxy_arp"
	ProxyDelayTemplate  = "net.ipv4.neigh.%s.proxy_delay"
	NDPProxyTemplate    = "net.ipv6.conf.%s.proxy_ndp"
	DisableIPv6Template = "net.ipv6.conf.%s.disable_ipv6"
)

func init() {
	// this ensures that main runs only on main thread (thread group leader).
	// since namespace ops (unshare, setns) are done for a single thread, we
	// must ensure that the goroutine does not jump from OS thread to thread
	runtime.LockOSThread()
}

type NetConf struct {
	types.NetConf
	IPMasq         bool `json:"ipMasq"`
	MTU            int  `json:"mtu"`
	EnableARPProxy bool `json:"enableARPProxy"`
	// HostVethPrefix is the veth for container prefix on host
	HostVethPrefix string `json:"vethPrefix"`
}

// K8SArgs is cni args of kubernetes
type K8SArgs struct {
	types.CommonArgs
	IP                         net.IP
	K8S_POD_NAME               types.UnmarshallableString
	K8S_POD_NAMESPACE          types.UnmarshallableString
	K8S_POD_INFRA_CONTAINER_ID types.UnmarshallableString
}

func setupVeth(contVethName, hostVethName string, mtu int, hostNS ns.NetNS) (net.Interface, net.Interface, error) {
	return ip.SetupVethWithName(contVethName, hostVethName, mtu, hostNS)
}

func setupContainerVeth(netns ns.NetNS, ifName string, hostVethName string, mtu int, pr *current.Result, enableARPProxy bool) (*current.Interface, *current.Interface, error) {
	// The IPAM result will be something like IP=***********/24, GW=***********.
	// What we want is really a point-to-point link but veth does not support IFF_POINTTOPOINT.
	// Next best thing would be to let it ARP but set interface to ***********/32 and
	// add a route like "***********/24 via *********** dev $ifName".
	// Unfortunately that won't work as the GW will be outside the interface's subnet.

	// Our solution is to configure the interface with ***********/24, then delete the
	// "***********/24 dev $ifName" route that was automatically added. Then we add
	// "***********/32 dev $ifName" and "***********/24 via *********** dev $ifName".
	// In other words we force all traffic to ARP via the gateway except for GW itself.

	hostInterface := &current.Interface{}
	containerInterface := &current.Interface{}

	err := netns.Do(func(hostNS ns.NetNS) error {
		hostVeth, contVeth0, err := setupVeth(ifName, hostVethName, mtu, hostNS)
		if err != nil {
			return err
		}
		hostInterface.Name = hostVeth.Name
		hostInterface.Mac = hostVeth.HardwareAddr.String()
		containerInterface.Name = contVeth0.Name
		containerInterface.Mac = contVeth0.HardwareAddr.String()
		containerInterface.Sandbox = netns.Path()

		for _, ipc := range pr.IPs {
			// All addresses apply to the container veth interface
			ipc.Interface = current.Int(1)
		}

		pr.Interfaces = []*current.Interface{hostInterface, containerInterface}

		if err = ipam.ConfigureIface(ifName, pr); err != nil {
			return err
		}

		contVeth, err := net.InterfaceByName(ifName)
		if err != nil {
			return fmt.Errorf("failed to look up %q: %v", ifName, err)
		}

		for _, ipc := range pr.IPs {
			// Delete the route that was automatically added
			route := netlink.Route{
				LinkIndex: contVeth.Index,
				Dst: &net.IPNet{
					IP:   ipc.Address.IP.Mask(ipc.Address.Mask),
					Mask: ipc.Address.Mask,
				},
				Scope: netlink.SCOPE_NOWHERE,
			}

			if err := netlink.RouteDel(&route); err != nil && !isNotExist(err) {
				return fmt.Errorf("failed to delete route %v: %v", route, err)
			}

			isIPv6 := false
			addrBits := 32
			if ipc.Version == "6" {
				isIPv6 = true
				addrBits = 128
			}
			if !enableARPProxy { // hostNetns 的 veth 有网关IP
				for _, r := range []netlink.Route{
					{
						LinkIndex: contVeth.Index,
						Dst: &net.IPNet{
							IP:   ipc.Gateway,
							Mask: net.CIDRMask(addrBits, addrBits),
						},
						Scope: netlink.SCOPE_LINK,
						Src:   ipc.Address.IP,
					},
					{
						LinkIndex: contVeth.Index,
						Dst: &net.IPNet{
							IP:   ipc.Address.IP.Mask(ipc.Address.Mask),
							Mask: ipc.Address.Mask,
						},
						Scope: netlink.SCOPE_UNIVERSE,
						Gw:    ipc.Gateway,
						Src:   ipc.Address.IP,
					},
				} {
					if err := netlink.RouteAdd(&r); err != nil {
						return fmt.Errorf("failed to add route %v: %v", r, err)
					}
				}
			} else { // arp/ndp proxy 模式
				if !isIPv6 {
					// ip route add *********** dev eth0 scope link
					gw := net.IPv4(169, 254, 1, 1)
					gwNet := &net.IPNet{IP: gw, Mask: net.CIDRMask(32, 32)}
					if err := netlink.RouteReplace(
						&netlink.Route{
							LinkIndex: contVeth.Index,
							Scope:     netlink.SCOPE_LINK,
							Dst:       gwNet,
						},
					); err != nil {
						return fmt.Errorf("failed to add gw route inside the container: %v", err)
					}
					// ip route add default via *********** dev eth0
					if err := netlink.RouteReplace(
						&netlink.Route{
							LinkIndex: contVeth.Index,
							Scope:     netlink.SCOPE_UNIVERSE,
							Dst:       &net.IPNet{IP: net.IPv4(0, 0, 0, 0), Mask: net.CIDRMask(0, 32)},
							Gw:        gw,
						},
					); err != nil {
						return fmt.Errorf("failed to add default route inside the container: %v", err)
					}

				}
				if isIPv6 {
					// Make sure ipv6 is enabled in the container/pod network namespace.
					// Without these sysctls enabled, interfaces will come up but they won't get a link local IPv6 address
					// which is required to add the default IPv6 route.
					if _, err := sysctl.Sysctl("net.ipv6.conf.all.disable_ipv6", "0"); err != nil {
						return fmt.Errorf("failed to set net.ipv6.conf.all.disable_ipv6=0: %s", err)
					}
					if _, err = sysctl.Sysctl("net.ipv6.conf.default.disable_ipv6", "0"); err != nil {
						return fmt.Errorf("failed to set net.ipv6.conf.default.disable_ipv6=0: %s", err)
					}

					if _, err = sysctl.Sysctl("net.ipv6.conf.lo.disable_ipv6", "0"); err != nil {
						return fmt.Errorf("failed to set net.ipv6.conf.lo.disable_ipv6=0: %s", err)
					}

					// Retry several times as the LL can take a several micro/miliseconds to initialize and we may be too fast after these sysctls
					var err error
					var addresses []netlink.Addr
					var hostVeth netlink.Link
					hostNSErr := hostNS.Do(func(_ ns.NetNS) error {
						// No need to add a dummy next hop route as the host veth device will already have an IPv6
						// link local address that can be used as a next hop.
						// Just fetch the address of the host end of the veth and use it as the next hop.
						for i := 0; i < 10; i++ {
							hostVeth, err = netlink.LinkByName(hostInterface.Name)
							if err != nil {
								err = fmt.Errorf("failed to get host veth link %v: %v", hostInterface.Name, err)
							}
							addresses, err = netlink.AddrList(hostVeth, netlink.FAMILY_V6)
							if err != nil {
								err = fmt.Errorf("error listing IPv6 addresses for the host side of the veth pair: %s", err)
							}
							if len(addresses) < 1 {
								err = fmt.Errorf("failed to get IPv6 addresses for host side of the veth pair")
							}
							if err == nil {
								break
							}
							time.Sleep(50 * time.Millisecond)
						}
						if err != nil {
							return err
						}
						return nil
					})
					if hostNSErr != nil {
						return fmt.Errorf("failed to get veth info of host side")
					}

					hostIPv6Addr := addresses[0].IP
					// ip route add hostveth dev eth0 scope link
					gwNet := &net.IPNet{IP: hostIPv6Addr, Mask: net.CIDRMask(128, 128)}
					if err := netlink.RouteReplace(
						&netlink.Route{
							LinkIndex: contVeth.Index,
							Scope:     netlink.SCOPE_LINK,
							Dst:       gwNet,
						},
					); err != nil {
						return fmt.Errorf("failed to add gw route inside the container: %v", err)
					}
					// ip route add default via *********** dev eth0
					if err := netlink.RouteReplace(
						&netlink.Route{
							LinkIndex: contVeth.Index,
							Scope:     netlink.SCOPE_UNIVERSE,
							Dst:       &net.IPNet{IP: net.IPv6zero, Mask: net.CIDRMask(0, 128)},
							Gw:        hostIPv6Addr,
						},
					); err != nil {
						return fmt.Errorf("failed to add default route inside the container: %v", err)
					}
				}
			}
		}

		// Send a gratuitous arp for all v4 addresses
		for _, ipc := range pr.IPs {
			if ipc.Version == "4" {
				_ = arping.GratuitousArpOverIface(ipc.Address.IP, *contVeth)
			}
		}

		return nil
	})
	if err != nil {
		return nil, nil, err
	}
	return hostInterface, containerInterface, nil
}

func setupHostVeth(vethName string, result *current.Result, enableARPProxy bool) error {
	var hasIPv4, hasIPv6 bool

	// hostVeth moved namespaces and may have a new ifindex
	veth, err := netlink.LinkByName(vethName)
	if err != nil {
		return fmt.Errorf("failed to lookup %q: %v", vethName, err)
	}

	for _, ipc := range result.IPs {
		maskLen := 128
		if ipc.Address.IP.To4() != nil {
			maskLen = 32
		}

		if ipc.Version == "4" {
			hasIPv4 = true
		} else if ipc.Version == "6" {
			hasIPv6 = true
		}

		if !enableARPProxy {
			ipn := &net.IPNet{
				IP:   ipc.Gateway,
				Mask: net.CIDRMask(maskLen, maskLen),
			}
			addr := &netlink.Addr{IPNet: ipn, Label: ""}
			if err = netlink.AddrAdd(veth, addr); err != nil {
				return fmt.Errorf("failed to add IP addr (%#v) to veth: %v", ipn, err)
			}
		}

		ipn := &net.IPNet{
			IP:   ipc.Address.IP,
			Mask: net.CIDRMask(maskLen, maskLen),
		}
		// dst happens to be the same as IP/net of host veth
		if err = netlink.RouteReplace(&netlink.Route{
			LinkIndex: veth.Attrs().Index,
			Scope:     netlink.SCOPE_LINK,
			Dst:       ipn,
		}); err != nil && !os.IsExist(err) {
			return fmt.Errorf("failed to add route on host: %v", err)
		}
	}

	if enableARPProxy {
		if err = configureSysctls(vethName, hasIPv4, hasIPv6); err != nil {
			return err
		}
	}

	return nil
}

func cmdAdd(args *skel.CmdArgs) error {
	conf := NetConf{}
	if err := json.Unmarshal(args.StdinData, &conf); err != nil {
		return fmt.Errorf("failed to load netconf: %v", err)
	}

	k8sConfig := K8SArgs{}
	if err := types.LoadArgs(args.Args, &k8sConfig); err != nil {
		return fmt.Errorf("add cmd: failed to load k8s config from args: %v", err)
	}

	hostVethName := VethNameForPod(string(k8sConfig.K8S_POD_NAME), string(k8sConfig.K8S_POD_NAMESPACE), conf.HostVethPrefix)
	// clean up old veth
	_ = ip.DelLinkByName(hostVethName)

	// run the IPAM plugin and get back the config to apply
	r, err := ipam.ExecAdd(conf.IPAM.Type, args.StdinData)
	if err != nil {
		return err
	}

	// Invoke ipam del if err to avoid ip leak
	defer func() {
		if err != nil {
			_ = ipam.ExecDel(conf.IPAM.Type, args.StdinData)
		}
	}()

	// Convert whatever the IPAM result was into the current Result type
	result, err := current.NewResultFromResult(r)
	if err != nil {
		return err
	}

	if len(result.IPs) == 0 {
		return errors.New("IPAM plugin returned missing IP config")
	}

	if err := ip.EnableForward(result.IPs); err != nil {
		return fmt.Errorf("Could not enable IP forwarding: %v", err)
	}

	netns, err := ns.GetNS(args.Netns)
	if err != nil {
		return fmt.Errorf("failed to open netns %q: %v", args.Netns, err)
	}
	defer netns.Close()

	hostInterface, _, err := setupContainerVeth(netns, args.IfName, hostVethName, conf.MTU, result, conf.EnableARPProxy)
	if err != nil {
		return err
	}

	if err = setupHostVeth(hostInterface.Name, result, conf.EnableARPProxy); err != nil {
		return err
	}

	if conf.IPMasq {
		chain := utils.FormatChainName(conf.Name, args.ContainerID)
		comment := utils.FormatComment(conf.Name, args.ContainerID)
		for _, ipc := range result.IPs {
			if err = ip.SetupIPMasq(&ipc.Address, chain, comment); err != nil {
				return err
			}
		}
	}

	// Only override the DNS settings in the previous result if any DNS fields
	// were provided to the ptp plugin. This allows, for example, IPAM plugins
	// to specify the DNS settings instead of the ptp plugin.
	if dnsConfSet(conf.DNS) {
		result.DNS = conf.DNS
	}

	return types.PrintResult(result, conf.CNIVersion)
}

func dnsConfSet(dnsConf types.DNS) bool {
	return dnsConf.Nameservers != nil ||
		dnsConf.Search != nil ||
		dnsConf.Options != nil ||
		dnsConf.Domain != ""
}

func cmdDel(args *skel.CmdArgs) error {
	conf := NetConf{}
	if err := json.Unmarshal(args.StdinData, &conf); err != nil {
		return fmt.Errorf("failed to load netconf: %v", err)
	}

	if err := ipam.ExecDel(conf.IPAM.Type, args.StdinData); err != nil {
		return err
	}

	if args.Netns == "" {
		return nil
	}

	// There is a netns so try to clean up. Delete can be called multiple times
	// so don't return an error if the device is already removed.
	// If the device isn't there then don't try to clean up IP masq either.
	var ipnets []*net.IPNet
	err := ns.WithNetNSPath(args.Netns, func(_ ns.NetNS) error {
		var err error
		ipnets, err = ip.DelLinkByNameAddr(args.IfName)
		if err != nil && err == ip.ErrLinkNotFound {
			return nil
		}
		return err
	})

	if err != nil {
		return err
	}

	if len(ipnets) != 0 && conf.IPMasq {
		chain := utils.FormatChainName(conf.Name, args.ContainerID)
		comment := utils.FormatComment(conf.Name, args.ContainerID)
		for _, ipn := range ipnets {
			err = ip.TeardownIPMasq(ipn, chain, comment)
		}
	}

	return err
}

func main() {
	skel.PluginMain(cmdAdd, cmdCheck, cmdDel, version.All, bv.BuildString("ptp"))
}

func cmdCheck(args *skel.CmdArgs) error {
	conf := NetConf{}
	if err := json.Unmarshal(args.StdinData, &conf); err != nil {
		return fmt.Errorf("failed to load netconf: %v", err)
	}

	netns, err := ns.GetNS(args.Netns)
	if err != nil {
		return fmt.Errorf("failed to open netns %q: %v", args.Netns, err)
	}
	defer netns.Close()

	// run the IPAM plugin and get back the config to apply
	err = ipam.ExecCheck(conf.IPAM.Type, args.StdinData)
	if err != nil {
		return err
	}
	if conf.NetConf.RawPrevResult == nil {
		return fmt.Errorf("ptp: Required prevResult missing")
	}
	if err := version.ParsePrevResult(&conf.NetConf); err != nil {
		return err
	}
	// Convert whatever the IPAM result was into the current Result type
	result, err := current.NewResultFromResult(conf.PrevResult)
	if err != nil {
		return err
	}

	var contMap current.Interface
	// Find interfaces for name whe know, that of host-device inside container
	for _, intf := range result.Interfaces {
		if args.IfName == intf.Name {
			if args.Netns == intf.Sandbox {
				contMap = *intf
				continue
			}
		}
	}

	// The namespace must be the same as what was configured
	if args.Netns != contMap.Sandbox {
		return fmt.Errorf("Sandbox in prevResult %s doesn't match configured netns: %s",
			contMap.Sandbox, args.Netns)
	}

	//
	// Check prevResults for ips, routes and dns against values found in the container
	if err := netns.Do(func(_ ns.NetNS) error {

		// Check interface against values found in the container
		err := validateCniContainerInterface(contMap)
		if err != nil {
			return err
		}

		if !conf.EnableARPProxy {
			err = ip.ValidateExpectedInterfaceIPs(args.IfName, result.IPs)
			if err != nil {
				return err
			}

			err = ip.ValidateExpectedRoute(result.Routes)
			if err != nil {
				return err
			}
		}
		return nil
	}); err != nil {
		return err
	}

	return nil
}

func validateCniContainerInterface(intf current.Interface) error {

	var link netlink.Link
	var err error

	if intf.Name == "" {
		return fmt.Errorf("Container interface name missing in prevResult: %v", intf.Name)
	}
	link, err = netlink.LinkByName(intf.Name)
	if err != nil {
		return fmt.Errorf("ptp: Container Interface name in prevResult: %s not found", intf.Name)
	}
	if intf.Sandbox == "" {
		return fmt.Errorf("ptp: Error: Container interface %s should not be in host namespace", link.Attrs().Name)
	}

	_, isVeth := link.(*netlink.Veth)
	if !isVeth {
		return fmt.Errorf("Error: Container interface %s not of type veth/p2p", link.Attrs().Name)
	}

	if intf.Mac != "" {
		if intf.Mac != link.Attrs().HardwareAddr.String() {
			return fmt.Errorf("ptp: Interface %s Mac %s doesn't match container Mac: %s", intf.Name, intf.Mac, link.Attrs().HardwareAddr)
		}
	}

	return nil
}

func configureSysctls(hostVethName string, hasIPv4, hasIPv6 bool) error {
	if hasIPv4 {
		if _, err := sysctl.Sysctl(fmt.Sprintf(ProxyDelayTemplate, hostVethName), "0"); err != nil {
			return fmt.Errorf("failed to set net.ipv4.neigh.%s.proxy_delay=0: %s", hostVethName, err)
		}

		if _, err := sysctl.Sysctl(fmt.Sprintf(ARPProxyTemplate, hostVethName), "1"); err != nil {
			return fmt.Errorf("failed to set net.ipv4.conf.%s.proxy_arp=1: %s", hostVethName, err)
		}
	}

	if hasIPv6 {
		if _, err := sysctl.Sysctl(fmt.Sprintf(DisableIPv6Template, hostVethName), "0"); err != nil {
			return fmt.Errorf("failed to set net.ipv6.conf.%s.disable_ipv6=0: %s", hostVethName, err)
		}

		if _, err := sysctl.Sysctl(fmt.Sprintf(NDPProxyTemplate, hostVethName), "1"); err != nil {
			return fmt.Errorf("failed to set net.ipv6.conf.%s.proxy_ndp=1: %s", hostVethName, err)
		}
	}

	return nil
}

// VethNameForPod return host-side veth name for pod
// max veth length is 15
func VethNameForPod(name, namespace, prefix string) string {
	// A SHA1 is always 20 bytes long, and so is sufficient for generating the
	// veth name and mac addr.
	h := sha1.New()
	h.Write([]byte(namespace + "." + name))
	return fmt.Sprintf("%s%s", prefix, hex.EncodeToString(h.Sum(nil))[:11])
}

func isNotExist(err error) bool {
	errno, ok := err.(syscall.Errno)
	if ok {
		return errno == syscall.ENOENT || errno == syscall.ESRCH
	}
	return false
}
