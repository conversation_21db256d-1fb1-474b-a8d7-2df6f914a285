package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/baidubce/bce-sdk-go/auth"
	enisdk "github.com/baidubce/bce-sdk-go/services/eni"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"

	vpcsdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

var (
	allocatedPodCache map[string]*v1alpha1.WorkloadEndpoint
	privateIPToDelete map[string][]string

	kubeconfig *string
	stsConfig  *string
	vpcID      *string
	clusterID  *string
	region     *string
)

type STSConfig struct {
	AccessKeyID     string `json:"AccessKeyID"`
	SecretAccessKey string `json:"SecretAccessKey"`
	SessionToken    string `json:"sessionToken"`
}

func main() {
	parseFlags()

	ctx := context.TODO()

	// use the current context in kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		log.Fatalf(ctx, "build config error: %v", err)
	}

	crdClient := versioned.NewForConfigOrDie(config)

	content, err := ioutil.ReadFile(*stsConfig)
	if err != nil {
		log.Fatalf(ctx, "read sts config file error: %v", err)
	}

	var sts STSConfig
	err = json.Unmarshal(content, &sts)
	if err != nil {
		log.Fatalf(ctx, "read sts config file error: %v", err)
	}

	eniClient, err := NewClient(sts.AccessKeyID, sts.SecretAccessKey, sts.SessionToken)
	if err != nil {
		log.Fatalf(ctx, "new eni client failed: %v", err)
	}

	wepList, err := crdClient.CceV1alpha1().WorkloadEndpoints(v1.NamespaceAll).List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Fatalf(ctx, "get wep list failed: %v", err)
	}

	allocatedPodCache = make(map[string]*v1alpha1.WorkloadEndpoint)
	privateIPToDelete = make(map[string][]string)

	// build allocated pod cache
	for idx, wep := range wepList.Items {
		log.Infof(ctx, "build allocated pod cache: found IP %v assigned to pod (%v %v)", wep.Spec.IP, wep.Namespace, wep.Name)
		allocatedPodCache[wep.Spec.IP] = &wepList.Items[idx]
	}

	listEniResult, err := eniClient.ListEni(&enisdk.ListEniArgs{
		VpcId:   *vpcID,
		MaxKeys: 1000,
	})
	if err != nil {
		log.Fatalf(ctx, "list eni failed: %v", err)
	}

	for _, eni := range listEniResult.Eni {
		if eni.Status != "inuse" || !ENICreatedByCluster(&eni, *clusterID) {
			continue
		}

		for _, ip := range eni.PrivateIpSet {
			if !ip.Primary {
				wep, ok := allocatedPodCache[ip.PrivateIpAddress]
				if !ok {
					log.Infof(ctx, "found private ip %v at %v leaked", ip.PrivateIpAddress, eni.EniId)
					//  delete private ip
					if len(privateIPToDelete[eni.EniId]) == 0 {
						privateIPToDelete[eni.EniId] = make([]string, 0)
					}
					privateIPToDelete[eni.EniId] = append(privateIPToDelete[eni.EniId], ip.PrivateIpAddress)
				} else {
					log.Infof(ctx, "found private ip %v at eni %v allocated to pod (%v %v)", ip.PrivateIpAddress, eni.EniId, wep.Namespace, wep.Name)
				}
			}
		}
	}

	log.Infof(ctx, "ip to delete: %+v", privateIPToDelete)
	prompt()

	for k, v := range privateIPToDelete {
		for _, ip := range v {
			if !canPing(ip) {
				log.Errorf(ctx, "error ping ip %v at %v", ip, k)
				err := eniClient.DeletePrivateIp(&enisdk.EniPrivateIpArgs{
					EniId:            k,
					PrivateIpAddress: ip,
				})

				if err != nil {
					log.Errorf(ctx, "error delete private ip %v at %v: %v", ip, k, err)
				} else {
					log.Infof(ctx, "delete private ip %v at %v done", ip, k)
				}
			} else {
				log.Warningf(ctx, "ping ip %v ok !!!", ip)
			}
		}
	}
}

func canPing(ip string) bool {
	cmd := exec.Command("ping", ip, "-c", "3", "-i", "0.01", "-W", "1", "-q")
	err := cmd.Run()
	if err != nil {
		return false
	}
	return true
}

func parseFlags() {
	if home := homedir.HomeDir(); home != "" {
		kubeconfig = flag.String("kubeconfig", filepath.Join(home, ".kube", "config"), "(optional) absolute path to the kubeconfig file")
	} else {
		kubeconfig = flag.String("kubeconfig", "", "absolute path to the kubeconfig file")
	}

	stsConfig = flag.String("sts", "sts.json", "")
	vpcID = flag.String("vpc", "vpc-vqgcn7jcdvc0", "")
	clusterID = flag.String("cluster", "", "")
	region = flag.String("region", "fwh", "")

	flag.Parse()
}

func NewClient(ak, sk, sessionToken string) (*enisdk.Client, error) {
	c, err := enisdk.NewClient(ak, sk, vpcsdk.Endpoints[*region])
	if err != nil {
		return nil, err
	}

	if sessionToken != "" {
		stsCredential, err := auth.NewSessionBceCredentials(ak, sk, sessionToken)
		if err != nil {
			return nil, err
		}
		c.Config.Credentials = stsCredential
	}

	return c, nil
}

func ENICreatedByCluster(eni *enisdk.Eni, clusterID string) bool {
	if eni == nil {
		return false
	}
	const eniNamePartNum int = 4
	parts := strings.Split(eni.Name, "/")
	if len(parts) != eniNamePartNum {
		return false
	}
	// parts[0] is clusterId and parts[1] is instanceId
	if !(strings.HasPrefix(parts[0], "c-") || strings.HasPrefix(parts[0], "cce-")) || !strings.HasPrefix(parts[1], "i-") {
		return false
	}

	if parts[0] != clusterID {
		return false
	}

	return true
}

func prompt() {
	fmt.Printf("-> Press Return key to continue delete leaked rules.")
	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		break
	}
	if err := scanner.Err(); err != nil {
		panic(err)
	}
	fmt.Println()
}
