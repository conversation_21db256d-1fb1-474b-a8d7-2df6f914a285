package main

import (
	"bufio"
	"fmt"
	"log"
	"os"
	"syscall"

	"github.com/vishvananda/netlink"
)

const (
	toContainerRulePriority   = 512
	fromContainerRulePriority = 1536
	mainRouteTableID          = 254
)

var (
	routeDstLinkMap = make(map[string]string)
	leakedRules     = make([]netlink.Rule, 0)
)

// TODO: CNI 会先创建 rule 再创建 route。 GC 可能遇到时序问题
func main() {
	routes, err := netlink.RouteList(nil, netlink.FAMILY_V4)
	if err != nil {
		log.Fatalf("failed to list ip routes: %v", err)
	}

	rules, err := netlink.RuleList(netlink.FAMILY_V4)
	if err != nil {
		log.Fatalf("failed to list ip rules: %v", err)
	}

	// build route cache
	buildRouteDstLinkMap(routes)
	log.Println("ip route show result: ", routeDstLinkMap)

	// check each rule
	for _, rule := range rules {
		var containerIP string

		// to container
		if rule.Priority == toContainerRulePriority {
			if rule.Dst == nil || rule.Dst.IP == nil {
				continue
			}
			containerIP = rule.Dst.IP.String()

			if rule.Table != mainRouteTableID {
				log.Printf("found unknown rule to %v\n", containerIP)
				continue
			}

			_, exist := routeDstLinkMap[containerIP]
			if !exist {
				log.Printf("found leaked ip rule to container: %+v to %v\n", rule, containerIP)
				leakedRules = append(leakedRules, rule)
			}
		}

		// from container
		if rule.Priority == fromContainerRulePriority {
			if rule.Src == nil || rule.Src.IP == nil {
				continue
			}
			containerIP = rule.Src.IP.String()

			_, exist := routeDstLinkMap[containerIP]
			if !exist {
				log.Printf("found leaked ip rule from container: %+v\n", rule)
				leakedRules = append(leakedRules, rule)
			}
		}
	}

	prompt()
	// let's clean
	for _, rule := range leakedRules {
		err := netlink.RuleDel(&rule)
		if err != nil && !isNotExist(err) {
			log.Println("failed to delete leaked ip rule: ", rule)
		}
	}

	log.Println("clean up done")
}

func buildRouteDstLinkMap(routes []netlink.Route) {
	for _, rt := range routes {
		if rt.Dst != nil {
			dst := rt.Dst.IP
			intf, err := netlink.LinkByIndex(rt.LinkIndex)
			if dst != nil && err == nil {
				routeDstLinkMap[dst.String()] = intf.Attrs().Name
			}
		}
	}
}

func prompt() {
	fmt.Printf("-> Press Return key to continue delete leaked rules.")
	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		break
	}
	if err := scanner.Err(); err != nil {
		panic(err)
	}
	fmt.Println()
}

func isNotExist(err error) bool {
	errno, ok := err.(syscall.Errno)
	if ok {
		return errno == syscall.ENOENT || errno == syscall.ESRCH
	}
	return false
}
