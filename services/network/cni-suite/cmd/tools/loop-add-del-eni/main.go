package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"strings"
	"time"

	"github.com/baidubce/bce-sdk-go/auth"
	bccsdk "github.com/baidubce/bce-sdk-go/services/bcc"
	"github.com/baidubce/bce-sdk-go/services/bcc/api"
	enisdk "github.com/baidubce/bce-sdk-go/services/eni"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/nodeagent/util"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

const (
	bccEndpoint = "bcc.bj.baidubce.com"
)

var (
	stsConfig  *string
	maxENINum  *int
	instanceID *string
	clusterID  *string
	nodeName   *string
)

var (
	vpcID           string
	subnetID        string
	securityGroupID string
)

type STSConfig struct {
	AccessKeyID     string `json:"AccessKeyID"`
	SecretAccessKey string `json:"SecretAccessKey"`
	SessionToken    string `json:"sessionToken"`
}

func parseFlags() {
	maxENINum = flag.Int("max-eni-num", 8, "")
	stsConfig = flag.String("sts", "sts.json", "")
	instanceID = flag.String("instance", "", "")
	clusterID = flag.String("cluster", "", "")
	nodeName = flag.String("node", "", "")

	flag.Parse()
}

func main() {
	parseFlags()

	ctx := context.TODO()

	content, err := ioutil.ReadFile(*stsConfig)
	if err != nil {
		log.Fatalf(ctx, "read sts config file error: %v", err)
	}

	var sts STSConfig
	err = json.Unmarshal(content, &sts)
	if err != nil {
		log.Fatalf(ctx, "read sts config file error: %v", err)
	}

	eniClient, err := NewENIClient(sts.AccessKeyID, sts.SecretAccessKey, sts.SessionToken)
	if err != nil {
		log.Fatalf(ctx, "new eni client failed: %v", err)
	}

	bccClient, err := NewBCCClient(sts.AccessKeyID, sts.SecretAccessKey, sts.SessionToken)
	if err != nil {
		log.Fatalf(ctx, "new bcc client failed: %v", err)
	}

	instanceDetail, err := bccClient.GetInstanceDetail(*instanceID)
	if err != nil {
		log.Fatalf(ctx, "GetInstanceDetail failed: %v", err)
	}

	secGroupResp, err := bccClient.ListSecurityGroup(&api.ListSecurityGroupArgs{InstanceId: *instanceID})
	if err != nil {
		log.Fatalf(ctx, "ListSecurityGroup failed: %v", err)
	}

	vpcID = instanceDetail.Instance.VpcId
	subnetID = instanceDetail.Instance.SubnetId
	securityGroupID = secGroupResp.SecurityGroups[0].Id

	// let's go
	for {
		ctx := log.NewContext()

		var relatedENIs []enisdk.Eni

		listEniResult, err := eniClient.ListEni(&enisdk.ListEniArgs{
			VpcId:   vpcID,
			MaxKeys: 1000,
		})
		if err != nil {
			log.Errorf(ctx, "list eni failed: %v", err)
			goto nextRound
		}

		for _, eni := range listEniResult.Eni {
			if eniOwnedByNode(eni.Name, *clusterID, *instanceID) {
				relatedENIs = append(relatedENIs, eni)
			}
		}

		log.Infof(ctx, "node %v has %d enis", *nodeName, len(relatedENIs))

		if len(relatedENIs) < *maxENINum {
			err = CreateENI(ctx, eniClient)
			if err != nil {
				log.Errorf(ctx, "error CreateENI: %v", err)
			}
		} else {
			err = ReleaseENI(ctx, eniClient, relatedENIs[*maxENINum-1].EniId)
			if err != nil {
				log.Errorf(ctx, "error ReleaseENI: %v", err)
			}
		}

	nextRound:
		time.Sleep(5 * time.Second)
	}
}

func eniOwnedByNode(eniName, clusterID, instanceID string) bool {
	if !eniCreatedByCCE(eniName) {
		return false
	}
	// ENICreatedByCCE ensures len(parts) == 4
	parts := strings.Split(eniName, "/")
	return clusterID == parts[0] && instanceID == parts[1]
}

func eniCreatedByCCE(eniName string) bool {
	const eniNamePartNum int = 4
	parts := strings.Split(eniName, "/")
	if len(parts) != eniNamePartNum {
		return false
	}
	// parts[0] is clusterId and parts[1] is instanceId
	if !(strings.HasPrefix(parts[0], "c-") || strings.HasPrefix(parts[0], "cce-")) || !strings.HasPrefix(parts[1], "i-") {
		return false
	}

	return true
}

func CreateENI(ctx context.Context, eniClient *enisdk.Client) error {
	eni, err := eniClient.CreateEni(&enisdk.CreateEniArgs{
		Name:             util.CreateNameForENI(*clusterID, *instanceID, *nodeName),
		SubnetId:         subnetID,
		SecurityGroupIds: []string{securityGroupID},
		PrivateIpSet:     []enisdk.PrivateIp{{Primary: true}},
		Description:      "auto created by cce-cni, do not modify",
	})
	if err != nil {
		return err
	}

	log.Infof(ctx, "create eni %v for node %v successfully", eni.EniId, *nodeName)

	err = eniClient.AttachEniInstance(&enisdk.EniInstance{
		EniId:      eni.EniId,
		InstanceId: *instanceID,
	})
	if err != nil {
		return err
	}
	log.Infof(ctx, "attach eni %v for node %v successfully", eni.EniId, *nodeName)

	time.Sleep(40 * time.Second)

	resp, err := eniClient.GetEniDetail(eni.EniId)
	if err != nil {
		return err
	}

	if resp.Status != "inuse" {
		_ = eniClient.DeleteEni(&enisdk.DeleteEniArgs{
			EniId: eni.EniId,
		})
		return fmt.Errorf("eni %v not inuse after attaching", eni.EniId)
	}

	log.Infof(ctx, "eni %v attached for node %v successfully", eni.EniId, *nodeName)

	return nil
}

func ReleaseENI(ctx context.Context, eniClient *enisdk.Client, eniID string) error {
	err := eniClient.DetachEniInstance(&enisdk.EniInstance{
		EniId:      eniID,
		InstanceId: *instanceID,
	})
	if err != nil {
		log.Errorf(ctx, "failed to detach eni %v for instance %v: %v", eniID, *instanceID, err)
		return err
	}

	log.Infof(ctx, "detach eni %v for node %v successfully", eniID, *nodeName)

	time.Sleep(40 * time.Second)

	resp, err := eniClient.GetEniDetail(eniID)
	if err != nil {
		return err
	}

	if resp.Status != "available" {
		return fmt.Errorf("eni %v not available after detaching", eniID)
	}

	err = eniClient.DeleteEni(&enisdk.DeleteEniArgs{
		EniId: eniID,
	})
	if err != nil {
		return err
	}

	log.Infof(ctx, "eni %v detached for node %v successfully", eniID, *nodeName)

	return nil
}

func NewENIClient(ak, sk, sessionToken string) (*enisdk.Client, error) {
	c, err := enisdk.NewClient(ak, sk, bccEndpoint)
	if err != nil {
		return nil, err
	}

	if sessionToken != "" {
		stsCredential, err := auth.NewSessionBceCredentials(ak, sk, sessionToken)
		if err != nil {
			return nil, err
		}
		c.Config.Credentials = stsCredential
	}

	return c, nil
}

func NewBCCClient(ak, sk, sessionToken string) (*bccsdk.Client, error) {
	c, err := bccsdk.NewClient(ak, sk, bccEndpoint)
	if err != nil {
		return nil, err
	}

	if sessionToken != "" {
		stsCredential, err := auth.NewSessionBceCredentials(ak, sk, sessionToken)
		if err != nil {
			return nil, err
		}
		c.Config.Credentials = stsCredential
	}

	return c, nil
}
