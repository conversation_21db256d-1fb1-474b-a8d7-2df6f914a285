package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"path/filepath"

	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/jsonmergepatch"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/apis/networking/v1alpha1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/generated/clientset/versioned"
)

func main() {
	var kubeconfig *string
	if home := homedir.HomeDir(); home != "" {
		kubeconfig = flag.String("kubeconfig", filepath.Join(home, ".kube", "config"), "(optional) absolute path to the kubeconfig file")
	} else {
		kubeconfig = flag.String("kubeconfig", "", "absolute path to the kubeconfig file")
	}
	flag.Parse()

	// use the current context in kubeconfig
	config, err := clientcmd.BuildConfigFromFlags("", *kubeconfig)
	if err != nil {
		log.Fatalf("build config error: %v", err)
	}

	crdClient := versioned.NewForConfigOrDie(config)
	kubeClient := kubernetes.NewForConfigOrDie(config)
	ctx := context.TODO()

	wepList, err := crdClient.CceV1alpha1().WorkloadEndpoints(v1.NamespaceAll).List(ctx, metav1.ListOptions{})
	if err != nil {
		log.Fatalf("get wep list failed: %v", err)
	}

	for _, wep := range wepList.Items {
		if wep.Spec.PodUID != "" {
			continue
		}

		pod, err := kubeClient.CoreV1().Pods(wep.Namespace).Get(ctx, wep.Name, metav1.GetOptions{})
		if err != nil {
			log.Printf("get pod (%v %v) error: %v, skip...", wep.Namespace, wep.Name, err)
			continue
		}

		nwep := wep.DeepCopy()
		nwep.Spec.PodUID = string(pod.UID)

		patchBytes, err := getPatchBytes(&wep, nwep)
		if err != nil {
			log.Printf("get patch bytes for pod (%v %v) error: %v, skip...", wep.Namespace, wep.Name, err)
			continue
		}

		log.Println(string(patchBytes))

		_, err = crdClient.CceV1alpha1().WorkloadEndpoints(wep.Namespace).Patch(ctx, wep.Name, types.MergePatchType, patchBytes, metav1.PatchOptions{})
		if err != nil {
			log.Printf("patch poduid for wep (%v %v) error: %v, skip...", wep.Namespace, wep.Name, err)
			continue
		} else {
			log.Printf("patch poduid for wep (%v %v) successfully", wep.Namespace, wep.Name)
		}

	}

}

func getPatchBytes(oldWep *v1alpha1.WorkloadEndpoint, newWep *v1alpha1.WorkloadEndpoint) ([]byte, error) {
	oldData, err := json.Marshal(oldWep)
	if err != nil {
		return nil, fmt.Errorf("failed to Marshal oldData for %s/%s: %v", oldWep.Namespace, oldWep.Name, err)
	}

	newData, err := json.Marshal(newWep)
	if err != nil {
		return nil, fmt.Errorf("failed to Marshal newData for  %s/%s: %v", newWep.Namespace, newWep.Name, err)
	}

	patchBytes, err := jsonmergepatch.CreateThreeWayJSONMergePatch(oldData, newData, oldData)
	if err != nil {
		return nil, fmt.Errorf("failed to CreateTwoWayMergePatch for wep %s/%s: %v", oldWep.Namespace, oldWep.Name, err)
	}
	return patchBytes, nil
}
