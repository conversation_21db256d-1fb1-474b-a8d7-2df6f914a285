package main

import (
	"bufio"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"strings"

	"github.com/baidubce/bce-sdk-go/auth"
	enisdk "github.com/baidubce/bce-sdk-go/services/eni"

	vpcsdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/vpc"
	log "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/cni-suite/pkg/util/logger"
)

var (
	stsConfig   *string
	vpcID       *string
	v1ClusterID *string
	v2ClusterID *string
	region      *string
)

type STSConfig struct {
	AccessKeyID     string `json:"AccessKeyID"`
	SecretAccessKey string `json:"SecretAccessKey"`
	SessionToken    string `json:"sessionToken"`
}

func main() {
	parseFlags()

	ctx := context.TODO()

	content, err := ioutil.ReadFile(*stsConfig)
	if err != nil {
		log.Fatalf(ctx, "read sts config file error: %v", err)
	}

	var sts STSConfig
	err = json.Unmarshal(content, &sts)
	if err != nil {
		log.Fatalf(ctx, "read sts config file error: %v", err)
	}

	eniClient, err := newClient(sts.AccessKeyID, sts.SecretAccessKey, sts.SessionToken)
	if err != nil {
		log.Fatalf(ctx, "new eni client failed: %v", err)
	}

	listEniResult, err := eniClient.ListEni(&enisdk.ListEniArgs{
		VpcId:   *vpcID,
		MaxKeys: 1000,
		Name:    *v1ClusterID,
	})
	if err != nil {
		log.Fatalf(ctx, "list eni failed: %v", err)
	}

	for _, eni := range listEniResult.Eni {
		if !ENICreatedByCluster(&eni, *v1ClusterID) {
			continue
		}
		log.Infof(ctx, "replace old eni %v to %v", eni.Name, newENIName(eni.Name, *v1ClusterID, *v2ClusterID))
	}

	prompt()

	for _, eni := range listEniResult.Eni {
		if !ENICreatedByCluster(&eni, *v1ClusterID) {
			continue
		}
		newName := newENIName(eni.Name, *v1ClusterID, *v2ClusterID)
		err := eniClient.UpdateEni(&enisdk.UpdateEniArgs{
			EniId:       eni.EniId,
			Name:        newName,
			Description: eni.Description,
		})
		if err != nil {
			log.Errorf(ctx, "failed to replace old eni %v to %v: %v", eni.Name, newName, err)
		} else {
			log.Infof(ctx, "replace old eni %v to %v done", eni.Name, newName)
		}
	}
}

func newENIName(oldName, v1Cluster, v2Cluster string) string {
	return strings.Replace(oldName, v1Cluster, v2Cluster, 1)
}

func parseFlags() {
	stsConfig = flag.String("sts", "sts.json", "")
	vpcID = flag.String("vpc", "vpc-vqgcn7jcdvc0", "")
	v1ClusterID = flag.String("v1-cluster", "", "")
	v2ClusterID = flag.String("v2-cluster", "", "")
	region = flag.String("region", "fwh", "")

	flag.Parse()
}

func newClient(ak, sk, sessionToken string) (*enisdk.Client, error) {
	c, err := enisdk.NewClient(ak, sk, vpcsdk.Endpoints[*region])
	if err != nil {
		return nil, err
	}

	if sessionToken != "" {
		stsCredential, err := auth.NewSessionBceCredentials(ak, sk, sessionToken)
		if err != nil {
			return nil, err
		}
		c.Config.Credentials = stsCredential
	}

	return c, nil
}

func ENICreatedByCluster(eni *enisdk.Eni, clusterID string) bool {
	if eni == nil {
		return false
	}
	const eniNamePartNum int = 4
	parts := strings.Split(eni.Name, "/")
	if len(parts) != eniNamePartNum {
		return false
	}
	// parts[0] is clusterId and parts[1] is instanceId
	if !(strings.HasPrefix(parts[0], "c-") || strings.HasPrefix(parts[0], "cce-")) || !strings.HasPrefix(parts[1], "i-") {
		return false
	}

	if parts[0] != clusterID {
		return false
	}

	return true
}

func prompt() {
	fmt.Printf("-> Press Return key to continue rename cluster enis.")
	scanner := bufio.NewScanner(os.Stdin)
	for scanner.Scan() {
		break
	}
	if err := scanner.Err(); err != nil {
		panic(err)
	}
	fmt.Println()
}
