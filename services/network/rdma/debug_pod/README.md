# RDMA debug pod

```bash
# 启动容器
$nvidia-docker run --shm-size=64g -it --privileged --network=host -v /data2/xym/PaddleClas:/paddle registry.baidubce.com/cce-plugin-dev/rdma-debug-pod:v0.0.1  /bin/bash

# 测试
# ib_read_bw -d mlx5_bond_0 ***********
---------------------------------------------------------------------------------------
                    RDMA_Read BW Test
 Dual-port       : OFF		Device         : mlx5_bond_0
 Number of qps   : 1		Transport type : IB
 Connection type : RC		Using SRQ      : OFF
 PCIe relax order: ON
 ibv_wr* API     : ON
 TX depth        : 128
 CQ Moderation   : 1
 Mtu             : 1024[B]
 Link type       : Ethernet
 GID index       : 3
 Outstand reads  : 16
 rdma_cm QPs	 : OFF
 Data ex. method : Ethernet
---------------------------------------------------------------------------------------
 local address: LID 0000 QPN 0x14dd PSN 0x13f4d4 OUT 0x10 RKey 0x182ce2 VAddr 0x007fdf99b4c000
 GID: 00:00:00:00:00:00:00:00:00:00:255:255:172:16:48:05
 remote address: LID 0000 QPN 0x14dd PSN 0xe84b2c OUT 0x10 RKey 0x182ce2 VAddr 0x007f9d96786000
 GID: 00:00:00:00:00:00:00:00:00:00:255:255:172:16:48:06
---------------------------------------------------------------------------------------
 #bytes     #iterations    BW peak[MB/sec]    BW average[MB/sec]   MsgRate[Mpps]
Conflicting CPU frequency values detected: 999.914000 != 1028.637000. CPU Frequency is not max.
 65536      1000             2755.41            2755.37		   0.044086
---------------------------------------------------------------------------------------
```