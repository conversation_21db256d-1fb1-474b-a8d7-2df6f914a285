apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ib-read-bw
  labels:
    app: ib-read-bw
spec:
  selector:
    matchLabels:
      app: ib-read-bw
  template:
    metadata:
      labels:
        app: ib-read-bw
    spec:
      nodeSelector:
        beta.kubernetes.io/arch: amd64
      tolerations:
        - operator: "Exists"
      restartPolicy: Always
      containers:
        - name: rdma-debug-pod
          image: registry.baidubce.com/cce-plugin-dev/rdma-debug-pod:v0.0.1
          command:
            - /bin/bash
            - "-c"
            - |
              SERVER_NODE="***********"
              if [[ $NODE_NAME == $SERVER_NODE ]];then
                echo "i am server"
                ib_read_bw -d mlx5_bond_0
              else
                echo "i am client"
              fi
              sleep inf
          resources:
            limits:
              rdma/hca: "1"
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          imagePullPolicy: IfNotPresent
          securityContext:
            privileged: true
      terminationGracePeriodSeconds: 0



