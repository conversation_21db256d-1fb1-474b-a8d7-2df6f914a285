FROM ubuntu:18.04

ENV HOME_WORK_DIR=/home/<USER>
WORKDIR ${HOME_WORK_DIR}

RUN mkdir -p ${HOME_WORK_DIR}

# Install RDMA Driver
COPY MLNX_OFED_LINUX-5.3-1.0.0.1-ubuntu18.04-x86_64.tgz ./

RUN apt-get update && apt-get install -y perl

RUN tar zxf MLNX_OFED_LINUX-5.3-1.0.0.1-ubuntu18.04-x86_64.tgz -C ${HOME_WORK_DIR}/ && \
    cd ${HOME_WORK_DIR}/MLNX_OFED_LINUX-5.3-1.0.0.1-ubuntu18.04-x86_64 && \
    ./mlnxofedinstall --user-space-only --force

