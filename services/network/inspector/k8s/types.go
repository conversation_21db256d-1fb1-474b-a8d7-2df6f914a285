package k8s

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// Interface defines the functions to get K8S resources
// CCE contains 4 different kinds of PodIP:
// 1. VPC IP
// 2. ENI in VPC IP
// 3. ENI IP
// 4. HostNetwork IP
type Interface interface {
	GetNodes(ctx context.Context) ([]*Node, error)
	GetDestinationPods(ctx context.Context, destType probe.DestinationType) ([]*Pod, error)
	GetServices(ctx context.Context) ([]*Service, error)

	// check if broadcastJob finished
	BroadcastJobFinished(ctx context.Context, broadcastJob string) (bool, error)
}

// Node defines node info to do probe
type Node struct {
	NodeIP string `json:"nodeIP"`
}

// Pod defines pod info to do probe
type Pod struct {
	PodIP   string `json:"podIP"`
	PodPort int32  `json:"podPort"`
	NodeIP  string `json:"nodeIP"`
}

// Service defines service info to do probe
type Service struct {
	ServiceName string `json:"serviceName"`
	ClusterIP   string `json:"clusterIP"`
	Port        int32  `json:"port"`
	NodePort    int32  `json:"nodePort"`
}
