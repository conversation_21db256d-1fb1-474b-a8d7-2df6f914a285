package env

import (
	"context"
	"fmt"
	"os"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

const (
	defaultNamespace = "cce-network"

	// 通过环境变量获取 mysql 相关信息
	mysqlEndpoint = "cce-network-inspector-database"
	mysqlPort     = 3306
	mysqlUserName = "cce_inspect"
	mysqlPassword = "Ngvgzzo.CJds*Xg9rVH9"
	mysqlDatabase = "cce_network_inspector"

	// 通过环境变量获取运行本程序的 Pod 相关信息
	envMyPodIP            = "MY_POD_IP"
	envMyNodeIP           = "MY_NODE_IP"
	envMySourceType       = "MY_SOURCE_TYPE"
	envNamespace          = "NAMESPACE"
	envMySQLEndpoint      = "MYSQL_ENDPOINT"
	envBroadcastJobWaited = "BROADCAST_JOB_WAITED"
)

// CurrentPodIP - return current PodIP from ENV
func CurrentPodIP(ctx context.Context) (string, error) {
	podIP, exist := os.LookupEnv(envMyPodIP)
	if !exist {
		msg := fmt.Sprintf("ENV %s not exist", envMyPodIP)
		logger.Errorf(ctx, msg)
		return "", fmt.Errorf("ENV %s not exist", envMyPodIP)
	}
	return podIP, nil
}

// CurrentNodeIP - return current NodeIP from ENV
func CurrentNodeIP(ctx context.Context) (string, error) {
	nodeIP, exist := os.LookupEnv(envMyNodeIP)
	if !exist {
		msg := fmt.Sprintf("ENV %s not exist", envMyNodeIP)
		logger.Errorf(ctx, msg)
		return "", fmt.Errorf(msg)
	}
	return nodeIP, nil
}

// CurrentSourceType - return current sourceType from ENV
func CurrentSourceType(ctx context.Context) (probe.SourceType, error) {
	sourceType, exist := os.LookupEnv(envMySourceType)
	if !exist {
		msg := fmt.Sprintf("ENV %s not exist", sourceType)
		logger.Errorf(ctx, msg)
		return "", fmt.Errorf(msg)
	}

	switch sourceType {
	case string(probe.SourceTypeVPCPod):
		return probe.SourceTypeVPCPod, nil
	case string(probe.SourceTypeENIInVPCPod):
		return probe.SourceTypeENIInVPCPod, nil
	case string(probe.SourceTypeENIPod):
		return probe.SourceTypeENIPod, nil
	case string(probe.SourceTypeNode):
		return probe.SourceTypeNode, nil
	}

	msg := fmt.Sprintf("unknown source type: %s", sourceType)
	logger.Errorf(ctx, msg)
	return "", fmt.Errorf(msg)
}

// Namespace - return current namespace from ENV, use default if not set
func Namespace(ctx context.Context) string {
	namespace, exist := os.LookupEnv(envNamespace)
	if !exist {
		namespace = defaultNamespace
		logger.Warnf(ctx, "Env %s not set, use default: %s", envNamespace, defaultNamespace)
	}
	return namespace
}

// MySQLEndpoint - return mysql endpoint from ENV, use default if not set
func MySQLEndpoint(ctx context.Context) string {
	endpoint, exist := os.LookupEnv(envMySQLEndpoint)
	if !exist {
		endpoint = fmt.Sprintf("%v:%v@tcp(%v:%v)/%v?charset=utf8", mysqlUserName, mysqlPassword, mysqlEndpoint, mysqlPort, mysqlDatabase)
		logger.Warnf(ctx, "Env %s not set, use default: %s", envMySQLEndpoint, endpoint)
	}

	return endpoint
}

// BroadcastJobWaited - return waited broadcastJOb from ENV, use empty if not set
func BroadcastJobWaited(ctx context.Context) string {
	broadcastJob, exist := os.LookupEnv(envBroadcastJobWaited)
	if !exist {
		broadcastJob = ""
		logger.Warnf(ctx, "Env %s not set, use empty", envBroadcastJobWaited)
	}
	return broadcastJob
}
