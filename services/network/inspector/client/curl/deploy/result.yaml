apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-network-result-serviceaccount
  namespace: cce-curl

---

apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: cce-network-result-role
  namespace: cce-curl
rules:
- apiGroups: [""]
  resources:
  - configmaps
  verbs: ["get", "list", "update"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cce-network-result-binding
  namespace: cce-curl
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: cce-network-result-role
subjects:
- kind: ServiceAccount
  name: cce-network-result-serviceaccount
  namespace: cce-curl

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-inspect-result
  namespace: cce-curl
data:
  "cce-inspector": "result"

---  
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-network-result
  namespace: cce-curl
  labels:
    cce-network-result: cce-network-result
spec:
  replicas: 1
  selector:
    matchLabels:
      cce-network-result: cce-network-result
  template:
    metadata:
      labels:
        cce-network-result: cce-network-result
    spec:
      serviceAccountName: cce-network-result-serviceaccount
      containers:
      - name: result
        image: {{.Values.ResultImageID}}
        imagePullPolicy: Always
        resources:
          limits:
            cpu: 100m
            memory: 100Mi
          requests:
            cpu: 100m
            memory: 70Mi
        env:
          - name: MYSQL_ENDPOINT
            value: cce_inspect:Ngvgzzo.CJds*Xg9rVH9@tcp(cce-network-inspector-database:3306)/cce_network_inspector?charset=utf8&parseTime=true
          - name: RESULT_CONFIGMAP_NAMESPACE
            value: cce-network
          - name: RESULT_CONFIGMAP
            value: cce-inspect-result