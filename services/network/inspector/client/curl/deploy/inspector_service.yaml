apiVersion: v1
kind: Service
metadata:
  name: cce-network-inspector-database
  namespace: cce-curl
spec:
  selector:
    cce-network-inspector: store
  type: NodePort
  sessionAffinity: None
  ports:
  - port: 3306
    targetPort: 3306

---
apiVersion: v1
kind: Service
metadata:
  name: host-network-0
  namespace: cce-curl
  labels:
    cce-network-inspector: host-network-0
spec:
  selector:
    cce-network-inspector: host-network-0
  type: NodePort
  sessionAffinity: None
  ports:
    - port: 80
      targetPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: vpc-ip-0
  namespace: cce-curl
  labels:
    cce-network-inspector: vpc-ip-0
spec:
  selector:
    cce-network-inspector: vpc-ip-0
  type: NodePort
  sessionAffinity: None
  ports:
    - port: 80
      targetPort: 80

---
apiVersion: v1
kind: Service
metadata:
  name: vpc-ip-1
  namespace: cce-curl
  labels:
    cce-network-inspector: vpc-ip-1
spec:
  selector:
    cce-network-inspector: vpc-ip-1
  type: NodePort
  sessionAffinity: None
  ports:
    - port: 80
      targetPort: 80
