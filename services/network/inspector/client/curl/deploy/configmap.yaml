apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-network-inspector-config
  namespace: cce-curl
data:
  app.json: |
    {
      "probeType": "curl",
      "probeRounds": 5,
      "probeRoundInterval": 30,
      "probeInterval": 30,
      "probeConcurrency": 5,
      "saveConcurrency": 5,
      "destinationPort": 80,
      "podNumPerNode": 2,
      "ExternalEndpoints":[
        {
            "www.baidu.com":80
        }
      ]
    }
  kubeconfig: |
    apiVersion: v1
    clusters:
    - cluster:
        certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVQ0hqMnJOSnZTRGpGUHg3ckZ2Zkt6Nnk3Skw4d0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdIaGNOTWpBeE1URTJNVEV4TlRBd1doY05NekF4TVRFME1URXhOVEF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ05WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFMVTAvRTdDSi80WDVQYzMKMkZLR25YMkkvK2ZyN3p1Mnhpb05ydFdabThEeUt6T0pDSVFIVHk3cnJJUTloQXBVaWlWa3pvNVRPdnZPU3htaQpobXRNSVFtL09JL0Q5ZW5yUFJOTXo5cEE4UUQzYXY5Z2orbkhvV0lVeU9ibythcThObCtENTF2SFI1cGpDNGxBCjZ3aFM2eFk2OWZiaDdwQnNrQ01iZG1yK253MzdhdVN0L2lrMUVudjFxMkVFWlFpVldqL1U4UmxNZmpwYTZBdUkKVURmUDY4WnpLdFhYWmZSd0NLbUM4dk90SmlZcUVWZ2ZJaUUvY0RxSUYwbWpIL21uYmwxN3ZoSlBBNmViNUFxUApDZTJRNU92Z0l6UWU1MTAzNWVnbTE5SVJ5alJzaGRtRkUrK1ltaW15WXJ5T09CRW1wTHJmOURDTWNqa3BxRmM5CmwyNFB3SjhDQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHcKSFFZRFZSME9CQllFRkViQVMrQ0cxb3JrVVVtK0VXRWpUb2UyVWpNRE1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQgpBUUJiVlpoOE1CMjV5VE9YV1RCdG5IZWtKMXo2b21vTDd0QkVmTjk0S1FDaklxV0JNdUJEU08vcHdKVElSYmM1CnZoWVYrU0oyME9QTEthZDFBYW9sMlIrTVlCSHIzRjk5VWVZRzJXZVF3WUpsb3F6S0pWNFB1RWtnWTJuRWcxZjEKZWFwMzYxMEtEK1NtdmxaR2tuNGJSVmZIQVA2Z1BLaHMrdzZtaGtmYnNGTC9HZmZtRWhzSitVU0RMcHd6bk9tcgo2b1hBZjZNcXRGbnQ1NU1GWG91NHRGSzRHTHJJVVl2NFcyMXBZMlV4aGVGekxQSHRNQ0duTEZvMXZ0VHZFbTNTCjZOMzBMSGxqMFROUjNFYzhzM0tLMG01VU9lN0k3RDk0dDYrWXI3RVl5WEdYOVVJYWdMVUhaUEhRbFd2Ly8rUjIKdmYxSkQ4UEowbjZvTnRNZFVFUlFZZnRBCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
        server: https://192.168.2.68:6443
      name: kubernetes
    contexts:
    - context:
        cluster: kubernetes
        user: eca97e148cb74e9683d7b7240829d1ff
      name: eca97e148cb74e9683d7b7240829d1ff@kubernetes
    current-context: eca97e148cb74e9683d7b7240829d1ff@kubernetes
    kind: Config
    preferences: {}
    users:
    - name: eca97e148cb74e9683d7b7240829d1ff
      user:
        client-certificate-data: 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
        client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
