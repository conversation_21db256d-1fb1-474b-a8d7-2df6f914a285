apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-network-inspector-store
  namespace: cce-curl
spec:
  selector:
    matchLabels:
      cce-network-inspector: store
  template:
    metadata:
      labels:
        cce-network-inspector: store
    spec:
      hostNetwork: true
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-mysql:latest
          imagePullPolicy: Always
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: Nisjdfuf.SEds*Xrsdfs
            - name: MYSQL_DATABASE
              value: cce_network_inspector
            - name: MYSQL_USER
              value: cce_inspect
            - name: MYSQL_PASSWORD
              value: Ngvgzzo.CJds*Xg9rVH9
          ports:
            - containerPort: 3306