apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-network-inspector-serviceaccount
  namespace: cce-curl

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: cce-network-inspector-role
  namespace: cce-curl
rules:
- apiGroups: [""]
  resources:
  - pods
  - nodes
  - services
  verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cce-network-inspector-binding
  namespace: cce-curl
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: cce-network-inspector-role
subjects:
- kind: ServiceAccount
  name: cce-network-inspector-serviceaccount
  namespace: cce-curl

---  
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: host-network-0
  namespace: cce-curl
spec:
  selector:
    matchLabels:
      cce-network-inspector: host-network-0
  template:
    metadata:
      labels:
        cce-network-inspector: host-network-0
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      serviceAccountName: cce-network-inspector-serviceaccount
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector:yuhongwei
          imagePullPolicy: Always
          env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: MY_SOURCE_TYPE
              value: "node"
          volumeMounts:
          - mountPath: /home/<USER>/cce/cce-network-inspector/conf/
            name: cce-network-inspector-config 
        - name: nginx
          image: registry.baidubce.com/cce-plugin-pro/nginx-alpine-go:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
      volumes:
      - configMap:
          defaultMode: 420
          name: cce-network-inspector-config
        name: cce-network-inspector-config
  updateStrategy:
    type: RollingUpdate

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-0
  namespace: cce-curl
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-0
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-0
    spec:
      serviceAccountName: cce-network-inspector-serviceaccount
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector:yuhongwei
          imagePullPolicy: Always
          env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: MY_SOURCE_TYPE
              value: "vpc_pod"
          volumeMounts:
          - mountPath: /home/<USER>/cce/cce-network-inspector/conf/
            name: cce-network-inspector-config 
        - name: nginx
          image: registry.baidubce.com/cce-plugin-pro/nginx-alpine-go:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
      volumes:
      - configMap:
          defaultMode: 420
          name: cce-network-inspector-config
        name: cce-network-inspector-config
  updateStrategy:
    type: RollingUpdate

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-1
  namespace: cce-curl
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-1
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-1
    spec:
      serviceAccountName: cce-network-inspector-serviceaccount
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector:yuhongwei
          imagePullPolicy: Always
          env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: MY_SOURCE_TYPE
              value: "vpc_pod"
          volumeMounts:
          - mountPath: /home/<USER>/cce/cce-network-inspector/conf/
            name: cce-network-inspector-config 
        - name: nginx
          image: registry.baidubce.com/cce-plugin-pro/nginx-alpine-go:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 80
      volumes:
      - configMap:
          defaultMode: 420
          name: cce-network-inspector-config
        name: cce-network-inspector-config
  updateStrategy:
    type: RollingUpdate