apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-ping
  name: cce-network-inspector-config
data:
  app.json: |
    {
      "probeType": "ping",
      "probeRounds": 5000,
      "probeRoundInterval": 20,
      "probeInterval": 20,
      "timeUnit": "millisecond",
      "probeConcurrency": 1,
      "saveConcurrency": 1,
      "podNumPerNode": 20,
      "pingOptions": [
        {
          "count": 2,
          "interval": 0.02,
          "packetSize": 56
        }
      ],
      "vpcPod": true,
      "skipLocalNode": true,
      "sleepAfterFinish": 0
    }