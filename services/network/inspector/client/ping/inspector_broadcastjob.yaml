# vpc-ip
apiVersion: apps.kruise.io/v1alpha1
kind: BroadcastJob
metadata:
  name: cce-ping-bj-vpc-ip
  namespace: cce-ping
spec:
  template:
    metadata:
      labels:
        cce-network-inspector: broadcast-job
    spec:
      containers:
        - name: netperf
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector:yuhongwei
          imagePullPolicy: Always
          env:
            - name: MY_POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: MY_NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: MY_SOURCE_TYPE
              value: "vpc_pod"
            - name: NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: BROADCAST_JOB_WAITED
              value: ""
          volumeMounts:
            - mountPath: /home/<USER>/cce/cce-network-inspector/conf
              name: cce-network-inspector-config
      restartPolicy: Never
      volumes:
        - name: cce-network-inspector-config
          configMap:
            name: cce-network-inspector-config
            defaultMode: 420
  completionPolicy:
    type: Always
    ttlSecondsAfterFinished: 30
  parallelism: 1

#---
## host network
#apiVersion: apps.kruise.io/v1alpha1
#kind: BroadcastJob
#metadata:
#  name: cce-ping-bj-host-network
#  namespace: cce-ping
#spec:
#  template:
#    metadata:
#      labels:
#        cce-network-inspector: broadcast-job
#    spec:
#      containers:
#      - name: netperf
#        image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector:yuhongwei
#        imagePullPolicy: Always
#        env:
#        - name: MY_POD_IP
#          valueFrom:
#            fieldRef:
#              fieldPath: status.podIP
#        - name: MY_NODE_IP
#          valueFrom:
#            fieldRef:
#              fieldPath: status.hostIP
#        - name: MY_SOURCE_TYPE
#          value: "node"
#        - name: NAMESPACE
#          valueFrom:
#            fieldRef:
#              fieldPath: metadata.namespace
#        - name: BROADCAST_JOB_WAITED
#          value: ""
#        volumeMounts:
#        - mountPath: /home/<USER>/cce/cce-network-inspector/conf
#          name: cce-network-inspector-config
#      restartPolicy: Never
#      hostNetwork: true
#      dnsPolicy: ClusterFirstWithHostNet
#      volumes:
#      - name: cce-network-inspector-config
#        configMap:
#          name: cce-network-inspector-config
#          defaultMode: 420
#  completionPolicy:
#    type: Always
#    ttlSecondsAfterFinished: 30
#  parallelism: 1
#
#---
## eni-in-vpc
#apiVersion: apps.kruise.io/v1alpha1
#kind: BroadcastJob
#metadata:
#  name: cce-ping-bj-eni-in-vpc
#  namespace: cce-ping
#spec:
#  template:
#    metadata:
#      labels:
#        cce-network-inspector: broadcast-job
#    spec:
#      containers:
#      - name: netperf
#        image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector:yuhongwei
#        imagePullPolicy: Always
#        resources:
#          limits:
#            baiduyun/eni: "1"
#        env:
#        - name: MY_POD_IP
#          valueFrom:
#            fieldRef:
#              fieldPath: status.podIP
#        - name: MY_NODE_IP
#          valueFrom:
#            fieldRef:
#              fieldPath: status.hostIP
#        - name: MY_SOURCE_TYPE
#          value: "eni_in_vpc_pod"
#        - name: NAMESPACE
#          valueFrom:
#            fieldRef:
#              fieldPath: metadata.namespace
#        - name: BROADCAST_JOB_WAITED
#          value: "cce-ping-bj-vpc-ip"
#        volumeMounts:
#        - mountPath: /home/<USER>/cce/cce-network-inspector/conf
#          name: cce-network-inspector-config
#      restartPolicy: Never
#      volumes:
#      - name: cce-network-inspector-config
#        configMap:
#          name: cce-network-inspector-config
#          defaultMode: 420
#  completionPolicy:
#    type: Always
#    ttlSecondsAfterFinished: 30
#  parallelism: 1