apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-network-inspector-serviceaccount
  namespace: cce-netperf

---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: cce-network-inspector-role
  namespace: cce-netperf
rules:
  - apiGroups: [""]
    resources:
      - pods
      - nodes
      - services
    verbs: ["get", "list"]
  - apiGroups: ["apps.kruise.io/v1alpha1"]
    resources:
      - BroadcastJob
    verbs: ["get", "list"]

---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: cce-network-inspector-rolebinding
  namespace: cce-netperf
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: cce-network-inspector-role
subjects:
  - kind: ServiceAccount
    namespace: cce-netperf
    name: cce-network-inspector-serviceaccount
