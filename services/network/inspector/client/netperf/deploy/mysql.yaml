apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-netperf-mysql
  namespace: cce-netperf
spec:
  selector:
    matchLabels:
      cce-network-inspector: mysql
  template:
    metadata:
      labels:
        cce-network-inspector: mysql
    spec:
      hostNetwork: true
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-mysql:yuhongwei
          imagePullPolicy: Always
          env:
            - name: MYSQL_ROOT_PASSWORD
              value: Nisjdfuf.SEds*Xrsdfs
            - name: MYSQL_DATABASE
              value: cce_network_inspector
            - name: MYSQL_USER
              value: cce_inspect
            - name: MYSQL_PASSWORD
              value: Ngvgzzo.CJds*Xg9rVH9
          ports:
            - containerPort: 3306

---
apiVersion: v1
kind: Service
metadata:
  name: cce-network-inspector-database
  namespace: cce-netperf
spec:
  selector:
    cce-network-inspector: mysql
  type: NodePort
  sessionAffinity: None
  ports:
  - port: 3306
    targetPort: 3306
