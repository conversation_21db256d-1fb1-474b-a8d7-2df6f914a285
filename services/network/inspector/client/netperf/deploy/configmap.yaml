apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-netperf
  name: cce-netperf-config
data:
  app.json: |
    {
      "probeType": "netperf",
      "probeRounds": 1,
      "probeRoundInterval": 5,
      "probeInterval": 5,
      "ProbeIntervalUnit": "second",
      "probeConcurrency": 1,
      "saveConcurrency": 1,
      "destinationPort": 12865,
      "podNumPerNode": 1,
      "netperfOptions": [
        {
          "testSeconds": 5,
          "netperfType": "TCP_STREAM",
          "sendSocketBytes": 16383,
          "recvSocketBytes": 87379,
          "sendPkgBytes": 16383,
          "recvPkgBytes": 87379,
          "tcpNoDelay": false
        },
        {
          "testSeconds": 5,
          "netperfType": "TCP_RR",
          "sendSocketBytes": 16383,
          "recvSocketBytes": 87379,
          "sendPkgBytes": 16383,
          "recvPkgBytes": 87379,
          "tcpNoDelay": false
        },
        {
          "testSeconds": 5,
          "netperfType": "UDP_STREAM",
          "sendSocketBytes": 16383,
          "recvSocketBytes": 87379,
          "sendPkgBytes": 16383,
          "recvPkgBytes": 87379,
          "tcpNoDelay": false
        }
      ],
      "sleepAfterFinish": 0
    }