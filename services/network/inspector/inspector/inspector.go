package inspector

import (
	"context"
	"fmt"
	"sync"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	configp "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/env"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/k8s"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
	utilsp "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/utils"
)

type Inspector struct {
	K8SClient   k8s.Interface
	ProbeClient probe.Interface

	ProbeRounds        int
	ProbeRoundInterval int
	ProbeInterval      int
	TimeUnit           utilsp.TimeUnit

	ToProbeSem  chan int        // control the concurrency of probing link
	ToProbeChan chan probe.Link // channel of link to be probed

	ToSaveSem  chan int          // control the concurrency of saving record
	ToSaveChan chan probe.Record // channel of probe record to be saved

	ExternalEndpoints map[string]int32

	Options probe.Options

	VPCPod           bool
	ENIInVPCPod      bool
	ENIPod           bool
	Node             bool
	ClusterIPService bool
	NodePortService  bool
	ExternalURL      bool

	SkipLocalNode bool
}

func NewInspector(ctx context.Context, k8sClient k8s.Interface, probeClient probe.Interface, config *configp.Config) (*Inspector, error) {
	if config == nil {
		return nil, fmt.Errorf("config is nil")
	}

	options, err := config.GetProbeOptions(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetProbeOptions failed: %v", err)
		return nil, err
	}

	return &Inspector{
		K8SClient:   k8sClient,
		ProbeClient: probeClient,

		ProbeRounds:        config.ProbeRounds,
		ProbeRoundInterval: config.ProbeRoundInterval,
		ProbeInterval:      config.ProbeInterval,
		TimeUnit:           config.TimeUnit,

		ToProbeSem:  make(chan int, config.ProbeConcurrency),
		ToProbeChan: make(chan probe.Link, 100),

		ToSaveSem:  make(chan int, config.SaveConcurrency),
		ToSaveChan: make(chan probe.Record, 100),

		ExternalEndpoints: config.ExternalEndpoints,

		Options: options,

		VPCPod:           config.VPCPod,
		ENIInVPCPod:      config.ENIInVPCPod,
		ENIPod:           config.ENIPod,
		Node:             config.Node,
		ClusterIPService: config.ClusterIPService,
		NodePortService:  config.NodePortService,
		ExternalURL:      config.ExternalURL,

		SkipLocalNode: config.SkipLocalNode,
	}, nil
}

// Run the main process to probe networking with other pods
func (t *Inspector) Run(ctx context.Context) error {
	wg := sync.WaitGroup{}

	// collect target endpoints and send into ToProbeChan
	wg.Add(1)
	go func() {
		defer wg.Done()

		logger.Infof(ctx, "sendEndpoints start")

		if err := t.sendEndpoints(); err != nil {
			logger.Errorf(ctx, "sendEndpoints failed: %v", err)
		} else {
			logger.Infof(ctx, "sendEndpoints succeed")
		}

		close(t.ToProbeChan)
	}()

	// probe and send record into ToSaveChan
	wg.Add(1)
	go func() {
		defer wg.Done()

		logger.Infof(ctx, "probeLinks start")

		if err := t.probeLinks(); err != nil {
			logger.Errorf(ctx, "probeLinks failed: %v", err)
		} else {
			logger.Infof(ctx, "probeLinks succeed")
		}

		close(t.ToSaveChan)
	}()

	// save records
	wg.Add(1)
	go func() {
		defer wg.Done()

		logger.Infof(ctx, "saveRecords start")

		if err := t.saveRecords(); err != nil {
			logger.Errorf(ctx, "saveRecords failed: %v", err)
		} else {
			logger.Infof(ctx, "saveRecords succeed")
		}
	}()

	wg.Wait()

	return nil
}

// sendEndpoints to get endpoints and send it into ToProbeChan
func (t *Inspector) sendEndpoints() error {
	for round := 0; round < t.ProbeRounds; round++ {
		// context to trace operation
		ctx := context.WithValue(context.Background(), logger.RequestID, logger.GetUUID())

		logger.Infof(ctx, "================ round %d ================", round+1)

		// send into ToProbeChan, only when ToProbeChan is empty, waiting last round finished
		for len(t.ToProbeChan) != 0 {
			logger.Warnf(ctx, "last round not finished, wait for %d %s", t.ProbeRoundInterval, t.TimeUnit)
			utilsp.Sleep(t.ProbeRoundInterval, t.TimeUnit)
		}

		startTime := time.Now()
		logger.Infof(ctx, "sendEndpoints begin: %v", startTime)

		// get vpc ip pods
		if t.VPCPod {
			if err := t.sendPods(ctx, probe.DestinationTypeVPCPod); err != nil {
				logger.Errorf(ctx, "send vpc ip pods failed: %v", err)
				return err
			}
			logger.Infof(ctx, "send vpc ip pods succeed, cost %v", time.Now().Sub(startTime))
		}

		// get eni in vpc ip pods
		if t.ENIInVPCPod {
			if err := t.sendPods(ctx, probe.DestinationTypeENIInVPCPod); err != nil {
				logger.Errorf(ctx, "send eni in vpc ip pods failed: %v", err)
				return err
			}
			logger.Infof(ctx, "send eni in vpc pods succeed, cost %v", time.Now().Sub(startTime))
		}

		// get eni ip pods
		if t.ENIPod {
			if err := t.sendPods(ctx, probe.DestinationTypeENIPod); err != nil {
				logger.Errorf(ctx, "send eni ip pods failed: %v", err)
				return err
			}
			logger.Infof(ctx, "send eni pods succeed, cost %v", time.Now().Sub(startTime))
		}

		// get host network ip pods
		if t.Node {
			if err := t.sendPods(ctx, probe.DestinationTypeNode); err != nil {
				logger.Errorf(ctx, "send host network pod failed: %v", err)
				return err
			}
			logger.Infof(ctx, "send host network pods succeed, cost %v", time.Now().Sub(startTime))
		}

		// get external url
		if t.ExternalURL {
			if err := t.sendExternalURLs(ctx, probe.DestinationTypeExternalURL); err != nil {
				logger.Errorf(ctx, "send external urls failed: %v", err)
				return err
			}
			logger.Infof(ctx, "send external urls succeed, cost %v", time.Now().Sub(startTime))
		}

		// get all services
		if t.ClusterIPService || t.NodePortService {
			if err := t.sendServices(ctx); err != nil {
				logger.Errorf(ctx, "send services failed: %v", err)
				return err
			}
			logger.Infof(ctx, "send services succeed, cost %v", time.Now().Sub(startTime))
		}
	}

	return nil
}

func (t *Inspector) sendPods(ctx context.Context, destType probe.DestinationType) error {
	// get source podIP
	srcPodIP, err := env.CurrentPodIP(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentPodIP failed: %v", err)
		return err
	}

	// get source nodeIP
	srcNodeIP, err := env.CurrentNodeIP(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentNodeIP failed: %v", err)
		return err
	}

	// get source type
	sourceType, err := env.CurrentSourceType(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentSourceType failed: %v", err)
		return err
	}

	logger.Infof(ctx, "got current podIP=%s, nodeIP=%s, sourceType=%s", srcPodIP, srcNodeIP, sourceType)

	// get destination pods
	pods, err := t.K8SClient.GetDestinationPods(ctx, destType)
	if err != nil {
		logger.Errorf(ctx, "GetDestinationPods failed: %v", err)
		return err
	}

	logger.Infof(ctx, "GetDestinationPods destType=%s succeed: %s", destType, utils.ToJSON(pods))

	// send pods into ToProbeChan
	for _, pod := range pods {
		link := probe.Link{
			// Using CTX.Request to identify this process batch.
			RoundID: ctx.Value(logger.RequestID).(string),

			SourceType:   sourceType,
			SourcePodIP:  srcPodIP,
			SourceNodeIP: srcNodeIP,

			DestinationNodeIP: pod.NodeIP,

			DestinationType: destType,
			DestinationIP:   pod.PodIP,
			DestinationPort: pod.PodPort,
		}

		logger.Infof(ctx, "send link to ToProbeChan succeed: %s", utils.ToJSON(link))

		t.ToProbeChan <- link
	}

	return nil
}

func (t *Inspector) sendExternalURLs(ctx context.Context, destType probe.DestinationType) error {
	// Get Source PodIP
	srcPodIP, err := env.CurrentPodIP(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentPodIP failed: %v", err)
		return err
	}

	// Get Source NodeIP
	srcNodeIP, err := env.CurrentNodeIP(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentNodeIP failed: %v", err)
		return err
	}

	// Get Source type
	sourceType, err := env.CurrentSourceType(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentSourceType failed: %v", err)
		return err
	}

	// send urls into ToProbeChan
	for url, port := range t.ExternalEndpoints {
		// Generate probe link
		link := probe.Link{
			RoundID: ctx.Value(logger.RequestID).(string),

			SourceType:   sourceType,
			SourcePodIP:  srcPodIP,
			SourceNodeIP: srcNodeIP,

			DestinationType: destType,
			DestinationIP:   url,
			DestinationPort: port,
		}

		logger.Infof(ctx, "Send link to ToProbeChan succeed: %s", utils.ToJSON(link))

		// Add Link into ToProbeChan
		t.ToProbeChan <- link
	}

	return nil
}

func (t *Inspector) sendServices(ctx context.Context) error {
	// get source PodIP
	srcPodIP, err := env.CurrentPodIP(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentPodIP failed: %v", err)
		return err
	}

	// get source NodeIP
	srcNodeIP, err := env.CurrentNodeIP(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentNodeIP failed: %v", err)
		return err
	}

	// get source type
	sourceType, err := env.CurrentSourceType(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCurrentSourceType failed: %v", err)
		return err
	}

	// get all services
	services, err := t.K8SClient.GetServices(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetServices failed: %v", err)
		return err
	}

	// get all nodes
	nodes, err := t.K8SClient.GetNodes(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetNodes failed: %v", err)
		return err
	}

	for _, service := range services {
		// send ClusterIP into ToProbeChan
		t.ToProbeChan <- probe.Link{
			// Using CTX.Request to identify this process batch.
			RoundID: ctx.Value(logger.RequestID).(string),

			SourceType:   sourceType,
			SourcePodIP:  srcPodIP,
			SourceNodeIP: srcNodeIP,

			DestinationType: probe.DestinationTypeClusterIPService,
			DestinationIP:   service.ClusterIP,
			DestinationPort: service.Port,
		}

		// send ServiceName into ToProbeChan
		t.ToProbeChan <- probe.Link{
			// Using CTX.Request to identify this process batch.
			RoundID: ctx.Value(logger.RequestID).(string),

			SourceType:   sourceType,
			SourcePodIP:  srcPodIP,
			SourceNodeIP: srcNodeIP,

			DestinationType: probe.DestinationTypeServiceName,
			DestinationIP:   service.ServiceName,
			DestinationPort: service.Port,
		}

		// send all NodePort into ToProbeChan
		for _, node := range nodes {
			t.ToProbeChan <- probe.Link{
				// Using CTX.Request to identify this process batch.
				RoundID: ctx.Value(logger.RequestID).(string),

				SourceType:   sourceType,
				SourcePodIP:  srcPodIP,
				SourceNodeIP: srcNodeIP,

				DestinationType: probe.DestinationTypeNodePortService,
				DestinationIP:   node.NodeIP,
				DestinationPort: service.NodePort,
			}
		}
	}

	return nil
}

func (t *Inspector) probeLinks() error {
	wg := sync.WaitGroup{}

	for link := range t.ToProbeChan {
		ctx := context.WithValue(context.Background(), logger.RequestID, link.RoundID)

		if t.SkipLocalNode == true && link.SourceNodeIP == link.DestinationNodeIP {
			logger.Infof(ctx, "sourceNode is same as destinationNode, just skip ping: %s", utils.ToJSON(link))
			continue
		}

		for _, option := range t.Options.ToSlice() {
			t.ToProbeSem <- 1
			wg.Add(1)

			go func(link probe.Link, option probe.Option) {
				defer wg.Done()

				logger.Infof(ctx, "probeLink start: link = %s, option = %s", utils.ToJSON(link), utils.ToJSON(option))

				if err := t.probeLink(ctx, link, option); err != nil {
					logger.Errorf(ctx, "probeLink failed: %v, cooling for %d %s", err, t.ProbeInterval, t.TimeUnit)
				} else {
					logger.Infof(ctx, "probeLink succeed, cooling for %d %s", t.ProbeInterval, t.TimeUnit)
				}

				// wait...
				utilsp.Sleep(t.ProbeInterval, t.TimeUnit)

				// concurrency control
				<-t.ToProbeSem
			}(link, option)
		}
	}

	// wait all goroutine finished
	wg.Wait()

	return nil
}

func (t *Inspector) probeLink(ctx context.Context, link probe.Link, option probe.Option) error {
	if link.DestinationIP == "" {
		return fmt.Errorf("invaild link: %s", utils.ToJSON(link))
	}

	logger.Infof(ctx, "probe start: link = %s, option = %s", utils.ToJSON(link), utils.ToJSON(option))

	record, err := t.ProbeClient.Probe(ctx, &link, option)
	if err != nil {
		logger.Errorf(ctx, "probe failed: %v", err)
		// save record even failed
	} else {
		logger.Infof(ctx, "probe succeed: record = %s", utils.ToJSON(record))
	}

	// send not nil record into ToSaveChan
	if record != nil {
		t.ToSaveChan <- record
	} else {
		logger.Warnf(ctx, "got record is nil, skip send to channel of saving")
	}

	return nil
}

// saveRecords to get record from ToSaveChan and Save it
func (t *Inspector) saveRecords() error {
	wg := sync.WaitGroup{}

	for record := range t.ToSaveChan {
		t.ToSaveSem <- 1
		wg.Add(1)

		go func(record probe.Record) {
			defer wg.Done()

			ctx := context.WithValue(context.Background(), logger.RequestID, record.GetRoundID())

			logger.Infof(ctx, "save record start: record = %s", utils.ToJSON(record))
			if err := t.ProbeClient.Save(ctx, record); err != nil {
				logger.Errorf(ctx, "save record failed: %v", err)
			} else {
				logger.Infof(ctx, "save record succeed")
			}

			// concurrency control
			<-t.ToSaveSem
		}(record)
	}

	// wait all goroutine finished
	wg.Wait()

	return nil
}
