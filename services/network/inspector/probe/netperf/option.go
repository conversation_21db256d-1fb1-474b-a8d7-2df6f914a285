package netperf

import (
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// netperf 测试选项
type Option struct {
	NetperfType     Type `json:"netperfType" gorm:"column:netperf_type"`
	TestSeconds     int  `json:"testSeconds" gorm:"column:test_seconds"`
	SendSocketBytes int  `json:"sendSocketBytes" gorm:"column:send_socket_bytes"`
	RecvSocketBytes int  `json:"recvSocketBytes" gorm:"column:recv_socket_bytes"`
	SendPkgBytes    int  `json:"sendPkgBytes" gorm:"column:send_pkg_bytes"`
	RecvPkgBytes    int  `json:"recvPkgBytes" gorm:"column:recv_pkg_bytes"`
	TCPNoDelay      bool `json:"tcpNoDelay" gorm:"column:tcp_no_delay"`
}

type Type string

const (
	TypeTCPStream Type = "TCP_STREAM"
	TypeTCPRR     Type = "TCP_RR"
	TypeTCPCRR    Type = "TCP_CRR"
	TypeUDPStream Type = "UDP_STREAM"
	TypeUDPRR     Type = "UDP_RR"
)

func (option *Option) Valid() error {
	if option == nil {
		return fmt.Errorf("netperf option is nil")
	}

	if option.TestSeconds == 0 {
		option.TestSeconds = 30
	}

	if option.NetperfType == "" {
		option.NetperfType = TypeTCPStream
	}

	if option.SendSocketBytes == 0 {
		option.SendSocketBytes = 16384
	}

	if option.RecvSocketBytes == 0 {
		option.RecvSocketBytes = 87380
	}

	if option.SendPkgBytes == 0 {
		option.SendPkgBytes = 16384
	}

	if option.RecvPkgBytes == 0 {
		option.RecvPkgBytes = 87380
	}

	return nil
}

type Options []*Option

func (options Options) ToSlice() []probe.Option {
	interfaceSlice := make([]probe.Option, len(options))

	for idx, value := range options {
		interfaceSlice[idx] = value
	}

	return interfaceSlice
}

func (options Options) Valid() error {
	if len(options) == 0 {
		return fmt.Errorf("netperf options length is zero")
	}

	for _, option := range options {
		if err := option.Valid(); err != nil {
			return err
		}
	}

	return nil
}
