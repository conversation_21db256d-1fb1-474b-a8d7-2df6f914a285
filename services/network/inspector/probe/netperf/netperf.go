package netperf

import (
	"context"
	"fmt"
	"os/exec"
	"strconv"
	"strings"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	_ "github.com/jinzhu/gorm/dialects/sqlite"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/mysql"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// netperf implements a probe based on netperf
type netperf struct {
	db *gorm.DB
}

// New - create a netperf probe
func New(ctx context.Context) (probe.Interface, error) {
	db, err := mysql.New(ctx)
	if err != nil {
		logger.Errorf(ctx, "connect to mysql failed: %s", err)
		return nil, err
	}

	// create database
	db.AutoMigrate(Record{})

	return &netperf{db: db}, nil
}

// Probe - probe the link with specified option
func (c *netperf) Probe(ctx context.Context, link *probe.Link, option probe.Option) (probe.Record, error) {
	if link == nil {
		return nil, fmt.Errorf("link is nil")
	}

	opt, ok := option.(*Option)
	if !ok {
		return nil, fmt.Errorf("type(%v) != netperf.Option", option)
	}

	// netperf record
	record := &Record{
		Link:   *link,
		Option: *opt,
		Result: Result{},
	}

	// generate netperf command
	netperf := fmt.Sprintf("netperf -H %s -p %d -l %d -t %s -P 0 -- -s %d -S %d",
		link.DestinationIP, link.DestinationPort, opt.TestSeconds, opt.NetperfType, opt.SendSocketBytes, opt.RecvSocketBytes)

	switch opt.NetperfType {
	case TypeTCPStream, TypeUDPStream:
		netperf = fmt.Sprintf("%s -m %d -M %d", netperf, opt.SendPkgBytes, opt.RecvPkgBytes)
	case TypeTCPRR, TypeTCPCRR, TypeUDPRR:
		netperf = fmt.Sprintf("%s -r %d,%d", netperf, opt.SendPkgBytes, opt.RecvPkgBytes)
	}

	if opt.TCPNoDelay {
		netperf = fmt.Sprintf("%s -D", netperf)
	}

	netperf = fmt.Sprintf("%s > tmp_file && awk 'NR==1 {print}' tmp_file", netperf)

	// execute netperf command
	logger.Infof(ctx, "exec start: command = [ %s ]", netperf)
	cmd := exec.Command("/bin/bash", "-c", netperf)
	res, err := cmd.CombinedOutput()
	if err != nil {
		logger.Errorf(ctx, "exec failed: %v", err)
		return record, err
	}
	line := strings.TrimRight(string(res), "\n")

	logger.Infof(ctx, "exec succeed: result = [ %s ]", line)

	// parse netperf result
	arr := strings.Fields(strings.TrimSpace(line))

	switch opt.NetperfType {
	case TypeTCPStream:
		throughput, err := strconv.ParseFloat(arr[4], 64)
		if err != nil {
			logger.Errorf(ctx, "convert throughput %s to float failed: %v", arr[5], err)
			return record, err
		}
		record.Result.Throughput = throughput
	case TypeUDPStream:
		pkgOkNum, err := strconv.ParseInt(arr[3], 10, 64)
		if err != nil {
			logger.Errorf(ctx, "convert pkgOkNum %s to int64 failed: %v", arr[3], err)
			return record, err
		}
		record.Result.PkgOkNum = pkgOkNum

		pkgErrNum, err := strconv.ParseInt(arr[4], 10, 64)
		if err != nil {
			logger.Errorf(ctx, "convert pkgErrNum %s to int64 failed: %v", arr[3], err)
			return record, err
		}
		record.Result.PkgErrNum = pkgErrNum

		throughput, err := strconv.ParseFloat(arr[5], 64)
		if err != nil {
			logger.Errorf(ctx, "convert throughput %s to float64 failed: %v", arr[5], err)
			return record, err
		}
		record.Result.Throughput = throughput
	case TypeTCPRR, TypeTCPCRR, TypeUDPRR:
		tps, err := strconv.ParseFloat(arr[5], 64)
		if err != nil {
			logger.Errorf(ctx, "convert tps %s to float failed: %v", arr[5], err)
			return record, err
		}
		record.Result.TPS = tps
	}

	record.Result.Success = true

	return record, nil
}

// Save - save the result into database
func (c *netperf) Save(ctx context.Context, record probe.Record) error {
	rcd, ok := record.(*Record)
	if !ok {
		return fmt.Errorf("type(%v) != *netperf.Record", rcd)
	}

	if err := c.db.Create(rcd).Error; err != nil {
		logger.Warnf(ctx, "save netperf record to db failed: %v", err)
		return fmt.Errorf("save netperf record to db failed: %v", err)
	}

	return nil
}
