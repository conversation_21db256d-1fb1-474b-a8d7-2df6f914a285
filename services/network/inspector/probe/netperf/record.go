package netperf

import (
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// Record netperf 测试 DB 记录
type Record struct {
	gorm.Model

	probe.Link `json:",inline" gorm:"EMBEDDED"`
	Option     `json:",inline" gorm:"EMBEDDED"`
	Result     `json:",inline" gorm:"EMBEDDED"`
}

// Result netperf 测试结果
type Result struct {
	// TCP_STREAM, UDP_STREAM (unit: 10^6bits/sec)
	Throughput float64 `json:"throughput" gorm:"column:throughput"`

	// UDP_STREAM
	PkgOkNum  int64 `json:"pkg_ok_num" gorm:"column:pkg_ok_num"`
	PkgErrNum int64 `json:"pkg_err_num" gorm:"column:pkg_err_num"`

	// TCP_RR, TCP_CRR, UDP_RR
	TPS float64 `json:"tps" gorm:"column:tps"`

	Success bool `json:"success" gorm:"column:success"`
}

// TableName - return database tableName
func (c *Record) TableName() string {
	return "t_netperf_record"
}

// GetRoundID - return roundID of record link
func (c *Record) GetRoundID() string {
	return c.Link.RoundID
}
