package probe

import (
	"context"
)

// Interface defines functions to communicates with another pods
type Interface interface {
	Probe(ctx context.Context, link *Link, option Option) (Record, error)
	Save(ctx context.Context, record Record) error
}

type Option interface {
	Valid() error
}

type Options interface {
	Valid() error
	ToSlice() []Option
}

type Record interface {
	TableName() string
	GetRoundID() string
}

// Link defines link info between source and destination
type Link struct {
	// RoundID to identify the round of probe
	RoundID string `json:"roundID" gorm:"column:round_id"`

	SourceType   SourceType `json:"sourceType" gorm:"column:source_type"`
	SourcePodIP  string     `json:"sourcePodIP" gorm:"column:source_pod_ip"`
	SourceNodeIP string     `json:"sourceNodeIP" gorm:"column:source_node_ip"`

	DestinationNodeIP string `json:"destinationNodeIP" gorm:"column:destination_node_ip"`

	DestinationType DestinationType `json:"destinationType" gorm:"column:destination_type"`
	DestinationIP   string          `json:"destinationIP" gorm:"column:destination_ip"`
	DestinationPort int32           `json:"destinationPort" gorm:"column:destination_port"`
}

// SourceType pod or node
type SourceType string

const (
	// SourceTypeVPCPod = source is vpc pod
	SourceTypeVPCPod SourceType = "vpc_pod"

	// SourceTypeENIInVPCPod = source is eni_in_vpc pod
	SourceTypeENIInVPCPod SourceType = "eni_in_vpc_pod"

	// SourceTypeENIPod = source is eni pod
	SourceTypeENIPod SourceType = "eni_pod"

	// SourceTypeNode = source is host network pod
	SourceTypeNode SourceType = "node"
)

// DestinationType destination type
type DestinationType string

const (
	// DestinationTypeVPCPod = destination is vpc pod
	DestinationTypeVPCPod DestinationType = "vpc_pod"

	// DestinationTypeENIInVPCPod = destination is eni_in_vpc pod
	DestinationTypeENIInVPCPod DestinationType = "eni_in_vpc_pod"

	// DestinationTypeENIPod = destination is eni pod
	DestinationTypeENIPod DestinationType = "eni_pod"

	// DestinationTypeNode = destination is host-network pod
	DestinationTypeNode DestinationType = "node"

	// DestinationTypeClusterIPService = destination is ClusterIP service
	DestinationTypeClusterIPService DestinationType = "clusterip_service"

	// DestinationTypeNodePortService = destination is NodePort service
	DestinationTypeNodePortService DestinationType = "nodeport_service"

	// DestinationTypeLBService = destination is LoadBalancer service
	DestinationTypeLBService DestinationType = "lb_service"

	// DestinationTypeServiceName = destination is service name
	DestinationTypeServiceName DestinationType = "service_name"

	// DestinationTypeExternalURL = destination is external url
	DestinationTypeExternalURL DestinationType = "external_url"
)

type Type string

const (
	TypeCurl    Type = "curl"
	TypeNetperf Type = "netperf"
	TypePing    Type = "ping"
)
