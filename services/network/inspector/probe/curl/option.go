package curl

import (
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

type Option struct {
}

func (option *Option) Valid() error {
	return nil
}

type Options []*Option

func (options Options) ToSlice() []probe.Option {
	interfaceSlice := make([]probe.Option, len(options))

	for idx, value := range options {
		interfaceSlice[idx] = value
	}

	return interfaceSlice
}

func (options Options) Valid() error {
	if len(options) == 0 {
		return fmt.Errorf("curl options length is zero")
	}

	for _, option := range options {
		if err := option.Valid(); err != nil {
			return err
		}
	}

	return nil
}
