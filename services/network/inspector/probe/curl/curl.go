package curl

import (
	"context"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"time"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	_ "github.com/jinzhu/gorm/dialects/sqlite"
	"github.com/tcnksm/go-httpstat"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/mysql"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// curl implements a probe based on curl
type curl struct {
	curl http.Client
	db   *gorm.DB
}

// New - create a curl probe
func New(ctx context.Context) (probe.Interface, error) {
	client := http.Client{
		Transport: &http.Transport{
			DisableKeepAlives: true,
			DialContext: (&net.Dialer{
				Timeout:   10 * time.Second,
				KeepAlive: -1 * time.Second,
				DualStack: false,
			}).DialContext,
		},
		Timeout: 15 * time.Second,
	}

	db, err := mysql.New(ctx)
	if err != nil {
		logger.Errorf(ctx, "connect to mysql failed: %s", err)
		return nil, err
	}

	// create database
	db.AutoMigrate(Record{})

	return &curl{
		curl: client,
		db:   db,
	}, nil
}

// Probe - probe the link with specified option
func (c *curl) Probe(ctx context.Context, link *probe.Link, option probe.Option) (probe.Record, error) {
	if link == nil {
		return nil, fmt.Errorf("link is nil")
	}

	// generate endpoint
	dstURL := fmt.Sprintf("http://%s:%d/", link.DestinationIP, link.DestinationPort)
	logger.Infof(ctx, fmt.Sprintf("Probe endpoint: %s", dstURL))

	result := Result{
		IsSuccess: false,
	}

	// send request
	req, err := http.NewRequest("GET", dstURL, nil)
	if err != nil {
		return nil, err
	}

	var endTime time.Time
	var stats httpstat.Result
	statCtx := httpstat.WithHTTPStat(req.Context(), &stats)
	req = req.WithContext(statCtx)

	// tricky way to set stats info
	defer func() {
		stats.End(endTime)
		result.DNSLookup = float64(stats.DNSLookup) / float64(time.Millisecond)
		result.TCPConnection = float64(stats.TCPConnection) / float64(time.Millisecond)
		result.TLSHandshake = float64(stats.TLSHandshake) / float64(time.Millisecond)
		result.ServerProcessing = float64(stats.ServerProcessing) / float64(time.Millisecond)
		result.ContentTransfer = float64(stats.ContentTransfer(endTime)) / float64(time.Millisecond)
	}()

	resp, err := c.curl.Do(req)
	if err != nil {
		msg := fmt.Sprintf("curl %s failed: %s", dstURL, err)
		logger.Errorf(ctx, msg)
		result.Error = msg
		endTime = time.Now()
		return &Record{
			Link:   *link,
			Result: result,
		}, err
	}

	if resp == nil {
		msg := fmt.Sprintf("curl %s failed: response is nil", dstURL)
		logger.Errorf(ctx, msg)
		result.Error = msg
		endTime = time.Now()
		return &Record{
			Link:   *link,
			Result: result,
		}, fmt.Errorf(msg)
	}

	code := resp.StatusCode
	result.Code = code
	if code == 200 {
		result.IsSuccess = true
	}

	if _, err := io.Copy(ioutil.Discard, resp.Body); err != nil {
		msg := fmt.Sprintf("failed to discard response body: %v", err)
		logger.Errorf(ctx, msg)
		result.Error = msg
		endTime = time.Now()
		return &Record{
			Link:   *link,
			Result: result,
		}, fmt.Errorf(msg)
	}

	endTime = time.Now()
	resp.Body.Close()

	return &Record{
		Link:   *link,
		Result: result,
	}, nil
}

// Save - save the result into database
func (c *curl) Save(ctx context.Context, record probe.Record) error {
	rcd, ok := record.(*Record)
	if !ok {
		return fmt.Errorf("type(%v) != *curl.Record", rcd)
	}

	if err := c.db.Create(rcd).Error; err != nil {
		logger.Warnf(ctx, "save curl record to db failed: %v", err)
		return fmt.Errorf("save curl record to db failed: %v", err)
	}

	logger.Infof(ctx, "save curl record succeeded: %v", utils.ToJSON(record))

	return nil
}
