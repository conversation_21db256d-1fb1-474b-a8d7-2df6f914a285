package curl

import (
	"github.com/jinzhu/gorm"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// Record curl 测试 DB 记录
type Record struct {
	gorm.Model

	probe.Link `json:",inline" gorm:"EMBEDDED"`
	Result     `json:",inline" gorm:"EMBEDDED"`
}

// Result curl 测试结果
type Result struct {
	IsSuccess bool   `json:"isSuccess"`
	Error     string `json:"error,omitempty"`
	Code      int    `json:"code,omitempty"`

	DNSLookup        float64 `json:"dnsLookup,omitempty"`
	TCPConnection    float64 `json:"tcpConnection,omitempty"`
	TLSHandshake     float64 `json:"tlsHandshake,omitempty"`
	ServerProcessing float64 `json:"serverProcessing,omitempty"`
	ContentTransfer  float64 `json:"ContentTransfer,omitempty"`
}

// TableName - return database tableName
func (c *Record) TableName() string {
	return "t_curl_record"
}

// GetRoundID - return roundID of record link
func (c *Record) GetRoundID() string {
	return c.Link.RoundID
}
