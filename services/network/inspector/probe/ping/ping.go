package ping

import (
	"context"
	"fmt"
	"os/exec"
	"strconv"
	"strings"

	"github.com/jinzhu/gorm"
	_ "github.com/jinzhu/gorm/dialects/mysql"
	_ "github.com/jinzhu/gorm/dialects/sqlite"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/mysql"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// netperf implements a probe based on netperf
type netperf struct {
	db *gorm.DB
}

// New - create a netperf probe
func New(ctx context.Context) (probe.Interface, error) {
	db, err := mysql.New(ctx)
	if err != nil {
		logger.Errorf(ctx, "connect to mysql failed: %s", err)
		return nil, err
	}

	// create database
	db.AutoMigrate(Record{})

	return &netperf{db: db}, nil
}

// Probe - probe the link with specified option
func (c *netperf) Probe(ctx context.Context, link *probe.Link, option probe.Option) (probe.Record, error) {
	if link == nil {
		return nil, fmt.Errorf("link is nil")
	}

	// ping option
	opt, ok := option.(*Option)
	if !ok {
		return nil, fmt.Errorf("type(%v) != ping.Option", option)
	}

	// ping record
	record := &Record{
		Link:   *link,
		Option: *opt,
		Result: Result{},
	}

	// generate ping command
	ping := fmt.Sprintf("ping -c %d -i %f -s %d %s", opt.Count, opt.Interval, opt.PacketSize, link.DestinationIP)
	ping = fmt.Sprintf("%s > tmp_file && awk 'END {print}' tmp_file", ping)

	// execute ping command
	logger.Infof(ctx, "exec start: command = [ %s ]", ping)
	cmd := exec.Command("/bin/bash", "-c", ping)
	res, err := cmd.CombinedOutput()
	if err != nil {
		logger.Errorf(ctx, "exec failed: %v", err)
		return record, err
	}
	line := strings.TrimRight(string(res), "\n")

	logger.Infof(ctx, "exec succeed: result = [ %s ]", line)

	// parse ping result
	arr := strings.Fields(strings.TrimSpace(line))
	times := strings.Split(arr[3], "/")
	if len(times) != 4 {
		msg := fmt.Sprintf("got illegal ping result: %v", line)
		logger.Errorf(ctx, msg)
		return record, fmt.Errorf(msg)
	}

	min, err := strconv.ParseFloat(times[0], 64)
	if err != nil {
		logger.Errorf(ctx, "got illegal ping result: %v", line)
		return record, err
	}

	avg, err := strconv.ParseFloat(times[1], 64)
	if err != nil {
		logger.Errorf(ctx, "got illegal ping result: %v", line)
		return record, err
	}

	max, err := strconv.ParseFloat(times[2], 64)
	if err != nil {
		logger.Errorf(ctx, "got illegal ping result: %v", line)
		return record, err
	}

	mdev, err := strconv.ParseFloat(times[3], 64)
	if err != nil {
		logger.Errorf(ctx, "got illegal ping result: %v", line)
		return record, err
	}

	record.Result = Result{
		RTTMin:  min,
		RTTAvg:  avg,
		RTTMax:  max,
		RTTMdev: mdev,
		RTTUnit: arr[4],
		Success: true,
	}

	return record, nil
}

// Save - save the result into database
func (c *netperf) Save(ctx context.Context, record probe.Record) error {
	rcd, ok := record.(*Record)
	if !ok {
		return fmt.Errorf("type(%v) != *netperf.Record", rcd)
	}

	if err := c.db.Create(rcd).Error; err != nil {
		logger.Warnf(ctx, "save netperf record to db failed: %v", err)
		return fmt.Errorf("save netperf record to db failed: %v", err)
	}

	return nil
}
