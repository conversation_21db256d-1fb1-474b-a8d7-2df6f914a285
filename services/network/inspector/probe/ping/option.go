package ping

import (
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/inspector/probe"
)

// ping 测试选项
type Option struct {
	// ping -c option
	Count int `json:"count" gorm:"column:count"`
	// ping -i option
	Interval float64 `json:"interval" gorm:"column:interval"`
	// ping -s option
	PacketSize int `json:"packetSize" gorm:"column:packet_size"`
}

func (option *Option) Valid() error {
	if option == nil {
		return fmt.Errorf("ping option is nil")
	}

	if option.Count <= 0 {
		option.Count = 1
	}

	if option.Interval <= 0 {
		option.Interval = 1
	}

	if option.PacketSize <= 0 {
		option.PacketSize = 56
	}

	return nil
}

type Options []*Option

func (options Options) ToSlice() []probe.Option {
	interfaceSlice := make([]probe.Option, len(options))

	for idx, value := range options {
		interfaceSlice[idx] = value
	}

	return interfaceSlice
}

func (options Options) Valid() error {
	if len(options) == 0 {
		return fmt.Errorf("ping options length is zero")
	}

	for _, option := range options {
		if err := option.Valid(); err != nil {
			return err
		}
	}

	return nil
}
