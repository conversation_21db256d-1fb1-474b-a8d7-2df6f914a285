# vpc-ip
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-0
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-0
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-0
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-1
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-1
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-1
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-1
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-2
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-2
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-2
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-2
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-3
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-3
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-3
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-3
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-4
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-4
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-4
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-4
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-5
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-5
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-5
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-5
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-6
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-6
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-6
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-6
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-7
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-7
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-7
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-7
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-8
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-8
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-8
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-8
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-9
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-9
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-9
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-9
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-10
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-10
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-10
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-10
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-11
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-11
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-11
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-11
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-12
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-12
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-12
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-12
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-13
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-13
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-13
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-13
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-14
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-14
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-14
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-14
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-15
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-15
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-15
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-15
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-16
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-16
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-16
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-16
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-17
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-17
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-17
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-17
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-18
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-18
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-18
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-18
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip-19
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-19
  namespace: cce-ping
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-19
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-19
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
## host-network
#apiVersion: apps/v1
#kind: DaemonSet
#metadata:
#  name: host-network-0
#  namespace: cce-ping
#spec:
#  selector:
#    matchLabels:
#      cce-network-inspector: host-network-0
#  template:
#    metadata:
#      labels:
#        cce-network-inspector: host-network-0
#    spec:
#      hostNetwork: true
#      dnsPolicy: ClusterFirstWithHostNet
#      containers:
#        - name: cce-network-inspector
#          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
#          imagePullPolicy: Always
#  updateStrategy:
#    type: RollingUpdate

---

#---
## eni-in-vpc
#apiVersion: apps/v1
#kind: DaemonSet
#metadata:
#  name: eni-in-vpc-0
#  namespace: cce-ping
#spec:
#  selector:
#    matchLabels:
#      cce-network-inspector: eni-in-vpc-0
#  template:
#    metadata:
#      labels:
#        cce-network-inspector: eni-in-vpc-0
#    spec:
#      serviceAccountName: cce-network-inspector-serviceaccount
#      containers:
#        - name: cce-network-inspector
#          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-ping:yuhongwei
#          imagePullPolicy: Always
#          resources:
#            limits:
#              baiduyun/eni: "1"
#  updateStrategy:
#    type: RollingUpdate