# host-network
apiVersion: v1
kind: Service
metadata:
  name: host-network-0
  namespace: cce-netperf
  labels:
    cce-network-inspector: host-network-0
spec:
  selector:
    cce-network-inspector: host-network-0
  type: NodePort
  sessionAffinity: None
  ports:
    - port: 12865
      targetPort: 12865

---
# vpc-ip
apiVersion: v1
kind: Service
metadata:
  name: vpc-ip-0
  namespace: cce-netperf
  labels:
    cce-network-inspector: vpc-ip-0
spec:
  selector:
    cce-network-inspector: vpc-ip-0
  type: NodePort
  sessionAffinity: None
  ports:
    - port: 12865
      targetPort: 12865

---
# eni-in-vpc
apiVersion: v1
kind: Service
metadata:
  name: eni-in-vpc-0
  namespace: cce-netperf
  labels:
    cce-network-inspector: eni-in-vpc-0
spec:
  selector:
    cce-network-inspector: eni-in-vpc-0
  type: NodePort
  sessionAffinity: None
  ports:
    - port: 12865
      targetPort: 12865