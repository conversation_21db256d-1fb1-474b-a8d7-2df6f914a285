# host-network
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: host-network-0
  namespace: cce-netperf
spec:
  selector:
    matchLabels:
      cce-network-inspector: host-network-0
  template:
    metadata:
      labels:
        cce-network-inspector: host-network-0
    spec:
      hostNetwork: true
      dnsPolicy: ClusterFirstWithHostNet
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-netserver:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

---
# vpc-ip
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: vpc-ip-0
  namespace: cce-netperf
spec:
  selector:
    matchLabels:
      cce-network-inspector: vpc-ip-0
  template:
    metadata:
      labels:
        cce-network-inspector: vpc-ip-0
    spec:
      containers:
        - name: cce-network-inspector
          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-netserver:yuhongwei
          imagePullPolicy: Always
  updateStrategy:
    type: RollingUpdate

#---
## eni-in-vpc
#apiVersion: apps/v1
#kind: DaemonSet
#metadata:
#  name: eni-in-vpc-0
#  namespace: cce-netperf
#spec:
#  selector:
#    matchLabels:
#      cce-network-inspector: eni-in-vpc-0
#  template:
#    metadata:
#      labels:
#        cce-network-inspector: eni-in-vpc-0
#    spec:
#      serviceAccountName: cce-network-inspector-serviceaccount
#      containers:
#        - name: cce-network-inspector
#          image: registry.baidubce.com/cce-plugin-dev/cce-network-inspector-netserver:yuhongwei
#          imagePullPolicy: Always
#          resources:
#            limits:
#              baiduyun/eni: "1"
#  updateStrategy:
#    type: RollingUpdate