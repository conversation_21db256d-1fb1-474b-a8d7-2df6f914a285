package utils

import (
	"time"
)

func Sleep(interval int, timeUnit TimeUnit) {
	switch timeUnit {
	case TimeUnitNanosecond:
		time.Sleep(time.Duration(interval) * time.Nanosecond)
	case TimeUnitMicrosecond:
		time.Sleep(time.Duration(interval) * time.Microsecond)
	case TimeUnitMillisecond:
		time.Sleep(time.Duration(interval) * time.Millisecond)
	case TimeUnitSecond:
		time.Sleep(time.Duration(interval) * time.Second)
	case TimeUnitMinute:
		time.Sleep(time.Duration(interval) * time.Minute)
	case TimeUnitHour:
		time.Sleep(time.Duration(interval) * time.Hour)
	}
}

type TimeUnit string

const (
	TimeUnitNanosecond  TimeUnit = "nanosecond"
	TimeUnitMicrosecond TimeUnit = "microsecond"
	TimeUnitMillisecond TimeUnit = "millisecond"
	TimeUnitSecond      TimeUnit = "second"
	TimeUnitMinute      TimeUnit = "minute"
	TimeUnitHour        TimeUnit = "hour"
)
