apiVersion: apps/v1
kind: Deployment
metadata:
  creationTimestamp: null
  labels:
    run: eca97e148cb74e9683d7b7240829d1ff-serverless-master-0616
  name: eca97e148cb74e9683d7b7240829d1ff-serverless-master-0616
  namespace: bci
spec:
  replicas: 2
  selector:
    matchLabels:
      run: eca97e148cb74e9683d7b7240829d1ff-serverless-master-0616
  strategy: {}
  template:
    metadata:
      creationTimestamp: null
      annotations:
        "bci.virtual-kubelet.io/bci-logical-zone": "zoneC"
        "bci.virtual-kubelet.io/bci-vpc-id": "vpc-unxsedibjsuv"
        "bci.virtual-kubelet.io/bci-vpc-uuid": "a0c23ef6-640d-4827-9ca8-9d7775c8d254"
        "bci.virtual-kubelet.io/bci-subnet-id": "sbn-jyvqgs5775kj"
        "bci.virtual-kubelet.io/bci-subnet-uuid": "dd626e8a-f7df-4382-b56a-c8414a39ee74"
        "bci.virtual-kubelet.io/bci-security-group-id": "g-0c2vzsii79hv"
      labels:
        run: eca97e148cb74e9683d7b7240829d1ff-serverless-master-0616
    spec:
      nodeSelector:
        type: "virtual-kubelet"
      tolerations:
      - key: "virtual-kubelet.io/provider"
        operator: "Equal"
        value: "baidu"
        effect: "NoSchedule"
      containers:
      - image: hub.baidubce.com/serverless-k8s/kube-apiserver:1.16.8
        name: apiserver
        env:
        - name: "NODE_IP"
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: "MASTER_COUNT"
          value: "2"
        - name: "ETCD_SERVERS"
          value: "https://*************:2379"
        - name: "BLB_VPC_IP"
          value: "***********"
        - name: "BLB_FLOATING_IP"
          value: "**************"
        - name: "BLB_EIP"
          value: "**************"
        - name: "SERVICE_CLUSTER_IP_RANGE"
          value: "**********/16"
        - name: "SERVER_NODE_PORT_RANGE"
          value: "30000-32767"
        - name: "MAX_MUTATING_REQUESTS_INFLIGHT"
          value: "2000"
        - name: "MAX_REQUESTS_INFLIGHT"
          value: "4000"
        - name: "ETCD_PREFIX"
          value: "/data/test/user2"
        - name: "ETCD_CAFILE"
          value: "/etc/etcd/certs/ca.pem"
        - name: "ETCD_CERTFILE"
          value: "/etc/etcd/certs/client.pem"
        - name: "ETCD_KEYFILE"
          value: "/etc/etcd/certs/client-key.pem"
        - name: "AUDIT_POLICY_FILE"
          value: "/etc/kubernetes/audit-policy.yaml"
        - name: "KUBE_APISERVER"
          value: "/etc/kubernetes/pki/kube-apiserver.pem"
        - name: "KUBE_APISERVER_KEY"
          value: "/etc/kubernetes/pki/kube-apiserver-key.pem"
        - name: "CA_FILE"
          value: "/etc/kubernetes/ca/ca.pem"
        - name: "CA_KEY"
          value: "/etc/kubernetes/ca/ca-key.pem"
        - name: "CA_CONFIG"
          value: "/etc/kubernetes/ca/ca-config.json"
        - name: "LOG_DIR"
          value: "/log-dir"
        resources:
          requests:
            cpu: 1
            memory: 2Gi
        volumeMounts:
        - name: "cluster-ca"
          mountPath: "/etc/kubernetes/ca"
        - name: "etcd-certs"
          mountPath: "/etc/etcd/certs"
        - name: "kubeconfig"
          mountPath: "/root/.kube"
        - name: "cluster-certs"
          mountPath: "/etc/kubernetes/pki"
        - name: "log-volume"
          mountPath: "/log-dir"

      - image: hub.baidubce.com/serverless-k8s/kube-controller-manager:1.16.8
        name: controller-manager
        env:
        - name: "NODE_IP"
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: "NODE_CIDR_MASK_SIZE"
          value: "24"
        - name: "CLUSTER_CIDR"
          value: "**********/18"
        - name: "KUBE_CONTROLLER_MANAGER"
          value: "/etc/kubernetes/pki/kube-controller-manager.pem"
        - name: "KUBE_CONTROLLER_MANAGER_KEY"
          value: "/etc/kubernetes/pki/kube-controller-manager-key.pem"
        - name: "KUBE_CONTROLLER_MANAGER_KUBECONFIG"
          value: "/etc/kubernetes/kube-controller-manager.kubeconfig"
        - name: "CA_FILE"
          value: "/etc/kubernetes/ca/ca.pem"
        - name: "CA_KEY"
          value: "/etc/kubernetes/ca/ca-key.pem"
        - name: "CA_CONFIG"
          value: "/etc/kubernetes/ca/ca-config.json"
        - name: "SERVICE_CLUSTER_IP_RANGE"
          value: "**********/16"
        - name: "LOG_DIR"
          value: "/log-dir"
        resources:
          requests:
            cpu: 1
            memory: 2Gi
        volumeMounts:
        - name: "cluster-ca"
          mountPath: "/etc/kubernetes/ca"
        - name: "kubeconfig"
          mountPath: "/root/.kube"
        - name: "cluster-certs"
          mountPath: "/etc/kubernetes/pki"
        - name: "log-volume"
          mountPath: "/log-dir"

      - image: hub.baidubce.com/serverless-k8s/kube-scheduler:1.16.8
        name: scheduler
        env:
        - name: "NODE_IP"
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: "KUBE_SCHEDULER"
          value: "/etc/kubernetes/pki/kube-scheduler.pem"
        - name: "KUBE_SCHEDULER_KEY"
          value: "/etc/kubernetes/pki/kube-scheduler-key.pem"
        - name: "KUBE_SCHEDULER_KUBECONFIG"
          value: "/etc/kubernetes/kube-scheduler.kubeconfig"
        - name: "CA_FILE"
          value: "/etc/kubernetes/ca/ca.pem"
        - name: "CA_KEY"
          value: "/etc/kubernetes/ca/ca-key.pem"
        - name: "CA_CONFIG"
          value: "/etc/kubernetes/ca/ca-config.json"
        - name: "LOG_DIR"
          value: "/log-dir"
        resources:
          requests:
            cpu: 1
            memory: 2Gi
        volumeMounts:
        - name: "cluster-ca"
          mountPath: "/etc/kubernetes/ca"
        - name: "kubeconfig"
          mountPath: "/root/.kube"
        - name: "cluster-certs"
          mountPath: "/etc/kubernetes/pki"
        - name: "log-volume"
          mountPath: "/log-dir"

      - image: hub.baidubce.com/serverless-k8s/virtual-kubelet:1.16.8
        name: virtual-kubelet
        env:
        - name: "BLB_VPC_IP"
          value: "***********"
        - name: "BLB_FLOATING_IP"
          value: "**************"
        - name: "BLB_EIP"
          value: "**************"
        - name: "CA_FILE"
          value: "/etc/kubernetes/ca/ca.pem"
        - name: KUBELET_PORT
          value: "10250"
        - name: VKUBELET_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          value: "virtual-kubelet"
        - name: BCI_REGION
          value: "gz"
        - name: BCI_LOGICAL_ZONE
          value: "zoneC"
        - name: CCE_CLUSTER_ID
          value: "c-yezichao"
        - name: BCI_VPC_ID
          value: "vpc-unxsedibjsuv"
        - name: BCI_VPC_UUID
          value: "a0c23ef6-640d-4827-9ca8-9d7775c8d254"
        - name: BCI_SUBNET_ID
          value: "sbn-jyvqgs5775kj"
        - name: BCI_SUBNET_UUID
          value: "dd626e8a-f7df-4382-b56a-c8414a39ee74"
        - name: BCI_SECURITY_GROUP_ID
          value: "g-0c2vzsii79hv"
        - name: APISERVER_CERT_LOCATION
          value: /etc/kubernetes/pki/admin.pem
        - name: APISERVER_KEY_LOCATION
          value: /etc/kubernetes/pki/admin-key.pem
        - name: APISERVER_CA_CERT_LOCATION
          value: /etc/kubernetes/ca/ca.pem
        - name: "LOG_DIR"
          value: "/log-dir"
        - name: "BCI_ACCESS_KEY"
          value: "xxx"
        - name: "BCI_SECRET_KEY"
          value: "xxx"
        resources:
          requests:
            cpu: 1
            memory: 2Gi
        volumeMounts:
        - name: "cluster-ca"
          mountPath: "/etc/kubernetes/ca"
        - name: "kubeconfig"
          mountPath: "/root/.kube"
        - name: "cluster-certs"
          mountPath: "/etc/kubernetes/pki"
        - name: "log-volume"
          mountPath: "/log-dir"

      - image: hub.baidubce.com/serverless-k8s/debug:1.16.8
        name: debug
        env:
        - name: "LOG_DIR"
          value: "/log-dir"
        - name: "LOG_ROTATE_COUNT"
          value: "4"
        volumeMounts:
        - name: "cluster-ca"
          mountPath: "/etc/kubernetes/ca"
        - name: "kubeconfig"
          mountPath: "/root/.kube"
        - name: "log-volume"
          mountPath: "/log-dir"

      restartPolicy: Always
      volumes:
      - name: "cluster-ca"
        configMap:
          defaultMode: 0755
          name: "cluster-ca"
      - name: "etcd-certs"
        configMap:
          defaultMode: 0644
          name: "etcd-certs"
      - name: "kubeconfig"
        emptyDir: {}
      - name: "cluster-certs"
        emptyDir: {}
      - name: "log-volume"
        emptyDir: {}
