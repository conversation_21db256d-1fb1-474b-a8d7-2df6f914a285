diff --git a/pkg/master/controller.go b/pkg/master/controller.go
index 36307de..70980ff 100644
--- a/pkg/master/controller.go
+++ b/pkg/master/controller.go
@@ -38,7 +38,7 @@ import (
 	"k8s.io/kubernetes/pkg/master/reconcilers"
 	"k8s.io/kubernetes/pkg/registry/core/rangeallocation"
 	corerest "k8s.io/kubernetes/pkg/registry/core/rest"
-	servicecontroller "k8s.io/kubernetes/pkg/registry/core/service/ipallocator/controller"
+	//servicecontroller "k8s.io/kubernetes/pkg/registry/core/service/ipallocator/controller"
 	portallocatorcontroller "k8s.io/kubernetes/pkg/registry/core/service/portallocator/controller"
 	"k8s.io/kubernetes/pkg/util/async"
 )
@@ -122,7 +122,7 @@ func (c *completedConfig) NewBootstrapController(legacyRESTStorage corerest.Lega
 
 		PublicIP: c.GenericConfig.PublicAddress,
 
-		ServiceIP:                 c.ExtraConfig.APIServerServiceIP,
+		// ServiceIP:                 c.ExtraConfig.APIServerServiceIP,
 		ServicePort:               c.ExtraConfig.APIServerServicePort,
 		ExtraServicePorts:         c.ExtraConfig.ExtraServicePorts,
 		ExtraEndpointPorts:        c.ExtraConfig.ExtraEndpointPorts,
@@ -154,20 +154,20 @@ func (c *Controller) Start() {
 		klog.Errorf("Unable to remove old endpoints from kubernetes service: %v", err)
 	}
 
-	repairClusterIPs := servicecontroller.NewRepair(c.ServiceClusterIPInterval, c.ServiceClient, c.EventClient, &c.ServiceClusterIPRange, c.ServiceClusterIPRegistry, &c.SecondaryServiceClusterIPRange, c.SecondaryServiceClusterIPRegistry)
+	// repairClusterIPs := servicecontroller.NewRepair(c.ServiceClusterIPInterval, c.ServiceClient, c.EventClient, &c.ServiceClusterIPRange, c.ServiceClusterIPRegistry, &c.SecondaryServiceClusterIPRange, c.SecondaryServiceClusterIPRegistry)
 	repairNodePorts := portallocatorcontroller.NewRepair(c.ServiceNodePortInterval, c.ServiceClient, c.EventClient, c.ServiceNodePortRange, c.ServiceNodePortRegistry)
 
 	// run all of the controllers once prior to returning from Start.
-	if err := repairClusterIPs.RunOnce(); err != nil {
-		// If we fail to repair cluster IPs apiserver is useless. We should restart and retry.
-		klog.Fatalf("Unable to perform initial IP allocation check: %v", err)
-	}
+	//if err := repairClusterIPs.RunOnce(); err != nil {
+	//	// If we fail to repair cluster IPs apiserver is useless. We should restart and retry.
+	//	klog.Fatalf("Unable to perform initial IP allocation check: %v", err)
+	//}
 	if err := repairNodePorts.RunOnce(); err != nil {
 		// If we fail to repair node ports apiserver is useless. We should restart and retry.
 		klog.Fatalf("Unable to perform initial service nodePort check: %v", err)
 	}
 
-	c.runner = async.NewRunner(c.RunKubernetesNamespaces, c.RunKubernetesService, repairClusterIPs.RunUntil, repairNodePorts.RunUntil)
+	c.runner = async.NewRunner(c.RunKubernetesNamespaces, c.RunKubernetesService,/* repairClusterIPs.RunUntil,*/ repairNodePorts.RunUntil)
 	c.runner.Start()
 }
 
diff --git a/pkg/registry/core/service/storage/rest.go b/pkg/registry/core/service/storage/rest.go
index c45af06..66a75fe 100644
--- a/pkg/registry/core/service/storage/rest.go
+++ b/pkg/registry/core/service/storage/rest.go
@@ -666,7 +666,7 @@ func allocateHealthCheckNodePort(service *api.Service, nodePortOp *portallocator
 
 // The return bool value indicates if a cluster IP is allocated successfully.
 func initClusterIP(service *api.Service, allocator ipallocator.Interface) (bool, error) {
-	switch {
+	/*switch {
 	case service.Spec.ClusterIP == "":
 		// Allocate next available.
 		ip, err := allocator.AllocateNext()
@@ -686,7 +686,7 @@ func initClusterIP(service *api.Service, allocator ipallocator.Interface) (bool,
 			return false, errors.NewInvalid(api.Kind("Service"), service.Name, el)
 		}
 		return true, nil
-	}
+	}*/
 
 	return false, nil
 }
diff --git a/vendor/github.com/coreos/go-oidc/jwks.go b/vendor/github.com/coreos/go-oidc/jwks.go
index e6a82c8..895441b 100644
--- a/vendor/github.com/coreos/go-oidc/jwks.go
+++ b/vendor/github.com/coreos/go-oidc/jwks.go
@@ -5,10 +5,13 @@ import (
 	"errors"
 	"fmt"
 	"io/ioutil"
+	"encoding/json"
 	"net/http"
 	"sync"
 	"time"
 
+        "k8s.io/klog"
+
 	"github.com/pquerna/cachecontrol"
 	jose "gopkg.in/square/go-jose.v2"
 )
@@ -108,35 +111,54 @@ func (r *remoteKeySet) verify(ctx context.Context, jws *jose.JSONWebSignature) (
 		keyID = sig.Header.KeyID
 		break
 	}
+        klog.Infof("keyID: %s", keyID)
 
 	keys, expiry := r.keysFromCache()
 
+        keysStr, _ := json.Marshal(keys)
+        klog.Infof("keysFromCache: %s", string(keysStr))
+
 	// Don't check expiry yet. This optimizes for when the provider is unavailable.
 	for _, key := range keys {
 		if keyID == "" || key.KeyID == keyID {
-			if payload, err := jws.Verify(&key); err == nil {
+			payload, err := jws.Verify(&key)
+                        if err == nil {
 				return payload, nil
 			}
+
+                        if err != nil {
+                            klog.Infof("jws.Verify failed in cache: %s", err)
+			}
 		}
 	}
 
 	if !r.now().Add(keysExpiryDelta).After(expiry) {
 		// Keys haven't expired, don't refresh.
-		return nil, errors.New("failed to verify id token signature")
+		return nil, errors.New("failed to verify id token signature, in expiry")
 	}
 
 	keys, err := r.keysFromRemote(ctx)
+
+        keysStr, _ = json.Marshal(keys)
+        klog.Infof("keysFromRemote: %s", string(keysStr))
+
 	if err != nil {
 		return nil, fmt.Errorf("fetching keys %v", err)
 	}
 
 	for _, key := range keys {
 		if keyID == "" || key.KeyID == keyID {
-			if payload, err := jws.Verify(&key); err == nil {
+			payload, err := jws.Verify(&key)
+                        if err == nil {
 				return payload, nil
 			}
+
+                        if err != nil {
+                            klog.Infof("jws.Verify failed in remote: %s", err)
+			}
 		}
 	}
+
 	return nil, errors.New("failed to verify id token signature")
 }
 
@@ -201,11 +223,18 @@ func (r *remoteKeySet) updateKeys() ([]jose.JSONWebKey, time.Time, error) {
 	}
 	defer resp.Body.Close()
 
+
+        str, _ := json.Marshal(resp)
+        klog.Infof("resp: %v", string(str))
+
 	body, err := ioutil.ReadAll(resp.Body)
 	if err != nil {
 		return nil, time.Time{}, fmt.Errorf("unable to read response body: %v", err)
 	}
 
+        str, _ = json.Marshal(body)
+        klog.Infof("body: %v", string(str))
+
 	if resp.StatusCode != http.StatusOK {
 		return nil, time.Time{}, fmt.Errorf("oidc: get keys failed: %s %s", resp.Status, body)
 	}
@@ -216,6 +245,9 @@ func (r *remoteKeySet) updateKeys() ([]jose.JSONWebKey, time.Time, error) {
 		return nil, time.Time{}, fmt.Errorf("oidc: failed to decode keys: %v %s", err, body)
 	}
 
+        str, _ = json.Marshal(keySet)
+        klog.Infof("keySet: %v", string(str))
+
 	// If the server doesn't provide cache control headers, assume the
 	// keys expire immediately.
 	expiry := r.now()
