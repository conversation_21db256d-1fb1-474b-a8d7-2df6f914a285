#!/bin/sh

set -eu

mkdir -p ${CERT_DIR} && cd ${CERT_DIR}

EXTERNAL_HOSTS=${NODE_IP}'","'${BLB_VPC_IP}'","'${BLB_FLOATING_IP}

if ! [ -z ${BLB_EIP+x} ]; then
  EXTERNAL_HOSTS=${EXTERNAL_HOSTS}'","'${BLB_EIP}
fi

FIRST_SVC_IP=$(echo -n ${SERVICE_CLUSTER_IP_RANGE} | awk -F "/" '{print $1}' | sed 's#0$#1#')
if ! [ -z ${FIRST_SVC_IP+x} ]; then
  EXTERNAL_HOSTS=${EXTERNAL_HOSTS}'","'${FIRST_SVC_IP}
fi

# service-controller
cat >service-controller-csr.json <<EOF
{
  "CN": "admin",
  "hosts": [
    "127.0.0.1",
    "kubernetes",
    "kubernetes.default",
    "kubernetes.default.svc",
    "kubernetes.default.svc.cluster",
    "kubernetes.default.svc.cluster.local",
    "${EXTERNAL_HOSTS}"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "kubernetes-admin",
      "ST": "BeiJing",
      "L": "BeiJing",
      "O": "system:masters",
      "OU": "cloudnative"
    }
  ]
}
EOF
# 生成admin证书和私钥
cfssl gencert -ca=${CA_FILE} -ca-key=${CA_KEY} -config=${CA_CONFIG} -profile=jpaas service-controller-csr.json | cfssljson -bare service-controller

# kubeconfig
kubectl config set-cluster kubernetes \
  --certificate-authority=${CA_FILE} \
  --embed-certs=true \
  --server=https://${NODE_IP}:6443 \
  --kubeconfig=service-controller.kubeconfig
#设置客户端认证参数
kubectl config set-credentials admin \
  --client-certificate=${CERT_DIR}/service-controller.pem \
  --client-key=${CERT_DIR}/service-controller-key.pem \
  --embed-certs=true \
  --kubeconfig=service-controller.kubeconfig
# 设置上下文参数
kubectl config set-context kubernetes \
  --cluster=kubernetes \
  --user=admin \
  --kubeconfig=service-controller.kubeconfig
# 设置默认上下文
kubectl config use-context kubernetes --kubeconfig=service-controller.kubeconfig

export KUBECONFIG=${CERT_DIR}/service-controller.kubeconfig

echo \{\"Region\":\"${REGION}\",\"CCEV2Endpoint\":\"${CCEV2_ENDPOINT}\",\"ClusterId\":\"${CLUSTER_ID}\"\} > ${CLOUD_CONFIG_PATH}

sed -i 's/__CLUSTER_IP__/'${KUBERNETES_CLUSTER_IP}'/g' /kubernetes-svc.yaml
kubectl apply -f /kubernetes-svc.yaml

/cce-service-controller --kubeconfig=${KUBECONFIG} --cloudconfig=${CLOUD_CONFIG_PATH} >>${LOG_DIR}/service-controller.log 2>&1