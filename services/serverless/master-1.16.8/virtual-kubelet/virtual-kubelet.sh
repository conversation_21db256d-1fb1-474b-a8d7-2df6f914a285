#!/bin/sh

set -eu

mkdir -p ${CERT_DIR} && cd ${CERT_DIR}

EXTERNAL_HOSTS=${NODE_IP}'","'${BLB_VPC_IP}'","'${BLB_FLOATING_IP}

if ! [ -z ${BLB_EIP+x} ]; then
  EXTERNAL_HOSTS=${EXTERNAL_HOSTS}'","'${BLB_EIP}
fi

FIRST_SVC_IP=$(echo -n ${SERVICE_CLUSTER_IP_RANGE} | awk -F "/" '{print $1}' | sed 's#0$#1#')
if ! [ -z ${FIRST_SVC_IP+x} ]; then
  EXTERNAL_HOSTS=${EXTERNAL_HOSTS}'","'${FIRST_SVC_IP}
fi

# kube-proxy
cat >kube-proxy-csr.json <<EOF
{
  "CN": "system:kube-proxy",
  "hosts": [
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "BeiJ<PERSON>",
      "L": "BeiJing",
      "O": "system:kube-proxy",
      "OU": "cloudnative"
    }
  ]
}
EOF

# 生成证书和私钥
cfssl gencert -ca=${CA_FILE} -ca-key=${CA_KEY} -config=${CA_CONFIG} -profile=jpaas kube-proxy-csr.json | cfssljson -bare kube-proxy

# set-cluster
kubectl config set-cluster kubernetes \
  --certificate-authority=${CA_FILE} \
  --embed-certs=true \
  --server=https://${NODE_IP}:6443 \
  --kubeconfig=kube-proxy.conf
# set-credentials
kubectl config set-credentials system:kube-proxy \
  --client-certificate=${CERT_DIR}/kube-proxy.pem \
  --embed-certs=true \
  --client-key=${CERT_DIR}/kube-proxy-key.pem \
  --kubeconfig=kube-proxy.conf
# set-context
kubectl config set-context system:kube-proxy@kubernetes \
  --cluster=kubernetes \
  --user=system:kube-proxy \
  --kubeconfig=kube-proxy.conf
# set default context
kubectl config use-context system:kube-proxy@kubernetes --kubeconfig=kube-proxy.conf

# vk.kubeconfig
cat >vk-csr.json <<EOF
{
  "CN": "admin",
  "hosts": [
    "127.0.0.1",
    "kubernetes",
    "kubernetes.default",
    "kubernetes.default.svc",
    "kubernetes.default.svc.cluster",
    "kubernetes.default.svc.cluster.local",
    "${EXTERNAL_HOSTS}"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "kubernetes-admin",
      "ST": "BeiJing",
      "L": "BeiJing",
      "O": "system:masters",
      "OU": "cloudnative"
    }
  ]
}
EOF
# 生成admin证书和私钥
cfssl gencert -ca=${CA_FILE} -ca-key=${CA_KEY} -config=${CA_CONFIG} -profile=jpaas vk-csr.json | cfssljson -bare vk

# kubeconfig
kubectl config set-cluster kubernetes \
  --certificate-authority=${CA_FILE} \
  --embed-certs=true \
  --server=https://${NODE_IP}:6443 \
  --kubeconfig=vk.kubeconfig
#设置客户端认证参数
kubectl config set-credentials admin \
  --client-certificate=${CERT_DIR}/vk.pem \
  --client-key=${CERT_DIR}/vk-key.pem \
  --embed-certs=true \
  --kubeconfig=vk.kubeconfig
# 设置上下文参数
kubectl config set-context kubernetes \
  --cluster=kubernetes \
  --user=admin \
  --kubeconfig=vk.kubeconfig
# 设置默认上下文
kubectl config use-context kubernetes --kubeconfig=vk.kubeconfig

export KUBECONFIG=${CERT_DIR}/vk.kubeconfig

/virtual-kubelet \
  --log-level debug \
  --disable-taint \
  --enable-node-lease \
  --enable-leader-election \
  --nodename ${NODE_NAME} \
  --kubeconfig ${KUBECONFIG} \
  >>${LOG_DIR}/virtual-kubelet.log 2>&1
