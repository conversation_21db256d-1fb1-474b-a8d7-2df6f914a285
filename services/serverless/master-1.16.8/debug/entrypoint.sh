#!/bin/sh

# launch logrotate
sed -i "s#LOG_DIR#${LOG_DIR}#" /logrotate/logrotate.conf
if [[ "${LOG_ROTATE_COUNT}" == "" ]]; then
	sed -i "s#LOG_ROTATE_COUNT#4#" /logrotate/logrotate.conf
else
	sed -i "s#LOG_ROTATE_COUNT#${LOG_ROTATE_COUNT}#" /logrotate/logrotate.conf
fi

# ensure log dir permission or logrotate cannot work
mkdir -p "${LOG_DIR}" && chmod 755 "${LOG_DIR}"
nohup crond > /var/log/cron 2>&1

# wait until apiserver is ready
until kubectl --request-timeout=10s --kubeconfig=/root/.kube/config get cs ; do sleep 1 ; done

# cluster role binding
if [[ "${ACCOUNT_ID}" == "" || ${USER_ID} == "" ]]; then
	echo "ACCOUNT_ID and USER_ID must be set"
	exit 127
fi

cat >clusterrolebinding.yaml <<EOF
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: cce:admin
rules:
- apiGroups: ["*"]
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: clusterrolebinding-${ACCOUNT_ID}-0
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- apiGroup: rbac.authorization.k8s.io
  kind: User
  name: ${ACCOUNT_ID}-0
EOF


[[ "${ACCOUNT_ID}" != "${USER_ID}" ]] && cat >>clusterrolebinding.yaml <<EOF
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: clusterrolebinding-${USER_ID}-0
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
- apiGroup: rbac.authorization.k8s.io
  kind: User
  name: ${USER_ID}-0
EOF

# cluster role binding
if [[ "${ACCOUNT_ID}" == "" ]] || [[ ${USER_ID} == "" ]]; then
	echo "Either ACCOUNT_ID or USER_ID is empty, skip clusterrolebinding"
else
	until kubectl --request-timeout=10s apply -f clusterrolebinding.yaml; do sleep 2; done
fi


trap 'exit 0' SIGTERM
while true; do :; done
