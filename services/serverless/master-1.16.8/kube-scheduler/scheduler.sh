#!/bin/sh

set -eu

mkdir -p ${CERT_DIR} && cd ${CERT_DIR}

export KUBE_APISERVER=https://${NODE_IP}:6443

cat >kube-scheduler-csr.json <<EOF
{
    "CN": "system:kube-scheduler",
    "hosts": [
      "${NODE_IP}"
    ],
    "key": {
        "algo": "rsa",
        "size": 2048
    },
    "names": [
      {
        "C": "CN",
        "ST": "BeiJing",
        "L": "BeiJing",
        "O": "system:kube-scheduler",
        "OU": "cloudnative"
      }
    ]
}
EOF

# 生成证书和私钥
cfssl gencert -ca=${CA_FILE} \
  -ca-key=${CA_KEY} \
  -config=${CA_CONFIG} \
  -profile=jpaas kube-scheduler-csr.json | cfssljson -bare kube-scheduler

# 创建kubeconfig文件
kubectl config set-cluster kubernetes \
  --certificate-authority=${CA_FILE} \
  --embed-certs=true \
  --server=${KUBE_APISERVER} \
  --kubeconfig=kube-scheduler.kubeconfig
kubectl config set-credentials system:kube-scheduler \
  --client-certificate=kube-scheduler.pem \
  --client-key=kube-scheduler-key.pem \
  --embed-certs=true \
  --kubeconfig=kube-scheduler.kubeconfig
kubectl config set-context system:kube-scheduler \
  --cluster=kubernetes \
  --user=system:kube-scheduler \
  --kubeconfig=kube-scheduler.kubeconfig
kubectl config use-context system:kube-scheduler --kubeconfig=kube-scheduler.kubeconfig

/kube-scheduler \
  --kubeconfig=${CERT_DIR}/kube-scheduler.kubeconfig \
  --logtostderr=true \
  --kube-api-qps=100 \
  --kube-api-burst=100 \
  --profiling \
  --feature-gates=VolumeSnapshotDataSource=true,CSINodeInfo=true,CSIDriverRegistry=true,WatchBookmark=true,NodeLease=true,APIListChunking=true \
  --v=6 \
  >>${LOG_DIR}/scheduler.log 2>&1
