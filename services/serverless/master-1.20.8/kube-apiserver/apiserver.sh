#!/bin/sh

set -eu

mkdir -p ${CERT_DIR} && cd ${CERT_DIR}

export ENCRYPTION_KEY=$(head -c 32 /dev/urandom | base64)

EXTERNAL_HOSTS=${NODE_IP}'","'${BLB_VPC_IP}'","'${BLB_FLOATING_IP}

if ! [ -z ${BLB_EIP+x} ]; then
  EXTERNAL_HOSTS=${EXTERNAL_HOSTS}'","'${BLB_EIP}
fi

FIRST_SVC_IP=$(echo -n ${SERVICE_CLUSTER_IP_RANGE} | awk -F "/" '{print $1}' | sed 's#0$#1#')
if ! [ -z ${FIRST_SVC_IP+x} ]; then
  EXTERNAL_HOSTS=${EXTERNAL_HOSTS}'","'${FIRST_SVC_IP}
fi

cat >admin-csr.json <<EOF
{
  "CN": "admin",
  "hosts": [
    "127.0.0.1",
    "kubernetes",
    "kubernetes.default",
    "kubernetes.default.svc",
    "kubernetes.default.svc.cluster",
    "kubernetes.default.svc.cluster.local",
    "${EXTERNAL_HOSTS}"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "kubernetes-admin",
      "ST": "BeiJing",
      "L": "BeiJing",
      "O": "system:masters",
      "OU": "cloudnative"
    }
  ]
}
EOF
# 生成admin证书和私钥
cfssl gencert -ca=${CA_FILE} -ca-key=${CA_KEY} -config=${CA_CONFIG} -profile=jpaas admin-csr.json | cfssljson -bare admin

# kubeconfig
kubectl config set-cluster kubernetes \
  --certificate-authority=${CA_FILE} \
  --embed-certs=true \
  --server=https://${NODE_IP}:6443 \
  --kubeconfig=kubectl.kubeconfig
#设置客户端认证参数
kubectl config set-credentials admin \
  --client-certificate=${CERT_DIR}/admin.pem \
  --client-key=${CERT_DIR}/admin-key.pem \
  --embed-certs=true \
  --kubeconfig=kubectl.kubeconfig
# 设置上下文参数
kubectl config set-context kubernetes \
  --cluster=kubernetes \
  --user=admin \
  --kubeconfig=kubectl.kubeconfig
# 设置默认上下文
kubectl config use-context kubernetes --kubeconfig=kubectl.kubeconfig

cp -p kubectl.kubeconfig /root/.kube/config

# kube-apiserver
cat >kube-apiserver-csr.json <<EOF
{
  "CN": "kubernetes",
  "hosts": [
    "127.0.0.1",
    "kubernetes",
    "kubernetes.default",
    "kubernetes.default.svc",
    "kubernetes.default.svc.cluster",
    "kubernetes.default.svc.cluster.local",
    "${EXTERNAL_HOSTS}"
  ],
  "key": {
    "algo": "rsa",
    "size": 2048
  },
  "names": [
    {
      "C": "CN",
      "ST": "BeiJing",
      "L": "BeiJing",
      "O": "k8s",
      "OU": "cloudnative"
    }
  ]
}
EOF

# 生成证书和私钥
cfssl gencert -ca=${CA_FILE} -ca-key=${CA_KEY} -config=${CA_CONFIG} -profile=jpaas kube-apiserver-csr.json | cfssljson -bare kube-apiserver

cat >encryption-config.yaml <<EOF
kind: EncryptionConfig
apiVersion: v1
resources:
  - resources:
      - secrets
    providers:
      - aescbc:
          keys:
            - name: key1
              secret: ${ENCRYPTION_KEY}
      - identity: {}
EOF

cat >"${AUDIT_POLICY_FILE}" <<EOF
apiVersion: audit.k8s.io/v1
kind: Policy
rules:
  # The following requests were manually identified as high-volume and low-risk, so drop them.
  - level: None
    resources:
      - group: ""
        resources:
          - endpoints
          - services
          - services/status
    users:
      - 'system:kube-proxy'
    verbs:
      - watch
  - level: None
    resources:
      - group: ""
        resources:
          - nodes
          - nodes/status
    userGroups:
      - 'system:nodes'
    verbs:
      - get
  - level: None
    namespaces:
      - kube-system
    resources:
      - group: ""
        resources:
          - endpoints
    users:
      - 'system:kube-controller-manager'
      - 'system:kube-scheduler'
      - 'system:serviceaccount:kube-system:endpoint-controller'
    verbs:
      - get
      - update
  - level: None
    resources:
      - group: ""
        resources:
          - namespaces
          - namespaces/status
          - namespaces/finalize
    users:
      - 'system:apiserver'
    verbs:
      - get
  # Don't log HPA fetching metrics.
  - level: None
    resources:
      - group: metrics.k8s.io
    users:
      - 'system:kube-controller-manager'
    verbs:
      - get
      - list
  # Don't log these read-only URLs.
  - level: None
    nonResourceURLs:
      - '/healthz*'
      - /version
      - '/swagger*'
  # Don't log events requests.
  - level: None
    resources:
      - group: ""
        resources:
          - events
  # node and pod status calls from nodes are high-volume and can be large, don't log responses for expected updates from nodes
  - level: Request
    omitStages:
      - RequestReceived
    resources:
      - group: ""
        resources:
          - nodes/status
          - pods/status
    users:
      - kubelet
      - 'system:node-problem-detector'
      - 'system:serviceaccount:kube-system:node-problem-detector'
    verbs:
      - update
      - patch
  - level: Request
    omitStages:
      - RequestReceived
    resources:
      - group: ""
        resources:
          - nodes/status
          - pods/status
    userGroups:
      - 'system:nodes'
    verbs:
      - update
      - patch
  # deletecollection calls can be large, don't log responses for expected namespace deletions
  - level: Request
    omitStages:
      - RequestReceived
    users:
      - 'system:serviceaccount:kube-system:namespace-controller'
    verbs:
      - deletecollection
  # Secrets, ConfigMaps, and TokenReviews can contain sensitive & binary data,
  # so only log at the Metadata level.
  - level: Metadata
    omitStages:
      - RequestReceived
    resources:
      - group: ""
        resources:
          - secrets
          - configmaps
      - group: authentication.k8s.io
        resources:
          - tokenreviews
  # Get repsonses can be large; skip them.
  - level: Request
    omitStages:
      - RequestReceived
    resources:
      - group: ""
      - group: admissionregistration.k8s.io
      - group: apiextensions.k8s.io
      - group: apiregistration.k8s.io
      - group: apps
      - group: authentication.k8s.io
      - group: authorization.k8s.io
      - group: autoscaling
      - group: batch
      - group: certificates.k8s.io
      - group: extensions
      - group: metrics.k8s.io
      - group: networking.k8s.io
      - group: policy
      - group: rbac.authorization.k8s.io
      - group: scheduling.k8s.io
      - group: settings.k8s.io
      - group: storage.k8s.io
    verbs:
      - get
      - list
      - watch
  # Default level for known APIs
  - level: RequestResponse
    omitStages:
      - RequestReceived
    resources:
      - group: ""
      - group: admissionregistration.k8s.io
      - group: apiextensions.k8s.io
      - group: apiregistration.k8s.io
      - group: apps
      - group: authentication.k8s.io
      - group: authorization.k8s.io
      - group: autoscaling
      - group: batch
      - group: certificates.k8s.io
      - group: extensions
      - group: metrics.k8s.io
      - group: networking.k8s.io
      - group: policy
      - group: rbac.authorization.k8s.io
      - group: scheduling.k8s.io
      - group: settings.k8s.io
      - group: storage.k8s.io
  # Default level for all other requests.
  - level: Metadata
    omitStages:
      - RequestReceived
EOF

/kube-apiserver \
  --enable-admission-plugins=DefaultStorageClass,DefaultTolerationSeconds,LimitRanger,MutatingAdmissionWebhook,NamespaceLifecycle,PersistentVolumeClaimResize,Priority,ResourceQuota,ServiceAccount,ValidatingAdmissionWebhook \
  --audit-webhook-mode=batch \
  --audit-webhook-batch-max-size=40 \
  --audit-policy-file=${AUDIT_POLICY_FILE} \
  --advertise-address=${NODE_IP} \
  --external-hostname=${NODE_IP}  \
  --allow-privileged=true \
  --apiserver-count=${MASTER_COUNT} \
  --authorization-mode=RBAC,Node \
  --bind-address=${NODE_IP} \
  --client-ca-file=${CA_FILE} \
  --enable-swagger-ui=true \
  --kubelet-preferred-address-types=InternalIP,Hostname \
  --etcd-cafile=${ETCD_CAFILE} \
  --etcd-certfile=${ETCD_CERTFILE} \
  --etcd-keyfile=${ETCD_KEYFILE} \
  --etcd-servers=${ETCD_SERVERS} \
  --etcd-prefix=${ETCD_PREFIX} \
  --enable-bootstrap-token-auth=true \
  --feature-gates=MixedProtocolLBService=true,TTLAfterFinished=true \
  --insecure-port=0 \
  --logtostderr=true \
  --secure-port=6443 \
  --service-account-issuer=https://kubernetes.default.svc.cluster.local \
  --api-audiences=kubernetes.default.svc \
  --service-account-signing-key-file=/${CA_KEY} \
  --service-account-key-file=/${CA_KEY} \
  --service-cluster-ip-range=${SERVICE_CLUSTER_IP_RANGE} \
  --service-node-port-range=30000-32768 \
  --storage-backend=etcd3 \
  --tls-cert-file=${CERT_DIR}/kube-apiserver.pem\
  --tls-private-key-file=${CERT_DIR}/kube-apiserver-key.pem \
  --tls-cipher-suites=TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305,TLS_RSA_WITH_AES_128_CBC_SHA,TLS_RSA_WITH_AES_256_CBC_SHA,TLS_RSA_WITH_AES_128_GCM_SHA256,TLS_RSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA \
  --kubelet-client-certificate=${CA_FILE} \
  --kubelet-client-key=${CA_KEY} \
  --anonymous-auth=false \
  --v=6 \
  --etcd-compaction-interval=0 \
  >>${LOG_DIR}/apiserver.log 2>&1

# followings are options removed from serverless apiserver
# --requestheader-client-ca-file=/etc/kubernetes/pki/front-proxy-ca.pem \
# --requestheader-allowed-names=front-proxy-client \
# --requestheader-extra-headers-prefix=X-Remote-Extra- \
# --requestheader-group-headers=X-Remote-Group \
# --requestheader-username-headers=X-Remote-User \
# --proxy-client-cert-file=/etc/kubernetes/pki/front-proxy-client.pem \
# --proxy-client-key-file=/etc/kubernetes/pki/front-proxy-client-key.pem \
# --enable-aggregator-routing=true \
# --audit-webhook-config-file=/etc/kubernetes/audit-webhook.conf \
# --cloud-config=/etc/kubernetes/cloud.config \
