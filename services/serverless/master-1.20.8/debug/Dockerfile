FROM registry.baidubce.com/serverless-k8s/base:1.20.8

LABEL maintainer "Baidu Cloud CCE <<EMAIL>>"

COPY logrotate.cron /logrotate/logrotate.cron
COPY logrotate.conf /logrotate/logrotate.conf
COPY entrypoint.sh /entrypoint.sh
RUN chmod +x /entrypoint.sh \
    && chmod +x /logrotate/logrotate.cron \
    && echo "*       *       *       *       *       /logrotate/logrotate.cron" >> /etc/crontabs/root
ENTRYPOINT [ "/entrypoint.sh" ]

