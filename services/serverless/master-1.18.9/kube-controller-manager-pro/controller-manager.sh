#!/bin/sh

set -eu

mkdir -p ${CERT_DIR} && cd ${CERT_DIR}

export KUBE_APISERVER=https://${NODE_IP}:6443

# 创建证书签名请求
cat >kube-controller-manager-csr.json <<EOF
{
    "CN": "system:kube-controller-manager",
    "key": {
        "algo": "rsa",
        "size": 2048
    },
    "hosts": [
      "${NODE_IP}"
    ],
    "names": [
      {
        "C": "CN",
        "ST": "BeiJing",
        "L": "BeiJing",
        "O": "system:kube-controller-manager",
        "OU": "cloudnative"
      }
    ]
}
EOF

# 生成证书和私钥
cfssl gencert -ca=${CA_FILE} \
  -ca-key=${CA_KEY} \
  -config=${CA_CONFIG} \
  -profile=jpaas kube-controller-manager-csr.json | cfssljson -bare kube-controller-manager

# 创建kubeconfig文件
kubectl config set-cluster kubernetes \
  --certificate-authority=${CA_FILE} \
  --embed-certs=true \
  --server=${KUBE_APISERVER} \
  --kubeconfig=kube-controller-manager.kubeconfig

kubectl config set-credentials system:kube-controller-manager \
  --client-certificate=kube-controller-manager.pem \
  --client-key=kube-controller-manager-key.pem \
  --embed-certs=true \
  --kubeconfig=kube-controller-manager.kubeconfig

kubectl config set-context system:kube-controller-manager \
  --cluster=kubernetes \
  --user=system:kube-controller-manager \
  --kubeconfig=kube-controller-manager.kubeconfig
kubectl config use-context system:kube-controller-manager --kubeconfig=kube-controller-manager.kubeconfig

/kube-controller-manager \
  --allocate-node-cidrs=true \
  --cluster-cidr=${CLUSTER_CIDR} \
  --cluster-name=kubernetes \
  --kube-api-qps=500 \
  --kube-api-burst=500 \
  --profiling \
  --cluster-signing-cert-file=${CA_FILE} \
  --cluster-signing-key-file=${CA_KEY} \
  --controllers=*,bootstrapsigner,tokencleaner \
  --feature-gates=VolumeSnapshotDataSource=true \
  --horizontal-pod-autoscaler-use-rest-clients=true \
  --kubeconfig=${CERT_DIR}/kube-controller-manager.kubeconfig \
  --authentication-kubeconfig=${CERT_DIR}/kube-controller-manager.kubeconfig \
  --leader-elect=true \
  --logtostderr=true \
  --master=${KUBE_APISERVER} \
  --root-ca-file=${CA_FILE} \
  --route-reconciliation-period=50s \
  --node-cidr-mask-size=${NODE_CIDR_MASK_SIZE} \
  --service-account-private-key-file=${CA_KEY} \
  --service-cluster-ip-range=${SERVICE_CLUSTER_IP_RANGE} \
  --use-service-account-credentials=true \
  --v=6
# --cloud-config=/etc/kubernetes/cloud.config \
