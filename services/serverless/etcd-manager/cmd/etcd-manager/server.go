package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"

	"github.com/gorilla/mux"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/auth"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/manager"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

type server struct {
	authClient auth.Interface
	model      models.Interface
	addr       string
	manager    manager.Manager
}

func NewServer(cfg config.Config) (*server, error) {
	if err := utils.Valid(cfg); err != nil {
		return nil, err
	}

	authClient := auth.NewClient(&bce.Config{
		Credentials: bce.NewCredentials("", ""),
		Endpoint:    cfg.AuthEndpoint,
		Timeout:     30 * time.Second,
		Checksum:    true,
	})

	model, err := models.NewClient(context.TODO(), cfg.MySQLConn)
	if err != nil {
		return nil, err
	}

	return &server{
		authClient: authClient,
		model:      model,
		manager:    manager.NewManager(context.TODO(), cfg),
		addr:       fmt.Sprintf("0.0.0.0:%d", cfg.Port),
	}, nil
}

func (s *server) run() error {
	router := mux.NewRouter()
	router.HandleFunc("/hello", s.helloHandler).Methods("GET")
	router.HandleFunc("/api/v1/user", s.addUserHandler).Methods("POST")
	router.HandleFunc("/api/v1/user/{username}", s.purgeUserHandler).Methods("DELETE")

	srv := &http.Server{
		Addr: s.addr,
		// Good practice to set timeouts to avoid Slowloris attacks.
		WriteTimeout: time.Second * 30,
		ReadTimeout:  time.Second * 30,
		IdleTimeout:  time.Second * 60,
		Handler:      router,
	}

	return srv.ListenAndServe()
}

func (*server) helloHandler(w http.ResponseWriter, r *http.Request) {
	logs.Info("hello")
	w.Write([]byte("hello"))
}

func (s *server) auth(ctx context.Context, r *http.Request) (userID, accountID string, err error) {
	if r.Header.Get("Authorization") == "" {
		msg := "empty authorization header"
		err = errorcode.NewError(errorcode.NewInvalidHTTPAuthHeader(msg), fmt.Errorf(msg))
		return
	}
	authArgs, err := auth.NewAuthenticationArgs(ctx, r)
	if err != nil {
		return
	}
	authResp, err := s.authClient.Authentication(ctx, authArgs, nil)
	if err != nil {
		return
	}
	return authResp.Result.User.ID, authResp.Result.User.Domain.ID, nil
}

func (s *server) addUserHandler(w http.ResponseWriter, r *http.Request) {
	requestID := r.Header.Get("x-bce-request-id")
	if requestID == "" {
		requestID = logger.GetUUID()
	}
	logs.Infof("add user request received, header=%v", r.Header)
	w.Header().Set("x-bce-request-id", requestID)
	ctx := logger.WithRequestID(context.TODO(), requestID)
	res, err := s.addUser(ctx, r)
	if err != nil {
		code := errorcode.GetCode(err)
		// This api is expected to be invoked internally, so detailed error can be returned for
		// convenient debug.
		msg := code.Message + ": " + err.Error()
		logs.Errorf("addUser err: %s", msg)
		w.WriteHeader(code.StatusCode)
		w.Write([]byte(msg))
	} else {
		w.Header().Set("content-type", "application/json")
		w.Write(res)
	}
}

func (s *server) addUser(ctx context.Context, r *http.Request) ([]byte, error) {
	_, accountID, err := s.auth(ctx, r)
	if err != nil {
		return nil, err
	}

	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}

	req := manager.NewAddUserReq()
	req.RequestID = logger.GetRequestID(ctx)
	err = json.Unmarshal(body, req)
	if err != nil {
		return nil, errorcode.NewError(errorcode.NewMalformedJSON(err.Error()), err)
	}
	if req.Username == "" {
		return nil, errorcode.NewInvalidParam("username must be set")
	}
	clusterID := req.Username
	cluster, err := s.model.GetClusterByClusterID(ctx, clusterID)
	if err != nil {
		if models.IsNotExist(err) {
			return nil, errorcode.NewError(errorcode.NewClusterNotFound(),
				fmt.Errorf("cluster %s not found: %v", clusterID, err))
		}
		return nil, err
	}
	// TODO: should depend on UserID instead of AccountID?
	if cluster.Spec.AccountID != accountID {
		msg := fmt.Sprintf("mismatch accountID: cluster=%s, signature=%s", cluster.Spec.AccountID, accountID)
		return nil, errorcode.NewError(errorcode.NewAccessDenied("mismatch accountID"), fmt.Errorf(msg))
	}

	logs.Infof("start to add user with req %+v", req)

	res, err := s.manager.AddUser(req)
	if err != nil {
		return nil, err
	}
	return json.Marshal(res)
}

func (s *server) purgeUserHandler(w http.ResponseWriter, r *http.Request) {
	requestID := r.Header.Get("x-bce-request-id")
	if requestID == "" {
		requestID = logger.GetUUID()
	}
	logs.Infof("purge user request received, header=%v", r.Header)
	w.Header().Set("x-bce-request-id", requestID)
	ctx := logger.WithRequestID(context.TODO(), requestID)
	username := mux.Vars(r)["username"]
	endpoints := r.URL.Query()["endpoints"]
	if err := s.purgeUser(ctx, r, username, endpoints); err != nil {
		code := errorcode.GetCode(err)
		// This api is expected to be invoked internally, so detailed error can be returned for
		// convenient debug.
		msg := code.Message + ": " + err.Error()
		logs.Errorf("addUser err: %s", msg)
		w.WriteHeader(code.StatusCode)
		w.Write([]byte(msg))
	}
}

func (s *server) purgeUser(ctx context.Context, r *http.Request, username string, endpoints []string) error {
	_, accountID, err := s.auth(ctx, r)
	if err != nil {
		return err
	}
	if username == "" {
		return errorcode.NewInvalidParam("username must be set")
	}

	clusterID := username
	cluster, err := s.model.GetClusterByClusterIDWithDeleted(ctx, clusterID)
	if err != nil {
		if models.IsNotExist(err) {
			return errorcode.NewError(errorcode.NewClusterNotFound(),
				fmt.Errorf("cluster %s not found: %v", clusterID, err))
		}
		return err
	}
	// TODO: should depend on UserID instead of AccountID?
	if cluster.Spec.AccountID != accountID {
		msg := fmt.Sprintf("mismatch accountID: cluster=%s, signature=%s", cluster.Spec.AccountID, accountID)
		return errorcode.NewError(errorcode.NewAccessDenied("mismatch accountID"), fmt.Errorf(msg))
	}

	logs.Infof("start to purge user with username=%s endpoints=%v", username, endpoints)
	req := manager.NewPurgeUserReq(username, endpoints, logger.GetRequestID(ctx))
	return s.manager.PurgeUser(req)
}
