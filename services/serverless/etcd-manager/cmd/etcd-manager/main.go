package main

import (
	"github.com/spf13/pflag"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/flag"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

var configFile = pflag.StringP("config", "c", "", "config file path")

func main() {
	flag.InitFlags()

	logs.InitLogs()
	defer logs.FlushLogs()

	cfg, err := config.NewConfigFromFile(*configFile)
	if err != nil {
		logs.Fatalf("fail to initialize config: %v", err)
	}

	server, err := NewServer(*cfg)
	if err != nil {
		logs.Fatalf("fail to initialize server: %v", err)
	}
	if err := server.run(); err != nil {
		logs.Fatal(err.Error())
	}
}
