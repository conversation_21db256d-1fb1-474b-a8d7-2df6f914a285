package main

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"testing"

	"github.com/golang/mock/gomock"
	"gotest.tools/assert"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/auth"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/auth/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/manager"
)

func Test_server_addUser(t *testing.T) {
	type fields struct {
		ctrl *gomock.Controller

		ctx context.Context
		r   *http.Request

		authClient auth.Interface
		model      models.Interface
		addr       string
		manager    manager.Manager

		want []byte
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "add successfully",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)
				authClient := mock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				mgr := manager.NewMockManager(ctrl)

				body, err := json.Marshal(&manager.AddUserReq{Username: "cce-test1234"})
				if err != nil {
					t.Fatal(err)
				}

				req, err := http.NewRequest(http.MethodPost, "http://example.org", bytes.NewBuffer(body))
				if err != nil {
					t.Fatal(err)
				}
				req.Header.Add("Authorization", "auth-string")
				req.Header.Add("x-bce-request-id", "test-request-id")
				ctx = logger.WithRequestID(ctx, "test-request-id")

				resp := &manager.AddUserRes{
					Cert:      "Cert",
					Key:       "Key",
					Endpoints: []string{"etcd.endpoint.com"},
				}
				respBytes, err := json.Marshal(resp)
				if err != nil {
					t.Fatal(err)
				}

				authArgs, err := auth.NewAuthenticationArgs(ctx, req)
				if err != nil {
					t.Fatal(err)
				}

				authClient.EXPECT().Authentication(ctx, authArgs, nil).Return(&auth.AuthResponse{
					Result: &auth.Token{
						User: &auth.User{
							ID: "userId",
							Domain: &auth.Domain{
								ID: "accountId",
							},
						},
					},
				}, nil)
				model.EXPECT().GetClusterByClusterID(ctx, "cce-test1234").Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						AccountID: "accountId",
					},
				}, nil)
				mgr.EXPECT().AddUser(&manager.AddUserReq{
					Username:  "cce-test1234",
					RequestID: "test-request-id",
				}).Return(resp, nil)
				return fields{
					ctrl:       ctrl,
					ctx:        ctx,
					r:          req,
					authClient: authClient,
					model:      model,
					manager:    mgr,
					want:       respBytes,
				}
			}(),
		},
		{
			name: "username in req is empty",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)
				authClient := mock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				mgr := manager.NewMockManager(ctrl)

				body, err := json.Marshal(&manager.AddUserReq{Username: ""})
				if err != nil {
					t.Fatal(err)
				}

				req, err := http.NewRequest(http.MethodPost, "http://example.org", bytes.NewBuffer(body))
				if err != nil {
					t.Fatal(err)
				}
				req.Header.Add("Authorization", "auth-string")
				req.Header.Add("x-bce-request-id", "test-request-id")
				ctx = logger.WithRequestID(ctx, "test-request-id")

				authArgs, err := auth.NewAuthenticationArgs(ctx, req)
				if err != nil {
					t.Fatal(err)
				}

				authClient.EXPECT().Authentication(ctx, authArgs, nil).Return(&auth.AuthResponse{
					Result: &auth.Token{
						User: &auth.User{
							ID: "userId",
							Domain: &auth.Domain{
								ID: "accountId",
							},
						},
					},
				}, nil)

				return fields{
					ctrl:       ctrl,
					ctx:        ctx,
					r:          req,
					authClient: authClient,
					model:      model,
					manager:    mgr,
				}
			}(),
			wantErr: true,
		},
		{
			name: "cluster not found",
			fields: func() fields {
				ctx := context.TODO()
				ctrl := gomock.NewController(t)
				authClient := mock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				mgr := manager.NewMockManager(ctrl)

				body, err := json.Marshal(&manager.AddUserReq{Username: "cce-test1234"})
				if err != nil {
					t.Fatal(err)
				}

				req, err := http.NewRequest(http.MethodPost, "http://example.org", bytes.NewBuffer(body))
				if err != nil {
					t.Fatal(err)
				}
				req.Header.Add("Authorization", "auth-string")
				req.Header.Add("x-bce-request-id", "test-request-id")
				ctx = logger.WithRequestID(ctx, "test-request-id")

				authArgs, err := auth.NewAuthenticationArgs(ctx, req)
				if err != nil {
					t.Fatal(err)
				}

				authClient.EXPECT().Authentication(ctx, authArgs, nil).Return(&auth.AuthResponse{
					Result: &auth.Token{
						User: &auth.User{
							ID: "userId",
							Domain: &auth.Domain{
								ID: "accountId",
							},
						},
					},
				}, nil)
				model.EXPECT().GetClusterByClusterID(ctx, "cce-test1234").Return(nil, models.ErrNotExist.New(ctx, "cluster not found"))
				return fields{
					ctrl:       ctrl,
					ctx:        ctx,
					r:          req,
					authClient: authClient,
					model:      model,
					manager:    mgr,
				}
			}(),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			s := &server{
				authClient: tt.fields.authClient,
				model:      tt.fields.model,
				addr:       tt.fields.addr,
				manager:    tt.fields.manager,
			}
			got, err := s.addUser(tt.fields.ctx, tt.fields.r)
			if (err != nil) != tt.wantErr {
				t.Errorf("server.addUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.fields.want)
		})
	}
}

func Test_server_purgeUser(t *testing.T) {
	type fields struct {
		ctrl *gomock.Controller

		ctx context.Context
		r   *http.Request

		authClient auth.Interface
		model      models.Interface
		addr       string
		manager    manager.Manager

		username  string
		endpoints []string
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "purge sucessfully",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				authClient := mock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				mgr := manager.NewMockManager(ctrl)
				ctx := logger.WithRequestID(context.TODO(), "test-request-id")

				req, err := http.NewRequest(http.MethodDelete, "http://example.org", nil)
				if err != nil {
					t.Fatal(err)
				}
				req.Header.Add("Authorization", "auth-string")
				req.Header.Add("x-bce-request-id", "test-request-id")

				authArgs, err := auth.NewAuthenticationArgs(ctx, req)
				if err != nil {
					t.Fatal(err)
				}

				authClient.EXPECT().Authentication(ctx, authArgs, nil).Return(&auth.AuthResponse{
					Result: &auth.Token{
						User: &auth.User{
							ID: "userId",
							Domain: &auth.Domain{
								ID: "accountId",
							},
						},
					},
				}, nil)
				model.EXPECT().GetClusterByClusterIDWithDeleted(ctx, "cce-test1234").Return(&models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						AccountID: "accountId",
					},
				}, nil)
				mgr.EXPECT().PurgeUser(manager.NewPurgeUserReq("cce-test1234", nil, "test-request-id")).Return(nil)

				return fields{
					ctrl:       ctrl,
					ctx:        ctx,
					r:          req,
					authClient: authClient,
					model:      model,
					manager:    mgr,
					username:   "cce-test1234",
				}
			}(),
		},
		{
			name: "cluster not found",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				authClient := mock.NewMockInterface(ctrl)
				model := models.NewMockInterface(ctrl)
				mgr := manager.NewMockManager(ctrl)
				ctx := logger.WithRequestID(context.TODO(), "test-request-id")

				req, err := http.NewRequest(http.MethodDelete, "http://example.org", nil)
				if err != nil {
					t.Fatal(err)
				}
				req.Header.Add("Authorization", "auth-string")
				req.Header.Add("x-bce-request-id", "test-request-id")

				authArgs, err := auth.NewAuthenticationArgs(ctx, req)
				if err != nil {
					t.Fatal(err)
				}

				authClient.EXPECT().Authentication(ctx, authArgs, nil).Return(&auth.AuthResponse{
					Result: &auth.Token{
						User: &auth.User{
							ID: "userId",
							Domain: &auth.Domain{
								ID: "accountId",
							},
						},
					},
				}, nil)
				model.EXPECT().GetClusterByClusterIDWithDeleted(ctx, "cce-test1234").Return(nil, models.ErrNotExist.New(ctx, "cluster not found"))

				return fields{
					ctrl:       ctrl,
					ctx:        ctx,
					r:          req,
					authClient: authClient,
					model:      model,
					manager:    mgr,
					username:   "cce-test1234",
				}
			}(),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			s := &server{
				authClient: tt.fields.authClient,
				model:      tt.fields.model,
				addr:       tt.fields.addr,
				manager:    tt.fields.manager,
			}
			if err := s.purgeUser(tt.fields.ctx, tt.fields.r, tt.fields.username, tt.fields.endpoints); (err != nil) != tt.wantErr {
				t.Errorf("server.purgeUser() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
