FROM registry.baidubce.com/cce-service-pro/cce-base:v1.0.0

ENV WORKDIR=/home/<USER>/cce/cce-etcd-manager

RUN mkdir -p $WORKDIR/log

# 设置时区
ENV TZ=Asia/Shanghai

COPY cce-etcd-manager $WORKDIR/

WORKDIR $WORKDIR

ENTRYPOINT ["/home/<USER>/cce/cce-etcd-manager/cce-etcd-manager", "-c", "/home/<USER>/cce/cce-etcd-manager/conf/conf.yaml", "--log-dir", "/home/<USER>/cce/cce-etcd-manager/log", "-v", "10", "alsologtostderr"]
