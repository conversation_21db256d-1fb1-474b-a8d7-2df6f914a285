package main

import (
	"io/ioutil"
	"os"
	"path/filepath"

	"go.etcd.io/etcd/clientv3"
	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
)

const (
	certPath = "/home/<USER>/serverless-etcd/ssl"
)

func main() {
	conf := config.Config{
		Port:         8381,
		AuthEndpoint: "10.169.25.216:8400",
		MySQLConn:    "cce_service_w:6fS_5tvWNA@tcp(************:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
		Clusters: []config.ETCDClusterConfig{
			{
				Config: clientv3.Config{
					Endpoints: []string{
						"*************:8379",
						"*************:8379",
						"*************:8379",
					},
				},
				Name:                  "etcd0",
				UserEndpoints:         []string{"**********:8379"},
				OutputDir:             "_output/cert",
				ETCDPrefix:            "/gztest/serverless",
				InsecureSkipTLSVerify: false,
				CACertFile:            filepath.Join(certPath, "ca.pem"),
				CAKeyFile:             filepath.Join(certPath, "ca-key.pem"),
				RootUsername:          "root",
				RootCertFile:          filepath.Join(certPath, "root.pem"),
				RootCertKeyFile:       filepath.Join(certPath, "root-key.pem"),
				TrustedCAFile:         filepath.Join(certPath, "ca.pem"),
				UserCAConfig:          filepath.Join(certPath, "ca-config.json"),
				UserProfile:           "client",
			},
		},
	}
	b, err := yaml.Marshal(conf)
	if err != nil {
		panic(err)
	}

	err = ioutil.WriteFile("example.yaml", b, os.ModePerm)
	if err != nil {
		panic(err)
	}
}
