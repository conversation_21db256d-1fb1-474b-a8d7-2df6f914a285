FROM ubuntu
RUN mkdir -p /usr/local/share /etc/etcd /data/etcd
COPY ca /etc/etcd/
COPY server/server.pem server/server-key.pem /etc/etcd/
COPY bin/etcd bin/etcdctl /
CMD ["/bin/sh", "-c",\
"/etcd --name=etcd --data-dir=/data/etcd/data --wal-dir=/data/etcd/wal --initial-cluster-state=new \
 --auto-compaction-mode=periodic --auto-compaction-retention=1 --client-cert-auth=true \
 --trusted-ca-file=/etc/etcd/ca.pem --cert-file=/etc/etcd/server.pem --key-file=/etc/etcd/server-key.pem \
 --initial-cluster-state=new --advertise-client-urls https://0.0.0.0:2379 --listen-client-urls https://0.0.0.0:2379 \
  >/usr/local/share/etcd.log 2>&1"]
