ca-cert-file: /home/<USER>/serverless-etcd/ssl/ca.pem
ca-key-file: /home/<USER>/serverless-etcd/ssl/ca-key.pem
endpoints:
- *************:8379
- *************:8379
- *************:8379
etcd-prefix: /serverless
insecure-skip-tls-verify: false
name: etcd0
output-dir: _output/cert
root-cert-file: /home/<USER>/serverless-etcd/ssl/root.pem
root-cert-key-file: /home/<USER>/serverless-etcd/ssl/root-key.pem
root-username: root
trusted-ca-file: /home/<USER>/serverless-etcd/ssl/ca.pem
user-ca-config: /home/<USER>/serverless-etcd/ssl/ca-config.json
user-endpoints:
- **********:8379
user-profile: client
