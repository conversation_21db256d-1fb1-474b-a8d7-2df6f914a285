package manager

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io/ioutil"
	"time"

	"go.etcd.io/etcd/clientv3"
	"go.etcd.io/etcd/pkg/tlsutil"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

const (
	// etcd同步集群信息的时间
	autoSyncInterval = time.Second * 30

	// 连接超时时间
	connectTimeout = time.Second * 5

	// 请求超时时间
	callTimeout = time.Second * 5

	// watch出错后重试的间隔时间
	watchErrorInterval time.Duration = time.Millisecond * 500

	// lockTimeout 获取锁的超时时间
	lockTimeout = time.Second * 3

	// 选举时会话保持的时间，单位是秒
	electTTL = 30

	// 每次选举时等待成功成为master的最长时间
	electTimeout = time.Second * 30
)

var _ ClientV3 = (*clientv3.Client)(nil)

//go:generate mockgen -destination ./mock_client.go -package manager icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/manager ClientV3
type ClientV3 interface {
	RoleList(ctx context.Context) (*clientv3.AuthRoleListResponse, error)
	RoleGet(ctx context.Context, name string) (*clientv3.AuthRoleGetResponse, error)
	RoleAdd(ctx context.Context, name string) (*clientv3.AuthRoleAddResponse, error)
	RoleDelete(ctx context.Context, name string) (*clientv3.AuthRoleDeleteResponse, error)
	RoleGrantPermission(ctx context.Context, name, key, rangeEnd string, permType clientv3.PermissionType) (*clientv3.AuthRoleGrantPermissionResponse, error)
	RoleRevokePermission(ctx context.Context, name string, key, rangeEnd string) (*clientv3.AuthRoleRevokePermissionResponse, error)
	UserList(ctx context.Context) (*clientv3.AuthUserListResponse, error)
	UserGet(ctx context.Context, name string) (*clientv3.AuthUserGetResponse, error)
	UserAddWithOptions(ctx context.Context, name, password string, opt *clientv3.UserAddOptions) (*clientv3.AuthUserAddResponse, error)
	UserGrantRole(ctx context.Context, user, role string) (*clientv3.AuthUserGrantRoleResponse, error)
	UserRevokeRole(ctx context.Context, user, role string) (*clientv3.AuthUserRevokeRoleResponse, error)
	AuthEnable(ctx context.Context) (*clientv3.AuthEnableResponse, error)
	UserDelete(ctx context.Context, user string) (*clientv3.AuthUserDeleteResponse, error)
	Delete(ctx context.Context, key string, opts ...clientv3.OpOption) (*clientv3.DeleteResponse, error)
	Close() error
}

// NewFromYAMLClient 使用配置生成etcd客户端
func NewETCDClient(cfg config.ETCDClusterConfig) (*ETCDClient, error) {
	if err := utils.Valid(cfg); err != nil {
		return nil, err
	}

	var (
		cert     *tls.Certificate
		certPool *x509.CertPool
	)

	certGen := cfssl.NewCertGenerator()

	// default configs
	cfg.Config.AutoSyncInterval = autoSyncInterval
	cfg.Config.DialTimeout = connectTimeout

	cfg.Config.Username = cfg.RootUsername

	cert, err := tlsutil.NewCert(cfg.RootCertFile, cfg.RootCertKeyFile, nil)
	if err != nil {
		return nil, fmt.Errorf("initialize root cert: %v", err)
	}

	certPool, err = tlsutil.NewCertPool([]string{cfg.TrustedCAFile})
	if err != nil {
		return nil, fmt.Errorf("initialize trusted ca file: %v", err)
	}

	tlscfg := &tls.Config{
		MinVersion:         tls.VersionTLS12,
		InsecureSkipVerify: cfg.InsecureSkipTLSVerify,
		RootCAs:            certPool,
	}
	if cert != nil {
		tlscfg.Certificates = []tls.Certificate{*cert}
	}
	cfg.Config.TLS = tlscfg

	c, err := clientv3.New(cfg.Config)
	if err != nil {
		return nil, fmt.Errorf("new etcd client failed: %w", err)
	}

	caByte, err := ioutil.ReadFile(cfg.TrustedCAFile)
	if err != nil {
		logs.Fatalf("read trusted ca file %s failed: %s", cfg.TrustedCAFile, err.Error())
	}

	return &ETCDClient{
		client:        c,
		certGenerator: certGen,
		caByte:        caByte,
		config:        &cfg,
		logger:        logs.NewLogger().WithField("cluster", cfg.Name),
	}, nil
}

type ETCDClient struct {
	client        ClientV3
	certGenerator cfssl.CertGenerator
	caByte        []byte // CA证书数据，用来返回给用户
	config        *config.ETCDClusterConfig
	logger        *logs.Logger
}

func (c *ETCDClient) defaultContext() (context.Context, context.CancelFunc) {
	return context.WithTimeout(context.Background(), callTimeout)
}

// Restart 重新开启etcd客户端。用来在添加root用户后保证当前权限正确。
func (c *ETCDClient) Restart() {
	err := c.client.Close()
	if err != nil {
		panic(err)
	}

	c.client, err = clientv3.New(c.config.Config)
	if err != nil {
		logs.Fatalf("renew etcd client failed: %s", err.Error())
		panic(err)
	}
}

// RoleList 获取角色列表
func (c *ETCDClient) RoleList() (*clientv3.AuthRoleListResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.RoleList(ctx)
	cancel()
	c.logger.V(8).Info("etcd role list")
	return res, err
}

// RoleGet 获取角色信息
func (c *ETCDClient) RoleGet(name string) (*clientv3.AuthRoleGetResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.RoleGet(ctx, name)
	cancel()
	c.logger.V(8).Infof("etcd role get %s", name)
	return res, err
}

// RoleAdd 添加角色
func (c *ETCDClient) RoleAdd(name string) (*clientv3.AuthRoleAddResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.RoleAdd(ctx, name)
	cancel()
	c.logger.V(8).Infof("etcd role add %s", name)
	return res, err
}

// RoleDelete 删除角色
func (c *ETCDClient) RoleDelete(name string) (*clientv3.AuthRoleDeleteResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.RoleDelete(ctx, name)
	cancel()
	c.logger.V(8).Infof("etcd role delete %s", name)
	return res, err
}

type PermissionType uint8

const (
	PermRead      PermissionType = 0
	PermWrite     PermissionType = 1
	PermReadWrite PermissionType = 2
)

// RoleGrantPermission 为角色添加数据权限，如果rangeEnd为空则认为对前缀做处理
func (c *ETCDClient) RoleGrantPermission(name string, key, rangeEnd string, permType PermissionType) (*clientv3.AuthRoleGrantPermissionResponse, error) {
	var t clientv3.PermissionType
	switch permType {
	case PermRead:
		t = clientv3.PermissionType(clientv3.PermRead)
	case PermWrite:
		t = clientv3.PermissionType(clientv3.PermWrite)
	case PermReadWrite:
		t = clientv3.PermissionType(clientv3.PermReadWrite)
	}

	if rangeEnd == "" {
		rangeEnd = clientv3.GetPrefixRangeEnd(key)
	}
	ctx, cancel := c.defaultContext()
	res, err := c.client.RoleGrantPermission(ctx, name, key, rangeEnd, t)
	cancel()
	c.logger.V(8).Infof("etcd role %s grant (%s %s)", name, key, rangeEnd)
	return res, err
}

// RoleRevokePermission 为角色删除数据权限，如果rangeEnd为空则认为对前缀做处理
func (c *ETCDClient) RoleRevokePermission(name string, key, rangeEnd string) (*clientv3.AuthRoleRevokePermissionResponse, error) {
	if rangeEnd == "" {
		rangeEnd = clientv3.GetPrefixRangeEnd(key)
	}
	ctx, cancel := c.defaultContext()
	res, err := c.client.RoleRevokePermission(ctx, name, key, rangeEnd)
	cancel()
	c.logger.V(8).Infof("etcd role %s revoke (%s %s)", name, key, rangeEnd)
	return res, err
}

// UserList 获取用户列表
func (c *ETCDClient) UserList() (*clientv3.AuthUserListResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.UserList(ctx)
	cancel()
	c.logger.V(8).Info("etcd user list")
	return res, err
}

// UserGet 获取用户信息
func (c *ETCDClient) UserGet(name string) (*clientv3.AuthUserGetResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.UserGet(ctx, name)
	cancel()
	c.logger.V(8).Infof("etcd user get %s", name)
	return res, err
}

// UserAdd 添加用户
func (c *ETCDClient) UserAdd(name string) (*clientv3.AuthUserAddResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.UserAddWithOptions(ctx, name, "", &clientv3.UserAddOptions{
		NoPassword: true,
	})
	cancel()
	c.logger.V(8).Infof("etcd user %s added", name)
	return res, err
}

// UserGrantRole 将角色权限赋予用户
func (c *ETCDClient) UserGrantRole(user, role string) (*clientv3.AuthUserGrantRoleResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.UserGrantRole(ctx, user, role)
	cancel()
	c.logger.V(8).Infof("etcd user %s grant role %s", user, role)
	return res, err
}

// UserRevokeRole 将角色权限从用户撤回
func (c *ETCDClient) UserRevokeRole(user, role string) (*clientv3.AuthUserRevokeRoleResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.UserRevokeRole(ctx, user, role)
	cancel()
	c.logger.V(8).Infof("etcd user %s revoke role %s", user, role)
	return res, err
}

func (c *ETCDClient) AuthEnable() (*clientv3.AuthEnableResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.AuthEnable(ctx)
	cancel()
	c.logger.V(8).Info("etcd auth enable")
	return res, err
}

func (c *ETCDClient) UserDelete(user string) (*clientv3.AuthUserDeleteResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.UserDelete(ctx, user)
	cancel()
	c.logger.V(8).Infof("etcd delete user %s", user)
	return res, err
}

func (c *ETCDClient) PurgePrefix(prefix string) (*clientv3.DeleteResponse, error) {
	ctx, cancel := c.defaultContext()
	res, err := c.client.Delete(ctx, prefix, clientv3.WithPrefix())
	cancel()
	c.logger.V(8).Infof("etcd purge prefix %s", prefix)
	return res, err
}
