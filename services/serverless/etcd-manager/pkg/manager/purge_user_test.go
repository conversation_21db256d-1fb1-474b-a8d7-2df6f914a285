package manager

import (
	"testing"

	"github.com/golang/mock/gomock"
	"go.etcd.io/etcd/clientv3"
	"go.etcd.io/etcd/etcdserver/api/v3rpc/rpctypes"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

func TestETCDClient_PurgeUser(t *testing.T) {
	type fields struct {
		ctrl          *gomock.Controller
		client        ClientV3
		certGenerator cfssl.CertGenerator
		caByte        []byte
		config        *config.ETCDClusterConfig
		logger        *logs.Logger
	}
	type args struct {
		req *PurgeUserReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserDelete(gomock.Any(), "user")
				client.EXPECT().RoleDelete(gomock.Any(), "user")
				client.EXPECT().Delete(gomock.Any(), "/prefix/user", gomock.AssignableToTypeOf(clientv3.WithPrefix()))
				return fields{
					ctrl:   ctrl,
					client: client,
					config: &config.ETCDClusterConfig{
						ETCDPrefix: "/prefix",
					},
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				req: &PurgeUserReq{
					Username:  "user",
					RequestID: "request-id",
				},
			},
		},
		{
			name: "user has been deleted already",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserDelete(gomock.Any(), "user").Return(nil, rpctypes.ErrUserNotFound)
				client.EXPECT().RoleDelete(gomock.Any(), "user")
				client.EXPECT().Delete(gomock.Any(), "/prefix/user", gomock.AssignableToTypeOf(clientv3.WithPrefix()))
				return fields{
					ctrl:   ctrl,
					client: client,
					config: &config.ETCDClusterConfig{
						ETCDPrefix: "/prefix",
					},
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				req: &PurgeUserReq{
					Username:  "user",
					RequestID: "request-id",
				},
			},
		},
		{
			name: "user and role has been deleted already",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserDelete(gomock.Any(), "user").Return(nil, rpctypes.ErrUserNotFound)
				client.EXPECT().RoleDelete(gomock.Any(), "user").Return(nil, rpctypes.ErrRoleNotFound)
				client.EXPECT().Delete(gomock.Any(), "/prefix/user", gomock.AssignableToTypeOf(clientv3.WithPrefix()))
				return fields{
					ctrl:   ctrl,
					client: client,
					config: &config.ETCDClusterConfig{
						ETCDPrefix: "/prefix",
					},
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				req: &PurgeUserReq{
					Username:  "user",
					RequestID: "request-id",
				},
			},
		},
		{
			name: "user, role and key range has been deleted already",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserDelete(gomock.Any(), "user").Return(nil, rpctypes.ErrUserNotFound)
				client.EXPECT().RoleDelete(gomock.Any(), "user").Return(nil, rpctypes.ErrRoleNotFound)
				client.EXPECT().Delete(gomock.Any(), "/prefix/user", gomock.AssignableToTypeOf(clientv3.WithPrefix())).Return(nil, rpctypes.ErrKeyNotFound)
				return fields{
					ctrl:   ctrl,
					client: client,
					config: &config.ETCDClusterConfig{
						ETCDPrefix: "/prefix",
					},
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				req: &PurgeUserReq{
					Username:  "user",
					RequestID: "request-id",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client:        tt.fields.client,
				certGenerator: tt.fields.certGenerator,
				caByte:        tt.fields.caByte,
				config:        tt.fields.config,
				logger:        tt.fields.logger,
			}
			if err := c.PurgeUser(tt.args.req); (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.PurgeUser() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
