package manager

import (
	"io/ioutil"
	"os"
	"path/filepath"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"go.etcd.io/etcd/clientv3"
	"gotest.tools/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

func TestETCDClient_RoleList(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	tests := []struct {
		name    string
		fields  fields
		want    *clientv3.AuthRoleListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleList(gomock.Any())
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.RoleList()
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.RoleList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.RoleList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_RoleGet(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthRoleGetResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleGet(gomock.Any(), "user").Return(nil, nil)
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name: "user",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.RoleGet(tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.RoleGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.RoleGet() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_RoleAdd(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthRoleAddResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleAdd(gomock.Any(), "role")
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name: "role",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.RoleAdd(tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.RoleAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.RoleAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_RoleDelete(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthRoleDeleteResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleDelete(gomock.Any(), "role")
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name: "role",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.RoleDelete(tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.RoleDelete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.RoleDelete() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_RoleGrantPermission(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name     string
		key      string
		rangeEnd string
		permType PermissionType
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthRoleGrantPermissionResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleGrantPermission(gomock.Any(), "role", "sub", clientv3.GetPrefixRangeEnd("sub"), clientv3.PermissionType(clientv3.PermReadWrite))
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name:     "role",
				key:      "sub",
				rangeEnd: clientv3.GetPrefixRangeEnd("sub"),
				permType: PermReadWrite,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.RoleGrantPermission(tt.args.name, tt.args.key, tt.args.rangeEnd, tt.args.permType)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.RoleGrantPermission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.RoleGrantPermission() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_RoleRevokePermission(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name     string
		key      string
		rangeEnd string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthRoleRevokePermissionResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleRevokePermission(gomock.Any(), "role", "sub", clientv3.GetPrefixRangeEnd("sub"))
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name:     "role",
				key:      "sub",
				rangeEnd: clientv3.GetPrefixRangeEnd("sub"),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.RoleRevokePermission(tt.args.name, tt.args.key, tt.args.rangeEnd)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.RoleRevokePermission() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.RoleRevokePermission() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_UserList(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	tests := []struct {
		name    string
		fields  fields
		want    *clientv3.AuthUserListResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserList(gomock.Any())
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.UserList()
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.UserList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.UserList() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_UserGet(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthUserGetResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserGet(gomock.Any(), "user")
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name: "user",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.UserGet(tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.UserGet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.UserGet() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_UserAdd(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		name string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthUserAddResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserAddWithOptions(gomock.Any(), "user", "", &clientv3.UserAddOptions{
					NoPassword: true,
				})
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				name: "user",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.UserAdd(tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.UserAdd() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.UserAdd() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_UserGrantRole(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		user string
		role string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthUserGrantRoleResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserGrantRole(gomock.Any(), "user", "role")
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				user: "user",
				role: "role",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.UserGrantRole(tt.args.user, tt.args.role)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.UserGrantRole() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.UserGrantRole() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_UserRevokeRole(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		user string
		role string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthUserRevokeRoleResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserRevokeRole(gomock.Any(), "user", "role")
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				user: "user",
				role: "role",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.UserRevokeRole(tt.args.user, tt.args.role)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.UserRevokeRole() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.UserRevokeRole() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_AuthEnable(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	tests := []struct {
		name    string
		fields  fields
		want    *clientv3.AuthEnableResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().AuthEnable(gomock.Any())
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.AuthEnable()
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.AuthEnable() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.AuthEnable() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_UserDelete(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		user string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.AuthUserDeleteResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserDelete(gomock.Any(), "user")
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				user: "user",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.UserDelete(tt.args.user)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.UserDelete() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("ETCDClient.UserDelete() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestETCDClient_PurgePrefix(t *testing.T) {
	type fields struct {
		ctrl   *gomock.Controller
		client ClientV3
		caByte []byte
		config *config.ETCDClusterConfig
		logger *logs.Logger
	}
	type args struct {
		prefix string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *clientv3.DeleteResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().Delete(gomock.Any(), "sub", gomock.AssignableToTypeOf(clientv3.WithPrefix()))
				return fields{
					ctrl:   ctrl,
					client: client,
				}
			}(),
			args: args{
				prefix: "sub",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client: tt.fields.client,
				caByte: tt.fields.caByte,
				config: tt.fields.config,
				logger: tt.fields.logger,
			}
			got, err := c.PurgePrefix(tt.args.prefix)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.PurgePrefix() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestNewETCDClient(t *testing.T) {
	tmpPath, err := ioutil.TempDir("", "caroot-*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpPath)

	files := map[string]string{
		"ca.pem": `-----BEGIN CERTIFICATE-----
MIIDnjCCAoagAwIBAgIUSXFea6p1QCArYU1ql16DGYoRBNQwDQYJKoZIhvcNAQEL
BQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl
aUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG
A1UEAxMFanBhYXMwHhcNMjIwNzEyMDkxODAwWhcNMzIwNzA5MDkxODAwWjBnMQsw
CQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO
MAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq
cGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKvSIfc+/C6F7lAd
1wrftUKWUy7cdwfVD3Y7pewK2/qa/x/lLsg4sEpPnsk7KZTIzziJQ0Us4GDO2Zsw
ogj6hjMqkl8gNn1BhPbXGLSmXEWp6bZTu6cH7/O5LELxzWOgpBDq+gFFQh0VH1rm
3Th5yuhBXByitAD//wYVNjRoCuiVhxgSsqrHWkAP3pc3UN1wOM73S0gXVVJIgbLY
+9vs+c5+GXJlJeG9wLAFbc2vDI0eS3ENtAScK1LrDwwq/mZIXbw9eFg5NFsZvEkV
gyOoCzn4xoDeywK+K2nChziQ7TsJPtFW5dULWovyTHgFEAQT9VhAy89ghzWrGpzk
iOTiXT8CAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w
HQYDVR0OBBYEFKtQd1xuDT90CZWgmg0smiKOi/QEMA0GCSqGSIb3DQEBCwUAA4IB
AQCJN2FxC2/Yid+OfGWgWUKe4eozpmV4vUT3E6gLQfmn0/dRf9f0wqg6UR64QBk1
+B5G4c120K/8IFacw2esDEEkPcRf/u7kOBu33Uj28N3TdYqf6tBFd5NrIhC+fJgO
NvcKwlHhIqjauVWzLyOtuwTWr0SVKylL2ABon0sh8rraBWPeS8NRdOCQWSVNIePb
r6d83T0F0U8cEVdAbwH5eu1ycB9OrfxAnwYNdPMqxlYJiHNkSBITUVrZgE+I4GU8
xEW1p4wBNohas9orj8EgsELX0MKE6Er/rvPxG1LAeCMrbSM8lgo5qtNqwy7xWTXy
Mv5c0YqV7usbkhUnejlsPKjD
-----END CERTIFICATE-----`,
		"ca-key.pem": `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
		"ca-config.json": `{
  "signing": {
    "default": {
      "expiry": "87600h"
    },
    "profiles": {
      "jpaas": {
        "usages": [
            "signing",
            "key encipherment",
            "server auth",
            "client auth"
        ],
        "expiry": "87600h"
      }
    }
  }
}`,
	}

	for filename, content := range files {
		if err := ioutil.WriteFile(filepath.Join(tmpPath, filename), []byte(content), 0644); err != nil {
			t.Fatalf("write file %s: %v", filename, err)
		}
	}

	type args struct {
		cfg config.ETCDClusterConfig
	}
	tests := []struct {
		name    string
		args    args
		want    *ETCDClient
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				cfg: config.ETCDClusterConfig{
					Name:            "test",
					UserEndpoints:   []string{"***********"},
					ETCDPrefix:      "/prefix",
					CACertFile:      filepath.Join(tmpPath, "ca.pem"),
					CAKeyFile:       filepath.Join(tmpPath, "ca-key.pem"),
					RootUsername:    "root",
					RootCertFile:    filepath.Join(tmpPath, "ca.pem"),
					RootCertKeyFile: filepath.Join(tmpPath, "ca-key.pem"),
					UserCAConfig:    filepath.Join(tmpPath, "ca-config.json"),
					TrustedCAFile:   filepath.Join(tmpPath, "ca.pem"),
					UserProfile:     "client",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewETCDClient(tt.args.cfg)
			assert.ErrorContains(t, err, "new etcd client failed")
		})
	}
}
