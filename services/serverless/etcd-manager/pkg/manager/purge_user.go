package manager

import (
	"errors"
	"path"

	"go.etcd.io/etcd/etcdserver/api/v3rpc/rpctypes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

type PurgeUserReq struct {
	Username  string   `json:"username"`
	Endpoints []string `json:"endpoints"`

	RequestID string       `json:"-"`
	logger    *logs.Logger `json:"-"`
}

func NewPurgeUserReq(username string, endpoints []string, requestID string) *PurgeUserReq {
	return &PurgeUserReq{
		Username:  username,
		Endpoints: endpoints,
		RequestID: requestID,
	}
}

func (m *manager) PurgeUser(req *PurgeUserReq) error {
	c := m.findETCD(req.Endpoints)
	if c == nil {
		return errors.New("etcd cluster not found")
	}

	return c.PurgeUser(req)
}

// findETCD 根据endpoints确认需要删除的用户所在的etcd集群
// TODO: 现在只返回第一个集群
func (m *manager) findETCD([]string) *ETCDClient {
	return m.etcdClients[0]
}

// DeleteUser cleans up user, role and date within corresponding prefix.
func (c *ETCDClient) PurgeUser(req *PurgeUserReq) error {
	req.logger = c.logger.WithField("user", req.Username).WithField("requestID", req.RequestID)
	req.logger.Info("start to purge user")

	req.logger.Info("start to delete user")
	delUserResp, err := c.UserDelete(req.Username)
	if err != nil {
		req.logger.Errorf("delete user %s err=%v, resp=%+v", req.Username, err, delUserResp)
		if err != rpctypes.ErrUserNotFound {
			return err
		}
	}

	req.logger.Info("start to delete role")
	delRoleResp, err := c.RoleDelete(req.Username)
	if err != nil {
		req.logger.Errorf("delete role %s err=%v, resp=%v", req.Username, err, delRoleResp)
		if err != rpctypes.ErrRoleNotFound {
			return err
		}
	}

	prefix := path.Join(c.config.ETCDPrefix, req.Username)
	req.logger.Infof("start to purge prefix %s", prefix)
	purgePrefixResp, err := c.PurgePrefix(prefix)
	if err != nil {
		req.logger.Errorf("purge prefix %s err=%v, resp=%v", prefix, err, purgePrefixResp)
		if err != rpctypes.ErrKeyNotFound {
			return err
		}
	}

	req.logger.Info("purge user successfully")
	return nil
}
