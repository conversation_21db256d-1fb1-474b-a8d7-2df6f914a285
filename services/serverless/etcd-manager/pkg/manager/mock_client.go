// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/manager (interfaces: ClientV3)

// Package manager is a generated GoMock package.
package manager

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	clientv3 "go.etcd.io/etcd/clientv3"
)

// MockClientV3 is a mock of ClientV3 interface.
type MockClientV3 struct {
	ctrl     *gomock.Controller
	recorder *MockClientV3MockRecorder
}

// MockClientV3MockRecorder is the mock recorder for MockClientV3.
type MockClientV3MockRecorder struct {
	mock *MockClientV3
}

// NewMockClientV3 creates a new mock instance.
func NewMockClientV3(ctrl *gomock.Controller) *MockClientV3 {
	mock := &MockClientV3{ctrl: ctrl}
	mock.recorder = &MockClientV3MockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockClientV3) EXPECT() *MockClientV3MockRecorder {
	return m.recorder
}

// AuthEnable mocks base method.
func (m *MockClientV3) AuthEnable(arg0 context.Context) (*clientv3.AuthEnableResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthEnable", arg0)
	ret0, _ := ret[0].(*clientv3.AuthEnableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AuthEnable indicates an expected call of AuthEnable.
func (mr *MockClientV3MockRecorder) AuthEnable(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthEnable", reflect.TypeOf((*MockClientV3)(nil).AuthEnable), arg0)
}

// Close mocks base method.
func (m *MockClientV3) Close() error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Close")
	ret0, _ := ret[0].(error)
	return ret0
}

// Close indicates an expected call of Close.
func (mr *MockClientV3MockRecorder) Close() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Close", reflect.TypeOf((*MockClientV3)(nil).Close))
}

// Delete mocks base method.
func (m *MockClientV3) Delete(arg0 context.Context, arg1 string, arg2 ...clientv3.OpOption) (*clientv3.DeleteResponse, error) {
	m.ctrl.T.Helper()
	varargs := []interface{}{arg0, arg1}
	for _, a := range arg2 {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(*clientv3.DeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Delete indicates an expected call of Delete.
func (mr *MockClientV3MockRecorder) Delete(arg0, arg1 interface{}, arg2 ...interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]interface{}{arg0, arg1}, arg2...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockClientV3)(nil).Delete), varargs...)
}

// RoleAdd mocks base method.
func (m *MockClientV3) RoleAdd(arg0 context.Context, arg1 string) (*clientv3.AuthRoleAddResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoleAdd", arg0, arg1)
	ret0, _ := ret[0].(*clientv3.AuthRoleAddResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoleAdd indicates an expected call of RoleAdd.
func (mr *MockClientV3MockRecorder) RoleAdd(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoleAdd", reflect.TypeOf((*MockClientV3)(nil).RoleAdd), arg0, arg1)
}

// RoleDelete mocks base method.
func (m *MockClientV3) RoleDelete(arg0 context.Context, arg1 string) (*clientv3.AuthRoleDeleteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoleDelete", arg0, arg1)
	ret0, _ := ret[0].(*clientv3.AuthRoleDeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoleDelete indicates an expected call of RoleDelete.
func (mr *MockClientV3MockRecorder) RoleDelete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoleDelete", reflect.TypeOf((*MockClientV3)(nil).RoleDelete), arg0, arg1)
}

// RoleGet mocks base method.
func (m *MockClientV3) RoleGet(arg0 context.Context, arg1 string) (*clientv3.AuthRoleGetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoleGet", arg0, arg1)
	ret0, _ := ret[0].(*clientv3.AuthRoleGetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoleGet indicates an expected call of RoleGet.
func (mr *MockClientV3MockRecorder) RoleGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoleGet", reflect.TypeOf((*MockClientV3)(nil).RoleGet), arg0, arg1)
}

// RoleGrantPermission mocks base method.
func (m *MockClientV3) RoleGrantPermission(arg0 context.Context, arg1, arg2, arg3 string, arg4 clientv3.PermissionType) (*clientv3.AuthRoleGrantPermissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoleGrantPermission", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(*clientv3.AuthRoleGrantPermissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoleGrantPermission indicates an expected call of RoleGrantPermission.
func (mr *MockClientV3MockRecorder) RoleGrantPermission(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoleGrantPermission", reflect.TypeOf((*MockClientV3)(nil).RoleGrantPermission), arg0, arg1, arg2, arg3, arg4)
}

// RoleList mocks base method.
func (m *MockClientV3) RoleList(arg0 context.Context) (*clientv3.AuthRoleListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoleList", arg0)
	ret0, _ := ret[0].(*clientv3.AuthRoleListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoleList indicates an expected call of RoleList.
func (mr *MockClientV3MockRecorder) RoleList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoleList", reflect.TypeOf((*MockClientV3)(nil).RoleList), arg0)
}

// RoleRevokePermission mocks base method.
func (m *MockClientV3) RoleRevokePermission(arg0 context.Context, arg1, arg2, arg3 string) (*clientv3.AuthRoleRevokePermissionResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RoleRevokePermission", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*clientv3.AuthRoleRevokePermissionResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RoleRevokePermission indicates an expected call of RoleRevokePermission.
func (mr *MockClientV3MockRecorder) RoleRevokePermission(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RoleRevokePermission", reflect.TypeOf((*MockClientV3)(nil).RoleRevokePermission), arg0, arg1, arg2, arg3)
}

// UserAddWithOptions mocks base method.
func (m *MockClientV3) UserAddWithOptions(arg0 context.Context, arg1, arg2 string, arg3 *clientv3.UserAddOptions) (*clientv3.AuthUserAddResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserAddWithOptions", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(*clientv3.AuthUserAddResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserAddWithOptions indicates an expected call of UserAddWithOptions.
func (mr *MockClientV3MockRecorder) UserAddWithOptions(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserAddWithOptions", reflect.TypeOf((*MockClientV3)(nil).UserAddWithOptions), arg0, arg1, arg2, arg3)
}

// UserDelete mocks base method.
func (m *MockClientV3) UserDelete(arg0 context.Context, arg1 string) (*clientv3.AuthUserDeleteResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserDelete", arg0, arg1)
	ret0, _ := ret[0].(*clientv3.AuthUserDeleteResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserDelete indicates an expected call of UserDelete.
func (mr *MockClientV3MockRecorder) UserDelete(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserDelete", reflect.TypeOf((*MockClientV3)(nil).UserDelete), arg0, arg1)
}

// UserGet mocks base method.
func (m *MockClientV3) UserGet(arg0 context.Context, arg1 string) (*clientv3.AuthUserGetResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserGet", arg0, arg1)
	ret0, _ := ret[0].(*clientv3.AuthUserGetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserGet indicates an expected call of UserGet.
func (mr *MockClientV3MockRecorder) UserGet(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserGet", reflect.TypeOf((*MockClientV3)(nil).UserGet), arg0, arg1)
}

// UserGrantRole mocks base method.
func (m *MockClientV3) UserGrantRole(arg0 context.Context, arg1, arg2 string) (*clientv3.AuthUserGrantRoleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserGrantRole", arg0, arg1, arg2)
	ret0, _ := ret[0].(*clientv3.AuthUserGrantRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserGrantRole indicates an expected call of UserGrantRole.
func (mr *MockClientV3MockRecorder) UserGrantRole(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserGrantRole", reflect.TypeOf((*MockClientV3)(nil).UserGrantRole), arg0, arg1, arg2)
}

// UserList mocks base method.
func (m *MockClientV3) UserList(arg0 context.Context) (*clientv3.AuthUserListResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserList", arg0)
	ret0, _ := ret[0].(*clientv3.AuthUserListResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserList indicates an expected call of UserList.
func (mr *MockClientV3MockRecorder) UserList(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserList", reflect.TypeOf((*MockClientV3)(nil).UserList), arg0)
}

// UserRevokeRole mocks base method.
func (m *MockClientV3) UserRevokeRole(arg0 context.Context, arg1, arg2 string) (*clientv3.AuthUserRevokeRoleResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UserRevokeRole", arg0, arg1, arg2)
	ret0, _ := ret[0].(*clientv3.AuthUserRevokeRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UserRevokeRole indicates an expected call of UserRevokeRole.
func (mr *MockClientV3MockRecorder) UserRevokeRole(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UserRevokeRole", reflect.TypeOf((*MockClientV3)(nil).UserRevokeRole), arg0, arg1, arg2)
}
