package manager

import (
	"testing"

	"github.com/golang/mock/gomock"
	"go.etcd.io/etcd/auth/authpb"
	"go.etcd.io/etcd/clientv3"
	"go.etcd.io/etcd/etcdserver/api/v3rpc/rpctypes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

func TestETCDClient_AddRolePerm(t *testing.T) {
	type fields struct {
		ctrl          *gomock.Controller
		client        ClientV3
		certGenerator cfssl.CertGenerator
		caByte        []byte
		config        *config.ETCDClusterConfig
		logger        *logs.Logger
	}
	type args struct {
		roleName string
		prefix   string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleGet(gomock.Any(), "role").Return(nil, rpctypes.ErrRoleNotFound)
				client.EXPECT().RoleAdd(gomock.Any(), "role").Return(nil, nil)
				client.EXPECT().RoleGrantPermission(gomock.Any(), "role", "/sub", clientv3.GetPrefixRangeEnd("/sub"), clientv3.PermissionType(clientv3.PermReadWrite)).Return(nil, nil)

				return fields{
					ctrl:   ctrl,
					client: client,
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				roleName: "role",
				prefix:   "/sub",
			},
		},
		{
			name: "role exists with right perm",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleGet(gomock.Any(), "role").Return(&clientv3.AuthRoleGetResponse{
					Perm: []*authpb.Permission{
						{
							Key:      []byte("/sub"),
							RangeEnd: []byte(clientv3.GetPrefixRangeEnd("/sub")),
							PermType: clientv3.PermReadWrite,
						},
					},
				}, nil)

				return fields{
					ctrl:   ctrl,
					client: client,
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				roleName: "role",
				prefix:   "/sub",
			},
		},
		{
			name: "role exists with wrong perm",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().RoleGet(gomock.Any(), "role").Return(&clientv3.AuthRoleGetResponse{
					Perm: []*authpb.Permission{
						{
							Key:      []byte("/suc"),
							RangeEnd: []byte(clientv3.GetPrefixRangeEnd("/suc")),
							PermType: clientv3.PermReadWrite,
						},
					},
				}, nil)
				client.EXPECT().RoleRevokePermission(gomock.Any(), "role", "/suc", clientv3.GetPrefixRangeEnd("/suc"))
				client.EXPECT().RoleGrantPermission(gomock.Any(), "role", "/sub", clientv3.GetPrefixRangeEnd("/sub"), clientv3.PermissionType(clientv3.PermReadWrite)).Return(nil, nil)

				return fields{
					ctrl:   ctrl,
					client: client,
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				roleName: "role",
				prefix:   "/sub",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client:        tt.fields.client,
				certGenerator: tt.fields.certGenerator,
				caByte:        tt.fields.caByte,
				config:        tt.fields.config,
				logger:        tt.fields.logger,
			}
			if err := c.AddRolePerm(tt.args.roleName, tt.args.prefix); (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.AddRolePerm() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestETCDClient_AddUserRole(t *testing.T) {
	type fields struct {
		ctrl          *gomock.Controller
		client        ClientV3
		certGenerator cfssl.CertGenerator
		caByte        []byte
		config        *config.ETCDClusterConfig
		logger        *logs.Logger
	}
	type args struct {
		userName string
		roleName string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserGet(gomock.Any(), "user").Return(nil, rpctypes.ErrUserNotFound)
				client.EXPECT().UserAddWithOptions(gomock.Any(), "user", "", &clientv3.UserAddOptions{
					NoPassword: true,
				})
				client.EXPECT().UserGrantRole(gomock.Any(), "user", "role")
				return fields{
					ctrl:   ctrl,
					client: client,
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				userName: "user",
				roleName: "role",
			},
		},
		{
			name: "user exists with right role",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserGet(gomock.Any(), "user").Return(&clientv3.AuthUserGetResponse{
					Roles: []string{"role"},
				}, nil)
				return fields{
					ctrl:   ctrl,
					client: client,
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				userName: "user",
				roleName: "role",
			},
		},
		{
			name: "user exists with wrong role",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)

				client.EXPECT().UserGet(gomock.Any(), "user").Return(&clientv3.AuthUserGetResponse{
					Roles: []string{"rol"},
				}, nil)
				client.EXPECT().UserRevokeRole(gomock.Any(), "user", "rol")
				client.EXPECT().UserGrantRole(gomock.Any(), "user", "role")
				return fields{
					ctrl:   ctrl,
					client: client,
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				userName: "user",
				roleName: "role",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			c := &ETCDClient{
				client:        tt.fields.client,
				certGenerator: tt.fields.certGenerator,
				caByte:        tt.fields.caByte,
				config:        tt.fields.config,
				logger:        tt.fields.logger,
			}
			if err := c.AddUserRole(tt.args.userName, tt.args.roleName); (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.AddUserRole() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
