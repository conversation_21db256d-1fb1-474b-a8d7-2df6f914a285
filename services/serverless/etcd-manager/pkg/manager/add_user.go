package manager

import (
	"errors"
	"io/ioutil"
	"os"
	"path"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

type AddUserReq struct {
	Username string `json:"username"`

	RequestID string       `json:"-"`
	logger    *logs.Logger `json:"-"`
}

type AddUserRes struct {
	Cert      string   `json:"cert"`
	Key       string   `json:"key"`
	Csr       string   `json:"csr"`
	CA        string   `json:"ca"`
	Prefix    string   `json:"prefix"`    // 用户独占的etcd数据前缀
	Endpoints []string `json:"endpoints"` // 用户需要连接的etcd endpoints
}

// NewAddUserReq 为请求生成默认值
func NewAddUserReq() *AddUserReq {
	return &AddUserReq{}
}

func (m *manager) AddUser(req *AddUserReq) (res *AddUserRes, err error) {
	c := m.chooseEtcd()
	if c == nil {
		err = errors.New("no suitable etcd cluster")
		return
	}

	return c.AddUser(req)

}

// chooseEtcd 为用户选出当前最适合的etcd集群。
// TODO: 现在只返回第一个集群
func (m *manager) chooseEtcd() *ETCDClient {
	return m.etcdClients[0]
}

func (c *ETCDClient) AddUser(req *AddUserReq) (res *AddUserRes, err error) {
	req.logger = c.logger.WithField("user", req.Username).WithField("requestID", req.RequestID)

	prefix, err := c.updateAuth(req)
	if err != nil {
		return
	}

	data, err := c.genCert(req)
	if err != nil {
		return
	}

	res = &AddUserRes{
		Cert:      string(data.Cert),
		Key:       string(data.Key),
		Csr:       string(data.Csr),
		CA:        string(c.caByte),
		Prefix:    prefix,
		Endpoints: c.config.UserEndpoints,
	}

	// 证书写到本地磁盘，当前用来测试
	if c.config.OutputDir != "" {
		saveCert(c.config.OutputDir, req, res)
	}

	return
}

// genCert 为用户生成etcd客户端证书
func (c *ETCDClient) genCert(req *AddUserReq) (*cfssl.CertData, error) {
	// 为用户生成证书
	certReq := &cfssl.CertReq{
		User:       req.Username,
		Hosts:      c.config.UserEndpoints,
		CAFile:     c.config.CACertFile,
		CAKeyFile:  c.config.CAKeyFile,
		ConfigFile: c.config.UserCAConfig,
		Profile:    c.config.UserProfile,
	}
	return c.certGenerator.GenCert(certReq)
}

// updateAuth 为用户创建etcd用户，并为其赋予对应前缀的的读写权限
func (c *ETCDClient) updateAuth(req *AddUserReq) (prefix string, err error) {
	// 添加角色
	pfx := path.Join(c.config.ETCDPrefix, req.Username)
	err = c.AddRolePerm(req.Username, pfx)
	if err != nil {
		req.logger.Errorf("add role %s prefix %s failed: %s", req.Username, pfx, err.Error())
		return
	}

	// 添加用户
	err = c.AddUserRole(req.Username /*user name*/, req.Username /*role name*/)
	if err != nil {
		req.logger.Errorf("add user %s failed: %s", req.Username, err.Error())
		return
	}

	prefix = pfx
	return
}

// saveCert 将证书保持到磁盘。在目录，为每个用户创建一个子目录，写入client.csr, client.pem, client-key.pem, ca.pem四个文件。
func saveCert(dir string, req *AddUserReq, data *AddUserRes) error {
	// 为用户创建子目录
	userDir := path.Join(dir, req.Username)
	err := ensureDir(userDir)
	if err != nil {
		req.logger.Errorf("mkdir %s failed: %s", userDir, err.Error())
		return err
	}

	// 将文件分别写入
	writeFile(userDir, "client.csr", data.Csr)
	writeFile(userDir, "client.pem", data.Cert)
	writeFile(userDir, "client-key.pem", data.Key)
	writeFile(userDir, "ca.pem", data.CA)

	return nil
}

func writeFile(dir, fileName string, s string) error {
	fullPath := path.Join(dir, fileName)
	err := ioutil.WriteFile(fullPath, []byte(s), 0644)
	if err != nil {
		logs.Errorf("write data to %s failed: %s", fullPath, err.Error())
	}
	return err
}

func ensureDir(dir string) error {
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		return os.MkdirAll(dir, os.ModePerm)
	}
	return nil
}
