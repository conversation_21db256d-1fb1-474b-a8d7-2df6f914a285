package manager

import (
	"testing"

	"github.com/golang/mock/gomock"
	"go.etcd.io/etcd/clientv3"
	"go.etcd.io/etcd/etcdserver/api/v3rpc/rpctypes"
	"gotest.tools/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

func TestETCDClient_AddUser(t *testing.T) {
	type fields struct {
		ctrl          *gomock.Controller
		client        ClientV3
		certGenerator cfssl.CertGenerator
		caByte        []byte
		config        *config.ETCDClusterConfig
		logger        *logs.Logger
	}
	type args struct {
		req *AddUserReq
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantRes *AddUserRes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				client := NewMockClientV3(ctrl)
				certGen := cfssl.NewMockCertGenerator(ctrl)

				client.EXPECT().RoleGet(gomock.Any(), "user").Return(nil, rpctypes.ErrRoleNotFound)
				client.EXPECT().RoleAdd(gomock.Any(), "user").Return(nil, nil)
				client.EXPECT().RoleGrantPermission(gomock.Any(), "user", "/prefix/user", clientv3.GetPrefixRangeEnd("/prefix/user"), clientv3.PermissionType(clientv3.PermReadWrite)).Return(nil, nil)

				client.EXPECT().UserGet(gomock.Any(), "user").Return(nil, rpctypes.ErrUserNotFound)
				client.EXPECT().UserAddWithOptions(gomock.Any(), "user", "", &clientv3.UserAddOptions{
					NoPassword: true,
				})
				client.EXPECT().UserGrantRole(gomock.Any(), "user", "user")

				certGen.EXPECT().GenCert(gomock.Any()).Return(&cfssl.CertData{
					Cert: []byte("cert"),
					Key:  []byte("key"),
					Csr:  []byte("csr"),
				}, nil)

				return fields{
					ctrl:          ctrl,
					client:        client,
					certGenerator: certGen,
					caByte:        []byte("ca"),
					config: &config.ETCDClusterConfig{
						ETCDPrefix:    "/prefix",
						UserEndpoints: []string{"10.10.10.10"},
					},
					logger: logs.NewLogger().WithField("cluster", "test"),
				}
			}(),
			args: args{
				req: &AddUserReq{
					Username:  "user",
					RequestID: "request-id",
				},
			},
			wantRes: &AddUserRes{
				Cert:      "cert",
				Key:       "key",
				Csr:       "csr",
				CA:        "ca",
				Prefix:    "/prefix/user",
				Endpoints: []string{"10.10.10.10"},
			},
		},
	}
	for _, tt := range tests {
		if tt.fields.ctrl != nil {
			defer tt.fields.ctrl.Finish()
		}
		t.Run(tt.name, func(t *testing.T) {
			c := &ETCDClient{
				client:        tt.fields.client,
				certGenerator: tt.fields.certGenerator,
				caByte:        tt.fields.caByte,
				config:        tt.fields.config,
				logger:        tt.fields.logger,
			}
			gotRes, err := c.AddUser(tt.args.req)
			if (err != nil) != tt.wantErr {
				t.Errorf("ETCDClient.AddUser() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, gotRes, tt.wantRes)
		})
	}
}
