package manager

import (
	"go.etcd.io/etcd/clientv3"
	"go.etcd.io/etcd/etcdserver/api/v3rpc/rpctypes"
)

// AddRolePerm 增加etcd角色，并为角色添加读写权限的前缀
func (c *ETCDClient) AddRolePerm(roleName, prefix string) error {
	roleRes, err := c.RoleGet(roleName)
	if err != nil {
		if err != rpctypes.ErrRoleNotFound {
			return err
		}

		// role不存在，直接添加就行
		c.logger.Infof("add role %s", roleName)
		_, err = c.RoleAdd(roleName)
		if err != nil {
			c.logger.Errorf("add role %s failed: %s", roleName, err.Error())
			return err
		}
	} else {
		// role存在，检查已有的权限
		permExisted := false
		for _, perm := range roleRes.Perm {
			key := string(perm.Key)
			rangeEnd := string(perm.RangeEnd)

			if key != prefix || rangeEnd != clientv3.GetPrefixRangeEnd(prefix) ||
				perm.PermType != clientv3.PermReadWrite {
				// 删除不符合的
				c.logger.Infof("role %s revoke permission (%s %s)", roleName, key, rangeEnd)
				_, err = c.RoleRevokePermission(roleName, key, rangeEnd)
				if err != nil {
					c.logger.Errorf("add role %s failed: %s", roleName, err.Error())
					return err
				}
			} else {
				permExisted = true
			}
		}

		if permExisted {
			c.logger.Infof("role %s permission %s existed, so skip to grant", roleName, prefix)
			return nil
		}
	}

	// 添加数据权限
	_, err = c.RoleGrantPermission(roleName, prefix, "", PermReadWrite)
	if err != nil {
		c.logger.Errorf("role %s grant permission failed: %s", roleName, err.Error())
		return err
	}

	return nil
}

// AddUserRole 添加用户，并与角色绑定
func (c *ETCDClient) AddUserRole(userName, roleName string) error {
	userRes, err := c.UserGet(userName)
	if err != nil {
		if err != rpctypes.ErrUserNotFound {
			return err
		}

		// user不存在，直接添加就行
		c.logger.Infof("adding user %s", userName)
		_, err = c.UserAdd(userName)
		if err != nil {
			c.logger.Errorf("add user %s failed: %s", userName, err.Error())
			return err
		}
		c.logger.Infof("added user %s", userName)
	} else {
		// user存在，检查角色
		roleExisted := false
		for _, role := range userRes.Roles {
			if role != roleName {
				_, err = c.UserRevokeRole(userName, role)
				if err != nil {
					return err
				}
			} else {
				roleExisted = true
			}
		}

		if roleExisted {
			c.logger.Infof("user %s granted role %s, so skip to grant", userName, roleName)
			return nil
		}
	}

	_, err = c.UserGrantRole(userName, roleName)
	if err != nil {
		c.logger.Errorf("user %s grant role %s failed: %s", userName, roleName, err.Error())
		return err
	}

	return nil
}
