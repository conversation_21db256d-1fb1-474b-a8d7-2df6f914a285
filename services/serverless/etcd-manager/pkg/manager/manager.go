package manager

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/cmd/etcd-manager/config"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/util/logs"
)

var _ Manager = (*manager)(nil)

//go:generate mockgen -destination ./mock_manager.go -package manager icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/manager Manager
type Manager interface {
	AddUser(req *AddUserReq) (res *AddUserRes, err error)
	PurgeUser(req *PurgeUserReq) error
}

type manager struct {
	etcdClients []*ETCDClient // 需要支持多集群，暂时先只支持一个
}

func NewManager(ctx context.Context, cfg config.Config) *manager {
	var etcdClients []*ETCDClient
	for _, cluster := range cfg.Clusters {
		etcdClient, err := startEtcdClient(cluster)
		if err != nil {
			logs.Fatalf("start etcd client for cluster %s failed: %v", etcdClient.config.Name, err)
		}
		logs.Infof("add etcd cluster %s", etcdClient.config.Name)
		etcdClients = append(etcdClients, etcdClient)
	}
	return &manager{
		etcdClients: etcdClients,
	}
}

// startEtcdClient
func startEtcdClient(cfg config.ETCDClusterConfig) (etcdClient *ETCDClient, err error) {
	if etcdClient, err = NewETCDClient(cfg); err != nil {
		logs.Errorf("fail to initialize etcd client with %+v: %v", cfg, err)
		return
	}

	if err = etcdClient.AddRolePerm("root", ""); err != nil {
		logs.Errorf("add role root failed: %s", err.Error())
		return
	}

	if err = etcdClient.AddUserRole("root", "root"); err != nil {
		logs.Errorf("check user root failed: %s", err.Error())
		return
	}

	if _, err = etcdClient.AuthEnable(); err != nil {
		logs.Errorf("enable auth failed: %s", err.Error())
		return
	}

	etcdClient.Restart()

	return
}
