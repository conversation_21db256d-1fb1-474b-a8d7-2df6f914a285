package cfssl

import (
	"github.com/cloudflare/cfssl/cli"
	"github.com/cloudflare/cfssl/cli/genkey"
	"github.com/cloudflare/cfssl/cli/sign"
	"github.com/cloudflare/cfssl/config"
	"github.com/cloudflare/cfssl/csr"
	"github.com/cloudflare/cfssl/signer"
)

var _ CertGenerator = (*certGenerator)(nil)

type CertData struct {
	Cert []byte
	Key  []byte
	Csr  []byte
}

type CertReq struct {
	User       string
	Hosts      []string
	CAFile     string
	CAKeyFile  string
	ConfigFile string
	Profile    string
}

//go:generate mockgen -destination ./mock_cfssl.go -package cfssl icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl CertGenerator
type CertGenerator interface {
	GenCert(certReq *CertReq) (data *CertData, err error)
}

type certGenerator struct{}

func NewCertGenerator() *certGenerator {
	return &certGenerator{}
}

func (*certGenerator) GenCert(certReq *CertReq) (data *CertData, err error) {
	g := &csr.Generator{Validator: genkey.Validator}
	req := NewCsr(certReq.User, certReq.Hosts)

	var key, csrBytes []byte
	csrBytes, key, err = g.ProcessRequest(req)
	if err != nil {
		key = nil
		return
	}

	c := &cli.Config{
		CAFile:     certReq.CAFile,
		CAKeyFile:  certReq.CAKeyFile,
		ConfigFile: certReq.ConfigFile,
		Profile:    certReq.Profile,
	}
	c.CFG, err = config.LoadFile(c.ConfigFile)
	if err != nil {
		return
	}
	s, err := sign.SignerFromConfig(*c)
	if err != nil {
		return
	}

	var cert []byte
	signReq := signer.SignRequest{
		Request: string(csrBytes),
		Hosts:   signer.SplitHosts(c.Hostname),
		Profile: c.Profile,
		Label:   c.Label,
	}

	if c.CRL != "" {
		signReq.CRLOverride = c.CRL
	}
	cert, err = s.Sign(signReq)
	if err != nil {
		return
	}

	data = &CertData{
		Cert: cert,
		Key:  key,
		Csr:  csrBytes,
	}
	return
}
