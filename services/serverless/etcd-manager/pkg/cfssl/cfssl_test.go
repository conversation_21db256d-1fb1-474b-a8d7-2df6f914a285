package cfssl

import (
	"io/ioutil"
	"os"
	"path/filepath"
	"testing"
)

func Test_certGenerator_GenCert(t *testing.T) {
	tmpPath, err := ioutil.TempDir("", "caroot-*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.RemoveAll(tmpPath)

	files := map[string]string{
		filepath.Join(tmpPath, "ca.pem"): `-----BEGIN CERTIFICATE-----
MIIDnjCCAoagAwIBAgIUSXFea6p1QCArYU1ql16DGYoRBNQwDQYJKoZIhvcNAQEL
BQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl
aUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG
A1UEAxMFanBhYXMwHhcNMjIwNzEyMDkxODAwWhcNMzIwNzA5MDkxODAwWjBnMQsw
CQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO
MAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq
cGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKvSIfc+/C6F7lAd
1wrftUKWUy7cdwfVD3Y7pewK2/qa/x/lLsg4sEpPnsk7KZTIzziJQ0Us4GDO2Zsw
ogj6hjMqkl8gNn1BhPbXGLSmXEWp6bZTu6cH7/O5LELxzWOgpBDq+gFFQh0VH1rm
3Th5yuhBXByitAD//wYVNjRoCuiVhxgSsqrHWkAP3pc3UN1wOM73S0gXVVJIgbLY
+9vs+c5+GXJlJeG9wLAFbc2vDI0eS3ENtAScK1LrDwwq/mZIXbw9eFg5NFsZvEkV
gyOoCzn4xoDeywK+K2nChziQ7TsJPtFW5dULWovyTHgFEAQT9VhAy89ghzWrGpzk
iOTiXT8CAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w
HQYDVR0OBBYEFKtQd1xuDT90CZWgmg0smiKOi/QEMA0GCSqGSIb3DQEBCwUAA4IB
AQCJN2FxC2/Yid+OfGWgWUKe4eozpmV4vUT3E6gLQfmn0/dRf9f0wqg6UR64QBk1
+B5G4c120K/8IFacw2esDEEkPcRf/u7kOBu33Uj28N3TdYqf6tBFd5NrIhC+fJgO
NvcKwlHhIqjauVWzLyOtuwTWr0SVKylL2ABon0sh8rraBWPeS8NRdOCQWSVNIePb
r6d83T0F0U8cEVdAbwH5eu1ycB9OrfxAnwYNdPMqxlYJiHNkSBITUVrZgE+I4GU8
xEW1p4wBNohas9orj8EgsELX0MKE6Er/rvPxG1LAeCMrbSM8lgo5qtNqwy7xWTXy
Mv5c0YqV7usbkhUnejlsPKjD
-----END CERTIFICATE-----`,
		filepath.Join(tmpPath, "ca-key.pem"): `******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`,
		filepath.Join(tmpPath, "ca-config.json"): `{
  "signing": {
    "default": {
      "expiry": "87600h"
    },
    "profiles": {
      "jpaas": {
        "usages": [
            "signing",
            "key encipherment",
            "server auth",
            "client auth"
        ],
        "expiry": "87600h"
      }
    }
  }
}`,
	}

	for filename, content := range files {
		if err := ioutil.WriteFile(filename, []byte(content), 0644); err != nil {
			t.Fatalf("write file %s: %v", filename, err)
		}
	}
	type args struct {
		certReq *CertReq
	}
	tests := []struct {
		name    string
		c       *certGenerator
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				certReq: &CertReq{
					User: "user",
					Hosts: []string{
						"127.0.0.1",
						"*************",
						"************",
						"cce-etcd.bj.baidubce.com",
					},
					CAFile:     filepath.Join(tmpPath, "ca.pem"),
					CAKeyFile:  filepath.Join(tmpPath, "ca-key.pem"),
					ConfigFile: filepath.Join(tmpPath, "ca-config.json"),
					Profile:    "jpaas",
				},
			},
		},
		{
			name: "cert file not found",
			args: args{
				certReq: &CertReq{
					User: "user",
					Hosts: []string{
						"127.0.0.1",
						"*************",
						"************",
						"cce-etcd.bj.baidubce.com",
					},
					CAFile:     filepath.Join(tmpPath, "ca-1.pem"),
					CAKeyFile:  filepath.Join(tmpPath, "ca-key.pem"),
					ConfigFile: filepath.Join(tmpPath, "ca-config.json"),
					Profile:    "jpaas",
				},
			},
			wantErr: true,
		},
		{
			name: "config file not found",
			args: args{
				certReq: &CertReq{
					User: "user",
					Hosts: []string{
						"127.0.0.1",
						"*************",
						"************",
						"cce-etcd.bj.baidubce.com",
					},
					CAFile:     filepath.Join(tmpPath, "ca.pem"),
					CAKeyFile:  filepath.Join(tmpPath, "ca-key.pem"),
					ConfigFile: filepath.Join(tmpPath, "ca-config-1.json"),
					Profile:    "jpaas",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &certGenerator{}
			_, err := c.GenCert(tt.args.certReq)
			if (err != nil) != tt.wantErr {
				t.Errorf("certGenerator.GenCert() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

		})
	}
}
