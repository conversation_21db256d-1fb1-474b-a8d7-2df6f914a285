package cfssl

import (
	"reflect"
	"testing"

	"github.com/cloudflare/cfssl/csr"
)

func TestNewCsr(t *testing.T) {
	type args struct {
		user  string
		hosts []string
	}
	tests := []struct {
		name string
		args args
		want *csr.CertificateRequest
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				user: "user",
				hosts: []string{
					"127.0.0.1",
					"*************",
					"************",
					"cce-etcd.bj.baidubce.com",
				},
			},
			want: &csr.CertificateRequest{
				CN: "user",
				Names: []csr.Name{
					{
						C:  "CN",
						ST: "BeiJing",
						L:  "BeiJing",
						O:  "k8s",
						OU: "4Paradigm",
					},
				},
				Hosts: []string{
					"127.0.0.1",
					"*************",
					"************",
					"cce-etcd.bj.baidubce.com",
				},
				KeyRequest: csr.NewKeyRequest(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := NewCsr(tt.args.user, tt.args.hosts); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("NewCsr() = %v, want %v", got, tt.want)
			}
		})
	}
}
