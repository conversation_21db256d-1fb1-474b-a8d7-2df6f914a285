// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/etcd-manager/pkg/cfssl (interfaces: CertGenerator)

// Package cfssl is a generated GoMock package.
package cfssl

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
)

// MockCertGenerator is a mock of CertGenerator interface.
type MockCertGenerator struct {
	ctrl     *gomock.Controller
	recorder *MockCertGeneratorMockRecorder
}

// MockCertGeneratorMockRecorder is the mock recorder for MockCertGenerator.
type MockCertGeneratorMockRecorder struct {
	mock *MockCertGenerator
}

// NewMockCertGenerator creates a new mock instance.
func NewMockCertGenerator(ctrl *gomock.Controller) *MockCertGenerator {
	mock := &MockCertGenerator{ctrl: ctrl}
	mock.recorder = &MockCertGeneratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockCertGenerator) EXPECT() *MockCertGeneratorMockRecorder {
	return m.recorder
}

// GenCert mocks base method.
func (m *MockCertGenerator) GenCert(arg0 *CertReq) (*CertData, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenCert", arg0)
	ret0, _ := ret[0].(*CertData)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenCert indicates an expected call of GenCert.
func (mr *MockCertGeneratorMockRecorder) GenCert(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenCert", reflect.TypeOf((*MockCertGenerator)(nil).GenCert), arg0)
}
