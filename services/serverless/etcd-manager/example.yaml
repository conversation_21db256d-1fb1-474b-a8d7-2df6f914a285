port: 8381
authEndpoint: *************:8400
mysqlConn: cce_service_w:6fS_5tvWNA@tcp(************:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true
clusters:
- name: etcd0
  caCertFile: /home/<USER>/serverless-etcd/ssl/ca.pem
  caKeyFile: /home/<USER>/serverless-etcd/ssl/ca-key.pem
  endpoints:
  - *************:8379
  - *************:8379
  - *************:8379
  etcdPrefix: /gztest/serverless
  insecureSkipTLSVerify: false
  outputDir: _output/cert
  rootCertFile: /home/<USER>/serverless-etcd/ssl/root.pem
  rootCertKeyFile: /home/<USER>/serverless-etcd/ssl/root-key.pem
  rootUsername: root
  trustedCAFile: /home/<USER>/serverless-etcd/ssl/ca.pem
  userCAConfig: /home/<USER>/serverless-etcd/ssl/ca-config.json
  userEndpoints:
  - **********:8379
  userProfile: client
