# 示例配置

## 依赖模块

首先保证以下组件已安装：

- etcd
- etcdctl
- cfssl
- cfssljson

## 启动服务
按下面的步骤启动服务。

进入sample/ca，生成CA证书
```
cfssl gencert -initca ca-csr.json | cfssljson -bare ca
```

进入sample/server，生成服务器证书
```
cfssl gencert -ca=../ca/ca.pem -ca-key=../ca/ca-key.pem -config=ca-config.json -profile=server -hostname=127.0.0.1 server.json | cfssljson -bare server
```

在sample/server中启动etcd服务
```
etcd --client-cert-auth=true --trusted-ca-file=../ca/ca.pem --cert-file=./server.pem --key-file=./server-key.pem \
--initial-cluster-state=new --advertise-client-urls https://0.0.0.0:2379 --listen-client-urls https://0.0.0.0:2379
```

生成服务所使用的etcd集群配置
```
go run sample/create_yaml.go
```

进入工程目录编译服务
```
make
```

启动程序
```
_output/bin/etcd-manager --etcd-cluster-conf example.yaml --alsologtostderr -v10
```
到此etcd服务已被添加root role和root user，并启动验证。

## 添加用户

接下来为etcd新增用户：
```
curl -X "POST" "http://127.0.0.1:8080/api/v1/AddUser" \
     -H 'Content-Type: application/json; charset=utf-8' \
     -d $'{
  "userName": "use1"
}'
```
返回的数据中有cert,key,csr,ca四个字段。
可以看到_output/cert/user1目录下生成了client.csr、client.pem、client-key.pem、ca.pem四个文件。

## 使用证书
现在可以进入_output/cert/user1来使用证书了。

可以以当前身份查看用户本身的权限：
```
etcdctl --cert=client.pem --key=client-key.pem --cacert=ca.pem role get user1
```
输出：
```
Role user1
KV Read:
	[data/user1, data/user2) (prefix data/user1)
KV Write:
	[data/user1, data/user2) (prefix data/user1)
```

读写用户所属权限的数据：
```
etcdctl --cert=client.pem --key=client-key.pem --cacert=ca.pem put data/user1/a 1
etcdctl --cert=client.pem --key=client-key.pem --cacert=ca.pem get data/user1/a
```

读写不属于用户权限的数据：
```
etcdctl --cert=client.pem --key=client-key.pem --cacert=ca.pem get data/user2/a
```
输出：
```
{"level":"warn","ts":"2020-05-13T11:29:56.596+0800","caller":"clientv3/retry_interceptor.go:62","msg":"retrying of unary invoker failed","target":"endpoint://client-d9309e3b-2168-40ea-afce-fb18dd56b63b/127.0.0.1:2379","attempt":0,"error":"rpc error: code = PermissionDenied desc = etcdserver: permission denied"}
Error: etcdserver: permission denied
```

## 生成镜像
生成etcd和etcd-manager的镜像，推送到仓库中，用来配置测试环境。

为etcd的证书绑定EIP，注意将hostname参数修改为申请的EIP
```
cd sample/server
cfssl gencert -ca=../ca/ca.pem -ca-key=../ca/ca-key.pem -config=ca-config.json -profile=server -hostname=************* server.json | cfssljson -bare server
```

推送etcd镜像
```
docker build -f sample/etcd.Dockerfile -t hub.baidubce.com/test-bci/etcd sample
docker push hub.baidubce.com/test-bci/etcd
```

先将etcd和etcd-manager的可执行文件放入sample/bin中
编译linux下的etcd-manager
```
KUN_BUILD_PLATFORMS="linux/amd64" make
cp _output/local/bin/linux/amd64/etcd-manager sample/bin
```

修改sample/test.yaml中的endpoints和user-endpoints，使之与EIP一致。

推送etcd-manager
```
docker build -f sample/etcd-manager.Dockerfile -t hub.baidubce.com/test-bci/etcd-manager sample
docker push hub.baidubce.com/test-bci/etcd-manager
```
