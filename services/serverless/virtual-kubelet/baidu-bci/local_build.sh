#!/bin/bash

set -eu

echo "execute local_build.sh ..."

# 生成动态的镜像标签
IMAGE_REPOSITORY="registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet"
IMAGE_TAG="bci-$(date +"%Y%m%d-%H%M%S")"
GOOS=linux GOARCH=amd64 CGO_ENABLED=0 go build -o ./virtual-kubelet.bin  -ldflags '-extldflags "-static"' ./cmd/virtual-kubelet

echo "build image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ..."
docker build -t ${IMAGE_REPOSITORY}:${IMAGE_TAG} .
echo "build image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ok"

echo "push image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ..."
docker push ${IMAGE_REPOSITORY}:${IMAGE_TAG}
echo "push image ${IMAGE_REPOSITORY}:${IMAGE_TAG} ok"

echo "execute local_build.sh ok"