/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  ws_helper
 * @Version: 1.0.0
 * @Date: 2019/12/13 下午1:54
 */
package baidu_bci

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/node/api"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	"nhooyr.io/websocket"
)

// The Websocket subprotocol "channel.k8s.io" prepends each binary message with a byte indicating
// the channel number (zero indexed) the message was sent on. Messages in both directions should
// prefix their messages with this channel byte. When used for remote execution, the channel numbers
// are by convention defined to match the POSIX file-descriptors assigned to STDIN, STDOUT, and STDERR
// (0, 1, and 2). No other conversion is performed on the raw subprotocol - writes are sent as they
// are received by the server.
//
// Example client session:
//
//    CONNECT http://server.com with subprotocol "channel.k8s.io"
//    WRITE []byte{0, 102, 111, 111, 10} # send "foo\n" on channel 0 (STDIN)
//    READ  []byte{1, 10}                # receive "\n" on channel 1 (STDOUT)
//    CLOSE
//

type wsMsgType byte

const (
	stdinMsgType     wsMsgType = 0
	stdoutMsgType    wsMsgType = 1
	stderrMsgType    wsMsgType = 2
	outOfBandMsgType wsMsgType = 3
	resizeMsgType    wsMsgType = 4
	invalidMsgType   wsMsgType = 5

	bciPingMsgType wsMsgType = 20
	bciPongMsgType wsMsgType = 21
)

var (
	errInvalidMsgType   = fmt.Errorf("invalid msg type")
	errInvalidMsgLength = fmt.Errorf("invalid msg length")
	errPongMsgReceived  = fmt.Errorf("pong msg received")
)

type websocketConn interface {
	Close()
	Write(context.Context, []byte) error
	Ping(context.Context, []byte) error
	Read(ctx context.Context) ([]byte, error)
	URL() string
	Resize(ctx context.Context, size api.TermSize) error
}

type bciWebsocketConn struct {
	url  string
	conn *websocket.Conn
}

func newBciWebsocketConn(ctx context.Context, wssURL string) (websocketConn, error) {
	ctx, span := trace.StartSpan(ctx, "newBciWebsocketConn")
	ctx = span.WithField(ctx, "wss url", wssURL)
	defer span.End()

	header := http.Header{}
	token, err := getTokenFromWSSURL(wssURL)
	if err != nil {
		return nil, err
	}
	if token != "" {
		header.Add("Cookie", fmt.Sprintf("token=%s", token))
	}
	c, resp, err := websocket.Dial(ctx, wssURL, &websocket.DialOptions{
		HTTPClient:   nil,
		HTTPHeader:   header,
		Subprotocols: []string{"binary"},
	})
	if resp != nil {
		log.G(ctx).Debugf("wss request: %+v", resp.Request)
		log.G(ctx).Debugf("wss response: %+v", resp)
	}
	if err != nil {
		log.G(ctx).WithError(err).Errorf("failed wss connection")
		return nil, err
	}
	return &bciWebsocketConn{url: wssURL, conn: c}, nil
}

func (c *bciWebsocketConn) Close() {
	c.conn.Close(websocket.StatusNormalClosure, "client close connection")
}

func (c *bciWebsocketConn) Write(ctx context.Context, data []byte) error {
	encoded := wsEncode(data, stdinMsgType)
	return c.conn.Write(ctx, websocket.MessageBinary, encoded)
}

func (c *bciWebsocketConn) Ping(ctx context.Context, pingMsg []byte) error {
	if len(pingMsg) > 0 {
		return c.conn.Write(ctx, websocket.MessageBinary, pingMsg)
	}
	return c.conn.Ping(ctx)
}

func (c *bciWebsocketConn) Resize(ctx context.Context, size api.TermSize) error {
	if size.Height == 0 || size.Width == 0 {
		return fmt.Errorf("size.Height and size.Width must be greater than zero: %+v", size)
	}
	data, err := json.Marshal(size)
	if err != nil {
		return fmt.Errorf("marshal resize msg %+v error: %v", size, err)
	}
	encoded := wsEncode(data, resizeMsgType)
	return c.conn.Write(ctx, websocket.MessageBinary, encoded)
}

func (c *bciWebsocketConn) Read(ctx context.Context) ([]byte, error) {
	ctx, span := trace.StartSpan(ctx, "bciWebsocketConn.Read")
	ctx = span.WithField(ctx, "wss url", c.URL())
	defer span.End()

	_, data, err := c.conn.Read(ctx)
	if err != nil {
		return nil, err
	}

	decoded, msgType, err := wsDecode(data)
	if msgType == outOfBandMsgType {
		log.G(ctx).Infof("recv out of band data: %x", decoded)
		return nil, nil
	}
	return decoded, err
}

func (c *bciWebsocketConn) URL() string {
	return c.url
}

func getTokenFromWSSURL(wssURL string) (string, error) {
	parsedUrl, err := url.Parse(wssURL)
	if err != nil {
		return "", err
	}
	return parsedUrl.Query().Get("token"), nil
}

func wsEncode(raw []byte, t wsMsgType) []byte {
	return append([]byte{byte(t)}, raw...)
}

func wsDecode(encoded []byte) ([]byte, wsMsgType, error) {
	if len(encoded) < 1 {
		return nil, invalidMsgType, errInvalidMsgLength
	}

	switch wsMsgType(encoded[0]) {
	case stdinMsgType:
		return encoded[1:], stdinMsgType, nil
	case stdoutMsgType:
		return encoded[1:], stdoutMsgType, nil
	case stderrMsgType:
		return encoded[1:], stderrMsgType, nil
	case outOfBandMsgType:
		return encoded[1:], outOfBandMsgType, nil
	case resizeMsgType:
		return encoded[1:], resizeMsgType, nil
	case bciPingMsgType:
		return encoded[1:], bciPingMsgType, nil
	case bciPongMsgType:
		return encoded[1:], bciPongMsgType, errPongMsgReceived
	default:
		return encoded, invalidMsgType, errInvalidMsgType
	}
}
