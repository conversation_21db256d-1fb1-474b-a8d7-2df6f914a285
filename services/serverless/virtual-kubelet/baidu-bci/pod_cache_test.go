package baidu_bci

import (
	"context"

	"fmt"
	"sort"
	"strconv"
	"testing"
	"time"

	"code.cloudfoundry.org/clock"
	"code.cloudfoundry.org/clock/fakeclock"
	"github.com/golang/mock/gomock"
	"github.com/sirupsen/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	logruslogger "github.com/virtual-kubelet/virtual-kubelet/log/logrus"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	"github.com/virtual-kubelet/virtual-kubelet/trace/opencensus"
	"gotest.tools/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci/v2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/manager"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/userstorage"
)

func init() {
	logger := logrus.StandardLogger()
	log.L = logruslogger.FromLogrus(logrus.NewEntry(logger))
	trace.T = opencensus.Adapter{}
}

func generateBCIPod(name, cceID, podID string, labels []bci.PodLabel, createdAt time.Time) *bci.DescribePodResponse {
	return &bci.DescribePodResponse{
		Pod: &bci.Pod{
			Name:        name,
			PodID:       podID,
			VCPU:        1,
			MemoryInGB:  2,
			Labels:      labels,
			CCEID:       cceID,
			CreatedTime: createdAt,
			UpdatedTime: createdAt,
		},
		Containers: []bci.Container{
			{
				Name:               "container01",
				ContainerImageInfo: getContainerImageInfo("nginx"),
				CPUInCore:          1,
				MemoryInGB:         2,
				Status: &bci.ContainerStatus{
					CurrentState: &bci.ContainerState{
						State:              bci.ContainerStateStringRunning,
						ContainerStartTime: createdAt,
					},
				},
			},
		},
	}
}

func podMapToList(input map[string]*bci.DescribePodResponse) (output []*bci.Pod) {
	for _, p := range input {
		output = append(output, p.Pod)
	}
	return
}

func bpodsToV1Pods(input map[string]*bci.DescribePodResponse) (output []*v1.Pod) {
	for _, p := range input {
		v1p, err := bciPodDetailToPod(context.TODO(), p)
		if err != nil {
			panic(err)
		}
		output = append(output, v1p)
	}
	return
}

func TestTicker(t *testing.T) {
	now := time.Now()
	t.Log("now is", now)
	testClock := fakeclock.NewFakeClock(now)
	ticker := testClock.NewTicker(time.Second)
	defer ticker.Stop()
	timer := testClock.NewTimer(time.Second * 2)
	defer timer.Stop()

	done := make(chan struct{}, 20)

	go func() {
		for i := 0; i < 10; i++ {
			t.Log("wait ticker")
			now := <-ticker.C()
			t.Log("ticker:", now)
			done <- struct{}{}
		}
	}()
	go func() {
		t.Log("wait timer")
		now := <-timer.C()
		t.Log("timer:", now)
		done <- struct{}{}
	}()

	for i := 0; i < 10; i++ {
		t.Logf("i=%d", i)
		testClock.IncrementBySeconds(1)
		<-time.After(time.Millisecond)
	}

	for i := 0; i < 11; i++ {
		<-done
	}
}

func TestPodCacheForSingleUser(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	clusterID := "cce-unittest"
	userStorage, _ := userstorage.NewEmptyStorage()

	provider := &BCIProvider{
		nodeName:    "bci-virtual-kubelet",
		userStorage: userStorage,
		clusterID:   clusterID,
	}
	podNamespace := "default"
	bpods := make(map[string]*bci.DescribePodResponse)
	podIDs := make(map[string]string)
	now := testClock.Now()
	for i := 0; i < 5; i++ {
		podName := fmt.Sprintf("pod-%d", i)
		podID := fmt.Sprintf("pod-%dpadding", i)
		labels := []bci.PodLabel{
			{LabelKey: UIDLabelKey, LabelValue: podName + "-uid"},
			{LabelKey: PodNameLabelKey, LabelValue: podName},
			{LabelKey: NamespaceLabelKey, LabelValue: podNamespace},
			{LabelKey: NodeNameLabelKey, LabelValue: provider.nodeName},
			{LabelKey: ClusterIDLabelKey, LabelValue: provider.clusterID},
			{LabelKey: CreationTimestampLabelKey, LabelValue: strconv.FormatInt(now.Unix(), 10)},
		}
		bpods[podID] = generateBCIPod(podNamespace+"-"+podName, clusterID, podID, labels, now)
		podIDs[podNamespace+"/"+podName] = podID
	}

	pc := &podCache{
		clock:               testClock,
		syncInterval:        time.Second,
		regularSyncInterval: time.Minute,
		pods:                make(map[string]*cachedPod),
		podIDs:              make(map[string]string),
		creatingPods:        make(map[string]*creatingPod),
		reqTokens:           make(chan struct{}, 128),
		provider:            provider,
		syncEnd:             make(chan struct{}, 1),
		notify:              func(*v1.Pod) {},
		subnetLongToShort:   make(map[string]string),
		subnetUsages:        make(map[string]*BCIResources),
		toNotFoundTimeout:   30 * time.Minute,
	}

	wantListOption := bci.NewMarkerListOption(clusterID, "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypeCCEID, clusterID))

	// var podID, podName, podUID string
	// var updatedPodNameAndUIDs [][]string
	// var wantPods []*v1.Pod

	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()

	type fields struct {
		ctrl *gomock.Controller

		setupProvider func(*BCIProvider)

		pre    func(*testing.T)
		assert func(*testing.T)
	}
	tests := []struct {
		name   string
		fields fields
	}{
		// TODO: Add test cases.
		{
			name: "start pod cache and then get all pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						for id, p := range bpods {
							bciClient.EXPECT().DescribePod(gomock.Any(), id, nil).Return(p, nil)
						}

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						pc.Start(ctx)
					},
					assert: func(t *testing.T) {
						v1Pods, err := pc.GetPods(ctx)
						assert.NilError(t, err, "podCache.GetPods")
						wantPods := bpodsToV1Pods(bpods)
						sort.Slice(v1Pods, func(i, j int) bool { return v1Pods[i].Name < v1Pods[j].Name })
						sort.Slice(wantPods, func(i, j int) bool { return wantPods[i].Name < wantPods[j].Name })
						assert.DeepEqual(t, v1Pods, wantPods)
						pod0 := v1Pods[0]
						gotPod0, err := pc.getUIDPod(ctx, pod0.GetNamespace(), pod0.GetName(), string(pod0.GetUID()))
						assert.NilError(t, err, "podCache.GetPod 0")
						assert.DeepEqual(t, gotPod0, pod0)
					},
				}
			}(),
		},
		/**
		{
			name: "first sync, no pod update occurs, just list all pods and notify all pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPodsForLight(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						for _, bpod := range bpods {
							namespace := getBCILabelValue(bpod.Pod, NamespaceLabelKey)
							name := getBCILabelValue(bpod.Pod, PodNameLabelKey)
							uid := getBCILabelValue(bpod.Pod, UIDLabelKey)
							// GetPod from notify process for all pods listed in the first time.
							rm.EXPECT().GetPod(name, namespace).Return(newTestV1Pod(namespace, name, uid, "", "", 0, bci.PodStatusPending), nil)
						}

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second + 1)
						// Wait sync to end so mock controller can be finished.
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
					},
				}
			}(),
		},
		{
			name: "a new pod added",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						podID = "pod-newpaddi"
						podName = "pod-new"
						podUID = podName + "-uid"
						now = testClock.Now()

						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bpods[podID] = generateBCIPod(podNamespace+"-"+podName, clusterID, podID,
							[]bci.PodLabel{
								{LabelKey: UIDLabelKey, LabelValue: podUID},
								{LabelKey: PodNameLabelKey, LabelValue: podName},
								{LabelKey: NamespaceLabelKey, LabelValue: podNamespace},
								{LabelKey: NodeNameLabelKey, LabelValue: provider.nodeName},
								{LabelKey: ClusterIDLabelKey, LabelValue: provider.clusterID},
								{LabelKey: CreationTimestampLabelKey, LabelValue: strconv.FormatInt(now.Unix(), 10)},
							}, now)
						podIDs[pc.getCacheKey(podNamespace, podName, podUID)] = podID
						t.Logf("add pod %s/%s/%s to bci at %s", podNamespace, podName, podUID, now.String())

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						bciClient.EXPECT().DescribePod(gomock.Any(), podID, nil).Return(bpods[podID], nil)
						rm.EXPECT().GetPod(podName, podNamespace).Return(newTestV1Pod(podNamespace, podName,
							getBCILabelValue(bpods[podID].Pod, UIDLabelKey), "", "", 0, bci.PodStatusPending), nil)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// Wait sync to end so mock controller can be finished.
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						// check the new pod is in podCache
						newPod, err := pc.getUIDPod(ctx, podNamespace, podName, podUID)
						assert.NilError(t, err, "get new pod "+podName)
						wantNewPod, _ := bciPodDetailToPod(ctx, bpods[podID])
						assert.DeepEqual(t, newPod, wantNewPod)
					},
				}
			}(),
		},
		{
			name: "a pod within pod cache is updated",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)
						for id, p := range bpods {
							podID = id
							podName = getBCILabelValue(p.Pod, PodNameLabelKey)
							podUID = getBCILabelValue(p.Pod, UIDLabelKey)
							t.Logf("update pod %s/%s in bpods", podNamespace, podName)
							p.Containers[0].Status.PreviousState = &bci.ContainerState{
								State:               bci.ContainerStateStringFailed,
								ExitCode:            3,
								ContainerStartTime:  p.Containers[0].Status.CurrentState.ContainerStartTime,
								ContainerFinishTime: testClock.Now(),
								DetailStatus:        "Exit 3",
							}
							p.Containers[0].Status.CurrentState.ContainerStartTime = testClock.Now().Add(50 * time.Millisecond)
							p.Containers[0].Status.RestartCount = 1
							p.UpdatedTime = testClock.Now().Add(100 * time.Millisecond)
							t.Logf("update pod %s/%s in bpods", podNamespace, podName)
							break
						}

						// next sync should update the pod above in podCache
						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						bciClient.EXPECT().DescribePod(gomock.Any(), podID, nil).Return(bpods[podID], nil)
						rm.EXPECT().GetPod(podName, podNamespace).Return(newTestV1Pod(podNamespace, podName,
							getBCILabelValue(bpods[podID].Pod, UIDLabelKey), "", "", 0, bci.PodStatusPending), nil)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// Wait sync to end so mock controller can be finished.
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						updatedPod, err := pc.getUIDPod(ctx, podNamespace, podName, podUID)
						assert.NilError(t, err, "get updated pod")
						assert.Equal(t, updatedPod.Status.ContainerStatuses[0].RestartCount, int32(1))
						assert.Equal(t, updatedPod.Status.ContainerStatuses[0].LastTerminationState.Terminated.ExitCode, int32(3))
					},
				}
			}(),
		},
		{
			name: "regular sync for the rest pods (except the last updated pod) and toNotFoundChecker is triggered",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						for id, p := range bpods {
							if id == podID {
								continue
							}
							bciClient.EXPECT().DescribePod(gomock.Any(), id, nil).Return(p, nil)
							rm.EXPECT().GetPod(getBCILabelValue(p.Pod, PodNameLabelKey), getBCILabelValue(p.Pod, NamespaceLabelKey)).Return(newTestV1Pod(
								getBCILabelValue(p.Pod, NamespaceLabelKey), getBCILabelValue(p.Pod, PodNameLabelKey), getBCILabelValue(p.Pod, UIDLabelKey),
								"", "", 0, bci.PodStatusPending), nil)
						}

						// Invoked by toNotFoundChecker.
						var v1Pods []*v1.Pod
						for _, bpod := range bpods {
							pod, err := bciPodDetailToPod(context.TODO(), bpod)
							if err != nil {
								t.Error(err)
							}
							v1Pods = append(v1Pods, pod)
						}
						rm.EXPECT().GetPods().Return(v1Pods)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Minute - time.Second + 1)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
					},
				}
			}(),
		},
		{
			name: "remove another pod (not the last updated one) in podCache",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						lastUpdatedPodID := podID
						var deletedBPod *bci.DescribePodResponse
						// remove another pod (not the last updated one) in podCache
						for id, p := range bpods {
							if id == podID {
								continue
							}
							podID = id
							podName = getBCILabelValue(p.Pod, PodNameLabelKey)
							podUID = getBCILabelValue(p.Pod, UIDLabelKey)
							t.Logf("delete pod %s/%s from bpods", podNamespace, podName)
							deletedBPod = bpods[id]
							delete(bpods, id)
							break
						}

						// next sync should remove the pod above from podCache, and also triggers regular sync for the last updated pod.
						bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil).AnyTimes()
						lastUpdated := bpods[lastUpdatedPodID]

						fmt.Println(lastUpdated)
						bciClient.EXPECT().DescribePod(gomock.Any(), lastUpdatedPodID, nil).Return(lastUpdated, nil)

						rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).
							Return(newTestV1Pod(getBCILabelValue(lastUpdated.Pod, NamespaceLabelKey), getBCILabelValue(lastUpdated.Pod, PodNameLabelKey),
								getBCILabelValue(lastUpdated.Pod, PodNameLabelKey)+"-uid", "", "", 0, bci.PodStatusPending), nil).Times(2)

						// pod removal triggers pod rescue, but pod is terminating
						k8sPod, _ := bciPodDetailToPod(ctx, deletedBPod)
						k8sPod.DeletionTimestamp = func() *metav1.Time {
							v1Time := metav1.NewTime(time.Now())
							return &v1Time
						}()
						provider.resourceManager = rm

						provider.podRescuer = NewMockPodRescuer(ctrl)
						bciClient.EXPECT().DescribePod(gomock.Any(), gomock.Any(), nil).Return(nil, fmt.Errorf("AccessNonOwnedResource"))
						// bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), nil).Return(&bci.ListPodsResponse{
						// 	MaxKeys:     bci.DefaultMaxKeys,
						// 	IsTruncated: false,
						// 	Result:      podMapToList(bpods),
						// }, fmt.Errorf("NotFound"))
						// rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).
						//	Return(newTestV1Pod(getBCILabelValue(lastUpdated.Pod, NamespaceLabelKey), getBCILabelValue(lastUpdated.Pod, PodNameLabelKey),
						//		getBCILabelValue(lastUpdated.Pod, PodNameLabelKey)+"-uid", "", "", 0, bci.PodStatusPending), nil)
						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						_, err := pc.getUIDPod(ctx, podNamespace, podName, podUID)
						assert.ErrorType(t, err, errdefs.IsNotFound)
					},
				}
			}(),
		},
		{
			name: "update all pods in bpods",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						for id, p := range bpods {
							podID = id
							podName = getBCILabelValue(p.Pod, PodNameLabelKey)
							podUID = getBCILabelValue(p.Pod, UIDLabelKey)
							updatedPodNameAndUIDs = append(updatedPodNameAndUIDs, []string{podName, podUID})
							t.Logf("update pod %s/%s in bpods", podNamespace, podName)
							p.Containers[0].Status.PreviousState = &bci.ContainerState{
								State:               bci.ContainerStateStringFailed,
								ExitCode:            255,
								ContainerStartTime:  p.Containers[0].Status.CurrentState.ContainerStartTime,
								ContainerFinishTime: testClock.Now(),
								DetailStatus:        "Exit 255",
							}
							p.Containers[0].Status.CurrentState.ContainerStartTime = testClock.Now().Add(50 * time.Millisecond)
							p.Containers[0].Status.RestartCount = 1
							p.UpdatedTime = testClock.Now().Add(100 * time.Millisecond)
							bciClient.EXPECT().DescribePod(gomock.Any(), podID, nil).Return(bpods[podID], nil)
							rm.EXPECT().GetPod(getBCILabelValue(p.Pod, PodNameLabelKey), getBCILabelValue(p.Pod, NamespaceLabelKey)).Return(newTestV1Pod(
								getBCILabelValue(p.Pod, NamespaceLabelKey), getBCILabelValue(p.Pod, PodNameLabelKey), getBCILabelValue(p.Pod, UIDLabelKey),
								"", "", 0, bci.PodStatusPending), nil)
						}

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						for _, podNameAndUID := range updatedPodNameAndUIDs {
							updatedPod, err := pc.getUIDPod(ctx, podNamespace, podNameAndUID[0], podNameAndUID[1])
							assert.NilError(t, err, "get updated pod")
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].RestartCount, int32(1))
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].LastTerminationState.Terminated.ExitCode, int32(255))
						}
					},
				}
			}(),
		},
		{
			name: "regular sync for all pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						for id, p := range bpods {
							bciClient.EXPECT().DescribePod(gomock.Any(), id, nil).Return(p, nil)
							rm.EXPECT().GetPod(getBCILabelValue(p.Pod, PodNameLabelKey), getBCILabelValue(p.Pod, NamespaceLabelKey)).Return(newTestV1Pod(
								getBCILabelValue(p.Pod, NamespaceLabelKey), getBCILabelValue(p.Pod, PodNameLabelKey), getBCILabelValue(p.Pod, UIDLabelKey),
								"", "", 0, bci.PodStatusPending), nil)
						}

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Minute + 1)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						for _, podNameAndUID := range updatedPodNameAndUIDs {
							updatedPod, err := pc.getUIDPod(ctx, podNamespace, podNameAndUID[0], podNameAndUID[1])
							assert.NilError(t, err, "get updated pod")
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].RestartCount, int32(1))
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].LastTerminationState.Terminated.ExitCode, int32(255))
						}
					},
				}
			}(),
		},
		{
			name: "error occurs in list pods case",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(nil, fmt.Errorf("some error occurs in list"))

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						for _, podNameAndUID := range updatedPodNameAndUIDs {
							updatedPod, err := pc.getUIDPod(ctx, podNamespace, podNameAndUID[0], podNameAndUID[1])
							assert.NilError(t, err, "get updated pod")
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].RestartCount, int32(1))
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].LastTerminationState.Terminated.ExitCode, int32(255))
						}
						v1Pods, err := pc.GetPods(ctx)
						assert.NilError(t, err, "podCache.GetPods")
						wantPods = bpodsToV1Pods(bpods)
						sort.Slice(v1Pods, func(i, j int) bool { return v1Pods[i].Name < v1Pods[j].Name })
						sort.Slice(wantPods, func(i, j int) bool { return wantPods[i].Name < wantPods[j].Name })
						assert.DeepEqual(t, v1Pods, wantPods)
					},
				}
			}(),
		},
		{
			name: "error occurs in describe pods case",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						for id, p := range bpods {
							podID = id
							podName = getBCILabelValue(p.Pod, PodNameLabelKey)
							t.Logf("update pod %s/%s in bpods", podNamespace, podName)
							p.Containers[0].Status.PreviousState = &bci.ContainerState{
								State:               bci.ContainerStateStringFailed,
								ExitCode:            3,
								ContainerStartTime:  p.Containers[0].Status.CurrentState.ContainerStartTime,
								ContainerFinishTime: testClock.Now(),
								DetailStatus:        "Exit 3",
							}
							p.Containers[0].Status.CurrentState.ContainerStartTime = testClock.Now().Add(50 * time.Millisecond)
							p.Containers[0].Status.RestartCount = 1
							p.UpdatedTime = testClock.Now().Add(100 * time.Millisecond)
							break
						}

						// next sync should try to update the pod above in podCache but failed due to error
						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)
						bciClient.EXPECT().DescribePod(gomock.Any(), podID, nil).Return(nil, fmt.Errorf("some error occurs in describe pod %s", podID))

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						// check the pods are still present as the last sync
						for _, podNameAndUID := range updatedPodNameAndUIDs {
							updatedPod, err := pc.getUIDPod(ctx, podNamespace, podNameAndUID[0], podNameAndUID[1])
							assert.NilError(t, err, "get updated pod")
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].RestartCount, int32(1))
							assert.Equal(t, updatedPod.Status.ContainerStatuses[0].LastTerminationState.Terminated.ExitCode, int32(255), podName)
						}
						v1Pods, err := pc.GetPods(ctx)
						assert.NilError(t, err, "podCache.GetPods")
						sort.Slice(v1Pods, func(i, j int) bool { return v1Pods[i].Name < v1Pods[j].Name })
						assert.DeepEqual(t, v1Pods, wantPods)
					},
				}
			}(),
		},
		{
			name: "remove all pods",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						removedPodsNum := len(bpods)
						for id, p := range bpods {
							podID = id
							podName = getBCILabelValue(p.Pod, PodNameLabelKey)
							t.Logf("delete pod %s/%s from bpods", podNamespace, podName)
							delete(bpods, id)
						}

						bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)

						bciClient.EXPECT().DescribePod(gomock.Any(), gomock.Any(), nil).Return(nil, errors.New("AccessNonOwnedResource")).Times(removedPodsNum)
						// bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), nil).Return(&bci.ListPodsResponse{
						// 	MaxKeys:     bci.DefaultMaxKeys,
						// 	IsTruncated: false,
						// 	Result:      podMapToList(bpods),
						// }, k8serror.NewNotFound(schema.GroupResource{Group: "v1", Resource: "Pods"}, "name")).Times(removedPodsNum)
						// The last pod would be rescued by podRescuer, but other pods not exist in k8s any more.
						rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).Times(removedPodsNum-1).Return(nil, k8serror.NewNotFound(schema.GroupResource{Group: "v1", Resource: "Pods"}, "name"))
						rm.EXPECT().GetPod(gomock.Any(), gomock.Any()).DoAndReturn(func(name, ns string) (*v1.Pod, error) {
							podName = name
							return &v1.Pod{
								TypeMeta: metav1.TypeMeta{
									Kind:       "Pod",
									APIVersion: "v1",
								},
								ObjectMeta: metav1.ObjectMeta{
									Name:              name,
									Namespace:         podNamespace,
									UID:               types.UID(name + "-uid"),
									CreationTimestamp: getMetaV1TimeFromString(t, testClock.Now().Add(-time.Minute).Format(time.RFC3339), time.RFC3339),
								},
								Spec: v1.PodSpec{
									Containers: []v1.Container{},
								},
								Status: v1.PodStatus{
									Phase: v1.PodPending,
								},
							}, nil
						})

						rescuer := NewMockPodRescuer(ctrl)
						rescuer.EXPECT().RescuePod(gomock.Any(), podNamespace, gomock.Any(), gomock.Any())

						provider.podRescuer = rescuer
						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						// wait sync to end
						<-pc.syncEnd
					},
					assert: func(*testing.T) {
						for _, podNameAndUID := range updatedPodNameAndUIDs {
							pod, err := pc.getUIDPod(ctx, podNamespace, podNameAndUID[0], podNameAndUID[1])
							t.Logf("get pod %s/%s from cache: %v, err: %v", podNamespace, podNameAndUID[0], pod, err)
							// assert.ErrorType(t, err, errdefs.IsNotFound)
							assert.ErrorContains(t, err, "can't find Pod")
						}
						// check subnet usage
						// assert.DeepEqual(t, pc.subnetLongToShort, wantSubnetLongToShort)
						// assert.DeepEqual(t, pc.subnetUsages, wantSubnetUsages)
					},
				}
			}(),
		},
		{
			name: "sync without draining syncEnd chan",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				return fields{
					ctrl: ctrl,
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)
						rm := manager.NewMockResourceManager(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), wantListOption, nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil).Times(3)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					pre: func(*testing.T) {
						testClock.Increment(time.Second)
						<-time.After(time.Millisecond)
						testClock.Increment(time.Second)
						<-time.After(time.Millisecond)
						testClock.Increment(time.Second)
						<-time.After(time.Millisecond)
						assert.Equal(t, len(pc.syncEnd), 1)
						<-time.After(50 * time.Millisecond)
					},
					assert: func(*testing.T) {},
				}
			}(),
		},
		*/
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			if tt.fields.setupProvider != nil {
				tt.fields.setupProvider(pc.provider)
			}
			if tt.fields.pre != nil {
				tt.fields.pre(t)
			}

			tt.fields.assert(t)
		})
	}
}

func TestJitter(t *testing.T) {
	base := 5 * time.Minute
	for i := 0; i < 10; i++ {
		got := Jitter(base, 0.2)
		assert.Assert(t, got >= base && got <= 6*time.Minute)
	}

	got := Jitter(base, 0)
	assert.Equal(t, got, base)
}

func Test_podCache_toNotFoundChecker(t *testing.T) {
	type fields struct {
		ctrl *gomock.Controller

		pods              map[string]*cachedPod
		provider          *BCIProvider
		notify            func(*v1.Pod)
		toNotFoundTimeout time.Duration
		setupProvider     func(*BCIProvider)
	}
	ctx, cancel := context.WithCancel(context.TODO())
	defer cancel()

	testClock := fakeclock.NewFakeClock(time.Now())
	ns := "default"
	clusterID := "cce-xxxxxxxx"
	defaultPC := &podCache{
		clock: testClock,
	}

	tests := []struct {
		name   string
		fields fields
	}{
		// All test cases.
		{
			name: "pending pod and time out is not reached yet",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-10*time.Minute).Unix(), bci.PodStatusPending),
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "pending pod and found in pod cache",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending),
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})
				pods := map[string]*cachedPod{
					defaultPC.getCacheKey(ns, "test-0", "test-uid-0"): {
						v1Pod: newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending),
					},
				}

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
						clusterID:       clusterID,
					},
					pods:              pods,
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "set pod status to not found",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				testPod := newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending)

				annotations := make(map[string]string)
				annotations["bci.virtual-kubelet.io/pod-id"] = "test-0"
				testPod.Annotations = annotations
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					testPod,
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
						clusterID:       clusterID,
						createV2:        true,
					},
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)

						bciV2Client.EXPECT().DescribePod(gomock.Any(), "test-0", nil).Return(&bci.DescribePodResponse{}, nil)
						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					notify: func(got *v1.Pod) {
						assert.DeepEqual(t, got, defaultPC.toNotFound(ctx, testPod))
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "set pod status to max retries exceeded",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				testPod := newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending)
				testPod.Status.Reason = podStatusReasonProviderFailed
				testPod.Status.Message = "some error occurs"

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					testPod,
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
					},
					notify: func(got *v1.Pod) {
						assert.DeepEqual(t, got, defaultPC.toMaxRetriesExceeded(ctx, testPod))
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
		{
			name: "set pod status to not found",
			fields: func() fields {
				ctrl := gomock.NewController(t)

				testPod := newTestV1Pod(ns, "test-0", "test-uid-0", "bci-vk", clusterID, testClock.Now().Add(-40*time.Minute).Unix(), bci.PodStatusPending)

				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetPods().Return([]*v1.Pod{
					testPod,
					newTestV1Pod(ns, "test-1", "test-uid-1", "bci-vk", clusterID, testClock.Now().Add(-time.Minute).Unix(), bci.PodStatusPending),
				})

				bpods := make(map[string]*bci.DescribePodResponse)
				return fields{
					ctrl: ctrl,
					provider: &BCIProvider{
						resourceManager: rm,
						nodeName:        "bci-vk",
						clusterID:       clusterID,
						createV2:        true,
					},
					setupProvider: func(p *BCIProvider) {
						bciClient := bci.NewMockClient(ctrl)
						bciV2Client := bciv2.NewMockClient(ctrl)

						bciV2Client.EXPECT().ListPods(gomock.Any(), gomock.Any(), nil).Return(&bci.ListPodsResponse{
							MaxKeys:     bci.DefaultMaxKeys,
							IsTruncated: false,
							Result:      podMapToList(bpods),
						}, nil)

						p.bciClient = bciClient
						p.bciV2Client = bciV2Client
						p.resourceManager = rm
					},
					notify: func(got *v1.Pod) {
						assert.DeepEqual(t, got, defaultPC.toNotFound(ctx, testPod))
					},
					toNotFoundTimeout: 30 * time.Minute,
				}
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			pc := &podCache{
				clock:             testClock,
				pods:              tt.fields.pods,
				notify:            tt.fields.notify,
				provider:          tt.fields.provider,
				toNotFoundTimeout: tt.fields.toNotFoundTimeout,
			}
			if tt.fields.setupProvider != nil {
				tt.fields.setupProvider(pc.provider)
			}
			pc.toNotFoundChecker(ctx)
		})
	}
}

func Test_podCache_toNotFound(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	testPod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:              "test-pod",
			Namespace:         "default",
			UID:               types.UID("test-uid"),
			CreationTimestamp: metav1.NewTime(testClock.Now()),
			ClusterName:       "cce-xxxxxxxx",
		},
		Spec: v1.PodSpec{
			NodeName: "bci-vk",
			Volumes:  []v1.Volume{},
			Containers: []v1.Container{
				{
					Name:  "container-0",
					Image: "nginx:latest",
				},
			},
		},
		Status: v1.PodStatus{
			Phase: v1.PodPending,
			ContainerStatuses: []v1.ContainerStatus{
				{
					Name: "container-0",
					State: v1.ContainerState{
						Waiting: &v1.ContainerStateWaiting{
							Reason: "Creating",
						},
					},
				},
			},
		},
	}
	type fields struct {
		clock clock.Clock
	}
	type args struct {
		ctx    context.Context
		k8sPod *v1.Pod
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *v1.Pod
	}{
		// All test cases.
		{
			name: "pending pod case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx:    context.TODO(),
				k8sPod: testPod.DeepCopy(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status.Phase = v1.PodFailed
				pod.Status.Reason = podStatusReasonNotFound
				pod.Status.Message = podStatusMessageNotFound
				pod.Status.ContainerStatuses[0] = v1.ContainerStatus{
					Name: "container-0",
					State: v1.ContainerState{
						Terminated: &v1.ContainerStateTerminated{
							ExitCode:   containerStatusExitCodeNotFound,
							Reason:     containerStatusReasonNotFound,
							Message:    containerStatusMessageNotFound,
							FinishedAt: metav1.NewTime(testClock.Now()),
						},
					},
				}

				return pod
			}(),
		},
		{
			name: "pending pod and bci is never seen case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx: context.TODO(),
				k8sPod: func() *v1.Pod {
					pod := testPod.DeepCopy()
					pod.Status.ContainerStatuses[0].State = v1.ContainerState{}
					return pod
				}(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status.Phase = v1.PodFailed
				pod.Status.Reason = podStatusReasonNotFound
				pod.Status.Message = podStatusMessageNotFound
				pod.Status.ContainerStatuses[0].State = v1.ContainerState{}
				return pod
			}(),
		},
		{
			name: "running pod case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx: context.TODO(),
				k8sPod: func() *v1.Pod {
					pod := testPod.DeepCopy()
					pod.Status = v1.PodStatus{
						Phase: v1.PodRunning,
						ContainerStatuses: []v1.ContainerStatus{
							{
								Name: "container-0",
								State: v1.ContainerState{
									Running: &v1.ContainerStateRunning{
										StartedAt: metav1.NewTime(testClock.Now().Add(-15 * time.Minute)),
									},
								},
								Ready:       true,
								ContainerID: "test-container-id",
							},
						},
					}
					return pod
				}(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status = v1.PodStatus{
					Phase:   v1.PodFailed,
					Message: podStatusMessageNotFound,
					Reason:  podStatusReasonNotFound,
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name: "container-0",
							State: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:    containerStatusExitCodeNotFound,
									Reason:      containerStatusReasonNotFound,
									Message:     containerStatusMessageNotFound,
									FinishedAt:  metav1.NewTime(testClock.Now()),
									StartedAt:   metav1.NewTime(testClock.Now().Add(-15 * time.Minute)),
									ContainerID: "test-container-id",
								},
							},
							ContainerID: "test-container-id",
						},
					},
				}
				return pod
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &podCache{
				clock: tt.fields.clock,
			}
			got := pc.toNotFound(tt.args.ctx, tt.args.k8sPod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_ensureTolerations(t *testing.T) {
	type args struct {
		tolerations []v1.Toleration
	}
	tests := []struct {
		name string
		args args
		want []v1.Toleration
	}{
		// TODO: Add test cases.
		{
			name: "not exist case",
			args: args{
				tolerations: []v1.Toleration{
					{
						Effect:   v1.TaintEffectNoSchedule,
						Key:      "virtual-kubelet.io/provider",
						Operator: v1.TolerationOpEqual,
						Value:    "baidu",
					},
					{
						Key:               v1.TaintNodeNotReady,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
					{
						Key:               v1.TaintNodeUnreachable,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
				},
			},
			want: []v1.Toleration{
				{
					Effect:   v1.TaintEffectNoSchedule,
					Key:      "virtual-kubelet.io/provider",
					Operator: v1.TolerationOpEqual,
					Value:    "baidu",
				},
				{
					Key:               v1.TaintNodeNotReady,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				{
					Key:               v1.TaintNodeUnreachable,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				TolerationForNodeNotReady,
				TolerationForNodeUnreachable,
			},
		},
		{
			name: "already exist case",
			args: args{
				tolerations: []v1.Toleration{
					{
						Effect:   v1.TaintEffectNoSchedule,
						Key:      "virtual-kubelet.io/provider",
						Operator: v1.TolerationOpEqual,
						Value:    "baidu",
					},
					{
						Key:               v1.TaintNodeNotReady,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
					{
						Key:               v1.TaintNodeUnreachable,
						Operator:          v1.TolerationOpExists,
						Effect:            v1.TaintEffectNoExecute,
						TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
					},
					TolerationForNodeNotReady,
					TolerationForNodeUnreachable,
				},
			},
			want: []v1.Toleration{
				{
					Effect:   v1.TaintEffectNoSchedule,
					Key:      "virtual-kubelet.io/provider",
					Operator: v1.TolerationOpEqual,
					Value:    "baidu",
				},
				{
					Key:               v1.TaintNodeNotReady,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				{
					Key:               v1.TaintNodeUnreachable,
					Operator:          v1.TolerationOpExists,
					Effect:            v1.TaintEffectNoExecute,
					TolerationSeconds: func() *int64 { var i int64 = 300; return &i }(),
				},
				TolerationForNodeNotReady,
				TolerationForNodeUnreachable,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ensureTolerations(tt.args.tolerations)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_ensureBCIAnnotations(t *testing.T) {
	type args struct {
		old  map[string]string
		bpod *bci.DescribePodResponse
	}
	tests := []struct {
		name string
		args args
		want map[string]string
	}{
		// All test cases.
		{
			name: "pending pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "p-mb6v9ipr",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
			},
		},
		{
			name: "running pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "bind eip to pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
						BoundEIPConfig: bci.BoundEIPConfig{
							EIPID:           "ip-fe76a3e0",
							PublicIP:        "**************",
							BandwidthInMbps: 100,
							EIPRouteType:    "BGP",
							EIPPayMethod:    "ByTraffic",
						},
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:       "zoneC",
				BCISecurityGroupIDAnnotationKey:   "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:          "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:             "",
				BCIVPCUUIDAnnotationKey:           "",
				OrderIDAnnotationKey:              "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:                "p-mb6v9ipr",
				PodUUIDAnnotationKey:              "67589edc-360c-4949-bf02-d176ad490bdc",
				BCIBoundEIPIDAnnotationKey:        "ip-fe76a3e0",
				BCIBoundEIPAddressAnnotationKey:   "**************",
				BCIBoundEIPBandwidthAnnotationKey: "100",
				BCIBoundEIPRouteTypeAnnotationKey: "BGP",
				BCIBoundEIPPayMethodAnnotationKey: "ByTraffic",
			},
		},
		{
			name: "unbind eip to pod case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:       "zoneC",
					BCISecurityGroupIDAnnotationKey:   "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:          "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:             "",
					BCIVPCUUIDAnnotationKey:           "",
					OrderIDAnnotationKey:              "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:                "p-mb6v9ipr",
					PodUUIDAnnotationKey:              "67589edc-360c-4949-bf02-d176ad490bdc",
					BCIBoundEIPIDAnnotationKey:        "ip-fe76a3e0",
					BCIBoundEIPAddressAnnotationKey:   "**************",
					BCIBoundEIPBandwidthAnnotationKey: "100",
					BCIBoundEIPRouteTypeAnnotationKey: "BGP",
					BCIBoundEIPPayMethodAnnotationKey: "ByTraffic",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "nil subnet case",
			args: args{
				old: map[string]string{
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
					PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "new subnet case",
			args: args{
				old: map[string]string{
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
					PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
					Subnet: &bci.Subnet{
						Name:       "some",
						SubnetID:   "f3a97589-d037-49b9-8dd3-25468fd21dd9",
						ShortID:    "sbn-366vk0zh4v3d",
						SubnetUUID: "f3a97589-d037-49b9-8dd3-25468fd21dd9",
					},
				},
			},
			want: map[string]string{
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
			},
		},
		{
			name: "custom annotations exists case",
			args: args{
				old: map[string]string{
					BCILogicalZoneAnnotationKey:     "zoneC",
					BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
					BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
					BCIVPCIDAnnotationKey:           "",
					BCIVPCUUIDAnnotationKey:         "",
					OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
					PodIDAnnotationKey:              "p-mb6v9ipr",
					"some-key":                      "some-value",
				},
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						PodID:   "p-mb6v9ipr",
						PodUUID: "67589edc-360c-4949-bf02-d176ad490bdc",
						OrderID: "1d48567396484052b87acd457366d37c",
						Status:  bci.PodStatusPending,
					},
				},
			},
			want: map[string]string{
				BCILogicalZoneAnnotationKey:     "zoneC",
				BCISecurityGroupIDAnnotationKey: "g-nuk8yx6p4mmt",
				BCISubnetIDAnnotationKey:        "sbn-366vk0zh4v3d",
				BCIVPCIDAnnotationKey:           "",
				BCIVPCUUIDAnnotationKey:         "",
				OrderIDAnnotationKey:            "1d48567396484052b87acd457366d37c",
				PodIDAnnotationKey:              "p-mb6v9ipr",
				PodUUIDAnnotationKey:            "67589edc-360c-4949-bf02-d176ad490bdc",
				"some-key":                      "some-value",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := ensureBCIAnnotations(tt.args.old, tt.args.bpod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_podCache_toMaxRetriesExceeded(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	errMsg := `Error Message: "get security group failed, securityGroupId g-cxcxcxcx", Error Code: "BadRequest", Status Code: 400, Request Id: "07db2f17-0d7a-414d-a48a-79aba7f07bd3"`
	testPod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:              "test-pod",
			Namespace:         "default",
			UID:               types.UID("test-uid"),
			CreationTimestamp: metav1.NewTime(testClock.Now()),
			ClusterName:       "cce-xxxxxxxx",
		},
		Spec: v1.PodSpec{
			NodeName: "bci-vk",
			Volumes:  []v1.Volume{},
			Containers: []v1.Container{
				{
					Name:  "container-0",
					Image: "nginx:latest",
				},
			},
		},
		Status: v1.PodStatus{
			Phase:   v1.PodPending,
			Reason:  podStatusReasonProviderFailed,
			Message: errMsg,
		},
	}
	type fields struct {
		clock clock.Clock
	}
	type args struct {
		ctx    context.Context
		k8sPod *v1.Pod
	}
	tests := []struct {
		name   string
		fields fields
		args   args
		want   *v1.Pod
	}{
		// All test cases.
		{
			name: "pending pod case",
			fields: fields{
				clock: testClock,
			},
			args: args{
				ctx:    context.TODO(),
				k8sPod: testPod.DeepCopy(),
			},
			want: func() *v1.Pod {
				pod := testPod.DeepCopy()
				pod.Status.Phase = v1.PodFailed
				pod.Status.Reason = podStatusReasonMaxRetriesExceeded
				pod.Status.Message = errMsg

				return pod
			}(),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			pc := &podCache{
				clock: tt.fields.clock,
			}
			got := pc.toMaxRetriesExceeded(tt.args.ctx, tt.args.k8sPod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}
