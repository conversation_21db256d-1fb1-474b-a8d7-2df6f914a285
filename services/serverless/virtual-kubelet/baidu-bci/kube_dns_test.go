/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  kube_dns_test
 * @Version: 1.0.0
 * @Date: 2020/8/19 4:54 下午
 */
package baidu_bci

import (
	"context"
	"testing"

	"github.com/google/go-cmp/cmp"
	v1 "k8s.io/api/core/v1"
	kubeAPIMetaV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtimeapi "k8s.io/cri-api/pkg/apis/runtime/v1alpha2"
	"k8s.io/utils/pointer"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

func TestInjectDNSConfig(t *testing.T) {
	clusterDNSServers := []string{"**********"}

	testCases := []struct {
		name        string
		cfg         *bci.PodConfig
		pod         *v1.Pod
		expectedErr bool
		expectedCfg *bci.PodConfig
	}{
		{
			name: "setup ClusterFirst dns config succeed",
			cfg:  &bci.PodConfig{},
			pod: &v1.Pod{
				TypeMeta: kubeAPIMetaV1.TypeMeta{},
				ObjectMeta: kubeAPIMetaV1.ObjectMeta{
					Namespace: "default",
				},
				Spec: v1.PodSpec{
					DNSPolicy: v1.DNSClusterFirst,
				},
				Status: v1.PodStatus{},
			},
			expectedErr: false,
			expectedCfg: &bci.PodConfig{
				Labels: []bci.PodLabel{
					{
						LabelKey: PodDNSConfigLabelKey,
						LabelValue: utils.ToJSON(runtimeapi.DNSConfig{
							Servers:  []string{"**********"},
							Searches: []string{"default.svc.cluster.local", "svc.cluster.local", "cluster.local"},
							Options:  []string{"ndots:5"},
						}),
					},
				},
				DnsConfig: &v1.PodDNSConfig{
					Nameservers: []string{"**********"},
					Searches:    []string{"default.svc.cluster.local", "svc.cluster.local", "cluster.local"},
					Options: []v1.PodDNSConfigOption{
						{
							Name:  "ndots",
							Value: pointer.String("5"),
						},
					},
				},
			},
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			err := injectDNSConfig(context.Background(), tc.cfg, tc.pod, clusterDNSServers, nil)
			if (err == nil && tc.expectedErr) || (err != nil && !tc.expectedErr) {
				t.Errorf("expected err: %v, actual: %v", tc.expectedErr, err)
				return
			}

			if !cmp.Equal(tc.cfg, tc.expectedCfg) {
				t.Errorf("diff cfg between actualy and expected: %s", cmp.Diff(tc.cfg, tc.expectedCfg))
			}
		})
	}
}
