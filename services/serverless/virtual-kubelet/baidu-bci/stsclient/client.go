package stsclient

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/util"
)

const (
	cmdClientEntrypoint string = "/entrypoint.sh"
)

//go:generate mockgen -destination ./mock.go -package stsclient -self_package icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/stsclient icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/stsclient Client
type Client interface {
	GetSessionToken(ctx context.Context, userUUID string) (*bce.SessionTokenResponse, error)
}

func NewClient(mode util.MultiTenantMode) (Client, error) {
	switch mode {
	case util.MultiTenantModeBSC:
		return NewCmdClient(cmdClientEntrypoint)
	}
	return NewUnimplementedClient()
}
