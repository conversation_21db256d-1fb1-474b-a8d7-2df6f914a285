package stsclient

import (
	"context"
	"errors"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
)

type unimplementedClient struct{}

func NewUnimplementedClient() (Client, error) {
	return &unimplementedClient{}, nil
}

func (*unimplementedClient) GetSessionToken(ctx context.Context, userUUID string) (*bce.SessionTokenResponse, error) {
	return nil, errors.New("GetSessionToken unimplemented")
}
