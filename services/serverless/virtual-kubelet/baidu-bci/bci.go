package baidu_bci

import (
	"bytes"
	"context"
	"crypto/sha1"
	"encoding/json"
	"errors"
	"fmt"
	"io"
	syslog "log"
	"os"
	"os/signal"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"syscall"
	"time"

	"code.cloudfoundry.org/clock"
	uuid "github.com/satori/go.uuid"
	"github.com/virtual-kubelet/virtual-kubelet/errdefs"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/node/api"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	v1 "k8s.io/api/core/v1"
	k8serror "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sversion "k8s.io/apimachinery/pkg/version"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci/v2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcm"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/retrypolicy"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/fake_bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/manager"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/provider"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/stsclient"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/userstorage"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/util"
)

const (
	UIDLabelKey               string = "UID"
	PodNameLabelKey           string = "PodName"
	NodeNameLabelKey          string = "NodeName"
	NamespaceLabelKey         string = "Namespace"
	CreationTimestampLabelKey string = "CreationTimestamp"
	ClusterIDLabelKey         string = "CCEClusterID"

	ResourceGPU v1.ResourceName = "nvidia.com/gpu"

	HostAliasesAnnotationKey string = "bci.virtual-kubelet.io/pod-host-aliases"

	OrderIDAnnotationKey         string = "bci.virtual-kubelet.io/order-id"
	HistoryOrderIDsAnnotationKey string = "bci.virtual-kubelet.io/history-order-ids"
	PodIDAnnotationKey           string = "bci.virtual-kubelet.io/pod-id"
	PodUUIDAnnotationKey         string = "bci.virtual-kubelet.io/pod-uuid"

	HidedContainersLabelKey         string = "bci.virtual-kubelet.io/hided-containers"
	KubeProxyContainerNameLabelKey  string = "bci.virtual-kubelet.io/kubeproxy-container"
	KubeProxyHealthzAddressLabelKey string = "bci.virtual-kubelet.io/kubeproxy-healthz-address"

	EnableAutoLogPushPhasesLabelKey string = "bci.baidubce.com/auto-enable-log-push-phases"

	CorePatternLabelKey string = "bci.virtual-kubelet.io/core-pattern"
	K8sVersionV122      string = "v1.22.0"

	IPResource  v1.ResourceName = "cce.baidubce.com/ip"
	ENIResource v1.ResourceName = "cce.baidubce.com/eni"
)

var (
	errNotImplemented = fmt.Errorf("not implemented by bci provider")
)

type BCIProvider struct {
	bciClient          bci.Client
	bciV2Client        bciv2.Client
	createV2           bool
	cceGatewayHelper   ccegateway.Helper
	cceGatewayTokenSrc tokenSource // where to get plugin token deployed by cce-gateway
	resourceManager    manager.ResourceManager
	region             string `valid:"Required"`
	nodeName           string `valid:"Required"`
	operatingSystem    string `description:"for OperatingSystem"`
	cpu                string `valid:"Required" description:"node cpu"`
	memory             string `valid:"Required" description:"node memory"`
	ephemeralStorage   string `valid:"Required" description:"node ephemeral storage"`
	pods               string `description:"preset pod quota"`
	ips                string `description:"preset ips quota"`
	enis               string `description:"preset enis quota"`
	internalIP         string `description:"for NodeAddresses"`
	daemonEndpointPort int32  `description:"for NodeDaemonEndpoints"`

	vpcID           string   `valid:"Required"`
	vpcUUID         string   `valid:"Required"`
	logicalZones    []string `valid:"MinSize(1)"`
	subnetIDs       []string `valid:"MinSize(1)"`
	securityGroupID string   `valid:"Required"`
	clusterID       string   `valid:"Required"`

	// for mutli-tenant mode, optional
	multiTenantMode util.MultiTenantMode
	userStorage     userstorage.Storage
	stsClient       stsclient.Client

	// standalone charge fields, optional
	chargeApplication string
	chargeAccessKey   string
	chargeSecretKey   string

	forceInjectDNSConfig bool
	AutoInjectKubeProxy  bool
	nodeDNSConfig        *v1.PodDNSConfig

	// pod cache option
	enablePodCache bool
	podCache       PodCache

	podRescuer PodRescuer

	eventSinker EventSinker

	subnetOptions map[string]*SubnetOption

	clock clock.Clock

	// token cache option
	enableTokenCache     bool
	tokenController      TokenController
	bciProfileConfig     *BCIProfileConfig
	bciProfileController *BCIProfileController
}

// PodNotifierWrapper wraps BCIProvider to only implement PodNotifier when podCache is enabled.
type PodNotifierWrapper struct {
	*BCIProvider
}

type SubnetOption struct {
	Weight      int          `json:"weight"`
	LogicalZone string       `json:"logicalZone"`
	Quota       BCIResources `json:"quota"`
}

type BCIResources struct {
	// fields only for json unmarshaling
	CPUQuantity    *resource.Quantity `json:"cpu,omitempty"`
	MemoryQuantity *resource.Quantity `json:"memory,omitempty"`

	// counting fields
	CPUInCore  float64 `json:"-"`
	MemoryInGB float64 `json:"-"`
	Pods       int     `json:"pods"`
}

func (res *BCIResources) UnmarshalJSON(data []byte) error {
	type Alias BCIResources
	v := new(Alias)
	if err := json.Unmarshal(data, v); err != nil {
		return err
	}
	res.CPUQuantity = v.CPUQuantity
	res.MemoryQuantity = v.MemoryQuantity
	res.Pods = v.Pods
	if res.CPUQuantity != nil {
		res.CPUInCore = float64(v.CPUQuantity.MilliValue()/10.00) / 100.00
	}
	if res.MemoryQuantity != nil {
		res.MemoryInGB = float64(v.MemoryQuantity.Value()/1024.00/1024.00) / 1024.00
	}

	return nil
}

func (x BCIResources) Less(y BCIResources) bool {
	if y.CPUInCore > 0 && x.CPUInCore >= y.CPUInCore {
		return false
	}
	if y.MemoryInGB > 0 && x.MemoryInGB >= y.MemoryInGB {
		return false
	}
	if y.Pods > 0 && x.Pods >= y.Pods {
		return false
	}
	return true
}

func ResourceAdd(x *BCIResources, y BCIResources) *BCIResources {
	if x == nil {
		x = &BCIResources{}
	}
	return &BCIResources{
		CPUInCore:  x.CPUInCore + y.CPUInCore,
		MemoryInGB: x.MemoryInGB + y.MemoryInGB,
		Pods:       x.Pods + y.Pods,
	}
}

func NewBCIProvider(ctx context.Context, cfg provider.InitConfig) (provider.Provider, error) {
	var p BCIProvider

	p.AutoInjectKubeProxy = cfg.AutoInjectKubeProxy
	p.resourceManager = cfg.ResourceManager
	p.operatingSystem = cfg.OperatingSystem
	p.nodeName = cfg.NodeName
	// get node ip
	p.internalIP = cfg.InternalIP
	err := p.getNodeInternalIP(ctx, cfg.InternalIP)
	if err != nil {
		return nil, err
	}
	//
	p.daemonEndpointPort = cfg.DaemonPort
	p.forceInjectDNSConfig = cfg.ForceInjectDNSConfig
	if cfg.DnsConfigPath != "" {
		if err := p.loadDNSConfigFromPath(cfg.DnsConfigPath); err != nil {
			log.G(ctx).Warnf("load dnsconfig err: %v, do nothing", err)
			// ignore error
		}
	}

	k8sServerVersion, _ := cfg.ResourceManager.GetRawClient().Discovery().ServerVersion()
	if k8sServerVersion != nil {
		p.enableTokenCache = k8sversion.CompareKubeAwareVersionStrings(K8sVersionV122, k8sServerVersion.String()) >= 0
	}

	if cfg.ConfigPath != "" {
		f, err := os.Open(cfg.ConfigPath)
		if err != nil {
			return nil, err
		}
		defer f.Close()
		if err := p.loadConfig(f); err != nil {
			return nil, err
		}
	}

	fillEnvErr := fillProviderByEnv(&p)
	if fillEnvErr != nil {
		return nil, fillEnvErr
	}

	if err := p.setupNetworkProfile(); err != nil {
		return nil, err
	}

	if err := p.setupCapacity(); err != nil {
		return nil, err
	}

	if v := os.Getenv("MULTI_TENANT_MODE"); v != "" {
		// 多租户版本VK依赖存储在namespace名称中的租户信息(userUUID)，即要求namespace格式为 ns-{{userUUID}}-...
		// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/xP9SDU9Dtz/ffc4L_yOlGAW4I
		p.multiTenantMode = util.MultiTenantMode(v)
	}
	p.userStorage, err = userstorage.NewStorage(p.multiTenantMode, cfg.NodeName)
	if err != nil {
		return nil, err
	}
	p.stsClient, err = stsclient.NewClient(p.multiTenantMode)
	if err != nil {
		return nil, err
	}

	var accessKey, secretKey string
	if ak := os.Getenv("BCI_ACCESS_KEY"); ak != "" {
		accessKey = ak
	}
	if sk := os.Getenv("BCI_SECRET_KEY"); sk != "" {
		secretKey = sk
	}
	protocol := "https"
	if v := os.Getenv("FORCE_HTTP_PROTOCOL"); v == "true" {
		protocol = "http"
	}
	if (accessKey == "" || secretKey == "") && p.multiTenantMode == "" {
		// no method for auth is available, fall back to cce-gateway
		p.cceGatewayHelper = ccegateway.NewHelper(p.region, p.clusterID)
		protocol = "http"
	}
	p.bciClient, p.bciV2Client = newBCIClient(ctx, accessKey, secretKey, p.region, os.Getenv("BCI_ENDPOINT"), protocol, p.cceGatewayHelper)
	// check if bci service is running through bci quota api
	podQuota, err := p.bciClient.GetPodQuota(ctx, p.getSignOption(ctx))
	if err != nil {
		log.G(ctx).WithError(err).Errorf("fail to get user quota")
	} else {
		log.G(ctx).WithField("pod_quota", podQuota).Info("get user quota")
	}
	var accountID string
	accountID, p.createV2, err = p.getUserVersion(ctx)
	if err != nil {
		return nil, err
	}

	// enable pod cache
	if v := os.Getenv("ENABLE_POD_CACHE"); v == "true" {
		p.enablePodCache = true
		if v = os.Getenv("ENABLE_POD_RESCUE"); v == "true" {
			p.podRescuer = NewPodRescuer(ctx, 16, &p)
			go p.podRescuer.Run(ctx)
		}

		p.podCache = NewPodCache(ctx, &p, getMaxReqInFlight(ctx), 3*time.Second, 4*time.Minute, 0.2)
	}

	// Start event sinker.
	esConfig := newDefaultEventSinkerConfig()
	if v := os.Getenv("EVENT_SINKER_CONFIG"); v != "" {
		if err := json.Unmarshal([]byte(v), esConfig); err != nil {
			log.G(ctx).WithError(err).Warnf("ignore invalid event sinker config '%s'", v)
		}
	}
	p.eventSinker, err = NewEventSinker(ctx, accountID, newBCMClient(ctx, accessKey, secretKey, p.region, os.Getenv("BCM_ENDPOINT"), protocol, p.cceGatewayHelper),
		esConfig, p.resourceManager.GetEventRecorder(), &p)
	if err != nil {
		return nil, err
	}

	p.bciProfileController, err = NewBCIProfileController(ctx, &p)
	if err != nil {
		log.G(ctx).Warnf("create bci profile controller err: %v, do nothing", err)
	}
	// sync kube-system bci-profile
	go p.bciProfileController.Run(ctx)

	p.clock = clock.NewClock()

	// Initialize stack trace dumper
	c := make(chan os.Signal, 1)
	signal.Notify(c, syscall.SIGUSR1)
	go stackDumper(c)

	if p.enableTokenCache {
		p.tokenController = NewTokenController(&p, getMaxReqInFlight(ctx))
	}

	// NOTE(yezichao): all async works must be started in the last so that all attrs of provider have been set.
	defer p.startAsyncWorks(ctx)

	if p.enablePodCache {
		return &PodNotifierWrapper{&p}, nil
	}
	return &p, nil
}

func fillProviderByEnv(p *BCIProvider) error {
	if v := os.Getenv("BCI_REGION"); v != "" {
		p.region = v
	} else {
		return errors.New("region can't be empty please set BCI_REGION")
	}
	if v := os.Getenv("CCE_CLUSTER_ID"); v != "" {
		p.clusterID = v
	} else {
		return errors.New("CCE cluster id can't be empty please set CCE_CLUSTER_ID")
	}
	if v := os.Getenv("CHARGE_APPLICATION"); v != "" {
		p.chargeApplication = v
	}
	if v := os.Getenv("CHARGE_ACCESS_KEY"); v != "" {
		p.chargeAccessKey = v
	}
	if v := os.Getenv("CHARGE_SECRET_KEY"); v != "" {
		p.chargeSecretKey = v
	}
	return nil
}

var exitfunc = func() {
	os.Exit(1)
}

func (p *BCIProvider) setBCIProfileConfig(config *BCIProfileConfig) {
	p.bciProfileConfig = config
}

func (p *BCIProvider) startAsyncWorks(ctx context.Context) {
	if p.podCache != nil {
		err := p.podCache.Start(ctx)
		if err != nil {
			log.G(ctx).WithError(err).Error("pod cache start failed")
			exitfunc()
		}
	}
	// eventSinker must start after pod cache (if pod cache is enabled).
	go p.eventSinker.Run(ctx)
	log.G(ctx).Info("start event sinker")

	// report k8s pods to bci
	go p.reportPods(ctx)
	log.G(ctx).Info("start reportPods")

	// sync serviceAccountToken
	if p.tokenController != nil {
		p.tokenController.Start(ctx)
		log.G(ctx).Info("start tokenController")
	}
}

func getMaxReqInFlight(ctx context.Context) int {
	maxReqInFlight := 128
	if v := os.Getenv("POD_CACHE_MAX_REQUESTS"); v != "" {
		if got, err := strconv.Atoi(v); err == nil {
			maxReqInFlight = got
		} else {
			log.G(ctx).WithError(err).Warnf("illegal POD_CACHE_MAX_REQUESTS value=%s", v)
		}
	}
	log.G(ctx).Debugf("use maxReqInFlight value %d", maxReqInFlight)
	return maxReqInFlight
}

func (p *BCIProvider) getUserVersion(ctx context.Context) (string, bool, error) {
	userVersion, err := p.bciV2Client.GetUserVersion(ctx, p.getSignOption(ctx))
	if err != nil {
		log.G(ctx).WithError(err).Errorf("fail to get user version")
		return "", false, err
	} else {
		if userVersion.IsV2 {
			log.G(ctx).Info("use v2 api for creation")
		}
	}
	return userVersion.AccountID, userVersion.IsV2, nil
}

// newBCIClient wraps the bci.NewClient
func newBCIClient(ctx context.Context, accessKey, secretKey, region, endpoint, protocol string, helper ccegateway.Helper) (bci.Client, bciv2.Client) {
	if endpoint == "FAKE" {
		// Use the same client for v1 and v2 to share created pods in memory.
		c := fake_bci.NewFakeBCIClient()
		// dry run mode
		return c, c
	}

	bceConfig := &bce.Config{
		Region:   region,
		Endpoint: endpoint,
		Protocol: protocol,
		Checksum: true,
		Timeout:  60 * time.Second,
		// Never retry on bci request.
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	}
	if accessKey != "" && secretKey != "" {
		bceConfig.Credentials = bce.NewCredentials(accessKey, secretKey)
	}
	// set cce-gateway proxy if defined
	if helper != nil {
		bceConfig.ProxyHost, bceConfig.ProxyPort = helper.GetHostAndPort()
		bceConfig.Protocol = "http"
	}
	c := bci.NewClient(bci.NewConfig(bceConfig))
	c.SetDebug(true)
	v2c := bciv2.NewClient(bciv2.NewConfig(bceConfig))
	v2c.SetDebug(true)
	return c, v2c
}

func newBCMClient(ctx context.Context, accessKey, secretKey, region, endpoint, protocol string, helper ccegateway.Helper) bcm.Interface {
	if endpoint == "FAKE" {
		// dry run mode
		return fake_bci.NewFakeBCMClient(ctx, "p-c9s3qjro")
	}

	bceConfig := &bce.Config{
		Region:   region,
		Endpoint: endpoint,
		Protocol: protocol,
		Checksum: true,
		Timeout:  30 * time.Second,
		// Never retry on bcm request.
		RetryPolicy: retrypolicy.NewIntervalRetryPolicy(ctx, 0, 0),
	}
	if accessKey != "" && secretKey != "" {
		bceConfig.Credentials = bce.NewCredentials(accessKey, secretKey)
	}
	// set cce-gateway proxy if defined
	if helper != nil {
		bceConfig.ProxyHost, bceConfig.ProxyPort = helper.GetHostAndPort()
		bceConfig.Protocol = "http"
	}
	c := bcm.NewClient(bceConfig)
	c.SetDebug(true)
	return c
}

// stackDumper dumps the current stack when received from c.
func stackDumper(c <-chan os.Signal) {
	for range c {
		var buf []byte
		var stackSize int
		bufferLen := 16384
		for stackSize == len(buf) {
			buf = make([]byte, bufferLen)
			stackSize = runtime.Stack(buf, true)
			bufferLen *= 2
		}
		buf = buf[:stackSize]
		syslog.Printf("=== BEGIN goroutine stack dump ===\n%s\n=== END goroutine stack dump ===", buf)
	}
}

// chooseBCIClient choose an appropriate bci client to handle the pod.
// If pod is nil (creation request), chooseBCIClient respects the createV2 flag.
func (p *BCIProvider) chooseBCIClient(pod *bci.Pod) bciv2.Client {
	if pod == nil {
		if p.createV2 {
			return p.bciV2Client
		}
		return p.bciClient
	}
	if pod.V2 {
		return p.bciV2Client
	}
	return p.bciClient
}

func (p *PodNotifierWrapper) NotifyPods(ctx context.Context, f func(*v1.Pod)) {
	if p.podCache == nil {
		log.G(ctx).Fatal("podCache cannot be nil")
	}
	log.G(ctx).Info("NotifyPods is enabled")
	p.podCache.NotifyPods(ctx, f)
}

// CreatePod takes a Kubernetes Pod and deploys it within the provider.
func (p *BCIProvider) CreatePod(ctx context.Context, pod *v1.Pod) error {
	ctx, span := trace.StartSpan(ctx, "bci.CreatePod")
	defer span.End()

	userUUID := p.getUserUUID(pod.GetNamespace(), pod.GetName())
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      pod.GetName(),
		"podNamespace": pod.GetNamespace(),
	}), userUUID)

	if p.podRescuer != nil &&
		p.podRescuer.IsRescuing(ctx, pod.GetNamespace(), pod.GetName(), string(pod.GetUID())) {
		log.G(ctx).Error("pod is being rescued")
		return fmt.Errorf("pod is being rescued")
	}

	var hidedContainers []string
	var kubeProxyContainerName string
	var kubeProxyHealthzPort int64

	annotationParams, err := parseBCIAnnotations(ctx, pod.Annotations)
	if err != nil {
		return err
	}

	// override settings from annotation params
	vpcID, vpcUUID := p.vpcID, p.vpcUUID
	if annotationParams.VPCID != "" && annotationParams.VPCUUID != "" {
		vpcID, vpcUUID = annotationParams.VPCID, annotationParams.VPCUUID
	}

	var logicalZone, subnetID string
	if annotationParams.LogicalZone != "" {
		logicalZone = annotationParams.LogicalZone
	}
	if annotationParams.SubnetID != "" {
		subnetID = annotationParams.SubnetID
	}

	if subnetID == "" && annotationParams.SubnetIDs == "" {
		subnetID, logicalZone, err = p.selectSubnet(ctx, pod)
		if err != nil {
			log.G(ctx).WithError(err).Error("fail to select subnet for creation")
			return err
		}
	}

	securityGroupID := p.securityGroupID
	if annotationParams.SecurityGroupID != "" {
		securityGroupID = annotationParams.SecurityGroupID
	}

	productType := bci.ProductTypePostPay
	var bidOption *bci.BidOption
	if annotationParams.BidModel != "" {
		productType = bci.ProductTypeBidding
		bidOption = &bci.BidOption{
			BidModel: annotationParams.BidModel,
			BidPrice: annotationParams.BidPrice,
		}
	}

	cfg := &bci.PodConfig{
		Name:                       p.getPodFullName(pod.GetNamespace(), pod.GetName()),
		RestartPolicy:              getRestartPolicy(pod.Spec.RestartPolicy),
		VPCID:                      vpcID,
		VPCUUID:                    vpcUUID,
		SubnetID:                   subnetID,
		SecurityGroupID:            securityGroupID,
		SubnetIDs:                  annotationParams.SubnetIDs,
		Application:                annotationParams.Application,
		CCEID:                      p.clusterID,
		LogicalZone:                logicalZone,
		ProductType:                productType,
		BidOption:                  bidOption,
		EIPIP:                      annotationParams.EIPAddress,
		CPUType:                    annotationParams.CPUType,
		DelayReleaseDurationMinute: annotationParams.DelayReleaseDurationMinute,
		DelayReleaseSucceeded:      annotationParams.DelayReleaseSucceeded,
		// NOTE: ServiceType and PurchaseNum are constant
		ServiceType:     bci.ServiceTypeBCI,
		PurchaseNum:     1,
		SecurityContext: pod.Spec.SecurityContext,
		IsTidal:         annotationParams.IsTidal,
	}

	clientToken := string(pod.GetUID())
	// Different token for pod rescue case.
	if v, ok := pod.GetAnnotations()[PodIDAnnotationKey]; ok && v != "" {
		clientToken += "-" + v
	}
	cfg.ClientToken = clientToken
	// Get containers.
	// Add EnvVarSource to implement status.podIP env value for bci v2.
	if p.createV2 {
		pod = addPodIPEnvSource(ctx, pod)
	}

	containers, err := p.getContainers(pod.Spec.Containers, annotationParams.GPUType)
	if err != nil {
		return err
	}
	// Add init containers and probes for bci v2.
	if p.createV2 {
		initContainers, err := p.getContainers(pod.Spec.InitContainers, annotationParams.GPUType)
		if err != nil {
			return err
		}
		for i := range initContainers {
			initContainers[i].ContainerType = bci.ContainerTypeInit
		}
		containers = append(containers, initContainers...)
	}
	networkPrivilegedContainers := p.getNetworkPrivilegedContainers(pod)
	// get registry creds
	creds, err := p.getImageRegistrySecrets(pod)
	if err != nil {
		return err
	}
	// NOTE: this append image namespace to servers in secrets to accommodate bci implementation.
	// This assumes only one secret for each registry.
	// Remove this when BCI support multiple possible creds for one image.
	creds = adjustBCISecretsBasedOnImages(creds, containers)

	// get volumes
	volumes, err := p.getVolumes(ctx, pod)
	if err != nil {
		return err
	}

	// get affinity
	affinity, err := p.getAffinity(ctx, pod)
	if err != nil {
		return err
	}

	cfg.Affinity = affinity
	cfg.TerminationGracePeriodSeconds = pod.Spec.TerminationGracePeriodSeconds
	cfg.Hostname = pod.Spec.Hostname
	cfg.Containers = containers
	cfg.ImageRegistrySecret = creds
	cfg.Volumes = volumes
	if volumes != nil {
		if len(volumes.TokenExtras) > 0 {
			cfg.Extras = volumes.TokenExtras
		}
	}

	// NOTE(yezichao) set pod attr in labels
	cfg.Labels = []bci.PodLabel{
		{LabelKey: UIDLabelKey, LabelValue: string(pod.UID)},
		{LabelKey: PodNameLabelKey, LabelValue: pod.GetName()},
		{LabelKey: NamespaceLabelKey, LabelValue: pod.GetNamespace()},
		{LabelKey: NodeNameLabelKey, LabelValue: pod.Spec.NodeName},
		{LabelKey: ClusterIDLabelKey, LabelValue: p.clusterID},
		{LabelKey: CreationTimestampLabelKey, LabelValue: strconv.FormatInt(pod.CreationTimestamp.Unix(), 10)},
	}

	cfg.MetadataLabels = getBCIPodMetadataLabels(pod)

	if annotationParams.EnableLogPush {
		cfg.EnableLog = true
	}
	if annotationParams.EnableAutoLogPushPhases != "" {
		cfg.Labels = append(cfg.Labels, bci.PodLabel{
			LabelKey:   EnableAutoLogPushPhasesLabelKey,
			LabelValue: annotationParams.EnableAutoLogPushPhases,
		})
	}

	annotations := make(map[string]interface{})
	if v, ok := pod.GetAnnotations()[PodIPAnnotationKey]; ok {
		log.G(ctx).Infof("found pod ip annotation '%s'", v)
		podIPAnnotation := NewPodIPAnnotationValue()
		err := json.Unmarshal([]byte(v), podIPAnnotation)
		if err != nil {
			log.G(ctx).WithError(err).Errorf("malformed pod ip annotation value '%s'", v)
			return err
		}
		annotations[PodIPAnnotationKey] = podIPAnnotation

	}
	if len(pod.Spec.HostAliases) > 0 {
		annotations[HostAliasesAnnotationKey] = pod.Spec.HostAliases
	}
	for k, v := range getLifecycleAnnotations(pod) {
		if len(v) > 0 {
			annotations[k] = v
		}
	}
	for k, v := range getTerminationParamAnnotations(pod) {
		if len(v) > 0 {
			annotations[k] = v
		}
	}

	// add schedule in pfs pool annotation
	if scheduleInPfsPool, ok := pod.Annotations[BCIScheduleInPfsPoolAnnotationKey]; ok {
		annotations[BCIScheduleInPfsPoolAnnotationKey] = scheduleInPfsPool
	}

	// add bci resource tag annotation
	if resourceTag, ok := pod.Annotations[BCIResourceTagAnnotationKey]; ok {
		annotations[BCIResourceTagAnnotationKey] = resourceTag
	}

	// add bci max pending minute annotation
	if maxPendingMinute, ok := pod.Annotations[BCIMaxPendingMinuteAnnotationKey]; ok {
		annotations[BCIMaxPendingMinuteAnnotationKey] = maxPendingMinute
	}

	// generate eip config if necessary
	var eipCfg *bci.EIPConfig
	if annotationParams.CreateEIP {
		if p.chargeApplication != "" && p.chargeAccessKey != "" && p.chargeSecretKey != "" {
			// NOTE(yezichao): EIP order cannot be properly handled with charge application in bci console.
			err := errors.New("EIP creation is forbidden with charge application present")
			log.G(ctx).WithError(err).Error("EIP creation forbidden")
			return err
		}
		eipName := p.clusterID + "-bci-" + cfg.Name
		if len(eipName) > bci.MaxEIPNameLength /* should be 65, so sha1sum is used here */ {
			eipName = fmt.Sprintf("%s-bci-%x", p.clusterID, sha1.Sum([]byte(cfg.Name)))
		}
		eipCfg = bci.NewEIPConfig(ctx, eipName, p.region, annotationParams.CreateEIPRouteType,
			annotationParams.CreateEIPBandwidth, annotationParams.CreateEIPBillingMethod, p.createV2)
	}

	if pod.Annotations == nil {
		pod.Annotations = make(map[string]string)
	}

	if p.AutoInjectKubeProxy {
		pod.Annotations[KUBEProxyEnabledAnnotationKey] = "true"
	}
	kubeProxyEnabled := pod.Annotations[KUBEProxyEnabledAnnotationKey]
	if kubeProxyEnabled == "true" {
		// 用随机生成的uuid来避免kubeproxy的container name刚好和用户设置的container name重复
		id := uuid.NewV4().String()
		kubeProxyHealthzPort = getKubeProxyHealthzPort(pod.Annotations)
		// serviceMeshEnabled
		serviceMeshEnabled := false
		for key := range pod.Labels {
			if strings.Contains(key, "istio.io") {
				serviceMeshEnabled = true
				break
			}
		}

		kubeProxyContainerName, annotations, err = injectKUBEProxy(ctx, cfg, id[:6], kubeProxyHealthzPort, annotations, p.createV2, serviceMeshEnabled)
		if err != nil {
			log.G(ctx).Errorf("failed to inject kube-proxy container into pod, err: %v", err)
			return err
		}
		hidedContainers = append(hidedContainers, kubeProxyContainerName)
		networkPrivilegedContainers = append(networkPrivilegedContainers, kubeProxyContainerName)
	}

	var clusterDNSServers []string
	// Cluster DNS is unnecessary with neither kube-proxy nor force injection present.
	if (kubeProxyEnabled == "true") || p.forceInjectDNSConfig {
		clusterDNSServers, err = p.getClusterDNSServers(ctx)
		if err != nil {
			if !k8serror.IsNotFound(err) {
				log.G(ctx).Errorf("failed to get cluster dns servers, err: %v", err)
				return err
			} else {
				log.G(ctx).Warnf("failed to inject dns config into pod, err: svc kube-dns in kube-system not found")
			}
		}
	}

	if err := injectDNSConfig(ctx, cfg, pod, clusterDNSServers, p.nodeDNSConfig); err != nil {
		log.G(ctx).Errorf("failed to inject dns config into pod, err: %v", err)
		return err
	}

	if len(annotations) > 0 {
		annotationsBytes, err := json.Marshal(annotations)
		if err != nil {
			return err
		}
		cfg.Annotations = string(annotationsBytes)
	}

	if len(hidedContainers) > 0 {
		cfg.Labels = append(cfg.Labels, bci.PodLabel{
			LabelKey:   HidedContainersLabelKey,
			LabelValue: utils.ToJSON(hidedContainers),
		})
	}
	if kubeProxyContainerName != "" {
		cfg.Labels = append(cfg.Labels,
			bci.PodLabel{
				LabelKey:   KubeProxyContainerNameLabelKey,
				LabelValue: kubeProxyContainerName,
			},
			bci.PodLabel{
				LabelKey:   KubeProxyHealthzAddressLabelKey,
				LabelValue: fmt.Sprintf("127.0.0.1:%d", kubeProxyHealthzPort),
			})
	}

	if len(networkPrivilegedContainers) > 0 {
		networkPrivilegedContainersBytes, err := json.Marshal(networkPrivilegedContainers)
		if err != nil {
			return err
		}
		cfg.Labels = append(cfg.Labels, bci.PodLabel{
			LabelKey:   NetworkPrivilegeLabelKey,
			LabelValue: string(networkPrivilegedContainersBytes),
		})
	}

	if annotationParams.CorePattern != "" {
		cfg.Labels = append(cfg.Labels, bci.PodLabel{
			LabelKey:   CorePatternLabelKey,
			LabelValue: annotationParams.CorePattern,
		})
	}

	// not rollback add even when err occurs in p.bciClient.CreatePod to ensure no user leak.
	if err = p.userStorage.AddUser(ctx, userUUID, p.nodeName); err != nil {
		log.G(ctx).WithError(err).Error("fail to add user to userstorage")
		return err
	}
	resp, err := p.chooseBCIClient(nil).CreatePod(ctx, cfg, eipCfg, p.getSignOption(ctx))
	if err != nil {
		return err
	}
	// Add resource for created pod to subnet usage before podCache knows this pod.
	// Usage may be temporarily larger than than real usage but recovers after podCache sync
	// because podCache sync updates subnet usage by counting all pods appeared in list.
	if p.podCache != nil && subnetID != "" {
		var cpu, mem float64
		for _, c := range containers {
			cpu += c.CPUInCore
			mem += c.MemoryInGB
		}
		p.podCache.AddSubnetUsage(ctx, subnetID, BCIResources{
			CPUInCore:  cpu,
			MemoryInGB: mem,
			Pods:       1,
		})
	}

	podAnnotations := pod.GetAnnotations()
	if podAnnotations == nil {
		pod.SetAnnotations(make(map[string]string))
		podAnnotations = pod.GetAnnotations()
	}
	podAnnotations[OrderIDAnnotationKey] = resp.OrderID
	podAnnotations[BCIVPCIDAnnotationKey] = vpcID
	podAnnotations[BCIVPCUUIDAnnotationKey] = vpcUUID
	podAnnotations[BCISecurityGroupIDAnnotationKey] = securityGroupID
	if subnetID != "" {
		podAnnotations[BCILogicalZoneAnnotationKey] = logicalZone
		podAnnotations[BCISubnetIDAnnotationKey] = subnetID
	}
	if len(resp.PodIDs) > 0 {
		podAnnotations[PodIDAnnotationKey] = resp.PodIDs[0]
		if p.enablePodCache {
			p.podCache.NotifyPodCreating(ctx, resp.PodIDs[0], pod)
		}
	}
	// 通知token-controller，向优先级队列中添加token
	if p.tokenController != nil && volumes != nil {
		for k, v := range volumes.TokenExtras {
			parsedTime, err := strconv.ParseInt(v, 10, 64)
			if err != nil {
				continue
			}
			p.tokenController.NotifyToken(ctx, k, pod.UID, parsedTime, AddTokenType)
		}
	}
	return nil
}

func getBCIPodMetadataLabels(pod *v1.Pod) []bci.PodLabel {
	var metadataLabels []bci.PodLabel
	for k, v := range pod.GetLabels() {
		metadataLabels = append(metadataLabels, bci.PodLabel{
			LabelKey:   k,
			LabelValue: v,
		})
	}
	return metadataLabels
}

func addPodIPEnvSource(ctx context.Context, pod *v1.Pod) *v1.Pod {
	if v, ok := pod.GetAnnotations()[PodIPAnnotationKey]; ok && v != "" {
		annotationValue := *NewPodIPAnnotationValue()
		if err := json.Unmarshal([]byte(v), &annotationValue); err == nil {
			addPodIPEnvSourceToContainers(annotationValue, pod.Spec.InitContainers)
			addPodIPEnvSourceToContainers(annotationValue, pod.Spec.Containers)
		} else {
			log.G(ctx).WithError(err).WithField("annotation", v).Error("ignore invalid pod ip annotation")
		}
	}
	return pod
}

func addPodIPEnvSourceToContainers(podIPAnnotation PodIPAnnotationValueType, cs []v1.Container) {
	for cIndex, c := range cs {
		if envs, ok := podIPAnnotation[c.Name]; ok && len(envs) > 0 {
			podIPEnvNames := make(map[string]struct{}, len(envs))
			for _, env := range envs {
				podIPEnvNames[env] = struct{}{}
			}
			for eIndex, env := range c.Env {
				if _, ok := podIPEnvNames[env.Name]; ok {
					cs[cIndex].Env[eIndex].ValueFrom = &v1.EnvVarSource{
						FieldRef: &v1.ObjectFieldSelector{
							FieldPath: "status.podIP",
						},
					}
				}
			}
		}
	}
}

// UpdatePod takes a Kubernetes Pod and updates it within the provider.
// Currently UpdatePod only handles pod re-creation case as following:
// 1. Pod 0 is created in k8s.
// 2. Corresponding bci 0 is created.
// 3. Pod 0 becomes pending and corresponding bci 0 is in creating status.
// 4. Delete pod 0 in k8s.
// 5. Pod 0 is deleted immediately in k8s as it is not running (vk logic).
// 6. Delete bci 0 but failed as a pending bci is not allowed to delete (or error occurs in deleting), requeue.
// 7. Pod 1 is created in k8s using the same name and namespace as pod 0.
// 8. GetPod returns bci 0 (with namespace and name as args only), so UpdatePod is triggered (vk logic).
// 9. If UpdatePod does nothing, after bci 0 is deleted, pod 1 will enter NotFound as no bci 1 is created.
// Pod live update is not supported yet.
func (p *BCIProvider) UpdatePod(ctx context.Context, pod *v1.Pod) error {
	ctx, span := trace.StartSpan(ctx, "bci.UpdatePod")
	defer span.End()

	userUUID := p.getUserUUID(pod.GetNamespace(), pod.GetName())
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      pod.GetName(),
		"podNamespace": pod.GetNamespace(),
		"podUID":       pod.GetUID(),
	}), userUUID)

	// Check if update request can be accepted.
	if pod.GetDeletionTimestamp() != nil {
		log.G(ctx).Warn("pod is being deleted, ignore update")
		return nil
	}
	if pod.Status.Phase != v1.PodPending {
		log.G(ctx).Warn("pod is not Pending, ignore update")
		return nil
	}

	// Check if pod already exists.
	bpods, err := p.filterAllBCIPods(ctx, pod)
	if err == nil && len(bpods) > 0 {
		var bpodIDs []string
		for _, bpod := range bpods {
			bpodIDs = append(bpodIDs, bpod.PodID)
		}
		log.G(ctx).Infof("pod already exists, ignore update: %v", bpodIDs)
		return nil
	}
	if !errdefs.IsNotFound(err) {
		log.G(ctx).WithError(err).Error("getAllBCIPodIDs by v1.Pod failed")
		return err
	}

	// Updated pod not found, try to create it.
	log.G(ctx).Info("updated pod not found, try to re-create it")
	return p.CreatePod(ctx, pod)
}

// DeletePod takes a Kubernetes Pod and deletes it from the provider.
func (p *BCIProvider) DeletePod(ctx context.Context, pod *v1.Pod) error {
	ctx, span := trace.StartSpan(ctx, "bci.DeletePod")
	defer span.End()

	userUUID := p.getUserUUID(pod.GetNamespace(), pod.GetName())
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      pod.GetName(),
		"podNamespace": pod.GetNamespace(),
		"podUID":       pod.GetUID(),
	}), userUUID)

	if p.podRescuer != nil &&
		p.podRescuer.IsRescuing(ctx, pod.GetNamespace(), pod.GetName(), string(pod.GetUID())) {
		log.G(ctx).Error("pod is being rescued")
		return fmt.Errorf("pod is being rescued")
	}

	bpods, err := p.filterAllBCIPods(ctx, pod)
	if err != nil {
		return err
	}
	if len(bpods) == 0 {
		return errdefs.AsNotFound(fmt.Errorf("DeletePod can't find BCI Pod for %s/%s", pod.Namespace, pod.Name))
	}

	podsToDelete := make([]*bci.DeletePod, 0)
	v2PodsToDelete := make([]*bci.DeletePod, 0)
	for _, bpod := range bpods {
		if bpod.V2 {
			v2PodsToDelete = append(v2PodsToDelete, bci.NewDeletePod(bpod.PodID, p.clusterID))
		} else {
			podsToDelete = append(podsToDelete, bci.NewDeletePod(bpod.PodID, p.clusterID))
		}
	}

	if len(podsToDelete) > 0 {
		args := bci.NewDeleteMultiplePodsArgs(podsToDelete, true) // TODO: should RelatedReleaseFlag be dynamic
		err = wrapError(p.bciClient.DeletePod(ctx, args, p.getSignOption(ctx)))
		if err != nil {
			log.G(ctx).WithField("method", "DeletePod").Errorf("fail to delete pod in bci: %v", err)
			return err
		}
	}
	if len(v2PodsToDelete) > 0 {
		args := bci.NewDeleteMultiplePodsArgs(v2PodsToDelete, true) // TODO: should RelatedReleaseFlag be dynamic
		err = wrapError(p.bciV2Client.DeletePod(ctx, args, p.getSignOption(ctx)))
		if err != nil {
			log.G(ctx).WithField("method", "DeletePod").Errorf("fail to delete v2 pod in bci: %v", err)
			return err
		}
	}

	// notify to delete token
	if p.tokenController != nil {
		p.tokenController.NotifyToken(ctx, "", pod.UID, 0, DeleteTokenType)
	}
	return nil
}

// GetPod retrieves a pod by name from the provider (can be cached).
func (p *BCIProvider) GetPod(ctx context.Context, namespace, name string) (*v1.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "bci.GetPod")
	defer span.End()

	if p.enablePodCache {
		if pod, err := p.podCache.GetPod(ctx, namespace, name); err == nil || errdefs.IsNotFound(err) {
			return pod, err
		}
	}

	userUUID := p.getUserUUID(namespace, name)
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      name,
		"podNamespace": namespace,
	}), userUUID)

	bpod, err := p.filterBCIPod(ctx, namespace, name)
	if err != nil {
		return nil, err
	}
	if bpod == nil {
		return nil, errdefs.AsNotFound(fmt.Errorf("GetPod can't find Pod %s", p.getPodFullName(namespace, name)))
	}

	podDetail, err := p.chooseBCIClient(bpod).DescribePod(ctx, bpod.PodID, p.getSignOption(ctx))
	if err != nil {
		return nil, err
	}

	return bciPodDetailToPod(ctx, podDetail)
}

// GetContainerLogs retrieves the logs of a container by name from the provider.
func (p *BCIProvider) GetContainerLogs(ctx context.Context, namespace, podName, containerName string, opts api.ContainerLogOpts) (io.ReadCloser, error) {
	ctx, span := trace.StartSpan(ctx, "bci.GetContainerLogs")
	defer span.End()

	userUUID := p.getUserUUID(namespace, podName)
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      podName,
		"podNamespace": namespace,
	}), userUUID)

	bpod, err := p.filterBCIPod(ctx, namespace, podName)
	if err != nil {
		return nil, err
	}
	if bpod == nil {
		return nil, errdefs.AsNotFound(fmt.Errorf("GetContainerLogs can't find Pod %s", p.getPodFullName(namespace, podName)))
	}

	logOpts := &bci.LogOptions{
		LimitBytes:   opts.LimitBytes,
		TailLines:    opts.Tail,
		Timestamps:   opts.Timestamps,
		Follow:       false, // opts.Follow,
		Previous:     opts.Previous,
		SinceSeconds: opts.SinceSeconds,
		SinceTime:    opts.SinceTime,
	}
	logInfo, err := p.chooseBCIClient(bpod).GetContainerLog(ctx, bpod.PodID, containerName, logOpts, p.getSignOption(ctx))
	if err != nil {
		return nil, err
	}

	return io.NopCloser(bytes.NewReader(logInfo)), nil
}

// getPodFullName retrieves the full pod name as defined in the provider context.
func (p *BCIProvider) getPodFullName(namespace string, name string) string {
	return fmt.Sprintf("%s-%s", namespace, name)
}

// getUserUUID trys to retrieves userUUID according to pod namespace and name
func (p *BCIProvider) getUserUUID(namespace string, name string) string {
	// bsc style, namespace in xxx-{userUUID}... format
	elems := strings.Split(namespace, "-")
	if len(elems) >= 2 && len(elems[1]) == 32 {
		return elems[1]
	}
	// cce style, pod name in {userUUID}-... format
	elems = strings.Split(name, "-")
	if len(elems[0]) == 32 {
		return elems[0]
	}
	return ""
}

// RunInContainer executes a command in a container in the pod, copying data
// between in/out/err and the container's stdin/stdout/stderr.
func (p *BCIProvider) RunInContainer(ctx context.Context, namespace, podName, containerName string, cmd []string, attach api.AttachIO) error {
	ctx, span := trace.StartSpan(ctx, "bci.RunInContainer")
	defer span.End()

	userUUID := p.getUserUUID(namespace, podName)
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      podName,
		"podNamespace": namespace,
	}), userUUID)

	wssURL, bpod, err := p.launchExecWSSURL(ctx, namespace, podName, containerName, cmd, attach)
	if err != nil {
		return err
	}
	// Connecting to vnc domain with suffix "bce.baidu.com" requires public net connection,
	// so replace its suffix with "baidubce.com" that can be accessed within vpc.
	// See http://wiki.baidu.com/pages/viewpage.action?pageId=**********
	wssURL = strings.Replace(wssURL, "bce.baidu.com", "baidubce.com", -1)
	conn, err := newBciWebsocketConn(ctx, wssURL)
	if err != nil {
		log.G(ctx).WithFields(log.Fields{
			"containerName": containerName,
			"wssURL":        wssURL,
		}).WithError(err).Error("error connecting to wss url")
		return err
	}
	defer conn.Close()

	return p.attachToWSS(ctx, bpod, conn, attach)
}

// GetPodStatus retrieves the status of a pod by name from the provider.
func (p *BCIProvider) GetPodStatus(ctx context.Context, namespace, name string) (*v1.PodStatus, error) {
	ctx, span := trace.StartSpan(ctx, "bci.GetPodStatus")
	defer span.End()

	userUUID := p.getUserUUID(namespace, name)
	ctx = util.WithUserUUID(span.WithFields(ctx, log.Fields{
		"userUUID":     userUUID,
		"podName":      name,
		"podNamespace": namespace,
	}), userUUID)

	pod, err := p.GetPod(ctx, namespace, name)
	if err != nil {
		return nil, err
	}

	if pod == nil {
		return nil, nil
	}

	return &pod.Status, nil
}

// GetPods retrieves a list of all pods running on the provider (can be cached).
func (p *BCIProvider) GetPods(ctx context.Context) ([]*v1.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "bci.GetPods")
	defer span.End()

	if p.enablePodCache {
		return p.podCache.GetPods(ctx)
	}

	allUserUUIDs, err := p.userStorage.GetAllUsers(ctx, p.nodeName)
	if err != nil {
		return nil, err
	}
	if allUserUUIDs == nil {
		// single user case
		allUserUUIDs = []string{""}
	}

	// get pod detail in limited parallel
	allUsersPods := make([][]*v1.Pod, len(allUserUUIDs), len(allUserUUIDs))
	errs := make(chan error, len(allUserUUIDs))
	maxUsersInFlight := 10
	tokens := make(chan struct{}, maxUsersInFlight)
	var wg sync.WaitGroup
	wg.Add(len(allUserUUIDs))
	for i, userUUID := range allUserUUIDs {
		go func(index int, userUUID string) {
			defer wg.Done()
			tokens <- struct{}{}
			defer func() {
				<-tokens
			}()
			ctx = util.WithUserUUID(span.WithField(ctx, "userUUID", userUUID), userUUID)
			pods, err := p.getPodsForSingleUser(ctx)
			if err != nil {
				log.G(ctx).WithError(err).Error("error list bci pod for user=%s", userUUID)
				errs <- fmt.Errorf("list bci pod for user=%s err: %v", userUUID, err)
				return
			}
			allUsersPods[index] = pods
		}(i, userUUID)
	}
	wg.Wait()

	if len(errs) == 0 {
		allPods := make([]*v1.Pod, 0, 0)
		for _, pods := range allUsersPods {
			if len(pods) > 0 {
				allPods = append(allPods, pods...)
			}
		}
		return allPods, nil
	}

	errStr := ""
	failedCnt := len(errs)
	for i := 0; i < failedCnt; i++ {
		errStr += (<-errs).Error() + " ;; "
	}
	log.G(ctx).WithField("failedCnt", failedCnt).Errorf("fail to get pods for all users: %s", errStr)

	return nil, errors.New(errStr)
}

func (p *BCIProvider) getPodsForSingleUser(ctx context.Context) ([]*v1.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "bci.getPods")
	defer span.End()

	bpods, err := p.listMyBCIPods(ctx)
	if err != nil {
		return nil, fmt.Errorf("fail to retrieve bci pods list: %v", err)
	}

	// get pod detail in parallel
	pods := make([]*v1.Pod, len(bpods), len(bpods))
	errs := make(chan error, len(bpods))
	var wg sync.WaitGroup
	for i, bpod := range bpods {
		wg.Add(1)
		go func(index int, bpod *bci.Pod) {
			defer wg.Done()
			podDetail, err := p.chooseBCIClient(bpod).DescribePod(ctx, bpod.PodID, p.getSignOption(ctx))
			if err != nil {
				log.G(ctx).WithFields(log.Fields{
					"name": bpod.Name,
					"id":   bpod.PodID,
				}).WithError(err).Error("error getting pod detailed info")
				errs <- fmt.Errorf("describe pod name=%s id=%s err: %v", bpod.Name, bpod.PodID, err)
				return
			}
			p, err := bciPodDetailToPod(ctx, podDetail)
			if err != nil {
				log.G(ctx).WithFields(log.Fields{
					"name": bpod.Name,
					"id":   bpod.PodID,
				}).WithError(err).Error("error converting bci pod to pod")
				errs <- fmt.Errorf("convert pod name=%s id=%s err: %v", bpod.Name, bpod.PodID, err)
				return
			}
			pods[index] = p
		}(i, bpod)
	}
	wg.Wait()

	if len(errs) == 0 {
		return pods, nil
	}

	errStr := ""
	failedCnt := len(errs)
	for i := 0; i < failedCnt; i++ {
		errStr += (<-errs).Error() + " ; "
	}
	log.G(ctx).WithField("failedCnt", failedCnt).Errorf("fail to get all pods in bci: %s", errStr)

	return nil, errors.New(errStr)
}

// getCapacity returns a resource list with the capacity constraints of the provider.
func (p *BCIProvider) getCapacity(ctx context.Context) v1.ResourceList {
	resourceList := v1.ResourceList{
		v1.ResourceCPU:              resource.MustParse(p.cpu),
		v1.ResourceMemory:           resource.MustParse(p.memory),
		v1.ResourcePods:             resource.MustParse(p.pods),
		IPResource:                  resource.MustParse(p.ips),
		ENIResource:                 resource.MustParse(p.enis),
		v1.ResourceEphemeralStorage: resource.MustParse(p.ephemeralStorage),
	}

	// Support GPU resource for bci v2.
	// Currently the GPU quantity is set the same as cpu.
	if p.createV2 {
		resourceList[ResourceGPU] = resource.MustParse(p.cpu)
	}

	return resourceList
}

// getNodeConditions returns a list of conditions (Ready, OutOfDisk, etc), which is
// polled periodically to update the node status within Kubernetes.
func (p *BCIProvider) getNodeConditions(ctx context.Context) []v1.NodeCondition {
	// TODO: Make these dynamic and augment with custom BCI specific conditions of interest
	return []v1.NodeCondition{
		{
			Type:               "Ready",
			Status:             v1.ConditionTrue,
			LastHeartbeatTime:  metav1.Now(),
			LastTransitionTime: metav1.Now(),
			Reason:             "KubeletReady",
			Message:            "kubelet is ready.",
		},
		{
			Type:               "OutOfDisk",
			Status:             v1.ConditionFalse,
			LastHeartbeatTime:  metav1.Now(),
			LastTransitionTime: metav1.Now(),
			Reason:             "KubeletHasSufficientDisk",
			Message:            "kubelet has sufficient disk space available",
		},
		{
			Type:               "MemoryPressure",
			Status:             v1.ConditionFalse,
			LastHeartbeatTime:  metav1.Now(),
			LastTransitionTime: metav1.Now(),
			Reason:             "KubeletHasSufficientMemory",
			Message:            "kubelet has sufficient memory available",
		},
		{
			Type:               "DiskPressure",
			Status:             v1.ConditionFalse,
			LastHeartbeatTime:  metav1.Now(),
			LastTransitionTime: metav1.Now(),
			Reason:             "KubeletHasNoDiskPressure",
			Message:            "kubelet has no disk pressure",
		},
		{
			Type:               "NetworkUnavailable",
			Status:             v1.ConditionFalse,
			LastHeartbeatTime:  metav1.Now(),
			LastTransitionTime: metav1.Now(),
			Reason:             "RouteCreated",
			Message:            "RouteController created a route",
		},
	}
}

func (p *BCIProvider) getNodeInternalIP(ctx context.Context, configVNodeIP string) error {
	if configVNodeIP == "" {
		// 托管集群托管组件如果设置了环境变量NET_INTERFACE_NAME且能成功获取该接口的ip，则用这个接口的ip作为node ip，否则尝试使用eth1的ip作为node ip
		interfaceName := os.Getenv("NET_INTERFACE_NAME")
		if interfaceName != "" {
			vkIP, err := util.GetIP(interfaceName)
			if err == nil && len(vkIP) > 0 {
				p.internalIP = vkIP
				log.G(ctx).Debugf("%d vk set node ip from %s: %s", interfaceName, p.internalIP)
				return nil
			}
		}

		eth1Ip, err := util.GetIP("eth1")
		if err == nil && len(eth1Ip) > 0 {
			p.internalIP = eth1Ip
			log.G(ctx).Debugf("vk set node ip from eth1: %s", p.internalIP)
			return nil
		} else {
			log.G(ctx).WithError(err).Error("failed to get node ip from eth1")
			return fmt.Errorf("failed to get node ip from eth1: %v", err)
		}
	} else {
		p.internalIP = configVNodeIP
		log.G(ctx).Debugf("%d vk set node ip from configVNodeIP: %s", configVNodeIP)

	}
	return nil
}

// getNodeAddresses returns a list of addresses for the node status
// within Kubernetes.
func (p *BCIProvider) getNodeAddresses(ctx context.Context) []v1.NodeAddress {
	// TODO: Make these dynamic and augment with custom BCI specific conditions of interest
	return []v1.NodeAddress{
		{
			Type:    "InternalIP",
			Address: p.internalIP,
		},
		{
			Type:    "Hostname",
			Address: p.internalIP,
		},
	}
}

// getNodeDaemonEndpoints returns getNodeDaemonEndpoints for the node status
// within Kubernetes.
func (p *BCIProvider) getNodeDaemonEndpoints(ctx context.Context) *v1.NodeDaemonEndpoints {
	return &v1.NodeDaemonEndpoints{
		KubeletEndpoint: v1.DaemonEndpoint{
			Port: p.daemonEndpointPort,
		},
	}
}

// getOperatingSystem returns the operating system that was provided by the config.
func (p *BCIProvider) getOperatingSystem() string {
	return p.operatingSystem
}

// ConfigureNode enables a provider to configure the node object that
// will be used for Kubernetes.
func (p *BCIProvider) ConfigureNode(ctx context.Context, node *v1.Node) {
	node.Status.Capacity = p.getCapacity(ctx)
	node.Status.Allocatable = p.getCapacity(ctx)
	node.Status.Conditions = p.getNodeConditions(ctx)
	node.Status.Addresses = p.getNodeAddresses(ctx)
	node.Status.DaemonEndpoints = *p.getNodeDaemonEndpoints(ctx)
	node.Status.NodeInfo.OperatingSystem = p.getOperatingSystem()
	node.ObjectMeta.Labels["alpha.service-controller.kubernetes.io/exclude-balancer"] = "true"
	if node.ObjectMeta.Annotations == nil {
		node.ObjectMeta.SetAnnotations(make(map[string]string))
	}
	node.ObjectMeta.Annotations["node.alpha.kubernetes.io/advertise-route"] = "false"
}

// listMyBCIPods list all bci pods managed by this vk instance
func (p *BCIProvider) listMyBCIPods(ctx context.Context) ([]*bci.Pod, error) {
	listOpt := bci.NewMarkerListOption(p.clusterID, "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypeCCEID, p.clusterID))
	listID := uuid.NewV4().String()
	var result []*bci.Pod
	for {
		resp, err := p.bciV2Client.ListPods(ctx, listOpt, p.getSignOption(ctx))
		if err != nil {
			return nil, err
		}
		log.G(ctx).WithField("listID", listID).Debugf("%d pods listed with isTruncated=%v", len(resp.Result), resp.IsTruncated)
		result = append(result, p.filterMyPod(resp.Result)...)
		if !resp.IsTruncated {
			break
		}
		listOpt.Marker = resp.NextMarker
	}

	return result, nil
}

// listMyBCIPods list all bci pods managed by this vk instance
func (p *BCIProvider) listBCIPodsForLight(ctx context.Context) ([]*bci.Pod, error) {
	listOpt := bci.NewMarkerListOption(p.clusterID, "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypeCCEID, p.clusterID))
	listID := uuid.NewV4().String()
	var result []*bci.Pod
	for {
		resp, err := p.bciV2Client.ListPodsForLight(ctx, listOpt, p.getSignOption(ctx))
		if err != nil {
			return nil, err
		}
		log.G(ctx).WithField("listID", listID).Debugf("%d pods listed with isTruncated=%v", len(resp.Result), resp.IsTruncated)
		result = append(result, p.filterMyPod(resp.Result)...)
		if !resp.IsTruncated {
			break
		}
		listOpt.Marker = resp.NextMarker
	}
	return result, nil
}

// filterMyPod filter pods managed by this vk instance from a bci pod list
func (p *BCIProvider) filterMyPod(all []*bci.Pod) (my []*bci.Pod) {
	for _, pod := range all {
		if pod.CCEID == p.clusterID && getBCILabelValue(pod, NodeNameLabelKey) == p.nodeName {
			my = append(my, pod)
		}
	}
	return
}

func (p *BCIProvider) searchBCIPodByPodName(ctx context.Context, podName string) ([]*bci.Pod, error) {
	listOpt := bci.NewMarkerListOption(p.clusterID, "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypePodName, podName))
	var result []*bci.Pod
	for {
		resp, err := p.bciV2Client.ListPods(ctx, listOpt, p.getSignOption(ctx))
		if err != nil {
			return nil, err
		}
		result = append(result, resp.Result...)
		if !resp.IsTruncated {
			break
		}
		listOpt.Marker = resp.NextMarker
	}
	return result, nil
}

// filterBCIPod filters the first corresponding bci pod for the kube pod specified by namespace and name.
// Only searches in pods managed by this vk instance
func (p *BCIProvider) filterBCIPod(ctx context.Context, namespace, podName string) (bpod *bci.Pod, err error) {
	allPods, err := p.searchBCIPodByPodName(ctx, p.getPodFullName(namespace, podName))
	if err != nil {
		return
	}
	for _, pod := range allPods {
		if getBCILabelValue(pod, ClusterIDLabelKey) == p.clusterID &&
			getBCILabelValue(pod, NodeNameLabelKey) == p.nodeName &&
			getBCILabelValue(pod, NamespaceLabelKey) == namespace &&
			getBCILabelValue(pod, PodNameLabelKey) == podName {
			bpod = pod
			break
		}
	}
	if bpod == nil {
		err = errdefs.AsNotFound(fmt.Errorf("cannot find bci pod for %s/%s", namespace, podName))
		return
	}
	return
}

// filterAllBCIPods filters all corresponding bci pods related the kube pod.
// Only searches in pods managed by this vk instance
func (p *BCIProvider) filterAllBCIPods(ctx context.Context, pod *v1.Pod) (bpods []*bci.Pod, err error) {
	if pod == nil {
		err = errors.New("pod cannot be nil")
		return
	}
	namespace, podName, uid := pod.GetNamespace(), pod.GetName(), pod.GetUID()
	allPods, err := p.searchBCIPodByPodName(ctx, p.getPodFullName(namespace, podName))
	if err != nil {
		return
	}
	for _, bpod := range allPods {
		if getBCILabelValue(bpod, ClusterIDLabelKey) == p.clusterID &&
			getBCILabelValue(bpod, NodeNameLabelKey) == p.nodeName &&
			getBCILabelValue(bpod, UIDLabelKey) == string(uid) &&
			getBCILabelValue(bpod, NamespaceLabelKey) == namespace &&
			getBCILabelValue(bpod, PodNameLabelKey) == podName {
			bpods = append(bpods, bpod)
		}
	}
	if len(bpods) == 0 {
		err = errdefs.AsNotFound(fmt.Errorf("cannot find bci pod for %s/%s", namespace, podName))
		return
	}
	return
}

// launchExecWSSURL launch wss url from bci
func (p *BCIProvider) launchExecWSSURL(ctx context.Context, namespace, podName, containerName string, cmd []string, attach api.AttachIO) (string, *bci.Pod, error) {
	ctx, span := trace.StartSpan(ctx, "BCIProvider.launchExecWSSURL")
	defer span.End()

	if attach == nil {
		return "", nil, fmt.Errorf("attachIO cannot be nil")
	}

	bpod, err := p.filterBCIPod(ctx, namespace, podName)
	if err != nil {
		log.G(ctx).WithFields(log.Fields{
			"namespace": namespace,
			"podName":   podName,
		}).WithError(err).Error("error getting bci pod id")
		return "", bpod, err
	}

	/* may support setting term size some day
	size := api.TermSize{
		Height: 60,
		Width:  120,
	}

	resize := attach.Resize()
	if resize != nil {
		select {
		case size = <-resize:
		case <-ctx.Done():
			return ctx.Err()
		}
	}
	*/

	args := bci.LaunchExecWSSUrlArgs{
		PodID:          bpod.PodID,
		ContainerName:  containerName,
		TerminalHeight: 0, // size.Height
		TerminalWeight: 0, // size.Weight
		TTY:            attach.TTY(),
		Stdout:         false, // attach.Stdout() != nil
		Stdin:          attach.Stdin() != nil,
		Stderr:         false, // attach.Stderr() != nil
		Command:        cmd,
	}
	wssURL, err := p.chooseBCIClient(bpod).LaunchExecWSSUrl(ctx, &args, p.getSignOption(ctx))
	if err != nil {
		log.G(ctx).WithFields(log.Fields{
			"podID":         bpod.PodID,
			"podName":       podName,
			"containerName": containerName,
		}).WithError(err).Error("error launching wss url")
		return "", bpod, err
	}

	return wssURL, bpod, nil
}

// attachToWSS attach IO to wss
func (p *BCIProvider) attachToWSS(ctx context.Context, bpod *bci.Pod, conn websocketConn, attach api.AttachIO) error {
	ctx, span := trace.StartSpan(ctx, "BCIProvider.attachToWSS")
	ctx = span.WithField(ctx, "wss url", conn.URL())
	defer span.End()

	// stop all goroutines after attachToWSS returns
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	out := attach.Stdout()
	if out != nil {
		defer out.Close()
	}

	heartbeat := func(hbctx context.Context) {
		ticker := p.clock.NewTicker(10 * time.Second)
		defer ticker.Stop()

		pingMsg := []byte{}
		if bpod != nil && bpod.V2 {
			pingMsg = wsEncode([]byte{}, bciPingMsgType)
		}

		for {
			select {
			case <-hbctx.Done():
				return
			case <-ticker.C():
				log.G(hbctx).Debug("wss ping")
				if err := conn.Ping(hbctx, pingMsg); err != nil {
					log.G(hbctx).WithError(err).Info("error occurs in wss ping")
				}
			}
		}
	}

	in := attach.Stdin()
	if in != nil {
		go func() {
			hbctx, cancel := context.WithCancel(ctx)
			defer cancel()
			go heartbeat(hbctx)
			for {
				select {
				case <-ctx.Done():
					return
				default:
				}

				msg := make([]byte, 512)
				n, err := in.Read(msg)
				if err != nil {
					log.G(ctx).WithError(err).Info("error in reading from stdin")
					if errors.Is(err, io.EOF) {
						log.G(ctx).Info("close conn as EOF is reached")
						conn.Close()
					}
					return
				}

				if n > 0 {
					log.G(ctx).Debugf("read data from stdin: %x", msg[:n])
					if err := conn.Write(ctx, msg[:n]); err != nil {
						log.G(ctx).WithError(err).Info("error in writing data to wss")
						return
					}
				}
			}
		}()
	}

	resizeCh := attach.Resize()
	if resizeCh != nil {
		go func() {
			for {
				select {
				case <-ctx.Done():
					return
				case size := <-resizeCh:
					if size.Height == 0 || size.Width == 0 {
						log.G(ctx).WithField("size", size).Error("receive zero resize request, stop resize routine")
						return
					}
					if err := conn.Resize(ctx, size); err != nil {
						log.G(ctx).WithError(err).Errorf("error in resize terminal to %+v", size)
					} else {
						log.G(ctx).Infof("resize terminal to %+v", size)
					}
				}
			}
		}()
	}

	if out != nil {
		for {
			select {
			case <-ctx.Done():
				return ctx.Err()
			default:
			}

			msg, err := conn.Read(ctx)
			if err != nil {
				switch err {
				case errInvalidMsgLength:
					log.G(ctx).WithError(err).Warn("invalid msg length from wss, msg: %x", msg)
					continue
				case errInvalidMsgType:
					log.G(ctx).WithError(err).Warn("unkonwn msg type from wss, msg: %x", msg)
					continue
				case errPongMsgReceived:
					log.G(ctx).Debug("pong msg received")
					continue
				default:
					log.G(ctx).WithError(err).Info("error in reading from wss")
				}
				break
			}

			n, err := out.Write(msg)
			if err != nil {
				log.G(ctx).WithError(err).Info("error in writing to stdout")
				break
			}

			log.G(ctx).Debugf("msg from ws, length: %d, msg: %x", n, msg)
		}
	}

	return ctx.Err()
}

func (p *BCIProvider) getClusterDNSServers(ctx context.Context) ([]string, error) {
	svc, err := p.resourceManager.GetService("kube-dns", "kube-system")
	if err != nil {
		log.G(ctx).Errorf("failed to get kube-dns service, err: %v", err)
		return nil, err
	}

	return []string{svc.Spec.ClusterIP}, nil
}

func (p *BCIProvider) reportPods(ctx context.Context) {
	ticker := time.NewTicker(2 * time.Minute)
	for {
		select {
		case <-ctx.Done():
			log.G(ctx).Info("report pods loop exits")
			return
		case <-ticker.C:
			p.reportPodsToBci(ctx)
		}
	}
}

func (p *BCIProvider) reportPodsToBci(ctx context.Context) {
	// currently only supports v2
	if !p.createV2 {
		return
	}
	log.G(ctx).Info("report pods starts at ", time.Now().Format(timeFormat))
	// list all known pods assigned to this virtual node.
	pods := p.resourceManager.GetPods()
	instances := make([]bci.InstanceInfo, 0)
	clusters := make([]*bci.ClusterInfo, 0)
	clusterName := p.clusterID
	for _, pod := range pods {
		namespace := pod.GetNamespace()
		// filter kube-system ns pods
		if namespace == "kube-system" {
			continue
		}

		podId := ""
		if v, ok := pod.GetAnnotations()[PodIDAnnotationKey]; ok && v != "" {
			podId = v
		}
		status := bci.StatusInfo{
			Phase:  pod.Status.Phase,
			Reason: pod.Status.Reason, // the reason of notfound pod is NotFound
		}
		instance := bci.InstanceInfo{
			InstanceId: podId,
			Name:       pod.GetName(),
			Namespace:  pod.GetNamespace(),
			Status:     status,
		}
		instances = append(instances, instance)
	}

	if len(instances) > 0 {
		cluster := &bci.ClusterInfo{
			ClusterId: clusterName,
			Instances: instances,
		}
		clusters = append(clusters, cluster)
	}

	clusterMap := make(map[string][]*bci.ClusterInfo)
	version := "2"
	if len(clusters) > 0 {
		clusterMap["clusters"] = clusters
		err := p.chooseBCIClient(nil).ReportPods(ctx, clusterMap, version, p.getSignOption(ctx))
		if err != nil {
			log.G(ctx).Errorf("report pods fail, err: %v ", err)
		}
	}
}

func (p *BCIProvider) UpdateConfigMap(ctx context.Context, pod *v1.Pod, configmap *v1.ConfigMap) error {
	ctx, span := trace.StartSpan(ctx, "bci.UpdateConfigMap")
	defer span.End()

	podID, ok := pod.GetAnnotations()[PodIDAnnotationKey]
	if !ok {
		log.G(ctx).Infof("ConfigMapController: pod has no bci pod id annotation, skip")
		return fmt.Errorf("pod has no bci pod id annotation")
	}

	for _, volume := range pod.Spec.Volumes {
		if volume.ConfigMap != nil && volume.ConfigMap.Name == configmap.Name && pod.Namespace == configmap.Namespace {
			// call bci api to update configmap
			var (
				volumeConfigFile = new(bci.VolumeConfigFile)
				ctx              = context.Background()
			)

			volumeConfigFile.DefaultMode = volume.ConfigMap.DefaultMode
			volumeConfigFile.Name = volume.Name

			configMapInfo := &ConfigMapInfo{
				VolumeName:    volume.Name,
				ConfigMapName: volume.ConfigMap.Name,
				Namespace:     pod.Namespace,
				Items:         volume.ConfigMap.Items,
				Optional:      volume.ConfigMap.Optional,
			}

			log.G(ctx).Infof("ConfigMapController: parseConfigMap2ConfigFiles podId: %s, namespace:%s, volume: %s configmap name:%s",
				podID, pod.Namespace, volume.Name, volume.ConfigMap.Name)
			configFiles, err := p.parseConfigMap2ConfigFiles(ctx, configMapInfo, configmap)
			if err != nil {
				log.G(ctx).Errorf("ConfigMapController: parseConfigMap2ConfigFiles podId: %s, namespace:%s, volume: %s configmap name:%s, error: %v",
					podID, pod.Namespace, volume.Name, volume.ConfigMap.Name, err)
			}

			log.G(ctx).Infof("ConfigMapController: parseConfigMap2ConfigFiles podId: %s, namespace:%s, volume: %s configmap name:%s, configfiles: %v",
				podID, pod.Namespace, volume.Name, volume.ConfigMap.Name, configFiles)

			volumeConfigFile.ConfigFiles = append(volumeConfigFile.ConfigFiles, configFiles...)

			// 2. 请求bci更新token对应的configmap
			err = p.chooseBCIClient(nil).UpdateConfigMap(ctx, podID, volumeConfigFile, p.getSignOption(ctx))
			if err != nil {
				log.G(ctx).Errorf("ConfigMapController: UpdateConfigMap podId: %s, namespace:%s, configmap name:%s, error: %v",
					podID, pod.Namespace, volume.ConfigMap.Name, err)
			}
			log.G(ctx).Infof("ConfigMapController: UpdateConfigMap podId: %s, namespace:%s, configmap name:%s success",
				podID, pod.Namespace, volume.ConfigMap.Name, err)
		}
	}
	return nil
}
