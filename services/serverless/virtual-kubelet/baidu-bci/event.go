package baidu_bci

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.cloudfoundry.org/clock"
	"github.com/patrickmn/go-cache"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/record"
	"k8s.io/client-go/tools/reference"
	"k8s.io/kubernetes/pkg/api/legacyscheme"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcm"
)

const lastEventSinkTimestampAnnotationKey = "bci.virtual-kubelet.io/last-event-sink-timestamp"

var eventTypes map[bcm.EventLevel]string = map[bcm.EventLevel]string{
	bcm.EventLevelNotice:  corev1.EventTypeNormal,
	bcm.EventLevelWarning: corev1.EventTypeWarning,
}

type EventSinker interface {
	Run(context.Context)
}

var _ EventSinker = (*eventSinker)(nil)

type eventSinker struct {
	bcmClient     bcm.Interface
	kubeClient    kubernetes.Interface
	eventRecorder record.EventRecorder
	provider      *BCIProvider
	accountID     string

	collectInterval time.Duration // interval between two collections.
	maxWindowSize   time.Duration
	windowOffset    time.Duration
	listPageSize    int64 // pageSize used in bcm event listing.

	// podCache when provider's podCache is disabled.
	involvedPods *cache.Cache

	// sinked is used to de-duplicate event as partial failure may happen in multi-pages listing.
	sinked *cache.Cache

	clock clock.Clock

	// for test case.
	notifyCollectDone chan struct{}
}

type EventSinkerConfig struct {
	NormalSize time.Duration `json:"normalSize,omitempty" valid:"Min(1)"`
	MaxSize    time.Duration `json:"maxSize,omitempty" valid:"Min(1)"`
	Offset     time.Duration `json:"offset,omitempty" valid:"Max(0)"`
}

func newDefaultEventSinkerConfig() *EventSinkerConfig {
	return &EventSinkerConfig{
		NormalSize: 5 * time.Second,
		MaxSize:    15 * time.Second,
		Offset:     -40 * time.Second,
	}
}

func (cfg *EventSinkerConfig) validate() error {
	if cfg == nil {
		return errors.New("config is nil")
	}
	if cfg.NormalSize <= 0 {
		return errors.New("normal size must be greater than zero")
	}
	if cfg.MaxSize <= 0 {
		return errors.New("max size must be greater than zero")
	}
	if cfg.NormalSize > cfg.MaxSize {
		return errors.New("bad window size: normal must be no more than max")
	}
	if cfg.Offset > 0 {
		return errors.New("offset must be no more than zero")
	}
	return nil
}

func NewEventSinker(ctx context.Context, accountID string, bcmClient bcm.Interface, cfg *EventSinkerConfig, er record.EventRecorder, p *BCIProvider) (*eventSinker, error) {
	if err := cfg.validate(); err != nil {
		return nil, fmt.Errorf("bad event sinker config: %w", err)
	}

	es := &eventSinker{
		bcmClient:       bcmClient,
		kubeClient:      p.resourceManager.GetRawClient(),
		eventRecorder:   er,
		provider:        p,
		accountID:       accountID,
		collectInterval: cfg.NormalSize,
		maxWindowSize:   cfg.MaxSize,
		windowOffset:    cfg.Offset,
		listPageSize:    100,
		involvedPods:    cache.New(10*time.Minute, 20*time.Minute),
		clock:           clock.NewClock(),
	}
	// Ensure only events on which no retry will be performed can expire.
	es.sinked = cache.New(2*es.maxWindowSize+120*time.Second, 3*es.maxWindowSize+120*time.Second)
	return es, nil
}

func (es *eventSinker) getLastSink(ctx context.Context) <-chan time.Time {
	ch := make(chan time.Time, 1)
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	if es.kubeClient == nil {
		return ch
	}

	node, err := es.kubeClient.CoreV1().Nodes().Get(ctx, es.provider.nodeName, metav1.GetOptions{})
	if err != nil {
		log.G(ctx).WithError(err).Warn("fail to get node info")
		return ch
	}
	v, ok := node.GetAnnotations()[lastEventSinkTimestampAnnotationKey]
	if !ok {
		log.G(ctx).Info("no node annotation found for key", lastEventSinkTimestampAnnotationKey)
		return ch
	}

	last, err := strconv.ParseInt(v, 10, 64)
	if err != nil {
		log.G(ctx).WithError(err).Warn("ignored malformed node annotation", v)
		return ch
	}

	ch <- time.Unix(last, 0)
	return ch
}

func (es *eventSinker) Run(ctx context.Context) {
	// Get the last sinking timestamp stored in node annotation if possible.
	lastSinkCh := es.getLastSink(ctx)

	ticker := es.clock.NewTicker(es.collectInterval)
	retryTicker := es.clock.NewTicker(time.Second)
	defer ticker.Stop()
	defer retryTicker.Stop()
	var recordedAt time.Time

	retry := false
	start := es.clock.Now().Add(es.windowOffset)
	var end time.Time
	for {
		select {
		case <-ctx.Done():
			log.G(ctx).Info("event sinker exits")
			return

		case lastSink := <-lastSinkCh:
			log.G(ctx).WithField("lastSink", lastSink).Info("fetch events from last to now")
			start = lastSink
			end = es.clock.Now().Add(es.windowOffset)
			if !start.Before(end) {
				log.G(ctx).Warn("lastSink is later than now, ignore last")
				continue
			}
			if d := end.Sub(start); d > time.Minute {
				log.G(ctx).Warn("start from 1m ago due to too long duration since lastSink:", d.String())
				start = end.Add(-time.Minute)
			}
			goto COLLECT

		case <-retryTicker.C():
		}
		if !retry {
			select {
			case <-ticker.C():
			default:
				continue
			}
		}

		retry = false
		end = es.clock.Now().Add(es.windowOffset)
		if end.Sub(start) > es.maxWindowSize {
			start = end.Add(-es.maxWindowSize)
		}

	COLLECT:
		cctx, cancel := context.WithTimeout(context.TODO(), 30*time.Second)
		log.G(cctx).WithFields(log.Fields{
			"start": start.UTC().Format(time.RFC3339),
			"end":   end.UTC().Format(time.RFC3339),
		}).Info("collectMyEvent starts")
		if err := es.collectMyEvent(cctx, start, end); err != nil {
			log.G(cctx).WithFields(log.Fields{
				"start": start.UTC().Format(time.RFC3339),
				"end":   end.UTC().Format(time.RFC3339),
			}).WithError(err).Error("error occurs in collectMyEvent")
			retry = true
		} else {
			start = end
		}

		// Set last sink timestamp if necessary.
		now := es.clock.Now()
		if now.Sub(recordedAt) >= 5*time.Second && es.kubeClient != nil {
			// Start has been set equal to end for successful collection.
			if err := es.recordSink(ctx, start.Unix()); err == nil {
				recordedAt = now
			} else {
				log.G(ctx).WithError(err).Error("fail to record event sink timestamp")
			}
		}

		// Notify one collection has done.
		// Only for test cases.
		if es.notifyCollectDone != nil {
			select {
			case es.notifyCollectDone <- struct{}{}:
			default:
			}
		}
		log.G(cctx).Info("collectMyEvent ends")
		cancel()
	}
}

func (es *eventSinker) recordSink(ctx context.Context, succeededAt int64) error {
	annotation := map[string]string{
		lastEventSinkTimestampAnnotationKey: strconv.FormatInt(succeededAt, 10),
	}
	log.G(ctx).WithField("annotation", annotation).Info("start to annotate event sink timestamp")
	out, err := json.Marshal(annotation)
	if err != nil {
		log.G(ctx).WithError(err).Error("fail to marshal annotation")
		return err
	}
	if _, err := es.kubeClient.CoreV1().Nodes().Patch(ctx, es.provider.nodeName, types.MergePatchType,
		[]byte(`{"metadata":{"annotations":`+string(out)+`}}`), metav1.PatchOptions{}); err != nil {
		log.G(ctx).WithError(err).Error("patch node annotation failed")
		return err
	}
	return nil
}

func (es *eventSinker) collectMyEvent(ctx context.Context, start, end time.Time) (err error) {
	ctx, span := trace.StartSpan(ctx, "eventSinker.collectMyEvent")
	defer span.End()

	ctx = span.WithFields(ctx, log.Fields{
		"start": start.UTC().Format(time.RFC3339),
		"end":   end.UTC().Format(time.RFC3339),
	})

	var pageNo int64 = 1
	var resp *bcm.ListEventsResponse
	var bcmEvents []*bcm.Event
	var sinked, total int64 = 0, 0
	for ; ; pageNo++ {
		resp, err = es.bcmClient.ListEvents(ctx,
			bcm.NewListOption(pageNo, es.listPageSize, start, end, "BCE_BCI", es.provider.region, es.accountID, "", "", ""),
			es.provider.getSignOption(ctx))
		if err != nil {
			log.G(ctx).WithError(err).WithField("page", pageNo).Error("list bcm events failed")
			break
		}
		total = resp.TotalElements
		bcmEvents = append(bcmEvents, resp.Content...)

		if resp.Last {
			break
		}
	}

	// Push event to k8s with reverse order so that earlier event is pushed first.
	for i := len(bcmEvents) - 1; i >= 0; i-- {
		event := bcmEvents[i]
		if _, ok := es.sinked.Get(event.EventID); ok {
			continue
		}

		pod := es.findInvolvedPod(ctx, event)
		if pod == nil {
			continue
		}

		content := make(map[string]string)
		if err := json.Unmarshal([]byte(event.Content), &content); err != nil {
			log.G(ctx).WithError(err).WithField("event", event.EventID).Errorf("fail to parse event content, skip: %s", event.Content)
			continue
		}

		ref, err := reference.GetPartialReference(legacyscheme.Scheme, pod, content["fieldPath"])
		if err != nil {
			log.G(ctx).WithFields(log.Fields{
				"event":     event.EventID,
				"pod":       fmt.Sprintf("%s/%s(%s)", pod.GetNamespace(), pod.GetName(), event.ResourceID),
				"fieldPath": content["fieldPath"],
			}).WithError(err).Error("fail to get ref for pod")
			// Fallback to ref manually on err.
			ref = &corev1.ObjectReference{
				APIVersion: "v1",
				Kind:       "Pod",
				Namespace:  pod.GetNamespace(),
				Name:       pod.GetName(),
				UID:        pod.GetUID(),
				FieldPath:  content["fieldPath"],
			}
		}
		msg := strings.ReplaceAll(content["message"], `"`, "")
		es.eventRecorder.Event(ref, eventTypes[event.EventLevel], content["reason"], msg)

		es.sinked.SetDefault(event.EventID, nil)
		sinked++
	}

	log.G(ctx).Infof("%d events sinked with remote total=%d", sinked, total)
	return
}

func (es *eventSinker) findInvolvedPod(ctx context.Context, event *bcm.Event) *corev1.Pod {
	podID := event.ResourceID
	ctx, span := trace.StartSpan(ctx, "eventSinker.findInvolvedPod")
	defer span.End()

	ctx = span.WithFields(ctx, log.Fields{
		"eventID": event.EventID,
		"podID":   podID,
	})

	if es.provider.enablePodCache {
		pod, err := es.provider.podCache.GetPodByPodID(ctx, podID)
		if err == nil {
			return pod
		}
		// Pod not found may be a common case so debug level log is used here.
		log.G(ctx).WithError(err).Debug("pod not found in podCache")
		return nil
	}

	return es.findInvolvedPodWithoutPodCache(ctx, podID)
}

func (es *eventSinker) findInvolvedPodWithoutPodCache(ctx context.Context, podID string) *corev1.Pod {
	if v, ok := es.involvedPods.Get(podID); ok {
		return v.(*corev1.Pod)
	}
	// only v2 bci pod emits event, so v2Client is used.
	podDetail, err := es.provider.bciV2Client.DescribePod(ctx, podID, es.provider.getSignOption(ctx))
	if err != nil {
		log.G(ctx).WithError(err).Error("describe pod failed")
		return nil
	}
	if podDetail.CCEID != es.provider.clusterID || getBCILabelValue(podDetail.Pod, NodeNameLabelKey) != es.provider.nodeName {
		log.G(ctx).Debugf("ignore pod event not managed by us")
		// Set nil in cache so no more unnecessary query is needed.
		// Use a jittered expiration time to avoid convergent query.
		es.involvedPods.Set(podID, (*corev1.Pod)(nil), Jitter(10*time.Minute, 1.0))
		return nil
	}

	pod, err := bciPodDetailToPod(ctx, podDetail)
	if err != nil {
		log.G(ctx).WithError(err).WithField("podID", podID).Error("fail to parse pod detail")
		return nil
	}
	// Use a jittered expiration time to avoid convergent query.
	es.involvedPods.Set(podID, pod, Jitter(5*time.Minute, 1.0))
	return pod
}
