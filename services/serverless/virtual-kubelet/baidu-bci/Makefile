# init project path
HOMEDIR := $(shell pwd)
OUTDIR  := $(HOMEDIR)/output
# init GOROOT
export GOROOT  := $(HOMEDIR)/../../../../../../../baidu/go-env/go1-13-linux-amd64
# init command params
GO      := $(GOROOT)/bin/go
GOMOD   := $(GO) mod
# use origin GOPATH if set
GOPATH  := $(shell $(GO) env GOPATH)
GOBUILD := $(GO) build
GOTEST  := $(GO) test
GOPKGS  := $$($(GO) list ./...| grep -vE "vendor")
# version
BUILD_VERSION := $(shell git describe --tags --always --dirty="-dev")
BUILD_DATE    := $(shell date -u '+%Y-%m-%d-%H:%M UTC')
VERSION_FLAGS := -ldflags='-X "main.buildVersion=$(BUILD_VERSION)" -X "main.buildTime=$(BUILD_DATE)"'
# make, make all
all: prepare compile package
# make prepare, download dependencies
prepare: prepare-dep
prepare-dep:
	bcloud local -U
	# git config --global http.sslVerify
set-env:
	$(GO) env -w GONOPROXY=\*\*.baidu.com\*\*
	$(GO) env -w GOPROXY=https://goproxy.baidu-int.com,direct
	$(GO) env -w GONOSUMDB=\*
# make compile, go build
compile: build
build: set-env
	$(GOMOD) tidy
	GOOS=linux GOARCH=amd64 CGO_ENABLED=0 $(GOBUILD) -o $(HOMEDIR)/virtual-kubelet.bin -ldflags '-extldflags "-static"' $(VERSION_FLAGS) ./cmd/virtual-kubelet
# make test, test your code
test: test-case
test-case: set-env
	$(GOTEST) -v -cover $(GOPKGS) -count=1
# make package
package: package-bin
package-bin:
	mkdir -p $(OUTDIR)
	mv virtual-kubelet.bin  $(OUTDIR)/
# make clean
clean:
	rm -rf $(OUTDIR)
	rm -rf $(HOMEDIR)/virtual-kubelet.bin
	rm -rf $(GOPATH)/pkg/darwin_amd64
# avoid filename conflict and speed up build
.PHONY: all prepare compile test package clean build
