package baidu_bci

import (
	"encoding/json"
	"fmt"

	v1 "k8s.io/api/core/v1"
)

const PodIPAnnotationKey string = "bci.virtual-kubelet.io/pod-ip-env-names"

type PodIPAnnotationValueType map[string][]string

func NewPodIPAnnotationValue() *PodIPAnnotationValueType {
	v := PodIPAnnotationValueType(make(map[string][]string))
	return &v
}

// appendPodIPEnvName append envName from container specified by containerName to annotations
// if same env name for same container has existed in annotations, do nothing
func (p *BCIProvider) AppendPodIPEnvName(pod *v1.Pod, container *v1.Container, envName string) error {
	if pod == nil {
		return fmt.Errorf("pod is nil")
	}
	if container == nil {
		return fmt.Errorf("container is nil")
	}
	newAnnotations, err := appendPodIPEnvName(pod.GetAnnotations(), container.Name, envName)
	if err != nil {
		return err
	}
	pod.SetAnnotations(newAnnotations)
	return nil
}

func appendPodIPEnvName(annotations map[string]string, containerName, envName string) (map[string]string, error) {
	annotationValue := *NewPodIPAnnotationValue()
	if old, ok := annotations[PodIPAnnotationKey]; ok {
		err := json.Unmarshal([]byte(old), &annotationValue)
		if err != nil {
			return nil, err
		}
		for _, oldEnvName := range annotationValue[containerName] {
			if oldEnvName == envName {
				// envName has already existed, return immediately
				return annotations, nil
			}
		}
		annotationValue[containerName] = append(annotationValue[containerName], envName)
	} else {
		annotationValue[containerName] = []string{envName}
	}

	annotationValueBytes, err := json.Marshal(annotationValue)
	if err != nil {
		return nil, err
	}
	if annotations == nil {
		annotations = make(map[string]string)
	}
	annotations[PodIPAnnotationKey] = string(annotationValueBytes)
	return annotations, nil
}
