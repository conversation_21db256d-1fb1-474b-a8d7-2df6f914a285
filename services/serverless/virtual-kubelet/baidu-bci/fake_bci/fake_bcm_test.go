package fake_bci

import (
	"context"
	"testing"
	"time"

	"gotest.tools/assert"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcm"
)

func TestFakeBCMClient_ListEvents(t *testing.T) {
	type fields struct {
		podIDs []string
	}
	type args struct {
		ctx     context.Context
		opt     *bcm.ListOption
		signOpt *bce.SignOption
	}
	accountID := "00dc1b52d8354d9193536e4dd2c41ae6"
	now, err := time.Parse(time.RFC3339, "2022-11-28T06:45:00Z")
	if err != nil {
		t.Fatal(err)
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *bcm.ListEventsResponse
		wantErr bool
	}{
		// All test cases.
		{
			name: "normal page 1 size 10",
			fields: fields{
				podIDs: []string{"p-c9s3qjro", "p-ddc8s6j4"},
			},
			args: args{
				ctx: context.TODO(),
				opt: bcm.NewListOption(1, 10, now.Add(-5*time.Second), now, "BCE_BCI", "bj", accountID, "", "", ""),
			},
			want: &bcm.ListEventsResponse{
				Content: []*bcm.Event{
					{
						Region:       "bj",
						ResourceID:   "p-c9s3qjro",
						ResourceType: bcm.ResourceTypeInstance,
						EventID:      "p-c9s3qjro-**********",
						EventType:    bcm.EventTypeCCEPodAbnormalEvent,
						EventAlias:   bcm.EventAliasCCEPodAbnormalEvent,
						EventLevel:   bcm.EventLevelNotice,
						AccountID:    accountID,
						ServiceName:  "BCE_BCI",
						Content:      `{"advice":"xxx","info":"xxx","message":"Mock Event p-c9s3qjro-**********","reason":"Mock"}`,
						Timestamp:    now.Format("2006-01-02T15:04:05Z"),
					},
					{
						Region:       "bj",
						ResourceID:   "p-ddc8s6j4",
						ResourceType: bcm.ResourceTypeInstance,
						EventID:      "p-ddc8s6j4-**********",
						EventType:    bcm.EventTypeCCEPodAbnormalEvent,
						EventAlias:   bcm.EventAliasCCEPodAbnormalEvent,
						EventLevel:   bcm.EventLevelNotice,
						AccountID:    accountID,
						ServiceName:  "BCE_BCI",
						Content:      `{"advice":"xxx","info":"xxx","message":"Mock Event p-ddc8s6j4-**********","reason":"Mock"}`,
						Timestamp:    now.Add(-2*time.Second - time.Second/2).Format("2006-01-02T15:04:05Z"),
					},
				},
				PageNo:        1,
				PageSize:      10,
				TotalElements: 2,
				Last:          true,
			},
		},
		{
			name: "normal page 1 size 1",
			fields: fields{
				podIDs: []string{"p-c9s3qjro", "p-ddc8s6j4"},
			},
			args: args{
				ctx: context.TODO(),
				opt: bcm.NewListOption(1, 1, now.Add(-5*time.Second), now, "BCE_BCI", "bj", accountID, "", "", ""),
			},
			want: &bcm.ListEventsResponse{
				Content: []*bcm.Event{
					{
						Region:       "bj",
						ResourceID:   "p-c9s3qjro",
						ResourceType: bcm.ResourceTypeInstance,
						EventID:      "p-c9s3qjro-**********",
						EventType:    bcm.EventTypeCCEPodAbnormalEvent,
						EventAlias:   bcm.EventAliasCCEPodAbnormalEvent,
						EventLevel:   bcm.EventLevelNotice,
						AccountID:    accountID,
						ServiceName:  "BCE_BCI",
						Content:      `{"advice":"xxx","info":"xxx","message":"Mock Event p-c9s3qjro-**********","reason":"Mock"}`,
						Timestamp:    now.Format("2006-01-02T15:04:05Z"),
					},
				},
				PageNo:        1,
				PageSize:      1,
				TotalElements: 2,
				Last:          false,
			},
		},
		{
			name: "normal page 2 size 1",
			fields: fields{
				podIDs: []string{"p-c9s3qjro", "p-ddc8s6j4"},
			},
			args: args{
				ctx: context.TODO(),
				opt: bcm.NewListOption(2, 1, now.Add(-5*time.Second), now, "BCE_BCI", "bj", accountID, "", "", ""),
			},
			want: &bcm.ListEventsResponse{
				Content: []*bcm.Event{
					{
						Region:       "bj",
						ResourceID:   "p-ddc8s6j4",
						ResourceType: bcm.ResourceTypeInstance,
						EventID:      "p-ddc8s6j4-**********",
						EventType:    bcm.EventTypeCCEPodAbnormalEvent,
						EventAlias:   bcm.EventAliasCCEPodAbnormalEvent,
						EventLevel:   bcm.EventLevelNotice,
						AccountID:    accountID,
						ServiceName:  "BCE_BCI",
						Content:      `{"advice":"xxx","info":"xxx","message":"Mock Event p-ddc8s6j4-**********","reason":"Mock"}`,
						Timestamp:    now.Add(-2*time.Second - time.Second/2).Format("2006-01-02T15:04:05Z"),
					},
				},
				PageNo:        2,
				PageSize:      1,
				TotalElements: 2,
				Last:          true,
			},
		},
		{
			name: "normal page 3 size 1",
			fields: fields{
				podIDs: []string{"p-c9s3qjro", "p-ddc8s6j4"},
			},
			args: args{
				ctx: context.TODO(),
				opt: bcm.NewListOption(3, 1, now.Add(-5*time.Second), now, "BCE_BCI", "bj", accountID, "", "", ""),
			},
			want: &bcm.ListEventsResponse{
				Content:       []*bcm.Event{},
				PageNo:        3,
				PageSize:      1,
				TotalElements: 2,
				Last:          true,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := &FakeBCMClient{
				podIDs: tt.fields.podIDs,
			}
			got, err := f.ListEvents(tt.args.ctx, tt.args.opt, tt.args.signOpt)
			if (err != nil) != tt.wantErr {
				t.Errorf("FakeBCMClient.ListEvents() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}
