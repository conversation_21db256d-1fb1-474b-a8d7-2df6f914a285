package fake_bci

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bcm"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

var _ bcm.Interface = (*FakeBCMClient)(nil)

type FakeBCMClient struct {
	podIDs []string
}

func NewFakeBCMClient(ctx context.Context, podIDs ...string) *FakeBCMClient {
	return &FakeBCMClient{
		podIDs: podIDs,
	}
}

func (*FakeBCMClient) SetDebug(bool) {}

func (*FakeBCMClient) PushEvent(ctx context.Context, accountID string, event *bcm.Event, option *bce.SignOption) (*bcm.PushEventResponse, error) {
	return nil, fmt.Errorf("not implemented")
}

func (f *FakeBCMClient) ListEvents(ctx context.Context, opt *bcm.ListOption, signOpt *bce.SignOption) (*bcm.ListEventsResponse, error) {
	if opt == nil {
		return nil, fmt.Errorf("nil opt")
	}
	if err := utils.Valid(opt); err != nil {
		return nil, err
	}
	var events []*bcm.Event
	for _, podID := range f.podIDs {
		events = append(events, &bcm.Event{
			Region:       opt.Region,
			ResourceID:   podID,
			ResourceType: bcm.ResourceTypeInstance,
			EventType:    bcm.EventTypeCCEPodAbnormalEvent,
			EventAlias:   bcm.EventAliasCCEPodAbnormalEvent,
			EventLevel:   bcm.EventLevelNotice,
			AccountID:    opt.AccountID,
			ServiceName:  "BCE_BCI",
		})
	}

	d := opt.EndTime.Sub(opt.StartTime)
	if d <= 0 {
		return nil, fmt.Errorf("invalid start and end")
	}

	interval := d / time.Duration((len(events)))
	for i, e := range events {
		ts := opt.EndTime.Add(-time.Duration(i) * interval)
		e.Timestamp = ts.Format("2006-01-02T15:04:05Z")
		eventID := fmt.Sprintf("%s-%d", e.ResourceID, ts.Unix())
		e.EventID = eventID
		content := map[string]string{
			"reason":  "Mock",
			"message": "Mock Event " + eventID,
			"info":    "xxx",
			"advice":  "xxx",
		}
		contentBytes, err := json.Marshal(content)
		if err != nil {
			return nil, err
		}
		e.Content = string(contentBytes)
	}

	total := int64(len(events))
	start := opt.PageSize * (opt.PageNo - 1)
	end := opt.PageSize * opt.PageNo
	last := true

	switch {
	case total < start:
		events = []*bcm.Event{}
	case total > end:
		events = events[start:end]
		last = false
	default:
		events = events[start:]
	}
	return &bcm.ListEventsResponse{
		Content:       events,
		PageNo:        opt.PageNo,
		PageSize:      opt.PageSize,
		Last:          last,
		TotalElements: total,
	}, nil
}
