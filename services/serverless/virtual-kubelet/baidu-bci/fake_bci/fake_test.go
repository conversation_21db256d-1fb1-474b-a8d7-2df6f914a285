package fake_bci

import (
	"context"
	"testing"
	"time"

	"code.cloudfoundry.org/clock/fakeclock"
	"gotest.tools/assert"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
)

func TestIPPoolNext(t *testing.T) {
	pool := new(ipPool)
	for i := 0; i < 32; i++ {
		t.Log(pool.Next())
	}
}

func TestFakeBCIClient_CreatePod(t *testing.T) {
	testClock := fakeclock.NewFakeClock(time.Now())
	c := &FakeBCIClient{
		pods:        make(map[string]*bciPod),
		nameToPodID: make(map[string]string),
		clock:       testClock,
		userID:      "eca97e148cb74e9683d7b7240829d1ff",
		region:      "bj",
		ipPool:      new(ipPool),
	}
	podNames := []string{"test-pod-0", "test-pod-1", "test-pod-2"}
	podIDs := make(map[string]string, len(podNames))
	ctx := context.TODO()

	// create all
	for _, name := range podNames {
		ret, err := c.CreatePod(ctx, generatePodConfig(name), nil, nil)
		assert.NilError(t, err, "CreatePod "+name)
		podIDs[name] = ret.PodIDs[0]
	}

	// list all

	all, err := c.ListPods(ctx, bci.NewMarkerListOption("cce-unittest", "", "", "", bci.DefaultMaxKeys, nil, nil), nil)
	assert.NilError(t, err, "ListPods")
	assert.Equal(t, len(all.Result), len(podNames), "unexpcted pod count")

	// list one by name keyword
	one, err := c.ListPods(ctx, bci.NewMarkerListOption("cce-unittest", "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypePodName, podNames[0])), nil)
	assert.NilError(t, err, "ListPods")
	assert.Equal(t, one.Result[0].PodID, podIDs[podNames[0]], "unexpcted pod 0 id")
	assert.Equal(t, one.Result[0].Status, bci.PodStatusPending, "unexpcted pod 0 status")

	// delete pending pod
	err = c.DeletePod(ctx, bci.NewDeletePodArgs(one.Result[0].PodID, "cce-unittest", true), nil)
	assert.ErrorContains(t, err, "cannot delete", "DeletePod")

	// check status after podRunningCost
	testClock.Increment(podRunningCost + 1)
	one, err = c.ListPods(ctx, bci.NewMarkerListOption("cce-unittest", "", "", "", bci.DefaultMaxKeys, nil, bci.NewListKeyword(bci.KeywordTypePodName, podNames[0])), nil)
	assert.NilError(t, err, "ListPods")
	assert.Equal(t, one.Result[0].Status, bci.PodStatusRunning, "unexpcted pod 0 status")

	pod0, err := c.DescribePod(ctx, one.Result[0].PodID, nil)
	assert.NilError(t, err, "DescribePod")

	// delete pod 0
	err = c.DeletePod(ctx, bci.NewDeletePodArgs(pod0.PodID, "cce-unittest", true), nil)
	assert.NilError(t, err, "DeletePod")
	all, err = c.ListPods(ctx, bci.NewMarkerListOption("cce-unittest", "", "", "", bci.DefaultMaxKeys, nil, nil), nil)
	assert.NilError(t, err, "ListPods")
	assert.Equal(t, len(all.Result), len(podNames)-1, "unexpcted pod count")
}

func generatePodConfig(name string) *bci.PodConfig {
	return &bci.PodConfig{
		Name:            name,
		RestartPolicy:   bci.RestartPolicyAlways,
		VPCID:           "vpc-unittest",
		VPCUUID:         "1386bfb5-3d04-4282-980f-c475b702155b",
		SubnetID:        "sbn-unittest",
		SecurityGroupID: "g-unittest",
		CCEID:           "cce-unittest",
		LogicalZone:     "zoneA",
		ServiceType:     bci.ServiceTypeBCI,
		ProductType:     bci.ProductTypePostPay,
		PurchaseNum:     1,
		Labels: []bci.PodLabel{
			{
				LabelKey:   "NodeName",
				LabelValue: "bci-virtual-kubelet",
			},
			{
				LabelKey:   "CCEID",
				LabelValue: "cce-unittest",
			},
		},
		Containers: []bci.Container{
			{
				Name: "container01",
				ContainerImageInfo: &bci.ContainerImageInfo{
					ImageAddress: "mysql",
					ImageName:    "mysql",
					ImageVersion: "5.7",
				},
				CPUInCore:  1,
				MemoryInGB: 2,
			},
		},
	}
}

func TestFakeBCIClient_GetUserVersion(t *testing.T) {
	type args struct {
		ctx context.Context
		opt *bce.SignOption
	}
	tests := []struct {
		name    string
		args    args
		want    *bci.UserVersionResponse
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				ctx: context.TODO(),
			},
			want: &bci.UserVersionResponse{
				AccountID: "00dc1b52d8354d9193536e4dd2c41ae6",
				IsV2:      false,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			f := NewFakeBCIClient()
			got, err := f.GetUserVersion(tt.args.ctx, tt.args.opt)
			if (err != nil) != tt.wantErr {
				t.Errorf("FakeBCIClient.GetUserVersion() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}
