package util

import (
	"path"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/kubernetes/scheme"
	corev1client "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/record"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/manager"
)

// FakeResourceManager returns an instance of the resource manager that will return the specified objects when its "GetX" methods are called.
// Objects can be any valid Kubernetes object (corev1.Pod, corev1.ConfigMap, corev1.Secret, ...).
func FakeResourceManager(objects ...runtime.Object) manager.ResourceManager {
	// Create a fake Kubernetes client that will list the specified objects.
	kubeClient := fake.NewSimpleClientset(objects...)
	// Create a shared informer factory from where we can grab informers and listers for pods, configmaps, secrets and services.
	kubeInformerFactory := informers.NewSharedInformerFactory(kubeClient, 30*time.Second)
	// Grab informers for pods, configmaps and secrets.
	pInformer := kubeInformerFactory.Core().V1().Pods()
	mInformer := kubeInformerFactory.Core().V1().ConfigMaps()
	sInformer := kubeInformerFactory.Core().V1().Secrets()
	svcInformer := kubeInformerFactory.Core().V1().Services()
	pvInformer := kubeInformerFactory.Core().V1().PersistentVolumes()
	pvcInformer := kubeInformerFactory.Core().V1().PersistentVolumeClaims()
	scInformer := kubeInformerFactory.Storage().V1().StorageClasses()
	// Start all the required informers.
	go pInformer.Informer().Run(wait.NeverStop)
	go mInformer.Informer().Run(wait.NeverStop)
	go sInformer.Informer().Run(wait.NeverStop)
	go svcInformer.Informer().Run(wait.NeverStop)
	go pvInformer.Informer().Run(wait.NeverStop)
	go pvcInformer.Informer().Run(wait.NeverStop)
	go scInformer.Informer().Run(wait.NeverStop)
	// Wait for the caches to be synced.
	if !cache.WaitForCacheSync(wait.NeverStop, pInformer.Informer().HasSynced, mInformer.Informer().HasSynced, sInformer.Informer().HasSynced, svcInformer.Informer().HasSynced) {
		panic("failed to wait for caches to be synced")
	}
	eb := record.NewBroadcaster()
	eb.StartRecordingToSink(&corev1client.EventSinkImpl{Interface: kubeClient.CoreV1().Events("default")})
	eventRecorder := eb.NewRecorder(scheme.Scheme, corev1.EventSource{Component: path.Join("node-name", "pod-controller")})

	// Create a new instance of the resource manager using the listers for pods, configmaps and secrets.
	r, err := manager.NewResourceManager(pInformer.Lister(), sInformer.Lister(), mInformer.Lister(), svcInformer.Lister(), pvInformer.Lister(), pvcInformer.Lister(), pInformer.Lister(), eventRecorder, kubeClient)
	if err != nil {
		panic(err)
	}
	return r
}
