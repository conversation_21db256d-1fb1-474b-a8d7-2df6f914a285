#!/bin/sh

# launch logrotate
mkdir -p /log-volume/${NODE_NAME}
sed -i "s#NODE_NAME#${NODE_NAME}#" /logrotate/logrotate.conf
if [[ "${LOG_ROTATE_COUNT}" == "" ]]; then
	sed -i "s#LOG_ROTATE_COUNT#4#" /logrotate/logrotate.conf
else
	sed -i "s#LOG_ROTATE_COUNT#${LOG_ROTATE_COUNT}#" /logrotate/logrotate.conf
fi

nohup crond > /var/log/cron 2>&1

/usr/bin/virtual-kubelet $@ 2>&1 | tee -a /log-volume/${NODE_NAME}/bci.log
