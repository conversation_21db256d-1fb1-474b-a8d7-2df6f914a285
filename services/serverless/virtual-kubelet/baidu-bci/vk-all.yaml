apiVersion: v1
kind: ServiceAccount
metadata:
  name: bci-virtual-kubelet
  namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: bci-virtual-kubelet
  namespace: kube-system
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: system:node
subjects:
  - kind: ServiceAccount
    name: bci-virtual-kubelet
    namespace: kube-system
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bci-virtual-kubelet
  namespace: kube-system
  labels:
    app: bci-virtual-kubelet
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bci-virtual-kubelet
  template:
    metadata:
      labels:
        app: bci-virtual-kubelet
    spec:
      serviceAccount: bci-virtual-kubelet
      volumes:
      - name: kube-proxy-config-volume
        hostPath:
          path: /etc/kubernetes
          type: ""
      - name: ca-volume
        hostPath:
          path: /etc/kubernetes/pki/
          type: ""
      - name: cce-plugin-token
        secret:
          defaultMode: 256
          secretName: cce-plugin-token
      - name: log-volume
        hostPath:
          path: /var/log/virtual-kubelet
          type: DirectoryOrCreate
      containers:
      - name: bci-virtual-kubelet
        image: registry.baidubce.com/cce/bci-virtual-kubelet:latest-1.16
        imagePullPolicy: Always
        args: ["--provider", "baidu", "--nodename", "$(NODE_NAME)", "--log-level", "debug"]
        env:
        - name: KUBELET_PORT
          value: "10250"
        - name: VKUBELET_POD_IP
          valueFrom:
            fieldRef:
              fieldPath: status.podIP
        - name: NODE_NAME
          value: yzc-vk-test
        - name: LOG_ROTATE_COUNT
          value: "4"
        - name: VKUBELET_TAINT_KEY
          value: "virtual-kubelet.io/provider"
        - name: VKUBELET_TAINT_VALUE
          value: "baidu"
        - name: VKUBELET_TAINT_EFFECT
          value: "NoSchedule"
        - name: BCI_QUOTA_CPU
          value: "1000"
        - name: BCI_QUOTA_MEMORY
          value: "4Ti"
        - name: BCI_QUOTA_PODS
          value: "1000"
        - name: BCI_QUOTA_IPS
          value: "1000"
        - name: BCI_QUOTA_ENIS
          value: "1000"
        - name: BCI_QUOTA_EPHEMERAL_STORAGE
          value: "40Ti"
        - name: CCE_GATEWAY_ENDPOINT
          value: "cce-gateway.hz.agilecloud.com:8997"
        - name: BCI_REGION
          value: "hz"
        - name: BCI_ENDPOINT
          value: "bcilogic.agilecloud.com:8784"
        - name: BCI_LOGICAL_ZONE
          value: "zoneA"
        - name: CCE_CLUSTER_ID
          value: "c-YiXUPo3Q"
        - name: BCI_VPC_ID
          value: "vpc-w94i3j1muwzu"
        - name: BCI_VPC_UUID
          value: "b3a6e74c-0522-4ef9-a7bf-335fde190e5e"
        - name: BCI_SUBNET_ID
          value: "sbn-6j0wa38near0"
        - name: BCI_SECURITY_GROUP_ID
          value: "g-v6gw7w0rhes6"
        - name: KUBE_PROXY_CONFIG_PATH
          value: /etc/kube-proxy/kube-proxy.conf
        - name: API_SERVER_ENDPOINT
          value: "https://***********:6443"
        - name: APISERVER_CERT_LOCATION
          value: /etc/virtual-kubelet/kubelet.pem
        - name: APISERVER_KEY_LOCATION
          value: /etc/virtual-kubelet/kubelet-key.pem
        - name: APISERVER_CA_CERT_LOCATION
          value: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        # multi-tenant settings, optional
        - name: MULTI_TENANT_MODE
          value: "CCE"
        - name: CCE_AUTH_ENDPOINT
          value: "10.133.65.14:8400"
        - name: CHARGE_APPLICATION
          value: "CCE"
        - name: CHARGE_ACCESS_KEY
          value: "xxx"
        - name: CHARGE_SECRET_KEY
          value: "xxx"
        volumeMounts:
        - name: ca-volume
          mountPath: /etc/virtual-kubelet/
        - name: cce-plugin-token
          mountPath: /var/run/secrets/cce/cce-plugin-token
          readOnly: true
        - name: log-volume
          mountPath: /log-volume
        - name: kube-proxy-config-volume
          mountPath: /etc/kube-proxy/
