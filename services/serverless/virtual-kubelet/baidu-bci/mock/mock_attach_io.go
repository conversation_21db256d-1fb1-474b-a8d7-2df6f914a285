/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  mock_attach_io
 * @Version: 1.0.0
 * @Date: 2019/12/16 下午4:43
 */
package mock

import (
	"io"

	"github.com/virtual-kubelet/virtual-kubelet/node/api"
)

type MockAttachIO struct{}

func (m *MockAttachIO) Stdin() io.Reader {
	return &mockReader{}
}
func (m *MockAttachIO) Stdout() io.WriteCloser {
	return &mockWriteCloser{}
}
func (m *MockAttachIO) Stderr() io.WriteCloser {
	return &mockWriteCloser{}
}
func (m *MockAttachIO) TTY() bool {
	return true
}
func (m *MockAttachIO) Resize() <-chan api.TermSize {
	return nil
}

type mockWriteCloser struct{}

func (m *mockWriteCloser) Write(p []byte) (n int, err error) {
	return len(p), nil
}
func (m *mockWriteCloser) Close() error {
	return nil
}

type mockReader struct{}

func (m *mockReader) Read(p []byte) (n int, err error) {
	return 16, nil
}
