/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  mock_websocket_conn
 * @Version: 1.0.0
 * @Date: 2019/12/16 下午4:44
 */
package mock

import (
	"context"

	"github.com/virtual-kubelet/virtual-kubelet/node/api"
)

type MockWebsocketConn struct{}

func (m *MockWebsocketConn) Close() {}

func (m *MockWebsocketConn) Write(context.Context, []byte) error {
	return nil
}
func (m *MockWebsocketConn) Ping(ctx context.Context, pingMsg []byte) error {
	return nil
}

func (m *MockWebsocketConn) Resize(context.Context, api.TermSize) error {
	return nil
}

func (m *MockWebsocketConn) Read(ctx context.Context) ([]byte, error) {
	return []byte("test"), nil
}

func (m *MockWebsocketConn) URL() string {
	return "test url"
}
