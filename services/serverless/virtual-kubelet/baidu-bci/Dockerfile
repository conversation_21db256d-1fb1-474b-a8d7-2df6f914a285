FROM registry.baidubce.com/cce-public/alphine-go-dumb-init:3.5

COPY docker/logrotate.cron /logrotate/logrotate.cron
COPY docker/logrotate.conf /logrotate/logrotate.conf
COPY docker/entrypoint.sh /usr/bin/entrypoint.sh
COPY virtual-kubelet.bin /usr/bin/virtual-kubelet
RUN chmod +x /usr/bin/entrypoint.sh \
    && chmod +x /logrotate/logrotate.cron \
    && echo "*       *       *       *       *       /logrotate/logrotate.cron" >> /etc/crontabs/root
ENTRYPOINT [ "/usr/bin/dumb-init", "--", "/usr/bin/entrypoint.sh" ]
CMD [ "--help" ]
