package util

import (
	"context"
	"fmt"
	"net"
)

type ContextKeyType string

const (
	ContextKeyUserUUID ContextKeyType = "userUUID"
)

type MultiTenantMode string

const (
	// running in cce meta cluster
	MultiTenantModeCCE MultiTenantMode = "CCE"
	// running in bsc cluster
	MultiTenantModeBSC MultiTenantMode = "BSC"
)

// WithUserUUID returns a context injected with userUUID
// If userUUID is empty, do nothing
func WithUserUUID(ctx context.Context, userUUID string) context.Context {
	if userUUID == "" {
		return ctx
	}
	return context.WithValue(ctx, ContextKeyUserUUID, userUUID)
}

// GetUserUUID extracts userUUID from context
// If userUUID is not set in context, return empty
func GetUserUUID(ctx context.Context) string {
	if ctx != nil {
		if userUUID := ctx.Value(ContextKeyUserUUID); userUUID != nil {
			return ctx.Value(ContextKeyUserUUID).(string)
		}
	}
	return ""
}

// GetIP 根据接口名称获取 IP 地址
func GetIP(interfaceName string) (string, error) {
	// 获取网络接口
	iface, err := net.InterfaceByName(interfaceName)
	if err != nil {
		return "", err
	}

	// 获取接口的地址
	addrs, err := iface.Addrs()
	if err != nil {
		return "", err
	}

	// 遍历地址，查找 IP 地址
	for _, addr := range addrs {
		if ipNet, ok := addr.(*net.IPNet); ok && ipNet.IP != nil {
			// 返回 IP 地址的字符串形式
			return ipNet.IP.String(), nil
		}
	}

	return "", fmt.Errorf("no IP found for interface %s", interfaceName)
}
