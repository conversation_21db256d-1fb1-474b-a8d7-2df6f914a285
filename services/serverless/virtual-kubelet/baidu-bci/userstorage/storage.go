package userstorage

import (
	"context"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/util"
)

const (
	fileStorageBaseDir = "/userstorage"
)

//go:generate mockgen -destination ./mock.go -package userstorage -self_package icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/userstorage icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/userstorage Storage
type Storage interface {
	GetAllUsers(ctx context.Context, nodeName string) ([]string, error)
	AddUser(ctx context.Context, userUUID, nodeName string) error
	RemoveUser(ctx context.Context, userUUID, nodeName string) error
}

func NewStorage(mode util.MultiTenantMode, nodeName string) (Storage, error) {
	switch mode {
	case util.MultiTenantModeCCE, util.MultiTenantModeBSC:
		return NewFileStorage(fileStorageBaseDir, nodeName)
	}
	return NewEmptyStorage()
}
