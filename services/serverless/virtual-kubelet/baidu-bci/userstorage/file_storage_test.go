package userstorage

import (
	"context"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"sync"
	"testing"
)

var testDir = filepath.Join(os.TempDir(), "userstorage_test")
var testNodeName = "test-bci-virtual-kubelet"

func setupTestEnv() {}

func teardownTestEnv() {
	if err := os.RemoveAll(testDir); err != nil {
		panic(err)
	}
}

func TestFileStorage(t *testing.T) {
	setupTestEnv()
	defer teardownTestEnv()

	t.Logf("test dir is %s", testDir)

	var wg sync.WaitGroup

	storage, err := NewFileStorage(testDir, testNodeName)
	if err != nil {
		t.<PERSON>rf("NewFileStorage err = %v, want err = nil", err)
		return
	}

	// init for multiple times
	for i := 0; i < 3; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			_, err := NewFileStorage(testDir, testNodeName)
			if err != nil {
				t.<PERSON><PERSON><PERSON>("%d.NewFileStorage err = %v, wantErr = nil", i, err)
				return
			}
		}()
	}
	wg.Wait()

	t.Log("NewFileStorage test done")

	wantUserUUIDs := []string{"a", "aaa", "bbccdd", "eeee", "asdf"}
	sort.Strings(wantUserUUIDs)

	ctx := context.TODO()
	nodeName := "virtual-kubelet"

	for _, userUUID := range wantUserUUIDs {
		// simultaneous adds
		for i := 0; i < 3; i++ {
			wg.Add(1)
			go func(id string) {
				defer wg.Done()
				err := storage.AddUser(ctx, id, nodeName)
				if err != nil {
					t.Errorf("fileStorage.AddUser(%s) err = %v, wantErr nil", id, err)
					return
				}
			}(userUUID)
		}
	}
	wg.Wait()

	t.Log("fileStorage.AddUser test done")

	allUsers, err := storage.GetAllUsers(ctx, nodeName)
	if err != nil {
		t.Errorf("fileStorage.GetAllUsers() err = %v, wantErr nil", err)
		return
	}
	sort.Strings(allUsers)
	if !reflect.DeepEqual(allUsers, wantUserUUIDs) {
		t.Errorf("fileStorage.GetAllUsers() = %v, want %v", allUsers, wantUserUUIDs)
		return
	}

	t.Log("fileStorage.GetAllUsers test done")

	for _, userUUID := range wantUserUUIDs[0:2] {
		// simultaneous removals
		for i := 0; i < 3; i++ {
			wg.Add(1)
			go func(id string) {
				defer wg.Done()
				err := storage.RemoveUser(ctx, id, nodeName)
				if err != nil {
					t.Errorf("fileStorage.RemoveUser(%s) err = %v, wantErr nil", id, err)
					return
				}
			}(userUUID)
		}
	}
	wg.Wait()

	t.Log("fileStorage.RemoveUser test done")

	allUsers, err = storage.GetAllUsers(ctx, nodeName)
	if err != nil {
		t.Errorf("fileStorage.GetAllUsers() err = %v, wantErr nil", err)
		return
	}
	sort.Strings(allUsers)
	if !reflect.DeepEqual(allUsers, wantUserUUIDs[2:]) {
		t.Errorf("2.fileStorage.GetAllUsers() = %v, want %v", allUsers, wantUserUUIDs)
		return
	}

	t.Log("2.fileStorage.GetAllUsers test done")
}
