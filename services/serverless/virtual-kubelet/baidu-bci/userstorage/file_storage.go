package userstorage

import (
	"context"
	"io/ioutil"
	"os"
	"path/filepath"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/trace"
)

type fileStorage struct {
	baseDir, nodeName string
}

func NewFileStorage(baseDir, nodeName string) (Storage, error) {
	if err := os.MkdirAll(filepath.Join(baseDir, nodeName), 0755); err != nil {
		return nil, err
	}
	return &fileStorage{
		baseDir:  baseDir,
		nodeName: nodeName,
	}, nil
}

func (s *fileStorage) GetAllUsers(ctx context.Context, nodeName string) ([]string, error) {
	ctx, span := trace.StartSpan(ctx, "fileStorage.GetAllUsers")
	defer span.End()

	files, err := ioutil.ReadDir(filepath.Join(s.baseDir, nodeName))
	if err != nil {
		return nil, err
	}

	log.G(ctx).WithField("nodeName", nodeName).Infof("find %d users", len(files))
	all := make([]string, 0, len(files))
	for _, fileInfo := range files {
		all = append(all, fileInfo.Name())
	}
	return all, nil
}

func (s *fileStorage) AddUser(ctx context.Context, userUUID, nodeName string) error {
	if err := os.MkdirAll(filepath.Join(s.baseDir, nodeName, userUUID), 0755); err != nil {
		return err
	}
	return nil
}

func (s *fileStorage) RemoveUser(ctx context.Context, userUUID, nodeName string) error {
	err := os.Remove(filepath.Join(s.baseDir, nodeName, userUUID))
	if err == nil || os.IsNotExist(err) {
		return nil
	}
	return err
}
