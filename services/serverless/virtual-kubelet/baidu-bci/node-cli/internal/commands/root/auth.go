package root

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"reflect"

	"github.com/virtual-kubelet/virtual-kubelet/log"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apiserver/pkg/authentication/authenticator"
	"k8s.io/apiserver/pkg/authentication/authenticatorfactory"
	"k8s.io/apiserver/pkg/authorization/authorizer"
	"k8s.io/apiserver/pkg/authorization/authorizerfactory"
	"k8s.io/apiserver/pkg/server/dynamiccertificates"
	clientset "k8s.io/client-go/kubernetes"
	authenticationclient "k8s.io/client-go/kubernetes/typed/authentication/v1"
	authorizationclient "k8s.io/client-go/kubernetes/typed/authorization/v1"

	kubeletconfig "k8s.io/kubernetes/pkg/kubelet/apis/config"
	"k8s.io/kubernetes/pkg/kubelet/server"
)

type AuthMiddleware struct {
	auth server.AuthInterface
}

func NewAuthMiddleware(ctx context.Context, nodeName types.NodeName, client clientset.Interface, config AuthConfig) (*AuthMiddleware, error) {
	cfg := kubeletconfig.KubeletConfiguration{
		Authentication: kubeletconfig.KubeletAuthentication{
			X509: kubeletconfig.KubeletX509Authentication{
				ClientCAFile: config.AuthN.X509.ClientCAFile,
			},
			Webhook: kubeletconfig.KubeletWebhookAuthentication{
				Enabled:  config.AuthN.Webhook.Enabled,
				CacheTTL: config.AuthN.Webhook.CacheTTL,
			},
			Anonymous: kubeletconfig.KubeletAnonymousAuthentication{
				Enabled: config.AuthN.Anonymous.Enabled,
			},
		},
		Authorization: kubeletconfig.KubeletAuthorization{
			Mode: kubeletconfig.KubeletAuthorizationMode(config.AuthZ.Mode),
			Webhook: kubeletconfig.KubeletWebhookAuthorization{
				CacheAuthorizedTTL:   config.AuthZ.Webhook.CacheAuthorizedTTL,
				CacheUnauthorizedTTL: config.AuthZ.Webhook.CacheUnauthorizedTTL,
			},
		},
	}
	log.G(ctx).Debugf("kubelet auth config: %+v", cfg)
	auth, run, err := BuildAuth(nodeName, client, cfg)
	if err != nil {
		return nil, err
	}

	run(ctx.Done())
	return &AuthMiddleware{
		auth: auth,
	}, nil
}

func (am *AuthMiddleware) Auth(w http.ResponseWriter, req *http.Request) (statusCode int, reason string, err error) {
	ctx := context.TODO()
	info, ok, err := am.auth.AuthenticateRequest(req)
	if err != nil {
		log.G(ctx).WithError(err).Error("unable to authenticate the request due to an error")
		return http.StatusUnauthorized, "Unauthorized", fmt.Errorf("unable to authenticate the request: %w", err)
	}
	if !ok {
		return http.StatusUnauthorized, "Unauthorized", errors.New("Unauthorized")
	}

	// Skip authZ for client certificate authN as APIServer client certs cannot pass authZ currently.
	if req.TLS != nil && len(req.TLS.PeerCertificates) > 0 {
		return http.StatusOK, "", nil
	}

	// Get authorization attributes
	attrs := am.auth.GetRequestAttributes(info.User, req)

	// Authorize
	decision, _, err := am.auth.Authorize(req.Context(), attrs)
	if err != nil {
		log.G(ctx).WithError(err).WithFields(log.Fields{
			"user":        attrs.GetUser().GetName(),
			"verb":        attrs.GetVerb(),
			"resource":    attrs.GetResource(),
			"subresource": attrs.GetSubresource(),
		}).Error("Authorization error")
		msg := fmt.Sprintf("Authorization error (user=%s, verb=%s, resource=%s, subresource=%s)", attrs.GetUser().GetName(), attrs.GetVerb(), attrs.GetResource(), attrs.GetSubresource())
		return http.StatusInternalServerError, msg, err
	}
	if decision != authorizer.DecisionAllow {
		log.G(ctx).WithFields(log.Fields{
			"user":        attrs.GetUser().GetName(),
			"verb":        attrs.GetVerb(),
			"resource":    attrs.GetResource(),
			"subresource": attrs.GetSubresource(),
		}).Info("Forbidden")
		msg := fmt.Sprintf("Forbidden (user=%s, verb=%s, resource=%s, subresource=%s)", attrs.GetUser().GetName(), attrs.GetVerb(), attrs.GetResource(), attrs.GetSubresource())
		return http.StatusForbidden, msg, errors.New("msg")
	}

	return http.StatusOK, "", nil
}

type AuthConfig struct {
	AuthN AuthNConfig
	AuthZ AuthZConfig
}

type AuthNConfig struct {
	X509      X509AuthN
	Webhook   WebhookAuthN
	Anonymous AnonymousAuthN
}

type X509AuthN struct {
	// clientCAFile is the path to a PEM-encoded certificate bundle. If set, any request presenting a client certificate
	// signed by one of the authorities in the bundle is authenticated with a username corresponding to the CommonName,
	// and groups corresponding to the Organization in the client certificate.
	ClientCAFile string
}

type WebhookAuthN struct {
	// enabled allows bearer token authentication backed by the tokenreviews.authentication.k8s.io API
	Enabled bool
	// cacheTTL enables caching of authentication results
	CacheTTL metav1.Duration
}

type AnonymousAuthN struct {
	// enabled allows anonymous requests to the kubelet server.
	// Requests that are not rejected by another authentication method are treated as anonymous requests.
	// Anonymous requests have a username of system:anonymous, and a group name of system:unauthenticated.
	Enabled bool
}

type AuthZMode string

const (
	// AuthorizationModeAlwaysAllow authorizes all authenticated requests
	AuthorizationModeAlwaysAllow AuthZMode = "AlwaysAllow"
	// AuthorizationModeWebhook uses the SubjectAccessReview API to determine authorization
	AuthorizationModeWebhook AuthZMode = "Webhook"
)

type AuthZConfig struct {
	Mode    AuthZMode
	Webhook WebhookAuthZ
}

type WebhookAuthZ struct {
	// cacheAuthorizedTTL is the duration to cache 'authorized' responses from the webhook authorizer.
	CacheAuthorizedTTL metav1.Duration
	// cacheUnauthorizedTTL is the duration to cache 'unauthorized' responses from the webhook authorizer.
	CacheUnauthorizedTTL metav1.Duration
}

// BuildAuth creates an authenticator, an authorizer, and a matching authorizer attributes getter compatible with the kubelet's needs
// It returns AuthInterface, a run method to start internal controllers (like cert reloading) and error.
func BuildAuth(nodeName types.NodeName, client clientset.Interface, config kubeletconfig.KubeletConfiguration) (server.AuthInterface, func(<-chan struct{}), error) {
	// Get clients, if provided
	var (
		tokenClient authenticationclient.TokenReviewInterface
		sarClient   authorizationclient.SubjectAccessReviewInterface
	)
	if client != nil && !reflect.ValueOf(client).IsNil() {
		tokenClient = client.AuthenticationV1().TokenReviews()
		sarClient = client.AuthorizationV1().SubjectAccessReviews()
	}

	authenticator, runAuthenticatorCAReload, err := BuildAuthn(tokenClient, config.Authentication)
	if err != nil {
		return nil, nil, err
	}

	attributes := server.NewNodeAuthorizerAttributesGetter(nodeName)

	authorizer, err := BuildAuthz(sarClient, config.Authorization)
	if err != nil {
		return nil, nil, err
	}

	return server.NewKubeletAuth(authenticator, attributes, authorizer), runAuthenticatorCAReload, nil
}

// BuildAuthn creates an authenticator compatible with the kubelet's needs
func BuildAuthn(client authenticationclient.TokenReviewInterface, authn kubeletconfig.KubeletAuthentication) (authenticator.Request, func(<-chan struct{}), error) {
	var dynamicCAContentFromFile *dynamiccertificates.DynamicFileCAContent
	var err error
	if len(authn.X509.ClientCAFile) > 0 {
		dynamicCAContentFromFile, err = dynamiccertificates.NewDynamicCAContentFromFile("client-ca-bundle", authn.X509.ClientCAFile)
		if err != nil {
			return nil, nil, err
		}
	}

	authenticatorConfig := authenticatorfactory.DelegatingAuthenticatorConfig{
		Anonymous:                          authn.Anonymous.Enabled,
		CacheTTL:                           authn.Webhook.CacheTTL.Duration,
		ClientCertificateCAContentProvider: dynamicCAContentFromFile,
	}

	if authn.Webhook.Enabled {
		if client == nil {
			return nil, nil, errors.New("no client provided, cannot use webhook authentication")
		}
		authenticatorConfig.TokenAccessReviewClient = client
	}

	authenticator, _, err := authenticatorConfig.New()
	if err != nil {
		return nil, nil, err
	}

	return authenticator, func(stopCh <-chan struct{}) {
		if dynamicCAContentFromFile != nil {
			go dynamicCAContentFromFile.Run(1, stopCh)
		}
	}, err
}

// BuildAuthz creates an authorizer compatible with the kubelet's needs
func BuildAuthz(client authorizationclient.SubjectAccessReviewInterface, authz kubeletconfig.KubeletAuthorization) (authorizer.Authorizer, error) {
	switch authz.Mode {
	case kubeletconfig.KubeletAuthorizationModeAlwaysAllow:
		return authorizerfactory.NewAlwaysAllowAuthorizer(), nil

	case kubeletconfig.KubeletAuthorizationModeWebhook:
		if client == nil {
			return nil, errors.New("no client provided, cannot use webhook authorization")
		}
		authorizerConfig := authorizerfactory.DelegatingAuthorizerConfig{
			SubjectAccessReviewClient: client,
			AllowCacheTTL:             authz.Webhook.CacheAuthorizedTTL.Duration,
			DenyCacheTTL:              authz.Webhook.CacheUnauthorizedTTL.Duration,
		}
		return authorizerConfig.New()

	case "":
		return nil, fmt.Errorf("no authorization mode specified")

	default:
		return nil, fmt.Errorf("unknown authorization mode %s", authz.Mode)

	}
}
