// Copyright © 2017 The virtual-kubelet authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package root

import (
	"os"
	"testing"

	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/opts"
)

func Test_newClient(t *testing.T) {
	f, err := os.CreateTemp("", "kubeconfig*")
	if err != nil {
		t.Fatal(err)
	}
	defer os.Remove(f.Name())

	if err := os.WriteFile(f.Name(), []byte(testKubeConfig), 0644); err != nil {
		t.Fatal(err)
	}

	type args struct {
		c *opts.Opts
	}
	tests := []struct {
		name    string
		args    args
		want    *kubernetes.Clientset
		want1   *kubernetes.Clientset
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			args: args{
				c: &opts.Opts{
					KubeConfigPath: f.Name(),
					KubeAPIQPS:     0,
					KubeAPIBurst:   0,
					EventRecordQPS: 0,
					EventBurst:     0,
				},
			},
		},
		{
			name: "negative kube api qps",
			args: args{
				c: &opts.Opts{
					KubeConfigPath: f.Name(),
					KubeAPIQPS:     -1,
					KubeAPIBurst:   0,
					EventRecordQPS: 0,
					EventBurst:     0,
				},
			},
			wantErr: true,
		},
		{
			name: "negative event qps",
			args: args{
				c: &opts.Opts{
					KubeConfigPath: f.Name(),
					KubeAPIQPS:     0,
					KubeAPIBurst:   0,
					EventRecordQPS: -1,
					EventBurst:     0,
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, _, err := newClient(tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("newClient() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}

const testKubeConfig = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVSDlQRVJPMUUwRnZqMnFINU1mb0c4bWhDY2FJd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdIaGNOTWpJeE1qQTVNRGd4T1RBd1doY05Nekl4TWpBMk1EZ3hPVEF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ05WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFNdWhRSnh3em9Vai9ZK3gKdXlvQlFIYy9HM1BQV0V3ZUdtTHRmYjVwSUxoeEhqb0hYZHVBWW4wTnRDNEhuakRjWUFnd0lXWHBSR2VQWVozRwpVS0plMitVdVFDZGtWdnYrNkNRQ0tNKyt2dHdBK0c5MlBaYm40dXllaHA0eHNzVGRGb1pOajZ0UFBzZGJrTlpPCm92NmFJMzNZUnBIRGQzOWdOa28yeENsNkFSeDJuam8vWHRrWnFKSEs0ZW9lZmdPdE9oczhJSnBOZ1JIeEdIWGoKWURkUkk0M3pobEFBU0hRbWJPeXpFSFB1VGJVV25vVWUyVk9LVFBtMmNjU3R3bkd3QVVsaEQ4VDJEamliMVoyWQppR2JKekVQK0J3TWNiOHpUYXpnUVNPNStiaW5wZ0NjWkEvcFArWE1IMWV2eGtDd1J6UmdLYzZRYVRKdmRLYTlLCkVUbFFJQU1DQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHcKSFFZRFZSME9CQllFRkdqdVlSTlFWazdIOHloeGVJbDluSHBjV3BBL01BMEdDU3FHU0liM0RRRUJDd1VBQTRJQgpBUUJsMUgvWmZrSlBObW5sbDlmRlpiQUVXS3NZVEkxUTJRY2xZQlpEQS84em4waklrdDVsanYyWUo2SjR4TmlOCmxjYUk0NWpSUmdqYSsrc1c4NzFNSGVrMnJyblFKMzdjU3Vxd0VZa1FVejQvK0RNNGg4Yk8vckFWZVNYUkxIQUgKUk4rZ2JneGtBZjVwbDMzS0x3eWRUeDBkbG1LSCtLU3ZBTjlBUGkzV21ZWWN3aXNWcG5Kb2FGTkoyOTdrWDd2ZQp6bWJibjI3QWVaZk96NnQ2S3JhWk8xemN5MVNqWnUwOVhVa1NrUWNINUhoUDlPWFVlZXo0VERRYTRhaFJDVkJwCkVUVWR3T1pVZVY2SlpOVFhvYWJLOHppR1NHSVUrRVBaNk95Z0w4SVBiTnpsWnNuclh4Z2ZQRy9oemtKdHpkRDUKYlErOEdEVWJPb2hucmdhRVppYTErQWhYCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    server: https://**************:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: eca97e148cb74e9683d7b7240829d1ff
  name: eca97e148cb74e9683d7b7240829d1ff@kubernetes
current-context: eca97e148cb74e9683d7b7240829d1ff@kubernetes
kind: Config
preferences: {}
users:
- name: eca97e148cb74e9683d7b7240829d1ff
  user:
    client-certificate-data: 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
    client-key-data: ****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`
