// Copyright © 2017 The virtual-kubelet authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package root

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io"
	"io/ioutil"
	"net"
	"net/http"
	"os"
	"path/filepath"
	"time"

	gmux "github.com/gorilla/mux"
	"github.com/pkg/errors"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/node/api"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	kuberestclient "k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/klog/v2"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/opts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/provider"
)

// AcceptedCiphers is the list of accepted TLS ciphers, with known weak ciphers elided
// Note this list should be a moving target.
var AcceptedCiphers = []uint16{
	tls.TLS_ECDHE_ECDSA_WITH_AES_256_CBC_SHA,
	tls.TLS_ECDHE_ECDSA_WITH_AES_128_CBC_SHA,
	tls.TLS_ECDHE_RSA_WITH_AES_256_CBC_SHA,
	tls.TLS_ECDHE_RSA_WITH_AES_128_CBC_SHA,

	tls.TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
	tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
	tls.TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
	tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
}

// 若CA证书路径为空，则默认使用/tmp/kubernates/ca.pem作为CA证书的路径
const DefaultKubeCAPath = "/tmp/kubernates/ca.pem"

func loadTLSConfig(certPath, keyPath string) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(certPath, keyPath)
	if err != nil {
		return nil, errors.Wrap(err, "error loading tls certs")
	}

	return &tls.Config{
		Certificates:             []tls.Certificate{cert},
		MinVersion:               tls.VersionTLS12,
		PreferServerCipherSuites: true,
		CipherSuites:             AcceptedCiphers,
	}, nil
}

func loadKubeConfig(kubeconfigPath string) (*kuberestclient.Config, error) {
	if kubeconfigPath == "" {
		return nil, errors.New("kubeconfig path is empty")
	}

	config, err := clientcmd.BuildConfigFromFlags("", kubeconfigPath)
	if err != nil {
		return nil, errors.New("build config from flags error")
	}
	return config, nil
}

// WriteFileWithDir 检查目录是否存在，如果不存在则创建目录，然后写入文件
func WriteFileWithDir(filePath string, data []byte) error {
	// 获取目录路径
	dir := filepath.Dir(filePath)

	// 检查目录是否存在
	if _, err := os.Stat(dir); os.IsNotExist(err) {
		// 如果目录不存在，创建目录
		err := os.MkdirAll(dir, 0755)
		if err != nil {
			return err
		}
	}

	// 将字节切片写入文件
	return os.WriteFile(filePath, data, 0644)
}

func loadTLSFromKubeconfig(kubeConfig *kuberestclient.Config) (*tls.Config, error) {

	cert, err := tls.X509KeyPair(kubeConfig.CertData, kubeConfig.KeyData)
	if err != nil {
		return nil, errors.Wrap(err, "error loading tls certs")
	}
	return &tls.Config{
		Certificates:             []tls.Certificate{cert},
		MinVersion:               tls.VersionTLS12,
		PreferServerCipherSuites: true,
		CipherSuites:             AcceptedCiphers,
	}, nil
}

func setupClientAuthTLSConfig(config *tls.Config, caCertPath string) error {
	caCertPem, err := ioutil.ReadFile(caCertPath)
	if err != nil {
		return err
	}

	caCertPool := x509.NewCertPool()
	if ok := caCertPool.AppendCertsFromPEM(caCertPem); !ok {
		return errors.New("failed to parse ca pem cert")
	}

	config.ClientAuth = tls.RequestClientCert
	config.ClientCAs = caCertPool
	return nil
}

func setupClientAuthTLSConfigFromKubeConfig(config *tls.Config, kubeConfig *kuberestclient.Config) error {
	caCertPem := kubeConfig.CAData
	caCertPool := x509.NewCertPool()
	if ok := caCertPool.AppendCertsFromPEM(caCertPem); !ok {
		return errors.New("failed to parse ca pem cert")
	}

	config.ClientAuth = tls.RequestClientCert
	config.ClientCAs = caCertPool
	return nil
}

func getTLSConfig(ctx context.Context, cfg *apiServerConfig) (*tls.Config, error) {
	if cfg.CertPath == "" || cfg.KeyPath == "" {
		// from kubeconfig get tls config
		kubeConfig, err := loadKubeConfig(cfg.KubeConfigPath)
		if err != nil {
			log.G(ctx).
				WithField("kubeConfigPath", cfg.KubeConfigPath).
				WithError(err).
				Error("TLS certificates provided from kubeconfig, load kubeconfig fail, not setting up pod http server")
			return nil, err
		}
		tlsCfg, err := loadTLSFromKubeconfig(kubeConfig)
		if err != nil {
			log.G(ctx).
				WithField("certData", string(kubeConfig.CertData)).
				WithField("keyData", string(kubeConfig.KeyData)).
				WithError(err).
				Error("TLS certificates provided CertData and KeyData from kubeconfig, but failed to setup client auth TLS config")
		}
		if err := setupClientAuthTLSConfigFromKubeConfig(tlsCfg, kubeConfig); err != nil {
			log.G(ctx).
				WithField("caData", string(kubeConfig.CAData)).
				WithError(err).
				Error("CA certificate data is provided from kubeconfig, but failed to setup client auth TLS config")
			return nil, err
		}
		// set ca data to default ca cert path
		if err := WriteFileWithDir(DefaultKubeCAPath, kubeConfig.CAData); err != nil {
			log.G(ctx).
				WithError(err).
				Error("failed to write ca cert data to default path")
		}
		// set ca path to auth config
		cfg.AuthConfig.AuthN.X509.ClientCAFile = DefaultKubeCAPath
		return tlsCfg, nil
	} else {
		tlsCfg, err := loadTLSConfig(cfg.CertPath, cfg.KeyPath)
		if err != nil {
			log.G(ctx).
				WithField("crtPath", string(cfg.CertPath)).
				WithField("keyPath", string(cfg.KeyPath)).
				WithError(err).
				Error("CA certificate path is provided, but failed to setup client auth TLS config")
			return nil, err
		}
		if cfg.CACertPath != "" {
			if err := setupClientAuthTLSConfig(tlsCfg, cfg.CACertPath); err != nil {
				log.G(ctx).
					WithField("caCertPath", cfg.CACertPath).
					WithError(err).
					Error("CA certificate path is provided, but failed to setup client auth TLS config")
				return nil, err
			}
		}
		return tlsCfg, nil
	}
}

func setupHTTPServer(ctx context.Context, p provider.Provider, cfg *apiServerConfig) (_ func(), retErr error) {
	var closers []io.Closer
	cancel := func() {
		for _, c := range closers {
			c.Close()
		}
	}
	defer func() {
		if retErr != nil {
			cancel()
		}
	}()

	var cadvisorHandlerFunc api.PodCAdvisorMetricsHandlerFunc
	if mp, ok := p.(provider.PodCAdvisorMetricsProvider); ok {
		cadvisorHandlerFunc = mp.GetCAdvisorMetrics
	}

	var summaryHandlerFunc api.PodStatsSummaryHandlerFunc
	if mp, ok := p.(provider.PodMetricsProvider); ok {
		summaryHandlerFunc = mp.GetStatsSummary
	}

	tlsCfg, err := getTLSConfig(ctx, cfg)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get TLS config")
	}

	l, err := tls.Listen("tcp", cfg.Addr, tlsCfg)
	if err != nil {
		return nil, errors.Wrap(err, "error setting up listener for pod http server")
	}

	log.G(ctx).Info("https server is listening on address: %s", cfg.Addr)
	mux := NewServeMux()
	am, err := NewAuthMiddleware(ctx, types.NodeName(cfg.NodeName), cfg.Client, cfg.AuthConfig)
	if err != nil {
		return nil, errors.Wrap(err, "initialize auth middleware failed")
	}
	mux.Use(func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			status, reason, err := am.Auth(w, r)
			if err != nil {
				log.G(ctx).WithError(err).Error("auth failed")
				w.WriteHeader(status)
				w.Write([]byte(reason))
				return
			}
			// Call the next handler, which can be another middleware in the chain, or the final handler.
			next.ServeHTTP(w, r)
		})
	})
	// Install route of setting klog verbosity.
	mux.HandleFunc("/debug/flags/klogv", StringFlagPutHandler(func(val string) (string, error) {
		var level klog.Level
		if err := level.Set(val); err != nil {
			return "", fmt.Errorf("failed set klog.logging.verbosity %s: %v", val, err)
		}
		return fmt.Sprintf("successfully set klog.logging.verbosity to %s", val), nil
	}))

	podRoutes := api.PodHandlerConfig{
		RunInContainer:        p.RunInContainer,
		GetContainerLogs:      p.GetContainerLogs,
		GetPods:               p.GetPods,
		StreamIdleTimeout:     4 * time.Hour,
		StreamCreationTimeout: 30 * time.Second,
		GetStatsSummary:       summaryHandlerFunc,
		GetCAdvisorMetrics:    cadvisorHandlerFunc,
	}
	api.AttachPodRoutes(podRoutes, mux, true)

	s := &http.Server{
		Handler:   mux,
		TLSConfig: tlsCfg,
	}
	go serveHTTP(ctx, s, l, "pods")
	closers = append(closers, s)

	if cfg.MetricsAddr == "" {
		log.G(ctx).Info("Pod metrics server not setup due to empty metrics address")
	} else {
		l, err := net.Listen("tcp", cfg.MetricsAddr)
		if err != nil {
			return nil, errors.Wrap(err, "could not setup listener for pod metrics http server")
		}

		mux := http.NewServeMux()

		podMetricsRoutes := api.PodMetricsConfig{
			GetStatsSummary: summaryHandlerFunc,
		}
		api.AttachPodMetricsRoutes(podMetricsRoutes, mux)
		s := &http.Server{
			Handler: mux,
		}
		go serveHTTP(ctx, s, l, "pod metrics")
		closers = append(closers, s)
	}

	return cancel, nil
}

// ServerMux wraps *gmux.Router and implments api.ServerMux.
type ServerMux struct {
	*gmux.Router
}

func NewServeMux() *ServerMux {
	return &ServerMux{
		Router: gmux.NewRouter(),
	}
}

func (s *ServerMux) Handle(path string, h http.Handler) {
	s.Router.PathPrefix(path).Handler(h)
}

func serveHTTP(ctx context.Context, s *http.Server, l net.Listener, name string) {
	if err := s.Serve(l); err != nil {
		select {
		case <-ctx.Done():
		default:
			log.G(ctx).WithError(err).Errorf("Error setting up %s http server", name)
		}
	}
	l.Close()
}

type apiServerConfig struct {
	CertPath       string
	KeyPath        string
	CACertPath     string
	Addr           string
	MetricsAddr    string
	KubeConfigPath string

	NodeName   string
	Client     kubernetes.Interface
	AuthConfig AuthConfig
}

func getAPIConfig(c *opts.Opts, client kubernetes.Interface) (*apiServerConfig, error) {
	config := apiServerConfig{
		CertPath:       os.Getenv("APISERVER_CERT_LOCATION"),
		KeyPath:        os.Getenv("APISERVER_KEY_LOCATION"),
		CACertPath:     os.Getenv("APISERVER_CA_CERT_LOCATION"),
		KubeConfigPath: os.Getenv("KUBECONFIG"),
	}

	config.Addr = fmt.Sprintf(":%d", c.ListenPort)
	config.MetricsAddr = c.MetricsAddr

	config.NodeName = c.NodeName
	config.Client = client

	// Initialize AuthConfig.
	// All TTLs are set to corresponding kubelet default.
	config.AuthConfig = AuthConfig{
		AuthN: AuthNConfig{
			X509: X509AuthN{
				ClientCAFile: config.CACertPath,
			},
			Webhook: WebhookAuthN{
				Enabled: true,
				CacheTTL: metav1.Duration{
					Duration: 2 * time.Minute,
				},
			},
			Anonymous: AnonymousAuthN{
				Enabled: false,
			},
		},
		AuthZ: AuthZConfig{
			Mode: AuthorizationModeWebhook,
			Webhook: WebhookAuthZ{
				CacheAuthorizedTTL: metav1.Duration{
					Duration: 5 * time.Minute,
				},
				CacheUnauthorizedTTL: metav1.Duration{
					Duration: 30 * time.Second,
				},
			},
		},
	}

	return &config, nil
}

// StringFlagSetterFunc is a func used for setting string type flag.
type StringFlagSetterFunc func(string) (string, error)

// StringFlagPutHandler wraps an http Handler to set string type flag.
func StringFlagPutHandler(setter StringFlagSetterFunc) http.HandlerFunc {
	return http.HandlerFunc(func(w http.ResponseWriter, req *http.Request) {
		switch {
		case req.Method == "PUT":
			body, err := ioutil.ReadAll(req.Body)
			if err != nil {
				writePlainText(http.StatusBadRequest, "error reading request body: "+err.Error(), w)
				return
			}
			defer req.Body.Close()
			response, err := setter(string(body))
			if err != nil {
				writePlainText(http.StatusBadRequest, err.Error(), w)
				return
			}
			writePlainText(http.StatusOK, response, w)
			return
		default:
			writePlainText(http.StatusNotAcceptable, "unsupported http method", w)
			return
		}
	})
}

// writePlainText renders a simple string response.
func writePlainText(statusCode int, text string, w http.ResponseWriter) {
	w.Header().Set("Content-Type", "text/plain")
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.WriteHeader(statusCode)
	fmt.Fprintln(w, text)
}
