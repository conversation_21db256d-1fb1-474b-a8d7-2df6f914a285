// Copyright © 2017 The virtual-kubelet authors
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package root

import (
	"context"
	"fmt"
	"os"
	"path"
	"strings"
	"time"

	"github.com/kubernetes-csi/csi-lib-utils/leaderelection"
	"github.com/pkg/errors"
	"github.com/spf13/cobra"
	"github.com/virtual-kubelet/virtual-kubelet/errdefs"
	"github.com/virtual-kubelet/virtual-kubelet/log"
	"github.com/virtual-kubelet/virtual-kubelet/node"
	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	kubeinformers "k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	coordinationv1 "k8s.io/client-go/kubernetes/typed/coordination/v1"
	corev1client "k8s.io/client-go/kubernetes/typed/core/v1"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/record"

	baidu_bci "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/manager"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/opts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/provider"
)

// NewCommand creates a new top-level command.
// This command is used to start the virtual-kubelet daemon
func NewCommand(ctx context.Context, name string, s *provider.Store, o *opts.Opts) *cobra.Command {
	cmd := &cobra.Command{
		Use:   name,
		Short: name + " provides a virtual kubelet interface for your kubernetes cluster.",
		Long: name + ` implements the Kubelet interface with a pluggable
backend implementation allowing users to create kubernetes nodes without running the kubelet.
This allows users to schedule kubernetes workloads on nodes that aren't running Kubernetes.`,
		RunE: func(cmd *cobra.Command, args []string) error {
			return runRootCommand(ctx, s, o)
		},
	}

	installFlags(cmd.Flags(), o)
	return cmd
}

func runRootCommand(ctx context.Context, s *provider.Store, c *opts.Opts) error {
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	if ok := provider.ValidOperatingSystems[c.OperatingSystem]; !ok {
		return errdefs.InvalidInputf("operating system %q is not supported", c.OperatingSystem)
	}

	if c.PodSyncWorkers == 0 {
		return errdefs.InvalidInput("pod sync workers must be greater than 0")
	}

	var taint *corev1.Taint
	if !c.DisableTaint {
		var err error
		taint, err = getTaint(c)
		if err != nil {
			return err
		}
	}

	client, eventClient, err := newClient(c)
	if err != nil {
		return err
	}

	// Create a shared informer factory for Kubernetes pods in the current namespace (if specified) and scheduled to the current node.
	podInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(
		client,
		c.InformerResyncPeriod,
		kubeinformers.WithNamespace(c.KubeNamespace),
		kubeinformers.WithTweakListOptions(func(options *metav1.ListOptions) {
			options.FieldSelector = fields.OneTermEqualSelector("spec.nodeName", c.NodeName).String()
		}))
	podInformer := podInformerFactory.Core().V1().Pods()

	// Create a shared informer factory for pods within kube-system namespace.
	systemInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(
		client,
		c.InformerResyncPeriod,
		kubeinformers.WithNamespace("kube-system"),
	)
	systemPodInformer := systemInformerFactory.Core().V1().Pods()

	// Create another shared informer factory for Kubernetes secrets and configmaps (not subject to any selectors).
	scmInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(client, c.InformerResyncPeriod)
	// Create a secret informer and a config map informer so we can pass their listers to the resource manager.
	secretInformer := scmInformerFactory.Core().V1().Secrets()
	configMapInformer := scmInformerFactory.Core().V1().ConfigMaps()
	serviceInformer := scmInformerFactory.Core().V1().Services()
	pvInformer := scmInformerFactory.Core().V1().PersistentVolumes()
	pvcInformer := scmInformerFactory.Core().V1().PersistentVolumeClaims()

	podLister := podInformer.Lister()
	secretLister := secretInformer.Lister()
	configMapLister := configMapInformer.Lister()
	serviceLister := serviceInformer.Lister()
	pvLister := pvInformer.Lister()
	pvcLister := pvcInformer.Lister()
	systemPodLister := systemPodInformer.Lister()

	go podInformerFactory.Start(ctx.Done())
	go systemInformerFactory.Start(ctx.Done())
	go scmInformerFactory.Start(ctx.Done())

	eb := record.NewBroadcaster()
	eb.StartLogging(log.G(ctx).Infof)
	eb.StartRecordingToSink(&corev1client.EventSinkImpl{Interface: eventClient.CoreV1().Events(c.KubeNamespace)})
	eventRecorder := eb.NewRecorder(scheme.Scheme, corev1.EventSource{Component: path.Join(c.NodeName, "pod-controller")})

	rm, err := manager.NewResourceManager(podLister, secretLister, configMapLister, serviceLister,
		pvLister, pvcLister, systemPodLister, eventRecorder, client)
	if err != nil {
		return errors.Wrap(err, "could not create resource manager")
	}

	apiConfig, err := getAPIConfig(c, client)
	if err != nil {
		return err
	}

	initConfig := provider.InitConfig{
		ConfigPath:           c.ProviderConfigPath,
		NodeName:             c.NodeName,
		OperatingSystem:      c.OperatingSystem,
		ResourceManager:      rm,
		DaemonPort:           int32(c.ListenPort),
		InternalIP:           os.Getenv("VKUBELET_POD_IP"),
		KubeClusterDomain:    c.KubeClusterDomain,
		ForceInjectDNSConfig: c.ForceInjectDNSConfig,
		DnsConfigPath:        c.DNSConfigPath,
		AutoInjectKubeProxy:  c.AutoInjectKubeProxy,
	}

	pInit := s.Get(c.Provider)
	if pInit == nil {
		return errors.Errorf("provider %q not found", c.Provider)
	}

	p, err := pInit(initConfig)
	if err != nil {
		return errors.Wrapf(err, "error initializing provider %s", c.Provider)
	}

	ctx = log.WithLogger(ctx, log.G(ctx).WithFields(log.Fields{
		"provider":         c.Provider,
		"operatingSystem":  c.OperatingSystem,
		"node":             c.NodeName,
		"watchedNamespace": c.KubeNamespace,
	}))

	var nodeOpts []node.NodeControllerOpt

	var leaseClient coordinationv1.LeaseInterface
	if c.EnableNodeLease {
		leaseClient = client.CoordinationV1().Leases(corev1.NamespaceNodeLease)
		nodeOpts = append(nodeOpts, node.WithNodeEnableLeaseV1(leaseClient, 0))
	}

	pNode := NodeFromProvider(ctx, c.NodeName, taint, p, c.Version)
	nodeOpts = append(nodeOpts,
		node.WithNodeStatusUpdateErrorHandler(func(ctx context.Context, err error) error {
			if !k8serrors.IsNotFound(err) {
				return err
			}

			log.G(ctx).Debug("node not found")
			newNode := pNode.DeepCopy()
			newNode.ResourceVersion = ""
			_, err = client.CoreV1().Nodes().Create(ctx, newNode, metav1.CreateOptions{})
			if err != nil {
				return err
			}
			log.G(ctx).Debug("created new node")
			return nil
		}))

	nodeRunner, err := node.NewNodeController(
		node.NaiveNodeProvider{},
		pNode,
		client.CoreV1().Nodes(),
		nodeOpts...,
	)
	if err != nil {
		log.G(ctx).Fatal(err)
	}

	pc, err := node.NewPodController(node.PodControllerConfig{
		PodClient:                     client.CoreV1(),
		PodInformer:                   podInformer,
		EventRecorder:                 eventRecorder,
		Provider:                      p,
		SecretInformer:                secretInformer,
		ConfigMapInformer:             configMapInformer,
		ServiceInformer:               serviceInformer,
		PersistentVolumeInformer:      pvInformer,
		PersistentVolumeClaimInformer: pvcInformer,
		EnableServiceLinks:            c.EnableServiceLinks,
	})
	if err != nil {
		return errors.Wrap(err, "error setting up pod controller")
	}

	cmc, err := baidu_bci.NewConfigMapController(p, client, configMapInformer, podInformer, rm)
	if err != nil {
		return errors.Wrap(err, "error setting update configmap controller")
	}

	// run wraps all startup actions as a function for leader election
	run := func(context.Context) {
		cancelHTTP, err := setupHTTPServer(ctx, p, apiConfig)
		if err != nil {
			log.G(ctx).Fatalf("fail to setup http server: %v", err)
		}
		defer cancelHTTP()

		go func() {
			if err := pc.Run(ctx, c.PodSyncWorkers); err != nil && errors.Cause(err) != context.Canceled {
				log.G(ctx).Fatal(err)
			}
		}()

		if c.StartupTimeout > 0 {
			// If there is a startup timeout, it does two things:
			// 1. It causes the VK to shutdown if we haven't gotten into an operational state in a time period
			// 2. It prevents node advertisement from happening until we're in an operational state
			err = waitFor(ctx, c.StartupTimeout, pc.Ready())
			if err != nil {
				log.G(ctx).Fatal(err)
			}
		}

		go func() {
			if err := nodeRunner.Run(ctx); err != nil {
				log.G(ctx).Fatal(err)
			}
		}()

		go func() {
			if err := cmc.Run(ctx); err != nil && errors.Cause(err) != context.Canceled {
				log.G(ctx).Fatal(err)
			}
		}()

		log.G(ctx).Info("Initialized")
		<-ctx.Done()
	}

	// run leader election if necessary
	if !c.EnableLeaderElection {
		run(ctx)
	} else {
		lockName := strings.Replace("vk-baidu-bci-"+c.NodeName, "/", "-", -1)

		var le leaderElection
		if c.EnableNodeLease {
			// use lease object for leader election
			le = leaderelection.NewLeaderElection(client, lockName, run)
		} else {
			// use endpoint object for leader election
			le = leaderelection.NewLeaderElectionWithEndpoints(client, lockName, run)
		}

		if c.LeaderElectionNamespace != "" {
			le.WithNamespace(c.LeaderElectionNamespace)
		} else {
			le.WithNamespace("kube-system")
		}

		if err := le.Run(); err != nil {
			log.G(ctx).Fatalf("failed to initialize leader election: %v", err)
		}
	}

	return nil
}

func waitFor(ctx context.Context, time time.Duration, ready <-chan struct{}) error {
	ctx, cancel := context.WithTimeout(ctx, time)
	defer cancel()

	// Wait for the VK / PC close the the ready channel, or time out and return
	log.G(ctx).Info("Waiting for pod controller / VK to be ready")

	select {
	case <-ready:
		return nil
	case <-ctx.Done():
		return errors.Wrap(ctx.Err(), "Error while starting up VK")
	}
}

func newClient(c *opts.Opts) (*kubernetes.Clientset, *kubernetes.Clientset, error) {
	configPath := c.KubeConfigPath
	var config *rest.Config

	// Check if the kubeConfig file exists.
	if _, err := os.Stat(configPath); !os.IsNotExist(err) {
		// Get the kubeconfig from the filepath.
		config, err = clientcmd.BuildConfigFromFlags("", configPath)
		if err != nil {
			return nil, nil, errors.Wrap(err, "error building client config")
		}
	} else {
		// Set to in-cluster config.
		config, err = rest.InClusterConfig()
		if err != nil {
			return nil, nil, errors.Wrap(err, "error building in cluster config")
		}
	}

	if masterURI := os.Getenv("MASTER_URI"); masterURI != "" {
		config.Host = masterURI
	}

	createOneClient := func(name string, qps, burst, defaultQPS, defaultBurst int32) (*kubernetes.Clientset, error) {
		if qps < 0 || burst < 0 {
			return nil, fmt.Errorf("%s: qps and burst must be no less than zero", name)
		}
		if qps == 0 {
			qps = defaultQPS
		}
		if burst == 0 {
			burst = defaultBurst
		}
		cfg := *config
		cfg.QPS = float32(qps)
		cfg.Burst = int(burst)
		return kubernetes.NewForConfig(&cfg)
	}

	client, err := createOneClient("kube-api", c.KubeAPIQPS, c.KubeAPIBurst, 500, 500)
	if err != nil {
		return nil, nil, err
	}

	eventClient, err := createOneClient("event", c.EventRecordQPS, c.EventBurst, 50, 50)
	if err != nil {
		return nil, nil, err
	}

	return client, eventClient, nil
}

type leaderElection interface {
	Run() error
	WithNamespace(namespace string)
}
