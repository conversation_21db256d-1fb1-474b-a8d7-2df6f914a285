// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/manager (interfaces: ResourceManager)

// Package manager is a generated GoMock package.
package manager

import (
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "k8s.io/api/core/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	kubernetes "k8s.io/client-go/kubernetes"
	record "k8s.io/client-go/tools/record"
)

// MockResourceManager is a mock of ResourceManager interface.
type MockResourceManager struct {
	ctrl     *gomock.Controller
	recorder *MockResourceManagerMockRecorder
}

// MockResourceManagerMockRecorder is the mock recorder for MockResourceManager.
type MockResourceManagerMockRecorder struct {
	mock *MockResourceManager
}

// NewMockResourceManager creates a new mock instance.
func NewMockResourceManager(ctrl *gomock.Controller) *MockResourceManager {
	mock := &MockResourceManager{ctrl: ctrl}
	mock.recorder = &MockResourceManagerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockResourceManager) EXPECT() *MockResourceManagerMockRecorder {
	return m.recorder
}

// GetConfigMap mocks base method.
func (m *MockResourceManager) GetConfigMap(arg0, arg1 string) (*v1.ConfigMap, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigMap", arg0, arg1)
	ret0, _ := ret[0].(*v1.ConfigMap)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigMap indicates an expected call of GetConfigMap.
func (mr *MockResourceManagerMockRecorder) GetConfigMap(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigMap", reflect.TypeOf((*MockResourceManager)(nil).GetConfigMap), arg0, arg1)
}

// GetEventRecorder mocks base method.
func (m *MockResourceManager) GetEventRecorder() record.EventRecorder {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEventRecorder")
	ret0, _ := ret[0].(record.EventRecorder)
	return ret0
}

// GetEventRecorder indicates an expected call of GetEventRecorder.
func (mr *MockResourceManagerMockRecorder) GetEventRecorder() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEventRecorder", reflect.TypeOf((*MockResourceManager)(nil).GetEventRecorder))
}

// GetPersistentVolume mocks base method.
func (m *MockResourceManager) GetPersistentVolume(arg0 string) (*v1.PersistentVolume, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersistentVolume", arg0)
	ret0, _ := ret[0].(*v1.PersistentVolume)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersistentVolume indicates an expected call of GetPersistentVolume.
func (mr *MockResourceManagerMockRecorder) GetPersistentVolume(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersistentVolume", reflect.TypeOf((*MockResourceManager)(nil).GetPersistentVolume), arg0)
}

// GetPersistentVolumeClaim mocks base method.
func (m *MockResourceManager) GetPersistentVolumeClaim(arg0, arg1 string) (*v1.PersistentVolumeClaim, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersistentVolumeClaim", arg0, arg1)
	ret0, _ := ret[0].(*v1.PersistentVolumeClaim)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersistentVolumeClaim indicates an expected call of GetPersistentVolumeClaim.
func (mr *MockResourceManagerMockRecorder) GetPersistentVolumeClaim(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersistentVolumeClaim", reflect.TypeOf((*MockResourceManager)(nil).GetPersistentVolumeClaim), arg0, arg1)
}

// GetPod mocks base method.
func (m *MockResourceManager) GetPod(arg0, arg1 string) (*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPod", arg0, arg1)
	ret0, _ := ret[0].(*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPod indicates an expected call of GetPod.
func (mr *MockResourceManagerMockRecorder) GetPod(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPod", reflect.TypeOf((*MockResourceManager)(nil).GetPod), arg0, arg1)
}

// GetPods mocks base method.
func (m *MockResourceManager) GetPods() []*v1.Pod {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPods")
	ret0, _ := ret[0].([]*v1.Pod)
	return ret0
}

// GetPods indicates an expected call of GetPods.
func (mr *MockResourceManagerMockRecorder) GetPods() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPods", reflect.TypeOf((*MockResourceManager)(nil).GetPods))
}

// GetRawClient mocks base method.
func (m *MockResourceManager) GetRawClient() kubernetes.Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRawClient")
	ret0, _ := ret[0].(kubernetes.Interface)
	return ret0
}

// GetRawClient indicates an expected call of GetRawClient.
func (mr *MockResourceManagerMockRecorder) GetRawClient() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRawClient", reflect.TypeOf((*MockResourceManager)(nil).GetRawClient))
}

// GetSecret mocks base method.
func (m *MockResourceManager) GetSecret(arg0, arg1 string) (*v1.Secret, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecret", arg0, arg1)
	ret0, _ := ret[0].(*v1.Secret)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecret indicates an expected call of GetSecret.
func (mr *MockResourceManagerMockRecorder) GetSecret(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecret", reflect.TypeOf((*MockResourceManager)(nil).GetSecret), arg0, arg1)
}

// GetService mocks base method.
func (m *MockResourceManager) GetService(arg0, arg1 string) (*v1.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetService", arg0, arg1)
	ret0, _ := ret[0].(*v1.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetService indicates an expected call of GetService.
func (mr *MockResourceManagerMockRecorder) GetService(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetService", reflect.TypeOf((*MockResourceManager)(nil).GetService), arg0, arg1)
}

// ListServices mocks base method.
func (m *MockResourceManager) ListServices() ([]*v1.Service, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServices")
	ret0, _ := ret[0].([]*v1.Service)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServices indicates an expected call of ListServices.
func (mr *MockResourceManagerMockRecorder) ListServices() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServices", reflect.TypeOf((*MockResourceManager)(nil).ListServices))
}

// ListSystemPods mocks base method.
func (m *MockResourceManager) ListSystemPods(arg0 labels.Selector) ([]*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListSystemPods", arg0)
	ret0, _ := ret[0].([]*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListSystemPods indicates an expected call of ListSystemPods.
func (mr *MockResourceManagerMockRecorder) ListSystemPods(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListSystemPods", reflect.TypeOf((*MockResourceManager)(nil).ListSystemPods), arg0)
}
