/**
 * @Author: pansiyuan02
 * @Description:
 * @File:  kube_proxy
 * @Version: 1.0.0
 * @Date: 2020/8/18 5:22 下午
 */
package baidu_bci

import (
	"context"
	"encoding/base64"
	"fmt"
	"io/ioutil"
	"os"
	"path/filepath"
	"strconv"

	"github.com/virtual-kubelet/virtual-kubelet/log"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
)

const (
	KUBEProxyEnabledAnnotationKey     = "bci.virtual-kubelet.io/kube-proxy-enabled"
	KUBEProxyHealthzPortAnnotationKey = "bci.virtual-kubelet.io/kube-proxy-healthz-port"
	KUBEProxyContainerNamePrefix      = "kube-proxy-"

	KUBEProxyCPU        = 0.25 // 250m
	KUBEProxyMemory     = 0.5  // 0.5GB
	KUBEProxyConfigPath = "/conf/kube-proxy.conf"

	KUBEProxyConfigVolumeNamePrefix = "kube-proxy-config-"

	ClusterCIDREnvName         = "CLUSTER_CIDR"
	KUBEProxyConfigPathEnvName = "KUBE_PROXY_CONFIG_PATH"
	APIServerEndpointEnvName   = "API_SERVER_ENDPOINT"
	KUBEProxyPodIPEnvName      = "MY_IP"

	NetworkPrivilegeLabelKey = "vk.bci.baidu.com/net-admin"
)

var (
	clusterCIDR                  string
	kubeProxyConfigContentBase64 string
	apiServerEndpoint            string

	KUBEProxyImage = "registry.baidubce.com/serverless-k8s/kube-proxy-amd64-with-iptables-ensurer:v1.16.8"
	// KUBEProxyImageV2 = "registry.baidubce.com/serverless-k8s/kube-proxy-amd64-with-iptables-ensurer:v1.16.8-v2"
	KUBEProxyImageV2 = "registry.baidubce.com/cce-plugin-pro/kube-proxy-amd64-with-iptables-ensurer:v1.16.8-v2-**********"
)

func init() {
	clusterCIDR = os.Getenv(ClusterCIDREnvName)
	apiServerEndpoint = os.Getenv(APIServerEndpointEnvName)
	kubeProxyConfigPath := os.Getenv(KUBEProxyConfigPathEnvName)
	content, err := ioutil.ReadFile(kubeProxyConfigPath)
	if err != nil {
		log.G(context.Background()).WithError(err).Errorf("fail to read cluster kubeconfig from path: %s", kubeProxyConfigPath)
	}
	// ignore error
	if content != nil {
		kubeProxyConfigContentBase64 = base64.StdEncoding.EncodeToString(content)
	}

	if v := os.Getenv("KUBE_PROXY_IMAGE"); v != "" {
		log.G(context.Background()).Infof("set kube proxy image to %s", v)
		KUBEProxyImage = v
	}

	if v := os.Getenv("KUBE_PROXY_IMAGE_V2"); v != "" {
		log.G(context.Background()).Infof("set kube proxy image v2 to %s", v)
		KUBEProxyImageV2 = v
	}
}

func injectKUBEProxy(ctx context.Context, cfg *bci.PodConfig, randToken string, healthzPort int64,
	annotations map[string]interface{}, v2 bool, serviceMeshEnable bool) (string, map[string]interface{}, error) {
	if healthzPort > 65535 || healthzPort < 1 {
		return "", annotations, fmt.Errorf("invalid healthz port %d", healthzPort)
	}
	kubeProxyContainerName := KUBEProxyContainerNamePrefix + randToken
	image := KUBEProxyImage
	if v2 {
		image = KUBEProxyImageV2
	}
	conEnvs := []bci.Env{{
		Key:   KUBEProxyPodIPEnvName,
		Value: "127.0.0.1",
	}}

	if serviceMeshEnable {
		conEnvs = append(conEnvs, bci.Env{
			Key:   "SERVICEMESH_ENABLE",
			Value: "true",
		})
	}
	cfg.Containers = append(cfg.Containers, bci.Container{
		Name:               kubeProxyContainerName,
		ContainerImageInfo: getContainerImageInfo(image),
		MemoryInGB:         KUBEProxyMemory,
		CPUInCore:          KUBEProxyCPU,
		WorkingDir:         "",
		ImagePullPolicy:    bci.PullAlways,
		Commands:           []string{"/bin/sh"},
		Args:               getKUBEProxyArgs(clusterCIDR, KUBEProxyConfigPath, apiServerEndpoint, healthzPort, v2),
		VolumeMounts: []bci.VolumeMount{
			{
				Name:      KUBEProxyConfigVolumeNamePrefix + randToken,
				MountPath: filepath.Dir(KUBEProxyConfigPath),
			},
		},
		Envs: conEnvs,
	})

	cfg.Volumes.ConfigFile = append(cfg.Volumes.ConfigFile, bci.VolumeConfigFile{
		Name: KUBEProxyConfigVolumeNamePrefix + randToken,
		ConfigFiles: []bci.ConfigFile{
			{
				Path: filepath.Base(KUBEProxyConfigPath),
				File: kubeProxyConfigContentBase64,
			},
		},
	})

	// kube-proxy启动参数需要使用自身IP
	if _, found := annotations[PodIPAnnotationKey]; !found {
		annotations[PodIPAnnotationKey] = NewPodIPAnnotationValue()
	}
	podIPEnvs := *annotations[PodIPAnnotationKey].(*PodIPAnnotationValueType)
	podIPEnvs[kubeProxyContainerName] = []string{
		KUBEProxyPodIPEnvName,
	}
	annotations[PodIPAnnotationKey] = &podIPEnvs

	return kubeProxyContainerName, annotations, nil
}

func getKUBEProxyArgs(clusterCIDR, kubeConfigPath, masterEndpoint string, healthzPort int64, v2 bool) []string {
	args := []string{
		"-c",
		fmt.Sprintf("iptables-ensurer && kube-proxy --bind-address=$%s --cluster-cidr=%s --proxy-mode=iptables --masquerade-all=false --hostname-override=$%s --kubeconfig=%s --master=%s --healthz-bind-address=127.0.0.1 --healthz-port=%d --logtostderr=true --v=6",
			KUBEProxyPodIPEnvName, clusterCIDR, KUBEProxyPodIPEnvName, kubeConfigPath, masterEndpoint, healthzPort),
	}

	if !v2 {
		return args
	}
	// Run kube-proxy in user ns for v2 bci.
	args[1] = args[1] + " --conntrack-tcp-timeout-established=0s --conntrack-tcp-timeout-close-wait=0s --conntrack-max-per-core=0"
	return args
}

func getKubeProxyHealthzPort(annotations map[string]string) int64 {
	var healthzPort int64 = 10256
	if v, ok := annotations[KUBEProxyHealthzPortAnnotationKey]; ok && v != "" {
		if port, err := strconv.ParseInt(v, 10, 64); err == nil {
			if port > 0 && port < 65536 {
				healthzPort = port
			}
		}
	}
	return healthzPort
}
