// package iptables-ensurer is meant to work within kube-proxy sidecar container for bci pods.
// If bci supports init container in the future, iptables-ensurer can be run within an init container.
// It ensures the iptables chains created by kubelet originally is present before kube-proxy is started, or iptables-restore may fail.
// The main logic of this file can be found at k8s.io/kubernetes/pkg/kubelet/kubelet_network_linux.go.
// Similiar issue: https://github.com/kubernetes/kubernetes/issues/80462.
package main

import (
	"fmt"

	"k8s.io/klog/v2"
	utiliptables "k8s.io/kubernetes/pkg/util/iptables"
	utilexec "k8s.io/utils/exec"
)

const (
	// KubeMarkMasqChain is the mark-for-masquerade chain
	// TODO: clean up this logic in kube-proxy
	KubeMarkMasqChain utiliptables.Chain = "KUBE-MARK-MASQ"

	// KubeMarkDropChain is the mark-for-drop chain
	KubeMarkDropChain utiliptables.Chain = "KUBE-MARK-DROP"

	// KubePostroutingChain is kubernetes postrouting rules
	KubePostroutingChain utiliptables.Chain = "KUBE-POSTROUTING"

	// KubeFirewallChain is kubernetes firewall rules
	KubeFirewallChain utiliptables.Chain = "KUBE-FIREWALL"
	KubeServiceChain  utiliptables.Chain = "KUBE-SERVICES"
)

func main() {
	// TODO: should the args here be variable?
	iptablesMasqueradeBit := 14           // from IPTablesMasqueradeBit default value
	iptablesDropBit := 15                 // from IPTablesDropBit default value
	protocol := utiliptables.ProtocolIPv4 // from kubelet default
	syncNetworkUtil(iptablesMasqueradeBit, iptablesDropBit, protocol)
}

// syncNetworkUtil is copied from pkg/kubelet/kubelet_network_linux.go with log and error handler modified.
func syncNetworkUtil(iptablesMasqueradeBit, iptablesDropBit int, protocol utiliptables.Protocol) {
	iptClient := utiliptables.New(utilexec.New(), protocol)

	if iptablesMasqueradeBit < 0 || iptablesMasqueradeBit > 31 {
		panic(fmt.Sprintf("invalid iptables-masquerade-bit %v not in [0, 31]", iptablesMasqueradeBit))
		return
	}

	if iptablesDropBit < 0 || iptablesDropBit > 31 {
		panic(fmt.Sprintf("invalid iptables-drop-bit %v not in [0, 31]", iptablesDropBit))
		return
	}

	if iptablesDropBit == iptablesMasqueradeBit {
		panic(fmt.Sprintf("iptables-masquerade-bit %v and iptables-drop-bit %v must be different", iptablesMasqueradeBit, iptablesDropBit))
		return
	}

	// Setup KUBE-MARK-DROP rules
	dropMark := getIPTablesMark(iptablesDropBit)
	if _, err := iptClient.EnsureChain(utiliptables.TableNAT, KubeMarkDropChain); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s exists: %v", utiliptables.TableNAT, KubeMarkDropChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Append, utiliptables.TableNAT, KubeMarkDropChain, "-j", "MARK", "--set-xmark", dropMark); err != nil {
		panic(fmt.Sprintf("Failed to ensure marking rule for %v: %v", KubeMarkDropChain, err))
		return
	}
	if _, err := iptClient.EnsureChain(utiliptables.TableFilter, KubeFirewallChain); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s exists: %v", utiliptables.TableFilter, KubeFirewallChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Append, utiliptables.TableFilter, KubeFirewallChain,
		"-m", "comment", "--comment", "kubernetes firewall for dropping marked packets",
		"-m", "mark", "--mark", dropMark,
		"-j", "DROP"); err != nil {
		panic(fmt.Sprintf("Failed to ensure rule to drop packet marked by %v in %v chain %v: %v", KubeMarkDropChain, utiliptables.TableFilter, KubeFirewallChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Prepend, utiliptables.TableFilter, utiliptables.ChainOutput, "-j", string(KubeFirewallChain)); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s jumps to %s: %v", utiliptables.TableFilter, utiliptables.ChainOutput, KubeFirewallChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Prepend, utiliptables.TableFilter, utiliptables.ChainInput, "-j", string(KubeFirewallChain)); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s jumps to %s: %v", utiliptables.TableFilter, utiliptables.ChainInput, KubeFirewallChain, err))
		return
	}

	// Setup KUBE-MARK-MASQ rules
	masqueradeMark := getIPTablesMark(iptablesMasqueradeBit)
	if _, err := iptClient.EnsureChain(utiliptables.TableNAT, KubeMarkMasqChain); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s exists: %v", utiliptables.TableNAT, KubeMarkMasqChain, err))
		return
	}
	if _, err := iptClient.EnsureChain(utiliptables.TableNAT, KubePostroutingChain); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s exists: %v", utiliptables.TableNAT, KubePostroutingChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Append, utiliptables.TableNAT, KubeMarkMasqChain, "-j", "MARK", "--set-xmark", masqueradeMark); err != nil {
		panic(fmt.Sprintf("Failed to ensure marking rule for %v: %v", KubeMarkMasqChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Prepend, utiliptables.TableNAT, utiliptables.ChainPostrouting,
		"-m", "comment", "--comment", "kubernetes postrouting rules", "-j", string(KubePostroutingChain)); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s jumps to %s: %v", utiliptables.TableNAT, utiliptables.ChainPostrouting, KubePostroutingChain, err))
		return
	}
	// Establish the masquerading rule.
	// NB: THIS MUST MATCH the corresponding code in the iptables and ipvs
	// modes of kube-proxy
	masqRule := []string{
		"-m", "comment", "--comment", "kubernetes service traffic requiring SNAT",
		"-m", "mark", "--mark", masqueradeMark,
		"-j", "MASQUERADE",
	}
	if iptClient.HasRandomFully() {
		masqRule = append(masqRule, "--random-fully")
		klog.V(3).Info("Using `--random-fully` in the MASQUERADE rule for iptables")
	} else {
		klog.V(2).Info("Not using `--random-fully` in the MASQUERADE rule for iptables because the local version of iptables does not support it")
	}
	if _, err := iptClient.EnsureRule(utiliptables.Append, utiliptables.TableNAT, KubePostroutingChain, masqRule...); err != nil {
		panic(fmt.Sprintf("Failed to ensure SNAT rule for packets marked by %v in %v chain %v: %v", KubeMarkMasqChain, utiliptables.TableNAT, KubePostroutingChain, err))
		return
	}

	// KUBE-SERVICES chain ensure first
	if _, err := iptClient.EnsureChain(utiliptables.TableNAT, KubeServiceChain); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s exists: %v", utiliptables.TableNAT, KubeServiceChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Append, utiliptables.TableNAT, utiliptables.ChainPrerouting,
		"-m", "comment", "--comment", "kubernetes service portals", "-j", string(KubeServiceChain)); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s jumps to %s: %v", utiliptables.TableNAT, utiliptables.ChainPrerouting, KubeServiceChain, err))
		return
	}
	if _, err := iptClient.EnsureRule(utiliptables.Append, utiliptables.TableNAT, utiliptables.ChainOutput,
		"-m", "comment", "--comment", "kubernetes service portals", "-j", string(KubeServiceChain)); err != nil {
		panic(fmt.Sprintf("Failed to ensure that %s chain %s jumps to %s: %v", utiliptables.TableNAT, utiliptables.ChainOutput, KubeServiceChain, err))
		return
	}

}

// getIPTablesMark returns the fwmark given the bit
func getIPTablesMark(bit int) string {
	value := 1 << uint(bit)
	return fmt.Sprintf("%#08x/%#08x", value, value)
}
