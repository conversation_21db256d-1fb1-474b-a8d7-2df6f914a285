package baidu_bci

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"os"
	"path/filepath"
	"reflect"
	"sort"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	"gotest.tools/assert"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/node-cli/manager"
	testutil "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci/test/util"
)

func Test_getContainerImageInfo(t *testing.T) {
	type args struct {
		image string
	}
	tests := []struct {
		name string
		args args
		want *bci.ContainerImageInfo
	}{
		// TODO: Add test cases.
		{
			name: "hub.baidubce.com case",
			args: args{
				image: "hub.baidubce.com/dib/dib-agent-online:1-0-0-20190222160907",
			},
			want: &bci.ContainerImageInfo{
				ImageAddress: "hub.baidubce.com/dib/dib-agent-online",
				ImageName:    "dib-agent-online",
				ImageVersion: "1-0-0-20190222160907",
			},
		},
		{
			name: "docker.io case",
			args: args{
				image: "mysql:5.7",
			},
			want: &bci.ContainerImageInfo{
				ImageAddress: "mysql",
				ImageName:    "mysql",
				ImageVersion: "5.7",
			},
		},
		{
			name: "docker.io without version case",
			args: args{
				image: "mysql",
			},
			want: &bci.ContainerImageInfo{
				ImageAddress: "mysql",
				ImageName:    "mysql",
				ImageVersion: "latest",
			},
		},
		{
			name: "docker.io case 2",
			args: args{
				image: "bitnami/postgresql:11.4.0-debian-9-r12",
			},
			want: &bci.ContainerImageInfo{
				ImageAddress: "bitnami/postgresql",
				ImageName:    "postgresql",
				ImageVersion: "11.4.0-debian-9-r12",
			},
		},
		{
			name: "with port and version case",
			args: args{
				image: "hub.baidubce.com:8011/dib/dib-agent-online:1-0-0-20190222160907",
			},
			want: &bci.ContainerImageInfo{
				ImageAddress: "hub.baidubce.com:8011/dib/dib-agent-online",
				ImageName:    "dib-agent-online",
				ImageVersion: "1-0-0-20190222160907",
			},
		},
		{
			name: "with port but no version case",
			args: args{
				image: "hub.baidubce.com:8011/dib/dib-agent-online",
			},
			want: &bci.ContainerImageInfo{
				ImageAddress: "hub.baidubce.com:8011/dib/dib-agent-online",
				ImageName:    "dib-agent-online",
				ImageVersion: "latest",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getContainerImageInfo(tt.args.image); !cmp.Equal(got, tt.want) {
				t.Errorf("getContainerImageInfo() = %v, want %v, diff is %s",
					got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestBCIProvider_getVolumes(t *testing.T) {
	secret1 := testutil.FakeSecret("namespace-0", "name-0", map[string]string{
		"key-0": "val-0",
		"key-1": "val-1",
	})

	configMap1 := testutil.FakeConfigMap("namespace-0", "name-0", map[string]string{
		"key-0": "val-0",
		"key-1": "val-1",
	})

	pvc1 := &v1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "namespace-0",
			Name:      "pvc-cfs",
		},
		Spec: v1.PersistentVolumeClaimSpec{
			VolumeName: "pv-cfs",
		},
		Status: v1.PersistentVolumeClaimStatus{
			Phase: v1.ClaimBound,
		},
	}
	pv1 := &v1.PersistentVolume{
		ObjectMeta: metav1.ObjectMeta{
			Name: "pv-cfs",
		},
		Spec: v1.PersistentVolumeSpec{
			PersistentVolumeSource: v1.PersistentVolumeSource{
				NFS: &v1.NFSVolumeSource{
					Server:   "cfs-test.bj.baidubce.com",
					Path:     "/path",
					ReadOnly: true,
				},
			},
		},
		Status: v1.PersistentVolumeStatus{
			Phase: v1.VolumeBound,
		},
	}

	fakeResourceManager := testutil.FakeResourceManager(configMap1, secret1, pvc1, pv1)

	failOnceResourceManager, err := manager.NewResourceManager(nil, nil,
		NewFailLimitedTimesConfigMapLister(1, configMap1), nil, nil, nil, nil, nil, fakeResourceManager.GetRawClient())
	if err != nil {
		panic(err)
	}

	basePod := testutil.FakePodWithSingleContainer("namespace-0", "name-0", "image-0")
	basePod.SetLabels(map[string]string{
		"label-0": "value-0",
		"label-1": "value-1",
	})
	sizeLimit := resource.MustParse("1Gi")
	sizeLimit_500 := resource.MustParse("512Mi")
	baseVolumes := []v1.Volume{
		{
			Name: "test-nfs-volume",
			VolumeSource: v1.VolumeSource{
				NFS: &v1.NFSVolumeSource{
					Server:   "a.b.c",
					Path:     "foo",
					ReadOnly: true,
				},
			},
		},
		{
			Name: "test-emptyDir-volume",
			VolumeSource: v1.VolumeSource{
				EmptyDir: &v1.EmptyDirVolumeSource{
					Medium:    v1.StorageMediumMemory,
					SizeLimit: &sizeLimit,
				},
			},
		},
		{
			Name: "test-secret-volume",
			VolumeSource: v1.VolumeSource{
				Secret: &v1.SecretVolumeSource{
					SecretName: "name-0",
				},
			},
		},
		{
			Name: "test-configMap-volume",
			VolumeSource: v1.VolumeSource{
				ConfigMap: &v1.ConfigMapVolumeSource{
					LocalObjectReference: v1.LocalObjectReference{
						Name: "name-0",
					},
				},
			},
		},
		{
			Name: "test-pvc-volume",
			VolumeSource: v1.VolumeSource{
				PersistentVolumeClaim: &v1.PersistentVolumeClaimVolumeSource{
					ClaimName: "pvc-cfs",
				},
			},
		},
		{
			Name: "test-downward-api-volume",
			VolumeSource: v1.VolumeSource{
				DownwardAPI: &v1.DownwardAPIVolumeSource{
					Items: []v1.DownwardAPIVolumeFile{
						{
							Path: "labels",
							FieldRef: &v1.ObjectFieldSelector{
								FieldPath: "metadata.labels",
							},
						},
						{
							Path: "cpu_request",
							ResourceFieldRef: &v1.ResourceFieldSelector{
								ContainerName: "name-0",
								Resource:      "requests.cpu",
								Divisor:       resource.MustParse("1m"),
							},
						},
						{
							Path: "memory_limit",
							ResourceFieldRef: &v1.ResourceFieldSelector{
								ContainerName: "name-0",
								Resource:      "limits.memory",
								Divisor:       resource.MustParse("1Mi"),
							},
						},
					},
				},
			},
		},
		{
			Name: "test-flex-volume",
			VolumeSource: v1.VolumeSource{
				FlexVolume: &v1.FlexVolumeSource{
					Driver: "k8s/sidecar-stdout",
				},
			},
		},
	}
	baseBCIVolumes := &bci.Volumes{
		NFS: []bci.VolumeNFS{
			{
				Name:     baseVolumes[0].Name,
				Server:   baseVolumes[0].NFS.Server,
				Path:     baseVolumes[0].NFS.Path,
				ReadOnly: baseVolumes[0].NFS.ReadOnly,
			},
			{
				Name:     baseVolumes[4].Name,
				Server:   pv1.Spec.NFS.Server,
				Path:     pv1.Spec.NFS.Path,
				ReadOnly: pv1.Spec.NFS.ReadOnly || baseVolumes[5].PersistentVolumeClaim.ReadOnly,
			},
		},
		EmptyDir: []bci.VolumeEmptyDir{
			{
				Name:         baseVolumes[1].Name,
				Medium:       bci.StorageMediumMemory,
				SizeLimitGiB: 1.0,
			},
		},
		ConfigFile: []bci.VolumeConfigFile{
			{
				Name: baseVolumes[2].Name,
				ConfigFiles: []bci.ConfigFile{
					{
						Path: "key-0",
						File: "dmFsLTA=",
					},
					{
						Path: "key-1",
						File: "dmFsLTE=",
					},
				},
			},
			{
				Name: baseVolumes[3].Name,
				ConfigFiles: []bci.ConfigFile{
					{
						Path: "key-0",
						File: "dmFsLTA=",
					},
					{
						Path: "key-1",
						File: "dmFsLTE=",
					},
				},
			},
			{
				Name: baseVolumes[5].Name,
				ConfigFiles: []bci.ConfigFile{
					{
						Path: "labels",
						File: "bGFiZWwtMD0idmFsdWUtMCIKbGFiZWwtMT0idmFsdWUtMSI=",
					},
					{
						Path: "cpu_request",
						File: "MTAwMA==",
					},
					{
						Path: "memory_limit",
						File: "MjA0OA==",
					},
				},
			},
		},
		FlexVolume: []bci.VolumeFlexVolume{
			{
				Name:   baseVolumes[6].Name,
				Driver: "k8s/sidecar-stdout",
			},
		},
	}

	type fields struct {
		resourceManager manager.ResourceManager
	}
	type args struct {
		ctx context.Context
		pod *v1.Pod
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *bci.Volumes
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = baseVolumes
					return testPod
				}(),
			},
			want:    baseBCIVolumes,
			wantErr: false,
		},
		{
			name: "pvc case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = []v1.Volume{baseVolumes[4]}
					return testPod
				}(),
			},
			want: &bci.Volumes{
				NFS: []bci.VolumeNFS{baseBCIVolumes.NFS[1]},
			},
			wantErr: false,
		},
		{
			name: "downwardAPI case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = []v1.Volume{baseVolumes[5]}
					return testPod
				}(),
			},
			want: &bci.Volumes{
				ConfigFile: []bci.VolumeConfigFile{baseBCIVolumes.ConfigFile[2]},
			},
		},
		{
			name: "emptyDir case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = []v1.Volume{{
						Name: "test-emptyDir-volume-Mi",
						VolumeSource: v1.VolumeSource{
							EmptyDir: &v1.EmptyDirVolumeSource{
								Medium:    v1.StorageMediumMemory,
								SizeLimit: &sizeLimit_500,
							},
						},
					}}
					return testPod
				}(),
			},
			want: func() *bci.Volumes {
				jsonDump, _ := json.Marshal(baseBCIVolumes)
				wantBCIVolumes := new(bci.Volumes)
				json.Unmarshal(jsonDump, wantBCIVolumes)
				wantBCIVolumes.NFS = nil
				wantBCIVolumes.ConfigFile = nil
				wantBCIVolumes.FlexVolume = nil
				wantBCIVolumes.EmptyDir = []bci.VolumeEmptyDir{{
					Name:         "test-emptyDir-volume-Mi",
					Medium:       bci.StorageMediumMemory,
					SizeLimitGiB: 0.5,
				},
				}
				return wantBCIVolumes
			}(),
		},
		{
			name: "emptyDir nil case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = []v1.Volume{{
						Name: "test-emptyDir-volume-nil",
						VolumeSource: v1.VolumeSource{
							EmptyDir: &v1.EmptyDirVolumeSource{},
						},
					}}
					return testPod
				}(),
			},
			want: func() *bci.Volumes {
				jsonDump, _ := json.Marshal(baseBCIVolumes)
				wantBCIVolumes := new(bci.Volumes)
				json.Unmarshal(jsonDump, wantBCIVolumes)
				wantBCIVolumes.NFS = nil
				wantBCIVolumes.ConfigFile = nil
				wantBCIVolumes.FlexVolume = nil
				wantBCIVolumes.EmptyDir = []bci.VolumeEmptyDir{
					{
						Name: "test-emptyDir-volume-nil",
					},
				}
				return wantBCIVolumes
			}(),
		},
		{
			name: "unsupport downwardAPI field case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = []v1.Volume{
						baseVolumes[5],
						{
							Name: "bad-downward-api-volume",
							VolumeSource: v1.VolumeSource{
								DownwardAPI: &v1.DownwardAPIVolumeSource{
									Items: []v1.DownwardAPIVolumeFile{
										{
											Path: "unsupported_podIP",
											FieldRef: &v1.ObjectFieldSelector{
												FieldPath: "status.podIP",
											},
										},
										{
											Path: "non-exist_container_cpu_request",
											ResourceFieldRef: &v1.ResourceFieldSelector{
												ContainerName: "my-container",
												Resource:      "requests.cpu",
												Divisor:       resource.MustParse("1m"),
											},
										},
									},
								},
							},
						},
					}
					return testPod
				}(),
			},
			wantErr: true,
		},
		{
			name: "config map getting failed once case",
			fields: fields{
				resourceManager: failOnceResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = []v1.Volume{baseVolumes[3]}
					return testPod
				}(),
			},
			want: &bci.Volumes{
				ConfigFile: []bci.VolumeConfigFile{baseBCIVolumes.ConfigFile[1]},
			},
			wantErr: false,
		},
		{
			name: "configMap with items case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = baseVolumes
					testPod = testPod.DeepCopy()
					testPod.Spec.Volumes[3].ConfigMap.Items = []v1.KeyToPath{
						{
							Key:  "key-0",
							Path: "key-0.yml",
						},
					}
					return testPod
				}(),
			},
			want: func() *bci.Volumes {
				jsonDump, _ := json.Marshal(baseBCIVolumes)
				wantBCIVolumes := new(bci.Volumes)
				json.Unmarshal(jsonDump, wantBCIVolumes)
				wantBCIVolumes.ConfigFile[1].ConfigFiles = []bci.ConfigFile{
					{
						Path: "key-0.yml",
						File: "dmFsLTA=",
					},
				}
				return wantBCIVolumes
			}(),
			wantErr: false,
		},
		{
			name: "configMap with bad items case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = baseVolumes
					testPod = testPod.DeepCopy()
					testPod.Spec.Volumes[3].ConfigMap.Items = []v1.KeyToPath{
						{
							Key:  "key-2",
							Path: "key-2.yml",
						},
					}
					return testPod
				}(),
			},
			wantErr: true,
		},
		{
			name: "non-exist configMap volume case",
			fields: fields{
				resourceManager: fakeResourceManager,
			},
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Volumes = baseVolumes
					testPod = testPod.DeepCopy()
					testPod.Spec.Volumes[3].ConfigMap.Name = "name-whatever"
					return testPod
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{
				resourceManager: tt.fields.resourceManager,
			}
			got, err := p.getVolumes(tt.args.ctx, tt.args.pod)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.getVolumes() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// sort all slices before comparison
			func(items ...*bci.Volumes) {
				for m := range items {
					if items[m] == nil {
						continue
					}
					configFiles := items[m].ConfigFile
					sort.Slice(configFiles, func(i, j int) bool { return configFiles[i].Name < configFiles[j].Name })
					for n, _ := range configFiles {
						innerConfigFiles := configFiles[n].ConfigFiles
						sort.Slice(innerConfigFiles, func(i, j int) bool { return innerConfigFiles[i].Path < innerConfigFiles[j].Path })
						configFiles[n].ConfigFiles = innerConfigFiles
					}
					items[m].ConfigFile = configFiles
					nfss := items[m].NFS
					sort.Slice(nfss, func(i, j int) bool { return nfss[i].Name < nfss[j].Name })
					items[m].NFS = nfss
					emptyDirs := items[m].EmptyDir
					sort.Slice(emptyDirs, func(i, j int) bool { return emptyDirs[i].Name < emptyDirs[j].Name })
					items[m].EmptyDir = emptyDirs
				}
			}(got, tt.want)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_getAffinity(t *testing.T) {

	basePod := testutil.FakePodWithSingleContainer("namespace-0", "name-0", "image-0")
	basePod.SetLabels(map[string]string{
		"label-0": "value-0",
		"label-1": "value-1",
	})
	baseBCIAffinity := &v1.Affinity{
		PodAffinity: &v1.PodAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: []v1.PodAffinityTerm{
				{
					LabelSelector: &metav1.LabelSelector{
						MatchExpressions: []metav1.LabelSelectorRequirement{
							{
								Key:      "key2",
								Operator: metav1.LabelSelectorOpIn,
								Values:   []string{"value1", "value2"},
							},
						},
					},
					TopologyKey: "zone",
					Namespaces:  []string{"ns"},
				},
			},
			PreferredDuringSchedulingIgnoredDuringExecution: []v1.WeightedPodAffinityTerm{
				{
					Weight: 10,
					PodAffinityTerm: v1.PodAffinityTerm{
						LabelSelector: &metav1.LabelSelector{
							MatchExpressions: []metav1.LabelSelectorRequirement{
								{
									Key:      "key2",
									Operator: metav1.LabelSelectorOpNotIn,
									Values:   []string{"value1", "value2"},
								},
							},
						},
						Namespaces:  []string{"ns"},
						TopologyKey: "region",
					},
				},
			},
		},
	}

	type fields struct {
		resourceManager manager.ResourceManager
	}
	type args struct {
		ctx context.Context
		pod *v1.Pod
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *v1.Affinity
		wantErr bool
	}{
		{
			name: "normal case",
			args: args{
				ctx: context.TODO(),
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Affinity = baseBCIAffinity
					return testPod
				}(),
			},
			want:    baseBCIAffinity,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{
				resourceManager: tt.fields.resourceManager,
			}
			got, err := p.getAffinity(tt.args.ctx, tt.args.pod)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.getAffinity() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_mapToItems(t *testing.T) {
	type args struct {
		pathFile map[string]string
		items    []v1.KeyToPath
		optional bool
	}
	tests := []struct {
		name            string
		args            args
		wantConfigFiles []bci.ConfigFile
		wantErr         bool
	}{
		// TODO: Add test cases.
		{
			name: "normal case",
			args: args{
				pathFile: map[string]string{
					"key-0": "val-0",
					"key-1": "val-1",
				},
				items: []v1.KeyToPath{
					{
						Key:  "key-0",
						Path: "key-0.yml",
					},
				},
				optional: false,
			},
			wantConfigFiles: []bci.ConfigFile{
				{
					Path: "key-0.yml",
					File: "val-0",
				},
			},
			wantErr: false,
		},
		{
			name: "item key not in pathFile case",
			args: args{
				pathFile: map[string]string{
					"key-0": "val-0",
					"key-1": "val-1",
				},
				items: []v1.KeyToPath{
					{
						Key:  "key-2",
						Path: "key-0.yml",
					},
				},
				optional: false,
			},
			wantConfigFiles: []bci.ConfigFile{},
			wantErr:         true,
		},
		{
			name: "item key not in pathFile but optional case",
			args: args{
				pathFile: map[string]string{
					"key-0": "val-0",
					"key-1": "val-1",
				},
				items: []v1.KeyToPath{
					{
						Key:  "key-2",
						Path: "key-0.yml",
					},
				},
				optional: true,
			},
			wantConfigFiles: []bci.ConfigFile{},
			wantErr:         false,
		},
		{
			name: "no items case",
			args: args{
				pathFile: map[string]string{
					"key-0": "val-0",
					"key-1": "val-1",
				},
				items:    nil,
				optional: false,
			},
			wantConfigFiles: []bci.ConfigFile{
				{
					Path: "key-0",
					File: "val-0",
				},
				{
					Path: "key-1",
					File: "val-1",
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotConfigFiles, err := mapToItems(tt.args.pathFile, tt.args.items, tt.args.optional)
			if (err != nil) != tt.wantErr {
				t.Errorf("mapToItems() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// sort slices before comparison
			sort.Slice(gotConfigFiles, func(i, j int) bool { return gotConfigFiles[i].Path < gotConfigFiles[j].Path })
			sort.Slice(tt.wantConfigFiles, func(i, j int) bool { return tt.wantConfigFiles[i].Path < tt.wantConfigFiles[j].Path })
			if !cmp.Equal(gotConfigFiles, tt.wantConfigFiles) {
				t.Errorf("mapToItems() = %v, want %v, diff is %s", gotConfigFiles, tt.wantConfigFiles, cmp.Diff(gotConfigFiles, tt.wantConfigFiles))
			}
		})
	}
}

func Test_parseBCIAnnotations(t *testing.T) {
	type args struct {
		ctx         context.Context
		annotations map[string]string
	}
	tests := []struct {
		name    string
		args    args
		want    *BCIAnnotationParams
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal case 1",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCILogicalZoneAnnotationKey: "zoneB",
					BCISubnetIDAnnotationKey:    "sbn-psupdhtdc0nv",
				},
			},
			want: &BCIAnnotationParams{
				CreateEIPBandwidth: 100,
				LogicalZone:        "zoneB",
				SubnetID:           "sbn-psupdhtdc0nv",
			},
			wantErr: false,
		},
		{
			name: "normal case 2",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCICreateEIPAnnotationKey:   "true",
					BCILogicalZoneAnnotationKey: "zoneB",
					BCISubnetIDAnnotationKey:    "sbn-psupdhtdc0nv",
				},
			},
			want: &BCIAnnotationParams{
				CreateEIP:          true,
				CreateEIPBandwidth: 100,
				LogicalZone:        "zoneB",
				SubnetID:           "sbn-psupdhtdc0nv",
			},
			wantErr: false,
		},
		{
			name: "normal case 3",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCICreateEIPAnnotationKey:   "false",
					BCILogicalZoneAnnotationKey: "zoneB",
					BCISubnetIDAnnotationKey:    "sbn-psupdhtdc0nv",
				},
			},
			want: &BCIAnnotationParams{
				CreateEIPBandwidth: 100,
				LogicalZone:        "zoneB",
				SubnetID:           "sbn-psupdhtdc0nv",
			},
			wantErr: false,
		},
		{
			name: "normal case 4",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCICreateEIPAnnotationKey:          "true",
					BCICreateEIPBandwidthAnnotationKey: "1000",
					BCILogicalZoneAnnotationKey:        "zoneB",
					BCISubnetIDAnnotationKey:           "sbn-psupdhtdc0nv",
				},
			},
			want: &BCIAnnotationParams{
				CreateEIP:          true,
				CreateEIPBandwidth: 1000,
				LogicalZone:        "zoneB",
				SubnetID:           "sbn-psupdhtdc0nv",
			},
			wantErr: false,
		},
		{
			name: "normal case 5",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCILogicalZoneAnnotationKey: "zoneB",
					BCISubnetIDAnnotationKey:    "sbn-psupdhtdc0nv",
					BCITidalPodAnnotationKey:    "true",
				},
			},
			want: &BCIAnnotationParams{
				CreateEIPBandwidth: 100,
				LogicalZone:        "zoneB",
				SubnetID:           "sbn-psupdhtdc0nv",
				IsTidal:            true,
			},
			wantErr: false,
		},
		{
			name: "illegal eip bandwidth",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCICreateEIPAnnotationKey:          "true",
					BCICreateEIPBandwidthAnnotationKey: "1000M",
					BCILogicalZoneAnnotationKey:        "zoneB",
					BCISubnetIDAnnotationKey:           "sbn-psupdhtdc0nv",
				},
			},
			want: &BCIAnnotationParams{
				CreateEIP:          true,
				CreateEIPBandwidth: 100,
				LogicalZone:        "zoneB",
				SubnetID:           "sbn-psupdhtdc0nv",
			},
			wantErr: false,
		},
		{
			name: "vpc uuid annotation without vpc id annotation case",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCICreateEIPAnnotationKey:   "true",
					BCILogicalZoneAnnotationKey: "zoneB",
					BCIVPCUUIDAnnotationKey:     "fbd536d9-cae7-432f-9d59-350c85e08448",
				},
			},
			wantErr: true,
		},
		{
			name: "declare eip address as well as eip creation annotations",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCICreateEIPAnnotationKey:  "true",
					BCIEIPAddressAnnotationKey: "*************",
				},
			},
			wantErr: true,
		},
		{
			name: "legal delay release annotations",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCIDelayReleaseDurationMinuteAnnotationKey: "30",
					BCIdelayReleaseSucceeded:                   "true",
				},
			},
			want: &BCIAnnotationParams{
				DelayReleaseDurationMinute: 30,
				DelayReleaseSucceeded:      true,
				CreateEIPBandwidth:         100,
			},
		},
		{
			name: "illegal delay release annotations",
			args: args{
				ctx: context.TODO(),
				annotations: map[string]string{
					BCIDelayReleaseDurationMinuteAnnotationKey: "30m",
					BCIdelayReleaseSucceeded:                   "true",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := parseBCIAnnotations(tt.args.ctx, tt.args.annotations)
			if (err != nil) != tt.wantErr {
				t.Errorf("parseBCIAnnotations() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_getContainers(t *testing.T) {
	basePod := testutil.FakePodWithSingleContainer("namespace-0", "name-0", "image-0")
	basePod.Spec.Containers[0].Env = []v1.EnvVar{
		{
			Name:  "test-env-name-0",
			Value: "test-env-value-0",
		},
		{
			Name:  "test-env-name-1",
			Value: "test-env-value-1",
		},
	}
	basePod.Spec.Containers[0].Ports = []v1.ContainerPort{
		{
			Name:          "test-container-port-0",
			ContainerPort: 8080,
			Protocol:      v1.ProtocolTCP,
		},
	}
	basePod.Spec.Containers[0].VolumeMounts = []v1.VolumeMount{
		{
			Name:      "test-volume-mounts-0",
			ReadOnly:  false,
			MountPath: "/test/mount-path",
		},
	}
	basePod.Spec.Containers[0].Resources = v1.ResourceRequirements{
		Requests: func() v1.ResourceList {
			v := v1.ResourceList(make(map[v1.ResourceName]resource.Quantity))
			v[v1.ResourceMemory] = resource.MustParse("512Mi")
			v[v1.ResourceCPU] = resource.MustParse("250m")
			return v
		}(),
	}
	testProbe := &v1.Probe{
		Handler: v1.Handler{
			Exec: &v1.ExecAction{
				Command: []string{"/bin/sh", "-c", "sleep 1 && exit 1"},
			},
		},
		InitialDelaySeconds: 30,
		FailureThreshold:    1,
		TimeoutSeconds:      30,
		PeriodSeconds:       10,
		SuccessThreshold:    3,
	}
	basePod.Spec.Containers[0].ReadinessProbe = testProbe.DeepCopy()
	basePod.Spec.Containers[0].LivenessProbe = testProbe.DeepCopy()
	basePod.Spec.Containers[0].StartupProbe = testProbe.DeepCopy()
	type args struct {
		pod     *v1.Pod
		gpuType string
	}
	tests := []struct {
		name    string
		args    args
		want    []bci.Container
		wantErr bool
	}{
		// All test cases.
		{
			name: "normal case",
			args: args{
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					return testPod
				}(),
			},
			want: []bci.Container{
				{
					Name: basePod.Spec.Containers[0].Name,
					ContainerImageInfo: &bci.ContainerImageInfo{
						ImageAddress: "image-0",
						ImageName:    "image-0",
						ImageVersion: "latest",
					},
					MemoryInGB:      0.5,
					CPUInCore:       0.25,
					ImagePullPolicy: bci.PullIfNotPresent,
					Ports: []bci.ContainerPort{
						{
							Port:     basePod.Spec.Containers[0].Ports[0].ContainerPort,
							Protocol: bci.ContainerNetworkProtocolTCP,
							Name:     "test-container-port-0",
						},
					},
					Envs: []bci.Env{
						{
							Key:   "test-env-name-0",
							Value: "test-env-value-0",
						},
						{
							Key:   "test-env-name-1",
							Value: "test-env-value-1",
						},
					},
					VolumeMounts: []bci.VolumeMount{
						{
							Name:      "test-volume-mounts-0",
							MountPath: "/test/mount-path",
							ReadOnly:  false,
						},
					},
					ReadinessProbe: testProbe.DeepCopy(),
					LivenessProbe:  testProbe.DeepCopy(),
					StartupProbe:   testProbe.DeepCopy(),
				},
			},
			wantErr: false,
		},
		{
			name: "use default resource case",
			args: args{
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Containers[0].Resources = v1.ResourceRequirements{}
					return testPod
				}(),
			},
			want: []bci.Container{
				{
					Name: basePod.Spec.Containers[0].Name,
					ContainerImageInfo: &bci.ContainerImageInfo{
						ImageAddress: "image-0",
						ImageName:    "image-0",
						ImageVersion: "latest",
					},
					MemoryInGB:      2,
					CPUInCore:       1,
					ImagePullPolicy: bci.PullIfNotPresent,
					Ports: []bci.ContainerPort{
						{
							Port:     basePod.Spec.Containers[0].Ports[0].ContainerPort,
							Protocol: bci.ContainerNetworkProtocolTCP,
							Name:     "test-container-port-0",
						},
					},
					Envs: []bci.Env{
						{
							Key:   "test-env-name-0",
							Value: "test-env-value-0",
						},
						{
							Key:   "test-env-name-1",
							Value: "test-env-value-1",
						},
					},
					VolumeMounts: []bci.VolumeMount{
						{
							Name:      "test-volume-mounts-0",
							MountPath: "/test/mount-path",
							ReadOnly:  false,
						},
					},
					ReadinessProbe: testProbe.DeepCopy(),
					LivenessProbe:  testProbe.DeepCopy(),
					StartupProbe:   testProbe.DeepCopy(),
				},
			},
			wantErr: false,
		},
		{
			name: "container with gpu resource",
			args: args{
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Containers[0].Resources.Requests[ResourceGPU] = resource.MustParse("1")
					return testPod
				}(),
				gpuType: "v100",
			},
			want: []bci.Container{
				{
					Name: basePod.Spec.Containers[0].Name,
					ContainerImageInfo: &bci.ContainerImageInfo{
						ImageAddress: "image-0",
						ImageName:    "image-0",
						ImageVersion: "latest",
					},
					MemoryInGB:      0.5,
					CPUInCore:       0.25,
					GPUType:         "v100",
					GPUCount:        1.00,
					ImagePullPolicy: bci.PullIfNotPresent,
					Ports: []bci.ContainerPort{
						{
							Port:     basePod.Spec.Containers[0].Ports[0].ContainerPort,
							Protocol: bci.ContainerNetworkProtocolTCP,
							Name:     "test-container-port-0",
						},
					},
					Envs: []bci.Env{
						{
							Key:   "test-env-name-0",
							Value: "test-env-value-0",
						},
						{
							Key:   "test-env-name-1",
							Value: "test-env-value-1",
						},
					},
					VolumeMounts: []bci.VolumeMount{
						{
							Name:      "test-volume-mounts-0",
							MountPath: "/test/mount-path",
							ReadOnly:  false,
						},
					},
					ReadinessProbe: testProbe.DeepCopy(),
					LivenessProbe:  testProbe.DeepCopy(),
					StartupProbe:   testProbe.DeepCopy(),
				},
			},
		},
		{
			name: "container with named port tcp probe",
			args: args{
				pod: func() *v1.Pod {
					testPod := basePod.DeepCopy()
					testPod.Spec.Containers[0].Resources.Requests[ResourceGPU] = resource.MustParse("1")
					testPod.Spec.Containers[0].ReadinessProbe = &v1.Probe{
						TimeoutSeconds: 3,
						PeriodSeconds:  10,
						Handler: v1.Handler{
							TCPSocket: &v1.TCPSocketAction{
								Port: intstr.FromString("http"),
							},
						},
					}
					testPod.Spec.Containers[0].Ports = []v1.ContainerPort{
						{
							Name:          "http",
							ContainerPort: 8080,
						},
					}
					return testPod
				}(),
				gpuType: "v100",
			},
			want: []bci.Container{
				{
					Name: basePod.Spec.Containers[0].Name,
					ContainerImageInfo: &bci.ContainerImageInfo{
						ImageAddress: "image-0",
						ImageName:    "image-0",
						ImageVersion: "latest",
					},
					MemoryInGB:      0.5,
					CPUInCore:       0.25,
					GPUType:         "v100",
					GPUCount:        1.00,
					ImagePullPolicy: bci.PullIfNotPresent,
					Ports: []bci.ContainerPort{
						{
							Port:     basePod.Spec.Containers[0].Ports[0].ContainerPort,
							Protocol: bci.ContainerNetworkProtocolTCP,
							Name:     "http",
						},
					},
					Envs: []bci.Env{
						{
							Key:   "test-env-name-0",
							Value: "test-env-value-0",
						},
						{
							Key:   "test-env-name-1",
							Value: "test-env-value-1",
						},
					},
					VolumeMounts: []bci.VolumeMount{
						{
							Name:      "test-volume-mounts-0",
							MountPath: "/test/mount-path",
							ReadOnly:  false,
						},
					},
					ReadinessProbe: &v1.Probe{
						TimeoutSeconds: 3,
						PeriodSeconds:  10,
						Handler: v1.Handler{
							TCPSocket: &v1.TCPSocketAction{
								Port: intstr.FromInt(8080),
							},
						},
					},
					LivenessProbe: testProbe.DeepCopy(),
					StartupProbe:  testProbe.DeepCopy(),
				},
			},
		},
		{
			name: "container with resource limits larger than requests",
			args: func() args {
				testPod := basePod.DeepCopy()
				testPod.Spec.Containers[0].Resources = v1.ResourceRequirements{
					Requests: v1.ResourceList(map[v1.ResourceName]resource.Quantity{
						v1.ResourceCPU:    resource.MustParse("512m"),
						v1.ResourceMemory: resource.MustParse("1Gi"),
					}),
					Limits: v1.ResourceList(map[v1.ResourceName]resource.Quantity{
						v1.ResourceCPU:    resource.MustParse("1"),
						v1.ResourceMemory: resource.MustParse("2Gi"),
					}),
				}

				return args{
					pod: testPod,
				}
			}(),
			want: []bci.Container{
				{
					Name: basePod.Spec.Containers[0].Name,
					ContainerImageInfo: &bci.ContainerImageInfo{
						ImageAddress: "image-0",
						ImageName:    "image-0",
						ImageVersion: "latest",
					},
					MemoryInGB:      2,
					CPUInCore:       1,
					GPUCount:        0.00,
					ImagePullPolicy: bci.PullIfNotPresent,
					Ports: []bci.ContainerPort{
						{
							Port:     basePod.Spec.Containers[0].Ports[0].ContainerPort,
							Protocol: bci.ContainerNetworkProtocolTCP,
							Name:     "test-container-port-0",
						},
					},
					Envs: []bci.Env{
						{
							Key:   "test-env-name-0",
							Value: "test-env-value-0",
						},
						{
							Key:   "test-env-name-1",
							Value: "test-env-value-1",
						},
					},
					VolumeMounts: []bci.VolumeMount{
						{
							Name:      "test-volume-mounts-0",
							MountPath: "/test/mount-path",
							ReadOnly:  false,
						},
					},
					ReadinessProbe: testProbe.DeepCopy(),
					LivenessProbe:  testProbe.DeepCopy(),
					StartupProbe:   testProbe.DeepCopy(),
				},
			},
		},
		{
			name: "container with only resource limits set",
			args: func() args {
				testPod := basePod.DeepCopy()
				testPod.Spec.Containers[0].Resources = v1.ResourceRequirements{
					Limits: v1.ResourceList(map[v1.ResourceName]resource.Quantity{
						v1.ResourceCPU:    resource.MustParse("1"),
						v1.ResourceMemory: resource.MustParse("2Gi"),
					}),
				}

				return args{
					pod: testPod,
				}
			}(),
			want: []bci.Container{
				{
					Name: basePod.Spec.Containers[0].Name,
					ContainerImageInfo: &bci.ContainerImageInfo{
						ImageAddress: "image-0",
						ImageName:    "image-0",
						ImageVersion: "latest",
					},
					MemoryInGB:      2,
					CPUInCore:       1,
					GPUCount:        0.00,
					ImagePullPolicy: bci.PullIfNotPresent,
					Ports: []bci.ContainerPort{
						{
							Port:     basePod.Spec.Containers[0].Ports[0].ContainerPort,
							Protocol: bci.ContainerNetworkProtocolTCP,
							Name:     "test-container-port-0",
						},
					},
					Envs: []bci.Env{
						{
							Key:   "test-env-name-0",
							Value: "test-env-value-0",
						},
						{
							Key:   "test-env-name-1",
							Value: "test-env-value-1",
						},
					},
					VolumeMounts: []bci.VolumeMount{
						{
							Name:      "test-volume-mounts-0",
							MountPath: "/test/mount-path",
							ReadOnly:  false,
						},
					},
					ReadinessProbe: testProbe.DeepCopy(),
					LivenessProbe:  testProbe.DeepCopy(),
					StartupProbe:   testProbe.DeepCopy(),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{}
			got, err := p.getContainers(tt.args.pod.Spec.Containers, tt.args.gpuType)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.getContainers() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !cmp.Equal(got, tt.want) {
				t.Errorf("BCIProvider.getContainers() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}

func TestAdjustBCISecretsBasedOnImages(t *testing.T) {
	type args struct {
		secrets    []bci.ImageRegistrySecret
		containers []bci.Container
	}
	tests := []struct {
		name string
		args args
		want []bci.ImageRegistrySecret
	}{
		// All test cases.
		{
			name: "1 image with no secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: nil,
		},
		{
			name: "1 image with 1 secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
			},
		},
		{
			name: "2 images with 1 secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/tomcat",
							ImageName:    "tomcat",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
			},
		},
		{
			name: "2 images from 2 namespaces with 1 secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/jpaas-public/cds-flex-volume",
							ImageName:    "cds-flex-volume",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
				{
					Server:   "https://hub.baidubce.com/jpaas-public",
					UserName: "user1",
					Password: "password1",
				},
			},
		},
		{
			name: "2 images from 2 registries with 1 secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
			},
		},
		{
			name: "2 images from 2 registries with 2 secrets",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
					{
						Server:   "http://hub.agilecloud.com:8011",
						UserName: "user2",
						Password: "password2",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/jpaas-public/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
				{
					Server:   "http://hub.agilecloud.com:8011/jpaas-public",
					UserName: "user2",
					Password: "password2",
				},
			},
		},
		{
			name: "1 images with 1 irrelavant secrets",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: nil,
		},
		{
			name: "2 images from 2 registries with 2 secrets but 1 of them is irrelavant",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
					{
						Server:   "http://hub.whatever.com:8011",
						UserName: "user3",
						Password: "password3",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/jpaas-public/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
			},
		},
		{
			name: "1 docker.io image with 1 secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						UserName: "user4",
						Password: "password4",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "my-namespace/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "my-namespace",
					UserName: "user4",
					Password: "password4",
				},
			},
		},
		{
			name: "1 docker.io image 1 other image with 1 docker.io secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						UserName: "user4",
						Password: "password4",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "my-namespace/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "my-namespace",
					UserName: "user4",
					Password: "password4",
				},
			},
		},
		{
			name: "1 docker.io image and 1 other image with 2 secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "http://hub.agilecloud.com:8011",
						UserName: "user2",
						Password: "password2",
					},
					{
						UserName: "user4",
						Password: "password4",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "my-namespace/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "http://hub.agilecloud.com:8011/cce",
					UserName: "user2",
					Password: "password2",
				},
				{
					Server:   "my-namespace",
					UserName: "user4",
					Password: "password4",
				},
			},
		},
		{
			name: "1 docker.io image and 1 other image with 1 other secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "http://hub.agilecloud.com:8011",
						UserName: "user2",
						Password: "password2",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "my-namespace/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.agilecloud.com:8011/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "http://hub.agilecloud.com:8011/cce",
					UserName: "user2",
					Password: "password2",
				},
			},
		},
		{
			name: "1 library image with 1 docker.io secret",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						UserName: "user4",
						Password: "password4",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "nginx",
							ImageName:    "nginx",
						},
					},
				},
			},
			want: nil,
		},
		{
			name: "1 image with 1 secret with namespace in server already",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com/cce",
						UserName: "user1",
						Password: "password1",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user1",
					Password: "password1",
				},
			},
		},
		{
			name: "2 images from 2 namespaces with 2 secret in which 1 contains namespace already",
			args: args{
				secrets: []bci.ImageRegistrySecret{
					{
						Server:   "https://hub.baidubce.com",
						UserName: "user1",
						Password: "password1",
					},
					{
						Server:   "https://hub.baidubce.com/cce",
						UserName: "user3",
						Password: "password3",
					},
				},
				containers: []bci.Container{
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/cce/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
					{
						ContainerImageInfo: &bci.ContainerImageInfo{
							ImageAddress: "hub.baidubce.com/jpaas-public/nginx-alpine-go",
							ImageName:    "nginx-alpine-go",
						},
					},
				},
			},
			want: []bci.ImageRegistrySecret{
				{
					Server:   "https://hub.baidubce.com/jpaas-public",
					UserName: "user1",
					Password: "password1",
				},
				{
					Server:   "https://hub.baidubce.com/cce",
					UserName: "user3",
					Password: "password3",
				},
			},
		},
	}
	cmpOpt := cmpopts.SortSlices(func(i, j bci.ImageRegistrySecret) bool {
		return i.Server < j.Server
	})

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := adjustBCISecretsBasedOnImages(tt.args.secrets, tt.args.containers); !cmp.Equal(got, tt.want, cmpOpt) {
				t.Errorf("appendNamespaceToImageRegistrySecrets() = %v, want %v, diff is %s",
					got, tt.want, cmp.Diff(got, tt.want, cmpOpt))
			}
		})
	}
}

func getMetaV1TimeFromString(t *testing.T, timeStr, format string) metav1.Time {
	timeV, err := time.Parse(format, timeStr)
	if err != nil {
		panic(err)
	}
	return metav1.NewTime(timeV)
}

func TestBCIPodDetailToPod(t *testing.T) {
	type args struct {
		bpod *bci.DescribePodResponse
	}
	tests := []struct {
		name    string
		args    args
		want    *v1.Pod
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal pod with pod phase embeded in container status",
			args: func() args {
				respBytes := []byte(`{"name":"default-restart-log-deployment-545697b4cf-vn7kk","podId":"p-7qVeBrsh","podUuid":"95d6f8d8-de25-4954-81a6-9175d3571839","status":"Running","memory":0.5,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"cce-uz84j3uq","internalIp":"**************","securityGroupUuid":"g-8ie3rndjkswy","restartPolicy":"Always","orderId":"b5d5f6716e5c44d1afbf2b61c35d1d33","createdTime":"2021-04-06T10:54:23Z","updatedTime":"2021-04-06T10:58:19Z","deletedTime":null,"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"emptyDir":[],"configFile":[{"name":"default-token-czhv9","configFiles":[{"path":"ca.crt","file":"-----BEGIN CERTIFICATE-----\nMIIDnjCCAoagAwIBAgIURVtd59dciGH2Sx8EaCLsR8B1hIgwDQYJKoZIhvcNAQEL\nBQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl\naUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG\nA1UEAxMFanBhYXMwHhcNMjEwNDA2MDkyNDAwWhcNMzEwNDA0MDkyNDAwWjBnMQsw\nCQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO\nMAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq\ncGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAM55VHEwb6sQE5YO\nerEm/kyc6VkAyR6Zq+OJKgSvkWe66g56m8fcXGQzvjZdkg3/y8gDUDbjOXCWu5C+\n7LiU1wukC2p9EJ03jzol+xDJB8QtriV+PIUmU+O/4kpLesGMKKqw7A9nv76vxDX+\nEHiRDiwQylNind4FMaNwU1J/qax74m0XTE/NZ7TcbeT+qOfvGLqBKYDN0tPQeCe0\neyGaB00VG7INOlUlHk30S9C9lgdi9PzD8Io5R0DWwHK0SIdPzclVtc7Z/2pBLYdA\ncObdn2F0qqWY7ikncFdQAPN9h4aGaWqOHknfjfbb7mRHsY5MTp3jJ1XEm9s+AT/+\nSgGDeTsCAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFB24LZ2xCnsweUGD1xGeBQqcQexTMA0GCSqGSIb3DQEBCwUAA4IB\nAQBqT3v2dYcV9JA8t+dknOIpqihXDppzJ6Zfbq81BQ0TUk0rrj6SiWJD9QWbp/Oi\n16kEOTjoV7G+v+KdHKEf7I/dKcWmpmeVW9HwuELK9rpByxWJDf4uPWlGawBa+YDs\nhv1K197fQrYWDP4zpShCAvpsx2BTc874CymHjjEhiK/Oro2PMB3J7kiwiWigm3+X\nEowDzsFr/aIo7dOcgD0BXkxgsBjDabq3S2Xz0sgySHv/3mQ+ZTml5idja4KzUXiN\naU6xilMcdqkzAwJnW7VgNPwZLss7Sysi/AYt4811hdpnTdklhXFTgrqtYkVrOl+U\naPJZcZMrz5kRCnFXqzhNsjfm\n-----END CERTIFICATE-----\n"},{"path":"namespace","file":"default"},{"path":"token","file":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}]}],"containers":[{"name":"container-0","containerUuid":"0190401ddd30f5fd86f9e2687d2f38d1c8e675f90718a89a103793d6027acb84","imageName":"nginx-alpine-go","imageVersion":"latest","imageAddress":"hub.baidubce.com/cce/nginx-alpine-go","cpu":0.25,"memory":0.5,"workingDir":"","imagePullPolicy":"Always","commands":["/bin/sh","-c","for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"],"args":[],"ports":[],"volumeMounts":[{"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount","readOnly":true,"name":"default-token-czhv9","type":"ConfigFile"}],"envs":[{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443"},{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"**************"},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443"},{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp"},{"key":"KUBERNETES_PORT","value":"tcp://**************:443"},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://**************:443"},{"key":"KUBERNETES_SERVICE_HOST","value":"**************"},{"key":"MY_IP","value":""},{"key":"KUBERNETES_SERVICE_PORT","value":"443"}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"state":"Succeeded","containerStartTime":"2021-04-06T10:57:05Z","exitCode":0,"containerFinishTime":"2021-04-06T10:57:55Z","detailStatus":"Completed"},"currentState":{"state":"Running","containerStartTime":"2021-04-06T10:58:18Z","detailStatus":"{\"phase\":\"Running\",\"conditions\":[{\"type\":\"Initialized\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T10:55:10Z\"},{\"type\":\"Ready\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T10:55:10Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [container-0]\"},{\"type\":\"ContainersReady\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T10:55:10Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [container-0]\"},{\"type\":\"PodScheduled\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T10:55:10Z\"}]}","exitCode":0},"restartCount":3},"createdTime":"2021-04-06T10:54:23Z","updatedTime":"2021-04-06T10:58:26Z","deletedTime":null}],"securityGroup":{"id":"90304","securityGroupId":"g-8ie3rndjkswy","uuid":"236bdd2c-1307-4c97-afaa-fa1d11091894","name":"CCE默认安全组","description":"CCE默认安全组，请勿删除","tenantId":"3a0f09d8b3c8460cb9ee653fd2c7069a","associateNum":1,"vpcId":"5278df64-b0c4-4124-a408-c9b118d56545","vpcShortId":null,"creator":null},"vpc":{"vpcId":"5278df64-b0c4-4124-a408-c9b118d56545","shortId":"vpc-53ujns4xehnp","name":"默认私有网络","cidr":"***********/16","status":0,"securityGroupNum":0,"subnetNum":0,"createTime":"2017-05-27T05:36:06Z","description":"default","defaultVpc":true},"subnet":{"name":"系统预定义子网B","subnetId":"b3fa07df-45a7-4ddf-adb6-d7e3cc979474","az":"zoneB","cidr":"************/20","vpcId":"5278df64-b0c4-4124-a408-c9b118d56545","vpcShortId":"vpc-53ujns4xehnp","subnetUuid":"b3fa07df-45a7-4ddf-adb6-d7e3cc979474","accountId":"eca97e148cb74e9683d7b7240829d1ff","subnetType":1,"type":1,"createdTime":"2018-09-29T14:01:19Z","updatedTime":"2018-09-29T14:01:19Z","description":"","shortId":"sbn-m6amr0pww3e1","usedIps":-1,"totalIps":-1},"logicalZone":"","region":"su","subnetType":"BCC","eipGroupId":"","labels":[{"labelKey":"UID","labelValue":"5708247c-6836-444b-afe9-f8c0887439fb"},{"labelKey":"PodName","labelValue":"restart-log-deployment-545697b4cf-vn7kk"},{"labelKey":"Namespace","labelValue":"default"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet"},{"labelKey":"CCEClusterID","labelValue":"cce-uz84j3uq"},{"labelKey":"CreationTimestamp","labelValue":"**********"},{"labelKey":"AccountID","labelValue":"eca97e148cb74e9683d7b7240829d1ff"}],"application":"default","pushLog":false,"vCpu":0.25,"tags":null}`)
				bpod := &bci.DescribePodResponse{}
				if err := json.Unmarshal(respBytes, bpod); err != nil {
					t.Fatal(err)
				}
				return args{
					bpod: bpod,
				}
			}(),
			want: &v1.Pod{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Pod",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:        "restart-log-deployment-545697b4cf-vn7kk",
					Namespace:   "default",
					ClusterName: "cce-uz84j3uq",
					UID:         "5708247c-6836-444b-afe9-f8c0887439fb",
					CreationTimestamp: func() metav1.Time {
						ct := time.Unix(**********, 0)
						return metav1.NewTime(ct)
					}(),
				},
				Spec: v1.PodSpec{
					NodeName: "bci-virtual-kubelet",
					Volumes:  []v1.Volume{},
					Containers: []v1.Container{
						{
							Name:    "container-0",
							Image:   "hub.baidubce.com/cce/nginx-alpine-go:latest",
							Command: []string{"/bin/sh", "-c", "for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"},
							Args:    []string{},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("0.25"),
									v1.ResourceMemory: resource.MustParse("0.5Gi"),
								},
							},
						},
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodRunning,
					Conditions: []v1.PodCondition{
						{
							Type:               v1.PodInitialized,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T10:54:23Z", time.RFC3339),
						},
						{
							Type:               v1.PodReady,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T10:54:23Z", time.RFC3339),
						},
						{
							Type:               v1.ContainersReady,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T10:54:23Z", time.RFC3339),
						},
						{
							Type:               v1.PodScheduled,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T10:54:23Z", time.RFC3339),
						},
					},
					PodIP: "**************",
					StartTime: func() *metav1.Time {
						v := getMetaV1TimeFromString(t, "2021-04-06T10:54:23Z", time.RFC3339)
						return &v
					}(),
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name:         "container-0",
							Ready:        true,
							RestartCount: 3,
							Image:        "hub.baidubce.com/cce/nginx-alpine-go:latest",
							ContainerID:  "bci://d79f10e88b8822723cad71d3fe115e77a67af0527063fa0f1c62a29a51e48278",
							State: v1.ContainerState{
								Running: &v1.ContainerStateRunning{
									StartedAt: getMetaV1TimeFromString(t, "2021-04-06T10:58:18Z", time.RFC3339),
								},
							},
							LastTerminationState: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:   0,
									FinishedAt: getMetaV1TimeFromString(t, "2021-04-06T10:57:55Z", time.RFC3339),
									StartedAt:  getMetaV1TimeFromString(t, "2021-04-06T10:57:05Z", time.RFC3339),
									Reason:     "Completed",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "ImagePullBackOff with pod phase embeded in container status",
			args: func() args {
				respBytes := []byte(`{"name":"default-yezichao-5545b55b75-6ck7b","podId":"p-qMFdJEuM","podUuid":"b4a20975-82a0-4cf8-bd74-10fe8d38174f","status":"Running","memory":2.0,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"cce-1nae0bdz","internalIp":"************","securityGroupUuid":"g-nwqefq8kp7w6","restartPolicy":"Always","orderId":"8f8a9a46da7f4e208c03b5a755972c86","createdTime":"2021-04-06T14:25:45Z","updatedTime":"2021-04-06T14:37:09Z","deletedTime":null,"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"emptyDir":[],"configFile":[{"name":"default-token-qpr4m","configFiles":[{"path":"namespace","file":"default"},{"path":"token","file":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"},{"path":"ca.crt","file":"-----BEGIN CERTIFICATE-----\nMIIDnjCCAoagAwIBAgIUWdimFGouhh4OhYUMuK9Croago9YwDQYJKoZIhvcNAQEL\nBQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl\naUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG\nA1UEAxMFanBhYXMwHhcNMjEwMzIzMDkwNjAwWhcNMzEwMzIxMDkwNjAwWjBnMQsw\nCQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO\nMAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq\ncGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKoZosJ08IzhUW8O\nKqrawF9+imQl0xQ8RIoEUIZ/LI1ZnGjfqk7wvVVAHiE/sOYlCEgeqoeKpqT1ynqf\nd+kgwMCVYscsniukqMJ1fRY69nWSfEhHfeZbU3vyor0X/TvbqVkXSUPiCWN2kW2s\ndkIDDI+8IIf3Rgzan0TQfP4hqpFWq3xKdYa0J5j6r2it2ZMuvLVykAQYs4MFmvEy\nBmhMMjTntV/40SVeB37OoXomMA9Rti/zkjHd9Q6+lo+vv4IhlgNWYBLyTSk5qVvf\n1Mxzg/2moO8QMYgIlPtN3vi8SP2b5S2eA6bf0xEs7f8zXqsJ+ORI36yiyk2ElmAd\nW0Y8888CAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFKhYRacbXKI333sEp3au1QNFIRS9MA0GCSqGSIb3DQEBCwUAA4IB\nAQAt77woDBnnRMCHkvkWcnjKH5r1v3Xqhy3q++o0S4MA58JRn0AwJ/fPDb9pVqXX\nFTJp8Xr4XPS+MctInoIiMVXNdHVWMouIUQn71oE6LxyKxzI6VcT4YQjVem4YlqXb\nRlHj15puglpNANNYULmvRkhKJUiG+d8DCZ4P0rfDC8CJsuMV0Wdm9PdTOKSdrTeh\nwPUgXcwL6WB54OSm0CDR72/ThzVRj0lV9j6i4MM0uO//IgEq4AoZmmzYJdKAwuEc\ndK4KAJI57R5cCsbHtSpDs7HJnBtIyAs1Vg76fgu0oQpHOXQQIVW4v8h1B06z+cf6\nZLjfcSLKr8Sm6uO/UvBcxr8e\n-----END CERTIFICATE-----\n"}]}],"containers":[{"name":"yezichao","containerUuid":"","imageName":"nginx","imageVersion":"latest","imageAddress":"hub.baidubce.com/cce/nginx","cpu":1.0,"memory":2.0,"workingDir":"","imagePullPolicy":"Always","commands":[],"args":[],"ports":[],"volumeMounts":[{"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount","readOnly":true,"name":"default-token-qpr4m","type":"ConfigFile"}],"envs":[{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"**********"},{"key":"KUBERNETES_SERVICE_HOST","value":"**********"},{"key":"KUBERNETES_SERVICE_PORT","value":"443"},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443"},{"key":"KUBERNETES_PORT","value":"tcp://**********:443"},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://**********:443"},{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp"},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443"}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"state":"","exitCode":0,"detailStatus":""},"currentState":{"state":"Creating","detailStatus":"{\"phase\":\"Pending\",\"conditions\":[{\"type\":\"Initialized\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:26:13Z\"},{\"type\":\"Ready\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:26:13Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [yezichao]\"},{\"type\":\"ContainersReady\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:26:13Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [yezichao]\"},{\"type\":\"PodScheduled\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:26:13Z\"}],\"container_detail_status\":\"ImagePullBackOff;;Back-off pulling image \\\"hub.baidubce.com/cce/nginx:latest\\\"\"}","exitCode":0},"restartCount":0},"createdTime":"2021-04-06T14:25:46Z","updatedTime":"2021-04-06T14:37:10Z","deletedTime":null}],"securityGroup":{"id":"593205","securityGroupId":"g-nwqefq8kp7w6","uuid":"706af6e1-07f9-43af-9ae1-d20f1fe6f9fe","name":"CCE默认安全组","description":"CCE默认安全组，请勿删除","tenantId":"3a0f09d8b3c8460cb9ee653fd2c7069a","associateNum":1,"vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","vpcShortId":null,"creator":null},"vpc":{"vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","shortId":"vpc-wzs8gqw7hwyy","name":"chenhuan-nat","cidr":"***********/16","status":0,"securityGroupNum":0,"subnetNum":0,"createTime":"2019-09-03T13:15:32Z","description":"","defaultVpc":false},"subnet":{"name":"bci-subnet-zoneD","subnetId":"0aabe3b1-4d2e-4bc6-b392-7cc015ab8837","az":"zoneD","cidr":"************/20","vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","vpcShortId":"vpc-wzs8gqw7hwyy","subnetUuid":"0aabe3b1-4d2e-4bc6-b392-7cc015ab8837","accountId":"eca97e148cb74e9683d7b7240829d1ff","subnetType":1,"type":0,"createdTime":"2021-03-22T17:01:09Z","updatedTime":"2021-03-22T17:01:09Z","description":"","shortId":"sbn-ff7f3eah1ujy","usedIps":-1,"totalIps":-1},"logicalZone":"","region":"bj","subnetType":"BCC","eipGroupId":"","labels":[{"labelKey":"UID","labelValue":"2248ad15-f708-48f9-918e-df91c638cd2d"},{"labelKey":"PodName","labelValue":"yezichao-5545b55b75-6ck7b"},{"labelKey":"Namespace","labelValue":"default"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet"},{"labelKey":"CCEClusterID","labelValue":"cce-1nae0bdz"},{"labelKey":"CreationTimestamp","labelValue":"**********"},{"labelKey":"AccountID","labelValue":"eca97e148cb74e9683d7b7240829d1ff"}],"application":"default","pushLog":false,"vCpu":1.0,"tags":null}`)
				bpod := &bci.DescribePodResponse{}
				if err := json.Unmarshal(respBytes, bpod); err != nil {
					t.Fatal(err)
				}
				return args{
					bpod: bpod,
				}
			}(),
			want: &v1.Pod{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Pod",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:        "yezichao-5545b55b75-6ck7b",
					Namespace:   "default",
					ClusterName: "cce-1nae0bdz",
					UID:         "2248ad15-f708-48f9-918e-df91c638cd2d",
					CreationTimestamp: func() metav1.Time {
						ct := time.Unix(**********, 0)
						return metav1.NewTime(ct)
					}(),
				},
				Spec: v1.PodSpec{
					NodeName: "bci-virtual-kubelet",
					Volumes:  []v1.Volume{},
					Containers: []v1.Container{
						{
							Name:    "yezichao",
							Image:   "hub.baidubce.com/cce/nginx:latest",
							Command: []string{},
							Args:    []string{},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("1.0"),
									v1.ResourceMemory: resource.MustParse("2.0Gi"),
								},
							},
						},
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodPending,
					Conditions: []v1.PodCondition{
						{
							Type:               v1.PodInitialized,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:25:45Z", time.RFC3339),
						},
						{
							Type:               v1.PodReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:25:45Z", time.RFC3339),
						},
						{
							Type:               v1.ContainersReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:25:45Z", time.RFC3339),
						},
						{
							Type:               v1.PodScheduled,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:25:45Z", time.RFC3339),
						},
					},
					PodIP: "************",
					StartTime: func() *metav1.Time {
						v := getMetaV1TimeFromString(t, "2021-04-06T14:25:45Z", time.RFC3339)
						return &v
					}(),
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name:         "yezichao",
							Ready:        false,
							RestartCount: 0,
							Image:        "hub.baidubce.com/cce/nginx:latest",
							ContainerID:  "",
							State: v1.ContainerState{
								Waiting: &v1.ContainerStateWaiting{
									Reason:  "ImagePullBackOff",
									Message: `Back-off pulling image "hub.baidubce.com/cce/nginx:latest"`,
								},
							},
						},
					},
				},
			},
		},
		{

			name: "CrashLoopBackOff with pod phase embeded in container status",
			args: func() args {
				respBytes := []byte(`{"name":"default-restart-log-deployment-545697b4cf-z8cvm","podId":"p-Ximtc1kZ","podUuid":"8aa583d3-70f2-496d-8ba2-a723be696484","status":"Running","memory":0.5,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"cce-1nae0bdz","internalIp":"************","securityGroupUuid":"g-nwqefq8kp7w6","restartPolicy":"Always","orderId":"47c9cda3d424415cbc5e1a6ac694262e","createdTime":"2021-04-06T14:45:16Z","updatedTime":"2021-04-06T14:54:01Z","deletedTime":null,"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"emptyDir":[],"configFile":[{"name":"default-token-qpr4m","configFiles":[{"path":"ca.crt","file":"-----BEGIN CERTIFICATE-----\nMIIDnjCCAoagAwIBAgIUWdimFGouhh4OhYUMuK9Croago9YwDQYJKoZIhvcNAQEL\nBQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl\naUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG\nA1UEAxMFanBhYXMwHhcNMjEwMzIzMDkwNjAwWhcNMzEwMzIxMDkwNjAwWjBnMQsw\nCQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO\nMAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq\ncGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKoZosJ08IzhUW8O\nKqrawF9+imQl0xQ8RIoEUIZ/LI1ZnGjfqk7wvVVAHiE/sOYlCEgeqoeKpqT1ynqf\nd+kgwMCVYscsniukqMJ1fRY69nWSfEhHfeZbU3vyor0X/TvbqVkXSUPiCWN2kW2s\ndkIDDI+8IIf3Rgzan0TQfP4hqpFWq3xKdYa0J5j6r2it2ZMuvLVykAQYs4MFmvEy\nBmhMMjTntV/40SVeB37OoXomMA9Rti/zkjHd9Q6+lo+vv4IhlgNWYBLyTSk5qVvf\n1Mxzg/2moO8QMYgIlPtN3vi8SP2b5S2eA6bf0xEs7f8zXqsJ+ORI36yiyk2ElmAd\nW0Y8888CAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFKhYRacbXKI333sEp3au1QNFIRS9MA0GCSqGSIb3DQEBCwUAA4IB\nAQAt77woDBnnRMCHkvkWcnjKH5r1v3Xqhy3q++o0S4MA58JRn0AwJ/fPDb9pVqXX\nFTJp8Xr4XPS+MctInoIiMVXNdHVWMouIUQn71oE6LxyKxzI6VcT4YQjVem4YlqXb\nRlHj15puglpNANNYULmvRkhKJUiG+d8DCZ4P0rfDC8CJsuMV0Wdm9PdTOKSdrTeh\nwPUgXcwL6WB54OSm0CDR72/ThzVRj0lV9j6i4MM0uO//IgEq4AoZmmzYJdKAwuEc\ndK4KAJI57R5cCsbHtSpDs7HJnBtIyAs1Vg76fgu0oQpHOXQQIVW4v8h1B06z+cf6\nZLjfcSLKr8Sm6uO/UvBcxr8e\n-----END CERTIFICATE-----\n"},{"path":"namespace","file":"default"},{"path":"token","file":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}]}],"containers":[{"name":"container-0","containerUuid":"16a68af3213462df0050b82dd1c37c6d05507420564419b6a4b1b6c97a5173ce","imageName":"nginx-alpine-go","imageVersion":"latest","imageAddress":"hub.baidubce.com/cce/nginx-alpine-go","cpu":0.25,"memory":0.5,"workingDir":"","imagePullPolicy":"Always","commands":["/bin/sh","-c","for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"],"args":[],"ports":[],"volumeMounts":[{"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount","readOnly":true,"name":"default-token-qpr4m","type":"ConfigFile"}],"envs":[{"key":"KUBERNETES_SERVICE_PORT","value":"443"},{"key":"KUBERNETES_PORT","value":"tcp://**********:443"},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://**********:443"},{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp"},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443"},{"key":"MY_IP","value":""},{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"**********"},{"key":"KUBERNETES_SERVICE_HOST","value":"**********"},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443"}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"state":"Succeeded","containerStartTime":"2021-04-06T14:53:03Z","exitCode":0,"containerFinishTime":"2021-04-06T14:53:53Z","detailStatus":"Completed"},"currentState":{"state":"Creating","detailStatus":"{\"phase\":\"Running\",\"conditions\":[{\"type\":\"Initialized\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:45:51Z\"},{\"type\":\"Ready\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:45:51Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [container-0]\"},{\"type\":\"ContainersReady\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:45:51Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [container-0]\"},{\"type\":\"PodScheduled\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-04-06T14:45:51Z\"}],\"container_detail_status\":\"CrashLoopBackOff;;Back-off 2m40s restarting failed container=container-0 pod=pod-8aa583d3-70f2-496d-8ba2-a723be696484_default(8aa583d3-70f2-496d-8ba2-a723be696484)\"}","exitCode":0},"restartCount":5},"createdTime":"2021-04-06T14:45:16Z","updatedTime":"2021-04-06T14:54:09Z","deletedTime":null}],"securityGroup":{"id":"593205","securityGroupId":"g-nwqefq8kp7w6","uuid":"706af6e1-07f9-43af-9ae1-d20f1fe6f9fe","name":"CCE默认安全组","description":"CCE默认安全组，请勿删除","tenantId":"3a0f09d8b3c8460cb9ee653fd2c7069a","associateNum":1,"vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","vpcShortId":null,"creator":null},"vpc":{"vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","shortId":"vpc-wzs8gqw7hwyy","name":"chenhuan-nat","cidr":"***********/16","status":0,"securityGroupNum":0,"subnetNum":0,"createTime":"2019-09-03T13:15:32Z","description":"","defaultVpc":false},"subnet":{"name":"bci-subnet-zoneD","subnetId":"0aabe3b1-4d2e-4bc6-b392-7cc015ab8837","az":"zoneD","cidr":"************/20","vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","vpcShortId":"vpc-wzs8gqw7hwyy","subnetUuid":"0aabe3b1-4d2e-4bc6-b392-7cc015ab8837","accountId":"eca97e148cb74e9683d7b7240829d1ff","subnetType":1,"type":0,"createdTime":"2021-03-22T17:01:09Z","updatedTime":"2021-03-22T17:01:09Z","description":"","shortId":"sbn-ff7f3eah1ujy","usedIps":-1,"totalIps":-1},"logicalZone":"","region":"bj","subnetType":"BCC","eipGroupId":"","labels":[{"labelKey":"UID","labelValue":"b4dc3286-cc4e-486b-88ba-b6736e3fa766"},{"labelKey":"PodName","labelValue":"restart-log-deployment-545697b4cf-z8cvm"},{"labelKey":"Namespace","labelValue":"default"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet"},{"labelKey":"CCEClusterID","labelValue":"cce-1nae0bdz"},{"labelKey":"CreationTimestamp","labelValue":"**********"},{"labelKey":"AccountID","labelValue":"eca97e148cb74e9683d7b7240829d1ff"}],"application":"default","pushLog":false,"vCpu":0.25,"tags":null}`)
				bpod := &bci.DescribePodResponse{}
				if err := json.Unmarshal(respBytes, bpod); err != nil {
					t.Fatal(err)
				}
				return args{
					bpod: bpod,
				}
			}(),
			want: &v1.Pod{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Pod",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:        "restart-log-deployment-545697b4cf-z8cvm",
					Namespace:   "default",
					ClusterName: "cce-1nae0bdz",
					UID:         "b4dc3286-cc4e-486b-88ba-b6736e3fa766",
					CreationTimestamp: func() metav1.Time {
						ct := time.Unix(**********, 0)
						return metav1.NewTime(ct)
					}(),
				},
				Spec: v1.PodSpec{
					NodeName: "bci-virtual-kubelet",
					Volumes:  []v1.Volume{},
					Containers: []v1.Container{
						{
							Name:    "container-0",
							Image:   "hub.baidubce.com/cce/nginx-alpine-go:latest",
							Command: []string{"/bin/sh", "-c", "for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"},
							Args:    []string{},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("0.25"),
									v1.ResourceMemory: resource.MustParse("0.5Gi"),
								},
							},
						},
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodRunning,
					Conditions: []v1.PodCondition{
						{
							Type:               v1.PodInitialized,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:45:16Z", time.RFC3339),
						},
						{
							Type:               v1.PodReady,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:45:16Z", time.RFC3339),
						},
						{
							Type:               v1.ContainersReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:45:16Z", time.RFC3339),
						},
						{
							Type:               v1.PodScheduled,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-04-06T14:45:16Z", time.RFC3339),
						},
					},
					PodIP: "************",
					StartTime: func() *metav1.Time {
						v := getMetaV1TimeFromString(t, "2021-04-06T14:45:16Z", time.RFC3339)
						return &v
					}(),
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name:         "container-0",
							Ready:        false,
							RestartCount: 5,
							Image:        "hub.baidubce.com/cce/nginx-alpine-go:latest",
							ContainerID:  "bci://ba9bcdd72c932e132b88f3066fd1ed1f2bfe6884b61e6a4efa3c41d6ee00671e",
							State: v1.ContainerState{
								Waiting: &v1.ContainerStateWaiting{
									Reason:  "CrashLoopBackOff",
									Message: "Back-off 2m40s restarting failed container=container-0 pod=pod-8aa583d3-70f2-496d-8ba2-a723be696484_default(8aa583d3-70f2-496d-8ba2-a723be696484)",
								},
							},
							LastTerminationState: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:   0,
									FinishedAt: getMetaV1TimeFromString(t, "2021-04-06T14:53:53Z", time.RFC3339),
									StartedAt:  getMetaV1TimeFromString(t, "2021-04-06T14:53:03Z", time.RFC3339),
									Reason:     "Completed",
								},
							},
						},
					},
				},
			},
		},
		{
			name: "running pod on crashed host",
			args: func() args {
				respBytes := []byte(`{"name":"default-nginx-deployment-6955f6ccd8-hq8lm","podId":"p-ZNSUYHpI","podUuid":"83fba8c4-bcf0-4ab5-8720-2349ddf5e43f","status":"Crashed","memory":2.0,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"cce-rbbt9qcx","internalIp":"**************","securityGroupUuid":"g-nwqefq8kp7w6","restartPolicy":"Always","orderId":"3f213f8f5c764d7e998c74593ae5aca9","createdTime":"2021-05-07T16:15:34Z","updatedTime":"2021-07-05T15:05:14Z","deletedTime":null,"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"emptyDir":[],"configFile":[{"name":"default-token-xzt94","configFiles":[{"path":"namespace","file":"default"},{"path":"token","file":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"},{"path":"ca.crt","file":"-----BEGIN CERTIFICATE-----\nMIIDnjCCAoagAwIBAgIUFaB+3V2P2f27xQwZJ4CrwL5pd/0wDQYJKoZIhvcNAQEL\nBQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl\naUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG\nA1UEAxMFanBhYXMwHhcNMjEwNDI4MTUzNTAwWhcNMzEwNDI2MTUzNTAwWjBnMQsw\nCQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO\nMAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq\ncGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAO2efhm7Qz43o0YE\nGpg4Y0fjh7zdq3uiGTI4QT9w5cIuc+FActXMQkmdxbJGfMnU31R1iAWGALP0os2N\nbWwhefGYezYhFHZNuyKmAfII8rRv0gAUVeGSaFlMFswz04SjwLfuTFf6uGr1zg14\nJc7nH+q2jGfSDKqxHjh457OHBqchs9tM689+XqPB/M2M5eQZ9Zcwa4rbTo4hokq1\nMWMHUHwWoIuOPHurBS+vQ349HdlbZ94KeypEv6pIB1FLTPNv4Z9nGfr4dqReuZRi\n7NwaiwEDnTnq7NVokRwuR9x9fuWRmZsZxhzvi5KSMuoWQp3asG7M1tFlkpt7vIL8\n6yuI8M8CAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFIcWWF2wTxVDF8QVpgLQxLigSqtIMA0GCSqGSIb3DQEBCwUAA4IB\nAQCBHtE0qkMztMJK7vocD/K9CAhtmxmesx0mbgyiVQnLCOFIXDu40onjogcsxv5n\n2jtXnM8ZLrTN6Hps6q6h9nOw6usjWkCFrQ6E5wVP+ZIqnqxV66/p+9hOmk6QO0u7\nq+JGfwOEPBT3FLQ8BCWl5wC+aI34dH5EAjtfes0GbDQCGkQgpe42MkrumbVzTHnL\n7a78oTz4IlcIq5LnefeMBr9q0J4hwQeEiQGNPx+Qq74Pr8QgXdpDnK+XujObA2Qd\nYdbAkFW4x17mYQdjSEKk3DHNPVCd5AMJKwVB4INlvnQyjnAgdHSf9NyAYOD+J7et\n6Lj4dt7hgDYsencSdAL3qn7K\n-----END CERTIFICATE-----\n"}]}],"containers":[{"name":"container-0","containerUuid":"f58551c80b1473529e2d3362cd0a630a15c04708c2f1e18f37dba5e4282c7149","imageName":"netshoot","imageVersion":"latest","imageAddress":"registry.baidubce.com/bci-test/netshoot","cpu":1.0,"memory":2.0,"workingDir":"","imagePullPolicy":"Always","commands":["sleep","360000"],"args":[],"ports":[],"volumeMounts":[{"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount","readOnly":true,"name":"default-token-xzt94","type":"ConfigFile"}],"envs":[{"key":"KUBERNETES_PORT","value":"tcp://************:443"},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://************:443"},{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp"},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443"},{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"************"},{"key":"KUBERNETES_SERVICE_HOST","value":"************"},{"key":"KUBERNETES_SERVICE_PORT","value":"443"},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443"}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"state":"Succeeded","containerStartTime":"2021-06-30T20:16:27Z","exitCode":0,"containerFinishTime":"2021-07-05T00:16:28Z","detailStatus":"Completed"},"currentState":{"state":"Running","containerStartTime":"2021-07-05T00:16:28Z","detailStatus":"{\"phase\":\"Running\",\"conditions\":[{\"type\":\"Initialized\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-05-07T16:16:03Z\"},{\"type\":\"Ready\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-05-07T16:16:03Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [container-0]\"},{\"type\":\"ContainersReady\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-05-07T16:16:03Z\",\"reason\":\"ContainersNotReady\",\"message\":\"containers with unready status: [container-0]\"},{\"type\":\"PodScheduled\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-05-07T16:16:03Z\"}]}","exitCode":0},"restartCount":14},"createdTime":"2021-05-07T16:15:34Z","updatedTime":"2021-07-05T15:05:21Z","deletedTime":null}],"securityGroup":{"id":"593205","securityGroupId":"g-nwqefq8kp7w6","uuid":"706af6e1-07f9-43af-9ae1-d20f1fe6f9fe","name":"CCE默认安全组","description":"CCE默认安全组，请勿删除","tenantId":"3a0f09d8b3c8460cb9ee653fd2c7069a","associateNum":1,"vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","vpcShortor":null},"vpc":{"vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","shortId":"vpc-wzs8gqw7hwyy","name":"chenhuan-nat","cidr":"***********/16","status":0,"securityGroupNum":0,"subnetNum":0,"createTime":"2019-09-03T13:15:32Z","description":"","defaultVpc":false},"subnet":{"name":"bci-subnet-zoneD","subnetId":"0aabe3b1-4d2e-4bc6-b392-7cc015ab8837","az":"zoneD","cidr":"************/20","vpcId":"7d0d8d2e-2f20-4d34-bf4e-c056f3fd8e05","vpcShortId":"vpc-wzs8gqw7hwyy","subnetUuid":"0aabe3b1-4d2e-4bc6-b392-7cc015ab8837","accountId":"eca97e148cb74e9683d7b7240829d1ff","subnetType":1,"type":0,"createdTime":"2021-03-22T17:01:09Z","updatedTime":"2021-03-22T17:01:09Z","description":"","shortId":"sbn-ff7f3eah1ujy","usedIps":-1,"totalIps":-1},"logicalZone":"","region":"bj","subnetType":"BCC","eipGroupId":"","labels":[{"labelKey":"UID","labelValue":"ddecdb6c-a836-473a-bf22-48d03d95d4c1"},{"labelKey":"PodName","labelValue":"nginx-deployment-6955f6ccd8-hq8lm"},{"labelKey":"Namespace","labelValue":"default"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet"},{"labelKey":"CCEClusterID","labelValue":"cce-rbbt9qcx"},{"labelKey":"CreationTimestamp","labelValue":"**********"},{"labelKey":"AccountID","labelValue":"eca97e148cb74e9683d7b7240829d1ff"}],"application":"default","pushLog":true,"vCpu":1.0,"tags":null}`)
				bpod := &bci.DescribePodResponse{}
				if err := json.Unmarshal(respBytes, bpod); err != nil {
					t.Fatal(err)
				}
				return args{
					bpod: bpod,
				}
			}(),
			//
			want: &v1.Pod{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Pod",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:        "nginx-deployment-6955f6ccd8-hq8lm",
					Namespace:   "default",
					ClusterName: "cce-rbbt9qcx",
					UID:         "ddecdb6c-a836-473a-bf22-48d03d95d4c1",
					CreationTimestamp: func() metav1.Time {
						ct := time.Unix(**********, 0)
						return metav1.NewTime(ct)
					}(),
				},
				Spec: v1.PodSpec{
					NodeName: "bci-virtual-kubelet",
					Volumes:  []v1.Volume{},
					Containers: []v1.Container{
						{
							Name:    "container-0",
							Image:   "registry.baidubce.com/bci-test/netshoot:latest",
							Command: []string{"sleep", "360000"},
							Args:    []string{},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("1"),
									v1.ResourceMemory: resource.MustParse("2Gi"),
								},
							},
						},
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodFailed,
					Conditions: []v1.PodCondition{
						{
							Type:               v1.PodInitialized,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-05-07T16:15:34Z", time.RFC3339),
						},
						{
							Type:               v1.PodReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-05-07T16:15:34Z", time.RFC3339),
						},
						{
							Type:               v1.ContainersReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-05-07T16:15:34Z", time.RFC3339),
						},
						{
							Type:               v1.PodScheduled,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-05-07T16:15:34Z", time.RFC3339),
						},
					},
					PodIP:   "**************",
					Message: "Host crashed",
					Reason:  "Crashed",
					StartTime: func() *metav1.Time {
						v := getMetaV1TimeFromString(t, "2021-05-07T16:15:34Z", time.RFC3339)
						return &v
					}(),
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name:         "container-0",
							Ready:        false,
							RestartCount: 14,
							Image:        "registry.baidubce.com/bci-test/netshoot:latest",
							ContainerID:  "bci://73a8a4a6e89bffb72369307e8f9b50b52b635a8c30aea813d55cd5b0a3f08914",
							State: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:   137,
									Reason:     "Crashed",
									Message:    "Host crashed",
									StartedAt:  getMetaV1TimeFromString(t, "2021-07-05T00:16:28Z", time.RFC3339),
									FinishedAt: getMetaV1TimeFromString(t, "2021-07-05T15:05:14Z", time.RFC3339),
								},
							},
							LastTerminationState: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:   0,
									FinishedAt: getMetaV1TimeFromString(t, "2021-07-05T00:16:28Z", time.RFC3339),
									StartedAt:  getMetaV1TimeFromString(t, "2021-06-30T20:16:27Z", time.RFC3339),
									Reason:     "Completed",
								},
							},
						},
					},
				},
			},
		},
		{

			name: "terminated pod on crashed host",
			args: func() args {
				respBytes := []byte(`{"name":"default-succeeded-job-m58mr","podId":"p-6bUIeDk4","podUuid":"fa19b56f-25fb-43bd-80a7-c58d20b7e5c0","status":"Crashed","memory":0.5,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"cce-9tswij5t","internalIp":"*************","securityGroupUuid":"g-8ie3rndjkswy","restartPolicy":"OnFailure","orderId":"26efd70b7e064934b8a14ba24eea41e0","createdTime":"2021-07-08T12:04:23Z","updatedTime":"2021-07-08T12:05:32Z","deletedTime":null,"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"emptyDir":[],"configFile":[{"name":"default-token-gpssn","configFiles":[{"path":"ca.crt","file":"-----BEGIN CERTIFICATE-----\nMIIDnjCCAoagAwIBAgIUDcJFY4iBKWCrcPsiJw32RJWWd28wDQYJKoZIhvcNAQEL\nBQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl\naUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG\nA1UEAxMFanBhYXMwHhcNMjEwNjMwMTMxNTAwWhcNMzEwNjI4MTMxNTAwWjBnMQsw\nCQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO\nMAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq\ncGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAL+A1ZCERxG6brqk\noNTpTDKgMIANdnabiGehHWz+JDIIwfKGm1XM/nkSt9L8VeVhlkZENUgHFDdamVZC\naFUmwJCvjDiVvmEf0G3AGQcq7hzs+T34WlaQ6jjgxF1BwY2uzFo8ki4CWyUYxI7R\nyDIQu8ppyimATlNFDW1ivw+RxYHqeW4WLurb4JUdY3AySbQWUc4jn1lsAzS37s0z\nkAeNZQj3Wr8qPcSzEpb7Is5tdm+VwuAUuFA5nJr//FuQ0h4HRiqrIA4GvXFXC+qa\nLODxgy/NzMx2USCZIeTMHxBymD7/zX3l3A0/PIACDslJz0rlcaPw3wgJBLhVLNe0\nXFq01RMCAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFBu8LOoxBGl0ncNyUgTpC5it4kZOMA0GCSqGSIb3DQEBCwUAA4IB\nAQBsRto2D5ucvkgzXQix/rWru4KZba4MW8GeMWnGkuduCGfXwrzl0f7h3mFeqpUa\nen0pR7JhHDgextJSL1KWVmt7OSh2r4o+cLQ3Yml5VA2Kz5DPCG8KXGgiAO646LGC\nKiYTzO6ZTmXc4PIf1z2AQDWqU3llFml6r5mC8pt9j9U0GmdJTEh4idrEAc2VHwQJ\njYwsCzUnT+FDTt2tBHvpAekESF5rWyjOYLsJqvA7oa76dZw5pGjyM5lSgovlzWF5\nDkkiblq/WpxHi5S95qAtdWKnqAKgpcIDXJONI45K7iXOHSHkrmzYOLMfdu/NB/3Y\nxCJIvOOWkMA6U4VuOpMLFq3s\n-----END CERTIFICATE-----\n"},{"path":"namespace","file":"default"},{"path":"token","file":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}]}],"containers":[{"name":"container-0","containerUuid":"cfda9a91681c082ffbb63995bee8bbd0868782dece0e4c056a0a26d514a86842","imageName":"nginx-alpine-go","imageVersion":"latest","imageAddress":"hub.baidubce.com/cce/nginx-alpine-go","cpu":0.25,"memory":0.5,"workingDir":"","imagePullPolicy":"Always","commands":["/bin/sh","-c","for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"],"args":[],"ports":[],"volumeMounts":[{"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount","readOnly":true,"name":"default-token-gpssn","type":"ConfigFile"}],"envs":[{"key":"KUBERNETES_SERVICE_PORT","value":"443"},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443"},{"key":"KUBERNETES_PORT","value":"tcp://*************:443"},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://*************:443"},{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp"},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443"},{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"*************"},{"key":"KUBERNETES_SERVICE_HOST","value":"*************"}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"state":"","exitCode":0,"detailStatus":""},"currentState":{"state":"Succeeded","containerStartTime":"2021-07-08T12:04:39Z","detailStatus":"{\"phase\":\"Succeeded\",\"conditions\":[{\"type\":\"Initialized\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-07-08T12:04:37Z\",\"reason\":\"PodCompleted\"},{\"type\":\"Ready\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-07-08T12:04:37Z\",\"reason\":\"PodCompleted\"},{\"type\":\"ContainersReady\",\"status\":\"False\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-07-08T12:04:37Z\",\"reason\":\"PodCompleted\"},{\"type\":\"PodScheduled\",\"status\":\"True\",\"lastProbeTime\":null,\"lastTransitionTime\":\"2021-07-08T12:04:37Z\"}],\"container_detail_status\":\"Completed\"}","exitCode":0,"containerFinishTime":"2021-07-08T12:05:29Z"},"restartCount":0},"createdTime":"2021-07-08T12:04:23Z","updatedTime":"2021-07-08T12:05:32Z","deletedTime":null}],"securityGroup":{"id":"90304","securityGroupId":"g-8ie3rndjkswy","uuid":"236bdd2c-1307-4c97-afaa-fa1d11091894","name":"CCE默认安全组","description":"CCE默认安全组，请勿删除","tenantId":"3a0f09d8b3c8460cb9ee653fd2c7069a","associateNum":1,"vpcId":"5278df64-b0c4-4124-a408-c9b118d56545","vpcShortId":null,"creator":null},"vpc":{"vpcId":"5278df64-b0c4-4124-a408-c9b118d56545","shortId":"vpc-53ujns4xehnp","name":"默认私有网络","cidr":"***********/16","status":0,"securityGroupNum":0,"subnetNum":0,"createTime":"2017-05-27T05:36:06Z","description":"default","defaultVpc":true},"subnet":{"name":"系统预定义子网B","subnetId":"b3fa07df-45a7-4ddf-adb6-d7e3cc979474","az":"zoneB","cidr":"************/20","vpcId":"5278df64-b0c4-4124-a408-c9b118d56545","vpcShortId":"vpc-53ujns4xehnp","subnetUuid":"b3fa07df-45a7-4ddf-adb6-d7e3cc979474","accountId":"eca97e148cb74e9683d7b7240829d1ff","subnetType":1,"type":1,"createdTime":"2018-09-29T14:01:19Z","updatedTime":"2018-09-29T14:01:19Z","description":"","shortId":"sbn-m6amr0pww3e1","usedIps":-1,"totalIps":-1},"logicalZone":"","region":"su","subnetType":"BCC","eipGroupId":"","labels":[{"labelKey":"UID","labelValue":"120e822e-1d27-4f79-a649-3cd3d665cb4f"},{"labelKey":"PodName","labelValue":"succeeded-job-m58mr"},{"labelKey":"Namespace","labelValue":"default"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet"},{"labelKey":"CCEClusterID","labelValue":"cce-9tswij5t"},{"labelKey":"CreationTimestamp","labelValue":"**********"},{"labelKey":"AccountID","labelValue":"eca97e148cb74e9683d7b7240829d1ff"}],"application":"default","pushLog":false,"vCpu":0.25,"tags":null}`)
				bpod := &bci.DescribePodResponse{}
				if err := json.Unmarshal(respBytes, bpod); err != nil {
					t.Fatal(err)
				}
				return args{
					bpod: bpod,
				}
			}(),
			want: &v1.Pod{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Pod",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:        "succeeded-job-m58mr",
					Namespace:   "default",
					ClusterName: "cce-9tswij5t",
					UID:         "120e822e-1d27-4f79-a649-3cd3d665cb4f",
					CreationTimestamp: func() metav1.Time {
						ct := time.Unix(**********, 0)
						return metav1.NewTime(ct)
					}(),
				},
				Spec: v1.PodSpec{
					NodeName: "bci-virtual-kubelet",
					Volumes:  []v1.Volume{},
					Containers: []v1.Container{
						{
							Name:    "container-0",
							Image:   "hub.baidubce.com/cce/nginx-alpine-go:latest",
							Command: []string{"/bin/sh", "-c", "for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"},
							Args:    []string{},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("250m"),
									v1.ResourceMemory: resource.MustParse("512Mi"),
								},
							},
						},
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodSucceeded,
					Conditions: []v1.PodCondition{
						{
							Type:               v1.PodInitialized,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-07-08T12:04:23Z", time.RFC3339),
						},
						{
							Type:               v1.PodReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-07-08T12:04:23Z", time.RFC3339),
						},
						{
							Type:               v1.ContainersReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-07-08T12:04:23Z", time.RFC3339),
						},
						{
							Type:               v1.PodScheduled,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2021-07-08T12:04:23Z", time.RFC3339),
						},
					},
					PodIP: "*************",
					StartTime: func() *metav1.Time {
						v := getMetaV1TimeFromString(t, "2021-07-08T12:04:23Z", time.RFC3339)
						return &v
					}(),
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name:         "container-0",
							Ready:        false,
							RestartCount: 0,
							Image:        "hub.baidubce.com/cce/nginx-alpine-go:latest",
							ContainerID:  "bci://24e25f04f1f232a382dfe01db8491b92f33a6fb516d903d2d98c078bf5115f02",
							State: v1.ContainerState{
								Terminated: &v1.ContainerStateTerminated{
									ExitCode:   0,
									Reason:     "Completed",
									StartedAt:  getMetaV1TimeFromString(t, "2021-07-08T12:04:39Z", time.RFC3339),
									FinishedAt: getMetaV1TimeFromString(t, "2021-07-08T12:05:29Z", time.RFC3339),
								},
							},
						},
					},
				},
			},
		},
		{
			name: "v2 pod with pod conditions set",
			args: func() args {
				respBytes := []byte(`{"name":"default-succeeded-job-cj6k2","podId":"p-1Uf3Vosj","podUuid":"94c4eec1-fbe5-4cd1-b60a-b5758056d2bb","status":"Running","memory":2.0,"eipUuid":"","publicIp":"","bandwidthInMbps":0,"cceUuid":"cce-g95873vi","internalIp":"*************","securityGroupUuid":"g-6m46vnh1cp47","restartPolicy":"OnFailure","orderId":"b75cc53748684880812a1de8e620f36f","createdTime":"2022-10-20T13:47:40Z","updatedTime":"2022-10-20T13:47:58Z","deletedTime":null,"description":"","userId":"eca97e148cb74e9683d7b7240829d1ff","taskStatus":"","nfs":[],"emptyDir":[],"configFile":[{"name":"default-token-mhzjd","configFiles":[{"path":"ca.crt","file":"-----BEGIN CERTIFICATE-----\nMIIDnjCCAoagAwIBAgIULYMQ32F/WJOBJl8NIstWBIUUqpMwDQYJKoZIhvcNAQEL\nBQAwZzELMAkGA1UEBhMCQ04xEDAOBgNVBAgTB0JlaUppbmcxEDAOBgNVBAcTB0Jl\naUppbmcxDjAMBgNVBAoTBWpwYWFzMRQwEgYDVQQLEwtjbG91ZG5hdGl2ZTEOMAwG\nA1UEAxMFanBhYXMwHhcNMjIwODIzMTE1MzAwWhcNMzIwODIwMTE1MzAwWjBnMQsw\nCQYDVQQGEwJDTjEQMA4GA1UECBMHQmVpSmluZzEQMA4GA1UEBxMHQmVpSmluZzEO\nMAwGA1UEChMFanBhYXMxFDASBgNVBAsTC2Nsb3VkbmF0aXZlMQ4wDAYDVQQDEwVq\ncGFhczCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALDuHz5yg+YGdKCl\njbUdJyL8y3RZgsH45J3syShMnqbJRNi8+ejOZYRD1wlzpRoCzYkuQnbxbnyuDtGk\nqPVe3jsjBURVKN9PdxXyB7BW+d7jNgabJyVHWNKBxpV1YPQHkYEA5Z9VNDcyFSPu\nCreycMNli7l/c+TLdeAjx4BxH0MeNB1j75di2UQ1HSpX6kV91uyAkwO18WXuVmUU\nz+rZiRtqir5Uj0I7PBGpZCJHGgpcChkCi80TSQl6X6G8MxRVXOKETP3TJn3jIWLe\nza5Iid7lxL8LZz5MAwI/p5oHn4c/7pRZ+Q/z7iay20tPn5nG2HcSb/bQJKow3yam\nTd+wLNcCAwEAAaNCMEAwDgYDVR0PAQH/BAQDAgEGMA8GA1UdEwEB/wQFMAMBAf8w\nHQYDVR0OBBYEFMROlgVZ0jCqUkuzWvswwkcn2ao0MA0GCSqGSIb3DQEBCwUAA4IB\nAQCTMB7GMIkwWheLcPl6uam3QQDUh0Jzj+20JZMmxvdlDwvkM5uELp4btgMNpkqF\nV3+aq2kxaESWb07nvzY25oMB/F7lcENodc6XBzUDRA+lk8z4PXn6K1YeeyoAF2ly\nN5j4BtSDmnZfdOGeHarNrcbbEz7aG8ePAoknQxqQkydpgiTftUTFwwa7+Zq6rF9S\nkDZHDhVPw+xpCAhN8oMVDR8lRkf3gJDrss+DwnwhJZ7m50GyU2/U/jrhE5G7t/4O\nhsYGcq/ApXBkXVtOK0G4em8kJkxGR7Z2Yky85oyoy9sIIqER5HiTZzg58OKUzleV\n8T4iQQMSHPADAsbcKpCYvDls\n-----END CERTIFICATE-----\n"},{"path":"namespace","file":"default"},{"path":"token","file":"************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"}]}],"containers":[{"name":"container01","containerUuid":"e30492e4b6feaf0939b83fc05399e03f9b57acf4dbdd7bab351632dc37cce5a4","imageName":"nginx-alpine-go","imageVersion":"latest","imageAddress":"hub.baidubce.com/cce/nginx-alpine-go","cpu":1.0,"memory":2.0,"workingDir":"","imagePullPolicy":"Always","commands":["/bin/sh","-c","for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"],"args":[],"ports":[],"volumeMounts":[{"mountPath":"/var/run/secrets/kubernetes.io/serviceaccount","readOnly":true,"name":"default-token-mhzjd","type":"ConfigFile"}],"envs":[{"key":"KUBERNETES_PORT_443_TCP_PROTO","value":"tcp"},{"key":"KUBERNETES_PORT_443_TCP_PORT","value":"443"},{"key":"KUBERNETES_PORT_443_TCP_ADDR","value":"**********"},{"key":"KUBERNETES_SERVICE_HOST","value":"**********"},{"key":"KUBERNETES_SERVICE_PORT","value":"443"},{"key":"KUBERNETES_SERVICE_PORT_HTTPS","value":"443"},{"key":"KUBERNETES_PORT","value":"tcp://**********:443"},{"key":"KUBERNETES_PORT_443_TCP","value":"tcp://**********:443"}],"userId":"eca97e148cb74e9683d7b7240829d1ff","status":{"previousState":{"state":"","exitCode":0,"detailStatus":""},"currentState":{"state":"Running","containerStartTime":"2022-10-20T13:47:57Z","detailStatus":"","exitCode":0},"restartCount":0,"ready":false,"started":null},"createdTime":"2022-10-20T13:47:40Z","updatedTime":"2022-10-20T13:48:25Z","deletedTime":null}],"securityGroup":{"id":"289450","securityGroupId":"g-6m46vnh1cp47","uuid":"0f8240f8-16f2-4aca-b152-4ce7dee6c72b","name":"默认安全组","description":"default","tenantId":"3a0f09d8b3c8460cb9ee653fd2c7069a","associateNum":1,"vpcId":"45d38bda-00b1-4146-a40a-27885d473353","vpcShortId":null,"creator":null},"vpc":{"vpcId":"45d38bda-00b1-4146-a40a-27885d473353","shortId":"vpc-sr94x4t7dxqx","name":"默认私有网络","cidr":"***********/16","status":0,"securityGroupNum":0,"subnetNum":0,"createTime":"2017-03-27T03:24:58Z","description":"default","defaultVpc":true},"subnet":{"name":"系统预定义子网D","subnetId":"3180bb36-e3b1-447b-8ee8-f663129932ee","az":"zoneD","cidr":"************/20","vpcId":"45d38bda-00b1-4146-a40a-27885d473353","vpcShortId":"vpc-sr94x4t7dxqx","subnetUuid":"3180bb36-e3b1-447b-8ee8-f663129932ee","accountId":"eca97e148cb74e9683d7b7240829d1ff","subnetType":1,"type":1,"createdTime":"2019-01-22T11:40:28Z","updatedTime":"2019-01-22T19:40:28Z","description":"","shortId":"sbn-gbusnjuneep1","usedIps":-1,"totalIps":-1},"logicalZone":"","region":"bj","subnetType":"BCC","eipGroupId":"","labels":[{"labelKey":"UID","labelValue":"3b48fea3-5a2b-45ab-a1de-a884abc9d0e3"},{"labelKey":"PodName","labelValue":"succeeded-job-cj6k2"},{"labelKey":"Namespace","labelValue":"default"},{"labelKey":"NodeName","labelValue":"bci-virtual-kubelet-0"},{"labelKey":"CCEClusterID","labelValue":"cce-g95873vi"},{"labelKey":"CreationTimestamp","labelValue":"**********"}],"application":"default","pushLog":false,"vCpu":1.0,"v2":true,"conditions":[{"lastTransitionTime":"2022-10-21T10:12:01Z","status":"True","type":"Initialized"},{"lastTransitionTime":"2022-10-21T10:11:39Z","message":"containers with unready status: [container01]","reason":"ContainersNotReady","status":"False","type":"Ready"},{"lastTransitionTime":"2022-10-21T10:11:39Z","message":"containers with unready status: [container01]","reason":"ContainersNotReady","status":"False","type":"ContainersReady"},{"lastTransitionTime":"2022-10-21T10:11:39Z","status":"True","type":"PodScheduled"}],"tags":null}`)
				bpod := &bci.DescribePodResponse{}
				if err := json.Unmarshal(respBytes, bpod); err != nil {
					t.Fatal(err)
				}
				return args{
					bpod: bpod,
				}
			}(),
			want: &v1.Pod{
				TypeMeta: metav1.TypeMeta{
					Kind:       "Pod",
					APIVersion: "v1",
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:        "succeeded-job-cj6k2",
					Namespace:   "default",
					ClusterName: "cce-g95873vi",
					UID:         "3b48fea3-5a2b-45ab-a1de-a884abc9d0e3",
					CreationTimestamp: func() metav1.Time {
						ct := time.Unix(**********, 0)
						return metav1.NewTime(ct)
					}(),
				},
				Spec: v1.PodSpec{
					NodeName: "bci-virtual-kubelet-0",
					Volumes:  []v1.Volume{},
					Containers: []v1.Container{
						{
							Name:    "container01",
							Image:   "hub.baidubce.com/cce/nginx-alpine-go:latest",
							Command: []string{"/bin/sh", "-c", "for i in $(seq 1 10); do echo ${i}; date; sleep 5; done"},
							Args:    []string{},
							Resources: v1.ResourceRequirements{
								Requests: v1.ResourceList{
									v1.ResourceCPU:    resource.MustParse("1"),
									v1.ResourceMemory: resource.MustParse("2Gi"),
								},
							},
						},
					},
				},
				Status: v1.PodStatus{
					Phase: v1.PodRunning,
					PodIP: "*************",
					StartTime: func() *metav1.Time {
						v := getMetaV1TimeFromString(t, "2022-10-20T13:47:40Z", time.RFC3339)
						return &v
					}(),
					Conditions: []v1.PodCondition{
						{
							Type:               v1.PodInitialized,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2022-10-21T10:12:01Z", time.RFC3339),
						},
						{
							Type:               v1.PodReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2022-10-21T10:11:39Z", time.RFC3339),
							Reason:             "ContainersNotReady",
							Message:            "containers with unready status: [container01]",
						},
						{
							Type:               v1.ContainersReady,
							Status:             v1.ConditionFalse,
							LastTransitionTime: getMetaV1TimeFromString(t, "2022-10-21T10:11:39Z", time.RFC3339),
							Reason:             "ContainersNotReady",
							Message:            "containers with unready status: [container01]",
						},
						{
							Type:               v1.PodScheduled,
							Status:             v1.ConditionTrue,
							LastTransitionTime: getMetaV1TimeFromString(t, "2022-10-21T10:11:39Z", time.RFC3339),
						},
					},
					ContainerStatuses: []v1.ContainerStatus{
						{
							Name:        "container01",
							Ready:       false,
							Image:       "hub.baidubce.com/cce/nginx-alpine-go:latest",
							ContainerID: "bci://12011af399ced3834b8800465095e3154d08e3ad52a1fb414b970aad7240e858",
							State: v1.ContainerState{
								Running: &v1.ContainerStateRunning{
									StartedAt: metav1.NewTime(func() time.Time {
										v, err := time.Parse(time.RFC3339, "2022-10-20T13:47:57Z")
										if err != nil {
											t.Fatal(err)
										}
										return v
									}()),
								},
							},
						},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := bciPodDetailToPod(context.TODO(), tt.args.bpod)
			if (err != nil) != tt.wantErr {
				t.Errorf("bciPodDetailToPod() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProviderSelectSubnet(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.TODO()
	rand.Seed(time.Now().UnixNano())

	podCache := NewMockPodCache(ctrl)
	p := &BCIProvider{
		podCache: podCache,
		subnetOptions: map[string]*SubnetOption{
			"subnet-1": {
				LogicalZone: "zoneA",
				Weight:      2,
			},
			"subnet-2": {
				LogicalZone: "zoneB",
				Weight:      2,
				Quota: BCIResources{
					CPUInCore:  5,
					MemoryInGB: 10,
				},
			},
			"subnet-3": {
				LogicalZone: "zoneB",
				Weight:      2,
				Quota: BCIResources{
					CPUInCore:  3,
					MemoryInGB: 6,
				},
			},
		},
	}

	// normal case
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-1").Times(10).Return(BCIResources{
		CPUInCore:  2,
		MemoryInGB: 4,
	})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-2").Times(10).Return(BCIResources{})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-3").Times(10).Return(BCIResources{
		CPUInCore:  2,
		MemoryInGB: 4,
	})
	gotSubnets := map[string]string{}
	for i := 0; i < 10; i++ {
		subnet, zone, err := p.selectSubnet(ctx, &v1.Pod{})
		assert.NilError(t, err)
		assert.Assert(t, subnet != "")
		gotSubnets[subnet] = zone
	}
	for sbn, zone := range gotSubnets {
		assert.Assert(t, p.subnetOptions[sbn].LogicalZone == zone)
	}

	// exclude subnet-3 due to out of quota
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-1").Times(10).Return(BCIResources{
		CPUInCore:  2,
		MemoryInGB: 4,
	})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-2").Times(10).Return(BCIResources{})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-3").Times(10).Return(BCIResources{
		CPUInCore:  5,
		MemoryInGB: 10,
	})
	gotSubnets = map[string]string{}
	for i := 0; i < 10; i++ {
		subnet, zone, err := p.selectSubnet(ctx, &v1.Pod{})
		assert.NilError(t, err)
		assert.Assert(t, subnet != "")
		gotSubnets[subnet] = zone
	}
	for sbn, zone := range gotSubnets {
		assert.Assert(t, p.subnetOptions[sbn].LogicalZone == zone)
	}
	_, ok := gotSubnets["subnet-3"]
	assert.Assert(t, !ok)

	// exclude subnet-2 and subnet-3 due to out of quota
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-1").Times(10).Return(BCIResources{})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-2").Times(10).Return(BCIResources{
		CPUInCore:  10,
		MemoryInGB: 20,
	})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-3").Times(10).Return(BCIResources{
		CPUInCore:  5,
		MemoryInGB: 10,
	})
	gotSubnets = map[string]string{}
	for i := 0; i < 10; i++ {
		subnet, zone, err := p.selectSubnet(ctx, &v1.Pod{})
		assert.NilError(t, err)
		assert.Assert(t, subnet != "")
		gotSubnets[subnet] = zone
	}

	assert.Assert(t, len(gotSubnets) == 1)
	assert.Assert(t, gotSubnets["subnet-1"] == p.subnetOptions["subnet-1"].LogicalZone)

	// set quota for subnet-1 and test all subnets are out of quota
	p.subnetOptions["subnet-1"].Quota = BCIResources{
		CPUInCore:  5,
		MemoryInGB: 10,
		Pods:       5,
	}
	// all subnets out of quota
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-1").Times(10).Return(BCIResources{
		CPUInCore:  10,
		MemoryInGB: 20,
	})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-2").Times(10).Return(BCIResources{
		CPUInCore:  10,
		MemoryInGB: 20,
	})
	podCache.EXPECT().GetSubnetUsage(gomock.Any(), "subnet-3").Times(10).Return(BCIResources{
		CPUInCore:  5,
		MemoryInGB: 10,
	})
	gotSubnets = map[string]string{}
	for i := 0; i < 10; i++ {
		_, _, err := p.selectSubnet(ctx, &v1.Pod{})
		assert.ErrorContains(t, err, "no subnet available")
	}
}

func Test_getLifecycleAnnotations(t *testing.T) {
	basePod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "yezichao-5545b55b75-6ck7b",
			Namespace: "default",
			UID:       "2248ad15-f708-48f9-918e-df91c638cd2d",
			CreationTimestamp: func() metav1.Time {
				ct := time.Unix(**********, 0)
				return metav1.NewTime(ct)
			}(),
		},
		Spec: v1.PodSpec{
			NodeName: "bci-virtual-kubelet",
			Volumes:  []v1.Volume{},
			Containers: []v1.Container{
				{
					Name:    "yezichao",
					Image:   "hub.baidubce.com/cce/nginx:latest",
					Command: []string{},
					Args:    []string{},
					Resources: v1.ResourceRequirements{
						Requests: v1.ResourceList{
							v1.ResourceCPU:    resource.MustParse("1.0"),
							v1.ResourceMemory: resource.MustParse("2.0Gi"),
						},
					},
				},
			},
		},
	}
	type args struct {
		pod *v1.Pod
	}
	tests := []struct {
		name string
		args args
		want map[string]map[string]*v1.Handler
	}{
		{
			name: "normal",
			args: args{
				pod: func() *v1.Pod {
					pod := basePod.DeepCopy()
					pod.Spec.Containers = []v1.Container{
						{
							Name:  "container-0",
							Image: "nginx",
							Lifecycle: &v1.Lifecycle{
								PreStop: &v1.Handler{
									Exec: &v1.ExecAction{
										Command: []string{"sleep", "10"},
									},
								},
							},
						},
					}
					pod.Spec.InitContainers = []v1.Container{
						{
							Name:  "init-0",
							Image: "nginx",
							Lifecycle: &v1.Lifecycle{
								PostStart: &v1.Handler{
									Exec: &v1.ExecAction{
										Command: []string{"sh", "-c", "echo 10 > /10.txt"},
									},
								},
							},
						},
					}
					return pod
				}(),
			},
			want: map[string]map[string]*v1.Handler{
				PostStartHookAnnotationKey: {
					"init-0": &v1.Handler{
						Exec: &v1.ExecAction{
							Command: []string{"sh", "-c", "echo 10 > /10.txt"},
						},
					},
				},
				PreStopHookAnnotationKey: {
					"container-0": &v1.Handler{
						Exec: &v1.ExecAction{
							Command: []string{"sleep", "10"},
						},
					},
				},
			},
		},
		{
			name: "empty lifecyle handler",
			args: args{
				pod: basePod.DeepCopy(),
			},
			want: map[string]map[string]*v1.Handler{
				PostStartHookAnnotationKey: {},
				PreStopHookAnnotationKey:   {},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getLifecycleAnnotations(tt.args.pod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func Test_getTerminationParamAnnotations(t *testing.T) {
	basePod := &v1.Pod{
		TypeMeta: metav1.TypeMeta{
			Kind:       "Pod",
			APIVersion: "v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "yezichao-5545b55b75-6ck7b",
			Namespace: "default",
			UID:       "2248ad15-f708-48f9-918e-df91c638cd2d",
			CreationTimestamp: func() metav1.Time {
				ct := time.Unix(**********, 0)
				return metav1.NewTime(ct)
			}(),
		},
		Spec: v1.PodSpec{
			NodeName: "bci-virtual-kubelet",
			Volumes:  []v1.Volume{},
			Containers: []v1.Container{
				{
					Name:    "yezichao",
					Image:   "hub.baidubce.com/cce/nginx:latest",
					Command: []string{},
					Args:    []string{},
					Resources: v1.ResourceRequirements{
						Requests: v1.ResourceList{
							v1.ResourceCPU:    resource.MustParse("1.0"),
							v1.ResourceMemory: resource.MustParse("2.0Gi"),
						},
					},
				},
			},
		},
	}
	type args struct {
		pod *v1.Pod
	}
	tests := []struct {
		name string
		args args
		want map[string]map[string]*TerminationParam
	}{
		// All test cases.
		{
			name: "normal",
			args: args{
				pod: func() *v1.Pod {
					pod := basePod.DeepCopy()
					pod.Spec.InitContainers = []v1.Container{
						{
							Name:                     "init",
							Image:                    "nginx",
							TerminationMessagePath:   "/dev/test-init-path",
							TerminationMessagePolicy: v1.TerminationMessageReadFile,
						},
					}
					pod.Spec.Containers = []v1.Container{
						{
							Name:                     "normal",
							Image:                    "nginx",
							TerminationMessagePath:   "/dev/test-path",
							TerminationMessagePolicy: v1.TerminationMessageFallbackToLogsOnError,
						},
					}
					return pod
				}(),
			},
			want: map[string]map[string]*TerminationParam{
				ContainerTerminationParamAnnotatonKey: {
					"init": {
						TerminationMessagePath:   "/dev/test-init-path",
						TerminationMessagePolicy: v1.TerminationMessageReadFile,
						ContainerType:            bci.ContainerTypeInit,
					},
					"normal": {
						TerminationMessagePath:   "/dev/test-path",
						TerminationMessagePolicy: v1.TerminationMessageFallbackToLogsOnError,
						ContainerType:            bci.ContainerTypeWorkload,
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got := getTerminationParamAnnotations(tt.args.pod)
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_GetResourceFieldRef(t *testing.T) {
	testPod := func() *v1.Pod {
		pod := newTestV1Pod("default", "test", "test-uid", "bci-vk", "", time.Now().Unix(), bci.PodStatusPending)
		pod.Spec.Containers = []v1.Container{
			{
				Name:  "container-0",
				Image: "nginx",
				Resources: v1.ResourceRequirements{
					Requests: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("0.25"),
						v1.ResourceMemory: resource.MustParse("512Mi"),
					},
					Limits: v1.ResourceList{
						v1.ResourceCPU:    resource.MustParse("1"),
						v1.ResourceMemory: resource.MustParse("1Gi"),
					},
				},
			},
		}
		return pod
	}()
	type args struct {
		pod *v1.Pod
		fs  *v1.ResourceFieldSelector
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// All test cases.
		{
			name: "requests.cpu 250m divisor 1m",
			args: args{
				pod: testPod,
				fs: &v1.ResourceFieldSelector{
					ContainerName: "container-0",
					Resource:      "requests.cpu",
					Divisor:       resource.MustParse("1m"),
				},
			},
			want: "250",
		},
		{
			name: "requests.cpu 250m divisor 0",
			args: args{
				pod: testPod,
				fs: &v1.ResourceFieldSelector{
					ContainerName: "container-0",
					Resource:      "requests.cpu",
					Divisor:       resource.MustParse("1"),
				},
			},
			want: "1",
		},
		{
			name: "limits.cpu always equals requests.cpu",
			args: args{
				pod: testPod,
				fs: &v1.ResourceFieldSelector{
					ContainerName: "container-0",
					Resource:      "limits.cpu",
					Divisor:       resource.MustParse("1m"),
				},
			},
			want: "250",
		},
		{
			name: "requests.memory 512Mi divisor 1Mi",
			args: args{
				pod: testPod,
				fs: &v1.ResourceFieldSelector{
					ContainerName: "container-0",
					Resource:      "requests.memory",
					Divisor:       resource.MustParse("1Mi"),
				},
			},
			want: "512",
		},
		{
			name: "requests.memory 512Mi divisor 0",
			args: args{
				pod: testPod,
				fs: &v1.ResourceFieldSelector{
					ContainerName: "container-0",
					Resource:      "requests.memory",
					Divisor:       resource.MustParse("0"),
				},
			},
			want: "536870912",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{}
			got, err := p.GetResourceFieldRef(tt.args.pod, tt.args.fs)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.GetResourceFieldRef() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("BCIProvider.GetResourceFieldRef() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_bciPodStatusToConditions(t *testing.T) {
	testTime, err := time.Parse(time.RFC3339, "2022-12-07T22:00:00Z")
	if err != nil {
		t.Fatal(err)
	}
	testMetaV1Time := metav1.NewTime(testTime)
	type args struct {
		bpod     *bci.DescribePodResponse
		podPhase v1.PodPhase
	}
	tests := []struct {
		name string
		args args
		want []v1.PodCondition
	}{
		// All test cases.
		{
			name: "normal pending pod",
			args: args{
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						Status:      bci.PodStatusPending,
						CreatedTime: testTime,
					},
					Containers: []bci.Container{
						{
							Name:   "container-0",
							Status: &bci.ContainerStatus{},
						},
					},
				},
				podPhase: v1.PodPending,
			},
			want: []v1.PodCondition{
				{
					Type:               v1.PodInitialized,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodReady,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.ContainersReady,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodScheduled,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
			},
		},
		{
			name: "normal running pod",
			args: args{
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						Status:      bci.PodStatusRunning,
						CreatedTime: testTime,
					},
					Containers: []bci.Container{
						{
							Name: "container-0",
							Status: &bci.ContainerStatus{
								CurrentState: &bci.ContainerState{
									State:              bci.ContainerStateStringRunning,
									ContainerStartTime: testTime.Add(30 * time.Second),
								},
							},
						},
					},
				},
				podPhase: v1.PodRunning,
			},
			want: []v1.PodCondition{
				{
					Type:               v1.PodInitialized,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodReady,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.ContainersReady,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodScheduled,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
			},
		},
		{
			name: "normal succeeded pod",
			args: args{
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						Status:      bci.PodStatusSucceeded,
						CreatedTime: testTime,
					},
					Containers: []bci.Container{
						{
							Name: "container-0",
							Status: &bci.ContainerStatus{
								CurrentState: &bci.ContainerState{
									State:               bci.ContainerStateStringSucceeded,
									ContainerStartTime:  testTime.Add(30 * time.Second),
									ContainerFinishTime: testTime.Add(time.Minute),
								},
							},
						},
					},
				},
				podPhase: v1.PodSucceeded,
			},
			want: []v1.PodCondition{
				{
					Type:               v1.PodInitialized,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodReady,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.ContainersReady,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodScheduled,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
			},
		},
		{
			name: "pending pod with init container",
			args: args{
				bpod: &bci.DescribePodResponse{
					Pod: &bci.Pod{
						Status:      bci.PodStatusPending,
						CreatedTime: testTime,
					},
					Containers: []bci.Container{
						{
							Name:          "init-0",
							Status:        &bci.ContainerStatus{},
							ContainerType: bci.ContainerTypeInit,
						},
						{
							Name:   "container-0",
							Status: &bci.ContainerStatus{},
						},
					},
				},
				podPhase: v1.PodPending,
			},
			want: []v1.PodCondition{
				{
					Type:               v1.PodInitialized,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodReady,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.ContainersReady,
					Status:             v1.ConditionFalse,
					LastTransitionTime: testMetaV1Time,
				},
				{
					Type:               v1.PodScheduled,
					Status:             v1.ConditionTrue,
					LastTransitionTime: testMetaV1Time,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := bciPodStatusToConditions(tt.args.bpod, tt.args.podPhase); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("bciPodStatusToConditions() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_translatePortName(t *testing.T) {
	type args struct {
		probe     *v1.Probe
		portNames map[string]int32
	}
	tests := []struct {
		name    string
		args    args
		want    *v1.Probe
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "exec probe",
			args: args{
				probe: &v1.Probe{
					TimeoutSeconds: 3,
					PeriodSeconds:  10,
					Handler: v1.Handler{
						Exec: &v1.ExecAction{
							Command: []string{"/healthcheck.sh"},
						},
					},
				},
			},
			want: &v1.Probe{
				TimeoutSeconds: 3,
				PeriodSeconds:  10,
				Handler: v1.Handler{
					Exec: &v1.ExecAction{
						Command: []string{"/healthcheck.sh"},
					},
				},
			},
		},
		{
			name: "httpGet probe with name port",
			args: args{
				probe: &v1.Probe{
					TimeoutSeconds: 3,
					PeriodSeconds:  10,
					Handler: v1.Handler{
						HTTPGet: &v1.HTTPGetAction{
							Path: "/healthcheck",
							Port: intstr.FromString("http"),
						},
					},
				},
				portNames: map[string]int32{
					"http": 8080,
				},
			},
			want: &v1.Probe{
				TimeoutSeconds: 3,
				PeriodSeconds:  10,
				Handler: v1.Handler{
					HTTPGet: &v1.HTTPGetAction{
						Path: "/healthcheck",
						Port: intstr.FromInt(8080),
					},
				},
			},
		},
		{
			name: "tcp probe with name port",
			args: args{
				probe: &v1.Probe{
					TimeoutSeconds: 3,
					PeriodSeconds:  10,
					Handler: v1.Handler{
						TCPSocket: &v1.TCPSocketAction{
							Port: intstr.FromString("http"),
						},
					},
				},
				portNames: map[string]int32{
					"http": 8080,
				},
			},
			want: &v1.Probe{
				TimeoutSeconds: 3,
				PeriodSeconds:  10,
				Handler: v1.Handler{
					TCPSocket: &v1.TCPSocketAction{
						Port: intstr.FromInt(8080),
					},
				},
			},
		},
		{
			name: "tcp probe with num port",
			args: args{
				probe: &v1.Probe{
					TimeoutSeconds: 3,
					PeriodSeconds:  10,
					Handler: v1.Handler{
						TCPSocket: &v1.TCPSocketAction{
							Port: intstr.FromInt(8080),
						},
					},
				},
			},
			want: &v1.Probe{
				TimeoutSeconds: 3,
				PeriodSeconds:  10,
				Handler: v1.Handler{
					TCPSocket: &v1.TCPSocketAction{
						Port: intstr.FromInt(8080),
					},
				},
			},
		},
		{
			name: "httpGet probe with num port",
			args: args{
				probe: &v1.Probe{
					TimeoutSeconds: 3,
					PeriodSeconds:  10,
					Handler: v1.Handler{
						HTTPGet: &v1.HTTPGetAction{
							Path: "/healthcheck",
							Port: intstr.FromInt(8080),
						},
					},
				},
			},
			want: &v1.Probe{
				TimeoutSeconds: 3,
				PeriodSeconds:  10,
				Handler: v1.Handler{
					HTTPGet: &v1.HTTPGetAction{
						Path: "/healthcheck",
						Port: intstr.FromInt(8080),
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := translatePortName(tt.args.probe, tt.args.portNames)
			if (err != nil) != tt.wantErr {
				t.Errorf("translatePortName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_parsePFSPV(t *testing.T) {
	testConfigEndpoint := "************"
	testSC := "pfs-sc"
	testStaticSC := "pfs-static-sc"
	testStaticDir := "/my/pfs/dir/"
	type fields struct {
		ctrl            *gomock.Controller
		resourceManager manager.ResourceManager
	}
	type args struct {
		ctx context.Context
		pv  *v1.PersistentVolume
	}
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantServer string
		wantPath   string
		wantErr    bool
	}{
		// TODO: Add test cases.
		{
			name: "dynamic pfs pv with latest pfs csi (all info can be found in tool configmap)",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mgr := testutil.FakeResourceManager(func() *v1.ConfigMap {
					cm := testutil.FakeConfigMap("kube-system", "client-conf-tools", map[string]string{
						"tools.sh": fmt.Sprintf(`#!/bin/bash
set -x
curl -o /etc/cluster/client.conf http://%s:8888/files/cluster.conf
[[ $? -gt 0 ]] && exit 1

default_net=$(cat /etc/cluster/client.conf |grep '#net' |awk -F'=' '{print $2}' |sed -e 's/^\\s*//' -e 's/\\s*$//')
default_nic=$(ip route |grep $default_net | awk '{print $3}' |head -n 1)
# if can't find it the exactly matched one
if [ -z \"$default_nic\" ];then
    target_ip=$(cat /etc/cluster/client.conf |grep cluster_addr |awk -F',' '{print $NF}')

    # ******* via *********** dev ens192 src ************* uid 0
    nic=$(ip route get $target_ip |head -n 1 |awk -F'dev' '{print $2}' |awk '{print $1}')
    # if CSI in the cluster, maybe it's just the localhost
    if [ \"$nic\" == \"lo\" ];then
        target_ip=$(cat /etc/cluster/client.conf |grep cluster_addr |awk  '{print $NF}' |awk -F',' '{print $1}')
        nic=$(ip route get $target_ip |head -n 1 |awk -F'dev' '{print $2}' |awk '{print $1}')
    fi

    ping -c 1 $target_ip > /dev/null
    if [ $? -eq 0 ];then
        default_nic=$nic
    fi
fi
`, testConfigEndpoint)})

					if cm.Annotations == nil {
						cm.Annotations = make(map[string]string)
					}
					cm.Annotations["csi-clusterfileplugin/parent-dir"] = "/kubernetes"
					cm.Annotations["csi-clusterfileplugin/config-endpoint"] = fmt.Sprintf("http://%s:8888", testConfigEndpoint)
					cm.Annotations["csi-clusterfileplugin/cluster"] = "/pfs"

					return cm
				}())

				return fields{
					ctrl:            ctrl,
					resourceManager: mgr,
				}
			}(),
			args: args{
				ctx: context.Background(),
				pv: &v1.PersistentVolume{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "PersistentVolume",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e",
						Annotations: map[string]string{
							"pv.kubernetes.io/provisioned-by": "csi-clusterfileplugin",
						},
					},
					Spec: v1.PersistentVolumeSpec{
						Capacity: v1.ResourceList{
							v1.ResourceStorage: resource.MustParse("100Ti"),
						},
						StorageClassName: testSC,
						PersistentVolumeSource: v1.PersistentVolumeSource{
							CSI: &v1.CSIPersistentVolumeSource{
								Driver:       "csi-clusterfileplugin",
								FSType:       "ext4",
								VolumeHandle: "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e",
							},
						},
					},
				},
			},
			wantServer: testConfigEndpoint,
			wantPath:   filepath.Join("/kubernetes/pfs", "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e"),
		},
		{
			name: "dynamic pfs pv with roce pfs",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mgr := testutil.FakeResourceManager(testutil.FakeConfigMap("kube-system", "client-conf-tools", map[string]string{
					"tools.sh": fmt.Sprintf(`#!/bin/bash
set -x
curl -o /etc/cluster/client.conf http://%s:8888/files/cluster.conf
[[ $? -gt 0 ]] && exit 1

default_net=$(cat /etc/cluster/client.conf |grep '#net' |awk -F'=' '{print $2}' |sed -e 's/^\\s*//' -e 's/\\s*$//')
default_nic=$(ip route |grep $default_net | awk '{print $3}' |head -n 1)
# if can't find it the exactly matched one
if [ -z \"$default_nic\" ];then
    target_ip=$(cat /etc/cluster/client.conf |grep cluster_addr |awk -F',' '{print $NF}')

    # ******* via *********** dev ens192 src ************* uid 0
    nic=$(ip route get $target_ip |head -n 1 |awk -F'dev' '{print $2}' |awk '{print $1}')
    # if CSI in the cluster, maybe it's just the localhost
    if [ \"$nic\" == \"lo\" ];then
        target_ip=$(cat /etc/cluster/client.conf |grep cluster_addr |awk  '{print $NF}' |awk -F',' '{print $1}')
        nic=$(ip route get $target_ip |head -n 1 |awk -F'dev' '{print $2}' |awk '{print $1}')
    fi

    ping -c 1 $target_ip > /dev/null
    if [ $? -eq 0 ];then
        default_nic=$nic
    fi
fi
`, testConfigEndpoint)}), &v1.Pod{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "Pod",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name:      "roce-pfs-csi-cce-roce-csi-pfs-plugin-provisioner-5b4d4959bcrv6d",
						Namespace: "kube-system",
						Labels: map[string]string{
							"app":               "cce-roce-csi-pfs-plugin-provisioner",
							"pod-template-hash": "5b4d4959b5",
						},
					},
					Spec: v1.PodSpec{
						InitContainers: []v1.Container{
							{
								Name:  "cluster-conf",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.8.1",
								Command: []string{
									"bash",
									"-c",
									"/etc/cluster/tools.sh",
								},
							},
						},
						Containers: []v1.Container{
							{
								Name:  "csi-provisioner",
								Image: "registry.baidubce.com/cce-plugin-pro/csi-provisioner:v1.6.1",
							},
							{
								Name:  "cluster",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.8.1",
								Env: []v1.EnvVar{
									{
										Name:  "CSI_ENDPOINT",
										Value: "unix:///var/lib/csi/sockets/pluginproxy/csi.sock",
									},
									{
										Name:  "CLUSTER",
										Value: "pfs",
									},
									{
										Name:  "PARENT_DIR",
										Value: "/kubernetes",
									},
								},
							},
						},
					},
				})

				return fields{
					ctrl:            ctrl,
					resourceManager: mgr,
				}
			}(),
			args: args{
				ctx: context.Background(),
				pv: &v1.PersistentVolume{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "PersistentVolume",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e",
						Annotations: map[string]string{
							"pv.kubernetes.io/provisioned-by": "csi-clusterfileplugin",
						},
					},
					Spec: v1.PersistentVolumeSpec{
						Capacity: v1.ResourceList{
							v1.ResourceStorage: resource.MustParse("100Ti"),
						},
						StorageClassName: testSC,
						PersistentVolumeSource: v1.PersistentVolumeSource{
							CSI: &v1.CSIPersistentVolumeSource{
								Driver:       "csi-clusterfileplugin",
								FSType:       "ext4",
								VolumeHandle: "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e",
							},
						},
					},
				},
			},
			wantServer: testConfigEndpoint,
			wantPath:   filepath.Join("/kubernetes/pfs", "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e"),
		},
		{
			name: "static pfs pv with latest pfs",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mgr := testutil.FakeResourceManager(testutil.FakeConfigMap("kube-system", "client-conf-tools", map[string]string{
					"tools.sh": fmt.Sprintf(`#!/bin/bash
set -x
curl -o /etc/cluster/client.conf http://%s:8888/files/cluster.conf
[[ $? -gt 0 ]] && exit 1

default_net=$(cat /etc/cluster/client.conf |grep '#net' |awk -F'=' '{print $2}' |sed -e 's/^\\s*//' -e 's/\\s*$//')
default_nic=$(ip route |grep $default_net | awk '{print $3}' |head -n 1)
# if can't find it the exactly matched one
if [ -z \"$default_nic\" ];then
    target_ip=$(cat /etc/cluster/client.conf |grep cluster_addr |awk -F',' '{print $NF}')

    # ******* via *********** dev ens192 src ************* uid 0
    nic=$(ip route get $target_ip |head -n 1 |awk -F'dev' '{print $2}' |awk '{print $1}')
    # if CSI in the cluster, maybe it's just the localhost
    if [ \"$nic\" == \"lo\" ];then
        target_ip=$(cat /etc/cluster/client.conf |grep cluster_addr |awk  '{print $NF}' |awk -F',' '{print $1}')
        nic=$(ip route get $target_ip |head -n 1 |awk -F'dev' '{print $2}' |awk '{print $1}')
    fi

    ping -c 1 $target_ip > /dev/null
    if [ $? -eq 0 ];then
        default_nic=$nic
    fi
fi
`, testConfigEndpoint)}), &v1.Pod{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "Pod",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name:      "roce-pfs-csi-cce-roce-csi-pfs-plugin-provisioner-5b4d4959bcrv6d",
						Namespace: "kube-system",
						Labels: map[string]string{
							"app":               "cce-roce-csi-pfs-plugin-provisioner",
							"pod-template-hash": "5b4d4959b5",
						},
					},
					Spec: v1.PodSpec{
						InitContainers: []v1.Container{
							{
								Name:  "cluster-conf",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.8.1",
								Command: []string{
									"bash",
									"-c",
									"/etc/cluster/tools.sh",
								},
							},
						},
						Containers: []v1.Container{
							{
								Name:  "csi-provisioner",
								Image: "registry.baidubce.com/cce-plugin-pro/csi-provisioner:v1.6.1",
							},
							{
								Name:  "cluster",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.8.1",
								Env: []v1.EnvVar{
									{
										Name:  "CSI_ENDPOINT",
										Value: "unix:///var/lib/csi/sockets/pluginproxy/csi.sock",
									},
									{
										Name:  "CLUSTER",
										Value: "pfs",
									},
									{
										Name:  "PARENT_DIR",
										Value: "/kubernetes",
									},
								},
							},
						},
					},
				})

				return fields{
					ctrl:            ctrl,
					resourceManager: mgr,
				}
			}(),
			args: args{
				ctx: context.Background(),
				pv: &v1.PersistentVolume{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "PersistentVolume",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "static-pfs-pv",
					},
					Spec: v1.PersistentVolumeSpec{
						StorageClassName: testStaticSC,
						PersistentVolumeSource: v1.PersistentVolumeSource{
							CSI: &v1.CSIPersistentVolumeSource{
								Driver:       "csi-clusterfileplugin",
								VolumeHandle: "data-id",
								VolumeAttributes: map[string]string{
									"path": testStaticDir,
								},
							},
						},
					},
				},
			},
			wantServer: testConfigEndpoint,
			wantPath:   filepath.Join("/kubernetes" + testStaticDir),
		},
		{
			name: "dynamic PV with legacy pfs csi (no tool configmap)",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mgr := testutil.FakeResourceManager(&v1.Pod{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "Pod",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name:      "pfs-csi-cce-csi-pfs-plugin-provisioner-0",
						Namespace: "kube-system",
						Labels: map[string]string{
							"app":               "cce-csi-pfs-plugin-provisioner",
							"pod-template-hash": "5b4d4959b5",
						},
					},
					Spec: v1.PodSpec{
						InitContainers: []v1.Container{
							{
								Name:  "cluster-conf",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.6.3b",
								Command: []string{
									"bash",
									"-c",
									fmt.Sprintf(`set -x

curl -o /etc/cluster/client.conf http://%s:8888/files/cluster.conf
[[ $? -gt 0 ]] && exit 1
cat /etc/cluster/client.conf |grep '#net' |awk -F'=' '{print $2}' |sed -e 's/^\s*//' -e 's/\s*$//' > /etc/cluster/net
echo 'conn_subnet_filter_file=/etc/cluster/net' >> /etc/cluster/client.conf
`, testConfigEndpoint),
								},
							},
						},
						Containers: []v1.Container{
							{
								Name:  "csi-provisioner",
								Image: "registry.baidubce.com/cce-plugin-pro/csi-provisioner:v1.6.1",
							},
							{
								Name:  "cluster",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.6.3b",
								Env: []v1.EnvVar{
									{
										Name:  "CSI_ENDPOINT",
										Value: "unix:///var/lib/csi/sockets/pluginproxy/csi.sock",
									},
									{
										Name:  "CLUSTER",
										Value: "pfs",
									},
									{
										Name:  "PARENT_DIR",
										Value: "/kubernetes",
									},
								},
							},
						},
					},
				})
				return fields{
					ctrl:            ctrl,
					resourceManager: mgr,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pv: &v1.PersistentVolume{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "PersistentVolume",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e",
						Annotations: map[string]string{
							"pv.kubernetes.io/provisioned-by": "csi-clusterfileplugin",
						},
					},
					Spec: v1.PersistentVolumeSpec{
						Capacity: v1.ResourceList{
							v1.ResourceStorage: resource.MustParse("100Ti"),
						},
						StorageClassName: testSC,
						PersistentVolumeSource: v1.PersistentVolumeSource{
							CSI: &v1.CSIPersistentVolumeSource{
								Driver:       "csi-clusterfileplugin",
								FSType:       "ext4",
								VolumeHandle: "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e",
							},
						},
					},
				},
			},
			wantServer: testConfigEndpoint,
			wantPath:   filepath.Join("/kubernetes/pfs", "pvc-40a732c5-5ab6-491f-97fb-0549024aa35e"),
		},
		{
			name: "static PV + legacy pfs csi (no tool configmap)",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				mgr := testutil.FakeResourceManager(&v1.Pod{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "Pod",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name:      "pfs-csi-cce-csi-pfs-plugin-provisioner-0",
						Namespace: "kube-system",
						Labels: map[string]string{
							"app":               "cce-csi-pfs-plugin-provisioner",
							"pod-template-hash": "5b4d4959b5",
						},
					},
					Spec: v1.PodSpec{
						InitContainers: []v1.Container{
							{
								Name:  "cluster-conf",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.6.3b",
								Command: []string{
									"bash",
									"-c",
									fmt.Sprintf(`set -x

curl -o /etc/cluster/client.conf http://%s:8888/files/cluster.conf
[[ $? -gt 0 ]] && exit 1
cat /etc/cluster/client.conf |grep '#net' |awk -F'=' '{print $2}' |sed -e 's/^\s*//' -e 's/\s*$//' > /etc/cluster/net
echo 'conn_subnet_filter_file=/etc/cluster/net' >> /etc/cluster/client.conf
`, testConfigEndpoint),
								},
							},
						},
						Containers: []v1.Container{
							{
								Name:  "csi-provisioner",
								Image: "registry.baidubce.com/cce-plugin-pro/csi-provisioner:v1.6.1",
							},
							{
								Name:  "cluster",
								Image: "registry.baidubce.com/cce-plugin-pro/pfs:v6.6.3b",
								Env: []v1.EnvVar{
									{
										Name:  "CSI_ENDPOINT",
										Value: "unix:///var/lib/csi/sockets/pluginproxy/csi.sock",
									},
									{
										Name:  "CLUSTER",
										Value: "pfs",
									},
									{
										Name:  "PARENT_DIR",
										Value: "/kubernetes",
									},
								},
							},
						},
					},
				})
				return fields{
					ctrl:            ctrl,
					resourceManager: mgr,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pv: &v1.PersistentVolume{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "PersistentVolume",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "static-pfs-pv",
					},
					Spec: v1.PersistentVolumeSpec{
						StorageClassName: testStaticSC,
						PersistentVolumeSource: v1.PersistentVolumeSource{
							CSI: &v1.CSIPersistentVolumeSource{
								Driver:       "csi-clusterfileplugin",
								VolumeHandle: "data-id",
								VolumeAttributes: map[string]string{
									"path": testStaticDir,
								},
							},
						},
					},
				},
			},
			wantServer: testConfigEndpoint,
			wantPath:   filepath.Join("/kubernetes", testStaticDir),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				resourceManager: tt.fields.resourceManager,
			}
			gotServer, gotPath, err := p.parsePFSPV(tt.args.ctx, tt.args.pv)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.parsePFSPV() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if gotServer != tt.wantServer {
				t.Errorf("BCIProvider.parsePFSPV() gotServer = %v, want %v", gotServer, tt.wantServer)
			}
			if gotPath != tt.wantPath {
				t.Errorf("BCIProvider.parsePFSPV() gotPath = %v, want %v", gotPath, tt.wantPath)
			}
		})
	}
}

func Test_getLogCollections(t *testing.T) {
	type args struct {
		c v1.Container
	}
	tests := []struct {
		name    string
		args    args
		want    []*bci.LogCollection
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "legal config case",
			args: args{
				c: v1.Container{
					Name:  "container-0",
					Image: "nginx",
					Env: []v1.EnvVar{
						{
							Name:  "env-0",
							Value: "0",
						},
						{
							Name:  "bls_task_internal_1_name",
							Value: "config-1",
						},
						{
							Name:  "bls_task_internal_1_logStore",
							Value: "logstore-1",
						},
						{
							Name:  "bls_task_internal_1_ttl",
							Value: "4",
						},
						{
							Name:  "bls_task_internal_1_rateLimit",
							Value: "50",
						},
						{
							Name:  "bls_task_internal_1_matchedPattern",
							Value: "^*.log$",
						},
						{
							Name:  "bls_task_stdout_2_name",
							Value: "config-2",
						},
						{
							Name:  "bls_task_stdout_2_srcDir",
							Value: "stdout",
						},
						{
							Name:  "bls_task_internal_1_srcDir",
							Value: "/var/log-1",
						},
						{
							Name:  "bls_task_stdout_2_logStore",
							Value: "logstore-stdout",
						},
						{
							Name:  "bls_task_stdout_2_ttl",
							Value: "5",
						},
						{
							Name:  "bls_task_stdout_2_rateLimit",
							Value: "20",
						},
						{
							Name:  "bls_task_stdout_2_matchedPattern",
							Value: "^.*$",
						},
					},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "bls_task_2",
							MountPath: "/stdout/",
						},
						{
							Name:      "bls_task_1",
							MountPath: "/var/log-1",
						},
					},
				},
			},
			want: []*bci.LogCollection{
				{
					Name:    "config-1",
					SrcType: "internal",
					Index:   1,
					SrcConfig: bci.SrcConfig{
						SrcDir:         "/var/log-1",
						MatchedPattern: "^*.log$",
						TTL:            4,
					},
					DestConfig: bci.DestConfig{
						DestType:  "BLS",
						LogStore:  "logstore-1",
						RateLimit: 50,
					},
				},
				{
					Name:    "config-2",
					SrcType: "stdout",
					Index:   2,
					SrcConfig: bci.SrcConfig{
						SrcDir:         "stdout",
						MatchedPattern: "^.*$",
						TTL:            5,
					},
					DestConfig: bci.DestConfig{
						DestType:  "BLS",
						LogStore:  "logstore-stdout",
						RateLimit: 20,
					},
				},
			},
		},
		{
			name: "missing property key case",
			args: args{
				c: v1.Container{
					Name:  "container-0",
					Image: "nginx",
					Env: []v1.EnvVar{
						{
							Name:  "env-0",
							Value: "0",
						},
						{
							Name:  "bls_task_internal_1_name",
							Value: "config-1",
						},
						{
							Name:  "bls_task_internal_1_logStore",
							Value: "logstore-1",
						},
						{
							Name:  "bls_task_internal_1_ttl",
							Value: "4",
						},
						{
							Name:  "bls_task_internal_1_rateLimit",
							Value: "50",
						},
						{
							Name:  "bls_task_internal_1_matchedPattern",
							Value: "^*.log$",
						},
						{
							Name:  "bls_task_stdout_2_name",
							Value: "config-2",
						},
						{
							Name:  "bls_task_stdout_2_srcDir",
							Value: "stdout",
						},
						{
							Name:  "bls_task_internal_1_srcDir",
							Value: "/var/log-1",
						},
						{
							Name:  "bls_task_stdout_2_logStore",
							Value: "logstore-stdout",
						},
						{
							Name:  "bls_task_stdout_2_ttl",
							Value: "5",
						},
						{
							Name:  "bls_task_stdout_2_rateLimit",
							Value: "20",
						},
					},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "bls_task_2",
							MountPath: "/stdout/",
						},
						{
							Name:      "bls_task_1",
							MountPath: "/var/log-1",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "duplicate index case",
			args: args{
				c: v1.Container{
					Name:  "container-0",
					Image: "nginx",
					Env: []v1.EnvVar{
						{
							Name:  "env-0",
							Value: "0",
						},
						{
							Name:  "bls_task_internal_1_name",
							Value: "config-1",
						},
						{
							Name:  "bls_task_internal_1_logStore",
							Value: "logstore-1",
						},
						{
							Name:  "bls_task_internal_1_ttl",
							Value: "4",
						},
						{
							Name:  "bls_task_internal_1_rateLimit",
							Value: "50",
						},
						{
							Name:  "bls_task_internal_1_matchedPattern",
							Value: "^*.log$",
						},
						{
							Name:  "bls_task_stdout_1_name",
							Value: "config-2",
						},
						{
							Name:  "bls_task_stdout_1_srcDir",
							Value: "stdout",
						},
						{
							Name:  "bls_task_internal_1_srcDir",
							Value: "/var/log-1",
						},
						{
							Name:  "bls_task_stdout_1_logStore",
							Value: "logstore-stdout",
						},
						{
							Name:  "bls_task_stdout_1_ttl",
							Value: "5",
						},
						{
							Name:  "bls_task_stdout_1_rateLimit",
							Value: "20",
						},
						{
							Name:  "bls_task_stdout_1_matchedPattern",
							Value: "^.*$",
						},
					},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "bls_task_2",
							MountPath: "/stdout/",
						},
						{
							Name:      "bls_task_1",
							MountPath: "/var/log-1",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "invalid property value case",
			args: args{
				c: v1.Container{
					Name:  "container-0",
					Image: "nginx",
					Env: []v1.EnvVar{
						{
							Name:  "env-0",
							Value: "0",
						},
						{
							Name:  "bls_task_internal_1_name",
							Value: "config-1",
						},
						{
							Name:  "bls_task_internal_1_logStore",
							Value: "logstore-1",
						},
						{
							Name:  "bls_task_internal_1_ttl",
							Value: "4m",
						},
						{
							Name:  "bls_task_internal_1_rateLimit",
							Value: "50",
						},
						{
							Name:  "bls_task_internal_1_matchedPattern",
							Value: "^*.log$",
						},
						{
							Name:  "bls_task_stdout_2_name",
							Value: "config-2",
						},
						{
							Name:  "bls_task_stdout_2_srcDir",
							Value: "stdout",
						},
						{
							Name:  "bls_task_internal_1_srcDir",
							Value: "/var/log-1",
						},
						{
							Name:  "bls_task_stdout_2_logStore",
							Value: "logstore-stdout",
						},
						{
							Name:  "bls_task_stdout_2_ttl",
							Value: "5",
						},
						{
							Name:  "bls_task_stdout_2_rateLimit",
							Value: "20",
						},
						{
							Name:  "bls_task_stdout_2_matchedPattern",
							Value: "^.*$",
						},
					},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "bls_task_2",
							MountPath: "/stdout/",
						},
						{
							Name:      "bls_task_1",
							MountPath: "/var/log-1",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "illegal property value case",
			args: args{
				c: v1.Container{
					Name:  "container-0",
					Image: "nginx",
					Env: []v1.EnvVar{
						{
							Name:  "env-0",
							Value: "0",
						},
						{
							Name:  "bls_task_internal_1_name",
							Value: "config-1",
						},
						{
							Name:  "bls_task_internal_1_logStore",
							Value: "logstore-1",
						},
						{
							Name:  "bls_task_internal_1_ttl",
							Value: "-1",
						},
						{
							Name:  "bls_task_internal_1_rateLimit",
							Value: "50",
						},
						{
							Name:  "bls_task_internal_1_matchedPattern",
							Value: "^*.log$",
						},
						{
							Name:  "bls_task_stdout_2_name",
							Value: "config-2",
						},
						{
							Name:  "bls_task_stdout_2_srcDir",
							Value: "stdout",
						},
						{
							Name:  "bls_task_internal_1_srcDir",
							Value: "/var/log-1",
						},
						{
							Name:  "bls_task_stdout_2_logStore",
							Value: "logstore-stdout",
						},
						{
							Name:  "bls_task_stdout_2_ttl",
							Value: "5",
						},
						{
							Name:  "bls_task_stdout_2_rateLimit",
							Value: "20",
						},
						{
							Name:  "bls_task_stdout_2_matchedPattern",
							Value: "^.*$",
						},
					},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "bls_task_2",
							MountPath: "/stdout/",
						},
						{
							Name:      "bls_task_1",
							MountPath: "/var/log-1",
						},
					},
				},
			},
			wantErr: true,
		},
		{
			name: "missing property value case",
			args: args{
				c: v1.Container{
					Name:  "container-0",
					Image: "nginx",
					Env: []v1.EnvVar{
						{
							Name:  "env-0",
							Value: "0",
						},
						{
							Name:  "bls_task_internal_1_name",
							Value: "config-1",
						},
						{
							Name:  "bls_task_internal_1_logStore",
							Value: "logstore-1",
						},
						{
							Name:  "bls_task_internal_1_ttl",
							Value: "4",
						},
						{
							Name:  "bls_task_internal_1_rateLimit",
							Value: "50",
						},
						{
							Name:  "bls_task_internal_1_matchedPattern",
							Value: "^*.log$",
						},
						{
							Name:  "bls_task_stdout_2_name",
							Value: "config-2",
						},
						{
							Name:  "bls_task_stdout_2_srcDir",
							Value: "stdout",
						},
						{
							Name:  "bls_task_internal_1_srcDir",
							Value: "",
						},
						{
							Name:  "bls_task_stdout_2_logStore",
							Value: "logstore-stdout",
						},
						{
							Name:  "bls_task_stdout_2_ttl",
							Value: "5",
						},
						{
							Name:  "bls_task_stdout_2_rateLimit",
							Value: "20",
						},
						{
							Name:  "bls_task_stdout_2_matchedPattern",
							Value: "^.*$",
						},
					},
					VolumeMounts: []v1.VolumeMount{
						{
							Name:      "bls_task_2",
							MountPath: "/stdout/",
						},
						{
							Name:      "bls_task_1",
							MountPath: "/var/log-1",
						},
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getLogCollections(tt.args.c)
			if (err != nil) != tt.wantErr {
				t.Errorf("getLogCollections() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			assert.DeepEqual(t, got, tt.want)
		})
	}
}

func TestBCIProvider_setupNetworkProfile(t *testing.T) {
	type fields struct {
		setup    func()
		teardown func()

		assert func(*BCIProvider)
	}
	tests := []struct {
		name    string
		fields  fields
		wantErr bool
	}{
		// All test cases.
		{
			name: "normal case",
			fields: func() fields {
				envs := map[string]string{
					"BCI_SECURITY_GROUP_ID": "g-someid",
					"BCI_SUBNET_ID":         "sbn-1,sbn-2",
					"BCI_LOGICAL_ZONE":      "zoneA,zoneB",
				}
				return fields{
					setup: func() {
						for k, v := range envs {
							if err := os.Setenv(k, v); err != nil {
								t.Fatalf("set env %s to %s: %v", k, v, err)
							}
						}
					},
					teardown: func() {
						for k, _ := range envs {
							if err := os.Unsetenv(k); err != nil {
								t.Fatalf("unset env %s : %v", k, err)
							}
						}
					},
					assert: func(p *BCIProvider) {
						want := &BCIProvider{
							securityGroupID: envs["BCI_SECURITY_GROUP_ID"],
							subnetOptions: map[string]*SubnetOption{
								"sbn-1": {
									Weight:      1,
									LogicalZone: "zoneA",
								},
								"sbn-2": {
									Weight:      1,
									LogicalZone: "",
								},
							},
						}
						assert.DeepEqual(t, p, want, cmpopts.IgnoreUnexported(BCIProvider{}))
					},
				}
			}(),
		},
		{
			name: "with empty zone case",
			fields: func() fields {
				envs := map[string]string{
					"BCI_SECURITY_GROUP_ID": "g-someid",
					"BCI_SUBNET_ID":         "sbn-1,sbn-2",
					"BCI_LOGICAL_ZONE":      "zoneA,",
				}
				return fields{
					setup: func() {
						for k, v := range envs {
							if err := os.Setenv(k, v); err != nil {
								t.Fatalf("set env %s to %s: %v", k, v, err)
							}
						}
					},
					teardown: func() {
						for k, _ := range envs {
							if err := os.Unsetenv(k); err != nil {
								t.Fatalf("unset env %s : %v", k, err)
							}
						}
					},
					assert: func(p *BCIProvider) {
						want := &BCIProvider{
							securityGroupID: envs["BCI_SECURITY_GROUP_ID"],
							subnetOptions: map[string]*SubnetOption{
								"sbn-1": {
									Weight:      1,
									LogicalZone: "zoneA",
								},
								"sbn-2": {
									Weight:      1,
									LogicalZone: "",
								},
							},
						}
						assert.DeepEqual(t, p, want, cmpopts.IgnoreUnexported(BCIProvider{}))
					},
				}
			}(),
		},
	}
	for _, tt := range tests {
		if tt.fields.teardown != nil {
			defer tt.fields.teardown()
		}
		if tt.fields.setup != nil {
			tt.fields.setup()
		}
		t.Run(tt.name, func(t *testing.T) {
			p := &BCIProvider{}
			if err := p.setupNetworkProfile(); (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.setupNetworkProfile() error = %v, wantErr %v", err, tt.wantErr)
			}
			if tt.fields.assert != nil {
				tt.fields.assert(p)
			}
		})
	}
}

func TestBCIProvider_parseBOSPV(t *testing.T) {
	type fields struct {
		region          string
		ctrl            *gomock.Controller
		resourceManager manager.ResourceManager
	}
	type args struct {
		ctx context.Context
		pv  *v1.PersistentVolume
	}
	data := map[string][]byte{}
	data["ak"] = []byte("test-ak")
	data["sk"] = []byte("test-sk")
	secret := &v1.Secret{
		Data: data,
	}
	secret.Name = "csi-bos-secret"
	secret.Namespace = "default"
	tests := []struct {
		name       string
		fields     fields
		args       args
		wantVolume bci.VolumeBOS
		wantErr    bool
	}{
		{
			name: "parse bos pv normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				rm := manager.NewMockResourceManager(ctrl)
				rm.EXPECT().GetSecret(gomock.Any(), gomock.Any()).Return(secret, nil).AnyTimes()
				return fields{
					ctrl:            ctrl,
					resourceManager: rm,
					region:          "bj",
				}
			}(),
			args: args{
				ctx: context.TODO(),
				pv: &v1.PersistentVolume{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "v1",
						Kind:       "PersistentVolume",
					},
					ObjectMeta: metav1.ObjectMeta{
						Name: "csi-bos-pv",
					},
					Spec: v1.PersistentVolumeSpec{
						StorageClassName: "csi-bos-sc",
						PersistentVolumeSource: v1.PersistentVolumeSource{
							CSI: &v1.CSIPersistentVolumeSource{
								Driver:       "csi-bosplugin",
								VolumeHandle: "test-bucket",
								NodePublishSecretRef: &v1.SecretReference{
									Name:      secret.Name,
									Namespace: secret.Namespace,
								},
							},
						},
					},
				},
			},
			wantVolume: bci.VolumeBOS{
				Url:       "bj.bcebos.com",
				Bucket:    "test-bucket",
				AccessKey: "test-ak",
				SecretKey: "test-sk",
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				region:          tt.fields.region,
				resourceManager: tt.fields.resourceManager,
			}
			volume, err := p.parseBOSPV(tt.args.ctx, tt.args.pv)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.parseBOSPV() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if volume != nil && volume.Bucket != tt.wantVolume.Bucket {
				t.Errorf("BCIProvider.parseBOSPV() gotBucket = %v, want %v", volume.Bucket, tt.wantVolume.Bucket)
			}
			if volume != nil && volume.Url != tt.wantVolume.Url {
				t.Errorf("BCIProvider.parseBOSPV() gotUrl = %v, want %v", volume.Url, tt.wantVolume.Url)
			}
			if volume != nil && volume.AccessKey != tt.wantVolume.AccessKey {
				t.Errorf("BCIProvider.parseBOSPV() gotAccesskey = %v, want %v", volume.AccessKey, tt.wantVolume.AccessKey)
			}
			if volume != nil && volume.SecretKey != tt.wantVolume.SecretKey {
				t.Errorf("BCIProvider.parseBOSPV() gotSecretkey = %v, want %v", volume.SecretKey, tt.wantVolume.SecretKey)
			}
		})
	}
}
