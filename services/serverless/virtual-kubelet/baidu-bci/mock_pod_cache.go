// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/services/serverless/virtual-kubelet/baidu-bci (interfaces: PodCache)

// Package baidu_bci is a generated GoMock package.
package baidu_bci

import (
	context "context"
	reflect "reflect"

	gomock "github.com/golang/mock/gomock"
	v1 "k8s.io/api/core/v1"
)

// MockPodCache is a mock of PodCache interface.
type MockPodCache struct {
	ctrl     *gomock.Controller
	recorder *MockPodCacheMockRecorder
}

// MockPodCacheMockRecorder is the mock recorder for MockPodCache.
type MockPodCacheMockRecorder struct {
	mock *MockPodCache
}

// NewMockPodCache creates a new mock instance.
func NewMockPodCache(ctrl *gomock.Controller) *MockPodCache {
	mock := &MockPodCache{ctrl: ctrl}
	mock.recorder = &MockPodCacheMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPodCache) EXPECT() *MockPodCacheMockRecorder {
	return m.recorder
}

// AddSubnetUsage mocks base method.
func (m *MockPodCache) AddSubnetUsage(arg0 context.Context, arg1 string, arg2 BCIResources) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "AddSubnetUsage", arg0, arg1, arg2)
}

// AddSubnetUsage indicates an expected call of AddSubnetUsage.
func (mr *MockPodCacheMockRecorder) AddSubnetUsage(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AddSubnetUsage", reflect.TypeOf((*MockPodCache)(nil).AddSubnetUsage), arg0, arg1, arg2)
}

// GetPod mocks base method.
func (m *MockPodCache) GetPod(arg0 context.Context, arg1, arg2 string) (*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPod", arg0, arg1, arg2)
	ret0, _ := ret[0].(*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPod indicates an expected call of GetPod.
func (mr *MockPodCacheMockRecorder) GetPod(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPod", reflect.TypeOf((*MockPodCache)(nil).GetPod), arg0, arg1, arg2)
}

// GetPodByPodID mocks base method.
func (m *MockPodCache) GetPodByPodID(arg0 context.Context, arg1 string) (*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodByPodID", arg0, arg1)
	ret0, _ := ret[0].(*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodByPodID indicates an expected call of GetPodByPodID.
func (mr *MockPodCacheMockRecorder) GetPodByPodID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodByPodID", reflect.TypeOf((*MockPodCache)(nil).GetPodByPodID), arg0, arg1)
}

// GetPodIDs mocks base method.
func (m *MockPodCache) GetPodIDs(arg0 context.Context) (map[string]*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodIDs", arg0)
	ret0, _ := ret[0].(map[string]*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodIDs indicates an expected call of GetPodIDs.
func (mr *MockPodCacheMockRecorder) GetPodIDs(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodIDs", reflect.TypeOf((*MockPodCache)(nil).GetPodIDs), arg0)
}

// GetPods mocks base method.
func (m *MockPodCache) GetPods(arg0 context.Context) ([]*v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPods", arg0)
	ret0, _ := ret[0].([]*v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPods indicates an expected call of GetPods.
func (mr *MockPodCacheMockRecorder) GetPods(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPods", reflect.TypeOf((*MockPodCache)(nil).GetPods), arg0)
}

// GetSubnetUsage mocks base method.
func (m *MockPodCache) GetSubnetUsage(arg0 context.Context, arg1 string) BCIResources {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSubnetUsage", arg0, arg1)
	ret0, _ := ret[0].(BCIResources)
	return ret0
}

// GetSubnetUsage indicates an expected call of GetSubnetUsage.
func (mr *MockPodCacheMockRecorder) GetSubnetUsage(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSubnetUsage", reflect.TypeOf((*MockPodCache)(nil).GetSubnetUsage), arg0, arg1)
}

// NotifyPodCreating mocks base method.
func (m *MockPodCache) NotifyPodCreating(arg0 context.Context, arg1 string, arg2 *v1.Pod) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NotifyPodCreating", arg0, arg1, arg2)
}

// NotifyPodCreating indicates an expected call of NotifyPodCreating.
func (mr *MockPodCacheMockRecorder) NotifyPodCreating(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyPodCreating", reflect.TypeOf((*MockPodCache)(nil).NotifyPodCreating), arg0, arg1, arg2)
}

// NotifyPods mocks base method.
func (m *MockPodCache) NotifyPods(arg0 context.Context, arg1 func(*v1.Pod)) {
	m.ctrl.T.Helper()
	m.ctrl.Call(m, "NotifyPods", arg0, arg1)
}

// NotifyPods indicates an expected call of NotifyPods.
func (mr *MockPodCacheMockRecorder) NotifyPods(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NotifyPods", reflect.TypeOf((*MockPodCache)(nil).NotifyPods), arg0, arg1)
}

// Start mocks base method.
func (m *MockPodCache) Start(arg0 context.Context) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Start", arg0)
	ret0, _ := ret[0].(error)
	return ret0
}

// Start indicates an expected call of Start.
func (mr *MockPodCacheMockRecorder) Start(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Start", reflect.TypeOf((*MockPodCache)(nil).Start), arg0)
}
