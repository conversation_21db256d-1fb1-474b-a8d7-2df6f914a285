package token

import (
	"math/rand"
	"time"
)

func TokenRefresh(expirationTimestamp time.Time, expirationSecond int64) bool {
	now := time.Now()
	iat := expirationTimestamp.Add(-1 * time.Duration(expirationSecond) * time.Second)

	jitter := time.Duration(rand.Float64()*maxJitter.Seconds()) * time.Second
	if now.After(iat.Add(maxTTL - jitter)) {
		return true
	}

	if now.After(expirationTimestamp.Add(-1*time.Duration(expirationSecond*20/100)*time.Second - jitter)) {
		return true
	}
	return false
}
