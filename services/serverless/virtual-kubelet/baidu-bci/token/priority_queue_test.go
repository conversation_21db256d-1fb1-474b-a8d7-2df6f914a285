package token

import (
	"testing"
)

func TestPriorityQueue(t *testing.T) {
	pq := make(PriorityQueue, 0)
	item1 := &Item{
		Value:    "token1",
		Priority: 1,
		Index:    0,
	}
	item2 := &Item{
		Value:    "token2",
		Priority: 2,
		Index:    1,
	}
	pq.Push(item1)
	pq.Push(item2)
	if pq.Len() != 2 {
		t.Fatalf("pq len is not equal")
	}

	if !pq.Less(0, 1) {
		t.Fatalf("pq priority invalid")
	}

	pq.Swap(0, 1)

	pq.Update(item1, 3)

	itemPop := pq.Pop()
	item := itemPop.(*Item)
	if item.Priority != item1.Priority {
		t.Fatalf("pq pop item not equal")
	}

	pq.Remove(item2)
}

func TestPriorityQueueSet(t *testing.T) {
	pqs := NewPriorityQueueSet()
	pqs.Push("token1", 1)
	pqs.Push("token2", 2)
	if pqs.Len() != 2 {
		t.Fatalf("pqs len is not equal")
	}

	itemPop := pqs.Pop()
	item := itemPop.(*Item)
	if item.Priority != 1 {
		t.Fatalf("pqs priority invalid")
	}

	pqs.Remove("token2")
}
