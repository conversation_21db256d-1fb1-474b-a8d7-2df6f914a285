package token

import (
	"testing"
	"time"
)

func TestTokenRefresh(t *testing.T) {
	type args struct {
		expirationTimestamp time.Time
		expirationSecond    int64
	}

	tests := []struct {
		name string
		args args
		now  time.Time
		want bool
	}{
		{
			name: "token expires in the 20% expiration",
			args: args{
				expirationTimestamp: time.Now().Add(1 * time.Minute),
				expirationSecond:    600,
			},
			now:  time.Now(),
			want: true,
		},
		{
			name: "token expires in the maxTTL expiration",
			args: args{
				expirationTimestamp: time.Now().Add(-10 * time.Minute),
				expirationSecond:    600,
			},
			now:  time.Now(),
			want: true,
		},
		{
			name: "token no need to refresh",
			args: args{
				expirationTimestamp: time.Now().Add(30 * time.Minute),
				expirationSecond:    1800,
			},
			now:  time.Now(),
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.now = time.Now()

			res := TokenRefresh(tt.args.expirationTimestamp, tt.args.expirationSecond)
			if res != tt.want {
				t.Errorf("expect %v, but got %v", tt.want, res)
			}
		})
	}
}
