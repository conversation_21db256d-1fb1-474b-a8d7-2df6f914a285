package token

import (
	"container/heap"
	"sync"
)

// Item 表示队列中的元素
type Item struct {
	Value    interface{} // 实际的数据
	Priority int64       // 优先级
	Index    int         // 元素在堆中的索引
}

// PriorityQueue 是一个优先级队列
type PriorityQueue []*Item

// Len 返回队列的长度
func (pq PriorityQueue) Len() int {
	return len(pq)
}

// Less 定义元素的优先级规则，值越小优先级越高
func (pq PriorityQueue) Less(i, j int) bool {
	return pq[i].Priority < pq[j].Priority
}

// Swap 交换两个元素的位置
func (pq PriorityQueue) Swap(i, j int) {
	pq[i], pq[j] = pq[j], pq[i]
	pq[i].Index = i
	pq[j].Index = j
}

// Push 向队列中添加元素
func (pq *PriorityQueue) Push(x interface{}) {
	n := len(*pq)
	item := x.(*Item)
	item.Index = n
	*pq = append(*pq, item)
}

// Pop 从队列中取出优先级最高的元素
func (pq *PriorityQueue) Pop() interface{} {
	old := *pq
	n := len(old)
	if n == 0 {
		return nil
	}
	item := old[n-1]
	item.Index = -1 // 删除元素
	*pq = old[0 : n-1]
	return item
}

// Update 更新队列中元素的优先级
func (pq *PriorityQueue) Update(item *Item, priority int64) {
	item.Priority = priority
	heap.Fix(pq, item.Index)
}

// Remove 删除队列中的元素
func (pq *PriorityQueue) Remove(item *Item) {
	heap.Remove(pq, item.Index)
}

// PriorityQueueSet 是一个并发安全的优先级去重队列
type PriorityQueueSet struct {
	pq           PriorityQueue         // 优先级队列
	items        map[interface{}]*Item // 去重用的映射，用于快速判断元素是否存在
	sync.RWMutex                       // 互斥锁，用于并发安全操作队列
}

// NewPriorityQueueSet 创建一个新的并发安全的优先级去重队列
func NewPriorityQueueSet() *PriorityQueueSet {
	return &PriorityQueueSet{
		pq:    make(PriorityQueue, 0),
		items: make(map[interface{}]*Item),
	}
}

// Len 返回队列的长度
func (pqs *PriorityQueueSet) Len() int {
	pqs.Lock()
	defer pqs.Unlock()
	return len(pqs.pq)
}

// Push 向队列中添加元素
func (pqs *PriorityQueueSet) Push(value interface{}, priority int64) {
	pqs.Lock()
	defer pqs.Unlock()

	// 先判断元素是否已存在于队列中，如果存在则判断优先级是否改变更新优先级，否则添加新元素
	if item, ok := pqs.items[value]; ok {
		if item.Priority < priority {
			pqs.pq.Update(item, priority)
		}
	} else {
		item := &Item{
			Value:    value,
			Priority: priority,
		}
		heap.Push(&pqs.pq, item)
		pqs.items[value] = item
	}
}

// Pop 从队列中取出优先级最高的元素
func (pqs *PriorityQueueSet) Pop() interface{} {
	pqs.RLock()
	defer pqs.RUnlock()

	item := heap.Pop(&pqs.pq).(*Item)
	delete(pqs.items, item.Value)
	return item
}

// Remove 从队列中删除指定元素
func (pqs *PriorityQueueSet) Remove(value interface{}) {
	pqs.Lock()
	defer pqs.Unlock()

	for _, item := range pqs.pq {
		if isEqual(item.Value, value) {
			pqs.pq.Remove(item)
			delete(pqs.items, item.Value)
			break
		}
	}
}

func isEqual(a, b interface{}) bool {
	switch a := a.(type) {
	case int:
		if b, ok := b.(int); ok {
			return a == b
		}
	case string:
		if b, ok := b.(string); ok {
			return a == b
		}
	}
	return false
}
