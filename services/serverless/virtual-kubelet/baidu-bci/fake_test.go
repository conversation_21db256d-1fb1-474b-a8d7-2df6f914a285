package baidu_bci

import (
	v1 "k8s.io/api/core/v1"
	corev1listers "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
)

// FailLimitedTimesConfigMapLister lead the resource manager based on it to fail limited times on GetConfigMap
// once hit the limit, the counter will be reset and wait for the next limit arrival
type FailLimitedTimesConfigMapLister struct {
	corev1listers.ConfigMapLister
	empty         corev1listers.ConfigMapLister
	getCnt, limit int
}

func (fo *FailLimitedTimesConfigMapLister) ConfigMaps(namespace string) corev1listers.ConfigMapNamespaceLister {
	fo.getCnt++
	if fo.getCnt == fo.limit+1 {
		fo.getCnt = 0
		return fo.ConfigMapLister.ConfigMaps(namespace)
	}
	return fo.empty.ConfigMaps(namespace)
}

func NewFailLimitedTimesConfigMapLister(limit int, configMaps ...*v1.ConfigMap) *FailLimitedTimesConfigMapLister {
	emptyIndexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc})
	empty := corev1listers.NewConfigMapLister(emptyIndexer)
	indexer := cache.NewIndexer(cache.MetaNamespaceKeyFunc, cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc})
	for _, configMap := range configMaps {
		indexer.Add(configMap)
	}
	configMapLister := corev1listers.NewConfigMapLister(indexer)
	if limit < 1 {
		limit = 1
	}
	return &FailLimitedTimesConfigMapLister{
		empty:           empty,
		ConfigMapLister: configMapLister,
		limit:           limit,
	}
}
