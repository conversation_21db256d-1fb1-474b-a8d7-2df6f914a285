package baidu_bci

import (
	"testing"

	"github.com/google/go-cmp/cmp"
)

func TestAppendPodIPEnvName(t *testing.T) {
	type args struct {
		annotations map[string]string
		container   string
		envName     string
	}
	tests := []struct {
		name    string
		args    args
		want    map[string]string
		wantErr bool
	}{
		// All test cases.
		{
			name: "nil annotation case",
			args: args{
				container: "container1",
				envName:   "env1",
			},
			want: map[string]string{
				PodIPAnnotationKey: `{"container1":["env1"]}`,
			},
			wantErr: false,
		},
		{
			name: "empty annotation case",
			args: args{
				annotations: map[string]string{},
				container:   "container1",
				envName:     "env1",
			},
			want: map[string]string{
				PodIPAnnotationKey: `{"container1":["env1"]}`,
			},
			wantErr: false,
		},
		{
			name: "fresh annotation case",
			args: args{
				annotations: map[string]string{
					BCICreateEIPAnnotationKey: "true",
				},
				container: "container1",
				envName:   "env1",
			},
			want: map[string]string{
				BCICreateEIPAnnotationKey: "true",
				PodIPAnnotationKey:        `{"container1":["env1"]}`,
			},
			wantErr: false,
		},
		{
			name: "append new container case",
			args: args{
				annotations: map[string]string{
					PodIPAnnotationKey: `{"container1":["env1"]}`,
				},
				container: "container2",
				envName:   "env2",
			},
			want: map[string]string{
				PodIPAnnotationKey: `{"container1":["env1"],"container2":["env2"]}`,
			},
			wantErr: false,
		},
		{
			name: "append new env case",
			args: args{
				annotations: map[string]string{
					PodIPAnnotationKey: `{"container1":["env1"],"container2":["env2"]}`,
				},
				container: "container1",
				envName:   "env3",
			},
			want: map[string]string{
				PodIPAnnotationKey: `{"container1":["env1","env3"],"container2":["env2"]}`,
			},
			wantErr: false,
		},
		{
			name: "env already exists case",
			args: args{
				annotations: map[string]string{
					PodIPAnnotationKey: `{"container1":["env1"],"container2":["env2"]}`,
				},
				container: "container1",
				envName:   "env1",
			},
			want: map[string]string{
				PodIPAnnotationKey: `{"container1":["env1"],"container2":["env2"]}`,
			},
			wantErr: false,
		},
		{
			name: "malformed annotations exists case",
			args: args{
				annotations: map[string]string{
					PodIPAnnotationKey: `i am annotation`,
				},
				container: "container1",
				envName:   "env3",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := appendPodIPEnvName(tt.args.annotations, tt.args.container, tt.args.envName)
			if (err != nil) != tt.wantErr {
				t.Errorf("appendPodIPEnvName() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !cmp.Equal(got, tt.want) {
				t.Errorf("appendPodIPEnvName() = %v, want %v, diff is %s", got, tt.want, cmp.Diff(got, tt.want))
			}
		})
	}
}
