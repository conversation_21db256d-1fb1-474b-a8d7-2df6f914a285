package baidu_bci

import (
	"context"
	"crypto/sha1"
	"fmt"
	"io"
	"sort"
	"strings"
	"testing"
	"time"

	"code.cloudfoundry.org/clock/fakeclock"
	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"gotest.tools/assert"
	v1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	bciv2 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci/v2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/util"
)

// podIDsMatcher matches string slice with order ignorance.
type podIDsMatcher struct {
	podIDs []string
}

func (m *podIDsMatcher) Matches(x interface{}) bool {
	v, ok := x.([]string)
	if !ok {
		return false
	}
	sort.Strings(v)
	return cmp.Equal(x, m.podIDs)
}

func (m *podIDsMatcher) String() string {
	return "podIDsMatcher"
}

func newPodIDsMatcher(podIDs []string) *podIDsMatcher {
	sort.Strings(podIDs)
	return &podIDsMatcher{
		podIDs: podIDs,
	}
}

// podIDsLenMatcher matches string slice with order ignorance.
type podIDsLenMatcher struct {
	length int
}

func (m *podIDsLenMatcher) Matches(x interface{}) bool {
	v, ok := x.([]string)
	if !ok {
		return false
	}
	return len(v) == m.length
}

func (m *podIDsLenMatcher) String() string {
	return "podIDsLenMatcher"
}

func newPodIDsLenMatcher(length int) *podIDsLenMatcher {
	return &podIDsLenMatcher{
		length: length,
	}
}

func TestBCIProvider_GetCAdvisorMetrics(t *testing.T) {
	podIDs := []string{"p-c9s3qjro", "p-efbi343f"}
	fakeClock := fakeclock.NewFakeClock(time.Unix(**********, 0))
	type fields struct {
		ctrl        *gomock.Controller
		bciV2Client bciv2.Client
		podCache    PodCache
		want        string
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				c := bciv2.NewMockClient(ctrl)
				pc := NewMockPodCache(ctrl)

				pc.EXPECT().GetPodIDs(gomock.Any()).Return(map[string]*v1.Pod{
					podIDs[0]: newTestV1Pod("kube-system", "bci-virtual-kubelet-0", "uid-0", "vk", "cce-xxxx", 0, bci.PodStatusRunning),
					podIDs[1]: newTestV1Pod("kube-system", "coredns", "uid-1", "vk", "cce-xxxx", 0, bci.PodStatusRunning),
				}, nil)

				c.EXPECT().ListPodsMetrics(gomock.Any(), newPodIDsMatcher(podIDs), gomock.Any()).
					Return(&bci.ListPodsMetricsResponse{
						Result: []*bci.PodMetrics{
							{
								PodShortID: podIDs[0],
								Metrics: []*bci.Metric{
									{
										Value:     246.93,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_cpu_usage_seconds_total",
											Image:       "registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",
											Container:   "bci-virtual-kubelet",
											ContainerID: "e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",
										},
									},
									{
										Value:     4.50654208e+08,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",
											Container:   "POD",
											ContainerID: "e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",
										},
									},
									{
										Value:     0.029585937,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_cpu_usage_seconds_total",
											Image:       "registry.baidubce.com/cce-public/pause:3.1",
											Container:   "POD",
											ContainerID: "b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",
										},
									},
									{
										Value:     45056,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "registry.baidubce.com/cce-public/pause:3.1",
											Container:   "POD",
											ContainerID: "b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",
										},
									},
									{
										Value:     0,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_fs_reads_merged_total",
											Image:       "registry.baidubce.com/cce-public/pause:3.1",
											Container:   "POD",
											ContainerID: "b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",
										},
									},
									{
										Value:     296.213309766,
										TimeStamp: 1669729431,
										Meta: bci.MetricMeta{
											Name:        "container_cpu_usage_seconds_total",
											Image:       "",
											Container:   "",
											ContainerID: "",
										},
									},
									{
										Value:     4.39967744e+08,
										TimeStamp: 1669729431,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "",
											Container:   "",
											ContainerID: "",
										},
									},
								},
							},
							{
								PodShortID: podIDs[1],
								Metrics: []*bci.Metric{
									{
										Value:     1.3914112e+07,
										TimeStamp: 1669732625,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "",
											Container:   "",
											ContainerID: "",
										},
									},
									{
										Value:     45056,
										TimeStamp: 1669732640,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "registry.baidubce.com/cce-public/pause:3.1",
											Container:   "POD",
											ContainerID: "dd8ed62a99f02d2aabbf9a4c17d57c230ed90822b8d3b6d6572128627f963b1c",
										},
									},
									{
										Value:     1.3869056e+07,
										TimeStamp: 1669732636,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "registry.baidubce.com/cce-plugin-pro/coredns@sha256:d6f7ed5b44eee2623d58162cef14a3d937cf99d203effb4c1d71edba70313f24",
											Container:   "coredns",
											ContainerID: "1e53f274f4cff42bcebaaa5b84143931de858d7af4a481e3f37345bd05f82744",
										},
									},
								},
							},
						},
					}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: c,
					podCache:    pc,
					want: strings.Join([]string{`# HELP bci_pods_count BCI pods count on this virtual node`,
						`# TYPE bci_pods_count gauge`,
						`bci_pods_count{version="v2"} 2 **********000`,
						`# HELP container_cpu_usage_seconds_total Cumulative cpu time consumed in seconds.`,
						`# TYPE container_cpu_usage_seconds_total counter`,
						`container_cpu_usage_seconds_total{container="bci-virtual-kubelet",id="/kubepods/guaranteed/poduid-0/e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",image="registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",name="e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 246.93 1669729428000`,
						`container_cpu_usage_seconds_total{container="POD",id="/kubepods/guaranteed/poduid-0/b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",image="registry.baidubce.com/cce-public/pause:3.1",name="b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 0.029585937 1669729428000`,
						`container_cpu_usage_seconds_total{container="",id="/kubepods/guaranteed/poduid-0",image="",name="",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 296.213309766 1669729431000`,
						`# HELP container_fs_reads_merged_total Cumulative count of reads merged`,
						`# TYPE container_fs_reads_merged_total counter`,
						`container_fs_reads_merged_total{container="POD",id="/kubepods/guaranteed/poduid-0/b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",image="registry.baidubce.com/cce-public/pause:3.1",name="b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 0 1669729428000`,
						`# HELP container_memory_working_set_bytes Current working set in bytes.`,
						`# TYPE container_memory_working_set_bytes gauge`,
						`container_memory_working_set_bytes{container="POD",id="/kubepods/guaranteed/poduid-0/e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",image="registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",name="e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 4.50654208e+08 1669729428000`,
						`container_memory_working_set_bytes{container="POD",id="/kubepods/guaranteed/poduid-0/b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",image="registry.baidubce.com/cce-public/pause:3.1",name="b47cb37c415f64e1cd906df94074ab8bc86c46153bd03127ee21a466e1c45bff",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 45056 1669729428000`,
						`container_memory_working_set_bytes{container="",id="/kubepods/guaranteed/poduid-0",image="",name="",namespace="kube-system",pod="bci-virtual-kubelet-0",job_pod_name="bci-virtual-kubelet-0",job_pod_namespace="kube-system"} 4.39967744e+08 1669729431000`,
						`container_memory_working_set_bytes{container="",id="/kubepods/guaranteed/poduid-1",image="",name="",namespace="kube-system",pod="coredns",job_pod_name="coredns",job_pod_namespace="kube-system"} 1.3914112e+07 1669732625000`,
						`container_memory_working_set_bytes{container="POD",id="/kubepods/guaranteed/poduid-1/dd8ed62a99f02d2aabbf9a4c17d57c230ed90822b8d3b6d6572128627f963b1c",image="registry.baidubce.com/cce-public/pause:3.1",name="dd8ed62a99f02d2aabbf9a4c17d57c230ed90822b8d3b6d6572128627f963b1c",namespace="kube-system",pod="coredns",job_pod_name="coredns",job_pod_namespace="kube-system"} 45056 1669732640000`,
						`container_memory_working_set_bytes{container="coredns",id="/kubepods/guaranteed/poduid-1/1e53f274f4cff42bcebaaa5b84143931de858d7af4a481e3f37345bd05f82744",image="registry.baidubce.com/cce-plugin-pro/coredns@sha256:d6f7ed5b44eee2623d58162cef14a3d937cf99d203effb4c1d71edba70313f24",name="1e53f274f4cff42bcebaaa5b84143931de858d7af4a481e3f37345bd05f82744",namespace="kube-system",pod="coredns",job_pod_name="coredns",job_pod_namespace="kube-system"} 1.3869056e+07 1669732636000`,
					}, "\n") + "\n",
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
		{
			name: "empty pod case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				c := bciv2.NewMockClient(ctrl)
				pc := NewMockPodCache(ctrl)

				pc.EXPECT().GetPodIDs(gomock.Any()).Return(map[string]*v1.Pod{}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: c,
					podCache:    pc,
					want: strings.Join([]string{`# HELP bci_pods_count BCI pods count on this virtual node`,
						`# TYPE bci_pods_count gauge`,
						`bci_pods_count{version="v2"} 0 **********000`,
					}, "\n") + "\n",
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
		{
			name: "concurrent normal case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				c := bciv2.NewMockClient(ctrl)
				pc := NewMockPodCache(ctrl)

				wantPodIDs := map[string]*v1.Pod{}
				for i := 0; i < 2*bci.MaxPodsPerListPodsMetricsRequest+1; i++ {
					podID := util.GenerateBCEShortID("p")
					wantPodIDs[podID] = newTestV1Pod("default", "pod-"+podID, "uid-"+podID, "vk", "cce-xxxx", 0, bci.PodStatusRunning)
				}
				pc.EXPECT().GetPodIDs(gomock.Any()).Return(wantPodIDs, nil)

				var ids []string
				for podID := range wantPodIDs {
					ids = append(ids, podID)
				}

				wantBCIMetrics := [][]*bci.PodMetrics{{}}
				var index, count int
				for _, podID := range ids {
					count++
					if count > bci.MaxPodsPerListPodsMetricsRequest {
						index++
						count = 1
						wantBCIMetrics = append(wantBCIMetrics, []*bci.PodMetrics{})
					}
					wantBCIMetrics[index] = append(wantBCIMetrics[index], &bci.PodMetrics{
						PodShortID: podID,
						Metrics: []*bci.Metric{
							{
								Value:     246.93,
								TimeStamp: 1669729428,
								Meta: bci.MetricMeta{
									Name:        "container_cpu_usage_seconds_total",
									Image:       "registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",
									Container:   "bci-virtual-kubelet",
									ContainerID: fmt.Sprintf("%x", sha1.Sum([]byte(podID))),
								},
							},
						},
					})
				}

				for i := range wantBCIMetrics {
					length := bci.MaxPodsPerListPodsMetricsRequest
					if i == len(wantBCIMetrics)-1 {
						length = 1
					}
					index := i
					c.EXPECT().ListPodsMetrics(gomock.Any(), newPodIDsLenMatcher(length), gomock.Any()).DoAndReturn(func(context.Context, []string, *bce.SignOption) (*bci.ListPodsMetricsResponse, error) {
						want := wantBCIMetrics[index]
						// later request returns later to keep order for assertion
						<-time.After(time.Duration(index) * 100 * time.Millisecond)
						return &bci.ListPodsMetricsResponse{
							Result: want,
						}, nil
					})
				}

				lines := []string{`# HELP bci_pods_count BCI pods count on this virtual node`,
					`# TYPE bci_pods_count gauge`,
					fmt.Sprintf(`bci_pods_count{version="v2"} %d **********000`, 2*bci.MaxPodsPerListPodsMetricsRequest+1),
					`# HELP container_cpu_usage_seconds_total Cumulative cpu time consumed in seconds.`,
					`# TYPE container_cpu_usage_seconds_total counter`,
				}

				for _, podID := range ids {
					lines = append(lines, fmt.Sprintf(`container_cpu_usage_seconds_total{container="bci-virtual-kubelet",id="/kubepods/guaranteed/poduid-%s/%s",image="registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",name="%s",namespace="default",pod="%s",job_pod_name="%s",job_pod_namespace="default"} 246.93 1669729428000`,
						podID, fmt.Sprintf("%x", sha1.Sum([]byte(podID))), fmt.Sprintf("%x", sha1.Sum([]byte(podID))), "pod-"+podID, "pod-"+podID))
				}

				return fields{
					ctrl:        ctrl,
					bciV2Client: c,
					podCache:    pc,
					want:        strings.Join(lines, "\n") + "\n",
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
		{
			name: "request error case",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				c := bciv2.NewMockClient(ctrl)
				pc := NewMockPodCache(ctrl)

				pc.EXPECT().GetPodIDs(gomock.Any()).Return(map[string]*v1.Pod{
					podIDs[0]: newTestV1Pod("kube-system", "bci-virtual-kubelet-0", "uid-0", "vk", "cce-xxxx", 0, bci.PodStatusRunning),
					podIDs[1]: newTestV1Pod("kube-system", "coredns", "uid-1", "vk", "cce-xxxx", 0, bci.PodStatusRunning),
				}, nil)

				c.EXPECT().ListPodsMetrics(gomock.Any(), newPodIDsMatcher(podIDs), gomock.Any()).
					Return(&bci.ListPodsMetricsResponse{}, fmt.Errorf("some error"))

				return fields{
					ctrl:        ctrl,
					bciV2Client: c,
					podCache:    pc,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciV2Client:    tt.fields.bciV2Client,
				podCache:       tt.fields.podCache,
				enablePodCache: true,
				clock:          fakeClock,
			}
			reader, err := p.GetCAdvisorMetrics(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.GetCAdvisorMetrics() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			buf, err := io.ReadAll(reader)
			if err != nil {
				t.Fatal(err)
			}
			assert.DeepEqual(t, string(buf), tt.fields.want)
		})
	}
}

func TestBCIProvider_GetStatsSummary(t *testing.T) {
	podIDs := []string{"p-c9s3qjro", "p-efbi343f"}
	fakeClock := fakeclock.NewFakeClock(time.Unix(**********, 0))
	type fields struct {
		ctrl        *gomock.Controller
		bciV2Client bciv2.Client
		podCache    PodCache
		want        int
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "normal",
			fields: func() fields {
				ctrl := gomock.NewController(t)
				c := bciv2.NewMockClient(ctrl)
				pc := NewMockPodCache(ctrl)

				pc.EXPECT().GetPodIDs(gomock.Any()).Return(map[string]*v1.Pod{
					podIDs[0]: newTestV1Pod("kube-system", "bci-virtual-kubelet-0", "uid-0", "vk", "cce-xxxx", 0, bci.PodStatusRunning),
					podIDs[1]: newTestV1Pod("kube-system", "coredns", "uid-1", "vk", "cce-xxxx", 0, bci.PodStatusRunning),
				}, nil)

				c.EXPECT().ListPodsSummary(gomock.Any(), newPodIDsMatcher(podIDs), gomock.Any()).
					Return(&bci.ListPodsMetricsResponse{
						Result: []*bci.PodMetrics{
							{
								PodShortID: podIDs[0],
								Metrics: []*bci.Metric{
									{
										Value:     246.93,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_cpu_usage_seconds_total",
											Image:       "registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",
											Container:   "container-0",
											ContainerID: "e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",
										},
									},
									{
										Value:     4.50654208e+08,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",
											Container:   "container-0",
											ContainerID: "e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",
										},
									},
									{
										Value:     4.39967744e+08,
										TimeStamp: 1669729431,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "",
											Container:   "",
											ContainerID: "",
										},
									},
								},
							},
							{
								PodShortID: podIDs[1],
								Metrics: []*bci.Metric{
									{
										Value:     1.3914112e+07,
										TimeStamp: 1669732625,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "",
											Container:   "",
											ContainerID: "",
										},
									},
									{
										Value:     45056,
										TimeStamp: 1669732640,
										Meta: bci.MetricMeta{
											Name:        "container_memory_working_set_bytes",
											Image:       "registry.baidubce.com/cce-public/pause:3.1",
											Container:   "container-0",
											ContainerID: "dd8ed62a99f02d2aabbf9a4c17d57c230ed90822b8d3b6d6572128627f963b1c",
										},
									},
									{
										Value:     246.93,
										TimeStamp: 1669729428,
										Meta: bci.MetricMeta{
											Name:        "container_cpu_usage_seconds_total",
											Image:       "registry.baidubce.com/cce-plugin-dev/bci-virtual-kubelet@sha256:6666ea32953ae27cceefadf658a03d15d91154f4e014a68e93b634aeced78e18",
											Container:   "container-0",
											ContainerID: "e13cbb41da36ae9e87ee8b8a4fc10da510acf3585cc66beb7d02d75c53eb7d75",
										},
									},
								},
							},
						},
					}, nil)

				return fields{
					ctrl:        ctrl,
					bciV2Client: c,
					podCache:    pc,
					want:        2,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.fields.ctrl != nil {
				defer tt.fields.ctrl.Finish()
			}
			p := &BCIProvider{
				bciV2Client:    tt.fields.bciV2Client,
				podCache:       tt.fields.podCache,
				enablePodCache: true,
				clock:          fakeClock,
			}
			reader, err := p.GetStatsSummary(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("BCIProvider.GetCAdvisorMetrics() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if err != nil {
				return
			}
			assert.Equal(t, len(reader.Pods), tt.fields.want)
		})
	}
}
