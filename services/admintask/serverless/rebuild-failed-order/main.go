package main

import (
	"bytes"
	"context"
	"flag"
	"fmt"
	"log"
	"os/exec"
	"strings"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccegateway"
)

var (
	cceID     = flag.String("cceId", "", "cce cluster id")
	namespace = flag.String("ns", "", "pod namespace")
	podName   = flag.String("name", "", "pod name")
	action    = flag.String("action", "find", "find/replay, find order or replay the order, default find")
)

func init() {
	flag.Parse()
}

func main() {
	ctx := context.TODO()

	if *cceID == "" || *podName == "" || *namespace == "" {
		log.Fatal("namespace, name and cceId must be set")
	}

	if err := replayBCIRequest(ctx, *cceID, *namespace, *podName); err != nil {
		log.Fatal(err)
	}
}

func runBashCmd(ctx context.Context, cmdline string, timeout time.Duration) (stdout, stderr string, err error) {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	stdoutBuf, stderrBuf := new(bytes.Buffer), new(bytes.Buffer)

	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", cmdline)
	cmd.Stdout, cmd.Stderr = stdoutBuf, stderrBuf

	// log.Printf("start to run cmd: %s", cmdline)

	if err := cmd.Start(); err != nil {
		return "", "", fmt.Errorf("start cmd err: %w, cmd: %s", err, cmd)
	}

	err = cmd.Wait()

	stdout, stderr = stdoutBuf.String(), stderrBuf.String()
	if err != nil {
		return "", "", fmt.Errorf("run cmd err: %v, stderr: %s, cmd: %s", err, stderr, cmdline)
	}
	// log.Printf("stdout: %s, err: %v", stdout, err)
	return
}

func replayBCIRequest(ctx context.Context, cceID, namespace, name string) error {
	stdout, _, err := runBashCmd(ctx, fmt.Sprintf("kubectl --request-timeout=10s -n bci get pod | grep %s | awk '{print $1}'", cceID), 15*time.Second)
	if err != nil {
		return fmt.Errorf("run meta kubectl err: %v", err)
	}

	masterPodsLines := strings.Split(stdout, "\n")

	var masterPods []string

	for _, line := range masterPodsLines {
		masterPod := strings.TrimSpace(line)
		if len(line) == 0 {
			continue
		}
		masterPods = append(masterPods, masterPod)
	}

	if len(masterPods) == 0 {
		return fmt.Errorf("cannot find master pods for cluster %s", cceID)
	}

	stdout, _, err = runBashCmd(ctx, fmt.Sprintf("kubectl --request-timeout=10s -n bci exec %s -c debug -- kubectl get pod -n %s %s -o yaml | grep order-id | awk '{print $2}'", masterPods[0], namespace, name), 15*time.Second)
	if err != nil {
		return fmt.Errorf("run kubectl err: %v", err)
	}

	orderID := strings.TrimSpace(stdout)
	if len(orderID) != 32 {
		return fmt.Errorf("cannot find legal order id for pod %s/%s in cluster %s: %s", namespace, name, cceID, orderID)
	}

	log.Printf("orderID: %s", orderID)
	if *action == "find" {
		return nil
	}

	var sendReqLine string
	// iterate all master pods to find create request
	for _, masterPod := range masterPods {
		stdout, _, err = runBashCmd(ctx, fmt.Sprintf(`kubectl --request-timeout=30s -n bci exec %s -c virtual-kubelet -- /find-request-by-order-id.sh %s`, masterPod, orderID), 60*time.Second)
		if err != nil {
			log.Printf("fail to find create requests based on orderID %s in %s: %v",
				orderID, masterPod, err)
			continue
		}
		sendReqLine = strings.TrimSpace(stdout)
		if sendReqLine == "" {
			log.Printf("no match create requests based on orderID %s in %s is found",
				orderID, masterPod)
			continue
		}

		log.Printf("create requests based on orderID %s in %s is found:\n%s\n", orderID, masterPod, sendReqLine)
		break
	}

	index := strings.Index(sendReqLine, "{")
	if index < 0 {
		return fmt.Errorf("cannot find bci request body in log: %s", sendReqLine)
	}

	reqBody := sendReqLine[index:]

	index = strings.Index(sendReqLine, "Cce-Remote-Host:[")
	endpointPart := sendReqLine[index+len("Cce-Remote-Host:["):]
	index = strings.Index(endpointPart, "]")
	endpoint := endpointPart[:index]

	regionPart := endpoint[4:] // bci.
	index = strings.Index(regionPart, ".")
	region := regionPart[:index]

	log.Printf("replayBCIRequest: region is %s, endpoint is %s", region, endpoint)

	cmdline := fmt.Sprintf("kubectl --request-timeout=10s -n bci exec %s -c debug -- kubectl --request-timeout=10s get secret -n kube-system cce-plugin-token -o jsonpath='{.data.token}' | base64 -d", masterPods[0])
	stdout, _, err = runBashCmd(ctx, cmdline, 15*time.Second)
	if err != nil {
		return fmt.Errorf("get cce-gateway token err: %v", err)
	}

	cceToken := strings.TrimSpace(stdout)
	helper := ccegateway.NewHelper(region, cceID)

	cfg := &bce.Config{
		Endpoint: endpoint,
		Protocol: "http",
		Checksum: true,
		Timeout:  30 * time.Second,
	}

	cfg.ProxyHost, cfg.ProxyPort = helper.GetHostAndPort()

	c := bci.NewClient(bci.NewConfig(cfg))
	c.SetDebug(true)

	signOpt := helper.NewSignOption(ctx, cceToken)
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	req, err := bce.NewRequest(
		"POST", c.GetURL("api/logical/bci/v1/pod/create", params), bytes.NewBuffer([]byte(reqBody)))
	if err != nil {
		return fmt.Errorf("new request err: %w", err)
	}
	log.Printf("start sending replay request")
	_, err = c.SendRequest(context.TODO(), req, signOpt)
	if err != nil {
		return fmt.Errorf("send request err: %w", err)
	}

	return nil
}
