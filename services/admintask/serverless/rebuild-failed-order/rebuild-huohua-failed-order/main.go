package main

import (
	"bytes"
	"context"
	"flag"
	"fmt"
	"io/ioutil"
	"log"
	"os"
	"os/exec"
	"strings"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccegateway"
)

var (
	namespace = flag.String("env", "", "env, online/qa")
	podName   = flag.String("name", "", "pod name")
	action    = flag.String("action", "find", "find/replay, find orderId or replay the order, default find")

	cceIds = map[string]string{
		"online": "cce-9w7i6dux",
		"qa":     "cce-r9p3qcr5",
	}

	/*
		---
		apiVersion: v1
		kind: ServiceAccount
		metadata:
		  name: yezichao-huohua-admin
		  namespace: default
		---
		apiVersion: rbac.authorization.k8s.io/v1
		kind: ClusterRoleBinding
		metadata:
		  name: yezichao-huohua-admin
		roleRef:
		  apiGroup: rbac.authorization.k8s.io
		  kind: ClusterRole
		  name: cluster-admin
		subjects:
		- kind: ServiceAccount
		  name: yezichao-huohua-admin
		  namespace: default
	*/

	metaKubeconfig = `apiVersion: v1
clusters:
- cluster:
    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVRDhXTUdRb0phc2ZUeUxuUFMrQ0FGcnZiWFZrd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdIaGNOTVRreE1qTXdNRFkwTkRBd1doY05NalF4TWpJNE1EWTBOREF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ05WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFNbm1majJGWGdhbTR1NXQKYUw2aktXR0ZYeW93RGpPbUFWTERtc3daV3I0WUlwSjgzR2I5Wk1aaEJnNlRhQTBKMjV6SDR1VHh2eU1tU0NFTQpaUDNIRVc2N2NvSUxSY0ZLa1hIZ3dUL1FVclNNUkF4cGhSb3F0U25rTE5VcjNXOGduUnhudGVhVHd1Mlk3WC9UCnRMeitNMWNHNkQrS2xYT0RSOThIUzh1RW05SmJCY0NhZ2RJeHNSZHJqN2cwQVFFekxBV3c4eU9lWHN3L0Y3TnEKQm1qWnJTSG80aHFzZUhWd0tMSms2QnJidGg1UzRPNC9yVmV3Ykd2cHJEV25LYVFwK3JacDhRbjUxVEpaS205ZgpsNm9pZG1Pd2xUT2J2dUlmclRKYm1iQXFCbFpGQ0dZcjhGRjRTbUk3anRzY05ha1VnZjU2WFI3UHdtVnIwb3dMCi8wRUo0TmtDQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHcKSFFZRFZSME9CQllFRkxNYkhPelFvamlKWTZBanp4R0YzUCtLN0dEWU1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQgpBUUJMUGpTQmFqd01aQ1B2RnFySXI5RHJLVlg5NkRFU1lsN21KOWNTVzJiemY3c0VweWNqb2hxUkJKYXRRVGJiCkI5dGl2Tk5BZFdpc1NGeVJ1RkY0cHJQMWZFUmFOUjhESUhwdncwYURXL3RlaWMrYVJiemhUNW1tYTNuUGxDc2wKMytvSG5TbE5Va05kbm5ybWFYWmZBVWF6MitCNlhTdHBFZk5STVZQWGs5ekR4UU5vVFcxRWxUUmdJK01OZXlxdApuSXJuZXVuakJNTXRVejA2YzREMVpPcmdBekxnQ3p1MzIydVdxbFB6ZWxFbElPUll0UFBqR0dLOWVIbmpRVmRjCjNvQXM1NzY2NHF0K0c0dk9BcGZJODBpNk1UWjNSSnZsL0V0Rk8veXJGY3ZsR1lOUUNLcHVJaE04U1FveGIrNHUKemYwM2ZZbFFnQ2J5WFVNMmJSTU1SME9wCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K
    server: https://**************:6443
  name: kubernetes
contexts:
- context:
    cluster: kubernetes
    user: yezichao-huohua-admin
  name: yezichao-huohua-admin@kubernetes
current-context: yezichao-huohua-admin@kubernetes
kind: Config
preferences: {}
users:
- name: yezichao-huohua-admin
  user:
    token:`

	metaToken = `**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`

	kubectl = "kubectl"
)

func main() {
	ctx := context.TODO()
	flag.Parse()

	if *podName == "" || *namespace == "" || cceIds[*namespace] == "" {
		log.Fatal("namespace, name and cceId must be set")
	}

	configFile, err := ioutil.TempFile("", "config")
	if err != nil {
		log.Fatal(err)
	}
	filename := configFile.Name()
	defer func() {
		if err := os.Remove(filename); err != nil {
			log.Printf("fail to remove kubeconfig template %s: %v", filename, err)
		}
	}()

	if _, err := configFile.Write([]byte(metaKubeconfig)); err != nil {
		log.Panicf("fail to write to kubeconfig template %s: %v", filename, err)
	}
	configFile.Close()
	kubectl = fmt.Sprintf("kubectl --kubeconfig=%s --token=%s", filename, metaToken)

	if err := replayBCIRequest(ctx, cceIds[*namespace], *namespace, *podName); err != nil {
		log.Fatal(err)
	}
}

func runBashCmd(ctx context.Context, cmdline string, timeout time.Duration) (stdout, stderr string, err error) {
	ctx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	stdoutBuf, stderrBuf := new(bytes.Buffer), new(bytes.Buffer)

	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", cmdline)
	cmd.Stdout, cmd.Stderr = stdoutBuf, stderrBuf

	// log.Printf("start to run cmd: %s", cmdline)

	if err := cmd.Start(); err != nil {
		return "", "", fmt.Errorf("start cmd err: %w, cmd: %s", err, cmd)
	}

	err = cmd.Wait()

	stdout, stderr = stdoutBuf.String(), stderrBuf.String()
	if err != nil {
		return "", "", fmt.Errorf("run cmd err: %v, stderr: %s, cmd: %s", err, stderr, cmdline)
	}
	// log.Printf("stdout: %s, err: %v", stdout, err)
	return
}

func replayBCIRequest(ctx context.Context, cceID, namespace, name string) error {
	stdout, _, err := runBashCmd(ctx, fmt.Sprintf("%s --request-timeout=10s -n bci get pod | grep %s | awk '{print $1}'", kubectl, cceID), 15*time.Second)
	if err != nil {
		return fmt.Errorf("run meta kubectl err: %v", err)
	}

	masterPodsLines := strings.Split(stdout, "\n")

	var masterPods []string

	for _, line := range masterPodsLines {
		masterPod := strings.TrimSpace(line)
		if len(line) == 0 {
			continue
		}
		masterPods = append(masterPods, masterPod)
	}

	if len(masterPods) == 0 {
		return fmt.Errorf("cannot find master pods for cluster %s", cceID)
	}

	stdout, _, err = runBashCmd(ctx, fmt.Sprintf("%s --request-timeout=10s -n bci exec %s -c debug -- kubectl get pod -n %s %s -o yaml | grep order-id | awk '{print $2}'", kubectl, masterPods[0], namespace, name), 15*time.Second)
	if err != nil {
		return fmt.Errorf("run kubectl err: %v", err)
	}

	orderID := strings.TrimSpace(stdout)
	if len(orderID) != 32 {
		return fmt.Errorf("cannot find legal order id for pod %s/%s in cluster %s: %s", namespace, name, cceID, orderID)
	}

	log.Printf("orderID: %s", orderID)
	if *action == "find" {
		return nil
	}

	var sendReqLine string
	// iterate all master pods to find create request
	for _, masterPod := range masterPods {
		stdout, _, err = runBashCmd(ctx, fmt.Sprintf(`%s --request-timeout=30s -n bci exec %s -c virtual-kubelet -- /find-request-by-order-id.sh %s`, kubectl, masterPod, orderID), 60*time.Second)
		if err != nil {
			log.Printf("fail to find create requests based on orderID %s in %s: %v",
				orderID, masterPod, err)
			continue
		}
		sendReqLine = strings.TrimSpace(stdout)
		if sendReqLine == "" {
			log.Printf("no match create requests based on orderID %s in %s is found",
				orderID, masterPod)
			continue
		}

		log.Printf("create requests based on orderID %s in %s is found:\n%s\n", orderID, masterPod, sendReqLine)
		break
	}

	index := strings.Index(sendReqLine, "{")
	if index < 0 {
		return fmt.Errorf("cannot find bci request body in log: %s", sendReqLine)
	}

	reqBody := sendReqLine[index:]

	index = strings.Index(sendReqLine, "Cce-Remote-Host:[")
	endpointPart := sendReqLine[index+len("Cce-Remote-Host:["):]
	index = strings.Index(endpointPart, "]")
	endpoint := endpointPart[:index]

	regionPart := endpoint[4:] // bci.
	index = strings.Index(regionPart, ".")
	region := regionPart[:index]

	log.Printf("replayBCIRequest: region is %s, endpoint is %s", region, endpoint)

	cmdline := fmt.Sprintf("%s --request-timeout=10s -n bci exec %s -c debug -- kubectl --request-timeout=10s get secret -n kube-system cce-plugin-token -o jsonpath='{.data.token}' | base64 -d", kubectl, masterPods[0])
	stdout, _, err = runBashCmd(ctx, cmdline, 15*time.Second)
	if err != nil {
		return fmt.Errorf("get cce-gateway token err: %v", err)
	}

	cceToken := strings.TrimSpace(stdout)
	helper := ccegateway.NewHelper(region, cceID)

	cfg := &bce.Config{
		Endpoint: endpoint,
		Protocol: "http",
		Checksum: true,
		Timeout:  30 * time.Second,
	}

	cfg.ProxyHost, cfg.ProxyPort = helper.GetHostAndPort()

	c := bci.NewClient(bci.NewConfig(cfg))
	c.SetDebug(true)

	signOpt := helper.NewSignOption(ctx, cceToken)
	params := map[string]string{
		"clientToken": c.GenerateClientToken(),
	}
	req, err := bce.NewRequest(
		"POST", c.GetURL("api/logical/bci/v1/pod/create", params), bytes.NewBuffer([]byte(reqBody)))
	if err != nil {
		return fmt.Errorf("new request err: %w", err)
	}
	log.Printf("start sending replay request")
	_, err = c.SendRequest(context.TODO(), req, signOpt)
	if err != nil {
		return fmt.Errorf("send request err: %w", err)
	}

	return nil
}
