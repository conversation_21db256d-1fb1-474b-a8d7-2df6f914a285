package main

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"strings"
)

var enableHiRobot bool

const maxBodyLength = 2000

func init() {
	if v := os.Getenv("ENABLE_HI_ROBOT"); v == "true" {
		enableHiRobot = true
	}
}

func SendWithAtAllOnce(ctx context.Context, bots []*HiRobot, groups []int64, body *HiBody) error {
	var errStr string
	for _, bot := range bots {
		if err := bot.SendWithAtAllOnce(ctx, groups, body); err != nil {
			logger.Errorf(ctx, err.Error())
			errStr += err.<PERSON>rror() + " ; "
		}
	}
	if errStr != "" {
		return fmt.Errorf(errStr)
	}
	return nil
}

func Send(ctx context.Context, bots []*HiRobot, groups []int64, body *HiBody) error {
	var errStr string
	for _, bot := range bots {
		if err := bot.Send(ctx, groups, body); err != nil {
			logger.Errorf(ctx, err.<PERSON>rror())
			errStr += err.Error() + " ; "
		}
	}
	if errStr != "" {
		return fmt.Errorf(errStr)
	}
	return nil
}

type HiRobot struct {
	URL      string
	Groups   []int64
	HasAtAll bool
}

type Response struct {
	// {"errcode":0,"errmsg":"ok","data":{"fail":{}}}
	ErrCode int                    `json:"errcode"`
	ErrMsg  string                 `json:"errmsg"`
	Data    map[string]interface{} `json:"data"`
}

func NewHiRobot(url string, groups ...int64) *HiRobot {
	return &HiRobot{
		URL:    url,
		Groups: groups,
	}
}

// SendWithAtAllOnce sends msg with only one atAll at the beginning to avoid too many at.
// No more atAll will be sent after the first atAll during the lifecycle of this robot.
func (hb *HiRobot) SendWithAtAllOnce(ctx context.Context, groups []int64, body *HiBody) error {
	if groups == nil {
		groups = hb.Groups
	}
	if !hb.HasAtAll {
		hb.Send(ctx, groups, NewATBody(nil, true))
		hb.HasAtAll = true
	}
	return hb.Send(ctx, groups, body)
}

func (hb *HiRobot) Send(ctx context.Context, groups []int64, body *HiBody) error {
	if !enableHiRobot {
		return nil
	}

	if body == nil {
		return fmt.Errorf("body cannot be nil")
	}

	if groups == nil {
		groups = hb.Groups
	}

	var bodies []*HiBody

	switch body.Type {
	case HiBodyTypeMD, HiBodyTypeTEXT:
		length := len(body.Content)
		times := (length - 1) / maxBodyLength
		for i := 0; i <= times; i++ {
			end := i*maxBodyLength + maxBodyLength
			if end > length {
				end = length
			}
			bodies = append(bodies, &HiBody{
				Type:    body.Type,
				Content: body.Content[maxBodyLength*i : end],
			})

		}
	default:
		bodies = []*HiBody{body}
	}

	// there are many length limitations in one request, split them in different requests
	var errs []string
	for _, each := range bodies {
		rr := &RobotRequest{
			&HiMessage{
				Bodies: []*HiBody{each},
			},
		}
		if len(groups) != 0 {
			rr.Message.Header.Toid = groups
		}

		msgBytes, err := json.Marshal(rr)
		if err != nil {
			return err
		}

		req, err := http.NewRequest("POST", hb.URL, bytes.NewBuffer(msgBytes))
		if err != nil {
			return err
		}
		req.Header.Set("Content-Type", "application/json")
		req.WithContext(ctx)

		logger.Infof(ctx, "sending msg to %v: %s", groups, string(msgBytes))

		resp, err := http.DefaultClient.Do(req)
		if err != nil {
			return err
		}

		if resp == nil {
			logger.Errorf(ctx, "resp is nil")
			errs = append(errs, "resp is nil")
			continue
		}
		defer resp.Body.Close()

		respBody, err := ioutil.ReadAll(resp.Body)
		if err != nil {
			return err
		}
		logger.Infof(ctx, "hi robot resp: status: %d, body: %v", resp.StatusCode, string(respBody))

		if resp.StatusCode >= 400 {
			errs = append(errs, fmt.Sprintf("status: %d, body: %v", resp.StatusCode, string(respBody)))
		}
	}
	if len(errs) > 0 {
		return fmt.Errorf(strings.Join(errs, " ;; "))
	}

	return nil
}

type RobotRequest struct {
	Message *HiMessage `json:"message"`
}

type HiMessage struct {
	Header struct {
		Toid []int64 `json:"toid,omitempty"`
	} `json:"header,omitempty"`

	Bodies []*HiBody `json:"body"`
}

type HiBodyType string

const (
	HiBodyTypeAT    HiBodyType = "AT"
	HiBodyTypeLINK  HiBodyType = "LINK"
	HiBodyTypeTEXT  HiBodyType = "TEXT"
	HiBodyTypeMD    HiBodyType = "MD"
	HiBodyTypeIMAGE HiBodyType = "IMAGE"
)

type HiBody struct {
	Type      HiBodyType `json:"type,omitempty"`
	Content   string     `json:"content,omitempty"`
	Href      string     `json:"href,omitempty"`
	AtAll     bool       `json:"atall,omitempty"`
	AtUserIDs []string   `json:"atuserids,omitempty"`
}

func NewMDBody(content string) *HiBody {
	return &HiBody{
		Type:    HiBodyTypeMD,
		Content: content,
	}
}

func NewATBody(users []string, atAll bool) *HiBody {
	return &HiBody{
		Type:      HiBodyTypeAT,
		AtUserIDs: users,
		AtAll:     atAll,
	}
}

func NewTextBody(content string) *HiBody {
	return &HiBody{
		Type:    HiBodyTypeTEXT,
		Content: content,
	}
}

func NewLinkBody(href string) *HiBody {
	return &HiBody{
		Type: HiBodyTypeLINK,
		Href: href,
	}
}
