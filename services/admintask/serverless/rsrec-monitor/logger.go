package main

import (
	"context"
	"log"
	"os"

	ccelogger "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

var (
	logFile = "monitor.log"
	logger  Logger
)

type Logger struct {
	*log.Logger
}

func (l Logger) Infof(ctx context.Context, format string, args ...interface{}) {
	l.Printf("[INFO] "+format, args...)
}

func (l Logger) Warnf(ctx context.Context, format string, args ...interface{}) {
	l.Printf("[WARN] "+format, args...)
}

func (l Logger) Errorf(ctx context.Context, format string, args ...interface{}) {
	l.Printf("[ERROR] "+format, args...)
}

func init() {
	if v := os.Getenv("LOG_FILE"); v != "" {
		logFile = v
	}

	f, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		log.Fatal(err)
	}
	logger.Logger = log.New(f, "", log.Ldate|log.Ltime|log.Lshortfile)

	ccelogger.SetLogger(logger)
}
