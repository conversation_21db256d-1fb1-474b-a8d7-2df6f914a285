package main

import (
	"bytes"
	"context"
	"flag"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"time"

	"sigs.k8s.io/yaml"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bci"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/ccegateway"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

var (
	creatingTimeout    = 4 * time.Minute
	maxCreatingTimeout = 60 * time.Minute

	Melchior  = NewHiRobot("http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=d50cac44a07cd097fcabc5bf5ffdc64de")
	Heimdallr = NewHiRobot("http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dd4df6fd090cd5ca18634b881dfea74a1", int64(4406701))
	HuohuaBot = NewHiRobot("http://apiin.im.baidu.com/api/msg/groupmsgsend?access_token=dfb5a9e6e5e976868441e5df547872741", int64(4476526))

	configFile = flag.String("c", "config.yaml", "config file path")
)

func main() {
	ctx := context.TODO()
	var err error
	defer func() {
		handleError(ctx, err)
		logger.Printf("\n---\n")
	}()

	if configFile == nil {
		logger.Errorf(ctx, "config is nil")
		err = fmt.Errorf("config is nil")
		return
	}

	clusters, err := LoadClustersFromFile(*configFile)
	if err != nil {
		logger.Errorf(ctx, err.Error())
		return
	}
	if err = utils.Valid(clusters); err != nil {
		logger.Errorf(ctx, err.Error())
		return
	}

	for i, cluster := range clusters {
		logger.Infof(ctx, "cluster[%d]: %+v", i, cluster)
	}

	alertCPU := 1000
	alertMemory := 2000

	if v := os.Getenv("ALERT_CPU"); v != "" {
		if cpu, err := strconv.Atoi(v); err == nil {
			alertCPU = cpu
		}
	}
	if v := os.Getenv("ALERT_MEMORY"); v != "" {
		if mem, err := strconv.Atoi(v); err == nil {
			alertMemory = mem
		}
	}

	allRes := make(map[*Cluster]*Resource, len(clusters))
	for _, cluster := range clusters {
		// Failure in one cluster should not affect the others.
		func() {
			var err error
			var res *Resource
			defer func() {
				ctx := context.WithValue(ctx, "env", cluster.Env)
				handleError(ctx, err)
			}()
			logger.Infof(ctx, "start to check pods for cluster %s", cluster.Env)
			if err = GetPods(ctx, cluster); err != nil {
				logger.Errorf(ctx, err.Error())
				return
			}

			logger.Infof(ctx, "start to check resource for cluster %s", cluster.Env)
			res, err = GetResource(ctx, cluster)
			if err != nil {
				logger.Errorf(ctx, err.Error())
				return
			}
			allRes[cluster] = res
		}()
	}

	// calculate total resource usage
	var totalCPU, totalMemory float64
	var totalBCI int64
	for cluster, res := range allRes {
		logger.Infof(ctx, "%s cluster res: %+v", cluster.Env, res)
		totalCPU += res.TotalCPU
		totalMemory += res.TotalMemory
		totalBCI += res.RunningBCI
	}

	accountUsage := fmt.Sprintf(`
account_TotalCPU: %.2f
account_TotalMemory: %.2f
account_RunningBCI: %d
`, totalCPU, totalMemory, totalBCI)

	fmt.Printf(accountUsage)
	logger.Infof(ctx, "account total usage: %s", accountUsage)

	// check if a hi message should be sent to alert resource usage
	msg := []string{fmt.Sprintf("#### 火花BCI资源用量预警")}
	if totalCPU > float64(alertCPU) {
		msg = append(msg, fmt.Sprintf(`<font color="red">CPU: %.2f 核</font>`, totalCPU))
		for cluster, res := range allRes {
			msg = append(msg, fmt.Sprintf("> %s 集群(%s): %.2f 核", cluster.Env, cluster.CCEID, res.TotalCPU))
		}
	}
	if totalMemory > float64(alertMemory) {
		msg = append(msg, fmt.Sprintf(`<font color="red">Memory: %.2f GiB</font>`, totalMemory))
		for cluster, res := range allRes {
			msg = append(msg, fmt.Sprintf("> %s 集群(%s): %.2f GiB", cluster.Env, cluster.CCEID, res.TotalMemory))
		}
	}

	if len(msg) > 1 {
		if err = SendWithAtAllOnce(ctx, []*HiRobot{Melchior, Heimdallr, HuohuaBot},
			nil, NewMDBody(strings.Join(msg, "\n"))); err != nil {
			err = fmt.Errorf("send resource usage alert message err: %w", err)
			return
		}
	} else {
		// regular resource monitor
		if isRegularNotifyTime(time.Now()) {
			msg := []string{fmt.Sprintf("#### 火花BCI资源用量"),
				fmt.Sprintf("> Total: %.2f 核, %.2f GiB, %d Pods", totalCPU, totalMemory, totalBCI)}
			for cluster, res := range allRes {
				msg = append(msg, fmt.Sprintf("> %s集群(%s): %.2f 核, %.2f GiB, %d Pods",
					cluster.Env, cluster.CCEID, res.TotalCPU, res.TotalMemory, res.RunningBCI))
			}
			if err = Send(ctx, []*HiRobot{HuohuaBot}, nil, NewMDBody(strings.Join(msg, "\n"))); err != nil {
				err = fmt.Errorf("send regular resource usage message err: %w", err)
				return
			}
		}
	}
}

func handleError(ctx context.Context, err error) {
	ctx, cancel := context.WithTimeout(ctx, time.Second*30)
	defer cancel()

	var prefix string
	if v := ctx.Value("env"); v != nil {
		if env, ok := v.(string); ok {
			prefix = fmt.Sprintf("env %s: ", env)
		}
	}

	if r := recover(); r != nil {
		callers := getCallers(r)
		if _, ok := r.(string); ok {
			logger.Errorf(ctx, prefix+"Observed a panic: %s\n%v", r, callers)
			SendWithAtAllOnce(ctx, []*HiRobot{Melchior, Heimdallr}, nil, NewMDBody("```\n"+
				fmt.Sprintf(prefix+"Observed a panic: %s\n%v", r, callers)+
				"\n```"))
		} else {
			logger.Errorf(ctx, prefix+"Observed a panic: %#v (%v)\n%v", r, r, callers)
			SendWithAtAllOnce(ctx, []*HiRobot{Melchior, Heimdallr}, nil, NewMDBody("```\n"+
				fmt.Sprintf(prefix+"Observed a panic: %#v (%v)\n%v", r, r, callers)+
				"\n```"))
		}
	}

	if err != nil {
		SendWithAtAllOnce(ctx, []*HiRobot{Melchior, Heimdallr}, nil, NewMDBody(prefix+"Error occurs:\n> "+err.Error()))
	}
}

func isRegularNotifyTime(now time.Time) bool {
	hour := now.Hour()
	minute := now.Minute()
	return hour <= 23 && hour >= 10 && minute == 0
}

type Cluster struct {
	Env        string   `json:"env" env:"ENV" valid:"Required"`
	Kubeconfig string   `json:"kubeconfig" env:"KUBECONFIG" valid:"Required"`
	Region     string   `json:"region" env:"REGION" valid:"Required"`
	CCEID      string   `json:"cce_id" env:"CCE_ID" valid:"Required"`
	Ignores    []string `json:"ignores" env:"IGNORES"`
}

func LoadClustersFromFile(filename string) ([]*Cluster, error) {
	if filename == "" {
		return nil, fmt.Errorf("config filename is empty")
	}
	content, err := ioutil.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("read config file=%s error: %w", filename, err)
	}
	res := make([]*Cluster, 0)
	err = yaml.Unmarshal(content, &res)
	if err != nil {
		return nil, fmt.Errorf("parse config file=%s error: %w", filename, err)
	}

	return res, nil
}

func getCallers(r interface{}) string {
	callers := ""
	for i := 0; true; i++ {
		_, file, line, ok := runtime.Caller(i)
		if !ok {
			break
		}
		callers = callers + fmt.Sprintf("%v:%v\n", file, line)
	}

	return callers
}

func shouldIgnore(ctx context.Context, line string, ignores []string) bool {
	for _, ignore := range ignores {
		if strings.Contains(line, ignore) {
			logger.Infof(ctx, "ignore=%s is found, ignore line %s", ignore, line)
			return true
		}
	}
	return false
}

func GetPods(ctx context.Context, cluster *Cluster) error {
	stdoutBuf, stderrBuf := new(bytes.Buffer), new(bytes.Buffer)

	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", "kubectl --kubeconfig "+cluster.Kubeconfig+" --request-timeout=15s get pod --all-namespaces")
	cmd.Stdout, cmd.Stderr = stdoutBuf, stderrBuf

	if err := cmd.Start(); err != nil {
		return fmt.Errorf("start kubectl err: %w", err)
	}

	err := cmd.Wait()

	stdout, stderr := stdoutBuf.String(), stderrBuf.String()
	if err != nil {
		return fmt.Errorf("run kubectl err: %v, stderr: %s", err, stderr)
	}

	logger.Infof(ctx, "all pods:\n%s\n", stdout)

	var total, running, creating, failed, succeeded, creatingTooLong, providerFailed, terminating, unknown int
	var creatingTooLongLines, providerFailedLines, failedLines []string

	lines := strings.Split(stdout, "\n")
	for i, line := range lines {
		if i == 0 {
			// skip title line
			continue
		}
		if len(line) == 0 {
			// skip empty line
			continue
		}

		parts := strings.Fields(line)
		if len(parts) != 6 {
			logger.Warnf(ctx, "invalid line %d: %s", i, line)
			continue
		}

		status := parts[3]
		age := parts[5]

		switch status {
		case "Creating", "Pending":
			creating++

			// creating too long
			if shouldIgnore(ctx, line, cluster.Ignores) {
				continue
			}
			d, err := time.ParseDuration(age)
			if err != nil {
				logger.Errorf(ctx, "invalid age of creating: %s, err: %v", age, err)
				continue
			}
			if d > creatingTimeout {
				logger.Warnf(ctx, "creating pod takes too long: %s", line)
				if d <= maxCreatingTimeout {
					creatingTooLong++
					creatingTooLongLines = append(creatingTooLongLines, line)
				}
			}
		case "Running":
			running++
		case "Succeeded", "Completed":
			succeeded++
		case "Failed", "Error", "CrashLoopBackOff", "ImagePullBackOff", "ErrImagePull":
			if shouldIgnore(ctx, line, cluster.Ignores) {
				continue
			}
			failed++
			failedLines = append(failedLines, line)
		case "ProviderFailed":
			if shouldIgnore(ctx, line, cluster.Ignores) {
				continue
			}
			d, err := time.ParseDuration(age)
			if err != nil {
				logger.Errorf(ctx, "invalid age of ProviderFailed: %s, err: %v", age, err)
				continue
			}
			if d > maxCreatingTimeout {
				logger.Warnf(ctx, "ProviderFailed pod stays too long, ignore: %s", line)
				continue
			}
			providerFailed++
			providerFailedLines = append(providerFailedLines, line)
		case "Terminating":
			terminating++

		default:
			logger.Infof(ctx, "unknown status: %s", line)
			unknown++
		}
		total++
	}

	output := fmt.Sprintf(`
env_Total: %d
env_Creating: %d
env_CreatingTooLong: %d
env_Running: %d
env_Succeeded: %d
env_Failed: %d
env_ProviderFailed: %d
env_Terminating: %d
env_Unknown: %d
`, total, creating, creatingTooLong, running, succeeded, failed, providerFailed, terminating, unknown)

	output = strings.ReplaceAll(output, "env_", cluster.Env+"_")

	logger.Infof(ctx, "%s\n", output)
	fmt.Print(output)

	// check if an alert hi message should be sent
	msg := []string{fmt.Sprintf("#### 火花 %s 集群Pod异常 (%s)", cluster.Env, cluster.CCEID)}
	if creatingTooLong > 0 {
		msg = append(msg, fmt.Sprintf(`<font color="red">CreatingTooLong: %d</font>`, creatingTooLong))
		msg = append(msg, creatingTooLongLines...)
	}
	if providerFailed > 0 {
		msg = append(msg, fmt.Sprintf(`<font color="red">ProviderFailed: %d</font>`, providerFailed))
		msg = append(msg, providerFailedLines...)
	}
	if failed > 0 {
		msg = append(msg, fmt.Sprintf(`<font color="red">Failed: %d</font>`, failed))
		msg = append(msg, failedLines...)
	}

	if len(msg) > 1 {
		var errs []string
		if err := SendWithAtAllOnce(ctx, []*HiRobot{Melchior, Heimdallr},
			nil, NewMDBody(strings.Join(msg, "\n"))); err != nil {
			errs = append(errs, err.Error())
		}
		if err := Send(ctx, []*HiRobot{HuohuaBot}, nil, NewMDBody((strings.Join(msg, "\n")))); err != nil {
			errs = append(errs, err.Error())
		}
		if len(errs) > 0 {
			return fmt.Errorf("send abnormal pod message err: %v", errs)
		}
	}

	return nil
}

func GetResource(ctx context.Context, cluster *Cluster) (*Resource, error) {
	stdoutBuf, stderrBuf := new(bytes.Buffer), new(bytes.Buffer)

	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	cmd := exec.CommandContext(ctx, "/bin/bash", "-c", "kubectl --kubeconfig "+cluster.Kubeconfig+" --request-timeout=15s get secret -n kube-system cce-plugin-token -o jsonpath='{.data.token}' | base64 -d")
	cmd.Stdout, cmd.Stderr = stdoutBuf, stderrBuf

	if err := cmd.Start(); err != nil {
		return nil, fmt.Errorf("start kubectl err: %v", err)
	}

	err := cmd.Wait()

	stdout, stderr := stdoutBuf.String(), stderrBuf.String()
	if err != nil {
		return nil, fmt.Errorf("run kubectl err: %v, stderr: %s", err, stderr)
	}

	cceToken := strings.TrimSpace(stdout)
	res, err := GetClusterBCIResource(ctx, cluster.Region, cluster.CCEID, cceToken)
	if err != nil {
		return nil, fmt.Errorf("get bci pods in cluster %s/%s err: %w", cluster.Region, cluster.CCEID, err)
	}

	logger.Infof(ctx, "resource in %s/%s: %+v", cluster.Region, cluster.CCEID, res)

	output := fmt.Sprintf(`
env_RunningBCI: %d
env_TotalCPU: %.2f
env_TotalMemory: %.2f
`, res.RunningBCI, res.TotalCPU, res.TotalMemory)

	output = strings.ReplaceAll(output, "env_", cluster.Env+"_")
	fmt.Print(output)

	return res, nil
}

type Resource struct {
	RunningBCI  int64
	TotalCPU    float64
	TotalMemory float64
}

func GetClusterBCIResource(ctx context.Context, region, cceID, cceToken string) (*Resource, error) {
	helper := ccegateway.NewHelper(region, cceID)

	cfg := &bce.Config{
		Region:   region,
		Protocol: "http",
		Checksum: true,
		Timeout:  10 * time.Second,
	}
	cfg.ProxyHost, cfg.ProxyPort = helper.GetHostAndPort()

	bciClient := bci.NewClient(bci.NewConfig(cfg))
	bciClient.SetDebug(true)

	signOpt := helper.NewSignOption(ctx, cceToken)

	listOpt := bci.NewListOption(cceID, 1, bci.MaxListPageSize, "", "", nil, bci.NewListKeyword(bci.KeywordTypeCCEID, cceID))

	httpCtx := context.TODO()
	resp, err := bciClient.ListPods(httpCtx, listOpt, signOpt)
	if err != nil {
		return nil, err
	}
	result := resp.Result
	if resp.TotalCount > bci.MaxListPageSize {
		totalPage := resp.TotalCount/bci.MaxListPageSize + 1
		var i int64
		for i = 2; i <= totalPage; i++ {
			listOpt.PageNo = i
			resp, err = bciClient.ListPods(httpCtx, listOpt, signOpt)
			if err != nil {
				break
			}
			result = append(result, resp.Result...)
		}
	}
	if err != nil {
		return nil, err
	}

	resource := &Resource{}

	for _, bpod := range result {
		if bpod.Status == bci.PodStatusPending {
			continue
		}

		logger.Infof(ctx, "adding resource of pod %s name=%s", bpod.PodID, bpod.Name)
		resource.TotalCPU += bpod.VCPU
		resource.TotalMemory += bpod.MemoryInGB
		resource.RunningBCI += 1
	}

	return resource, nil
}
