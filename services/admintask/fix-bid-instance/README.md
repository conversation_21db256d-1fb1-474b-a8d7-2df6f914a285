# Fix BID Instance

## 清理 SOP

1. 获取泄漏 BID Instance:

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: fix-bid-instance
spec:
  containers:
    - image: registry.baidubce.com/cce-plugin-dev/fix-bid-instance:chenhuan
      imagePullPolicy: Always
      name: fix
  restartPolicy: Never
  schedulerName: default-scheduler
  securityContext: {}
  serviceAccount: default
```

```bash
$kubectl logs  fix-bid-instance | grep FixInstance | awk '{print $3}'
$kubectl logs  fix-bid-instance | grep BCCNotExist | awk '{print $3}'
```

2. 将泄漏 Instance 进行打标:

```bash
for id in $(cat fuck); do     kubectl label instance $id kubernetes.io/cce.instance.instance-has-been-drained="true"; done
```

3. 等待 GC Controller 回收竞价实例

```bash
watch "kubectl get instance -l kubernetes.io/cce.instance.instance-has-been-drained="true" |wc -l"
```

## EKS 的集群

0c0b3c9dbb6e41308d3bfd587d908922

```bash
# 北京
cce-520adkqr
cce-mfhy9jot
cce-qxpixa2x
cce-v8rhd9sv
cce-xqy98niu

# 苏州
cce-sh9oae1t

# 保定
cce-podm22lu
cce-y73djxeq

# 广州
cce-kjk12mij
```

## 相关处理命令
