package main

import (
	"context"
	"fmt"
	"os"
	"time"

	bcerrror "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/error"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/iam"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/logicbcc"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/sts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
)

func main() {
	ctx := context.TODO()

	if err := fix(ctx); err != nil {
		logger.Errorf(ctx, "fix failed: %s", err)
		os.Exit(1)
	}
}

func fix(ctx context.Context) error {
	// 苏州
	region := "su"
	clusterID := "cce-sh9oae1t"
	accountID := "0c0b3c9dbb6e41308d3bfd587d908922"

	model, err := models.NewClient(ctx, "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true")
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	// 北京
	// region := "bj"
	// clusterID := "cce-520adkqr"
	// clusterID := "cce-mfhy9jot"
	// clusterID := "cce-qxpixa2x"
	// clusterID := "cce-v8rhd9sv"
	// clusterID := "cce-xqy98niu"
	// accountID := "0c0b3c9dbb6e41308d3bfd587d908922"

	// model, err := models.NewClient(ctx, "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true")
	// if err != nil {
	// 	logger.Errorf(ctx, "NewClient failed: %s", err)
	// 	return err
	// }

	// 保定
	// region := "bd"
	// clusterID := "cce-y73djxeq"
	// accountID := "0c0b3c9dbb6e41308d3bfd587d908922"

	// model, err := models.NewClient(ctx, "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true")
	// if err != nil {
	// 	logger.Errorf(ctx, "NewClient failed: %s", err)
	// 	return err
	// }

	// 广州
	//region := "gz"
	//clusterID := "cce-kjk12mij"
	//accountID := "0c0b3c9dbb6e41308d3bfd587d908922"
	//
	//model, err := models.NewClient(ctx, "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true")
	//if err != nil {
	//	logger.Errorf(ctx, "NewClient failed: %s", err)
	//	return err
	//}

	serviceRoleName := "BceServiceRole_SERVICE_CCE"
	serviceName := "cce"
	servicePassword := "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"

	bccclient := logicbcc.NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Endpoint: logicbcc.Endpoints[region],
	})

	// 初始化 sts client
	stsclient := sts.NewClient(context.TODO(), &bce.Config{
		Endpoint: sts.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iam.Endpoints[region],
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, serviceRoleName, serviceName, servicePassword)

	// 获取 CCE Instances
	instances, err := model.GetInstances(ctx, accountID, clusterID)
	if err != nil {
		logger.Errorf(ctx, "GetInstancesByBCCIDs failed: %s", err)
		return err
	}

	logger.Infof(ctx, "Total instance count: %d", len(instances))

	// 构建 Map
	ipInstanceMap := map[string][]*models.Instance{}
	for _, instance := range instances {
		vpcIP := instance.Status.Machine.VPCIP

		if _, ok := ipInstanceMap[vpcIP]; !ok {
			ipInstanceMap[vpcIP] = []*models.Instance{
				instance,
			}
		} else {
			ipInstanceMap[vpcIP] = append(ipInstanceMap[vpcIP], instance)
		}
	}

	// 获取时间较早的 Instance
	result := map[string]*models.Instance{}
	for vpcIP, pair := range ipInstanceMap {
		if len(pair) == 1 {
			continue
		}

		if len(pair) == 2 {
			instance0 := pair[0]
			instance1 := pair[1]

			if instance0.CreatedAt.Before(instance1.CreatedAt) {
				result[vpcIP] = instance0
			} else {
				result[vpcIP] = instance1
			}
		}
	}

	// 打印结果
	for vpcIP, instance := range result {
		instanceID := instance.Status.Machine.InstanceID

		_, err := bccclient.GetServerByID(ctx, instanceID, stsclient.NewSignOption(ctx, accountID))
		if err == nil {
			logger.Infof(ctx, "Instance %s still exists, waiting for delete", instanceID)
			continue
		}

		bceErr, ok := err.(*bcerrror.Error)
		if ok && bceErr.StatusCode == 404 && bceErr.Code == "NoSuchObject" {
			fmt.Printf("FixInstance: %s %s\n", vpcIP, instance.Spec.CCEInstanceID)
		}
	}

	// 获取所有虚机已删除， instanceCRD 仍存在的情况
	for _, instance := range instances {
		instanceID := instance.Status.Machine.InstanceID
		_, err := bccclient.GetServerByID(ctx, instanceID, stsclient.NewSignOption(ctx, accountID))
		if err == nil {
			logger.Infof(ctx, "Instance %s still exists, waiting for delete", instanceID)
			continue
		}

		bceErr, ok := err.(*bcerrror.Error)
		if ok && bceErr.StatusCode == 404 && bceErr.Code == "NoSuchObject" {
			fmt.Printf("BCCNotExist: %s %s %s\n", instance.Status.Machine.VPCIP, instance.Spec.CCEInstanceID, instanceID)
		}

		time.Sleep(time.Millisecond * 300)
	}

	return nil
}
