package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path"
	"strings"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
)

// 用于检查和输出用户 lb controller 和 cloud-node-controller 版本 辅助升级集群

type checkLBServiceConfig struct {
	Region    string
	AccountID string
	ClusterID string
}

var (
	config = checkLBServiceConfig{}

	databaseEndpoint = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.11.48.247:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(10.70.16.33:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}
)

func filterCluster(model *models.Client, clusterIDs []string) []string {
	result := make([]string, 0)
	if config.ClusterID != "" {
		result = append(result, config.ClusterID)
		return result
	}

	for _, clusterID := range clusterIDs {
		if strings.HasPrefix(clusterID, "cce-") {
			result = append(result, clusterID)
		}
	}

	return result
}

func parseFlags() {
	flag.StringVar(&config.Region, "region", "", "[Required]Region of the clusters to be checked.")
	flag.StringVar(&config.AccountID, "accountid", "", "Account ID")
	flag.StringVar(&config.ClusterID, "clusterid", "", "Cluster ID")

	flag.Parse()
}

func main() {
	ctx := context.TODO()

	// 解析参数
	parseFlags()

	if _, ok := databaseEndpoint[config.Region]; !ok {
		fmt.Printf("Region %v is not valid\n", config.Region)
		os.Exit(1)
	}

	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint[config.Region])
	if err != nil {
		fmt.Printf("NewClient failed: %s\n", err)
		os.Exit(1)
	}

	// 获取地域所有集群, 包含 V1 和 V2
	// 获取所有待定集群
	clusterIDs := make([]string, 0)
	if config.AccountID != "" {
		// 根据 account id 获取地域所有集群
		clusters, err := model.GetClusterList(ctx, config.AccountID)
		if err != nil {
			fmt.Printf("GetClusterList failed: %s\n", err)
			os.Exit(1)
		}
		for _, c := range clusters {
			clusterIDs = append(clusterIDs, c.Spec.ClusterID)
		}
	} else {
		// 获取地域所有集群, 包含 V1 和 V2
		clusterIDs, err = model.GetAllRunningClusterIDsCompatibility(ctx)
		if err != nil {
			fmt.Printf("GetAllRunningClusterIDsCompatibility failed: %s\n", err)
			os.Exit(1)
		}
	}

	// 条件过滤集群
	targetClusters := filterCluster(model, clusterIDs)

	// 检查所有集群
	err = os.MkdirAll("kubeconfigs", 0755)
	if err!= nil {
		fmt.Printf("Create kubeconfigs dir failed: %s\n", err)
		os.Exit(1)
	}
	for _, clusterID := range targetClusters {
		clusterInfo, err := checkOneCluster(model, clusterID)
		if err != nil {
			fmt.Printf("checkOneCluster failed: %s\n", err)
			continue
		}
		dstFile,err := os.Create(path.Join("kubeconfigs", clusterID))
		if err!=nil{
			fmt.Printf("create kubeconfig for [%s] failed: %s\n", clusterID,err)
			continue
		} 
		defer dstFile.Close()
		_, err = dstFile.WriteString(clusterInfo.kubeconfig)
		if err != nil {
			continue
		}

		fmt.Printf("%s %s %s\n", clusterInfo.clusterID, clusterInfo.k8sVersion, clusterInfo.name)
	}
}

type clusterInfo struct {
	name              string
	accountID         string
	clusterID         string
	k8sVersion        string
	kubeconfig        string
}

func checkOneCluster(model *models.Client, clusterID string) (*clusterInfo, error) {
	ctx := context.TODO()
	info := &clusterInfo{
		clusterID: clusterID,
	}

	// 获取集群的 AccountID
	cluster, err := model.GetClusterByClusterID(context.TODO(), clusterID)
	if err != nil {
		return nil, err
	}
	info.name = cluster.Spec.ClusterName
	info.accountID = cluster.Spec.AccountID
	info.k8sVersion = string(cluster.Spec.K8SVersion)

	// 构建 User Cluster K8S Client
	kubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		fmt.Printf("GetAdminKubeConfigCompatibility failed: %s\n", err)
		return nil, err
	}
	info.kubeconfig = kubeconfig.KubeConfigFile
	return info, nil
}
