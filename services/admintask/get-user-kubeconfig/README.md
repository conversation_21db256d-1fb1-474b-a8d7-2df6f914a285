# 获取用户集群的kubeconfig （仅限CCE维护人员内部使用）

工具会自动获取目标的集群的kubeconfig，并将kubeconfig存储到当前工作路径下。

```
$: ./get-user-kubeconfig -region gztest -clusterid cce-0af4lvvx
cce-0af4lvvx 1.22.5 test-bos
```

## 参数列表：
* -accountid string
        Account ID
*  -clusterid string
        Cluster ID
*  -region string
        [Required]Region of the clusters to be checked.

> 可选region地域列表：
* sz 苏州  
* bj 北京
* hkg 香港
* wh 武汉
* sz 苏州
* gz 广州
* bd 保定
* gztest  广州测试

## 程序输出结果
```
$: ls
get-user-kubeconfig  kubeconfigs

$: ls kubeconfigs
cce-0af4lvvx  cce-42r3ijq8  cce-88bu6xjt  cce-bl458ehz  cce-d4v6amrn  cce-icqdbobb  cce-jlgj82hs     cce-mudz3hqm  cce-ptpenwpk  cce-tiafquo3  cce-uhbtr9m2  cce-yz9wvmhk
cce-1gat4vlj  cce-52gund8u  cce-906c2gcm  cce-bub9a05v  cce-ddgyfb9x  cce-iuzeegii  cce-jrtogwqq     cce-nvdip5xu  cce-q8a9t94l  cce-tyce1fdj  cce-vme8bjdy  cce-zlaitbls
cce-1z9u45rm  cce-56vfri22  cce-anqh3ql3  cce-c5easmhc  cce-geepmwy2  cce-iz8anceb  cce-jy445of4     cce-o4qy98e2  cce-qg0bo37g  cce-u8mpopyi  cce-xcr7x4gh
cce-3gj16fwy  cce-6dc5uqjl  cce-aymzw521  cce-czorchmf  cce-i2l93s86  cce-jbqf6lyz  cce-metacluster  cce-pptpb9x3  cce-r1rylpjw  cce-udnb857b  cce-xumxjfqw
```
其中kubeconfigs下每个文件是以集群ID命名的kuebconfig，其中A区apiserver的IP地址是BLB的floatingIP。

## 注意
### 风险
1. 请勿并发调用get-user-kubeconfig工具，可能会对CCE数据库造成压力
2. 请勿泄露本程序，仅限CCE维护人员内部使用