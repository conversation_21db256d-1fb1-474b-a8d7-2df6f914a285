apiVersion: batch/v1
kind: Job
metadata:
  name: cce-freeze-v1-c-qdwtoRax
  namespace: cce-trans
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    metadata:
      name: cce-trans
    spec:
      hostNetwork: true
      serviceAccount: cce-trans-serviceaccount
      imagePullSecrets:	
      - name: ccr-registry-secret
      containers:
      - name: cce-trans
        image: registry.baidubce.com/cce-service-dev/cce-freeze-cluster:chenhuan
        imagePullPolicy: Always
        args: 
        - -region=gztest
        - -cluster-id=c-qdwtoRax
        - -account-id=eca97e148cb74e9683d7b7240829d1ff
      restartPolicy: Never
