// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/09 19:14:00, by <EMAIL>, create
*/
/*
DESCRIPTION
将 V1 Cluster/Instance 状态设置为 DELETED
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	beegologger "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger/beego"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans"
)

func main() {
	var (
		logFile   string
		region    string
		accountID string
		clusterID string
	)

	flag.StringVar(&logFile, "log-file", "", "日志路径, 不设置则打印到 STDOUT.")

	flag.StringVar(&region, "region", "", "sandbox, gztest, gz, su, bj, bd, hkg, fwh")
	flag.StringVar(&accountID, "account-id", "", "迁移账户 ID")
	flag.StringVar(&clusterID, "cluster-id", "", "迁移集群 ID")

	flag.Parse()

	logger.SetLogger(beegologger.NewLogger(logFile))

	// 完成转换
	if err := freeze(accountID, clusterID, region); err != nil {
		logger.Errorf(context.TODO(), "Trans %s to v2 failed: %s", clusterID, err)
		os.Exit(-1)
	}

	return
}

func freeze(accountID, clusterID, region string) error {
	ctx := context.TODO()

	if accountID == "" {
		return fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	if region == "" {
		return fmt.Errorf("region is empty")
	}

	// 后续语境中只使用 oldClusterID 和 newClusterID
	oldClusterID := clusterID

	// 初始化 trans.Client
	client, err := trans.NewClient(ctx, accountID, oldClusterID, ccetypes.Region(region))
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	// 冻结 V1 集群
	if err := client.FreezeV1Cluster(ctx, accountID, oldClusterID); err != nil {
		logger.Errorf(ctx, "FreezeV1Cluster failed: %s", err)
		return err
	}

	return nil
}
