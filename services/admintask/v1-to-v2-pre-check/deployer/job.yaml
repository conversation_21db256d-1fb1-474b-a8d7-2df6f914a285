apiVersion: batch/v1
kind: Job
metadata:
  name: cce-v1-to-v2-pre-check-c-qdwtoRax
  namespace: cce-trans
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    metadata:
      name: cce-trans
    spec:
      hostNetwork: true
      serviceAccount: cce-trans-serviceaccount
      imagePullSecrets:	
      - name: ccr-registry-secret
      containers:
      - name: cce-trans
        image: registry.baidubce.com/cce-service-dev/v1-to-v2-pre-check:haha
        imagePullPolicy: Always
        args: 
        - -region=gztest
        - -cluster-id=c-qdwtoRax
        - -account-id=eca97e148cb74e9683d7b7240829d1ff
        - -enable-hostname=false
      restartPolicy: Never
