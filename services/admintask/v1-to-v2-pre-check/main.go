// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/09/10 15:54:00, by <EMAIL>, create
*/
/*
DESCRIPTION
V1 升级 V2 前置检查
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	beegologger "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger/beego"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans"
)

func main() {
	var (
		logFile   string
		region    string
		accountID string
		clusterID string

		enableHostname bool
	)

	flag.StringVar(&logFile, "log-file", "", "日志路径, 不设置则打印到 STDOUT.")

	flag.StringVar(&region, "region", "", "sandbox, gztest, gz, su, bj, bd, hkg, fwh")
	flag.StringVar(&accountID, "account-id", "", "迁移账户 ID")
	flag.StringVar(&clusterID, "cluster-id", "", "迁移集群 ID")
	flag.BoolVar(&enableHostname, "enable-hostname", false, "是否开启 hostname")

	flag.Parse()

	logger.SetLogger(beegologger.NewLogger(logFile))

	// 前置检查
	if err := check(accountID, clusterID, region, enableHostname); err != nil {
		logger.Errorf(context.TODO(), "Trans %s to v2 failed: %s", clusterID, err)
		os.Exit(-1)
	}

	return
}

func check(accountID, clusterID, region string, enableHostname bool) error {
	ctx := context.TODO()

	if accountID == "" {
		return fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	if region == "" {
		return fmt.Errorf("region is empty")
	}

	// 后续语境中只使用 oldClusterID 和 newClusterID
	oldClusterID := clusterID

	// 初始化 trans.Client
	client, err := trans.NewClient(ctx, accountID, oldClusterID, ccetypes.Region(region))
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	// 检查 K8S NodeName 和 数据库 Hostname 是否一致
	if enableHostname {
		if err := client.CheckV1Hostname(ctx, accountID, oldClusterID); err != nil {
			logger.Errorf(ctx, "CheckV1Hostname failed: %s", err)
			return err
		}
	}

	// 检查 t_cluster, t_instance, cluster_info 主子账号信息
	if err := client.CheckAccountID(ctx, accountID, oldClusterID); err != nil {
		logger.Errorf(ctx, "CheckAccountID failed: %s", err)
		return err
	}

	return nil
}
