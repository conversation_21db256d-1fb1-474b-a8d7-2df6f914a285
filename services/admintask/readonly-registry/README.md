# readonly-registry

[TOC]

通过设置registry使用的bos存储权限，设置registry为只读/可写/允许部分namespace推送。

## 使用说明

### 参数设置

设置`main`函数入口处的三个变量，变更镜像仓库可以推送的范围。

```go
func main() {
    env := "sandbox"  // jpaas,sandbox,online
    want := "partial" // ro,partial,rw
    // allowed namespaces in partial mode
    allowedNamespaces := []string{
        "cce",
    }

    ...
}
```

参数说明

- `env`: 变更配置的环境
  - `jpaas`: 测试bos权限设置功能的测试桶，位于jpaas账号下
  - `sandbox`: 沙盒registry (<http://gzns-store-sandbox105.gzns:8000>)
  - `online`: 线上registry (<https://hub.baidubce.com>)
- `want`: 想要设置的状态
  - `ro`: 全部只读，完全禁止推送镜像
  - `rw`: 全部可写，所有用户均可以推送镜像
  - `partial`: 部分可写，允许向部分namespace中推送镜像
- `allowedNamespaces`: 仅在`partial`状态下生效，设置允许推送的namespace列表（**注意**: 这个列表是全量列表，每次操作是覆盖之前的允许列表，所以添加namespace直接写在后面即可，不要删除前面的namespace，除非确认要回收某个namespace的推送权限）


### 运行方式

```bash
$ go run main.go
```

线上仓库操作bos有机器白名单限制，需要先build为二进制，放到对应有权限的机器运行。

```bash
$ CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build services/admintask/readonly-registry
```

运行时会输出原始的acl，提交的acl，以及提交之后的新acl。

```bash
$ go run main.go
2021/01/28 23:00:58 orignal acl is {"accessControlList":[{"grantee":[{"id":"*"}],"permission":["WRITE"],"notResource":["cce-registry-qa-sandbox-bj/docker/registry/v2/repositories/cce/*","cce-registry-qa-sandbox-bj/docker/registry/v2/blobs/*"],"condition":{"ipAddress":null,"referer":{"stringLike":null,"stringEquals":null}}},{"grantee":[{"id":"4a99345e11e54331a1297c3c273c758b"}],"permission":["FULL_CONTROL"],"condition":{"ipAddress":null,"referer":{"stringLike":null,"stringEquals":null}}}],"owner":{"id":"4a99345e11e54331a1297c3c273c758b"}}
2021/01/28 23:00:58 enablePartialWrite acl is {
    "accessControlList":[
        {
            "notResource": ["cce-registry-qa-sandbox-bj/docker/registry/v2/blobs/*","cce-registry-qa-sandbox-bj/docker/registry/v2/repositories/cce/*"],
            "permission": ["WRITE"],
            "effect": "Deny",
            "grantee": [{
                "id": "*"
            }]
        }
    ]
}
2021/01/28 23:00:58 enablePartialWrite successfully
2021/01/28 23:00:58 new acl is {"accessControlList":[{"grantee":[{"id":"*"}],"permission":["WRITE"],"notResource":["cce-registry-qa-sandbox-bj/docker/registry/v2/blobs/*","cce-registry-qa-sandbox-bj/docker/registry/v2/repositories/cce/*"],"condition":{"ipAddress":null,"referer":{"stringLike":null,"stringEquals":null}}},{"grantee":[{"id":"4a99345e11e54331a1297c3c273c758b"}],"permission":["FULL_CONTROL"],"condition":{"ipAddress":null,"referer":{"stringLike":null,"stringEquals":null}}}],"owner":{"id":"4a99345e11e54331a1297c3c273c758b"}}
```

### 回滚方式

checkout回仓库中的文件版本，重新执行一遍即可。
