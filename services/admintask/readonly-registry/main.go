package main

import (
	"encoding/json"
	"fmt"
	"log"

	"github.com/baidubce/bce-sdk-go/services/bos"
	bosapi "github.com/baidubce/bce-sdk-go/services/bos/api"
)

var bosContexts = map[string]bosContext{
	// test bucket in jpaas
	"jpaas": {
		AK:       "184f0d0786854108a310f345d6ad90b4",
		SK:       "00d96528df0446c38554ae844c5d8ade",
		Endpoint: "bj.bcebos.com",
		Bucket:   "bos-csi-test-bj-jpaas",
	},
	// sandbox registry
	"sandbox": {
		AK:       "********************************",
		SK:       "ab263cb59879481790120db3a709bbaf",
		Endpoint: "bj-bos-sandbox.baidu-int.com",
		Bucket:   "cce-registry-qa-sandbox-bj",
	},
	// online registry
	"online": {
		AK:       "c0d3690042364b47a8e5b91e3d834b83",
		SK:       "fe2e4b7c70234a2c926a159112996820",
		Endpoint: "bj.bcebos.com",
		Bucket:   "cce-registry-online-bj",
	},
}

type bosContext struct {
	AK       string
	SK       string
	Endpoint string
	Bucket   string
}

func main() {
	env := "online"   // jpaas,sandbox,online
	want := "partial" // ro,partial,rw
	// allowed namespaces in partial mode
	allowedNamespaces := []string{
		"cce",
		"poc-images", // added on 20210201, should be removed before 20210206, <EMAIL>
	}

	ctx, ok := bosContexts[env]
	if !ok {
		log.Fatalln("unknown env:", env)
	}

	// 创建BOS服务的Client
	bosClient, err := bos.NewClient(ctx.AK, ctx.SK, ctx.Endpoint)
	if err != nil {
		log.Fatalln("bos.NewClient err:", err)
	}

	acl, err := bosClient.GetBucketAcl(ctx.Bucket)
	if err != nil {
		log.Fatalln("GetBucketAcl err:", err)
	}
	log.Println("orignal acl is", mustToJSON(acl))

	switch want {
	case "ro":
		if err := disableWrite(bosClient, ctx.Bucket); err != nil {
			log.Fatalln("disableWrite err:", err)
		} else {
			log.Println("disableWrite successfully")
		}

	case "partial":
		// blob prefix is always allowed in partial write case
		allowed := []string{ctx.Bucket + "/docker/registry/v2/blobs/*"}
		for _, ns := range allowedNamespaces {
			allowed = append(allowed, ctx.Bucket+fmt.Sprintf("/docker/registry/v2/repositories/%s/*", ns))
		}
		if err := enablePartialWrite(bosClient, ctx.Bucket, allowed); err != nil {
			log.Fatalln("enablePartialWrite err:", err)
		} else {
			log.Println("enablePartialWrite successfully")
		}

	case "rw":
		if err := enableWrite(bosClient, ctx.Bucket); err != nil {
			log.Fatalln("enableWrite err:", err)
		} else {
			log.Println("enableWrite successfully")
		}

	default:
		log.Fatalln("unknown want state", want)
	}

	acl, err = bosClient.GetBucketAcl(ctx.Bucket)
	if err != nil {
		log.Fatalln("GetBucketAcl err:", err)
	}
	log.Println("new acl is", mustToJSON(acl))
}

func disableWrite(c *bos.Client, bucket string) error {
	aclString := `{
    "accessControlList":[
        {
            "permission": ["WRITE"],
            "effect": "Deny",
            "grantee": [{
                "id": "*"
            }]
        }
    ]
}`
	return c.PutBucketAclFromString(bucket, aclString)
}

func enablePartialWrite(c *bos.Client, bucket string, allowPatterns []string) error {
	aclString := fmt.Sprintf(`{
    "accessControlList":[
        {
            "notResource": %s,
            "permission": ["WRITE"],
            "effect": "Deny",
            "grantee": [{
                "id": "*"
            }]
        }
    ]
}`, mustToJSON(allowPatterns))
	log.Println("enablePartialWrite acl is", aclString)
	return c.PutBucketAclFromString(bucket, aclString)
}

func enableWrite(c *bos.Client, bucket string) error {
	return c.PutBucketAclFromCanned(bucket, bosapi.CANNED_ACL_PRIVATE)
}

func mustToJSON(v interface{}) string {
	ret, err := json.Marshal(v)
	if err != nil {
		panic(err)
	}
	return string(ret)
}
