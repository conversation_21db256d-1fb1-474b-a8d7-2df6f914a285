apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-upgrade
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-print-log
  namespace: default
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    spec:
      hostNetwork: true
      restartPolicy: Never
      containers:
        - name: cce-print-log
          image: registry.baidubce.com/cce-plugin-dev/print-log:jichao
          imagePullPolicy: Always
          args:
            - /print-log

