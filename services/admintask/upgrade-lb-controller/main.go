package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"os"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

type workFlowConfiguration struct {
	ClusterID   string
	TargetImage string
	AccountID   string
	UserID      string
}

type upgradeLBControllerConfig struct {
	Region                    string
	TargetImage               string
	ClusterIDFilePath         string
	MetaClusterKubeConfigFile string
}

var (
	config = upgradeLBControllerConfig{}

	databaseEndpoint = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.11.48.247:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(10.70.16.33:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}
)

func main() {
	ctx := context.TODO()

	// 解析参数
	parseFlags()

	// 准备依赖环境
	model, err := models.NewClient(ctx, databaseEndpoint[config.Region])
	if err != nil {
		fmt.Printf("New DB Client failed: %s\n", err)
		return
	}

	// 初始化 metaclient
	metaclient, err := meta.NewClient(ctx, config.MetaClusterKubeConfigFile)
	if err != nil {
		fmt.Printf("New Meta Cluster Cilent failed: %v\n", err)
		return
	}

	// 获取待升级的 Cluster 列表
	clusterIDList, err := getClusterIDList()
	if err != nil {
		fmt.Printf("Get Cluster ID  failed: %v\n", err)
		return
	}
	clusters := make([]*models.Cluster, 0)
	for _, clusterID := range clusterIDList {
		cluster, err := model.GetClusterByClusterID(context.TODO(), clusterID)
		if err != nil {
			fmt.Printf("Get Cluster by ID %s failed: %v\n", clusterID, err)
			return
		}
		clusters = append(clusters, cluster)
	}

	// 升级全部集群
	if err := upgradeClusters(clusters, metaclient); err != nil {
		fmt.Printf("Upgrade clusters failed: %v\n", err)
		return
	}

	// 结束
	fmt.Println("Upgrade process complete")
}

func parseFlags() {
	flag.StringVar(&config.Region, "region", "", "[Required]Region of the clusters to be upgraded.")
	flag.StringVar(&config.TargetImage, "target-image", "", "[Required]Target full image address of cce-lb-controller")
	flag.StringVar(&config.ClusterIDFilePath, "clusterid-file-path", "", "[Required]File path of the text file which includes list of clusterID")
	flag.StringVar(&config.MetaClusterKubeConfigFile, "meta-cluster-kubeconfig", "", "KubeConfig file path ")

	flag.Parse()
}

func getClusterIDList() ([]string, error) {
	// 打开集群ID文件
	file, err := os.Open(config.ClusterIDFilePath)
	if err != nil {
		fmt.Printf("Fail to open file %s\n", config.ClusterIDFilePath)
		return nil, err
	}
	defer file.Close()

	// 逐行读取集群ID文件
	clusters := make([]string, 0)
	scanner := bufio.NewScanner(file)
	// optionally, resize scanner's capacity for lines over 64K, see next example
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		clusters = append(clusters, line)
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("Fail to read lines %s\n", err.Error())
		return nil, err
	}

	return clusters, nil
}

func upgradeClusters(clusters []*models.Cluster, metaClusterClient meta.Interface) error {
	upgradeClusters := make([]*models.Cluster, 0)
	failedToUpgradeCluster := make([]string, 0)
	// 为每一个集群部署 Workflow
	for _, cluster := range clusters {
		wfConf := workFlowConfiguration{
			ClusterID:   cluster.Spec.ClusterID,
			AccountID:   cluster.Spec.AccountID,
			UserID:      cluster.Spec.AccountID,
			TargetImage: config.TargetImage,
		}
		if err := upgradeOneCluster(wfConf, metaClusterClient); err != nil {
			fmt.Printf("Fail to deploy upgrade workflow for cluster %s: %v\n", cluster.Spec.ClusterID, err)
			failedToUpgradeCluster = append(failedToUpgradeCluster, cluster.Spec.ClusterID)
		} else {
			upgradeClusters = append(upgradeClusters, cluster)
		}
	}

	// 检查所有 workflow 执行结果`
	for count := 1; count <= len(upgradeClusters)*2; count++ {
		upgrading := make([]string, 0)
		succeed := make([]string, 0)
		failed := make([]string, 0)
		other := make([]string, 0)

		fmt.Println("Waiting....Count ", count)
		time.Sleep(20 * time.Second)

		for _, cluster := range upgradeClusters {
			phase, err := checkOneCluster(cluster.Spec.ClusterID, metaClusterClient)
			if err != nil {
				fmt.Printf("Fail to check workflow: %v", err)
				continue
			}
			switch phase {
			case ccetypes.WorkflowPhaseSucceeded:
				succeed = append(succeed, cluster.Spec.ClusterID)
			case ccetypes.WorkflowPhaseFailed:
				failed = append(failed, cluster.Spec.ClusterID)
			case ccetypes.WorkflowPhaseUpgrading:
				upgrading = append(upgrading, cluster.Spec.ClusterID)
			default:
				other = append(other, cluster.Spec.ClusterID)
			}
		}
		if len(succeed) == len(upgradeClusters) {
			fmt.Println("All workflow execution complete")
			if len(failedToUpgradeCluster) != 0 {
				fmt.Printf("fail to create workflow %v\n", failedToUpgradeCluster)
				return fmt.Errorf("some cluster fail to create workflow")
			} else {
				return nil
			}
		} else {
			fmt.Println("Not all workflow execution complete")
			fmt.Printf("fail to create workflow %v\n", failedToUpgradeCluster)
			fmt.Printf("upgrading %v\n", upgrading)
			fmt.Printf("succeed %v\n", succeed)
			fmt.Printf("failed %v\n", failed)
			fmt.Printf("other %v\n", other)
		}
	}

	return fmt.Errorf("not all workflow execution complete")
}

/*
`
apiVersion: cce.baidubce.com/v1
kind: Workflow
metadata:
  namespace: default
  name: upgrade-lb-controller-{{.ClusterID}}
spec:
  workflowType: UpgradeCCELBController
  handler: default
  config:
    upgradePluginWorkflowConfig:
      cceLBController:
        cceLBControllerImage: {{.TargetImage}}
  accountID: {{.AccountID}}
  userID: {{.UserID}}
  clusterID: {{.ClusterID}}
`
*/

func checkOneCluster(clusterID string, metaClusterClient meta.Interface) (ccetypes.WorkflowPhase, error) {
	workflowname := fmt.Sprintf("upgrade-lb-controller-%s", clusterID)
	wkf, err := metaClusterClient.GetWorkflow(context.TODO(), "default", workflowname, &metav1.GetOptions{})
	if err != nil {
		return "", err
	}
	return wkf.Status.WorkflowPhase, err
}

func upgradeOneCluster(config workFlowConfiguration, metaClusterClient meta.Interface) error {
	// 渲染 Workflow
	workflow := &ccev1.Workflow{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "default",
			Name:      fmt.Sprintf("upgrade-lb-controller-%s", config.ClusterID),
		},
		Spec: ccetypes.WorkflowSpec{
			WorkflowType: ccetypes.WorkflowTypeUpgradeCCELBController,
			Handler:      "default",
			ClusterID:    config.ClusterID,
			AccountID:    config.AccountID,
			UserID:       config.UserID,
			WorkflowConfig: ccetypes.WorkflowConfig{
				UpgradePluginWorkflowConfig: &ccetypes.UpgradePluginWorkflowConfig{
					CCELBController: &ccetypes.UpgradeCCELBControllerConfig{
						CCELBControllerImage: config.TargetImage,
					},
				},
			},
		},
	}

	// 应用在 Meta Cluster 上
	_, err := metaClusterClient.CreateWorkflow(context.TODO(), "default", workflow)
	if err != nil {
		return err
	}
	return nil
}
