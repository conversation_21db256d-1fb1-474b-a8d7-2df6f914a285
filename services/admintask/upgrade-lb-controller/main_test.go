package main

import (
	"fmt"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"strconv"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	matamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
)

func Test_checkOneCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	cases := []struct {
		name              string
		clusterID         string
		metaClusterClient meta.Interface
		expectErr         bool
	}{
		{
			name:      "normal",
			clusterID: "cce-********",
			metaClusterClient: func() meta.Interface {
				client := matamock.NewMockInterface(ctrl)
				client.EXPECT().GetWorkflow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(&ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						WorkflowPhase: ccetypes.WorkflowPhasePending,
					},
				}, nil)

				return client
			}(),
			expectErr: false,
		},
		{
			name:      "error",
			clusterID: "cce-********",
			metaClusterClient: func() meta.Interface {
				client := matamock.NewMockInterface(ctrl)
				client.EXPECT().GetWorkflow(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("unexpected error"))

				return client
			}(),
			expectErr: true,
		},
	}

	for _, c := range cases {
		_, err := checkOneCluster(c.clusterID, c.metaClusterClient)
		if (err != nil) != c.expectErr {
			t.Errorf("checkOneCluster failed expect isError=%s but actual not", strconv.FormatBool(c.expectErr))
		}
	}
}

func Test_upgradeOneCluster(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	cases := []struct {
		name              string
		config            workFlowConfiguration
		metaClusterClient meta.Interface
		expectErr         bool
	}{
		{
			name: "normal",
			config: workFlowConfiguration{
				ClusterID:   "cce-********",
				TargetImage: "a.com/b:c",
				AccountID:   "abcde",
				UserID:      "abcde",
			},
			metaClusterClient: func() meta.Interface {
				client := matamock.NewMockInterface(ctrl)
				client.EXPECT().CreateWorkflow(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil)

				return client
			}(),
			expectErr: false,
		},
		{
			name: "create error",
			config: workFlowConfiguration{
				ClusterID:   "cce-********",
				TargetImage: "a.com/b:c",
				AccountID:   "abcde",
				UserID:      "abcde",
			},
			metaClusterClient: func() meta.Interface {
				client := matamock.NewMockInterface(ctrl)
				client.EXPECT().CreateWorkflow(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, fmt.Errorf("unexpected error"))

				return client
			}(),
			expectErr: true,
		},
	}

	for _, c := range cases {
		err := upgradeOneCluster(c.config, c.metaClusterClient)
		if (err != nil) != c.expectErr {
			t.Errorf("upgradeOneCluster failed expect isError=%s but actual not", strconv.FormatBool(c.expectErr))
		}
	}
}
