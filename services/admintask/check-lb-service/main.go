package main

import (
	"context"
	"flag"
	"fmt"
	"sort"
	"strings"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/kubernetes/pkg/apis/apps"
	v1 "k8s.io/kubernetes/pkg/apis/apps/v1"
	"k8s.io/kubernetes/pkg/apis/extensions/v1beta1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

type clusterInfo struct {
	name         string
	accountID    string
	clusterID    string
	imageVersion string
	lbService    []string
}

type checkLBServiceConfig struct {
	Region    string
	AccountID string
}

var (
	config = checkLBServiceConfig{}

	databaseEndpoint = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.11.48.247:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(10.70.16.33:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}
)

func main() {
	ctx := context.TODO()

	// 解析参数
	parseFlags()

	if _, ok := databaseEndpoint[config.Region]; !ok {
		fmt.Printf("Region %v is not valid\n", config.Region)
		return
	}

	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint[config.Region])
	if err != nil {
		fmt.Printf("NewClient failed: %s\n", err)
		return
	}

	// 获取地域所有集群, 包含 V1 和 V2
	// 获取所有待定集群
	clusterIDs := make([]string, 0)
	if config.AccountID != "" {
		// 根据 account id 获取地域所有集群
		clusters, err := model.GetClusterList(ctx, config.AccountID)
		if err != nil {
			fmt.Printf("GetClusterList failed: %s\n", err)
			return
		}
		for _, c := range clusters {
			clusterIDs = append(clusterIDs, c.Spec.ClusterID)
		}
	} else {
		// 获取地域所有集群, 包含 V1 和 V2
		clusterIDs, err = model.GetAllRunningClusterIDsCompatibility(ctx)
		if err != nil {
			fmt.Printf("GetAllRunningClusterIDsCompatibility failed: %s\n", err)
			return
		}
	}

	fmt.Printf("All ClusterIDs: %s\n", utils.ToJSON(clusterIDs))

	// 条件过滤集群
	targetClusters := filterCluster(model, clusterIDs)
	fmt.Printf("Target ClusterIDs: %s\n", utils.ToJSON(targetClusters))

	// 检查所有集群
	infoList := make([]clusterInfo, 0)
	for _, clusterID := range targetClusters {
		info, err := checkOneCluster(model, clusterID)
		if err != nil {
			fmt.Printf("Fail to check cluster %s: %v\n", clusterID, err)
			continue
		}
		infoList = append(infoList, *info)
	}
	sort.SliceStable(infoList, func(i, j int) bool {
		return len(infoList[i].lbService) < len(infoList[j].lbService)
	})

	printResult(infoList)
}

func checkOneCluster(model *models.Client, clusterID string) (*clusterInfo, error) {
	ctx := context.TODO()
	info := &clusterInfo{
		clusterID: clusterID,
		lbService: make([]string, 0),
	}

	// 获取集群的 AccountID
	cluster, err := model.GetClusterByClusterID(context.TODO(), clusterID)
	if err != nil {
		return nil, err
	}

	info.name = cluster.Spec.ClusterName
	info.accountID = cluster.Spec.AccountID

	// 构建 User Cluster K8S Client
	kubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		fmt.Printf("GetAdminKubeConfigCompatibility failed: %s\n", err)
		return nil, err
	}
	userK8sClient, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
	if err != nil {
		fmt.Printf("NewK8SClient failed: %s\n", err)
		return nil, err
	}

	// 获取集群的 cce-lb-controller 版本
	imageID := ""
	deployment, err := getDeployment(ctx, userK8sClient, "kube-system", "cce-lb-controller")
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return info, err
		}
		imageID = "DeploymentNotFound"
	}
	if deployment != nil {
		if len(deployment.Spec.Template.Spec.Containers) == 1 {
			imageID = deployment.Spec.Template.Spec.Containers[0].Image
		} else {
			imageID = "UnexpectedDeployment"
		}
	}
	info.imageVersion = imageID

	// 获取集群的 LB Service
	svcList, err := userK8sClient.CoreV1().Services("").List(ctx, metav1.ListOptions{})
	for _, svc := range svcList.Items {
		if svc.Spec.Type == corev1.ServiceTypeLoadBalancer {
			info.lbService = append(info.lbService, fmt.Sprintf("%s/%s", svc.Namespace, svc.Name))
		}
	}

	return info, nil
}

func getDeployment(ctx context.Context, k8sClient kubernetes.Interface, ns string, name string) (*apps.Deployment, error) {
	deployment := &apps.Deployment{}

	// 1. 先找 apps/v1 版本的 Deployment 并转换为内部版本返回
	deploymentAppsV1, err := k8sClient.AppsV1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, err
	}
	if err == nil {
		if err := v1.Convert_v1_Deployment_To_apps_Deployment(deploymentAppsV1, deployment, nil); err != nil {
			return nil, err
		}
		return deployment, nil
	}

	// 2. 再找 extensions/v1beta1 版本的 Deployment(早期的部署) 并转换为内部版本返回
	deploymentExtensionsV1Beta1, err := k8sClient.ExtensionsV1beta1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	if err := v1beta1.Convert_v1beta1_Deployment_To_apps_Deployment(deploymentExtensionsV1Beta1, deployment, nil); err != nil {
		return nil, err
	}
	return deployment, nil
}

func filterCluster(model *models.Client, clusterIDs []string) []string {
	result := make([]string, 0)

	for _, clusterID := range clusterIDs {
		if strings.HasPrefix(clusterID, "cce-") {
			result = append(result, clusterID)
		}
	}

	return result
}

func parseFlags() {
	flag.StringVar(&config.Region, "region", "", "[Required]Region of the clusters to be checked.")
	flag.StringVar(&config.AccountID, "accountid", "", "Account ID")

	flag.Parse()
}

func printResult(results []clusterInfo) {
	fmt.Printf("%20s %15s %100s %50s %40s\n", "LB Service Number", "Cluster ID", "Image", "Name", "AccountID")
	for _, clusterInfo := range results {
		fmt.Printf("%20d %15s %100s %50s %40s\n", len(clusterInfo.lbService), clusterInfo.clusterID, clusterInfo.imageVersion, clusterInfo.name, clusterInfo.accountID)
	}
}
