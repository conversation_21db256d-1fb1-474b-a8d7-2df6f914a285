apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cce-trans
rules:
- apiGroups:
  - ""
  resources:
  - configmaps
  verbs:
  - get
  - list
  - watch
  - create
  - update
  - patch
  - delete
- apiGroups:
  - cce.baidubce.com
  resources:
  - clusters
  verbs:
  - get
  - list
  - watch
  - patch
  - update
  - create
- apiGroups:
  - cce.baidubce.com
  resources:
  - instances
  verbs:
  - get
  - list
  - patch
  - watch
  - update
  - delete
  - create
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: cce-trans
  name: cce-trans-serviceaccount
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cce-trans-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-trans
subjects:
- kind: ServiceAccount
  namespace: cce-trans
  name: cce-trans-serviceaccount
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-trans
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-trans-c-qdwtorax
  namespace: cce-trans
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    metadata:
      name: cce-trans
    spec:
      hostNetwork: true
      serviceAccount: cce-trans-serviceaccount
      imagePullSecrets:	
      - name: ccr-registry-secret
      containers:
      - name: cce-trans
        image: registry.baidubce.com/cce-service-dev/cce-cluster-trans:chenhuan
        imagePullPolicy: Always
        args: 
        - -region=gztest
        - -cluster-id=c-qdwtoRax
        - -account-id=eca97e148cb74e9683d7b7240829d1ff
        - -enable-hostname=true
        - -runtime-version=20.10.5
        - -lb-service-subnet-id=sbn-fy320duj1pc5
        - -node-port-range-min=30000
        - -node-port-range-max=32768
        - -cluster-pod-cidr=**********/16
        - -cluster-ip-service-cidr=**********/16
        - -max-pods-per-node=256
        - -kube-proxy-mode=ipvs
        - -enable-node-local-dns=false
        - -node-local-dns-addr=
        - -cluster-blb-id=lb-afb06eb6
      restartPolicy: Never


