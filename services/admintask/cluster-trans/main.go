// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/26 13:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
将 V1 Cluster/Instance 迁移到 V2 声明式架构
*/

package main

import (
	"context"
	"flag"
	"fmt"
	"os"
	"strings"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	beegologger "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger/beego"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans"
)

func main() {
	var (
		logFile   string
		region    string
		accountID string
		clusterID string

		enableHostname bool
		runtimeVersion string

		lbServceSubnetID     string
		nodePortRangeMin     int
		nodePortRangeMax     int
		clusterPodCIDR       string
		clusterIPServiceCIDR string
		maxPodsPerNode       int
		kubeProxyMode        string
		enableNodeLocalDNS   bool
		nodeLocalDNSAddr     string

		clusterBLBID string
	)

	flag.StringVar(&logFile, "log-file", "", "日志路径, 不设置则打印到 STDOUT.")

	flag.StringVar(&region, "region", "", "sandbox, gztest, gz, su, bj, bd, hkg, fwh")
	flag.StringVar(&accountID, "account-id", "", "迁移账户 ID")
	flag.StringVar(&clusterID, "cluster-id", "", "迁移集群 ID")

	flag.BoolVar(&enableHostname, "enable-hostname", false, "是否使用 Hostname")
	flag.StringVar(&runtimeVersion, "runtime-version", "", "docker 运行时版本")

	flag.StringVar(&lbServceSubnetID, "lb-service-subnet-id", "", "LB Service 默认子网")
	flag.IntVar(&nodePortRangeMin, "node-port-range-min", 30000, "NodePort 最小值")
	flag.IntVar(&nodePortRangeMax, "node-port-range-max", 32767, "NodePort 最大值")
	flag.StringVar(&clusterPodCIDR, "cluster-pod-cidr", "", "集群 Pod 网段")
	flag.StringVar(&clusterIPServiceCIDR, "cluster-ip-service-cidr", "", "集群 ClusterIP 网段")
	flag.IntVar(&maxPodsPerNode, "max-pods-per-node", 256, "节点最大 Pod 数")
	flag.StringVar(&kubeProxyMode, "kube-proxy-mode", "ipvs", "kube-proxy 模式")
	flag.BoolVar(&enableNodeLocalDNS, "enable-node-local-dns", false, "是否开启 NodeLocalDNS")
	flag.StringVar(&nodeLocalDNSAddr, "node-local-dns-addr", "", "NodeLocalDNS 地址")

	flag.StringVar(&clusterBLBID, "cluster-blb-id", "", "Cluster BLB ID")

	flag.Parse()

	logger.SetLogger(beegologger.NewLogger(logFile))

	config := configuration.Config{
		EnableHostname: enableHostname,
		RuntimeVersion: runtimeVersion,

		LBServiceVPCSubnetID: lbServceSubnetID,
		NodePortRangeMin:     nodePortRangeMin,
		NodePortRangeMax:     nodePortRangeMax,
		ClusterPodCIDR:       clusterPodCIDR,
		ClusterIPServiceCIDR: clusterIPServiceCIDR,
		MaxPodsPerNode:       maxPodsPerNode,
		KubeProxyMode:        ccetypes.KubeProxyMode(kubeProxyMode),
		EnableNodeLocalDNS:   enableNodeLocalDNS,
		NodeLocalDNSAddr:     nodeLocalDNSAddr,

		ClusterBLBID: clusterBLBID,
	}

	// 完成转换
	if err := transform(accountID, clusterID, region, config); err != nil {
		logger.Errorf(context.TODO(), "Trans %s to v2 failed: %s", clusterID, err)
		os.Exit(-1)
	}

	return
}

func transform(accountID, clusterID, region string, config configuration.Config) error {
	ctx := context.TODO()

	if accountID == "" {
		return fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	if region == "" {
		return fmt.Errorf("region is empty")
	}

	// 后续语境中只使用 oldClusterID 和 newClusterID
	oldClusterID := clusterID

	// 初始化 trans.Client
	client, err := trans.NewClient(ctx, accountID, oldClusterID, ccetypes.Region(region))
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	// 迁移 Cluster 对象
	logger.Infof(ctx, "-----------------------------Trans Cluster Begin--------------------------------")
	cluster, err := transCluster(ctx, client, accountID, oldClusterID, config)
	if err != nil {
		logger.Errorf(ctx, "transCluster failed: %s", err)
		return err
	}

	logger.Infof(ctx, "TransCluster success: %s", utils.ToJSON(cluster))

	logger.Infof(ctx, "-----------------------------Trans Cluster Success--------------------------------")

	// 迁移 CaCert
	logger.Infof(ctx, "-----------------------------Trans CACert Begin--------------------------------")
	if err := transCACert(ctx, client, accountID, oldClusterID); err != nil {
		logger.Errorf(ctx, "transCACert failed: %s", err)
		return err
	}

	logger.Infof(ctx, "-----------------------------Trans CACert Success--------------------------------")

	// 迁移 KubeConfig
	logger.Infof(ctx, "-----------------------------Trans KubeConfig Begin--------------------------------")
	if err := transKubeConfig(ctx, client, accountID, oldClusterID); err != nil {
		logger.Errorf(ctx, "transKubeConfig failed: %s", err)
		return err
	}

	logger.Infof(ctx, "-----------------------------Trans KubeConfig Success--------------------------------")

	// 迁移 Instances
	logger.Infof(ctx, "-----------------------------Trans Instances Begin--------------------------------")

	if err := transInstances(ctx, client, accountID, oldClusterID, cluster); err != nil {
		logger.Errorf(ctx, "transInstances failed: %s", err)
		return err
	}

	logger.Infof(ctx, "-----------------------------Trans Instances Success--------------------------------")

	return nil
}

func transCluster(ctx context.Context, client trans.Interface, accountID, clusterID string, config configuration.Config) (*ccev1.Cluster, error) {
	if client == nil {
		return nil, fmt.Errorf("client is nil")
	}

	// 设置 t_cluster status = Freezed
	// TODO: 手动执行 Freezed
	// if err := client.SetV1ClusterToFreezed(ctx, accountID, clusterID); err != nil {
	// 	logger.Errorf(ctx, "SetV1ClusterToFreezed failed: %s", err)
	// 	return nil, err
	// }

	// 获取 t_cluster To t_cce_cluster 数据结构
	cluster, err := client.GetV2ClusterFromV1(ctx, accountID, clusterID)
	if err != nil {
		logger.Errorf(ctx, "GetV2Cluster failed: %s", err)
		return nil, err
	}

	// 补全 Spec
	cluster.Spec.ClusterID = v2ClusterID(ctx, clusterID)
	cluster.Spec.Handler = ccev1.ClusterHandlerV1ToV2

	cluster.Spec.K8SCustomConfig.EnableHostname = config.EnableHostname
	cluster.Spec.RuntimeVersion = config.RuntimeVersion

	cluster.Spec.ContainerNetworkConfig.LBServiceVPCSubnetID = config.LBServiceVPCSubnetID
	cluster.Spec.ContainerNetworkConfig.NodePortRangeMin = config.NodePortRangeMin
	cluster.Spec.ContainerNetworkConfig.NodePortRangeMax = config.NodePortRangeMax
	cluster.Spec.ContainerNetworkConfig.ClusterPodCIDR = config.ClusterPodCIDR
	cluster.Spec.ContainerNetworkConfig.ClusterIPServiceCIDR = config.ClusterIPServiceCIDR
	cluster.Spec.ContainerNetworkConfig.MaxPodsPerNode = config.MaxPodsPerNode
	cluster.Spec.ContainerNetworkConfig.KubeProxyMode = config.KubeProxyMode
	cluster.Spec.ContainerNetworkConfig.EnableNodeLocalDNS = config.EnableNodeLocalDNS
	cluster.Spec.ContainerNetworkConfig.NodeLocalDNSAddr = config.NodeLocalDNSAddr

	// 补全 Status
	cluster.Status.ClusterPhase = ccetypes.ClusterPhaseRunning
	cluster.Status.InfrastructureReady = true
	cluster.Status.APIServerAccessSuccess = true
	cluster.Status.K8SPluginDeploySuccess = true
	cluster.Status.ClusterBLB.ID = config.ClusterBLBID

	logger.Infof(ctx, "GetV2Cluster success: %s", utils.ToJSON(cluster))

	// 写入 t_cce_cluster
	if err := client.CreateV2ClusterInDB(ctx, cluster); err != nil {
		logger.Errorf(ctx, "SaveV2Cluster failed: %s", err)
		return nil, err
	}

	// 提交到 meta-cluster
	crd, err := client.CreateV2ClusterInMetaCluster(ctx, cluster)
	if err != nil {
		logger.Errorf(ctx, "CreateV2ClusterInMetadata failed: %s", err)
		return nil, err
	}

	return crd, nil
}

func transCACert(ctx context.Context, client trans.Interface, accountID, clusterID string) error {
	if client == nil {
		return fmt.Errorf("client is nil")
	}

	// 获取 V1 Cluster CaCert
	caCert, err := client.GetCACertFromV1(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "GetCACertFromV1 failed: %s", err)
		return err
	}

	// 转化 ClusterID
	newClusterID := v2ClusterID(ctx, clusterID)
	caCert.ClusterID = v2ClusterID(ctx, clusterID)
	caCert.ID = 0 // 使用 Model ID

	// Cluster CaCert 存入 DB
	if err := client.CreateV2CACertInDB(ctx, newClusterID, caCert); err != nil {
		logger.Errorf(ctx, "CreateV2CACertInDB failed: %s", err)
		return err
	}

	return nil
}

func transKubeConfig(ctx context.Context, client trans.Interface, accountID, clusterID string) error {
	if client == nil {
		return fmt.Errorf("client is nil")
	}

	// 获取 V1 Cluster CaCert
	kubeconfig, err := client.GetKubeConfigFromV1(ctx, accountID, clusterID)
	if err != nil {
		logger.Errorf(ctx, "GetKubeConfigFromV1 failed: %s", err)
		return err
	}

	// 转化 ClusterID
	newClusterID := v2ClusterID(ctx, clusterID)
	kubeconfig.ClusterID = v2ClusterID(ctx, clusterID)

	// Cluster CaCert 存入 DB
	if err := client.CreateKubeConfigInDB(ctx, accountID, newClusterID, kubeconfig); err != nil {
		logger.Errorf(ctx, "CreateKubeConfigInDB failed: %s", err)
		return err
	}

	return nil
}

func transInstances(ctx context.Context, client trans.Interface, accountID string, oldClusterID string, cluster *ccev1.Cluster) error {
	if client == nil {
		return fmt.Errorf("client is nil")
	}

	if cluster == nil {
		return fmt.Errorf("cluster is nil")
	}

	// 获取 V1 Instance 列表, 包括 master 节点
	instances, err := client.GetInstancesFromV1(ctx, accountID, oldClusterID)
	if err != nil {
		logger.Errorf(ctx, "GetInstancesFromV1 failed: %s", err)
		return err
	}

	logger.Infof(ctx, "GetInstancesFromV1 accountID=%s clusterID=%s success: %s", accountID, oldClusterID, utils.ToJSON(instances))

	// 转化 ClusterID 和 InstanceID
	newClusterID := v2ClusterID(ctx, oldClusterID)
	for _, instance := range instances {
		cceInstanceID := instance.Spec.CCEInstanceID
		instanceID := instance.Status.Machine.InstanceID
		instance.Spec.ClusterID = newClusterID

		// 强制设置状态为 Ready, 避免误操作
		instance.Status.InstancePhase = ccetypes.InstancePhaseRunning
		instance.Status.InfrastructureReady = true
		instance.Status.ReinstallOSAlready = true
		instance.Status.DeploySuccess = true

		// 在 DB 中创建 Instance
		if err := client.CreateInstanceInDB(ctx, accountID, newClusterID, instance); err != nil {
			logger.Errorf(ctx, "CreateInstanceInDB failed: %s", err)
			return err
		}

		logger.Infof(ctx, "CreateInstanceInDB cceInstanceID=%s instanceID=%s success", cceInstanceID, instanceID)

		// 在 MetaCluster 中创建 CRD
		if err := client.CreateInstanceInMetaCluster(ctx, accountID, cluster, instance); err != nil {
			logger.Errorf(ctx, "CreateInstanceInMetaCluster failed: %s", err)
			return err
		}

		logger.Infof(ctx, "CreateInstanceInMetaCluster cceInstanceID=%s instanceID=%s success", cceInstanceID, instanceID)
	}

	return nil
}

// 将 c-cluster 转成 cce-cluster, 并转小写
func v2ClusterID(ctx context.Context, clusterID string) string {
	return strings.ToLower(strings.Replace(clusterID, "c-", "cce-", -1) + "v1")
}
