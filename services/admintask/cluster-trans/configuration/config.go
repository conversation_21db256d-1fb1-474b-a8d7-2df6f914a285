// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/09/07 15:51:00, by ch<PERSON><EMAIL>, create
*/
/*
DESCRIPTION
V1 迁移 V2 待补齐配置
*/

package configuration

import (
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

// Config - V1 迁移 V2 需要手动传入配置
type Config struct {
	EnableHostname bool
	RuntimeVersion string

	LBServiceVPCSubnetID string
	NodePortRangeMin     int
	NodePortRangeMax     int
	ClusterPodCIDR       string
	ClusterIPServiceCIDR string
	MaxPodsPerNode       int
	KubeProxyMode        ccetypes.KubeProxyMode
	EnableNodeLocalDNS   bool
	NodeLocalDNSAddr     string

	ClusterBLBID string
}
