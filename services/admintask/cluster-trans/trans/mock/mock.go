// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	models "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CheckAccountID mocks base method
func (m *MockInterface) CheckAccountID(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckAccountID", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckAccountID indicates an expected call of CheckAccountID
func (mr *MockInterfaceMockRecorder) CheckAccountID(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckAccountID", reflect.TypeOf((*MockInterface)(nil).CheckAccountID), arg0, arg1, arg2)
}

// CheckV1Hostname mocks base method
func (m *MockInterface) CheckV1Hostname(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckV1Hostname", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CheckV1Hostname indicates an expected call of CheckV1Hostname
func (mr *MockInterfaceMockRecorder) CheckV1Hostname(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckV1Hostname", reflect.TypeOf((*MockInterface)(nil).CheckV1Hostname), arg0, arg1, arg2)
}

// CreateInstanceInDB mocks base method
func (m *MockInterface) CreateInstanceInDB(arg0 context.Context, arg1, arg2 string, arg3 *models.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceInDB", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateInstanceInDB indicates an expected call of CreateInstanceInDB
func (mr *MockInterfaceMockRecorder) CreateInstanceInDB(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceInDB", reflect.TypeOf((*MockInterface)(nil).CreateInstanceInDB), arg0, arg1, arg2, arg3)
}

// CreateInstanceInMetaCluster mocks base method
func (m *MockInterface) CreateInstanceInMetaCluster(arg0 context.Context, arg1 string, arg2 *v1.Cluster, arg3 *models.Instance) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateInstanceInMetaCluster", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateInstanceInMetaCluster indicates an expected call of CreateInstanceInMetaCluster
func (mr *MockInterfaceMockRecorder) CreateInstanceInMetaCluster(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateInstanceInMetaCluster", reflect.TypeOf((*MockInterface)(nil).CreateInstanceInMetaCluster), arg0, arg1, arg2, arg3)
}

// CreateKubeConfigInDB mocks base method
func (m *MockInterface) CreateKubeConfigInDB(arg0 context.Context, arg1, arg2 string, arg3 *models.KubeConfig) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateKubeConfigInDB", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateKubeConfigInDB indicates an expected call of CreateKubeConfigInDB
func (mr *MockInterfaceMockRecorder) CreateKubeConfigInDB(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateKubeConfigInDB", reflect.TypeOf((*MockInterface)(nil).CreateKubeConfigInDB), arg0, arg1, arg2, arg3)
}

// CreateV2CACertInDB mocks base method
func (m *MockInterface) CreateV2CACertInDB(arg0 context.Context, arg1 string, arg2 *models.CaCert) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateV2CACertInDB", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateV2CACertInDB indicates an expected call of CreateV2CACertInDB
func (mr *MockInterfaceMockRecorder) CreateV2CACertInDB(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateV2CACertInDB", reflect.TypeOf((*MockInterface)(nil).CreateV2CACertInDB), arg0, arg1, arg2)
}

// CreateV2ClusterInDB mocks base method
func (m *MockInterface) CreateV2ClusterInDB(arg0 context.Context, arg1 *models.Cluster) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateV2ClusterInDB", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateV2ClusterInDB indicates an expected call of CreateV2ClusterInDB
func (mr *MockInterfaceMockRecorder) CreateV2ClusterInDB(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateV2ClusterInDB", reflect.TypeOf((*MockInterface)(nil).CreateV2ClusterInDB), arg0, arg1)
}

// CreateV2ClusterInMetaCluster mocks base method
func (m *MockInterface) CreateV2ClusterInMetaCluster(arg0 context.Context, arg1 *models.Cluster) (*v1.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateV2ClusterInMetaCluster", arg0, arg1)
	ret0, _ := ret[0].(*v1.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateV2ClusterInMetaCluster indicates an expected call of CreateV2ClusterInMetaCluster
func (mr *MockInterfaceMockRecorder) CreateV2ClusterInMetaCluster(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateV2ClusterInMetaCluster", reflect.TypeOf((*MockInterface)(nil).CreateV2ClusterInMetaCluster), arg0, arg1)
}

// FreezeV1Cluster mocks base method
func (m *MockInterface) FreezeV1Cluster(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FreezeV1Cluster", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// FreezeV1Cluster indicates an expected call of FreezeV1Cluster
func (mr *MockInterfaceMockRecorder) FreezeV1Cluster(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FreezeV1Cluster", reflect.TypeOf((*MockInterface)(nil).FreezeV1Cluster), arg0, arg1, arg2)
}

// GetCACertFromV1 mocks base method
func (m *MockInterface) GetCACertFromV1(arg0 context.Context, arg1 string) (*models.CaCert, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCACertFromV1", arg0, arg1)
	ret0, _ := ret[0].(*models.CaCert)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCACertFromV1 indicates an expected call of GetCACertFromV1
func (mr *MockInterfaceMockRecorder) GetCACertFromV1(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCACertFromV1", reflect.TypeOf((*MockInterface)(nil).GetCACertFromV1), arg0, arg1)
}

// GetInstancesFromV1 mocks base method
func (m *MockInterface) GetInstancesFromV1(arg0 context.Context, arg1, arg2 string) ([]*models.Instance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetInstancesFromV1", arg0, arg1, arg2)
	ret0, _ := ret[0].([]*models.Instance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetInstancesFromV1 indicates an expected call of GetInstancesFromV1
func (mr *MockInterfaceMockRecorder) GetInstancesFromV1(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetInstancesFromV1", reflect.TypeOf((*MockInterface)(nil).GetInstancesFromV1), arg0, arg1, arg2)
}

// GetKubeConfigFromV1 mocks base method
func (m *MockInterface) GetKubeConfigFromV1(arg0 context.Context, arg1, arg2 string) (*models.KubeConfig, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetKubeConfigFromV1", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.KubeConfig)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetKubeConfigFromV1 indicates an expected call of GetKubeConfigFromV1
func (mr *MockInterfaceMockRecorder) GetKubeConfigFromV1(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetKubeConfigFromV1", reflect.TypeOf((*MockInterface)(nil).GetKubeConfigFromV1), arg0, arg1, arg2)
}

// GetV2ClusterFromV1 mocks base method
func (m *MockInterface) GetV2ClusterFromV1(arg0 context.Context, arg1, arg2 string) (*models.Cluster, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetV2ClusterFromV1", arg0, arg1, arg2)
	ret0, _ := ret[0].(*models.Cluster)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetV2ClusterFromV1 indicates an expected call of GetV2ClusterFromV1
func (mr *MockInterfaceMockRecorder) GetV2ClusterFromV1(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetV2ClusterFromV1", reflect.TypeOf((*MockInterface)(nil).GetV2ClusterFromV1), arg0, arg1, arg2)
}

// SetV1ClusterToFreezed mocks base method
func (m *MockInterface) SetV1ClusterToFreezed(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SetV1ClusterToFreezed", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// SetV1ClusterToFreezed indicates an expected call of SetV1ClusterToFreezed
func (mr *MockInterfaceMockRecorder) SetV1ClusterToFreezed(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SetV1ClusterToFreezed", reflect.TypeOf((*MockInterface)(nil).SetV1ClusterToFreezed), arg0, arg1, arg2)
}
