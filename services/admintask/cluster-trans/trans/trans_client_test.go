// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/26 14:26:00, by <EMAIL>, create
*/
/*
DESCRIPTION
实现 trans.Interface, 完成集群迁移
*/

package trans

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/compatible"
	compatiblemock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/compatible/mock"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Test_client_CreateV2ClusterInMetaCluster(t *testing.T) {
	type fields struct {
		clientSet        *clientset.ClientSet
		compatibleClient compatible.Interface
	}
	type args struct {
		ctx     context.Context
		cluster *models.Cluster
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *ccev1.Cluster
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程: 不存在 Cluster 并新建",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				metaclient := metamock.NewMockInterface(ctl)
				compatible := compatiblemock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, "default", "cce-clusterv1", &metav1.GetOptions{}).Return(nil, meta.ErrClusterNotExist.New(ctx, "")),
					metaclient.EXPECT().CreateCluster(ctx, "default", &ccev1.Cluster{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "cce-clusterv1",
							Namespace: "default",
							Finalizers: []string{
								ccev1.ClusterFinalizer,
								string(metav1.DeletePropagationForeground),
							},
						},
						Spec: ccetypes.ClusterSpec{
							ClusterID: "cce-clusterv1",
						},
						Status: ccetypes.ClusterStatus{},
					}).Return(&ccev1.Cluster{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "cce-clusterv1",
							Namespace: "default",
							Finalizers: []string{
								ccev1.ClusterFinalizer,
								string(metav1.DeletePropagationForeground),
							},
						},
						Spec: ccetypes.ClusterSpec{
							ClusterID: "cce-clusterv1",
						},
						Status: ccetypes.ClusterStatus{},
					}, nil),
				)

				return fields{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: metaclient,
						},
					},
					compatibleClient: compatible,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				cluster: &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-clusterv1",
					},
					Status: &ccetypes.ClusterStatus{},
				},
			},
			want: &ccev1.Cluster{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "cce-clusterv1",
					Namespace: "default",
					Finalizers: []string{
						ccev1.ClusterFinalizer,
						string(metav1.DeletePropagationForeground),
					},
				},
				Spec: ccetypes.ClusterSpec{
					ClusterID: "cce-clusterv1",
				},
				Status: ccetypes.ClusterStatus{},
			},
			wantErr: false,
		},
		{
			name: "正常流程: 存在直接返回",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				metaclient := metamock.NewMockInterface(ctl)
				compatible := compatiblemock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, "default", "cce-clusterv1", &metav1.GetOptions{}).Return(&ccev1.Cluster{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "cce-clusterv1",
							Namespace: "default",
							Finalizers: []string{
								ccev1.ClusterFinalizer,
								string(metav1.DeletePropagationForeground),
							},
						},
						Spec: ccetypes.ClusterSpec{
							ClusterID: "cce-clusterv1",
						},
						Status: ccetypes.ClusterStatus{},
					}, nil),
				)

				return fields{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: metaclient,
						},
					},
					compatibleClient: compatible,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				cluster: &models.Cluster{
					Spec: &ccetypes.ClusterSpec{
						ClusterID: "cce-clusterv1",
					},
					Status: &ccetypes.ClusterStatus{},
				},
			},
			want: &ccev1.Cluster{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "cce-clusterv1",
					Namespace: "default",
					Finalizers: []string{
						ccev1.ClusterFinalizer,
						string(metav1.DeletePropagationForeground),
					},
				},
				Spec: ccetypes.ClusterSpec{
					ClusterID: "cce-clusterv1",
				},
				Status: ccetypes.ClusterStatus{},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &client{
				clientSet:        tt.fields.clientSet,
				compatibleClient: tt.fields.compatibleClient,
			}
			got, err := c.CreateV2ClusterInMetaCluster(tt.args.ctx, tt.args.cluster)
			if (err != nil) != tt.wantErr {
				t.Errorf("client.CreateV2ClusterInMetaCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("client.CreateV2ClusterInMetaCluster() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_client_CreateInstanceInMetaCluster(t *testing.T) {
	type fields struct {
		clientSet        *clientset.ClientSet
		compatibleClient compatible.Interface
	}
	type args struct {
		ctx       context.Context
		accountID string
		cluster   *ccev1.Cluster
		instance  *models.Instance
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程: Instance 不存在, 新建",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				metaclient := metamock.NewMockInterface(ctl)
				compatible := compatiblemock.NewMockInterface(ctl)

				BlockOwnerDeletion := true

				gomock.InOrder(
					metaclient.EXPECT().GetInstance(ctx, "default", "cce-clusterv1-instance", &metav1.GetOptions{}).Return(nil, nil),
					metaclient.EXPECT().CreateInstance(ctx, "default", &ccev1.Instance{
						ObjectMeta: metav1.ObjectMeta{
							Name:      "cce-clusterv1-instance",
							Namespace: "default",
							Finalizers: []string{
								ccev1.InstanceFinalizer,
							},
							Labels: map[string]string{
								"cluster-id":   "cce-clusterv1",
								"cluster-role": "node",
							},
							OwnerReferences: []metav1.OwnerReference{
								metav1.OwnerReference{
									APIVersion:         ccev1.ClusterAPIVersion,
									Kind:               ccev1.ClusterKind,
									Name:               "cce-clusterv1",
									UID:                "cce-cluster-uid",
									BlockOwnerDeletion: &BlockOwnerDeletion,
								},
							},
						},
						Spec: ccetypes.InstanceSpec{
							CCEInstanceID: "cce-clusterv1-instance",
							ClusterRole:   ccetypes.ClusterRoleNode,
						},
						Status: ccetypes.InstanceStatus{},
					}).Return(nil, nil),
				)

				return fields{
					clientSet: &clientset.ClientSet{
						Clients: &clientset.Clients{
							MetaClient: metaclient,
						},
					},
					compatibleClient: compatible,
				}
			}(),
			args: args{
				ctx:       context.TODO(),
				accountID: "account-id",
				cluster: &ccev1.Cluster{
					ObjectMeta: metav1.ObjectMeta{
						UID: "cce-cluster-uid",
					},
					Spec: ccetypes.ClusterSpec{
						ClusterID: "cce-clusterv1",
					},
				},
				instance: &models.Instance{
					Spec: &ccetypes.InstanceSpec{
						CCEInstanceID: "cce-clusterv1-instance",
						ClusterRole:   ccetypes.ClusterRoleNode,
					},
					Status: &ccetypes.InstanceStatus{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &client{
				clientSet:        tt.fields.clientSet,
				compatibleClient: tt.fields.compatibleClient,
			}
			if err := c.CreateInstanceInMetaCluster(tt.args.ctx, tt.args.accountID, tt.args.cluster, tt.args.instance); (err != nil) != tt.wantErr {
				t.Errorf("client.CreateInstanceInMetaCluster() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
