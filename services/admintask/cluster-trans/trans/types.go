// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/26 14:26:00, by <EMAIL>, create
*/
/*
DESCRIPTION
将 V1 Cluster/Instance 迁移到 V2 声明式架构
*/

package trans

import (
	"context"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
)

//go:generate mockgen -destination ./mock/mock.go -package mock icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans Interface

// Interface - 定义 v1 迁移 v2 相关方法
type Interface interface {
	SetV1ClusterToFreezed(ctx context.Context, accountID, clusterID string) error
	GetV2ClusterFromV1(ctx context.Context, accountID, clusterID string) (*models.Cluster, error)
	CreateV2ClusterInDB(ctx context.Context, cluster *models.Cluster) error
	CreateV2ClusterInMetaCluster(ctx context.Context, cluster *models.Cluster) (*ccev1.Cluster, error)

	GetCACertFromV1(ctx context.Context, clusterID string) (*models.CaCert, error)
	CreateV2CACertInDB(ctx context.Context, clusterID string, caCert *models.CaCert) error

	GetKubeConfigFromV1(ctx context.Context, accountID, clusterID string) (*models.KubeConfig, error)
	CreateKubeConfigInDB(ctx context.Context, accountID, clusterID string, kubeconfig *models.KubeConfig) error

	GetInstancesFromV1(ctx context.Context, accountID, clusterID string) ([]*models.Instance, error)
	CreateInstanceInDB(ctx context.Context, accountID, clusterID string, instance *models.Instance) error
	CreateInstanceInMetaCluster(ctx context.Context, accountID string, cluster *ccev1.Cluster, instance *models.Instance) error

	FreezeV1Cluster(ctx context.Context, accountID string, clusterID string) error

	CheckV1Hostname(ctx context.Context, accountID string, clusterID string) error
	CheckAccountID(ctx context.Context, accountID string, clusterID string) error
}
