// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/26 14:26:00, by <EMAIL>, create
*/
/*
DESCRIPTION
实现 trans.Interface, 完成集群迁移
*/

package trans

import (
	"context"
	"fmt"
	"strings"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/compatible"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/middleware"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

type client struct {
	clientSet        *clientset.ClientSet
	compatibleClient compatible.Interface
}

// NewClient - 初始化化 client
func NewClient(ctx context.Context, accountID, clusterID string, region ccetypes.Region) (Interface, error) {
	if accountID == "" {
		return nil, fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	if region == "" {
		return nil, fmt.Errorf("region is empty")
	}

	// 不会在私有化环境运行, 使用该方法初始化
	clientset, err := clientset.NewClientSetByRegionDeprecated(ctx, region)
	if err != nil {
		logger.Errorf(ctx, "NewClientSetByRegionDeprecated failed: %s", err)
		return nil, err
	}

	compatibleclient, err := compatible.NewClientByClients(ctx, accountID, clusterID, clientset)
	if err != nil {
		logger.Errorf(ctx, "NewClientByClients failed: %s", err)
		return nil, err
	}

	return &client{
		clientSet:        clientset,
		compatibleClient: compatibleclient,
	}, nil
}

// SetV1ClusterToFreezed - 将 t_cluster 中 Cluster Status 设置为 Freezed, 确保 cce-service 不再处理该集群
func (c *client) SetV1ClusterToFreezed(ctx context.Context, accountID, clusterID string) error {
	return nil
}

func (c *client) GetV2ClusterFromV1(ctx context.Context, accountID, clusterID string) (*models.Cluster, error) {
	if accountID == "" {
		return nil, fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is empty")
	}

	return c.compatibleClient.GetCompleteCluster(ctx, accountID, clusterID)
}

func (c *client) CreateV2ClusterInDB(ctx context.Context, cluster *models.Cluster) error {
	if cluster == nil {
		return fmt.Errorf("cluster is nil")
	}

	accountID := cluster.Spec.AccountID
	clusterID := cluster.Spec.ClusterID

	// 检查是否存在, 保证幂等
	got, err := c.clientSet.Model.GetCluster(ctx, clusterID, accountID)
	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetCluster failed: %s", err)
		return err
	}

	// 不存在则新建
	if models.IsNotExist(err) || got == nil {
		logger.Infof(ctx, "Cluster %s not exist, create one: %s", clusterID, utils.ToJSON(cluster))

		if _, _, _, err := c.clientSet.Model.CreateCluster(ctx, cluster, []*models.Instance{}, []*models.Instance{}, nil); err != nil {
			logger.Errorf(ctx, "CreateCluster failed: %s", err)
			return err
		}
	}

	if got != nil {
		logger.Infof(ctx, "Cluster %s exist, skip create: %s", clusterID, utils.ToJSON(got))
	}

	return nil
}

func (c *client) CreateV2ClusterInMetaCluster(ctx context.Context, cluster *models.Cluster) (*ccev1.Cluster, error) {
	// 将 models.Cluster 转 ccev1.Cluster
	target := &ccev1.Cluster{
		ObjectMeta: metav1.ObjectMeta{
			Name:      cluster.Spec.ClusterID,
			Namespace: "default",
			Finalizers: []string{
				ccev1.ClusterFinalizer,
				string(metav1.DeletePropagationForeground),
			},
		},
		Spec:   *cluster.Spec,
		Status: *cluster.Status,
	}

	// 检查是否存在
	got, err := c.clientSet.MetaClient.GetCluster(ctx, "default", cluster.Spec.ClusterID, &metav1.GetOptions{})
	if err != nil && !meta.IsClusterNotExist(err) {
		logger.Errorf(ctx, "GetCluster failed: %s", err)
		return nil, err
	}

	if err == nil && got != nil {
		logger.Infof(ctx, "Cluster exists in metaCluster")
		return got, err
	}

	// 不存在, 则提交到 meta-cluster
	if meta.IsClusterNotExist(err) || got == nil {
		logger.Infof(ctx, "Cluster not exists in meta, create one: %s", utils.ToJSON(cluster))

		return c.clientSet.MetaClient.CreateCluster(ctx, "default", target)
	}

	return nil, fmt.Errorf("GetCluster %s return (nil, nil)", cluster.Spec.ClusterID)
}

func (c *client) GetCACertFromV1(ctx context.Context, clusterID string) (*models.CaCert, error) {
	return c.compatibleClient.GetCACert(ctx)
}

func (c *client) CreateV2CACertInDB(ctx context.Context, clusterID string, caCert *models.CaCert) error {
	// 从数据库查询, 保证幂等
	got, err := c.clientSet.Model.GetCaCert(ctx, clusterID)
	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetCaCert %s failed: %s", clusterID, err)
		return err
	}

	if models.IsNotExist(err) || got == nil {
		logger.Infof(ctx, "GetCaCert %s not exists, create one: %s", clusterID, utils.ToJSON(caCert))

		return c.clientSet.Model.AddCaCert(ctx, caCert)
	}

	logger.Infof(ctx, "GetCaCert %s exists, skip create one", clusterID)
	return nil
}

func (c *client) GetKubeConfigFromV1(ctx context.Context, accountID, clusterID string) (*models.KubeConfig, error) {
	// 存入 kubeconfig_info 表中默认为 vpc ip
	return c.compatibleClient.GetKubeConfig(ctx, clusterID, &middleware.UserInfo{
		AccountID: accountID,
		UserID:    accountID,
	}, models.KubeConfigTypeVPC)
}

func (c *client) CreateKubeConfigInDB(ctx context.Context, accountID, clusterID string, kubeconfig *models.KubeConfig) error {
	// 检查 KubeConfig 是否存在
	got, err := c.clientSet.Model.GetKubeConfig(ctx, clusterID, accountID, models.KubeConfigTypeInternal)
	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetKubeConfig failed: %s", err)
		return err
	}

	if models.IsNotExist(err) || got == nil {
		logger.Infof(ctx, "GetKubeConfig userID=%s clusterID=%s not exists, create one", accountID, clusterID)

		return c.clientSet.Model.AddKubeConfig(ctx, kubeconfig)
	}

	logger.Infof(ctx, "GetKubeConfig userID=%s clusterID=%s exists, skip create one", accountID, clusterID)

	return nil
}

func (c *client) GetInstancesFromV1(ctx context.Context, accountID, clusterID string) ([]*models.Instance, error) {
	return c.compatibleClient.GetInstances(ctx, accountID, clusterID)
}

func (c *client) CreateInstanceInDB(ctx context.Context, accountID, clusterID string, instance *models.Instance) error {
	if instance == nil {
		return fmt.Errorf("instance is nil")
	}

	cceInstanceID := instance.Spec.CCEInstanceID

	// 检查 Instance 是否存在
	got, err := c.clientSet.Model.GetInstanceByCCEID(ctx, cceInstanceID, accountID)
	if err != nil && !models.IsNotExist(err) {
		logger.Errorf(ctx, "GetInstanceByCCEID failed: %s", err)
		return err
	}

	if models.IsNotExist(err) || got == nil {
		logger.Infof(ctx, "CCEInstanceID %s not exists in t_cce_instance, create one", cceInstanceID)

		_, err := c.clientSet.Model.CreateInstances(ctx, clusterID, []*models.Instance{
			instance,
		})

		return err
	}

	logger.Infof(ctx, "CCEInstanceID %s exists in t_cce_instance, skip create one", cceInstanceID)

	return nil
}

func (c *client) CreateInstanceInMetaCluster(ctx context.Context, accountID string, cluster *ccev1.Cluster, instance *models.Instance) error {
	if cluster == nil {
		return fmt.Errorf("cluster is nil")
	}

	if instance == nil {
		return fmt.Errorf("instance is nil")
	}

	clusterID := cluster.Spec.ClusterID
	cceInstanceID := instance.Spec.CCEInstanceID

	// name: cce-19o5a1tj-v5knghb1
	// namespace: default
	// finalizers:
	// - finalizer.k8s-instance.cce.baidubce.com
	// labels:
	//   cluster-id: cce-19o5a1tj
	//   cluster-role: node
	// ownerReferences:
	// - apiVersion: cce.baidubce.com/v1
	//   blockOwnerDeletion: true
	//   kind: Cluster
	//   name: cce-19o5a1tj
	//   uid: 63b677f5-e6cc-11ea-abc2-6c92bfb5650a
	BlockOwnerDeletion := true
	target := &ccev1.Instance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      cceInstanceID,
			Namespace: "default",
			Finalizers: []string{
				ccev1.InstanceFinalizer,
			},
			Labels: map[string]string{
				"cluster-id":   clusterID,
				"cluster-role": string(instance.Spec.ClusterRole),
			},
			OwnerReferences: []metav1.OwnerReference{
				metav1.OwnerReference{
					APIVersion:         ccev1.ClusterAPIVersion,
					Kind:               ccev1.ClusterKind,
					Name:               clusterID,
					UID:                cluster.ObjectMeta.UID,
					BlockOwnerDeletion: &BlockOwnerDeletion,
				},
			},
		},
		Spec:   *instance.Spec,
		Status: *instance.Status,
	}

	// 检查 Instance 是否存在
	got, err := c.clientSet.MetaClient.GetInstance(ctx, "default", cceInstanceID, &metav1.GetOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Errorf(ctx, "GetInstance failed: %s", err)
		return err
	}

	if kerrors.IsNotFound(err) || got == nil {
		logger.Infof(ctx, "Instance not exists in metacluster, create one: %s", utils.ToJSON(target))

		if _, err := c.clientSet.MetaClient.CreateInstance(ctx, "default", target); err != nil {
			logger.Errorf(ctx, "CreateInstance failed: %s", err)
			return err
		}
	}

	logger.Infof(ctx, "CCEInstanceID %s exists in t_cce_instance, skip create one", cceInstanceID)

	return nil
}

// FreezeV1Cluster - 将 Cluster/Instance 设置为 DELETED
func (c *client) FreezeV1Cluster(ctx context.Context, accountID string, clusterID string) error {
	if accountID == "" {
		return fmt.Errorf("accountID is empty")
	}

	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	// 将 t_instance 设置为 DELETED
	instances, err := c.clientSet.Model.GetServiceInstances(ctx, accountID, clusterID)
	if err != nil {
		logger.Errorf(ctx, "GetServiceInstances failed: %s", err)
		return err
	}

	logger.Infof(ctx, "V1 Cluster Instances: %s", utils.ToJSON(instances))

	for _, instance := range instances {
		instanceID := instance.InstanceID

		logger.Infof(ctx, "Delete instance %s begin", instanceID)

		if err := c.clientSet.Model.DeleteServiceInstance(ctx, accountID, clusterID, instanceID); err != nil {
			logger.Errorf(ctx, "DeleteServiceInstance %s failed: %s", instanceID, err)
			return err
		}
	}

	// 将 t_cluster 设置为 DELETED
	if err := c.clientSet.Model.DeleteServiceCluster(ctx, accountID, clusterID); err != nil {
		logger.Errorf(ctx, "DeleteServiceCluster %s failed: %s", clusterID, err)
		return err
	}

	// 将 cluster_info 设置为 is_deleted=1
	if err := c.clientSet.Model.DeleteCaaSCluster(ctx, accountID, clusterID); err != nil {
		logger.Errorf(ctx, "DeleteCaaSCluster %s failed: %s", clusterID, err)
		return err
	}

	return nil
}

// CheckV1Hostname - 检查 K8S Nodename 和数据库 Hostname 是否一致
func (c *client) CheckV1Hostname(ctx context.Context, accountID string, clusterID string) error {
	// 获取 kubeconfig
	kubeconfig, err := c.clientSet.Model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		logger.Errorf(ctx, "GetKubeConfigFromV1 failed: %s", err)
		return err
	}

	// 初始化 K8S Client
	k8sclient, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "NewK8SClient failed: %s", err)
		return err
	}

	// List Nodes
	nodeList, err := k8sclient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "k8sclient ListNodes failed: %s", err)
		return err
	}

	// 检查 Node Name 和数据库 hostname 是否一致
	diff := []string{}
	for _, node := range nodeList.Items {
		nodeName := node.GetName()
		providerID := node.Spec.ProviderID
		instanceID := strings.TrimLeft(providerID, "cce://")

		// 查询数据库获取对应 Instance
		instance, err := c.clientSet.Model.GetServiceInstance(ctx, accountID, instanceID)
		if err != nil {
			logger.Errorf(ctx, "GetServceInstance %s failed: %s", nodeName, err)
			return err
		}

		logger.Infof(ctx, "Instance %s: K8SNodeName=%s DBHostName=%s", instanceID, nodeName, instance.HostName)

		// 检查 HostName
		if instance.HostName != nodeName {
			diff = append(diff, fmt.Sprintf("Instance %s: K8SNodeName=%s DBHostName=%s", instanceID, nodeName, instance.HostName))
		}
	}

	if len(diff) > 0 {
		logger.Errorf(ctx, "CheckV1Hostname failed: %s", utils.ToJSON(diff))
		return fmt.Errorf("CheckV1Hostname failed: %s", utils.ToJSON(diff))
	}

	return nil
}

// CheckAccountID - 检查 t_cluster, t_instance, cluster_info 中 AccountID
func (c *client) CheckAccountID(ctx context.Context, accountID string, clusterID string) error {
	return nil
}
