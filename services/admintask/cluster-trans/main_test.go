// Copyright 2020 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/08/26 13:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
将 V1 Cluster/Instance 迁移到 V2 声明式架构
*/

package main

import (
	"context"
	"reflect"
	"testing"

	"github.com/golang/mock/gomock"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/configuration"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans"
	transmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/cluster-trans/trans/mock"
)

func Test_v2ClusterID(t *testing.T) {
	type args struct {
		ctx       context.Context
		clusterID string
	}
	tests := []struct {
		name string
		args args
		want string
	}{
		// TODO: Add test cases.
		{
			name: "c-ChenHuan",
			args: args{
				ctx:       context.TODO(),
				clusterID: "c-ChenHuan",
			},
			want: "cce-chenhuanv1",
		},
		{
			name: "cce-ChenHuan",
			args: args{
				ctx:       context.TODO(),
				clusterID: "c-ChenHuan",
			},
			want: "cce-chenhuanv1",
		},
		{
			name: "cce-chenhuan",
			args: args{
				ctx:       context.TODO(),
				clusterID: "c-chenhuan",
			},
			want: "cce-chenhuanv1",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := v2ClusterID(tt.args.ctx, tt.args.clusterID); got != tt.want {
				t.Errorf("v2ClusterID() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_transCluster(t *testing.T) {
	type args struct {
		ctx       context.Context
		client    trans.Interface
		accountID string
		clusterID string
		config    configuration.Config
	}
	tests := []struct {
		name    string
		args    args
		want    *ccev1.Cluster
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: func() args {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				transClient := transmock.NewMockInterface(ctl)

				gomock.InOrder(
					// transClient.EXPECT().SetV1ClusterToFreezed(ctx, "account-id", "c-cluster").Return(nil),
					transClient.EXPECT().GetV2ClusterFromV1(ctx, "account-id", "c-cluster").Return(&models.Cluster{
						Spec:   &ccetypes.ClusterSpec{},
						Status: &ccetypes.ClusterStatus{},
					}, nil),
					transClient.EXPECT().CreateV2ClusterInDB(ctx, &models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-clusterv1",
							Handler:   "handler-v1-to-v2",
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								LBServiceVPCSubnetID: "lb-service-vpc-subnet-id",
							},
							RuntimeVersion: ccetypes.DockerVersion20,
							K8SCustomConfig: ccetypes.K8SCustomConfig{
								EnableHostname: true,
							},
						},
						Status: &ccetypes.ClusterStatus{
							ClusterBLB: ccetypes.BLB{
								ID: "cluster-blb-id",
							},
							ClusterPhase:           ccetypes.ClusterPhaseRunning,
							InfrastructureReady:    true,
							APIServerAccessSuccess: true,
							K8SPluginDeploySuccess: true,
						},
					}).Return(nil),
					transClient.EXPECT().CreateV2ClusterInMetaCluster(ctx, &models.Cluster{
						Spec: &ccetypes.ClusterSpec{
							ClusterID: "cce-clusterv1",
							Handler:   "handler-v1-to-v2",
							ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
								LBServiceVPCSubnetID: "lb-service-vpc-subnet-id",
							},
							RuntimeVersion: ccetypes.DockerVersion20,
							K8SCustomConfig: ccetypes.K8SCustomConfig{
								EnableHostname: true,
							},
						},
						Status: &ccetypes.ClusterStatus{
							ClusterBLB: ccetypes.BLB{
								ID: "cluster-blb-id",
							},
							ClusterPhase:           ccetypes.ClusterPhaseRunning,
							InfrastructureReady:    true,
							APIServerAccessSuccess: true,
							K8SPluginDeploySuccess: true,
						},
					}).Return(nil, nil),
				)

				return args{
					ctx:       ctx,
					client:    transClient,
					accountID: "account-id",
					clusterID: "c-cluster",
					config: configuration.Config{
						EnableHostname:       true,
						RuntimeVersion:       ccetypes.DockerVersion20,
						LBServiceVPCSubnetID: "lb-service-vpc-subnet-id",
						ClusterBLBID:         "cluster-blb-id",
					},
				}
			}(),
			want:    nil,
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := transCluster(tt.args.ctx, tt.args.client, tt.args.accountID, tt.args.clusterID, tt.args.config)
			if (err != nil) != tt.wantErr {
				t.Errorf("transCluster() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("transCluster() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_transCACert(t *testing.T) {
	type args struct {
		ctx       context.Context
		client    trans.Interface
		accountID string
		clusterID string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: func() args {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				transClient := transmock.NewMockInterface(ctl)

				gomock.InOrder(
					transClient.EXPECT().GetCACertFromV1(ctx, "c-cluster").Return(&models.CaCert{}, nil),
					transClient.EXPECT().CreateV2CACertInDB(ctx, "cce-clusterv1", &models.CaCert{
						ClusterID: "cce-clusterv1",
					}).Return(nil),
				)

				return args{
					ctx:       ctx,
					client:    transClient,
					accountID: "account-id",
					clusterID: "c-cluster",
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := transCACert(tt.args.ctx, tt.args.client, tt.args.accountID, tt.args.clusterID); (err != nil) != tt.wantErr {
				t.Errorf("transCACert() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_transKubeConfig(t *testing.T) {
	type args struct {
		ctx       context.Context
		client    trans.Interface
		accountID string
		clusterID string
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: func() args {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				transClient := transmock.NewMockInterface(ctl)

				gomock.InOrder(
					transClient.EXPECT().GetKubeConfigFromV1(ctx, "account-id", "c-cluster").Return(&models.KubeConfig{}, nil),
					transClient.EXPECT().CreateKubeConfigInDB(ctx, "account-id", "cce-clusterv1", &models.KubeConfig{
						ClusterID: "cce-clusterv1",
					}).Return(nil),
				)

				return args{
					ctx:       ctx,
					client:    transClient,
					accountID: "account-id",
					clusterID: "c-cluster",
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := transKubeConfig(tt.args.ctx, tt.args.client, tt.args.accountID, tt.args.clusterID); (err != nil) != tt.wantErr {
				t.Errorf("transKubeConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_transInstances(t *testing.T) {
	type args struct {
		ctx          context.Context
		client       trans.Interface
		accountID    string
		oldClusterID string
		cluster      *ccev1.Cluster
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: func() args {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				transClient := transmock.NewMockInterface(ctl)

				gomock.InOrder(
					transClient.EXPECT().GetInstancesFromV1(ctx, "account-id", "c-cluster").Return([]*models.Instance{
						&models.Instance{
							Spec: &ccetypes.InstanceSpec{
								CCEInstanceID: "cce-clusterv1-instance",
							},
							Status: &ccetypes.InstanceStatus{
								Machine: ccetypes.Machine{
									InstanceID: "i-instance",
								},
							},
						},
					}, nil),
					transClient.EXPECT().CreateInstanceInDB(ctx, "account-id", "cce-clusterv1", &models.Instance{
						Spec: &ccetypes.InstanceSpec{
							ClusterID:     "cce-clusterv1",
							CCEInstanceID: "cce-clusterv1-instance",
						},
						Status: &ccetypes.InstanceStatus{
							Machine: ccetypes.Machine{
								InstanceID: "i-instance",
							},
							InstancePhase:       ccetypes.InstancePhaseRunning,
							InfrastructureReady: true,
							ReinstallOSAlready:  true,
							DeploySuccess:       true,
						},
					}).Return(nil),
					transClient.EXPECT().CreateInstanceInMetaCluster(ctx, "account-id", &ccev1.Cluster{}, &models.Instance{
						Spec: &ccetypes.InstanceSpec{
							ClusterID:     "cce-clusterv1",
							CCEInstanceID: "cce-clusterv1-instance",
						},
						Status: &ccetypes.InstanceStatus{
							Machine: ccetypes.Machine{
								InstanceID: "i-instance",
							},
							InstancePhase:       ccetypes.InstancePhaseRunning,
							InfrastructureReady: true,
							ReinstallOSAlready:  true,
							DeploySuccess:       true,
						},
					}).Return(nil),
				)

				return args{
					ctx:          ctx,
					client:       transClient,
					accountID:    "account-id",
					oldClusterID: "c-cluster",
					cluster:      &ccev1.Cluster{},
				}
			}(),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := transInstances(tt.args.ctx, tt.args.client, tt.args.accountID, tt.args.oldClusterID, tt.args.cluster); (err != nil) != tt.wantErr {
				t.Errorf("transInstances() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
