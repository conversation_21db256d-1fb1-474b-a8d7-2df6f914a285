// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/07/11 10:14:00, by <EMAIL>, create
*/
/*
修复 t_cce_instance 中 CPU/MEM 为 0 的数据。
*/

package main

import (
	"context"
	"fmt"
	"math"
	"os"
	"strings"

	"github.com/jinzhu/gorm"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

var (
	// GZTEST
	// databaseEndpoint = "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true"

	// BJ
	// databaseEndpoint = "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true"

	// 苏州
	// databaseEndpoint = "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true"

	// HKG
	// databaseEndpoint = "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.11.48.247:5596)/hkg_cce_service?charset=utf8&parseTime=true"

	mysqlEndpoins = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.11.48.247:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(10.70.16.33:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}
)

func main() {
	ctx := context.TODO()

	if err := Run(ctx); err != nil {
		logger.Errorf(ctx, "run failed: %s", err)
		os.Exit(-1)
	}
}

// Run -
func Run(ctx context.Context) error {
	// 获取 Region 信息
	region := os.Getenv("region")
	if region == "" {
		region = "gztest"
	}
	databaseEndpoint := mysqlEndpoins[region]

	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint)
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	db, err := model.DB(ctx)
	if err != nil {
		logger.Errorf(ctx, "model.DB failed: %s", err)
		return err
	}

	// 获取所有 CPU/MEM 为 0 的 Running Instance
	instances, err := getAllZeroCPUMEMInstances(ctx, db)
	if err != nil {
		logger.Errorf(ctx, "getAllZeroCPUMEMInstances failed: %s", err)
		return err
	}

	logger.Infof(ctx, "Zero CPU/MEM Instances: %d", len(instances))

	if instances == nil || len(instances) == 0 {
		return nil
	}

	// 按 Cluster 分类
	clusterInstances := map[string][]*models.Instance{}
	for _, instance := range instances {
		clusterID := instance.Spec.ClusterID
		if !strings.Contains(clusterID, "cce-") {
			logger.Warnf(ctx, "clusterID=%s, skip", clusterID)
			continue
		}

		logger.Infof(ctx, "ClusterID=%s CCEInstanceID=%s InstanceID=%s",
			clusterID, instance.Spec.CCEInstanceID, instance.Status.Machine.InstanceID)

		if _, ok := clusterInstances[clusterID]; !ok {
			clusterInstances[clusterID] = []*models.Instance{}
		}

		clusterInstances[clusterID] = append(clusterInstances[clusterID], instance)
	}

	logger.Infof(ctx, "Zero CPU/MEM Clusters: %d", len(clusterInstances))

	// 按 Cluster 重新获取 CPU/MEM
	for clusterID, instances := range clusterInstances {
		// 获取 Cluster
		cluster, err := model.GetClusterByClusterID(ctx, clusterID)
		if err != nil {
			logger.Errorf(ctx, "GetClusterByClusterID failed: %s", err)
			return err
		}

		// 获取 Kubeconfig
		kubeconfig, err := model.GetAdminKubeConfig(ctx, clusterID, models.KubeConfigTypeInternal)
		if err != nil {
			logger.Errorf(ctx, "GetAdminKubeConfig failed: %s", err)
			return err
		}

		// 初始化 Client
		client, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
		if err != nil {
			logger.Errorf(ctx, "NewK8SClient failed: %s", err)
			return err
		}

		for _, instance := range instances {
			if instance.Spec.ClusterRole != ccetypes.ClusterRoleNode {
				logger.Infof(ctx, "Instance %s cluster_role %s != %s, skip",
					instance.Spec.CCEInstanceID, instance.Spec.ClusterRole, ccetypes.ClusterRoleNode)
				continue
			}

			// 获取 NodeName
			nodeName := utils.GetNodeName(ctx,
				instance.Status.Machine.VPCIP,
				instance.Status.Machine.Hostname,
				instance.Spec.InstanceName, cluster.Spec.K8SCustomConfig.EnableHostname)

			// 获取 K8S Node
			node, err := client.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "NewK8SClient clusterID=%s cceInstanceID=%s failed, skip: %s",
					clusterID, instance.Spec.CCEInstanceID, err)
				continue
			}

			logger.Infof(ctx, "Instance %s associated node: %s", instance.Spec.CCEInstanceID, utils.ToJSON(node))

			// 获取 CPU/MEM
			cpu, mem, err := getCPUMEMFromNode(ctx, node)
			if err != nil {
				logger.Errorf(ctx, "getCPUMEMFromNode failed: %s", err)
				return err
			}

			logger.Infof(ctx, "Instance %s CPU=%d MEM=%d", instance.Spec.CCEInstanceID, cpu, mem)

			// 更新数据库
			if err := updateZeroCPUMEMInstance(ctx, db, instance, cpu, mem); err != nil {
				logger.Errorf(ctx, "updateZeroCPUMEMInstance failed: %s", err)
				continue
			}

			logger.Infof(ctx, "Update Instance %s CPU=%d MEM=%d succeed", instance.Spec.CCEInstanceID, cpu, mem)
		}
	}

	return nil
}

func getAllZeroCPUMEMInstances(ctx context.Context, db *gorm.DB) ([]*models.Instance, error) {
	instances := []*models.Instance{}

	if err := db.Unscoped().Table("t_cce_instance").
		Where("instance_phase = ? AND cluster_role = ? AND cpu = 0", string(ccetypes.InstancePhaseRunning), ccetypes.ClusterRoleNode).
		Find(&instances).Error; err != nil {

		logger.Errorf(ctx, "select instances failed: %v", err)
		return nil, err
	}

	if len(instances) == 0 {
		logger.Errorf(ctx, "this db does not have any non-deleted instances")
		return nil, nil
	}

	return instances, nil
}

func updateZeroCPUMEMInstance(ctx context.Context, db *gorm.DB, instance *models.Instance, cpu, mem int) error {
	if cpu <= 0 {
		return fmt.Errorf("cpu=%d not valid", cpu)
	}

	if mem <= 0 {
		return fmt.Errorf("mem=%d not valid", mem)
	}

	clusterID := instance.Spec.ClusterID
	cceInstanceID := instance.Spec.CCEInstanceID
	accountID := instance.Spec.AccountID

	// 重新检查防止出错
	instances := []*models.Instance{}

	if err := db.Unscoped().Table("t_cce_instance").
		Where("cluster_id = ? AND cce_instance_id = ? AND account_id = ?",
			clusterID, cceInstanceID, accountID).
		Find(&instances).Error; err != nil {

		logger.Errorf(ctx, "select instances failed: %v", err)
		return err
	}

	if len(instances) != 1 {
		logger.Errorf(ctx, "Multi instance exist: cluster_id = %s AND cce_instance_id = %s AND account_id = %s", clusterID, cceInstanceID, accountID)
		return fmt.Errorf("Multi instance exist: cluster_id = %s AND cce_instance_id = %s AND account_id = %s", clusterID, cceInstanceID, accountID)
	}

	// 更新字段
	updateMap := map[string]interface{}{
		"cpu": cpu,
		"mem": mem,
	}

	// 更新 Instance
	if err := db.Unscoped().Table("t_cce_instance").Where("cluster_id = ? AND cce_instance_id = ? AND account_id = ?",
		clusterID, cceInstanceID, accountID).
		Update(updateMap).Error; err != nil {
		logger.Errorf(ctx, "Update instance %s cpu/mem failed: %v", cceInstanceID, err)
		return err
	}

	return nil
}

func getCPUMEMFromNode(ctx context.Context, node *corev1.Node) (int, int, error) {
	capacity := node.Status.Capacity

	cpu, _ := capacity.Cpu().AsInt64()
	mem, _ := capacity.Memory().AsInt64()

	logger.Infof(ctx, "capaciy.Memory: %d", mem)

	memInGB := math.Ceil(float64(mem) / 1024.0 / 1024.0 / 1024.0)

	return int(cpu), int(memInGB), nil
}
