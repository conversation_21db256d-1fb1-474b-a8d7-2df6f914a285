package main

import (
	"context"
	"encoding/json"
	"errors"
	"flag"
	"fmt"
	"regexp"
	"strings"
	"time"

	v1 "k8s.io/api/core/v1"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/version"
	utilyaml "k8s.io/apimachinery/pkg/util/yaml"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
)

type Config struct {
	Region    string
	UserID    string
	ClusterID string
	Image     string
	DryRun    bool
}

type Summary struct {
	SkippedClusterNBR int
	FailedClusterNBR  int
	SucceedClusterNBR int
}

type cniConfiguration struct {
	Kubeconfig string `json:"kubeconfig"`
	CNIMode    string `json:"cniMode"`
	Workers    int    `json:"workers"`
}

const (
	nameSpaceKubeSystem = "kube-system"
	daemonsetName       = "cce-cni-node-agent"
	configMapName       = "cce-cni-node-agent"
	labelSelector       = "name=cce-cni-node-agent"
	cniImageName        = "registry.baidubce.com/cce-plugin-pro/cce-cni:v1.9.1"
)

var (
	config  = Config{}
	summary = Summary{}

	databaseEndpoint map[string]string = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(************:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(***********:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}

	cniImageNamePattern = regexp.MustCompile(`registry.baidubce.com/cce-plugin-(pro|dev)/cce-cni:v(\d.\d.\d)(-rc\d)?`)
	cniMinimalVersion   = version.MustParseGeneric("v1.3.5")
)

func parseFlags() {
	flag.StringVar(&config.Region, "r", "bj", "region")
	flag.StringVar(&config.UserID, "u", "", "user id")
	flag.StringVar(&config.ClusterID, "c", "", "cluster id")
	flag.StringVar(&config.Image, "i", "", "image")
	flag.BoolVar(&config.DryRun, "dry-run", true, "")

	flag.Parse()
}

func filterCluster(ctx context.Context, model *models.Client, clusterIDs []string) []string {
	var (
		result []string
	)

	for _, clusterID := range clusterIDs {
		// 集群 id
		if config.ClusterID != "" && config.ClusterID == clusterID {
			result = append(result, clusterID)
		}

		if config.ClusterID == "" {
			result = append(result, clusterID)
		}
	}

	return result
}

func main() {
	var (
		ctx            context.Context = context.TODO()
		clusterIDs     []string
		targetClusters []string
		err            error
	)

	// 解析参数
	parseFlags()

	if _, ok := databaseEndpoint[config.Region]; !ok {
		logger.Errorf(ctx, "Region %v is not valid", config.Region)
		return
	}

	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint[config.Region])
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return
	}

	// 获取所有待定集群
	if config.UserID != "" {
		// 根据 account id 获取地域所有集群
		clusters, err := model.GetClusterList(ctx, config.UserID)
		if err != nil {
			logger.Errorf(ctx, "GetClusterList failed: %s", err)
			return
		}

		for _, c := range clusters {
			clusterIDs = append(clusterIDs, c.Spec.ClusterID)
		}
	} else {
		// 获取地域所有集群, 包含 V1 和 V2
		clusterIDs, err = model.GetAllRunningClusterIDsCompatibility(ctx)
		if err != nil {
			logger.Errorf(ctx, "GetAllRunningClusterIDsCompatibility failed: %s", err)
			return
		}
	}

	logger.Infof(ctx, "All ClusterIDs: %s", utils.ToJSON(clusterIDs))

	// 条件过滤集群
	targetClusters = filterCluster(ctx, model, clusterIDs)
	logger.Infof(ctx, "Target ClusterIDs: %s", utils.ToJSON(targetClusters))

	// 开始执行升级
	updateTargetClusters(model, targetClusters)

	logger.Infof(ctx, "Summary: %+v", summary)
}

func updateTargetClusters(model *models.Client, targetClusters []string) {
	// 挨个升级集群
	for _, clusterID := range targetClusters {
		var (
			ctx = context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())
		)

		logger.Infof(ctx, "====> Updating Route Controller for Cluster %s Begin <====", clusterID)

		err := updateOneCluster(ctx, model, clusterID)
		if err != nil {
			logger.Errorf(ctx, "update cluster %v failed: %v", clusterID, err)
		}

		logger.Infof(ctx, "====> Updating Route Controller for Cluster %s Ends <====", clusterID)

		logger.Infof(ctx, "\n\n")
	}
}

func isNotExistError(err error) bool {
	return strings.Contains(err.Error(), "not exist in")
}

func needUpgrade(ctx context.Context, kubeClient kubernetes.Interface) (bool, error) {
	var (
		cniConf             cniConfiguration
		containers          []v1.Container
		agentContainerImage string
	)

	// 获取 daemonset cce-cni-node-agent
	ds, err := kubeClient.AppsV1().DaemonSets(nameSpaceKubeSystem).Get(ctx, daemonsetName, metav1.GetOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Errorf(ctx, "GetDaemonSets kube-system/%v failed: %s", daemonsetName, err)
		return false, err
	}

	if kerrors.IsNotFound(err) {
		logger.Infof(ctx, "Daemonset %v not found, skip upgrade", daemonsetName)
		return false, nil
	}

	// 获取 configmap
	cm, err := kubeClient.CoreV1().ConfigMaps(nameSpaceKubeSystem).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Errorf(ctx, "GetConfigMaps kube-system/%v failed: %s", configMapName, err)
		return false, err
	}

	logger.Infof(ctx, "data: %+v", cm.Data)

	// 检查配置合法性
	cniConfigStr := cm.Data["config"]
	jsonStr, err := utilyaml.ToJSON([]byte(cniConfigStr))
	if err != nil {
		logger.Errorf(ctx, "ToJSON kube-system/%v failed: %s", configMapName, err)
		return false, err
	}
	err = json.Unmarshal([]byte(jsonStr), &cniConf)
	if err != nil {
		logger.Errorf(ctx, "Unmarshal kube-system/%v failed: %s", configMapName, err)
		return false, err
	}

	// 检查网络模式
	if !(cniConf.CNIMode == "kubenet" || strings.HasPrefix(cniConf.CNIMode, "vpc-route-")) {
		logger.Errorf(ctx, "cni mode %v is not correct", cniConf.CNIMode)
		return false, nil
	}

	containers = ds.Spec.Template.Spec.Containers

	// 检查容器数量
	if len(containers) != 1 {
		msg := fmt.Sprintf("DaemonSet kube-system/%s has more than one containers", daemonsetName)
		logger.Errorf(ctx, msg)
		return false, errors.New(msg)
	}

	// 检查镜像名字
	agentContainerImage = containers[0].Image
	matches := cniImageNamePattern.FindStringSubmatch(agentContainerImage)
	if len(matches) != 4 {
		msg := fmt.Sprintf("container image %v is not valid", agentContainerImage)
		logger.Errorf(ctx, msg)
		return false, errors.New(msg)
	}

	logger.Infof(ctx, "mode: %v, image: %v", cniConf.CNIMode, agentContainerImage)

	// 检查版本
	currentVersion, err := version.ParseGeneric(matches[2])
	if err != nil {
		msg := fmt.Sprintf("cni container image version %v is not valid", matches[2])
		logger.Errorf(ctx, msg)
		return false, errors.New(msg)
	}

	return currentVersion.LessThan(cniMinimalVersion), nil
}

// TODO:
func execute(ctx context.Context, kubeClient kubernetes.Interface) error {
	// 获取 daemonset cce-cni-node-agent
	ds, err := kubeClient.AppsV1().DaemonSets(nameSpaceKubeSystem).Get(ctx, daemonsetName, metav1.GetOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Errorf(ctx, "GetDaemonSets kube-system/%v failed: %s", daemonsetName, err)
		return err
	}

	containers := ds.Spec.Template.Spec.Containers

	// 检查容器数量
	if len(containers) != 1 {
		msg := fmt.Sprintf("DaemonSet kube-system/%s has more than one containers", daemonsetName)
		logger.Errorf(ctx, msg)
		return errors.New(msg)
	}

	if config.Image == "" {
		config.Image = cniImageName
	}
	ds.Spec.Template.Spec.Containers[0].Image = config.Image

	_, err = kubeClient.AppsV1().DaemonSets(nameSpaceKubeSystem).Update(ctx, ds, metav1.UpdateOptions{})
	if err != nil {
		msg := fmt.Sprintf("DaemonSet kube-system/%s updated error: %v", daemonsetName, err)
		logger.Errorf(ctx, msg)
		return errors.New(msg)
	}

	_ = kubeClient.CoreV1().Pods(nameSpaceKubeSystem).DeleteCollection(ctx, *metav1.NewDeleteOptions(0), metav1.ListOptions{
		LabelSelector: labelSelector,
	})

	return nil
}

func checkStatusAfterExecute(ctx context.Context, kubeClient kubernetes.Interface) error {
	// 获取 cni 所有 Pod
	podList, err := kubeClient.CoreV1().Pods(nameSpaceKubeSystem).List(ctx, metav1.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		logger.Errorf(ctx, "Get kube-system/cce-cni-node-agent pods failed: %s", err)
		return err
	}

	if len(podList.Items) == 0 {
		return fmt.Errorf("pods of cce-cni-node-agent is 0")
	}

	for _, pod := range podList.Items {
		// 检查 Pod 是否 Running
		ready, err := tasks.CheckPodReady(ctx, &pod)
		if err != nil {
			logger.Errorf(ctx, "CheckPodReady failed: %s", err)
			return err
		}

		if !ready {
			logger.Warnf(ctx, "Pod %v not ready: %s", pod.Name, err)
		}
	}

	// run probe pod
	err = runProbe(ctx, kubeClient)
	if err != nil {
		logger.Errorf(ctx, "runProbe failed: %s", err)
		return err
	}

	return nil
}

func updateOneCluster(ctx context.Context, model *models.Client, clusterID string) error {
	var (
		err error
	)

	defer func() {
		if err != nil {
			summary.FailedClusterNBR++
		} else {
			summary.SucceedClusterNBR++
		}
	}()

	// 获取集群 kubeconfig
	kubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		// 可能存在 kubeconfig_info 中存在, 但是 t_cluster 不存在情况
		if isNotExistError(err) {
			logger.Infof(ctx, "Cluster %s not exist, skip update", clusterID)
			summary.SkippedClusterNBR++
			return nil
		}

		logger.Errorf(ctx, "GetAdminKubeConfigCompatibility failed: %s", err)
		return err
	}

	// 构建 K8S Client
	client, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "NewK8SClient failed: %s", err)
		return err
	}

	// 判断是否升级
	if ok, err := needUpgrade(ctx, client); err != nil || !ok {
		logger.Infof(ctx, "skipped upgrading cluster %v due to incompatible cni version", clusterID)
		summary.SkippedClusterNBR++
		return err
	}

	logger.Infof(ctx, "The center has decided, you have to be upgraded. %v", clusterID)

	// 升级 daemonset
	if !config.DryRun {
		err = execute(ctx, client)
		if err != nil {
			logger.Errorf(ctx, "execute failed: %v", err)
			return err
		} else {
			logger.Infof(ctx, "execute done: %v", clusterID)
		}
	}

	// 等待就绪
	if !config.DryRun {
		wait(ctx, client)
		err = checkStatusAfterExecute(ctx, client)
		if err != nil {
			logger.Errorf(ctx, "checkStatusAfterExecute %v failed: %v", clusterID, err)
			return err
		} else {
			logger.Infof(ctx, "checkStatusAfterExecute done: %v", clusterID)
		}
	}

	return nil
}

func wait(ctx context.Context, kubeClient kubernetes.Interface) {
	var (
		nodeNum   = 10
		sleepTime time.Duration
	)
	nodeList, err := kubeClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err == nil {
		nodeNum = len(nodeList.Items)
	}

	sleepTime = time.Second*20 + time.Duration(nodeNum)*time.Second
	logger.Infof(ctx, "sleep %v", sleepTime)
	time.Sleep(sleepTime)
}

func runProbe(ctx context.Context, kubeClient kubernetes.Interface) error {
	var (
		probePodName                        = "cce-busybox-test"
		terminationGracePeriodSeconds int64 = 0

		busybox = &v1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      probePodName,
				Namespace: nameSpaceKubeSystem,
			},
			Spec: v1.PodSpec{
				Containers: []v1.Container{
					{
						Name:  "busybox",
						Image: cniImageName,
						Command: []string{
							"sleep",
							"36000",
						},
						ImagePullPolicy: v1.PullIfNotPresent,
					},
				},
				RestartPolicy:                 v1.RestartPolicyAlways,
				TerminationGracePeriodSeconds: &terminationGracePeriodSeconds,
				HostNetwork:                   false,
			},
		}
	)

	// create pod
	_, err := kubeClient.CoreV1().Pods(nameSpaceKubeSystem).Create(ctx, busybox, metav1.CreateOptions{})
	if err != nil {
		logger.Errorf(ctx, "failed to create probe pod: %v", err)
		return err
	}

	// wait ready
	time.Sleep(10 * time.Second)

	// check pod
	pod, err := kubeClient.CoreV1().Pods(nameSpaceKubeSystem).Get(ctx, probePodName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "failed to get probe pod: %v", err)
		return err
	}

	ready, err := tasks.CheckPodReady(ctx, pod)
	if err != nil {
		logger.Errorf(ctx, "failed to check probe pod: %s", err)
		return err
	}

	if !ready {
		logger.Errorf(ctx, "probe pod %v not ready: %s", pod.Name, err)
		return errors.New("probe pod not ready")
	}

	// cleanup
	err = kubeClient.CoreV1().Pods(nameSpaceKubeSystem).Delete(ctx, probePodName, *metav1.NewDeleteOptions(0))
	if err != nil {
		logger.Errorf(ctx, "failed to delete probe pod: %s", err)
	}

	return nil
}
