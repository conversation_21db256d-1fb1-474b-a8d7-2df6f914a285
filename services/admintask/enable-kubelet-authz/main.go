package main

import (
	"context"
	"encoding/json"
	"os"
	"time"

	flag "github.com/spf13/pflag"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kubeletv1beta1 "k8s.io/kubelet/config/v1beta1"
)

const (
	defaultKubeletConfigPath = "/etc/kubernetes/kubelet-config.json"
)

func main() {
	kubeletConfigPath := flag.String("config", defaultKubeletConfigPath, "path to kubeconfig")
	flag.Parse()

	ctx := context.TODO()

	b, err := os.ReadFile(*kubeletConfigPath)
	if err != nil {
		logger.Errorf(ctx, "read default kubelet config file error, %s", err.Error())
		os.Exit(1)
	}

	var kubeletConfig kubeletv1beta1.KubeletConfiguration
	if err := json.Unmarshal(b, &kubeletConfig); err != nil {
		logger.Errorf(ctx, "unmarshal kubelet config file error, %s", err.Error())
		os.Exit(1)
	}

	if err := enableKubeletAuthZ(&kubeletConfig); err != nil {
		logger.Errorf(ctx, "enable kubelet authz error, %s", err.Error())
		os.Exit(1)
	}

	newBytes, err := json.MarshalIndent(kubeletConfig, "", "    ")
	if err != nil {
		logger.Errorf(ctx, "marshal kubelet config file error, %s", err.Error())
		os.Exit(1)
	}

	if err := os.WriteFile(defaultKubeletConfigPath, newBytes, 0644); err != nil {
		logger.Errorf(ctx, "write kubelet config file error, %s", err.Error())
		os.Exit(1)
	}

	logger.Infof(ctx, "enable kubelet authz success")
	os.Exit(0)
}

func enableKubeletAuthZ(kubeletConfig *kubeletv1beta1.KubeletConfiguration) error {
	kubeletConfig.Authorization = kubeletv1beta1.KubeletAuthorization{
		Mode: kubeletv1beta1.KubeletAuthorizationModeWebhook,
		Webhook: kubeletv1beta1.KubeletWebhookAuthorization{
			CacheAuthorizedTTL:   metav1.Duration{Duration: 5 * time.Minute},
			CacheUnauthorizedTTL: metav1.Duration{Duration: 30 * time.Second},
		},
	}
	return nil
}
