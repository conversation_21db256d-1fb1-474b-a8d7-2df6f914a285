apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: enable-kubelet-authz
  namespace: kube-system
spec:
  selector:
    matchLabels:
      name: enable-kubelet-authz
  template:
    metadata:
      labels:
        name: enable-kubelet-authz
    spec:
      hostNetwork: true
      initContainers:
        - name: core
          image: registry.baidubce.com/cce-plugin-pro/enable-kubelet-authz:20240707
          command: ["/home/<USER>/runner"]
          volumeMounts:
            - name: kubelet-config
              mountPath: /etc/kubernetes
      containers:
        - name: prober
          image: registry.baidubce.com/cce-plugin-pro/busybox:latest
          command:
            - sh
            - -c
            - "while true; do echo 'enable kubelet api authz success'; sleep 600; done"
          volumeMounts:
            - name: kubelet-config
              mountPath: /etc/kubernetes
              readOnly: true
      volumes:
        - name: kubelet-config
          hostPath:
            path: /etc/kubernetes
            type: Directory
      tolerations:
        - operator: Exists
      restartPolicy: Always