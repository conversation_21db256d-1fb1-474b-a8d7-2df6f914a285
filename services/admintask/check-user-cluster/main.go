package main

import (
	"context"
	"flag"
	"fmt"
	"strings"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/kubernetes/pkg/apis/apps"
	v1 "k8s.io/kubernetes/pkg/apis/apps/v1"
	"k8s.io/kubernetes/pkg/apis/extensions/v1beta1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

// 用于检查和输出用户 lb controller 和 cloud-node-controller 版本 辅助升级集群

type checkLBServiceConfig struct {
	Region    string
	AccountID string
}

var (
	config = checkLBServiceConfig{}

	databaseEndpoint = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(10.11.48.247:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(10.70.16.33:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(10.11.151.20:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(10.11.74.19:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}
)

func filterCluster(model *models.Client, clusterIDs []string) []string {
	result := make([]string, 0)

	for _, clusterID := range clusterIDs {
		if strings.HasPrefix(clusterID, "cce-") {
			result = append(result, clusterID)
		}
	}

	return result
}

func parseFlags() {
	flag.StringVar(&config.Region, "region", "", "[Required]Region of the clusters to be checked.")
	flag.StringVar(&config.AccountID, "accountid", "", "Account ID")

	flag.Parse()
}

func main() {
	ctx := context.TODO()

	// 解析参数
	parseFlags()

	if _, ok := databaseEndpoint[config.Region]; !ok {
		fmt.Printf("Region %v is not valid\n", config.Region)
		return
	}

	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint[config.Region])
	if err != nil {
		fmt.Printf("NewClient failed: %s\n", err)
		return
	}

	// 获取地域所有集群, 包含 V1 和 V2
	// 获取所有待定集群
	clusterIDs := make([]string, 0)
	if config.AccountID != "" {
		// 根据 account id 获取地域所有集群
		clusters, err := model.GetClusterList(ctx, config.AccountID)
		if err != nil {
			fmt.Printf("GetClusterList failed: %s\n", err)
			return
		}
		for _, c := range clusters {
			clusterIDs = append(clusterIDs, c.Spec.ClusterID)
		}
	} else {
		// 获取地域所有集群, 包含 V1 和 V2
		clusterIDs, err = model.GetAllRunningClusterIDsCompatibility(ctx)
		if err != nil {
			fmt.Printf("GetAllRunningClusterIDsCompatibility failed: %s\n", err)
			return
		}
	}

	fmt.Printf("All ClusterIDs: %s\n", utils.ToJSON(clusterIDs))

	// 条件过滤集群
	targetClusters := filterCluster(model, clusterIDs)
	fmt.Printf("Target ClusterIDs: %s\n", utils.ToJSON(targetClusters))

	resultLB := make(map[string][]string)
	resultCloudNdde := make(map[string][]string)
	// 检查所有集群

	for _, clusterID := range targetClusters {
		clusterInfo, err := checkOneCluster(model, clusterID)
		if err != nil {
			fmt.Printf("checkOneCluster failed: %s\n", err)
			continue
		}

		fmt.Printf("%s %s %s\n", clusterInfo.clusterID, clusterInfo.k8sVersion, clusterInfo.name)

		if lbs, ok := resultLB[clusterInfo.lbControllerImage]; ok {
			resultLB[clusterInfo.lbControllerImage] = append(lbs, clusterInfo.clusterID)
		} else {
			resultLB[clusterInfo.lbControllerImage] = []string{clusterInfo.clusterID}
		}

		if cns, ok := resultCloudNdde[clusterInfo.cloudNodeImage]; ok {
			resultCloudNdde[clusterInfo.cloudNodeImage] = append(cns, clusterInfo.clusterID)
		} else {
			resultCloudNdde[clusterInfo.cloudNodeImage] = []string{clusterInfo.clusterID}
		}
	}

	fmt.Println("===========LB Controller=============")
	for key, values := range resultLB {
		splitImages := strings.Split(key, ":")
		fmt.Printf("%s %s \n", splitImages[len(splitImages)-1], values)
	}

	fmt.Println("===========CloudNode Controller=============")
	for key, values := range resultCloudNdde {
		splitImages := strings.Split(key, ":")
		fmt.Printf("%s %s \n", splitImages[len(splitImages)-1], values)
	}
}

type clusterInfo struct {
	name              string
	accountID         string
	clusterID         string
	k8sVersion        string
	cloudNodeImage    string
	lbControllerImage string
}

func checkOneCluster(model *models.Client, clusterID string) (*clusterInfo, error) {
	ctx := context.TODO()
	info := &clusterInfo{
		clusterID: clusterID,
	}

	// 获取集群的 AccountID
	cluster, err := model.GetClusterByClusterID(context.TODO(), clusterID)
	if err != nil {
		return nil, err
	}
	info.name = cluster.Spec.ClusterName
	info.accountID = cluster.Spec.AccountID
	info.k8sVersion = string(cluster.Spec.K8SVersion)

	// 构建 User Cluster K8S Client
	kubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		fmt.Printf("GetAdminKubeConfigCompatibility failed: %s\n", err)
		return nil, err
	}
	userK8sClient, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
	if err != nil {
		fmt.Printf("NewK8SClient failed: %s\n", err)
		return nil, err
	}

	// 获取集群的 cce-lb-controller 版本
	lbdeployment, err := getDeployment(ctx, userK8sClient, "kube-system", "cce-lb-controller")
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return info, err
		}
		info.lbControllerImage = "DeploymentNotFound"
	}
	if lbdeployment != nil {
		if len(lbdeployment.Spec.Template.Spec.Containers) == 1 {
			info.lbControllerImage = lbdeployment.Spec.Template.Spec.Containers[0].Image
		} else {
			info.lbControllerImage = "UnexpectedDeployment"
		}
	}

	// 获取集群的 cce-cloud-node-controller 版本
	cloudNodeDeployment, err := getDeployment(ctx, userK8sClient, "kube-system", "cce-cloud-node-controller")
	if err != nil {
		if !apierrors.IsNotFound(err) {
			return info, err
		}
		info.cloudNodeImage = "DeploymentNotFound"
	}
	if cloudNodeDeployment != nil {
		if len(cloudNodeDeployment.Spec.Template.Spec.Containers) == 1 {
			info.cloudNodeImage = cloudNodeDeployment.Spec.Template.Spec.Containers[0].Image
		} else {
			info.cloudNodeImage = "UnexpectedDeployment"
		}
	}

	return info, nil
}

func getDeployment(ctx context.Context, k8sClient kubernetes.Interface, ns string, name string) (*apps.Deployment,
	error) {
	deployment := &apps.Deployment{}

	// 1. 先找 apps/v1 版本的 Deployment 并转换为内部版本返回
	deploymentAppsV1, err := k8sClient.AppsV1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, err
	}
	if err == nil {
		if err := v1.Convert_v1_Deployment_To_apps_Deployment(deploymentAppsV1, deployment, nil); err != nil {
			return nil, err
		}
		return deployment, nil
	}

	// 2. 再找 extensions/v1beta1 版本的 Deployment(早期的部署) 并转换为内部版本返回
	deploymentExtensionsV1Beta1, err := k8sClient.ExtensionsV1beta1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	if err := v1beta1.Convert_v1beta1_Deployment_To_apps_Deployment(deploymentExtensionsV1Beta1, deployment, nil); err != nil {
		return nil, err
	}
	return deployment, nil
}
