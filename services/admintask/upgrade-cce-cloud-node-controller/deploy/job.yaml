apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cce-upgrade-cce-cloud-node-controller
rules:
  - apiGroups:
      - cce.baidubce.com
    resources:
      - workflows
    verbs:
      - create
      - get
      - list
      - watch
      - patch
      - update
      - delete
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: cce-upgrade
  name: cce-upgrade-cce-cloud-node-controller
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cce-upgrade-cce-cloud-node-controller-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-upgrade-cce-cloud-node-controller
subjects:
  - kind: ServiceAccount
    namespace: cce-upgrade
    name: cce-upgrade-cce-cloud-node-controller
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-upgrade
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-upgrade
  name: cce-upgrade-cce-cce-cloud-node-controller-clusters
data:
  input.txt: |
    cce-cdwxsba9
    cce-dodn1yii
    cce-906c2gcm
---
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-upgrade-cce-cce-cloud-node-controller-********-1408
  namespace: cce-upgrade
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    spec:
      hostNetwork: true
      restartPolicy: Never
      serviceAccount: cce-upgrade-cce-cloud-node-controller
      imagePullSecrets:
        - name: ccr-registry-secret
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-upgrade-cce-cce-cloud-node-controller-clusters
          name: cce-upgrade-cce-cce-cloud-node-controller-clusters
      containers:
        - name: cce-upgrade-cce-cloud-node-controller
          image: registry.baidubce.com/cce-service-dev/upgrade-cce-cloud-node-controller:jichao
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /home/<USER>/cce/config
              name: cce-upgrade-cce-cce-cloud-node-controller-clusters
          args:
            - /upgrade-cce-cloud-node-controller
            - --region=bj
            - --target-image=registry.baidubce.com/cce-plugin-dev/cce-cloud-node-controller:jichao
            - --clusterid-file-path=/home/<USER>/cce/config/input.txt


