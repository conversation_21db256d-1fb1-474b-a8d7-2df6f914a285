package main

import (
	"context"
	"flag"
	"fmt"
	"strings"
	"time"

	v1beta1 "k8s.io/api/extensions/v1beta1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

type Config struct {
	Region string
	Update bool
}

type Summary struct {
	SkippedClusterNBR int
	FailedClusterNBR  int
	SucceedClusterNBR int
}

const (
	nameSpaceKubeSystem = "kube-system"
)

var (
	config  = Config{}
	summary = Summary{}

	databaseEndpoint map[string]string = map[string]string{
		"bj":     "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true",
		"hkg":    "cce_service_w:mAiQdc9rbwEWtAJbRwiqxKU@tcp(************:5596)/hkg_cce_service?charset=utf8&parseTime=true",
		"wh":     "cce_service_w:1bU_YVAgx2CCJrmDtP7BaqaEp@tcp(***********:5201)/whgg_cce_service?charset=utf8&parseTime=true",
		"sz":     "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(*************:5875)/sz_cce_service?charset=utf8&parseTime=true",
		"gz":     "gz_cce_service_w:UJ5XrDr_rLcfRMau@tcp(************:5982)/gz_cce_service?charset=utf8&parseTime=true",
		"bd":     "cce_service_w:XjJPujGjSDb@tcp(***********:5979)/bdbl_cce_service?charset=utf8&parseTime=true",
		"gztest": "cce_service_w:6fS_5tvWNA@tcp(************:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true",
	}

	v1ClusterIDs []string
)

func parseFlags() {
	flag.StringVar(&config.Region, "r", "gztest", "")
	flag.BoolVar(&config.Update, "u", false, "")

	flag.Parse()
}

func main() {
	var (
		ctx context.Context = context.TODO()
	)

	// 解析参数
	parseFlags()

	if _, ok := databaseEndpoint[config.Region]; !ok {
		logger.Errorf(ctx, "Region %v is not valid", config.Region)
		return
	}

	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint[config.Region])
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return
	}

	// 获取地域所有集群, 包含 V1 和 V2
	clusterIDs, err := model.GetAllRunningClusterIDsCompatibility(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetAllRunningClusterIDsCompatibility failed: %s", err)
		return
	}

	logger.Infof(ctx, "All ClusterIDs: %s", utils.ToJSON(clusterIDs))

	for _, cluster := range clusterIDs {
		if strings.HasPrefix(cluster, "c-") {
			v1ClusterIDs = append(v1ClusterIDs, cluster)
		}
	}

	logger.Infof(ctx, "All Fucking Old V1 ClusterIDs: %s", utils.ToJSON(v1ClusterIDs))

	// 挨个升级集群
	for _, clusterID := range v1ClusterIDs {
		logger.Infof(ctx, "====> Updating CoreDNS for Cluster %s Begin <====", clusterID)

		err = updateOneCluster(model, clusterID)
		if err != nil {
			logger.Errorf(ctx, "update cluster %v failed: %v", clusterID, err)
		}

		logger.Infof(ctx, "====> Updating CoreDNS for Cluster %s Ends <====", clusterID)

		logger.Infof(ctx, "\n\n")
	}

	logger.Infof(ctx, "Summary: %+v", summary)
}

func updateOneCluster(model *models.Client, clusterID string) error {
	var (
		ctx = context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())
		err error
	)

	defer func() {
		if err != nil {
			summary.FailedClusterNBR++
		} else {
			summary.SucceedClusterNBR++
		}
	}()

	// 获取集群 kubeconfig
	kubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		// 可能存在 kubeconfig_info 中存在, 但是 t_cluster 不存在情况
		if strings.Contains(err.Error(), "not exist in") {
			logger.Infof(ctx, "Cluster %s not exist, skip update", clusterID)
			summary.SkippedClusterNBR++
			return nil
		}

		logger.Errorf(ctx, "GetAdminKubeConfigCompatibility failed: %s", err)
		return err
	}

	// 构建 K8S Client
	client, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "NewK8SClient failed: %s", err)
		return err
	}

	dnsDeploy, replicas, err := GetK8sDNSReplicas(ctx, client)
	if err != nil {
		logger.Errorf(ctx, "GetK8sDNSReplicas failed: %s", err)
		return err
	}

	if replicas <= 1 {
		logger.Warnf(ctx, "god damn it, finally catch you: %v, %v replicas: %d", clusterID, dnsDeploy.Name, replicas)
	}

	if replicas <= 1 && config.Update {
		var (
			expectedReplicas int32 = 3
		)
		dnsDeploy.Spec.Replicas = &expectedReplicas
		_, err = client.ExtensionsV1beta1().Deployments(nameSpaceKubeSystem).Update(ctx, dnsDeploy, metav1.UpdateOptions{})
		if err != nil {
			logger.Errorf(ctx, "update replicas failed: %s", err)
			return err
		}

		for i := 0; i < 10; i++ {
			deploy, err := client.ExtensionsV1beta1().Deployments(nameSpaceKubeSystem).Get(ctx, dnsDeploy.Name, metav1.GetOptions{})
			if err != nil {
				logger.Warnf(ctx, "error fetch deploy %v: %v", dnsDeploy.Name, err)
			}

			if deploy.Status.Replicas >= expectedReplicas {
				logger.Infof(ctx, "deployment %v readyReplicas >= %d, done", dnsDeploy.Name, expectedReplicas)
				break
			}

			logger.Infof(ctx, "deployment %v readyReplicas == %d, next round", dnsDeploy.Name, deploy.Status.Replicas)
			time.Sleep(time.Second * 3)
		}
	}

	return nil
}

func GetK8sDNSReplicas(ctx context.Context, kubeClient kubernetes.Interface) (*v1beta1.Deployment, int, error) {
	var (
		k8sDNS   = []string{"kube-dns", "coredns"}
		replicas = 0
		errs     = []error{}
	)

	for _, dns := range k8sDNS {
		deploy, err := kubeClient.ExtensionsV1beta1().Deployments(nameSpaceKubeSystem).Get(ctx, dns, metav1.GetOptions{})
		if err == nil {
			logger.Infof(ctx, "find deployment %v successfully", dns)
			replicas = int(*deploy.Spec.Replicas)
			if replicas == 0 {
				logger.Infof(ctx, "deployment %v replicas == 0, continue", dns)
				continue
			}
			return deploy, replicas, nil
		}
		logger.Errorf(ctx, "failed to find deployment %v: %v", dns, err)
		errs = append(errs, err)
	}

	return nil, 0, fmt.Errorf("neither kube-dns or coredns is found: %v", errs)
}
