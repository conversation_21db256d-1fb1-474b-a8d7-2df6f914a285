package main

import (
	"context"
	"encoding/json"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/blb"
	eipsdk "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/eip"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalblb"
	"testing"
	"time"

	"k8s.io/apimachinery/pkg/util/intstr"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/appblb"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
)

var (
	ServicePassword   = "c8QBqhOKxmGu0Sl2p13guMil3U2grbYt"
	ResourceAccountID = "02fa0a988caf43a9a5c0270310347581"
	ResourceAK        = "2daa909dc0cb4c7892cc409135cf0c37"
	ResourceSK        = "6a4eea1ca53543be80e06601756613aa"

	NormalBLBID        = "lb-********"
	User_AccoundID     = "111111111111111111111111111"
	targetIPList       = []string{"**********", "**********", "**********"}
	restoreMachineList = []string{"eec8d094-b9d9-4dec-8b9e-094303adbc26", "e4e91a49-b704-4af8-9dc6-27d755a22552", "8485e36c-2b0b-483a-a583-60bb7dc687ca"}

	region         = "bd"
	stsEndpoint    = "sts.bdbl.bce.baidu-int.com:8586/v1"
	iamEndpoint    = "iam.bdbl.bce.baidu-int.com/v3"
	blbEndpoint    = "blb.bd.baidubce.com"
	eipEndpoint    = "eip.bd.baidubce.com"
	appblbEndpoint = "blb.bd.baidubce.com/v1"

	//region         = "gz"
	//stsEndpoint    = "sts.gz.iam.sdns.baidu.com:8586/v1"
	//iamEndpoint    = "iam.gz.bce-internal.baidu.com/v3"
	//blbEndpoint    = "blb.gz.baidubce.com"
	//eipEndpoint    = "eip.gz.baidubce.com"
	//appblbEndpoint = "blb.gz.baidubce.com/v1"
)

var (
	VPCID    = "vpc-111"
	SubnetID = "sbn-111"
)

// 创建托管环境下的 EIP
// 用途：Pro 集群额外的 EIP
func test_Create_Managed_EIP(t *testing.T) {
	ctx := context.TODO()

	// 0. 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, "BceServiceRole_SERVICE_CCE", "cce", ServicePassword)

	arg := &eipsdk.CreateEIPArgs{
		Name:            "jichao",
		BandwidthInMbps: 2,
		Billing: &eipsdk.Billing{
			PaymentTiming: eipsdk.PaymentTimingPostpaid,
			BillingMethod: eipsdk.BillingMethodByBandwidth,
		},
	}

	eipclient := eipsdk.NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
		Endpoint: eipEndpoint,
	})
	eipclient.SetDebug(true)

	eip, err := eipclient.CreateEIPWithClientToken(ctx, arg, "asdf",
		stsclient.NewSignOptionWithResourceAK(ctx, User_AccoundID, ResourceAK, ResourceSK, ResourceAccountID, "cce"))

	if err != nil {
		t.Errorf("Failed CreateEIPWithClientToken %s", err)
	}
	t.Logf("CreateEIPWithClientToken Success: %v", eip)
}

// 为托管的普通型 BLB 创建监听器并恢复 RS
// 用途：误操作时快速恢复老托管集群的 BLB
func test_Recover_Normal_BLB(t *testing.T) {
	ctx := context.TODO()

	// 0.初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, "BceServiceRole_SERVICE_CCE", "cce", ServicePassword)

	c := blb.NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
		Endpoint: blbEndpoint,
	})
	c.SetDebug(true)

	// 1. 恢复 6443 监听器
	args := blb.CreateTCPListenerArgs{
		LoadBalancerId: NormalBLBID,
		ListenerPort:   6443,
		BackendPort:    6443,
		Scheduler:      "RoundRobin",
	}
	err := c.CreateTCPListener(ctx, &args, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("Failed toCreateTCPListener %s", err)
		return
	}
	t.Logf("CreateTCPListener Success")
	time.Sleep(5 * time.Second)

	blbInternalClient := internalblb.NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   "bd",
		Endpoint: "blb.bdbl.bce.baidu-int.com",
	})

	// 2. 恢复 RS
	rsToAdd := []*internalblb.BackendServerAbstract{}
	for _, bccID := range restoreMachineList {
		rsToAdd = append(rsToAdd, &internalblb.BackendServerAbstract{
			Weight:       100,
			InstanceUUID: bccID,
		})
	}
	err = blbInternalClient.CreateBackendServer(ctx, NormalBLBID, &internalblb.CreateBackendServerArgs{
		BackendServerList: rsToAdd,
	}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("Failed AddBackendServers %s", err)
		return
	}

}

// 创建托管的应用型 BLB 创建 6443 端口并配置完整信息
// 用途：Pro 集群额外的 BLB
func test_Create_Managed_App_BLB(t *testing.T) {
	ctx := context.TODO()

	// 0. 初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, "BceServiceRole_SERVICE_CCE", "cce", ServicePassword)

	appblbClient := appblb.NewClient(&bce.Config{
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
		Endpoint: appblbEndpoint,
	})
	appblbClient.SetDebug(true)

	resp, err := appblbClient.CreateAppLoadBalancer(ctx, &appblb.CreateAppLoadBalancerArgs{
		Name:     "jichao",
		Desc:     "jichao",
		VPCID:    VPCID,
		SubNetID: SubnetID,
	}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("Failed CreateAppLoadBalancer %s", err)
	}
	t.Logf("CreateAppLoadBalancer Success: %v", resp)
	time.Sleep(10 * time.Second)

	NormalBLBID = resp.BLBID

	// 1. 检查和创建 6443 TCP 监听器
	listeners, err := appblbClient.DescribeAppTCPListener(ctx, NormalBLBID, 6443,
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("DescribeTCPListener failed: %v", err)
		return
	}
	listenersBytes, err := json.MarshalIndent(listeners, "", " ")
	if err != nil {
		t.Errorf("Failed to marshal indent %s", err)
	}
	t.Logf("Existing Listeners: %s", string(listenersBytes))

	if len(listeners.ListenerList) == 0 {
		t.Logf("Needs Create TCP Listener")
		if err := appblbClient.CreateAppTCPListener(ctx, NormalBLBID, &appblb.CreateAppTCPListenerArgs{
			ListenerPort: 6443,
			Scheduler:    "LeastConnection",
		}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey,
			ResourceAccountID, "cce")); err != nil {
			t.Errorf("CreateAppTCPListener Fail: %s", err)
			return
		}
		t.Logf("CreateAppTCPListener Success")
		time.Sleep(5 * time.Second)
	} else {
		t.Logf("No needs Create TCP Listener")
	}

	// 2. 检查和创建 IP 组
	ipgroupResp, err := appblbClient.ListIPGroups(ctx, NormalBLBID, &appblb.ListIPGroupArgs{Name: "TCP-6443", ExactlyMatch: true},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("ListIPGroups Fail: %s", err)
		return
	}
	ipGroupID := ""
	if len(ipgroupResp.AppIPGroupList) == 0 {
		resp, err := appblbClient.CreateIPGroup(ctx, NormalBLBID, &appblb.CreateIPGroupArgs{
			Name: "TCP-6443",
		},
			stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
		if err != nil {
			t.Errorf("CreateIPGroup Fail: %s", err)
			return
		}
		t.Logf("CreateIPGroup Success: %v", resp)
		time.Sleep(5 * time.Second)
		ipGroupID = resp.ID
	} else {
		ipGroupID = ipgroupResp.AppIPGroupList[0].ID
		t.Logf("No needs Create IP Group")
	}

	// 3. 创建 IP组规则
	if err := appblbClient.CreateIPGroupBackendPolicy(ctx, NormalBLBID, &appblb.CreateIPGroupBackendPolicyArgs{
		IPGroupID:   ipGroupID,
		Type:        "TCP",
		HealthCheck: "TCP",
	}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")); err != nil {
		t.Errorf("CreateIPGroupBackendPolicy Fail: %s", err)
		return
	}
	t.Logf("CreateIPGroupBackendPolicy Success")
	time.Sleep(5 * time.Second)

	// 4. 创建转发规则
	pol := &appblb.AppPolicy{
		AppIPGroupID: ipGroupID,
		PortType:     "TCP",
		FrontendPort: 6443,
		BackendPort:  6443,
		Priority:     1,
		RuleList: []*appblb.AppRule{
			{
				Key:   appblb.AppRuleKeyTypeAll,
				Value: "*",
			},
		},
	}
	if err := appblbClient.CreateAppPolicys(ctx, NormalBLBID,
		&appblb.CreateAppPolicyArgs{
			ListenerPort:  6443,
			AppPolicyList: []*appblb.AppPolicy{pol},
		},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")); err != nil {
		t.Errorf("CreateAppPolicys Fail: %s", err)
		return
	}
	t.Logf("CreateAppPolicys Success")
	time.Sleep(5 * time.Second)

	// 5. 向 IP 组中添加 IP
	targetMembers := make([]*appblb.AppIPGroupMember, 0)
	for _, ip := range targetIPList {
		targetMembers = append(targetMembers, &appblb.AppIPGroupMember{
			IP:     ip,
			Port:   intstr.IntOrString{IntVal: 6443},
			Weight: 100,
		})
	}
	if err := appblbClient.CreateIPGroupMember(ctx, NormalBLBID,
		&appblb.CreateIPGroupMemberArgs{
			IPGroupID:  ipGroupID,
			MemberList: targetMembers,
		},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")); err != nil {
		t.Errorf("CreateIPGroupMember Fail: %s", err)
		return
	}
	t.Logf("CreateIPGroupMember Success")
}

// 移除普通型 BLB 的所有监听器和后端
// 用途：Pro 集群迁移 BLB 变更的第一阶段
func test_Step_1_Remove_Listeners_And_Backend(t *testing.T) {
	ctx := context.Background()

	// 0.初始化 sts client
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, "BceServiceRole_SERVICE_CCE", "cce", ServicePassword)
	stsclient.SetDebug(true)

	c := blb.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    blbEndpoint,
	})
	c.SetDebug(true)

	// 1. 列出所有监听器
	listeners, err := c.DescribeTCPListener(ctx,
		&blb.DescribeTCPListenerArgs{
			LoadBalancerId: NormalBLBID,
		},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("DescribeTCPListener failed: %v", err)
		return
	}
	listenersBytes, err := json.MarshalIndent(listeners, "", " ")
	if err != nil {
		t.Errorf("Failed to marshal indent %s", err)
	}
	t.Logf("Existing Listeners: %s", string(listenersBytes))

	if len(listeners) > 1 {
		t.Errorf("More than one listeners exists, which is unexpected.")
		return
	}

	// 2. 删除所有监听器
	if len(listeners) != 0 {
		if err := c.DeleteListeners(ctx, &blb.DeleteListenersArgs{
			LoadBalancerId: NormalBLBID,
			PortList:       []int{ 6443 },
		}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey,
			ResourceAccountID, "cce")); err != nil {
			t.Errorf("DeleteListeners failed: %s", err)
			return
		}
		t.Logf("DeleteListeners Success")
	} else {
		t.Logf("No listeners exist, skip")
	}
	time.Sleep(3 * time.Second)

	// 3. 二次列出所有监听器
	listenerAfterDelete, err := c.DescribeTCPListener(ctx,
		&blb.DescribeTCPListenerArgs{
			LoadBalancerId: NormalBLBID,
		},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("DescribeTCPListener failed: %v", err)
		return
	}

	listenerAfterDeleteBytes, err := json.MarshalIndent(listenerAfterDelete, "", " ")
	if err != nil {
		t.Errorf("Failed to marshal indent %s", err)
		return
	}
	t.Logf("Existing Listeners after delete: %s", string(listenerAfterDeleteBytes))

	if len(listenerAfterDelete) > 0 {
		t.Errorf("Expect no listeners exist, but actually not")
		return
	}

	// 4. 列出所有 RS
	signOpt := stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")
	signOpt.AddHeader("backendServer-uuid", "true")
	rsList, err := c.DescribeBackendServers(ctx, &blb.DescribeBackendServersArgs{
		LoadBalancerId: NormalBLBID,
	}, signOpt)
	if err != nil {
		t.Errorf("DescribeBackendServers failed: %v", err)
		return
	}
	rs := make([]string, 0)
	for _, rsEle := range rsList {
		rs = append(rs, rsEle.InstanceId)
	}
	t.Logf("RS Record: %s", rs)

	// 5. 删除所有 RS
	if len(rs) != 0 {
		signOpt = stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")
		signOpt.AddHeader("backendServer-uuid", "true")
		if err := c.RemoveBackendServers(ctx, &blb.RemoveBackendServersArgs{
			LoadBalancerId:    NormalBLBID,
			BackendServerList: rs,
		}, signOpt); err != nil {
			t.Errorf("RemoveBackendServers failed: %v", err)
			return
		}
		t.Logf("RemoveBackendServers Success")
	} else {
		t.Logf("No rs exists, skip remove backend servers")
	}
	time.Sleep(3 * time.Second)

	// 6. 再次列出所有 RS
	signOpt = stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")
	signOpt.AddHeader("backendServer-uuid", "true")
	rsList, err = c.DescribeBackendServers(ctx, &blb.DescribeBackendServersArgs{LoadBalancerId: NormalBLBID }, signOpt)
	if err != nil {
		t.Errorf("DescribeBackendServers failed: %v", err)
		return
	}
	rs = make([]string, 0)
	for _, rsEle := range rsList {
		rs = append(rs, rsEle.InstanceId)
	}
	t.Logf("RS Record: %s", rs)
	if len(rs) != 0 {
		t.Errorf("Expected no rs exists, but actually not")
		return
	}

	t.Logf("Finish")
}

// 为应用型 BLB 添加监听器、转发规则、IP 组和健康检查
// 用途：Pro 集群迁移 BLB 变更的第三阶段
func test_Step_2_Add_Listeners_Policies_And_Backend(t *testing.T) {
	// 0. 初始化 sts client
	ctx := context.Background()
	stsclient := sts.NewClient(ctx, &bce.Config{
		Endpoint: stsEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, &bce.Config{
		Endpoint: iamEndpoint,
		Checksum: true,
		Timeout:  30 * time.Second,
		Region:   region,
	}, "BceServiceRole_SERVICE_CCE", "cce", ServicePassword)

	appblbClient := appblb.NewClient(&bce.Config{
		Checksum:    true,
		Timeout:     30 * time.Second,
		Region:      region,
		Endpoint:    appblbEndpoint,
	})
	appblbClient.SetDebug(true)

	// 1. 检查和创建 6443 TCP 监听器
	listeners, err := appblbClient.DescribeAppTCPListener(ctx,NormalBLBID, 6443,
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err != nil {
		t.Errorf("DescribeTCPListener failed: %v", err)
		return
	}
	listenersBytes, err := json.MarshalIndent(listeners, "", " ")
	if err != nil {
		t.Errorf("Failed to marshal indent %s", err)
	}
	t.Logf("Existing Listeners: %s", string(listenersBytes))

	if len(listeners.ListenerList) == 0 {
		t.Logf("Needs Create TCP Listener")
		if err := appblbClient.CreateAppTCPListener(ctx, NormalBLBID, &appblb.CreateAppTCPListenerArgs{
			ListenerPort: 6443,
			Scheduler:    "LeastConnection",
		}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey,
			ResourceAccountID, "cce")); err != nil {
			t.Errorf("CreateAppTCPListener Fail: %s", err)
			return
		}
		t.Logf("CreateAppTCPListener Success")
		time.Sleep(5* time.Second)
	} else {
		t.Logf("No needs Create TCP Listener")
	}

	// 2. 检查和创建 IP 组
	ipgroupResp, err := appblbClient.ListIPGroups(ctx, NormalBLBID,&appblb.ListIPGroupArgs{Name: "TCP-6443", ExactlyMatch: true},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
	if err!= nil {
		t.Errorf("ListIPGroups Fail: %s", err)
		return
	}
	ipGroupID := ""
	if len(ipgroupResp.AppIPGroupList) == 0 {
		resp, err := appblbClient.CreateIPGroup(ctx, NormalBLBID, &appblb.CreateIPGroupArgs{
			Name: "TCP-6443",
		},
			stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"))
		if err != nil {
			t.Errorf("CreateIPGroup Fail: %s", err)
			return
		}
		t.Logf("CreateIPGroup Success: %v", resp)
		time.Sleep(5* time.Second)
		ipGroupID = resp.ID
	} else {
		ipGroupID = ipgroupResp.AppIPGroupList[0].ID
		t.Logf("No needs Create IP Group")
	}

	// 3. 创建 IP组规则
	if err := appblbClient.CreateIPGroupBackendPolicy(ctx, NormalBLBID, &appblb.CreateIPGroupBackendPolicyArgs{
		IPGroupID:   ipGroupID,
		Type:        "TCP",
		HealthCheck: "TCP",
	}, stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"));err != nil {
		t.Errorf("CreateIPGroupBackendPolicy Fail: %s", err)
		return
	}
	t.Logf("CreateIPGroupBackendPolicy Success")
	time.Sleep(5* time.Second)

	// 4. 创建转发规则
	pol := &appblb.AppPolicy{
		AppIPGroupID: ipGroupID,
		PortType:     "TCP",
		FrontendPort: 6443,
		BackendPort:  6443,
		Priority:     1,
		RuleList: []*appblb.AppRule{
			{
				Key:   appblb.AppRuleKeyTypeAll,
				Value: "*",
			},
		},
	}
	if err := appblbClient.CreateAppPolicys(ctx, NormalBLBID,
		&appblb.CreateAppPolicyArgs{
			ListenerPort:  6443,
			AppPolicyList: []*appblb.AppPolicy{pol},
		},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce")); err != nil {
		t.Errorf("CreateAppPolicys Fail: %s", err)
		return
	}
	t.Logf("CreateAppPolicys Success")
	time.Sleep(5* time.Second)

	// 5. 向 IP 组中添加 IP
	targetMembers := make([]*appblb.AppIPGroupMember, 0)
	for _, ip := range targetIPList {
		targetMembers = append(targetMembers, &appblb.AppIPGroupMember{
			IP: ip,
			Port: intstr.IntOrString{IntVal: 6443},
			Weight: 100,
		})
	}
	if err := appblbClient.CreateIPGroupMember(ctx, NormalBLBID,
		&appblb.CreateIPGroupMemberArgs{
			IPGroupID: ipGroupID,
			MemberList: targetMembers,
		},
		stsclient.NewSignOptionWithResourceHeader(ctx, User_AccoundID, blb.ResourceHeaderHexKey, ResourceAccountID, "cce"));err != nil {
		t.Errorf("CreateIPGroupMember Fail: %s", err)
		return
	}
	t.Logf("CreateIPGroupMember Success")
}

