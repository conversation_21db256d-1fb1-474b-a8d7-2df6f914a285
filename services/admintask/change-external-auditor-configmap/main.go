package main

import (
	"context"
	"os"
	"strings"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
)

const (
	// GZTEST
	// databaseEndpoint = "cce_service_w:6fS_5tvWNA@tcp(10.11.151.20:5982)/bce_gzns_sandbox_cce_service?charset=utf8&parseTime=true"

	// BJ
	databaseEndpoint = "cce_service_w:8bqvaLui3Pp3_nFt@tcp(drds.cce-bj.baidu-int.com:5991)/cce_service?charset=utf8&parseTime=true"

	// 苏州
	// databaseEndpoint = "sz_cce_service_w:Kxt6L_FGxmerRuPD@tcp(10.11.110.155:5875)/sz_cce_service?charset=utf8&parseTime=true"
)

func main() {
	ctx := context.TODO()

	if err := run(ctx); err != nil {
		logger.Errorf(ctx, "run failed: %s", err)
		os.Exit(-1)
	}
}

func run(ctx context.Context) error {
	// 初始化数据库
	model, err := models.NewClient(ctx, databaseEndpoint)
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	// 获取地域所有集群, 包含 V1 和 V2
	clusterIDs, err := model.GetAllRunningClusterIDsCompatibility(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetAllRunningClusterIDsCompatibility failed: %s", err)
		return err
	}

	logger.Infof(ctx, "All ClusterIDs: %s", utils.ToJSON(clusterIDs))

	// 替换集群中 kube-system/audit-config ConfigMap
	for _, clusterID := range clusterIDs {
		ctx = context.WithValue(ctx, logger.RequestID, logger.GetUUID())

		logger.Infof(ctx, "Update cluster %s begin", clusterID)

		// 获取集群 kubeconfig
		kubeconfig, err := model.GetAdminKubeConfigCompatibility(ctx, clusterID, models.KubeConfigTypeInternal)
		if err != nil {
			// 可能存在 kubeconfig_info 中存在, 但是 t_cluster 不存在情况
			if strings.Contains(err.Error(), "not exist in") {
				logger.Infof(ctx, "Cluster %s not exist, skip update", clusterID)
				continue
			}

			logger.Errorf(ctx, "GetAdminKubeConfigCompatibility failed: %s", err)
			return err
		}

		// 构建 K8S Client
		client, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
		if err != nil {
			logger.Errorf(ctx, "NewK8SClient failed: %s", err)
			return err
		}

		// 检查集群是否存在 ConfigMap
		configmap, err := client.CoreV1().ConfigMaps("kube-system").Get(ctx, "audit-config", metav1.GetOptions{})
		if err != nil && !kerrors.IsNotFound(err) {
			if strings.Contains(err.Error(), "connection timed out") {
				logger.Errorf(ctx, "Cluster %s APIServer connection timed out, skip update", clusterID)
				continue
			}

			logger.Errorf(ctx, "Get ConfigMaps kube-system/audit-config failed: %s", err)
			return err
		}

		// 审计未开启, 不操作
		if kerrors.IsNotFound(err) {
			logger.Infof(ctx, "Cluster %s Configmap not exists, skip", clusterID)
			continue
		}

		logger.Infof(ctx, "Cluster %s kube-system/audit-config exists, update configmap", clusterID)

		// 审计开启
		// 修改 Endpoint 为: engine-a.apigw.sdns.baidu.com
		configmap.Data["endpoint"] = "engine-a.apigw.sdns.baidu.com"

		// 更新 ConfigMap
		if _, err := client.CoreV1().ConfigMaps("kube-system").Update(ctx, configmap, metav1.UpdateOptions{}); err != nil {
			logger.Errorf(ctx, "Update ConfigMaps kube-system/audit-config failed: %s", err)
			return err
		}

		logger.Infof(ctx, "Update cluster %s success", clusterID)
	}

	return nil
}
