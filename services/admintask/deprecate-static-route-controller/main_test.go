//go:build linux
// +build linux

/*
 * Copyright (c) 2021 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

// CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build .
package main

import (
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/deprecate-static-route-controller/netlinkwrapper"
)

func Test_parseArgs(t *testing.T) {
	tests := []struct {
		name string
	}{
		{
			name: "normal process",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			parseArgs()
		})
	}
}

func Test_doWork(t *testing.T) {
	type args struct {
		nlink netlinkwrapper.Interface
	}
	tests := []struct {
		name string
		args args
	}{
		{
			name: "normal process",
			args: args{
				nlink: &netlinkwrapper.Fake{},
			},
		},
	}
	for _, tt := range tests {
		dryRun = false
		t.Run(tt.name, func(t *testing.T) {
			doWork(tt.args.nlink)
		})
	}
}
