/*
 * Copyright (c) 2021 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package netlinkwrapper

import (
	"net"

	"github.com/vishvananda/netlink"
)

type Fake struct {
}

func (*Fake) LinkAdd(link netlink.Link) error {
	return nil
}

func (*Fake) LinkByName(name string) (netlink.Link, error) {
	return &netlink.Dummy{}, nil
}

func (*Fake) LinkSetNsFd(link netlink.Link, fd int) error {
	return nil
}

func (*Fake) ParseAddr(s string) (*netlink.Addr, error) {
	return nil, nil
}

func (*Fake) AddrAdd(link netlink.Link, addr *netlink.Addr) error {
	return nil
}

func (*Fake) AddrDel(link netlink.Link, addr *netlink.Addr) error {
	return nil
}

func (*Fake) LinkSetUp(link netlink.Link) error {
	return nil
}

func (*Fake) LinkList() ([]netlink.Link, error) {
	return nil, nil
}

func (*Fake) LinkSetDown(link netlink.Link) error {
	return nil
}

func (*Fake) LinkByIndex(index int) (netlink.Link, error) {
	return &netlink.Bond{
		LinkAttrs: netlink.LinkAttrs{
			Index: 100,
		},
	}, nil
}

func (*Fake) RouteList(link netlink.Link, family int) ([]netlink.Route, error) {
	var (
		_, cidr1, _ = net.ParseCIDR("10.113.138.0/23")
		_, cidr2, _ = net.ParseCIDR("100.113.138.0/23")
		_, cidr3, _ = net.ParseCIDR("10.101.4.0/24")
	)

	return []netlink.Route{
		{
			Dst:       nil,
			LinkIndex: 100,
		},
		{
			LinkIndex: 99,
		},
		{
			LinkIndex: 100,
			Dst:       cidr1,
		},
		{
			LinkIndex: 100,
			Dst:       cidr2,
		},
		{
			LinkIndex: 100,
			Dst:       cidr3,
		},
	}, nil
}

func (*Fake) RouteAdd(route *netlink.Route) error {
	return nil
}

func (*Fake) RouteReplace(route *netlink.Route) error {
	return nil
}

func (*Fake) RouteDel(route *netlink.Route) error {
	return nil
}

func (*Fake) RouteListFiltered(family int, filter *netlink.Route, filterMask uint64) ([]netlink.Route, error) {
	return nil, nil
}

func (*Fake) AddrList(link netlink.Link, family int) ([]netlink.Addr, error) {
	return []netlink.Addr{
		{IPNet: &net.IPNet{
			IP:   net.ParseIP("*******"),
			Mask: net.CIDRMask(32, 32),
		}},
	}, nil
}

func (*Fake) NeighAdd(neigh *netlink.Neigh) error {
	return nil
}

func (*Fake) LinkDel(link netlink.Link) error {
	return nil
}

func (*Fake) NewRule() *netlink.Rule {
	return nil
}

func (*Fake) RuleAdd(rule *netlink.Rule) error {
	return nil
}

func (*Fake) RuleDel(rule *netlink.Rule) error {
	return nil
}

func (*Fake) RuleList(family int) ([]netlink.Rule, error) {
	return nil, nil
}

func (*Fake) LinkSetMTU(link netlink.Link, mtu int) error {
	return nil
}
