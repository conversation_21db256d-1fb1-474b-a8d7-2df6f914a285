// +build linux

/*
 * Copyright (c) 2021 Baidu, Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License"); you may not use this file
 * except in compliance with the License. You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under the
 * License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,
 * either express or implied. See the License for the specific language governing permissions
 * and limitations under the License.
 *
 */

package main

import (
	"flag"
	"fmt"
	"log"
	"net"

	gocidr "github.com/apparentlymart/go-cidr/cidr"
	"github.com/vishvananda/netlink"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/admintask/deprecate-static-route-controller/netlinkwrapper"
)

var (
	containerNetworkCIDR = "**********/12"
	dryRun               bool
)

var (
	ipFamily = netlink.FAMILY_V4
)

func parseArgs() {
	flag.StringVar(&containerNetworkCIDR, "container-cidr", "**********/12", "container cidr")
	flag.BoolVar(&dryRun, "dry-run", true, "dry run")

	flag.Parse()
}

// CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build .
func main() {
	var (
		nlink netlinkwrapper.Interface = netlinkwrapper.New()
	)

	parseArgs()

	doWork(nlink)
}

func doWork(nlink netlinkwrapper.Interface) {
	intf, err := detectDefaultRouteInterfaceName(nlink)
	if err != nil {
		log.Fatalf("detectDefaultRouteInterfaceName error %v", err)
	}

	log.Printf("detectDefaultRouteInterfaceName interface: %v", intf.Attrs().Name)

	routes, err := nlink.RouteList(nil, ipFamily)
	if err != nil {
		log.Fatalf("RouteList error %v", err)
	}

	_, containerCIDR, err := net.ParseCIDR(containerNetworkCIDR)
	if err != nil {
		log.Fatalf("parse containerNetworkCIDR %v error %v", containerNetworkCIDR, err)
	}
	log.Printf("containerCIDR: %v", containerCIDR.String())

	for _, rt := range routes {
		if rt.LinkIndex == intf.Attrs().Index && rt.Dst != nil && managedByCni(rt.Dst, containerCIDR) {
			if !dryRun {
				err := nlink.RouteDel(&rt)
				if err != nil {
					log.Printf("delete route %v error %v", rt.String(), err)
				}
			}

			log.Printf("delete route %v successfully", rt.String())
		}
	}
}

func managedByCni(dst *net.IPNet, containerCIDR *net.IPNet) bool {
	first, last := gocidr.AddressRange(dst)

	if containerCIDR.Contains(first) && containerCIDR.Contains(last) {
		return true
	}

	return false
}

func detectDefaultRouteInterfaceName(nlink netlinkwrapper.Interface) (netlink.Link, error) {
	routes, err := nlink.RouteList(nil, ipFamily)
	if err != nil {
		return nil, err
	}

	for _, v := range routes {
		if v.Dst == nil {
			l, err := nlink.LinkByIndex(v.LinkIndex)
			if err != nil {
				return nil, err
			}
			return l, nil
		}
	}

	return nil, fmt.Errorf("no default route interface found")
}
