apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cce-upgrade-ingress-controller
rules:
  - apiGroups:
      - cce.baidubce.com
    resources:
      - workflows
    verbs:
      - create
      - get
      - list
      - watch
      - patch
      - update
      - delete
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: cce-upgrade
  name: cce-upgrade-ingress-controller
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cce-upgrade-ingress-controller-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cce-upgrade-ingress-controller
subjects:
  - kind: ServiceAccount
    namespace: cce-upgrade
    name: cce-upgrade-ingress-controller
---
apiVersion: v1
kind: Secret
metadata:
  name: ccr-registry-secret
  namespace: cce-upgrade
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ************************************************************************************************************************************************************************************************
---
apiVersion: v1
kind: ConfigMap
metadata:
  namespace: cce-upgrade
  name: cce-upgrade-cce-ingress-controller-clusters
data:
  input.txt: |
    cce-7e3yivrf
---
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-upgrade-cce-ingress-controller-********
  namespace: cce-upgrade
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    spec:
      hostNetwork: true
      restartPolicy: Never
      serviceAccount: cce-upgrade-lb-controller
      imagePullSecrets:
        - name: ccr-registry-secret
      volumes:
        - configMap:
            defaultMode: 420
            name: cce-upgrade-cce-ingress-controller-clusters
          name: cce-upgrade-cce-ingress-controller-clusters
      containers:
        - name: cce-upgrade-ingress-controller
          image: registry.baidubce.com/cce-service-dev/upgrade-ingress-controller:jichao
          imagePullPolicy: Always
          volumeMounts:
            - mountPath: /home/<USER>/cce/config
              name: cce-upgrade-cce-ingress-controller-clusters
          args:
            - /upgrade-ingress-controller
            - --region=gztest
            - --target-image=registry.baidubce.com/cce-plugin-pro/cce-ingress-controller:2022.06.22.1641
            - --clusterid-file-path=/home/<USER>/cce/config/input.txt


