[Unit]
Description=Kubernetes Extender Scheduler
After=network.target
After=kube-apiserver.service
After=kube-scheduler.service

[Service]
Environment=KUBECONFIG=/etc/kubernetes/scheduler.conf

ExecStart=/opt/kube/bin/kube-extender-scheduler \
--logtostderr \
--mps=false  \
--core=100  \
--health-check=true \
--memory-unit=GiB \
--mem-quota-env-name=GPU_MEMORY \
--compute-quota-env-name=GPU_COMPUTATION \
--v=6
Restart=always
Type=simple
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target