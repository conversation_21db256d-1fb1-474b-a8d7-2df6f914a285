{"kind": "Policy", "apiVersion": "v1", "predicates": [{"name": "PodFitsHostPorts"}, {"name": "PodFitsResources"}, {"name": "NoDiskConflict"}, {"name": "CheckVolumeBinding"}, {"name": "NoVolumeZoneConflict"}, {"name": "MatchNodeSelector"}, {"name": "HostName"}], "priorities": [{"name": "LeastRequestedPriority", "weight": 1}, {"name": "BalancedResourceAllocation", "weight": 1}, {"name": "ServiceSpreadingPriority", "weight": 1}, {"name": "EqualPriority", "weight": 1}], "extenders": [{"urlPrefix": "http://127.0.0.1:39999/gpushare-scheduler", "filterVerb": "filter", "bindVerb": "bind", "enableHttps": false, "nodeCacheCapable": true, "ignorable": false, "managedResources": [{"name": "baidu.com/v100_cgpu_memory", "ignoredByScheduler": false}, {"name": "baidu.com/v100_cgpu_core", "ignoredByScheduler": false}, {"name": "baidu.com/k40_cgpu_memory", "ignoredByScheduler": false}, {"name": "baidu.com/k40_cgpu_core", "ignoredByScheduler": false}, {"name": "baidu.com/p40_cgpu_memory", "ignoredByScheduler": false}, {"name": "baidu.com/p40_cgpu_core", "ignoredByScheduler": false}, {"name": "baidu.com/p4_cgpu_memory", "ignoredByScheduler": false}, {"name": "baidu.com/p4_cgpu_core", "ignoredByScheduler": false}]}], "hardPodAffinitySymmetricWeight": 10}