---
# RBAC authn and authz
apiVersion: v1
kind: ServiceAccount
metadata:
  name: cce-gpushare-device-plugin
  namespace: kube-system
  labels:
    k8s-app: cce-gpushare-device-plugin
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile

---
kind: ClusterRole
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: cce-gpushare-device-plugin
  labels:
    k8s-app: cce-gpushare-device-plugin
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
    verbs:
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - patch
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - update
      - patch
      - get
      - list
      - watch
  - apiGroups:
      - ""
    resources:
      - nodes/status
    verbs:
      - patch
      - update

---
kind: ClusterRoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  namespace: kube-system
  name: cce-gpushare-device-plugin
  labels:
    k8s-app: cce-gpushare-device-plugin
    kubernetes.io/cluster-service: "true"
    addonmanager.kubernetes.io/mode: Reconcile
subjects:
  - kind: ServiceAccount
    name: cce-gpushare-device-plugin
    namespace: kube-system
    apiGroup: ""
roleRef:
  kind: ClusterRole
  name: cce-gpushare-device-plugin
  apiGroup: ""

---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  namespace: kube-system
  name: cce-gpushare-device-plugin
  labels:
    app: cce-gpushare-device-plugin
spec:
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app: cce-gpushare-device-plugin
  template:
    metadata:
      labels:
        app: cce-gpushare-device-plugin
    spec:
      serviceAccountName: cce-gpushare-device-plugin
      nodeSelector:
        beta.kubernetes.io/instance-type: GPU
      containers:
        - name: cce-gpushare-device-plugin
          image: registry.baidubce.com/jpaas-public/cce-nvidia-share-device-plugin:v0
          imagePullPolicy: Always
          args:
            - --logtostderr
            - --mps=false
            - --core=100
            - --health-check=true
            - --memory-unit=GiB
            - --mem-quota-env-name=GPU_MEMORY
            - --compute-quota-env-name=GPU_COMPUTATION
            - --gpu-type=baidu.com/gpu_k40_4,baidu.com/gpu_k40_16,baidu.com/gpu_p40_8,baidu.com/gpu_v100_8,baidu.com/gpu_p4_4
            - --v=1
          resources:
            limits:
              memory: "300Mi"
              cpu: "1"
            requests:
              memory: "300Mi"
              cpu: "1"
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop: ["ALL"]
          volumeMounts:
            - name: device-plugin
              mountPath: /var/lib/kubelet/device-plugins
      volumes:
        - name: device-plugin
          hostPath:
            path: /var/lib/kubelet/device-plugins
      dnsPolicy: ClusterFirst
      hostNetwork: true
      restartPolicy: Always