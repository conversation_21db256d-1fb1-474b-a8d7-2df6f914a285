// Copyright (c) 2017, NVIDIA CORPORATION. All rights reserved.

package main

import (
	"fmt"
	"log"
	"os/exec"
	"strings"

	"github.com/NVIDIA/gpu-monitoring-tools/bindings/go/nvml"
	pluginapi "k8s.io/kubelet/pkg/apis/deviceplugin/v1beta1"

	"golang.org/x/net/context"
)

func check(err error) {
	if err != nil {
		log.Panicln("Fatal:", err)
	}
}

func initXPU() error {
	log.Println("Init xpu")
	return nil
}

func exeSysCommand(cmdStr string) string {
	cmd := exec.Command("sh", "-c", cmdStr)
	opBytes, err := cmd.Output()
	if err != nil {
		fmt.Println(err)
		return ""
	}
	return string(opBytes)
}

func getDeviceCount() ([]string, error) {
	var result []string

	device := exeSysCommand("ls /dev | grep xpu")
	deviceArray := strings.Split(device, "\n")
	if len(deviceArray) > 0 {
		for _, d := range deviceArray {
			if strings.Contains(d, "xpu") && !strings.Contains(d, "ctrl") {
				result = append(result, d)
			}
		}
	}
	return result, nil
}

func getDeviceNames(pciInfo string) []string {
	device := exeSysCommand(fmt.Sprintf("ls /sys/bus/pci/devices/0000:%s/xpu", pciInfo))
	deviceArray := strings.Split(device, "\n")
	var result []string
	for i := range deviceArray {
		if strings.Contains(deviceArray[i], "xpu") {
			result = append(result, deviceArray[i])
		}
	}
	return result
}

func getDeviceInfo() (map[string][]string, error) {
	count := 0
	result := make(map[string][]string)
	infos := exeSysCommand("lspci | grep 1d22")
	infosArray := strings.Split(infos, "\n")
	for _, info := range infosArray {
		var pciInfo, vendorID, deviceID string
		infoArray := strings.Split(info, " ")
		pciInfo = infoArray[0]
		for i := 1; i < len(infoArray); i++ {
			if strings.Contains(infoArray[i], "Device") {
				IDs := strings.Split(infoArray[i+1], ":")
				if len(IDs) == 2 {
					vendorID = IDs[0]
					deviceID = IDs[1]
				}
			}
		}
		if pciInfo == "" || vendorID == "" || deviceID == "" {
			continue
		}
		key := makeMapKey(pciInfo, vendorID, deviceID)
		value := getDeviceNames(pciInfo)
		result[key] = value
		count = count + len(value)
		log.Printf("getDeviceInfo: key %s device %s \n", key, result[key])
	}
	device, err := getDeviceCount()
	check(err)
	if count != len(device) {
		log.Printf("getDevices len :%d count %d", len(device), count)
	}
	return result, nil
}

func makeMapKey(pciInfo, vendorID, deviceID string) string {
	return fmt.Sprintf("%s-%s-%s", pciInfo, vendorID, deviceID)
}

func makeDeviceUUID(pciVenderDeviceKey, deviceName string, i int) string {
	uuid := fmt.Sprintf("%s-%s-%d", pciVenderDeviceKey, deviceName, i)
	return uuid
}

func getDevices() []*pluginapi.Device {
	device, err := getDeviceInfo()
	check(err)
	var devs []*pluginapi.Device
	for k, v := range device {
		for i := 0; i < len(v); i++ {
			devs = append(devs, &pluginapi.Device{
				ID:     makeDeviceUUID(k, v[i], i),
				Health: pluginapi.Healthy,
			})
		}
	}

	return devs
}

func deviceExists(devs []*pluginapi.Device, id string) bool {
	for _, d := range devs {
		if d.ID == id {
			return true
		}
	}
	return false
}

func watchXIDs(ctx context.Context, devs []*pluginapi.Device, xids chan<- *pluginapi.Device) {
	eventSet := nvml.NewEventSet()
	defer nvml.DeleteEventSet(eventSet)

	for _, d := range devs {
		err := nvml.RegisterEventForDevice(eventSet, nvml.XidCriticalError, d.ID)
		if err != nil && strings.HasSuffix(err.Error(), "Not Supported") {
			log.Printf("Warning: %s is too old to support healthchecking: %s. Marking it unhealthy.", d.ID, err)

			xids <- d
			continue
		}

		if err != nil {
			log.Panicln("Fatal:", err)
		}
	}

	for {
		select {
		case <-ctx.Done():
			return
		default:
		}

		e, err := nvml.WaitForEvent(eventSet, 5000)
		if err != nil && e.Etype != nvml.XidCriticalError {
			continue
		}

		// FIXME: formalize the full list and document it.
		// http://docs.nvidia.com/deploy/xid-errors/index.html#topic_4
		// Application errors: the GPU should still be healthy
		if e.Edata == 31 || e.Edata == 43 || e.Edata == 45 {
			continue
		}

		if e.UUID == nil || len(*e.UUID) == 0 {
			// All devices are unhealthy
			for _, d := range devs {
				xids <- d
			}
			continue
		}

		for _, d := range devs {
			if d.ID == *e.UUID {
				xids <- d
			}
		}
	}
}
