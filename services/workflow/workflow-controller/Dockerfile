# 打镜像
FROM registry.baidubce.com/cce-service-pro/cce-base:v1.0.0

WORKDIR /home/<USER>/cce/cce-workflow-controller/

RUN mkdir -p /home/<USER>/cce/cce-workflow-controller/ && \
mkdir -p /home/<USER>/cce/cce-workflow-controller/conf && \
mkdir -p /home/<USER>/cce/cce-workflow-controller/logs && \
mkdir -p /home/<USER>/cce/plugins/temp_kube_config && \
mkdir -p /home/<USER>/cce/plugins/temp_helm_values

# 设置时区
ENV TZ=Asia/Shanghai

# 依赖二进制在 /cce/bin/ 目录
ENV PATH=${PATH}:/cce/bin/

COPY  cce-workflow-controller  /home/<USER>/cce/cce-workflow-controller/

ENTRYPOINT ["/home/<USER>/cce/cce-workflow-controller/cce-workflow-controller"]
