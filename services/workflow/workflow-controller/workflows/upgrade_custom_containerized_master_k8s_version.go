// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/10 14:06:00, by <EMAIL>, create
*/

package workflows

import (
	"context"
	"fmt"
	"sort"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/addon"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	taskscheck "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/check"
	taskscluster "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/cluster"
	tasksmaster "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/master"
	tasksplugin "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/plugin"
)

const (
	// WorkflowTypeUpgradeCustomContainerizedMasterK8SVersion - 没啥用
	WorkflowTypeUpgradeCustomContainerizedMasterK8SVersion ccetypes.WorkflowType = "UpgradeCustomContainerizedMasterK8SVersion"
)

var _ Interface = &upgradeCustomContainerizedMasterK8SVersionWorkflow{}

type upgradeCustomContainerizedMasterK8SVersionWorkflow struct {
	*baseWorkflow

	config *ccetypes.UpgradeMasterK8SVersionWorkflowConfig
}

func newUpgradeCustomContainerizedMasterK8SVersionWorkflow(ctx context.Context, taskConfig *tasks.TaskBaseConfig,
	config *ccetypes.UpgradeMasterK8SVersionWorkflowConfig) (*upgradeCustomContainerizedMasterK8SVersionWorkflow, error) {
	// 初始化 BaseWorkflow
	base, err := newBaseWorkflow(ctx, taskConfig)
	if err != nil {
		logger.Errorf(ctx, "newBaseWorkflow failed: %s", err)
		return nil, err
	}

	// 初始化 UpgradeMasterK8SVersionWorkflowConfig
	if config == nil {
		return nil, fmt.Errorf("UpgradeMasterK8SVersionWorkflowConfig is nil")
	}

	if config.TargetK8SVersion == "" {
		return nil, fmt.Errorf("UpgradeMasterK8SVersionWorkflowConfig.TargetK8SVersion is empty")
	}

	return &upgradeCustomContainerizedMasterK8SVersionWorkflow{
		baseWorkflow: base,
		config:       config,
	}, nil
}

func (w *upgradeCustomContainerizedMasterK8SVersionWorkflow) Name(ctx context.Context) ccetypes.WorkflowType {
	return WorkflowTypeUpgradeCustomContainerizedMasterK8SVersion
}

func (w *upgradeCustomContainerizedMasterK8SVersionWorkflow) PreCheckTaskClientList(ctx context.Context) (
	result []tasks.Interface, err error) {
	return []tasks.Interface{}, nil
}

func (w *upgradeCustomContainerizedMasterK8SVersionWorkflow) BackupTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	taskList := []tasks.Interface{}
	return taskList, nil
}

func (w *upgradeCustomContainerizedMasterK8SVersionWorkflow) OperationTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	taskList := []tasks.Interface{}
	clusterID := w.TaskBaseConfig.Cluster.Spec.ClusterID

	// 生成 Master 升级 Tasks
	masterList, err := w.TaskBaseConfig.Clients.MetaClient.ListClusterMasters(ctx, consts.MetaClusterDefaultNamespace, clusterID)
	if err != nil {
		logger.Errorf(ctx, "ListClusterMasters failed: %s", err)
		return nil, err
	}

	if len(masterList.Items) < 1 {
		return taskList, fmt.Errorf("cluster %s len(masterList) < 1", clusterID)
	}

	sort.Slice(masterList.Items, func(i, j int) bool {
		return masterList.Items[i].Name < masterList.Items[j].Name
	})
	for _, master := range masterList.Items {
		masterCCEInstanceID := master.GetName()

		// Password
		password, ok := w.config.MasterPasswords[masterCCEInstanceID]
		if !ok {
			logger.Warnf(ctx, "Master %s not set password, use default instead", masterCCEInstanceID)
			password = master.Spec.AdminPassword
		}

		// K8SNodeName
		k8sNodeName := utils.GetNodeName(ctx, master.Status.Machine.VPCIP, master.Status.Machine.Hostname,
			master.Spec.InstanceName,
			w.TaskBaseConfig.Cluster.Spec.K8SCustomConfig.EnableHostname,
		)
		if k8sNodeName == "" {
			logger.Errorf(ctx, "GetNodeName %s failed: nodeName is empty, check instance status", masterCCEInstanceID)
			return nil, fmt.Errorf("GetNodeName %s failed: nodeName is empty, check instance status", masterCCEInstanceID)
		}

		// 初始化 Task
		task, err := tasksmaster.NewUpgradeContainerziedMasterK8SVersion(ctx, w.TaskBaseConfig,
			&tasksmaster.UpgradeContainerizedMasterK8SVersionConfig{
				MasterCCEInstanceID: masterCCEInstanceID,
				ToK8SVersion:        w.config.TargetK8SVersion,
				Password:            password,
				K8SNodeName:         k8sNodeName,
			},
		)
		if err != nil {
			logger.Errorf(ctx, "NewUpgradeContainerizedMasterK8SVersion failed: %s", err)
			return nil, err
		}

		taskList = append(taskList, task)
	}

	// Plugin 升级
	pluginTaskList, err := w.pluginOperationTaskList(ctx)
	if err != nil {
		logger.Errorf(ctx, "pluginOperationTaskList failed: %s", err)
		return taskList, err
	}
	taskList = append(taskList, pluginTaskList...)

	// 修改 Cluster K8SVersion Task
	upgradeK8SVersionTask, err := taskscluster.NewUpgradeClusterK8SVersion(ctx, w.TaskBaseConfig,
		&taskscluster.UpgradeClusterK8SVersionConfig{
			From: w.TaskBaseConfig.Cluster.Spec.K8SVersion,
			To:   w.config.TargetK8SVersion,
		},
	)
	if err != nil {
		logger.Errorf(ctx, "NewUpgradeClusterK8SVersion failed: %s", err)
		return nil, err
	}
	taskList = append(taskList, upgradeK8SVersionTask)

	return taskList, nil
}

func (w *upgradeCustomContainerizedMasterK8SVersionWorkflow) PostCheckTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	taskList := []tasks.Interface{}

	// 检查容器化 Master 状态
	checkMasterStatusTask, err := taskscheck.NewCheckContainerizedMasterStatus(ctx, w.TaskBaseConfig,
		&taskscheck.CheckContainerizedMasterStatusConfig{
			TargetK8SVersion: w.config.TargetK8SVersion,
		})
	if err != nil {
		logger.Errorf(ctx, "NewCheckContainerizedMasterStatus failed: %s", err)
		return taskList, err
	}
	taskList = append(taskList, checkMasterStatusTask)

	return taskList, nil
}

// pluginOperationTaskList - 插件升级
func (w *upgradeCustomContainerizedMasterK8SVersionWorkflow) pluginOperationTaskList(ctx context.Context) ([]tasks.Interface, error) {
	taskList := []tasks.Interface{}

	// KubeProxy 升级 Task
	// 开启eBPF增强，不用升级kubeproxy
	if !w.baseWorkflow.TaskBaseConfig.Cluster.Spec.ContainerNetworkConfig.EBPFConfig.Enabled {
		kubeProxyTask, err := tasksplugin.NewUpgradeKubeProxy(ctx, w.TaskBaseConfig,
			&tasksplugin.UpgradeKubeProxyConfig{
				ToK8SVersion: w.config.TargetK8SVersion,
			},
		)
		if err != nil {
			logger.Errorf(ctx, "NewUpgradeKubeProxy failed: %s", err)
			return nil, err
		}
		taskList = append(taskList, kubeProxyTask)
	}

	if isAfterOrEqual1_22, _ := w.config.TargetK8SVersion.IsAfterOrEqual(ccetypes.K8S_1_22_5); isAfterOrEqual1_22 {
		metricsServerTask, err := tasksplugin.NewUpgradeMetricsServer(ctx, w.TaskBaseConfig,
			&ccetypes.UpgradeMetricsServerConfig{
				PluginName: string(addon.AddonMetricsServerV2),
			})
		if err != nil {
			logger.Errorf(ctx, "NewUpgradeMetricsServer failed: %s", err)
			return nil, err
		}
		taskList = append(taskList, metricsServerTask)

		ksmTask, err := tasksplugin.NewUpgradeKubeStateMetrics(ctx, w.TaskBaseConfig, &ccetypes.UpgradeKubeStateMetricsConfig{
			PluginName: string(addon.AddonKubeStateMetrics),
		})
		if err != nil {
			logger.Errorf(ctx, "NewUpgradeKubeStateMetrics failed: %s", err)
			return nil, err
		}

		taskList = append(taskList, ksmTask)
	}

	return taskList, nil
}
