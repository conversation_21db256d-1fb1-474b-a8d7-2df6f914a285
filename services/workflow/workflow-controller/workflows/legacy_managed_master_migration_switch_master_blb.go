package workflows

import (
	"context"
	"fmt"

	master_migration "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/master-migration"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
)

var _ Interface = &LegacyManagedMasterMigrationSwitchMasterBLB{}

type LegacyManagedMasterMigrationSwitchMasterBLB struct {
	*baseWorkflow
	workflowConfig *ccetypes.LegacyMasterMigrationConfig
}

func newLegacyManagedMasterMigrationSwitchMasterBLB(ctx context.Context, taskConfig *tasks.TaskBaseConfig, workflowConfig *ccetypes.LegacyMasterMigrationConfig) (Interface, error) {
	if workflowConfig == nil {
		return nil, fmt.Errorf("workflowConfig is nil")
	}

	base, err := newBaseWorkflow(ctx, taskConfig)
	if err != nil {
		logger.Errorf(ctx, "newBaseWorkflow failed: %s", err)
		return nil, err
	}

	return &LegacyManagedMasterMigrationSwitchMasterBLB{
		baseWorkflow:   base,
		workflowConfig: workflowConfig,
	}, nil
}

func (workflow LegacyManagedMasterMigrationSwitchMasterBLB) Name(ctx context.Context) ccetypes.WorkflowType {
	return ccetypes.WorkflowTypeLegacyManagedMasterMigrationSwitchMasterBLB
}

func (workflow *LegacyManagedMasterMigrationSwitchMasterBLB) OperationTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	var taskList []tasks.Interface

	if workflow.TaskBaseConfig.Cluster != nil && (workflow.TaskBaseConfig.Cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeManaged || workflow.TaskBaseConfig.Cluster.Spec.MasterConfig.MasterType == ccetypes.MasterTypeManagedOld) {
		// 1. 清空普通型 BLB 内容并切换成应用型 BLB
		cleanNormalBLB, err := master_migration.NewLegacyMasterMigrationCleanNormalBLB(ctx, workflow.TaskBaseConfig, workflow.baseWorkflow.PluginConfig, workflow.workflowConfig)
		if err != nil {
			logger.Errorf(ctx, "NewLegacyMasterMigrationCleanNormalBLB task err: %v", err)
			return taskList, err
		}
		taskList = append(taskList, cleanNormalBLB)
	}

	// 2. 创建 Master BLB Service
	deployMasterBLBService, err := master_migration.NewLegacyMasterMigrationDeployMasterBLBService(ctx, workflow.TaskBaseConfig, workflow.baseWorkflow.PluginConfig, workflow.workflowConfig)
	if err != nil {
		logger.Errorf(ctx, "NewLegacyMasterMigrationDeployMasterBLBService task err: %v", err)
		return taskList, err
	}
	taskList = append(taskList, deployMasterBLBService)

	return taskList, nil
}
