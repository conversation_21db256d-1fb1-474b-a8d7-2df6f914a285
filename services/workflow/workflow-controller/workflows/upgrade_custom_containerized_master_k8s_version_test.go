// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/10 14:06:00, by <EMAIL>, create
*/

package workflows

import (
	"context"
	"testing"

	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
)

func Test_upgradeCustomContainerizedMasterK8SVersionWorkflow_pluginOperationTaskList(t *testing.T) {
	type fields struct {
		baseWorkflow *baseWorkflow
		config       *ccetypes.UpgradeMasterK8SVersionWorkflowConfig
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []tasks.Interface
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "ebpf enable",
			fields: fields{
				config: &ccetypes.UpgradeMasterK8SVersionWorkflowConfig{},
				baseWorkflow: &baseWorkflow{
					&tasks.TaskBaseConfig{
						Cluster: &v1.Cluster{
							Spec: ccetypes.ClusterSpec{
								ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
									EBPFConfig: ccetypes.EBPFConfiuration{
										Enabled: true,
									},
								},
							},
						},
					},
				},
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    []tasks.Interface{},
			wantErr: false,
		},
		{
			name: "kube proxy, NewUpgradeKubeProxy failed",
			fields: fields{
				config: &ccetypes.UpgradeMasterK8SVersionWorkflowConfig{
					TargetK8SVersion: ccetypes.K8S_1_22_5,
				},
				baseWorkflow: &baseWorkflow{
					&tasks.TaskBaseConfig{
						Cluster: &v1.Cluster{
							Spec: ccetypes.ClusterSpec{
								ContainerNetworkConfig: ccetypes.ContainerNetworkConfig{
									EBPFConfig: ccetypes.EBPFConfiuration{},
								},
							},
						},
					},
				},
			},
			args: args{
				ctx: context.TODO(),
			},
			want:    []tasks.Interface{},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &upgradeCustomContainerizedMasterK8SVersionWorkflow{
				baseWorkflow: tt.fields.baseWorkflow,
				config:       tt.fields.config,
			}
			_, err := w.pluginOperationTaskList(tt.args.ctx)
			if (err != nil) != tt.wantErr {
				t.Errorf("upgradeCustomContainerizedMasterK8SVersionWorkflow.pluginOperationTaskList() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
		})
	}
}
