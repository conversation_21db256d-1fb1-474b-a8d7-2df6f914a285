package workflows

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	apiv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	dptypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
)

func Test_newUpgradeCCECloudNodeControllerWorkflow(t *testing.T) {
	tests := []struct {
		name       string
		taskConfig *tasks.TaskBaseConfig
		config     *ccetypes.UpgradeCCECloudNodeControllerConfig
		wantErr    bool
	}{
		{
			name:       "task config is nil",
			taskConfig: nil,
			config:     &ccetypes.UpgradeCCECloudNodeControllerConfig{},
			wantErr:    true,
		},
		{
			name: "plugin config is nil",
			taskConfig: &tasks.TaskBaseConfig{
				Cluster:        &apiv1.Cluster{},
				DeployerConfig: &dptypes.Config{},
				Clients:        &clientset.ClientSet{},
			},
			config:  nil,
			wantErr: true,
		},
		{
			name: "normal",
			taskConfig: &tasks.TaskBaseConfig{
				Cluster:        &apiv1.Cluster{},
				DeployerConfig: &dptypes.Config{},
				Clients:        &clientset.ClientSet{},
			},
			config:  &ccetypes.UpgradeCCECloudNodeControllerConfig{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := newUpgradeCCECloudNodeControllerWorkflow(context.TODO(), tt.taskConfig, tt.config); (err != nil) != tt.wantErr {
				t.Errorf("newUpgradeCCELBControllerWorkflow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUpgradeCCECloudNodeControllerWorkflow_Name(t *testing.T) {
	workflow := &upgradeCCECloudNodeControllerWorkflow{}
	tests := []struct {
		name   string
		expect ccetypes.WorkflowType
	}{
		{
			name:   "normal",
			expect: ccetypes.WorkflowTypeUpgradeCCECloudNodeController,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := workflow.Name(context.TODO()); got != tt.expect {
				t.Errorf("Name() got = %s, want %vs", got, tt.expect)
			}
		})
	}
}

func TestUpgradeCCECloudNodeControllerWorkflow_OperationTaskClientList(t *testing.T) {
	tests := []struct {
		name     string
		workflow *upgradeCCECloudNodeControllerWorkflow
		wantErr  bool
	}{
		{
			name: "task config is nil",
			workflow: &upgradeCCECloudNodeControllerWorkflow{
				baseWorkflow: &baseWorkflow{
					TaskBaseConfig: nil,
				},
				config: &ccetypes.UpgradeCCECloudNodeControllerConfig{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := tt.workflow.OperationTaskClientList(context.TODO()); (err != nil) != tt.wantErr {
				t.Errorf("OperationTaskClientList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
