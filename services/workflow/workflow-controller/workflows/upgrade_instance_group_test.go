package workflows

import (
	"context"
	"strings"
	"testing"

	"github.com/agiledragon/gomonkey/v2"
	"github.com/golang/mock/gomock"
	"github.com/smartystreets/goconvey/convey"
	"gotest.tools/assert"
	coreV1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	taskscheck "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/check"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/instancegroup"
)

func Test_checkInstanceUpgradeConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name          string
		wantErr       string
		instance      *models.Instance
		config        *ccetypes.UpgradeNodesWorkflowConfig
		cluster       *v1.Cluster
		instanceCrd   *ccev1.Instance
		wantErrString string
	}{
		{
			name:          "instance is nil",
			wantErr:       "instance is nil",
			wantErrString: "",
			instanceCrd: &ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					NvidiaContainerToolkitVersion: "1.9.0",
				},
			},
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			client := fake.NewSimpleClientset()
			_, err := client.CoreV1().Nodes().Create(context.TODO(), &coreV1.Node{
				ObjectMeta: metav1.ObjectMeta{
					Name: "1.1.1.1",
				},
				Status: coreV1.NodeStatus{
					NodeInfo: coreV1.NodeSystemInfo{
						KubeletVersion:          "1.28.8",
						ContainerRuntimeVersion: "containerd://1.6.28",
					},
				},
			}, metav1.CreateOptions{})
			result, err := checkInstanceUpgradeConfig(context.TODO(), tt.instance, coreV1.Node{}, tt.config, tt.cluster,
				tt.instanceCrd)
			if tt.wantErr != "" {
				assert.Check(t, err != nil)
				assert.Equal(t, err.Error(), tt.wantErr)
			}
			assert.Equal(t, result, tt.wantErrString)
		})
	}
}

func Test_PostCheckTaskClientList(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	testInfos := []struct {
		name          string
		wantErr       error
		components    []ccetypes.Component
		instanceGroup *ccev1.InstanceGroup
		wantErrString string
	}{
		{
			name: "upgrade runtime to instanceGroup",
			components: []ccetypes.Component{
				{
					Name:          ccetypes.ComponentContainerRuntime,
					TargetVersion: "containerd://1.6.28",
				},
				{
					Name:          ccetypes.ComponentNvidiaContainerToolkit,
					TargetVersion: string(ccetypes.Toolkit_1_9_0),
				},
			},
			instanceGroup: &ccev1.InstanceGroup{
				ObjectMeta: metav1.ObjectMeta{
					Name: "instance-group-1",
				},
				Spec: ccetypes.InstanceGroupSpec{
					InstanceGroupName: "group-id",
					InstanceTemplate:  ccetypes.InstanceTemplate{},
					InstanceTemplates: []ccetypes.InstanceTemplate{{
						InstanceSpec: ccetypes.InstanceSpec{
							RuntimeType: "1",
						},
					}},
				},
			},
			wantErr: nil,
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			mockMeta := metamock.NewMockInterface(ctrl)

			mockMeta.EXPECT().GetInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(tt.instanceGroup, nil).AnyTimes()
			mockMeta.EXPECT().UpdateInstanceGroup(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, nil).AnyTimes()
			workflow := &upgradeNodesWorkflow{
				config: &ccetypes.UpgradeNodesWorkflowConfig{
					InstanceGroupID: "instance-group-1",
					Components:      tt.components,
				},
				baseWorkflow: &baseWorkflow{
					TaskBaseConfig: &tasks.TaskBaseConfig{
						Clients: &clientset.ClientSet{
							Clients: &clientset.Clients{
								MetaClient: mockMeta,
							},
						},
					},
				},
			}
			_, err := workflow.PostCheckTaskClientList(context.TODO())
			assert.Equal(t, err, tt.wantErr)

		})
	}
}

func Test_CheckUpgradeNodesWorkflowConfig(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.TODO()
	metaClient := metamock.NewMockInterface(ctrl)
	modelClient := models.NewMockInterface(ctrl)
	k8sClient := fake.NewSimpleClientset()

	instanceId := "mock-instance-id"
	instanceGroupId := "mock-instance-group-id"
	vpcIp := "mock-vpc-ip"
	nodeName := vpcIp
	hostName := "mock-host-name"
	clusterId := "mock-cluster-id"
	metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.Instance{
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID:                 "mock-instance-id",
			NvidiaContainerToolkitVersion: "1.9.0",
		}}, nil)
	modelClient.EXPECT().GetInstances(ctx, gomock.Any(), gomock.Any()).Return([]*models.Instance{
		{
			Spec: &ccetypes.InstanceSpec{
				CCEInstanceID:                 instanceId,
				InstanceGroupID:               instanceGroupId,
				ClusterID:                     clusterId,
				NvidiaContainerToolkitVersion: "1.9.0",
			},
			Status: &ccetypes.InstanceStatus{
				Machine: ccetypes.Machine{
					VPCIP:    vpcIp,
					Hostname: hostName,
				},
			},
		},
	}, nil)

	_, _ = k8sClient.CoreV1().Nodes().Create(ctx, &coreV1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: nodeName,
		},
		Status: coreV1.NodeStatus{
			NodeInfo: coreV1.NodeSystemInfo{
				KubeletVersion:          "1.28.8",
				ContainerRuntimeVersion: "docker://20.10.24",
			},
		},
	}, metav1.CreateOptions{})

	testInfos := []struct {
		name    string
		config  *ccetypes.UpgradeNodesWorkflowConfig
		cluster *v1.Cluster
		reason  string
		errStr  string
	}{
		{
			name: "节点列表和节点组id都为空",
			config: &ccetypes.UpgradeNodesWorkflowConfig{
				Components:        make([]ccetypes.Component, 1),
				CCEInstanceIDList: make([]string, 0),
				InstanceGroupID:   "",
			},
			reason:  "cceInstanceIDList and instanceGroupId is nil",
			cluster: &v1.Cluster{},
		},
		{
			name: "正常流程",
			config: &ccetypes.UpgradeNodesWorkflowConfig{
				Components: []ccetypes.Component{
					{
						Name:          ccetypes.ComponentContainerRuntime,
						TargetVersion: string(ccetypes.Containerd_1_7_25),
					},
					{
						Name:          ccetypes.ComponentKubelet,
						TargetVersion: string(ccetypes.K8S_1_30_1),
					},
				},
				CCEInstanceIDList:    make([]string, 0),
				InstanceGroupID:      instanceGroupId,
				BatchIntervalMinutes: func(i int) *int { return &i }(5),
				PausePolicy:          func(p ccetypes.PausePolicy) *ccetypes.PausePolicy { return &p }(ccetypes.NotPause),
				NodeUpgradeBatchSize: 1,
			},
			cluster: &v1.Cluster{
				Spec: ccetypes.ClusterSpec{
					K8SCustomConfig: ccetypes.K8SCustomConfig{
						EnableHostname: false,
					},
					ClusterID:  clusterId,
					K8SVersion: ccetypes.K8S_1_30_1,
				},
			},
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			reason, err := CheckUpgradeNodesWorkflowConfig(ctx, tt.config, tt.cluster, k8sClient, metaClient, modelClient)
			assert.Equal(t, reason, tt.reason)
			assert.Equal(t, err == nil, tt.errStr == "")
			if err != nil {
				assert.Check(t, strings.Contains(err.Error(), tt.errStr))
			}
		})
	}
}

func Test_CheckUpgradeNodesWorkflowConfig2(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	ctx := context.TODO()
	metaClient := metamock.NewMockInterface(ctrl)
	modelClient := models.NewMockInterface(ctrl)
	k8sClient := fake.NewSimpleClientset()

	instanceId := "mock-instance-id"
	instanceGroupId := "mock-instance-group-id"
	vpcIp := "mock-vpc-ip"
	nodeName := vpcIp
	hostName := "mock-host-name"
	clusterId := "mock-cluster-id"
	metaClient.EXPECT().GetInstance(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).AnyTimes().Return(&ccev1.Instance{
		Spec: ccetypes.InstanceSpec{
			CCEInstanceID:                 "mock-instance-id",
			NvidiaContainerToolkitVersion: "1.9.0",
		}}, nil)
	modelClient.EXPECT().GetInstances(ctx, gomock.Any(), gomock.Any()).Return([]*models.Instance{
		{
			Spec: &ccetypes.InstanceSpec{
				CCEInstanceID:                 instanceId,
				InstanceGroupID:               instanceGroupId,
				ClusterID:                     clusterId,
				NvidiaContainerToolkitVersion: "1.9.0",
			},
			Status: &ccetypes.InstanceStatus{
				Machine: ccetypes.Machine{
					VPCIP:    vpcIp,
					Hostname: hostName,
				},
			},
		},
	}, nil).AnyTimes()

	_, _ = k8sClient.CoreV1().Nodes().Create(ctx, &coreV1.Node{
		ObjectMeta: metav1.ObjectMeta{
			Name: nodeName,
		},
		Status: coreV1.NodeStatus{
			NodeInfo: coreV1.NodeSystemInfo{
				KubeletVersion:          "1.18.9",
				ContainerRuntimeVersion: "docker://20.10.24",
			},
		},
	}, metav1.CreateOptions{})

	testInfos := []struct {
		name    string
		config  *ccetypes.UpgradeNodesWorkflowConfig
		cluster *v1.Cluster
		reason  string
		errStr  string
	}{
		{
			name: "正常流程",
			config: &ccetypes.UpgradeNodesWorkflowConfig{
				Components: []ccetypes.Component{
					{
						Name:          ccetypes.ComponentContainerRuntime,
						TargetVersion: string(ccetypes.Containerd_1_7_25),
					},
					{
						Name:          ccetypes.ComponentKubelet,
						TargetVersion: string(ccetypes.K8S_1_18_9),
					},
				},
				CCEInstanceIDList:    make([]string, 0),
				InstanceGroupID:      instanceGroupId,
				BatchIntervalMinutes: func(i int) *int { return &i }(5),
				PausePolicy:          func(p ccetypes.PausePolicy) *ccetypes.PausePolicy { return &p }(ccetypes.NotPause),
				NodeUpgradeBatchSize: 1,
			},
			cluster: &v1.Cluster{
				Spec: ccetypes.ClusterSpec{
					K8SCustomConfig: ccetypes.K8SCustomConfig{
						EnableHostname: false,
					},
					ClusterID:  clusterId,
					K8SVersion: ccetypes.K8S_1_18_9,
				},
			},
			reason: "kubelet 1.18.9 only support containerd 1.5.4",
		},
	}
	for _, tt := range testInfos {
		t.Run(tt.name, func(t *testing.T) {
			reason, err := CheckUpgradeNodesWorkflowConfig(ctx, tt.config, tt.cluster, k8sClient, metaClient, modelClient)
			assert.Equal(t, reason, tt.reason)
			assert.Equal(t, err == nil, tt.errStr == "")
			if err != nil {
				assert.Check(t, strings.Contains(err.Error(), tt.errStr))
			}
		})
	}
}

func Test_upgradeNodes(t *testing.T) {
	ctx := context.Background()
	ctl := gomock.NewController(t)

	metaClient := metamock.NewMockInterface(ctl)

	w := upgradeNodesWorkflow{
		config: &ccetypes.UpgradeNodesWorkflowConfig{
			CCEInstanceIDList:    []string{"1", "2"},
			NodeUpgradeBatchSize: 1,
			Components: []ccetypes.Component{
				{
					Name:          ccetypes.ComponentContainerRuntime,
					TargetVersion: string(ccetypes.Containerd_1_7_13),
				},
			},
			InstanceIdToNeedDrain: map[string]bool{
				"1": true,
			},
			PausePolicy:          func(p ccetypes.PausePolicy) *ccetypes.PausePolicy { return &p }(ccetypes.NotPause),
			BatchIntervalMinutes: func(i int) *int { return &i }(1),
		},
		baseWorkflow: &baseWorkflow{
			TaskBaseConfig: &tasks.TaskBaseConfig{
				Clients: &clientset.ClientSet{
					Clients: &clientset.Clients{
						MetaClient: metaClient,
					},
				},
			},
		},
	}
	convey.Convey("不暂停", t, func() {
		patches := gomonkey.ApplyFuncReturn(instancegroup.NewDrainTask, &instancegroup.DrainTask{}, nil)
		patches.ApplyFuncReturn(instancegroup.NewUpgradeTask, &instancegroup.UpgradeTask{}, nil)
		patches.ApplyFuncReturn(instancegroup.NewUpdateInstanceTask, &instancegroup.UpdateInstanceTask{}, nil)
		defer patches.Reset()
		_, err := w.OperationTaskClientList(ctx)
		convey.So(err, convey.ShouldBeNil)
	})

	convey.Convey("暂停", t, func() {
		w.config.PausePolicy = func(p ccetypes.PausePolicy) *ccetypes.PausePolicy { return &p }(ccetypes.FirstBatch)
		patches := gomonkey.ApplyFuncReturn(instancegroup.NewDrainTask, &instancegroup.DrainTask{}, nil)
		patches.ApplyFuncReturn(instancegroup.NewUpgradeTask, &instancegroup.UpgradeTask{}, nil)
		patches.ApplyFuncReturn(instancegroup.NewUpdateInstanceTask, &instancegroup.UpdateInstanceTask{}, nil)
		defer patches.Reset()
		_, err := w.OperationTaskClientList(ctx)
		convey.So(err, convey.ShouldBeNil)
	})

	convey.Convey("前置检查", t, func() {
		patches := gomonkey.ApplyFuncReturn(taskscheck.NewCheckNodeStatusNoK8sVersion,
			&taskscheck.CheckNodeStatusNoK8sVersion{}, nil)
		patches.ApplyFuncReturn(taskscheck.NewCheckConflictWorkflowInstanceGroup,
			&taskscheck.CheckConflictWorkflowInstanceGroup{}, nil)
		patches.ApplyFuncReturn(taskscheck.NewCheckNodeSystemStatus, &taskscheck.CheckNodeSystemStatus{}, nil)
		defer patches.Reset()
		metaClient.EXPECT().GetInstance(ctx, "default", gomock.Any(), &metav1.GetOptions{}).Return(
			&ccev1.Instance{
				Spec: ccetypes.InstanceSpec{
					ClusterID:       "cce-other-cluster",
					InstanceGroupID: "group-id",
				},
				Status: ccetypes.InstanceStatus{
					RetryCount: 20,
				},
			}, nil,
		).AnyTimes()
		_, err := w.PreCheckTaskClientList(ctx)
		convey.So(err, convey.ShouldBeNil)
	})

	w.config.CCEInstanceIDList = nil
	convey.Convey("0节点升级，跳过其他升级任务", t, func() {
		//patches := gomonkey.ApplyFuncReturn(instancegroup.NewDrainTask, &instancegroup.DrainTask{}, nil)
		//patches.ApplyFuncReturn(instancegroup.NewUpgradeTask, &instancegroup.UpgradeTask{}, nil)
		//patches.ApplyFuncReturn(instancegroup.NewUpdateInstanceTask, &instancegroup.UpdateInstanceTask{}, nil)
		//defer patches.Reset()
		_, err := w.OperationTaskClientList(ctx)
		convey.So(err, convey.ShouldBeNil)
	})
}

// TestUpgradeNodesWorkflow_PreCheckTaskClientList_GivenZeroNodes_WhenPreCheck_ThenReturnEmptyTaskList 测试0节点升级前置检查
func TestUpgradeNodesWorkflow_PreCheckTaskClientList_GivenZeroNodes_WhenPreCheck_ThenReturnEmptyTaskList(t *testing.T) {
	ctx := context.Background()

	// 准备测试数据 - 0节点配置
	config := &ccetypes.UpgradeNodesWorkflowConfig{
		CCEInstanceIDList: []string{}, // 空节点列表
		InstanceGroupID:   "test-ig-id",
		IsPreCheck:        true,
		Components: []ccetypes.Component{
			{
				Name:          ccetypes.ComponentKubelet,
				TargetVersion: "1.26.9",
			},
		},
	}

	taskConfig := &tasks.TaskBaseConfig{
		ClusterID: "test-cluster",
		AccountID: "test-account",
		Clients:   &clientset.ClientSet{},
		Cluster: &v1.Cluster{
			Spec: ccetypes.ClusterSpec{
				ClusterID: "test-cluster",
			},
		},
	}

	workflow := &upgradeNodesWorkflow{
		config: config,
		baseWorkflow: &baseWorkflow{
			TaskBaseConfig: taskConfig,
		},
	}

	// 执行测试
	taskList, err := workflow.PreCheckTaskClientList(ctx)

	// 验证结果
	assert.NilError(t, err)
	assert.Equal(t, 0, len(taskList), "0节点升级前置检查应该返回空任务列表")
}

// TestUpgradeNodesWorkflow_PreCheckTaskClientList_GivenNonZeroNodes_WhenPreCheck_ThenReturnTaskList 测试非0节点升级前置检查
func TestUpgradeNodesWorkflow_PreCheckTaskClientList_GivenNonZeroNodes_WhenPreCheck_ThenReturnTaskList(t *testing.T) {
	ctx := context.Background()
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// 准备测试数据 - 非0节点配置
	config := &ccetypes.UpgradeNodesWorkflowConfig{
		CCEInstanceIDList: []string{"instance-1", "instance-2"},
		InstanceGroupID:   "test-ig-id",
		IsPreCheck:        true,
		Components: []ccetypes.Component{
			{
				Name:          ccetypes.ComponentKubelet,
				TargetVersion: "1.26.9",
			},
		},
	}

	metaClient := metamock.NewMockInterface(ctrl)
	taskConfig := &tasks.TaskBaseConfig{
		ClusterID: "test-cluster",
		AccountID: "test-account",
		Clients:   &clientset.ClientSet{},
		Cluster: &v1.Cluster{
			Spec: ccetypes.ClusterSpec{
				ClusterID: "test-cluster",
			},
		},
	}

	workflow := &upgradeNodesWorkflow{
		config: config,
		baseWorkflow: &baseWorkflow{
			TaskBaseConfig: taskConfig,
		},
	}

	// Mock 相关函数
	patches := gomonkey.ApplyFuncReturn(taskscheck.NewCheckNodeStatusNoK8sVersion,
		&taskscheck.CheckNodeStatusNoK8sVersion{}, nil)
	patches.ApplyFuncReturn(taskscheck.NewCheckConflictWorkflowInstanceGroup,
		&taskscheck.CheckConflictWorkflowInstanceGroup{}, nil)
	patches.ApplyFuncReturn(taskscheck.NewCheckNodeSystemStatus, &taskscheck.CheckNodeSystemStatus{}, nil)
	defer patches.Reset()

	metaClient.EXPECT().GetInstance(ctx, "default", gomock.Any(), &metav1.GetOptions{}).Return(
		&ccev1.Instance{
			Spec: ccetypes.InstanceSpec{
				ClusterID:       "test-cluster",
				InstanceGroupID: "test-ig-id",
			},
		}, nil,
	).AnyTimes()

	// 执行测试
	taskList, err := workflow.PreCheckTaskClientList(ctx)

	// 验证结果
	assert.NilError(t, err)
	assert.Assert(t, len(taskList) > 0, "非0节点升级前置检查应该返回任务列表")
}

//// TestCheckUpgradeNodesWorkflowConfig_GivenZeroNodes_WhenCheck_ThenSuccess 测试0节点升级配置检查
//func TestCheckUpgradeNodesWorkflowConfig_GivenZeroNodes_WhenCheck_ThenSuccess(t *testing.T) {
//	ctx := context.Background()
//	ctrl := gomock.NewController(t)
//	defer ctrl.Finish()
//
//	// 准备测试数据 - 0节点配置
//	config := &ccetypes.UpgradeNodesWorkflowConfig{
//		CCEInstanceIDList: []string{}, // 空节点列表
//		InstanceGroupID:   "test-ig-id",
//		Components: []ccetypes.Component{
//			{
//				Name:          ccetypes.ComponentKubelet,
//				TargetVersion: "1.26.9",
//			},
//		},
//	}
//
//	cluster := &v1.Cluster{
//		Spec: ccetypes.ClusterSpec{
//			ClusterID: "test-cluster",
//		},
//	}
//
//	k8sClient := fake.NewSimpleClientset()
//	metaClient := metamock.NewMockInterface(ctrl)
//	modelClient := &models.MockInterface{}
//
//	// 执行测试
//	message, err := CheckUpgradeNodesWorkflowConfig(ctx, config, cluster, k8sClient, metaClient, modelClient)
//
//	// 验证结果
//	assert.NilError(t, err)
//	assert.Equal(t, "", message, "0节点升级配置检查应该成功")
//}
//
//// TestCheckInstanceUpgradeConfig_GivenKubeletVersionDowngrade_WhenCheck_ThenReturnError 测试Kubelet版本回退检查
//func TestCheckInstanceUpgradeConfig_GivenKubeletVersionDowngrade_WhenCheck_ThenReturnError(t *testing.T) {
//	ctx := context.Background()
//
//	// 准备测试数据 - 版本回退场景
//	instance := &models.Instance{
//		Spec: &ccetypes.InstanceSpec{
//			CCEInstanceID:   "test-instance",
//			InstanceGroupID: "test-ig-id",
//			ClusterID:       "test-cluster",
//		},
//	}
//
//	k8sNode := coreV1.Node{
//		Status: coreV1.NodeStatus{
//			NodeInfo: coreV1.NodeSystemInfo{
//				KubeletVersion:             "v1.26.9", // 当前版本
//				ContainerRuntimeVersion:    "containerd://1.6.28", // 使用支持的版本
//			},
//		},
//	}
//
//	config := &ccetypes.UpgradeNodesWorkflowConfig{
//		Components: []ccetypes.Component{
//			{
//				Name:          ccetypes.ComponentKubelet,
//				TargetVersion: "1.24.4", // 目标版本低于当前版本，使用支持的版本
//			},
//		},
//	}
//
//	cluster := &v1.Cluster{
//		Spec: ccetypes.ClusterSpec{
//			ClusterID:  "test-cluster",
//			K8SVersion: ccetypes.K8SVersion("1.24.4"),
//		},
//	}
//
//	instanceCrd := &ccev1.Instance{
//		Spec: ccetypes.InstanceSpec{
//			NvidiaContainerToolkitVersion: "1.9.0",
//		},
//	}
//
//	// 执行测试
//	message, err := checkInstanceUpgradeConfig(ctx, instance, k8sNode, config, cluster, instanceCrd)
//
//	// 验证结果
//	assert.NilError(t, err)
//	assert.Assert(t, strings.Contains(message, "kubelet version downgrade is not allowed"), "应该检测到Kubelet版本回退")
//}
