package workflows

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	apiv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	dptypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
)

func TestLegacyManagedMasterMigrationSwitchMasterBLB_Name(t *testing.T) {
	workflow := &LegacyManagedMasterMigrationSwitchMasterBLB{}
	tests := []struct {
		name   string
		expect ccetypes.WorkflowType
	}{
		{
			name:   "normal",
			expect: ccetypes.WorkflowTypeLegacyManagedMasterMigrationSwitchMasterBLB,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := workflow.Name(context.TODO()); got != tt.expect {
				t.Errorf("Name() got = %s, want %vs", got, tt.expect)
			}
		})
	}
}

func TestLegacyManagedMasterMigrationSwitchMasterBLB_OperationTaskClientList(t *testing.T) {
	tests := []struct {
		name     string
		workflow *LegacyManagedMasterMigrationSwitchMasterBLB
		wantErr  bool
	}{
		{
			name: "normal",
			workflow: &LegacyManagedMasterMigrationSwitchMasterBLB{
				baseWorkflow: &baseWorkflow{
					TaskBaseConfig: &tasks.TaskBaseConfig{},
				},
				workflowConfig: &ccetypes.LegacyMasterMigrationConfig{},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := tt.workflow.OperationTaskClientList(context.TODO()); (err != nil) != tt.wantErr {
				t.Errorf("OperationTaskClientList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_newLegacyManagedMasterMigrationSwitchMasterBLB(t *testing.T) {
	tests := []struct {
		name       string
		taskConfig *tasks.TaskBaseConfig
		config     *ccetypes.LegacyMasterMigrationConfig
		wantErr    bool
	}{
		{
			name:       "task config is nil",
			taskConfig: nil,
			config:     &ccetypes.LegacyMasterMigrationConfig{},
			wantErr:    true,
		},
		{
			name: "plugin config is nil",
			taskConfig: &tasks.TaskBaseConfig{
				Cluster:        &apiv1.Cluster{},
				DeployerConfig: &dptypes.Config{},
				Clients:        &clientset.ClientSet{},
			},
			config:  nil,
			wantErr: true,
		},
		{
			name: "normal",
			taskConfig: &tasks.TaskBaseConfig{
				Cluster:        &apiv1.Cluster{},
				DeployerConfig: &dptypes.Config{},
				Clients:        &clientset.ClientSet{},
			},
			config:  &ccetypes.LegacyMasterMigrationConfig{},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if _, err := newLegacyManagedMasterMigrationSwitchMasterBLB(context.TODO(), tt.taskConfig, tt.config); (err != nil) != tt.wantErr {
				t.Errorf("newLegacyManagedMasterMigrationSwitchMasterBLB() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
