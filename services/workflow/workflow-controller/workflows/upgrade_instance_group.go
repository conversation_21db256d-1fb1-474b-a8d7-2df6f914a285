// Copyright 2021 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/02/10 13:43:00, by <EMAIL>, create
*/

package workflows

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	taskscheck "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/check"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/instancegroup"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/instancegroup/upgrade"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
)

var _ Interface = &upgradeNodesWorkflow{}

// TODO 待后续新增nvidia-container-toolkit组件版本获取方式后修改
var defaultNvidiaContainerVersion = ccetypes.Toolkit_Unknown

type upgradeNodesWorkflow struct {
	*baseWorkflow

	config *ccetypes.UpgradeNodesWorkflowConfig
}

func newUpgradeNodes(ctx context.Context, taskConfig *tasks.TaskBaseConfig,
	config *ccetypes.UpgradeNodesWorkflowConfig) (*upgradeNodesWorkflow, error) {
	// 初始化 BaseWorkflow
	base, err := newBaseWorkflow(ctx, taskConfig)
	if err != nil {
		return nil, err
	}

	// 检查 UpgradeNodesK8SVersionWorkflowConfig
	k8sClient, err := base.Clients.NewK8SClientWithClusterID(ctx, taskConfig.ClusterID, base.Clients.Model)
	if err != nil {
		return nil, fmt.Errorf("get k8s client failed: %v", err)
	}
	message, err := CheckUpgradeNodesWorkflowConfig(ctx, config, base.TaskBaseConfig.Cluster,
		k8sClient, base.Clients.MetaClient, base.Clients.Model)
	if err != nil {
		return nil, err
	}
	if message != "" {
		return nil, errors.New(message)
	}

	return &upgradeNodesWorkflow{
		baseWorkflow: base,
		config:       config,
	}, nil
}

func (w *upgradeNodesWorkflow) Name(ctx context.Context) ccetypes.WorkflowType {
	if w.config.IsPreCheck {
		return ccetypes.WorkflowTypeUpgradeNodesPreCheck
	}
	return ccetypes.WorkflowTypeUpgradeNodes
}

func (w *upgradeNodesWorkflow) PreCheckTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	if !w.config.IsPreCheck {
		return []tasks.Interface{}, nil
	}
	// 前置检查：
	// 节点 ready
	// 冲突升级任务
	//  * 节点组是否处于缩容中
	//  * 节点组自愈
	// 节点系统命令检查
	// * systemctl命令正常、bos连接正常、toolkit安装命令正常
	var taskList []tasks.Interface

	nodeInstanceIDs := w.config.CCEInstanceIDList
	// 支持0节点升级前置检查：如果节点列表为空，直接返回空任务列表，不执行任何检查
	if len(nodeInstanceIDs) <= 0 {
		logger.Infof(ctx, "Node instance ID list is empty, skip all precheck tasks for 0-node upgrade")
		return taskList, nil
	}

	nodeTask, err := taskscheck.NewCheckNodeStatusNoK8sVersion(ctx, w.TaskBaseConfig, &taskscheck.CheckNodeStatusNoK8sVersionConfig{
		CCEInstanceIDList: nodeInstanceIDs,
	})
	if err != nil {
		logger.Errorf(ctx, "NewCheckNodesStatus failed: %v", err)
		return nil, fmt.Errorf("NewCheckNodesStatus failed: %w", err)
	}
	taskList = append(taskList, nodeTask)

	// 是否存在冲突任务：该节点组存在其他升级任务、处于缩容状态中
	// 判断缩容中状态使用instanceGroupCRD.status.deletingReplicas 字段是否大于1表示
	instanceGroupID := w.config.InstanceGroupID
	if instanceGroupID == "" {
		// 找到InstanceGroupID
		for _, nodeID := range nodeInstanceIDs {
			instanceCrd, err := w.Clients.MetaClient.GetInstance(ctx, consts.MetaClusterDefaultNamespace, nodeID,
				&metav1.GetOptions{})
			if err != nil {
				logger.Errorf(ctx, "GetInstance[%s] failed: %v", nodeID, err)
				continue
			}

			if instanceCrd.Spec.InstanceGroupID != "" {
				instanceGroupID = instanceCrd.Spec.InstanceGroupID
				break
			}
		}
	}
	conflictTask, err := taskscheck.NewCheckConflictWorkflowInstanceGroup(ctx, w.TaskBaseConfig,
		&taskscheck.CheckConflictWorkflowInstanceGroupConfig{InstanceGroupID: instanceGroupID})
	if err != nil {
		logger.Errorf(ctx, "NewCheckConflictWorkflowInstanceGroup failed: %v", err)
		return nil, fmt.Errorf("NewCheckConflictWorkflowInstanceGroup failed: %w", err)
	}
	taskList = append(taskList, conflictTask)

	// 检查节点上的系统组件是否正常
	task, err := taskscheck.NewCheckNodeSystemStatus(ctx, w.TaskBaseConfig,
		&taskscheck.CheckNodeSystemStatusConfig{CCEInstanceIDList: nodeInstanceIDs})
	if err != nil {
		logger.Errorf(ctx, "NewCheckNodeSystemStatus failed: %v", err)
		return nil, fmt.Errorf("NewCheckNodeSystemStatus failed: %w", err)
	}
	taskList = append(taskList, task)
	return taskList, nil
}

func (w *upgradeNodesWorkflow) BackupTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	return []tasks.Interface{}, nil
}

func (w *upgradeNodesWorkflow) OperationTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	if w.config.IsPreCheck {
		return []tasks.Interface{}, nil
	}

	taskList := make([]tasks.Interface, 0)

	// 构建更新节点组配置Task
	if w.config.InstanceGroupID != "" {
		upgradeIgTask, err := instancegroup.NewUpdateInstanceGroupTask(ctx, w.TaskBaseConfig, w.config)
		if err != nil {
			return nil, fmt.Errorf("NewUpdateInstanceGroupTask failed: %v", err)
		}
		taskList = append(taskList, upgradeIgTask)
	}

	if len(w.config.CCEInstanceIDList) == 0 {
		logger.Infof(ctx, "Node instance ID list is empty, skip other upgrade tasks")
		return taskList, nil
	}

	// 按 NodeUpgradeBatchSize 对节点列表进行分组
	splitInstanceIDList, err := splitCCEInstanceIDList(ctx, w.config.NodeUpgradeBatchSize, w.config.CCEInstanceIDList)
	if err != nil {
		return nil, fmt.Errorf("splitCCEInstanceIDList failed: %v", err)
	}

	// 构建升级组件Task, 通过排序控制顺序从runtime -> kubelet
	sorted, err := sortComponents(w.config.Components)
	if err != nil {
		return nil, fmt.Errorf("sortComponents failed: %v", err)
	}
	for i, ids := range splitInstanceIDList {
		// 构建排水task
		drainInstanceIds := make([]string, 0)
		for _, id := range ids {
			// 以界面配置为准，如果用户界面勾选了排水，则本次升级所有节点都排水。
			if w.config.DrainNodeBeforeUpgrade != nil && *w.config.DrainNodeBeforeUpgrade {
				drainInstanceIds = append(drainInstanceIds, id)
			} else if w.config.IsInstanceDrain(id) {
				drainInstanceIds = append(drainInstanceIds, id)
			}
		}
		if len(drainInstanceIds) > 0 {
			task, err := instancegroup.NewDrainTask(ctx, w.TaskBaseConfig, &instancegroup.DrainTaskConfig{
				CCEInstanceIDList: drainInstanceIds,
			})
			if err != nil {
				return taskList, fmt.Errorf("NewDrainTask failed: %v", err)
			}
			taskList = append(taskList, task)
		}
		// 构建组件升级task
		for _, component := range sorted {
			task, err := instancegroup.NewUpgradeTask(ctx, w.TaskBaseConfig, &upgrade.Config{
				CCEInstanceIDs: ids,
				ComponentName:  component.Name,
				TargetVersion:  component.TargetVersion,
			})
			if err != nil {
				return taskList, fmt.Errorf("NewUpgradeTask failed: %v", err)
			}
			taskList = append(taskList, task)
		}

		// TODO 更新InstanceCRD 放在 NewUpgradeTask 任务中，这里暂时注释掉，等待后续测试没问题，删掉。
		//组件升级成功后，更新meta集群的CRD原数据
		//updateInstanceTask, err := instancegroup.NewUpdateInstanceTask(ctx, w.TaskBaseConfig, &instancegroup.UpdateInstanceTaskConfig{
		//	CCEInstanceIDList: ids,
		//	Components:        sorted,
		//})
		//if err != nil {
		//	return taskList, fmt.Errorf("NewUpdateInstanceTask failed: %v", err)
		//}
		//taskList = append(taskList, updateInstanceTask)

		// 构建恢复调度task
		if len(drainInstanceIds) > 0 {
			task, err := instancegroup.NewDrainTask(ctx, w.TaskBaseConfig, &instancegroup.DrainTaskConfig{
				CCEInstanceIDList: drainInstanceIds,
				Uncordon:          true,
			})
			if err != nil {
				return taskList, fmt.Errorf("NewDrainTask failed: %v", err)
			}
			taskList = append(taskList, task)
		}
		// 添加间隔等待
		if i != len(splitInstanceIDList)-1 {
			if w.config.PausePolicy == nil || *w.config.PausePolicy == ccetypes.NotPause {
				if w.config.BatchIntervalMinutes != nil {
					duration := time.Duration(*w.config.BatchIntervalMinutes) * time.Minute
					taskList = append(taskList, instancegroup.NewWaitTask(duration))
				}
			} else {
				if *w.config.PausePolicy == ccetypes.EveryBatch || (*w.config.PausePolicy == ccetypes.FirstBatch && i == 0) {
					taskList = append(taskList, instancegroup.NewPauseTask())
				}
			}
		}
	}

	return taskList, nil
}

func (w *upgradeNodesWorkflow) PostCheckTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	return []tasks.Interface{}, nil
}

func (w *upgradeNodesWorkflow) TaskConcurrency(ctx context.Context, taskGroupName ccetypes.TaskGroupName) bool {
	return false
}

func CheckUpgradeNodesWorkflowConfig(ctx context.Context, config *ccetypes.UpgradeNodesWorkflowConfig,
	cluster *v1.Cluster, k8sClient kubernetes.Interface, metaClient meta.Interface, modelClient models.Interface) (string, error) {
	// 1. 入参非空校验
	if config == nil {
		return "", fmt.Errorf("config is nil")
	}
	if cluster == nil {
		return "", fmt.Errorf("cluster is nil")
	}
	if k8sClient == nil {
		return "", fmt.Errorf("k8sClient is nil")
	}
	if metaClient == nil {
		return "", fmt.Errorf("metaClient is nil")
	}
	if len(config.Components) == 0 {
		return "upgradeNodesWorkflowConfig.components is nil", nil
	}
	if len(config.CCEInstanceIDList) == 0 {
		if config.InstanceGroupID == "" {
			return "cceInstanceIDList and instanceGroupId is nil", nil
		}
	}
	if config.BatchIntervalMinutes != nil && (*config.BatchIntervalMinutes < 5 || *config.BatchIntervalMinutes > 120) {
		return "batchIntervalMinutes invalid, need in [5, 120]", nil
	}

	// 2. 检查instance id list是否有重复
	if len(utils.RemoveDuplicateElement(config.CCEInstanceIDList)) != len(config.CCEInstanceIDList) {
		return "cceInstanceIDList has duplicate element", nil
	}

	// 2. 校验NodeUpgradeBatchSize
	if config.NodeUpgradeBatchSize < 1 || config.NodeUpgradeBatchSize > 10 {
		return "nodeUpgradeBatchSize less than 1 or greater than 10", nil
	}

	// 4. 获取集群所有instance，记录到map[instanceId]instance中
	instances, err := modelClient.GetInstances(ctx, cluster.Spec.AccountID, cluster.Spec.ClusterID)
	if err != nil {
		return "", err
	}
	instancesMap := make(map[string]*models.Instance)
	for i := 0; i < len(instances); i++ {
		instance := instances[i]
		instancesMap[instance.Spec.CCEInstanceID] = instance
	}

	// 5. 获取集群所有node，记录到map[nodeName]node中
	nodes, err := k8sClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return "", err
	}
	nodesMap := make(map[string]corev1.Node)
	for i := 0; i < len(nodes.Items); i++ {
		node := nodes.Items[i]
		nodesMap[node.Name] = node
	}

	// 6. 升级全部节点的情况, 自动填充实例id
	if len(config.CCEInstanceIDList) == 0 {
		config.CCEInstanceIDList = make([]string, 0)
		for instanceId, instance := range instancesMap {
			if instance.Spec.InstanceGroupID == config.InstanceGroupID {
				config.CCEInstanceIDList = append(config.CCEInstanceIDList, instanceId)
			}
		}
	}
	// 6.1 获取节点组所有instanceCRD，toolkit组件版本需要从其中获取
	// 这里需要异步获取InstanceCRD
	var wg sync.WaitGroup
	var mu sync.Mutex
	var instanceCRDList []*ccev1.Instance
	for _, cceInstanceID := range config.CCEInstanceIDList {
		wg.Add(1)
		go func(ctx context.Context, cceInstanceID string) {
			defer wg.Done()
			instanceCrd, err := metaClient.GetInstance(ctx, "default", cceInstanceID, &metav1.GetOptions{})
			if err != nil {
				logger.Warnf(ctx, "GetInstance(%s) failed: %v", cceInstanceID, err)
				return
			}
			if instanceCrd != nil {
				mu.Lock()
				instanceCRDList = append(instanceCRDList, instanceCrd)
				mu.Unlock()
			}
		}(ctx, cceInstanceID)
	}
	wg.Wait()
	instanceCRDsMap := make(map[string]*ccev1.Instance)
	for i := 0; i < len(instanceCRDList); i++ {
		instance := instanceCRDList[i]
		if instance == nil || instance.Spec.CCEInstanceID == "" {
			logger.Warnf(ctx, "Invalid instanceCRD: instance is nil or CCEInstanceID is empty")
			continue
		}
		instanceCRDsMap[instance.Spec.CCEInstanceID] = instance
	}

	// 7. 校验每个实例
	for _, instanceId := range config.CCEInstanceIDList {
		if _, ok := instancesMap[instanceId]; !ok {
			return fmt.Sprintf("instance %s not found", instanceId), nil
		}
		instance := instancesMap[instanceId]
		instanceCRD := instanceCRDsMap[instanceId]
		k8sNodeName := utils.GetNodeName(ctx, instance.Status.Machine.VPCIP, instance.Status.Machine.Hostname,
			instance.Spec.InstanceName,
			cluster.Spec.K8SCustomConfig.EnableHostname,
		)
		if _, ok := nodesMap[k8sNodeName]; !ok {
			return fmt.Sprintf("node %s not found", k8sNodeName), nil
		}
		node := nodesMap[k8sNodeName]
		message, err := checkInstanceUpgradeConfig(ctx, instance, node, config, cluster, instanceCRD)
		if message != "" || err != nil {
			return message, err
		}
	}

	return "", nil
}

func checkInstanceUpgradeConfig(ctx context.Context, instance *models.Instance, k8sNode corev1.Node,
	config *ccetypes.UpgradeNodesWorkflowConfig, cluster *v1.Cluster, instanceCrd *ccev1.Instance) (string, error) {
	if instance == nil {
		return "", fmt.Errorf("instance is nil")
	}
	instanceId := instance.Spec.CCEInstanceID

	// 4. 检查实例id归属
	if config.InstanceGroupID != "" && instance.Spec.InstanceGroupID != config.InstanceGroupID {
		return fmt.Sprintf("%s not belong to %s", instanceId, config.InstanceGroupID), nil
	}
	if cluster.Spec.ClusterID != instance.Spec.ClusterID {
		return fmt.Sprintf("%s not belong to %s", instanceId, cluster.Spec.ClusterID), nil
	}

	// 7. 获取当前的组件版本
	// currentNvidiaContainerVersion版本目前从instanceCRD.spec.NvidiaContainerVersion字段获取
	currentNvidiaContainerVersion := instanceCrd.Spec.NvidiaContainerToolkitVersion
	currentKubeletVersion := ccetypes.K8SVersion(strings.TrimPrefix(k8sNode.Status.NodeInfo.KubeletVersion, "v"))
	currentRuntimeVersion := ccetypes.RuntimeVersion(k8sNode.Status.NodeInfo.ContainerRuntimeVersion)
	if !currentKubeletVersion.IsSupported() {
		return fmt.Sprintf("current %s kubelet version %v unsupport", instanceId, currentKubeletVersion), nil
	}
	if !currentRuntimeVersion.IsSupported() {
		return fmt.Sprintf("current %s runtime version %v unsupport", instanceId, currentRuntimeVersion), nil
	}

	// 8. 获取目标组件版本
	var targetKubeletVersion ccetypes.K8SVersion
	var targetRuntimeVersion ccetypes.RuntimeVersion
	var targetNvidiaContainerVersion string
	for _, component := range config.Components {
		if component.TargetVersion == "" {
			return fmt.Sprintf("need component targetVersion"), nil
		}
		switch component.Name {
		case ccetypes.ComponentKubelet:
			targetKubeletVersion = ccetypes.K8SVersion(component.TargetVersion)
			if !targetKubeletVersion.IsSupported() {
				return fmt.Sprintf("unsupported kubelet version [%s]", string(targetKubeletVersion)), nil
			}
		case ccetypes.ComponentContainerRuntime:
			targetRuntimeVersion = ccetypes.RuntimeVersion(component.TargetVersion)
			if !targetRuntimeVersion.IsSupported() {
				return fmt.Sprintf("unsupported runtime version [%s]", string(targetRuntimeVersion)), nil
			}
		case ccetypes.ComponentNvidiaContainerToolkit:
			targetNvidiaContainerVersion = component.TargetVersion
			if !ccetypes.IsSupported(targetNvidiaContainerVersion) {
				return fmt.Sprintf("unsupported toolkit version [%s]", string(targetRuntimeVersion)), nil
			}
		default:
			return fmt.Sprintf("invalid component name: [%s]", component.Name), nil
		}
	}
	needUpgradeKubelet := targetKubeletVersion != ""
	needUpgradeRuntime := targetRuntimeVersion != ""
	needUpgradeToolkit := targetNvidiaContainerVersion != ""
	if targetKubeletVersion == "" {
		targetKubeletVersion = currentKubeletVersion
	}
	if targetRuntimeVersion == "" {
		targetRuntimeVersion = currentRuntimeVersion
	}
	if targetNvidiaContainerVersion == "" {
		targetNvidiaContainerVersion = currentNvidiaContainerVersion
	}

	// 9. 检查目标组件版本
	// 判断目标runtime版本是否支持arm集群
	if needUpgradeRuntime && cluster.Spec.ClusterType == ccetypes.ClusterTypeARM && !targetRuntimeVersion.ArmSupported() {
		return fmt.Sprintf("arm cluster unsupport runtime %v", targetRuntimeVersion), nil
	}
	// kubelet只能升级到和控制面一样的版本
	if needUpgradeKubelet && targetKubeletVersion != cluster.Spec.K8SVersion {
		return fmt.Sprintf("kubelet version need equal to cluster version"), nil
	}

	// 如果升级kubelet
	if needUpgradeKubelet && targetKubeletVersion != currentKubeletVersion {
		if ge124, err := targetKubeletVersion.IsAfterOrEqual(ccetypes.K8S_1_24_4); err != nil {
			logger.Errorf(ctx, "parse %v failed, instanceId %s, err %v", targetKubeletVersion, instanceId, err)
			return fmt.Sprintf("invalid target kubelet version %s", targetKubeletVersion), nil
		} else if ge124 {
			// 如果升级到1.24及以后，必须是containerd
			if targetRuntimeVersion.GetRuntimeType() != ccetypes.RuntimeTypeContainerd {
				return fmt.Sprintf("%v need upgrade runtime to containerd when kubelet greater or equal 1.24", instanceId), nil
			}

			// 如果升级到1.26及以后，必须要containerd 1.6及以上
			if ge126, err := targetKubeletVersion.IsAfterOrEqual(ccetypes.K8S_1_26_9); err != nil {
				logger.Errorf(ctx, "parse %v failed, instanceId %s, err %v", targetKubeletVersion, instanceId, err)
				return fmt.Sprintf("invalid target kubelet version %s", targetKubeletVersion), nil
			} else if ge126 {
				if targetRuntimeVersion == ccetypes.Containerd_1_5_4 {
					return fmt.Sprintf("%v runtime must greater or equal containerd 1.6 when kubelet greater or qual 1.26", instanceId), nil
				}
			}
		}
	}

	// 如果升级containerd
	if needUpgradeRuntime && targetRuntimeVersion != currentRuntimeVersion {
		canBeUpgradeTo := currentRuntimeVersion.CanBeUpgradedTo()
		ok, err := currentKubeletVersion.IsBefore(ccetypes.K8S_1_20_8)
		if err != nil {
			return fmt.Sprintf("invalid kubelet version %s", currentKubeletVersion), nil
		}
		if ok {
			// 节点小于1.20最高支持到containerd 1.5
			if targetRuntimeVersion.GetRuntimeType() == ccetypes.RuntimeTypeContainerd {
				if targetRuntimeVersion != ccetypes.Containerd_1_5_4 {
					return fmt.Sprintf("kubelet %v only support containerd 1.5.4", currentKubeletVersion), nil
				}
			}
		}
		if len(canBeUpgradeTo) == 0 {
			return fmt.Sprintf("%v can't upgrade runtime version from %s to %s", instanceId, currentRuntimeVersion, targetRuntimeVersion), nil
		}
		// 需要校验当前节点是否需要开启排水，如果未开启，需要报错。
		needDrain, ok := canBeUpgradeTo[targetRuntimeVersion]
		if !ok {
			return fmt.Sprintf("%v, can't upgrade runtime version from %s to %s", instanceId, currentRuntimeVersion, targetRuntimeVersion), nil
		}
		// 需要判断需要强制开启排水，但是未开启排水才需要报错
		if needDrain && config.DrainNodeBeforeUpgrade != nil && *config.DrainNodeBeforeUpgrade == false {
			return fmt.Sprintf("%v, upgrade runtime version from %s to %s, must drain node before upgrade", instanceId, currentRuntimeVersion, targetRuntimeVersion), nil
		}

		if needDrain {
			config.SetInstanceNeedDrain(instanceId)
		}
	}

	// 升级 nvidia-container-toolkit
	if needUpgradeToolkit && targetNvidiaContainerVersion != currentNvidiaContainerVersion {
		// 校验版本是否符合需求
		// key 格式为 '{K8SVersion}-{os}-{runtimeType}-{runtimeVersion}'
		os := ccetypes.GetToolkitVersionMapKeyOS(string(instance.Spec.InstanceOS.OSName), instance.Spec.InstanceOS.OSVersion)
		key := fmt.Sprintf("%s-%s-%s-%s", cluster.Spec.K8SVersion, os, instance.Spec.RuntimeType, instance.Spec.RuntimeVersion)
		if !ccetypes.IsSupportNvidiaContainerToolkitVersion(key, targetNvidiaContainerVersion) {
			return fmt.Sprintf("instance %s upgrade nvidia Container Toolkit version %s is not supported",
				instanceId, targetNvidiaContainerVersion), nil
		}
		config.SetInstanceNeedDrain(instanceId)
	}
	return "", nil
}

// sortComponents 确保升级顺序是 toolkit -> runtime -> kubelet
func sortComponents(s []ccetypes.Component) ([]ccetypes.Component, error) {
	if len(s) == 0 {
		return []ccetypes.Component{}, nil
	}
	sorted := make([]ccetypes.Component, 0)
	for _, component := range s {
		if component.Name == ccetypes.ComponentNvidiaContainerToolkit {
			sorted = append(sorted, component)
		}
	}
	for _, component := range s {
		if component.Name == ccetypes.ComponentContainerRuntime {
			sorted = append(sorted, component)
		}
	}
	for _, component := range s {
		if component.Name == ccetypes.ComponentKubelet {
			sorted = append(sorted, component)
		}
	}
	if len(s) != len(sorted) {
		return nil, fmt.Errorf("found unknown component")
	}
	return sorted, nil
}
