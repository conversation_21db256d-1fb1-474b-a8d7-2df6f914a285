package workflows

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	master_migration "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/master-migration"
)

var _ Interface = &LegacyManagedMasterMigrationUpdateMetadata{}

type LegacyManagedMasterMigrationUpdateMetadata struct {
	*baseWorkflow
	workflowConfig *ccetypes.LegacyMasterMigrationConfig
}

func newLegacyManagedMasterMigrationUpdateMetadata(ctx context.Context, taskConfig *tasks.TaskBaseConfig, workflowConfig *ccetypes.LegacyMasterMigrationConfig) (Interface, error) {
	if workflowConfig == nil {
		return nil, fmt.Errorf("workflowConfig is nil")
	}

	base, err := newBaseWorkflow(ctx, taskConfig)
	if err != nil {
		logger.Errorf(ctx, "newBaseWorkflow failed: %s", err)
		return nil, err
	}

	return &LegacyManagedMasterMigrationUpdateMetadata{
		baseWorkflow:   base,
		workflowConfig: workflowConfig,
	}, nil
}

func (workflow *LegacyManagedMasterMigrationUpdateMetadata) Name(ctx context.Context) ccetypes.WorkflowType {
	return ccetypes.WorkflowTypeLegacyManagedMasterMigrationUpdateMetadata
}

func (workflow *LegacyManagedMasterMigrationUpdateMetadata) OperationTaskClientList(ctx context.Context) ([]tasks.Interface, error) {
	var taskList []tasks.Interface

	skipValidateCRD, err := master_migration.NewLegacyMasterMigrationUpdateAnnotation(ctx, workflow.TaskBaseConfig, workflow.baseWorkflow.PluginConfig, workflow.workflowConfig)
	if err != nil {
		logger.Errorf(ctx, "NewLegacyMasterMigrationUpdateAnnotation task err: %v", err)
		return taskList, err
	}
	taskList = append(taskList, skipValidateCRD)

	updateMetadata, err := master_migration.NewLegacyMasterMigrationUpdateMetadata(ctx, workflow.TaskBaseConfig, workflow.baseWorkflow.PluginConfig, workflow.workflowConfig)
	if err != nil {
		logger.Errorf(ctx, "NewLegacyMasterMigrationUpdateMetadata task err: %v", err)
		return taskList, err
	}
	taskList = append(taskList, updateMetadata)

	return taskList, nil
}
