// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/08 11:31:00, by <EMAIL>, create
*/
/*
WorkflowController 实现 CCE Workflow 变更的最终一致.
*/

package controllers

import (
	"context"
	"fmt"
	"reflect"
	"sync"
	"testing"

	"github.com/golang/mock/gomock"
	"github.com/google/go-cmp/cmp"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	dptypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	metamock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta/mock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	modelcmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	clustercontrollers "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/controllers"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/event"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	taskscluster "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/cluster"
	tasksmaster "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/master"
	tasksmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks/mock"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/controller"
)

func TestWorkflowReconciler_reconcilePhase(t *testing.T) {
	type fields struct {
		config           Configuration
		controller       controller.Controller
		metaclient       meta.Interface
		model            models.Interface
		recorder         event.Interface
		clientset        clustercontrollers.Clientset
		externalWatchers sync.Map
		defaultResult    ctrl.Result
	}
	type args struct {
		ctx      context.Context
		workflow *ccev1.Workflow
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    ccetypes.WorkflowStatus
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "Status.TaskGroupList 为空, WorkflowPhase = pending",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase: ccetypes.WorkflowPhasePending,
				TaskGroupList: []*ccetypes.TaskGroup{},
			},
			wantErr: false,
		},
		{
			name: "Workflow.Spec.Pause = true 处于暂停状态",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Spec: ccetypes.WorkflowSpec{
						Paused: true,
					},
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase: ccetypes.WorkflowPhasePaused,
				TaskGroupList: []*ccetypes.TaskGroup{},
			},
			wantErr: false,
		},
		{
			name: "Workflow.Spec.Pause = false && WorkflowPhase = ccetypes.WorkflowPhasePaused 恢复后, 处于自然状态",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Spec: ccetypes.WorkflowSpec{
						Paused: false,
					},
					Status: ccetypes.WorkflowStatus{
						WorkflowPhase: ccetypes.WorkflowPhasePaused,
						TaskGroupList: []*ccetypes.TaskGroup{},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase: ccetypes.WorkflowPhasePending,
				TaskGroupList: []*ccetypes.TaskGroup{},
			},
			wantErr: false,
		},
		{
			name: "TaskGroup 中 Task 为空, 则都为 succeeded",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase: ccetypes.WorkflowPhaseSucceeded,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: ccetypes.WorkflowPhaseSucceeded,
						TaskList:       []*ccetypes.WorkflowTask{},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "TaskGroup 中 Task 都为 pending",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
									},
								},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase:  ccetypes.WorkflowPhasePending,
				TotalTaskCount: 1,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: ccetypes.WorkflowPhasePending,
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "WorkflowPhase 失败",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseFailed,
									},
								},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase:     ccetypes.WorkflowPhaseFailed,
				TotalTaskCount:    4,
				FinishedTaskCount: 3,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: ccetypes.WorkflowPhaseFailed,
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseFailed,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "WorkflowPhase 失败, 多 TaskGroup",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
								},
							},
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseFailed,
									},
								},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase:     ccetypes.WorkflowPhaseFailed,
				TotalTaskCount:    8,
				FinishedTaskCount: 7,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: ccetypes.WorkflowPhaseSucceeded,
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
					{
						TaskGroupPhase: ccetypes.WorkflowPhaseFailed,
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseFailed,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "WorkflowPhase 成功",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
								},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase:     ccetypes.WorkflowPhaseSucceeded,
				TotalTaskCount:    4,
				FinishedTaskCount: 4,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: ccetypes.WorkflowPhaseSucceeded,
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "WorkflowPhase 进行中",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseUpgrading,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
									},
								},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				WorkflowPhase:     ccetypes.WorkflowPhaseUpgrading,
				TotalTaskCount:    5,
				FinishedTaskCount: 3,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: ccetypes.WorkflowPhaseUpgrading,
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseUpgrading,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
							},
						},
					},
				},
			},
			wantErr: false,
		},
		{
			name: "WorkflowPhase 超过最大重试次数, 状态为 Failed",
			fields: fields{
				config: Configuration{
					DefaultMaxRetryCount: 1,
				},
			},
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					Status: ccetypes.WorkflowStatus{
						RetryCount: 1,
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskList: []*ccetypes.WorkflowTask{
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhaseUpgrading,
									},
									{
										WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
									},
								},
							},
						},
					},
				},
			},
			want: ccetypes.WorkflowStatus{
				RetryCount:        1,
				WorkflowPhase:     ccetypes.WorkflowPhaseFailed,
				TotalTaskCount:    5,
				FinishedTaskCount: 3,
				TaskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupPhase: "",
						TaskList: []*ccetypes.WorkflowTask{
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhaseUpgrading,
							},
							{
								WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
							},
						},
					},
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &WorkflowReconciler{
				config:        tt.fields.config,
				controller:    tt.fields.controller,
				metaclient:    tt.fields.metaclient,
				model:         tt.fields.model,
				recorder:      tt.fields.recorder,
				defaultResult: tt.fields.defaultResult,
			}

			if err := r.reconcilePhase(tt.args.ctx, tt.args.workflow); (err != nil) != tt.wantErr {
				t.Errorf("WorkflowReconciler.reconcilePhase() error = %v, wantErr %v", err, tt.wantErr)
			}

			if !cmp.Equal(tt.args.workflow.Status, tt.want) {
				t.Errorf("WorkflowReconciler.reconcilePhase() Diff = %v", cmp.Diff(tt.args.workflow.Status, tt.want))
			}
		})
	}
}

func TestWorkflowReconciler_ensureClusterStatusUpdated(t *testing.T) {
	type fields struct {
		config           Configuration
		controller       controller.Controller
		metaclient       meta.Interface
		model            models.Interface
		recorder         event.Interface
		clientset        *clientset.ClientSet
		externalWatchers sync.Map
		defaultResult    ctrl.Result
	}
	type args struct {
		ctx          context.Context
		clusterID    string
		handler      string
		clusterPhase ccetypes.ClusterPhase
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常情况: 更新为 upgrading",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				metaclient := metamock.NewMockInterface(ctl)
				modelclient := modelcmock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, "default", "cce-cluster", &metav1.GetOptions{}).Return(&ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							Handler:   clusterDefaultHandler,
							AccountID: "account-id",
						},
						Status: ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}, nil),
					metaclient.EXPECT().UpdateCluster(ctx, "default", "cce-cluster", &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							Handler:   clusterUpgradingHandler,
							AccountID: "account-id",
						},
						Status: ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseUpgrading,
						},
					}).Return(nil),
					modelclient.EXPECT().UpdateClusterPhase(ctx, "cce-cluster", "account-id", ccetypes.ClusterPhaseUpgrading).Return(nil),
				)

				return fields{
					metaclient: metaclient,
					model:      modelclient,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				clusterID:    "cce-cluster",
				handler:      clusterUpgradingHandler,
				clusterPhase: ccetypes.ClusterPhaseUpgrading,
			},
			wantErr: false,
		},
		{
			name: "正常情况: 更新为 running",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)

				metaclient := metamock.NewMockInterface(ctl)
				modelclient := modelcmock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, "default", "cce-cluster", &metav1.GetOptions{}).Return(&ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							Handler:   clusterUpgradingHandler,
							AccountID: "account-id",
						},
						Status: ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseUpgrading,
						},
					}, nil),
					metaclient.EXPECT().UpdateCluster(ctx, "default", "cce-cluster", &ccev1.Cluster{
						Spec: ccetypes.ClusterSpec{
							Handler:   clusterDefaultHandler,
							AccountID: "account-id",
						},
						Status: ccetypes.ClusterStatus{
							ClusterPhase: ccetypes.ClusterPhaseRunning,
						},
					}).Return(nil),
					modelclient.EXPECT().UpdateClusterPhase(ctx, "cce-cluster", "account-id", ccetypes.ClusterPhaseRunning).Return(nil),
				)

				return fields{
					metaclient: metaclient,
					model:      modelclient,
				}
			}(),
			args: args{
				ctx:          context.TODO(),
				clusterID:    "cce-cluster",
				handler:      clusterDefaultHandler,
				clusterPhase: ccetypes.ClusterPhaseRunning,
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &WorkflowReconciler{
				config:        tt.fields.config,
				controller:    tt.fields.controller,
				metaclient:    tt.fields.metaclient,
				model:         tt.fields.model,
				recorder:      tt.fields.recorder,
				clientset:     tt.fields.clientset,
				defaultResult: tt.fields.defaultResult,
			}
			if err := r.ensureClusterStatusUpdated(tt.args.ctx, tt.args.clusterID, tt.args.handler, tt.args.clusterPhase); (err != nil) != tt.wantErr {
				t.Errorf("WorkflowReconciler.ensureClusterStatusUpdated() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestWorkflowReconciler_ensureTaskGroupListInStatus(t *testing.T) {
	type fields struct {
		config           Configuration
		controller       controller.Controller
		metaclient       meta.Interface
		model            models.Interface
		recorder         event.Interface
		clientset        *clientset.ClientSet
		externalWatchers sync.Map
		defaultResult    ctrl.Result
	}
	type args struct {
		ctx         context.Context
		old         *ccev1.Workflow
		workflow    *ccev1.Workflow
		taskClients map[ccetypes.TaskGroupName][]tasks.Interface
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程: TaskGroupList 更新成功",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)
				metaclient := metamock.NewMockInterface(ctl)

				workflow := &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "workflow-chenhuan",
						Namespace: "default",
					},
					Status: ccetypes.WorkflowStatus{
						WorkflowPhase:  ccetypes.WorkflowPhasePending,
						TotalTaskCount: 2,
						TaskGroupList: []*ccetypes.TaskGroup{
							{
								TaskGroupName:  ccetypes.TaskGroupNamePreCheck,
								TaskGroupPhase: ccetypes.WorkflowPhasePending,
								TaskList: []*ccetypes.WorkflowTask{
									{
										TaskName: "precheck-task-name",
										TaskConfig: func() []byte {
											return []byte(utils.ToJSON(taskscluster.UpgradeClusterK8SVersionConfig{
												From: ccetypes.K8S_1_16_8,
												To:   ccetypes.K8S_1_20_8,
											}))
										}(),
										WorkflowTaskType:  taskscluster.TaskTypeUpgradeClusterK8SVersion,
										WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
									},
								},
							},
							{
								TaskGroupName:  ccetypes.TaskGroupNameOperate,
								TaskGroupPhase: ccetypes.WorkflowPhasePending,
								TaskList: []*ccetypes.WorkflowTask{
									{
										TaskName: "operate-task-name",
										TaskConfig: func() []byte {
											return []byte(utils.ToJSON(tasksmaster.UpgradeContainerizedMasterK8SVersionConfig{
												MasterCCEInstanceID: "cce-master-id",
												ToK8SVersion:        ccetypes.K8S_1_20_8,
											}))
										}(),
										WorkflowTaskType:  tasksmaster.TaskTypeUpgradeContainerizedMasterK8SVersion,
										WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
									},
								},
							},
						},
					},
				}

				logger.Infof(ctx, "Workflow in unittest: %s", utils.ToJSON(workflow))

				gomock.InOrder(
					metaclient.EXPECT().UpdateWorkflow(ctx, "default", "workflow-chenhuan", workflow),
				)

				return fields{
					metaclient: metaclient,
				}
			}(),
			args: args{
				ctx: context.TODO(),
				old: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "workflow-chenhuan",
						Namespace: "default",
					},
					Status: ccetypes.WorkflowStatus{},
				},
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "workflow-chenhuan",
						Namespace: "default",
					},
					Status: ccetypes.WorkflowStatus{},
				},
				taskClients: func() map[ccetypes.TaskGroupName][]tasks.Interface {
					ctx := context.TODO()
					ctl := gomock.NewController(t)
					precheck := tasksmock.NewMockInterface(ctl)
					operate := tasksmock.NewMockInterface(ctl)

					gomock.InOrder(
						precheck.EXPECT().TaskType(ctx).Return(taskscluster.TaskTypeUpgradeClusterK8SVersion),
						precheck.EXPECT().TaskConfig(ctx).Return(taskscluster.UpgradeClusterK8SVersionConfig{
							From: ccetypes.K8S_1_16_8,
							To:   ccetypes.K8S_1_20_8,
						}),
						precheck.EXPECT().Name(ctx).Return("precheck-task-name"),

						operate.EXPECT().TaskType(ctx).Return(tasksmaster.TaskTypeUpgradeContainerizedMasterK8SVersion),
						operate.EXPECT().TaskConfig(ctx).Return(tasksmaster.UpgradeContainerizedMasterK8SVersionConfig{
							MasterCCEInstanceID: "cce-master-id",
							ToK8SVersion:        ccetypes.K8S_1_20_8,
						}),
						operate.EXPECT().Name(ctx).Return("operate-task-name"),
					)

					return map[ccetypes.TaskGroupName][]tasks.Interface{
						ccetypes.TaskGroupNamePreCheck: {
							precheck,
						},
						ccetypes.TaskGroupNameOperate: {
							operate,
						},
					}
				}(),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &WorkflowReconciler{
				config:        tt.fields.config,
				controller:    tt.fields.controller,
				metaclient:    tt.fields.metaclient,
				model:         tt.fields.model,
				recorder:      tt.fields.recorder,
				clientset:     tt.fields.clientset,
				defaultResult: tt.fields.defaultResult,
			}

			if err := r.ensureTaskGroupListInStatus(tt.args.ctx, tt.args.old, tt.args.workflow, tt.args.taskClients); (err != nil) != tt.wantErr {
				t.Errorf("WorkflowReconciler.ensureTaskGroupListInStatus() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_finishedTaskCount(t *testing.T) {
	type args struct {
		ctx           context.Context
		taskGroupList []*ccetypes.TaskGroup
	}
	tests := []struct {
		name         string
		args         args
		wantTotal    int
		wantFinished int
		wantErr      bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: args{
				ctx: context.TODO(),
				taskGroupList: []*ccetypes.TaskGroup{
					{
						TaskGroupName:  ccetypes.TaskGroupNamePreCheck,
						TaskGroupPhase: ccetypes.WorkflowPhasePending,
						TaskList: []*ccetypes.WorkflowTask{
							{
								TaskName: "precheck-task-name",
								TaskConfig: func() []byte {
									return []byte(utils.ToJSON(taskscluster.UpgradeClusterK8SVersionConfig{
										From: ccetypes.K8S_1_16_8,
										To:   ccetypes.K8S_1_20_8,
									}))
								}(),
								WorkflowTaskType:  taskscluster.TaskTypeUpgradeClusterK8SVersion,
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								TaskName: "precheck-task-name",
								TaskConfig: func() []byte {
									return []byte(utils.ToJSON(taskscluster.UpgradeClusterK8SVersionConfig{
										From: ccetypes.K8S_1_16_8,
										To:   ccetypes.K8S_1_20_8,
									}))
								}(),
								WorkflowTaskType:  taskscluster.TaskTypeUpgradeClusterK8SVersion,
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
						},
					},
					{
						TaskGroupName:  ccetypes.TaskGroupNameOperate,
						TaskGroupPhase: ccetypes.WorkflowPhasePending,
						TaskList: []*ccetypes.WorkflowTask{
							{
								TaskName: "operate-task-name",
								TaskConfig: func() []byte {
									return []byte(utils.ToJSON(tasksmaster.UpgradeContainerizedMasterK8SVersionConfig{
										MasterCCEInstanceID: "cce-master-id",
										ToK8SVersion:        ccetypes.K8S_1_20_8,
									}))
								}(),
								WorkflowTaskType:  tasksmaster.TaskTypeUpgradeContainerizedMasterK8SVersion,
								WorkflowTaskPhase: ccetypes.WorkflowPhaseSucceeded,
							},
							{
								TaskName: "operate-task-name",
								TaskConfig: func() []byte {
									return []byte(utils.ToJSON(tasksmaster.UpgradeContainerizedMasterK8SVersionConfig{
										MasterCCEInstanceID: "cce-master-id",
										ToK8SVersion:        ccetypes.K8S_1_20_8,
									}))
								}(),
								WorkflowTaskType:  tasksmaster.TaskTypeUpgradeContainerizedMasterK8SVersion,
								WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
							},
						},
					},
				},
			},
			wantTotal:    4,
			wantFinished: 3,
			wantErr:      false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			gotTotal, gotFinished, err := finishedTaskCount(tt.args.ctx, tt.args.taskGroupList)
			if (err != nil) != tt.wantErr {
				t.Errorf("finishedTaskCount() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if gotTotal != tt.wantTotal {
				t.Errorf("finishedTaskCount() Total = %v, want %v", gotTotal, tt.wantTotal)
			}

			if gotFinished != tt.wantFinished {
				t.Errorf("finishedTaskCount() Finished = %v, want %v", gotFinished, tt.wantFinished)
			}
		})
	}
}

func Test_validateTaskClientList(t *testing.T) {
	type args struct {
		ctx            context.Context
		taskGroup      *ccetypes.TaskGroup
		taskClientList []tasks.Interface
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常情况: 二者一致",
			args: args{
				ctx: context.TODO(),
				taskGroup: &ccetypes.TaskGroup{
					TaskList: []*ccetypes.WorkflowTask{
						{
							TaskName:         "task-name-a",
							WorkflowTaskType: ccetypes.WorkflowTaskType("UpgradeNodesK8SVersion"),
						},
					},
				},
				taskClientList: func() []tasks.Interface {
					ctx := context.TODO()
					ctl := gomock.NewController(t)
					client := tasksmock.NewMockInterface(ctl)

					gomock.InOrder(
						client.EXPECT().TaskType(ctx).Return(ccetypes.WorkflowTaskType("UpgradeNodesK8SVersion")),
						client.EXPECT().Name(ctx).Return("task-name-a"),
					)

					return []tasks.Interface{
						client,
					}
				}(),
			},
			wantErr: false,
		},
		{
			name: "正常情况: 二者不一致",
			args: args{
				ctx: context.TODO(),
				taskGroup: &ccetypes.TaskGroup{
					TaskList: []*ccetypes.WorkflowTask{
						{
							TaskName:         "task-name-b",
							WorkflowTaskType: ccetypes.WorkflowTaskType("UpgradeNodesK8SVersion"),
						},
					},
				},
				taskClientList: func() []tasks.Interface {
					ctx := context.TODO()
					ctl := gomock.NewController(t)
					client := tasksmock.NewMockInterface(ctl)

					gomock.InOrder(
						client.EXPECT().TaskType(ctx).Return(ccetypes.WorkflowTaskType("UpgradeNodesK8SVersion")),
						client.EXPECT().Name(ctx).Return("task-name-a"),
					)

					return []tasks.Interface{
						client,
					}
				}(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := validateTaskClientList(tt.args.ctx, tt.args.taskGroup, tt.args.taskClientList); (err != nil) != tt.wantErr {
				t.Errorf("validateTaskClientList() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_needGenerateFailedPodOwnerReferencesSet(t *testing.T) {
	type args struct {
		ctx      context.Context
		workflow *ccev1.Workflow
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		// TODO: Add test cases.
		{
			name: "默认生成",
			args: args{
				ctx:      context.TODO(),
				workflow: &ccev1.Workflow{},
			},
			want: true,
		},
		{
			name: "设置 annotation 跳过",
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							ccev1.AnnotationSkipGenerateFailedPods: "true",
						},
					},
				},
			},
			want: false,
		},
		{
			name: "其他情况需要生成",
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							"chenhuan": "true",
						},
					},
				},
			},
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := needGenerateFailedPodOwnerReferencesSet(tt.args.ctx, tt.args.workflow); got != tt.want {
				t.Errorf("needGenerateFailedPodOwnerReferencesSet() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getPodsOwnerReferencesSet(t *testing.T) {
	type args struct {
		ctx     context.Context
		podList *corev1.PodList
	}
	tests := []struct {
		name string
		args args
		want map[types.UID]interface{}
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: args{
				ctx: context.TODO(),
				podList: &corev1.PodList{
					Items: []corev1.Pod{
						{
							ObjectMeta: metav1.ObjectMeta{
								DeletionTimestamp: &metav1.Time{},
								OwnerReferences: []metav1.OwnerReference{
									{
										UID: types.UID("uid-a"),
									},
								},
							},
							Status: corev1.PodStatus{
								Phase: corev1.PodRunning,
							},
						},
						{
							ObjectMeta: metav1.ObjectMeta{
								DeletionTimestamp: &metav1.Time{},
								OwnerReferences: []metav1.OwnerReference{
									{
										UID: types.UID("uid-b"),
									},
								},
							},
							Status: corev1.PodStatus{
								Phase: corev1.PodFailed,
							},
						},
						{
							ObjectMeta: metav1.ObjectMeta{
								DeletionTimestamp: &metav1.Time{},
								OwnerReferences: []metav1.OwnerReference{
									{
										UID: types.UID("uid-c"),
									},
								},
							},
							Status: corev1.PodStatus{
								Phase: corev1.PodFailed,
							},
						},
						{
							ObjectMeta: metav1.ObjectMeta{
								DeletionTimestamp: &metav1.Time{},
								OwnerReferences: []metav1.OwnerReference{
									{
										UID: types.UID("uid-d"),
									},
								},
							},
							Status: corev1.PodStatus{
								Phase: corev1.PodRunning,
								ContainerStatuses: []corev1.ContainerStatus{
									{
										Ready: false,
									},
								},
							},
						},
					},
				},
			},
			want: map[types.UID]interface{}{
				"uid-b": nil,
				"uid-c": nil,
				"uid-d": nil,
			},
		},
		{
			name: "podList is nil",
			args: args{
				ctx: context.TODO(),
			},
			want: map[types.UID]interface{}{},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := getPodsOwnerReferencesSet(tt.args.ctx, tt.args.podList); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getPodsOwnerReferencesSet() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestWorkflowReconciler_generateTaskBaseConfig(t *testing.T) {
	type fields struct {
		config           Configuration
		controller       controller.Controller
		metaclient       meta.Interface
		model            models.Interface
		recorder         event.Interface
		clientset        *clientset.ClientSet
		externalWatchers sync.Map
		defaultResult    ctrl.Result
	}
	type args struct {
		ctx      context.Context
		workflow *ccev1.Workflow
	}

	cluster := &ccev1.Cluster{}

	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *tasks.TaskBaseConfig
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常情况: 不生成 FailedPodOwnerReferencesSet",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)
				metaclient := metamock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, consts.MetaClusterDefaultNamespace, "cce-cluster", &metav1.GetOptions{}).Return(
						cluster,
						nil,
					),
					metaclient.EXPECT().ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
						LabelSelector: "cluster-id=cce-cluster",
					}).Return(
						&ccev1.InstanceList{
							Items: []ccev1.Instance{
								{
									ObjectMeta: metav1.ObjectMeta{
										Name: "cce-instance-id-a",
									},
									Status: ccetypes.InstanceStatus{
										Machine: ccetypes.Machine{
											VPCIP: "vpc-ip-a",
										},
									},
								},
								{
									ObjectMeta: metav1.ObjectMeta{
										Name: "cce-instance-id-b",
									},
									Status: ccetypes.InstanceStatus{
										Machine: ccetypes.Machine{
											VPCIP: "vpc-ip-b",
										},
									},
								},
								{
									ObjectMeta: metav1.ObjectMeta{
										Name: "cce-instance-id-c",
									},
									Status: ccetypes.InstanceStatus{
										Machine: ccetypes.Machine{
											VPCIP: "vpc-ip-c",
										},
									},
								},
							},
						},
						nil,
					),
				)

				return fields{
					metaclient: metaclient,
					config: Configuration{
						DeployerConfig: &dptypes.Config{},
					},
					recorder: event.NewFakeClient("workflow"),
				}
			}(),
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							"kubernetes.io/cce.workflow.skip-generate-failed-pods": "true",
						},
					},
					Spec: ccetypes.WorkflowSpec{
						ClusterID: "cce-cluster",
						AccountID: "accountID",
					},
				},
			},
			want: &tasks.TaskBaseConfig{
				ClusterID:      "cce-cluster",
				AccountID:      "accountID",
				Cluster:        cluster,
				DeployerConfig: &dptypes.Config{},
				Clients:        nil,
				CCEInstanceIDToVPCIP: map[string]string{
					"cce-instance-id-a": "vpc-ip-a",
					"cce-instance-id-b": "vpc-ip-b",
					"cce-instance-id-c": "vpc-ip-c",
				},
				CCEInstanceIDToHostname:     map[string]string{},
				FailedPodOwnerReferencesSet: nil,
			},
		},
		{
			name: "异常情况: GetCluster",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)
				metaclient := metamock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, consts.MetaClusterDefaultNamespace, "cce-cluster", &metav1.GetOptions{}).Return(
						cluster,
						fmt.Errorf(""),
					),
				)

				return fields{
					metaclient: metaclient,
					config: Configuration{
						DeployerConfig: &dptypes.Config{},
					},
					recorder: event.NewFakeClient("workflow"),
				}
			}(),
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							"kubernetes.io/cce.workflow.skip-generate-failed-pods": "true",
						},
					},
					Spec: ccetypes.WorkflowSpec{
						ClusterID: "cce-cluster",
						AccountID: "accountID",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "异常情况: ListInstances",
			fields: func() fields {
				ctx := context.TODO()
				ctl := gomock.NewController(t)
				metaclient := metamock.NewMockInterface(ctl)

				gomock.InOrder(
					metaclient.EXPECT().GetCluster(ctx, consts.MetaClusterDefaultNamespace, "cce-cluster", &metav1.GetOptions{}).Return(
						cluster,
						nil,
					),
					metaclient.EXPECT().ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
						LabelSelector: "cluster-id=cce-cluster",
					}).Return(
						nil,
						fmt.Errorf(""),
					),
				)

				return fields{
					metaclient: metaclient,
					config: Configuration{
						DeployerConfig: &dptypes.Config{},
					},
					recorder: event.NewFakeClient("workflow"),
				}
			}(),
			args: args{
				ctx: context.TODO(),
				workflow: &ccev1.Workflow{
					ObjectMeta: metav1.ObjectMeta{
						Annotations: map[string]string{
							"kubernetes.io/cce.workflow.skip-generate-failed-pods": "true",
						},
					},
					Spec: ccetypes.WorkflowSpec{
						ClusterID: "cce-cluster",
						AccountID: "accountID",
					},
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &WorkflowReconciler{
				config:           tt.fields.config,
				controller:       tt.fields.controller,
				metaclient:       tt.fields.metaclient,
				model:            tt.fields.model,
				recorder:         tt.fields.recorder,
				clientset:        tt.fields.clientset,
				externalWatchers: tt.fields.externalWatchers,
				defaultResult:    tt.fields.defaultResult,
			}
			got, err := r.generateTaskBaseConfig(tt.args.ctx, tt.args.workflow)
			if (err != nil) != tt.wantErr {
				t.Errorf("WorkflowReconciler.generateTaskBaseConfig() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("WorkflowReconciler.generateTaskBaseConfig() = %v, want %v, diff %v", got, tt.want, cmp.Diff(got, tt.want))

			}
		})
	}
}

func TestWorkflowReconciler_executeTask(t *testing.T) {
	type fields struct {
		config           Configuration
		controller       controller.Controller
		metaclient       meta.Interface
		model            modelcmock.Interface
		recorder         event.Interface
		clientset        *clientset.ClientSet
		externalWatchers sync.Map
		defaultResult    ctrl.Result
	}
	type args struct {
		ctx        context.Context
		task       *ccetypes.WorkflowTask
		taskClient tasks.Interface
	}
	ctl := gomock.NewController(t)
	taskclient := tasksmock.NewMockInterface(ctl)
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "创建集群前置检查进入待确认状态",
			fields: func() fields {
				ctx := context.TODO()
				cluster := ccev1.Cluster{
					TypeMeta:   metav1.TypeMeta{},
					ObjectMeta: metav1.ObjectMeta{},
					Spec:       ccetypes.ClusterSpec{},
					Status:     ccetypes.ClusterStatus{},
				}

				task := &ccetypes.WorkflowTask{
					TaskName:          "",
					WorkflowTaskType:  "",
					WorkflowTaskPhase: "",
					ErrorMessage:      "",
					TaskConfig:        nil,
					TaskExecuteResult: nil,
					StartTime:         nil,
					FinishedTime:      nil,
				}

				taskResult := &ccetypes.WorkflowTask{
					TaskName:          "",
					WorkflowTaskType:  "",
					WorkflowTaskPhase: ccetypes.WorkflowPhaseVerifying,
					ErrorMessage:      "[Balance needs to be confirmed.]",
					TaskConfig:        nil,
					TaskExecuteResult: nil,
					StartTime:         nil,
					FinishedTime:      nil,
				}
				gomock.InOrder(
					taskclient.EXPECT().Targets(ctx).Return([]interface{}{cluster}, nil),
					taskclient.EXPECT().Targets(ctx).Return([]interface{}{cluster}, nil),
					taskclient.EXPECT().NeedUpgrade(ctx, *task).Return(true, nil),
					taskclient.EXPECT().CheckStatusBeforeExecute(ctx, *task).Return(nil),
					taskclient.EXPECT().UpdateMetaBeforeExecute(ctx, *task).Return(nil),
					taskclient.EXPECT().Execute(ctx, *task).Return(nil, fmt.Errorf("[Balance needs to be confirmed.]")),
					taskclient.EXPECT().Targets(ctx).Return([]interface{}{cluster}, nil),
					taskclient.EXPECT().Clean(ctx, *taskResult).Return(nil),
				)

				return fields{
					config:           Configuration{},
					controller:       nil,
					metaclient:       nil,
					model:            nil,
					recorder:         nil,
					clientset:        nil,
					externalWatchers: sync.Map{},
					defaultResult:    ctrl.Result{},
				}
			}(),
			args: args{
				ctx: context.TODO(),
				task: &ccetypes.WorkflowTask{
					TaskName:          "",
					WorkflowTaskType:  "",
					WorkflowTaskPhase: "",
					ErrorMessage:      "",
					TaskConfig:        nil,
					TaskExecuteResult: nil,
					StartTime:         nil,
					FinishedTime:      nil,
				},
				taskClient: taskclient,
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			r := &WorkflowReconciler{
				config:           tt.fields.config,
				controller:       tt.fields.controller,
				metaclient:       tt.fields.metaclient,
				model:            tt.fields.model,
				recorder:         tt.fields.recorder,
				clientset:        tt.fields.clientset,
				externalWatchers: tt.fields.externalWatchers,
				defaultResult:    tt.fields.defaultResult,
			}
			if err := r.executeTask(tt.args.ctx, tt.args.task, tt.args.taskClient); (err != nil) != tt.wantErr {
				t.Errorf("executeTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
