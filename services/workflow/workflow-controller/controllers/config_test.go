// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/13 14:18:00, by <EMAIL>, create
*/
/*
WorkflowController 通用配置
*/

package controllers

import (
	"context"
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	dptypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
)

func TestCheckConfig(t *testing.T) {
	type args struct {
		ctx    context.Context
		config Configuration
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常流程",
			args: args{
				config: Configuration{
					MySQLConnConfig: "MySQLConnConfig",
					DeployerConfig: &dptypes.Config{
						CCEAgentImage: "CCEAgentImage",
					},
					ClientSetConfig:           &clientset.Config{},
					WorkflowHandlerAnnotation: "WorkflowHandlerAnnotation",
				},
			},
			wantErr: false,
		},
		{
			name: "MySQLConnConfig == nil",
			args: args{
				config: Configuration{
					MySQLConnConfig: "",
				},
			},
			wantErr: true,
		},
		{
			name: "DeployerConfig == nil",
			args: args{
				config: Configuration{
					MySQLConnConfig: "MySQLConnConfig",
				},
			},
			wantErr: true,
		},
		{
			name: "DeployerConfig.CCEAgentImage == nil",
			args: args{
				config: Configuration{
					MySQLConnConfig: "MySQLConnConfig",
					DeployerConfig:  &dptypes.Config{},
				},
			},
			wantErr: true,
		},
		{
			name: "ClientSetConfig == nil",
			args: args{
				config: Configuration{
					MySQLConnConfig: "MySQLConnConfig",
					DeployerConfig: &dptypes.Config{
						CCEAgentImage: "CCEAgentImage",
					},
				},
			},
			wantErr: true,
		},
		{
			name: "WorkflowHandlerAnnotation == nil",
			args: args{
				config: Configuration{
					MySQLConnConfig: "MySQLConnConfig",
					DeployerConfig: &dptypes.Config{
						CCEAgentImage: "CCEAgentImage",
					},
					ClientSetConfig: &clientset.Config{},
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := CheckConfig(tt.args.ctx, tt.args.config); (err != nil) != tt.wantErr {
				t.Errorf("CheckConfig() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
