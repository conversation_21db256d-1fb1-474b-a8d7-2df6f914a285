// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/08 11:31:00, by <EMAIL>, create
*/
/*
WorkflowController 实现 CCE Workflow 变更的最终一致.
*/

package controllers

import (
	"context"
	"fmt"
	"os"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/go-cmp/cmp"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/cluster-api/util"
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/controller"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/consts"
	clustercontrollers "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/controllers"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/event"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/tasks"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/workflow/workflow-controller/workflows"
)

const (
	// ReconcileWorkflowEventType - reconcileWorkflow 事件类型
	ReconcileWorkflowEventType = "ReconcileWorkflow"

	clusterUpgradingHandler = "upgrading"
	clusterDefaultHandler   = "default"
)

// WorkflowReconciler reconciles a Workflow object
type WorkflowReconciler struct {
	config     Configuration
	controller controller.Controller

	metaclient meta.Interface
	model      models.Interface
	recorder   event.Interface
	clientset  *clientset.ClientSet

	externalWatchers sync.Map
	defaultResult    ctrl.Result // 失败后默认重试策略
}

// SetupWithManager 初始化 WorkflowReconciler
func (r *WorkflowReconciler) SetupWithManager(mgr ctrl.Manager, options controller.Options,
	requeueAfterSeconds int, config Configuration) error {
	ctx := context.TODO()

	// 初始化 controller
	c, err := ctrl.NewControllerManagedBy(mgr).
		For(&ccev1.Workflow{}).
		WithOptions(options).
		Build(r)
	if err != nil {
		return fmt.Errorf("SetWithManager failed: %v", err)
	}
	r.controller = c

	// 初始化 event recorder
	recorder, err := event.NewClient("cce-workflow-controller", mgr)
	if err != nil {
		logger.Errorf(ctx, "event.NewClient failed: %v", err)
		return err
	}
	r.recorder = recorder

	// 初始化 metaclient
	metaclient, err := meta.NewClient(ctx, config.MetaClusterKubeConfig)
	if err != nil {
		logger.Errorf(ctx, "meta.NewClient failed: %v", err)
		return err
	}
	r.metaclient = metaclient

	// 初始化 model client
	connCfg := config.MySQLConnConfig
	if connCfg == "" {
		logger.Errorf(ctx, "MySQLConnConfig is empty")
		return err
	}

	model, err := models.NewClient(ctx, connCfg)
	if err != nil {
		logger.Errorf(ctx, "new model client failed: %v", err)
		return err
	}
	r.model = model

	// 初始化 clientset
	clientset, err := clientset.NewClientSetByConfig(ctx, config.ClientSetConfig)
	if err != nil {
		logger.Errorf(ctx, "NewClientSetByConfig failed: %s", err)
		return err
	}
	r.clientset = clientset

	// 重试策略
	if requeueAfterSeconds < 30 {
		requeueAfterSeconds = 30
	}

	r.defaultResult = ctrl.Result{
		Requeue:      true,
		RequeueAfter: time.Second * time.Duration(requeueAfterSeconds),
	}

	// 存储配置
	r.config = config

	// 更新 DeployConfig
	// 升级暂时不支持 oos，因此暂时用不到这个配置，但是先提前预热
	r.fillConf(ctx)

	return nil
}

// Reconcile - WorkflowController 主流程, 保证 Workflow 成功执行完成
// Reconciler performs a full reconciliation for the object referred to by the Request.
// The Controller will requeue the Request to be processed again if an error is non-nil or
// Result.Requeue is true, otherwise upon completion it will remove the work from the queue.
// PS: ctrl.Result 和 error 是分开处理.
//
// PARAMS:
//   - req: NamespacedName
//
// RETURNS:
//
//	ctrl.Result: 是否需要重新进入队列
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) Reconcile(req ctrl.Request) (ctrl.Result, error) {
	var err error

	// 生成 context, 用于追踪记录
	ctx := context.WithValue(context.Background(), logger.RequestID, logger.GetUUID())
	logger.Infof(ctx, "Reconcile workflow begin: %v", req.NamespacedName)

	// 获取 Workflow Namespace & Name
	namespace := req.NamespacedName.Namespace
	workflowID := req.NamespacedName.Name

	// 获取 WorkflowCRD
	workflow, err := r.metaclient.GetWorkflow(ctx, namespace, workflowID, &metav1.GetOptions{})
	if err != nil && !meta.IsWorkflowNotExist(err) {
		logger.Errorf(ctx, "GetWorkflow failed: %v", err)
		return ctrl.Result{}, err
	}

	if meta.IsWorkflowNotExist(err) || workflow == nil {
		msg := fmt.Sprintf("Workflow %s/%s is nil, skip reconcile", namespace, workflowID)
		logger.Warnf(ctx, msg)
		return ctrl.Result{}, nil
	}

	logger.Infof(ctx, "GetWorkflow successed: %v", utils.ToJSON(workflow))
	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType, "Get workflow %v/%v success", namespace, workflowID)

	// 确认 Workflow 是否由 cce-workflow-controller 处理
	// 1. handler=default: 进入 reconcile 和 reconcileDelete 流程;
	// 2. 其他: 不处理
	if !clustercontrollers.HandledByCCE(ctx, workflow.Spec.Handler, r.config.WorkflowHandlerAnnotation) {
		msg := fmt.Sprintf("Workflow %s not handled by cce-workflow-controller, got=%v want=%v, skip",
			workflowID, workflow.Spec.Handler, r.config.WorkflowHandlerAnnotation)

		logger.Infof(ctx, msg)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType, msg)

		return ctrl.Result{}, nil
	}

	retry := clustercontrollers.RetryReconcile(ctx, workflow.Status.RetryCount, r.config.DefaultMaxRetryCount, workflow.Annotations)
	old := workflow.DeepCopy()

	// 更新 Status
	defer func() {
		// 如未达到 MaxRetryCount 限制，更新 RetryCount
		if retry == true {
			if err == nil {
				workflow.Status.RetryCount = 0
			} else if !models.ErrWorkflowNeedWait.Is(err) && !models.ErrWorkflowNeedPause.Is(err) {
				workflow.Status.RetryCount = workflow.Status.RetryCount + 1
			}
		}

		needPause := models.ErrWorkflowNeedPause.Is(err)

		// 更新 Status
		if err := r.updateWorkflow(ctx, old, workflow); err != nil {
			logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
				"UpdateWorkflow failed: %s", err)
		}

		if needPause {
			currentWorkflow, err := r.metaclient.GetWorkflow(ctx, namespace, workflowID, &metav1.GetOptions{})
			if err == nil && currentWorkflow != nil {
				old = currentWorkflow.DeepCopy()
				currentWorkflow.Spec.Paused = true
				_ = r.updateWorkflow(ctx, old, currentWorkflow)
			}
		}

	}()

	// 检查是否超过重试次数
	if retry == false {
		logger.Warnf(ctx, "Stop reconcile workflow %v: exceeds retry limit", workflow.Spec.WorkflowID)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"Stop reconcile workflow %v: exceeds retry limit", workflowID)
		return ctrl.Result{}, nil
	}

	// 执行 Workflow 删除
	if !workflow.ObjectMeta.DeletionTimestamp.IsZero() {
		logger.Infof(ctx, "workflow.ObjectMeta.DeletionTimestamp %v, delete workflow", workflow.ObjectMeta.DeletionTimestamp)

		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Workflow %s reconcileDelete begin", workflowID)

		err = r.reconcileDelete(ctx, old, workflow)
		if err != nil {
			workflow.Status.ErrorMessage = err.Error()

			msg := fmt.Sprintf("reconcileDelete workflow failed: %v", err)
			logger.Errorf(ctx, msg)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType, msg)

			return r.defaultResult, nil
		}

		workflow.Status.ErrorMessage = ""
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Workflow reconcileDelete success")

		return ctrl.Result{}, nil
	}

	// 执行 Workflow 操作
	if clustercontrollers.HandledByCCE(ctx, workflow.Spec.Handler, r.config.WorkflowHandlerAnnotation) {
		err = r.reconcile(ctx, old, workflow)
		if err != nil {
			workflow.Status.ErrorMessage = err.Error()

			msg := fmt.Sprintf("reconcile workflow failed: %v", err)
			logger.Errorf(ctx, msg)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType, msg)

			return r.defaultResult, nil
		}

		workflow.Status.ErrorMessage = ""
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Workflow reconcile success")
	}

	return ctrl.Result{}, nil
}

// reconcile - 保证 WorkflowCRD 和 CCE K8S Workflow 最终一致
//
// PARAMS:
//   - ctx: context to trace request
//   - workflow: WorkflowCRD
//
// RETURNS:
//
//	ctrl.Result: 是否需要重新进入队列
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) reconcile(ctx context.Context, old, workflow *ccev1.Workflow) error {
	if old == nil || workflow == nil {
		return fmt.Errorf("old or cluster is nil")
	}

	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType, "Reconcile workflow begin")

	// Workflow 状态为 Succeeded 或 Paused, 跳过执行
	if workflow.Status.WorkflowPhase == ccetypes.WorkflowPhaseSucceeded ||
		workflow.Status.WorkflowPhase == ccetypes.WorkflowPhasePaused || workflow.Spec.Paused == true {
		logger.Infof(ctx, "Workflow %s Phase = %v, skip reconcile", workflow.GetName(), workflow.Status.WorkflowPhase)

		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Workflow %s Phase = %v, skip reconcile", workflow.GetName(), workflow.Status.WorkflowPhase)
		return nil
	}

	// 如果 Workflow 没有 Finalizer, 则加上 Finalizer
	if !util.Contains(workflow.Finalizers, ccev1.WorkflowFinalizer) {
		workflow.Finalizers = append(workflow.ObjectMeta.Finalizers, ccev1.WorkflowFinalizer)

		// The object exists in the key-value store until the garbage collector
		// deletes all the dependents whose ownerReference.blockOwnerDeletion=true
		// from the key-value store.  API sever will put the "foregroundDeletion"
		// finalizer on the object, and sets its deletionTimestamp.  This policy is
		// cascading, i.e., the dependents will be deleted with Foreground
		workflow.Finalizers = append(workflow.ObjectMeta.Finalizers, string(metav1.DeletePropagationForeground))
	}

	// 更新 StartTime
	if workflow.Status.StartTime.IsZero() {
		startTime := metav1.NewTime(time.Now())
		workflow.Status.StartTime = &startTime
	}

	// 初始化状态
	if workflow.Status.WorkflowPhase == "" {
		workflow.Status.WorkflowPhase = ccetypes.WorkflowPhasePending
	}

	// 更新 Workflow
	if err := r.updateWorkflow(ctx, old, workflow); err != nil {
		logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"UpdateWorkflow failed: %s", err)
		return err
	}

	// 执行 Workflow 操作
	if err := r.reconcileWorkflow(ctx, old, workflow); err != nil {
		logger.Errorf(ctx, "reconcileWorkflow failed: %v", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"reconcileWorkflow failed: %s", err)
		return err
	}

	// 更新 FinishedTime
	if workflow.Status.FinishedTime.IsZero() {
		finishedTime := metav1.NewTime(time.Now())
		workflow.Status.FinishedTime = &finishedTime
	}

	return nil
}

// reconcileDelete - 删除 Workflow
//
// PARAMS:
//   - ctx: The context to trace request
//   - old: WorkflowCRD
//   - workflow: WorkflowCRD
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) reconcileDelete(ctx context.Context, old, workflow *ccev1.Workflow) error {
	if old == nil {
		return fmt.Errorf("old is nil")
	}

	if workflow == nil {
		return fmt.Errorf("workflow is nil")
	}

	// 是否关联集群
	workflowID := workflow.GetName()
	clusterID := workflow.Spec.ClusterID
	if clusterID != "" {
		// 将关联集群设置为 Running
		if err := r.setClusterRunning(ctx, workflow); err != nil {
			logger.Errorf(ctx, "setClusterRunning failed: %s", err)
			return err
		}

		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Set cluster %s to running succeeded", clusterID)
	}

	// 删除 Finalizer
	workflow.Finalizers = util.Filter(workflow.Finalizers, string(metav1.DeletePropagationForeground))
	workflow.Finalizers = util.Filter(workflow.Finalizers, ccev1.WorkflowFinalizer)

	logger.Infof(ctx, "reconcileDelete %s succeeded", workflowID)
	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
		"reconcileDelete %s succeeded", workflowID)

	return nil
}

// setClusterRunning - 将关联 Cluster 设置为 Running
// 将 Cluster 设置为 Running 仅为一个体验相关操作, 未避免更大问题, 暂时不做错误处理
func (r *WorkflowReconciler) setClusterRunning(ctx context.Context, workflow *ccev1.Workflow) error {
	if workflow == nil {
		return fmt.Errorf("workflow is nil")
	}

	workflowID := workflow.GetName()
	clusterID := workflow.Spec.ClusterID
	if clusterID == "" {
		return fmt.Errorf("workflow.Spec.ClusterID is empty")
	}

	// 获取集群
	cluster, err := r.clientset.MetaClient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Infof(ctx, "Get cluster %s failed, skip set cluster running: %s", clusterID, err)
		return nil
	}

	// 非 Upgrade 和 Running 状态 Cluster 无需处理
	// 为什么 ClusterCRD == running 也需要更新 ?
	// 1. 提交 workflow 后未避免前端 cluster 仍为 running, cluster-service 会修改 cluster 数据库为 upgrading;
	// 2. clusterCRD 的状态是由 workflow-controller 来更新;
	// 3. 如果 workflow-controller 在执行更新 clusterCRD 为 upgrading 前失败, 则会出现 ClusterCRD 和数据库状态不一致情况。
	if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseUpgrading &&
		cluster.Status.ClusterPhase != ccetypes.ClusterPhaseUpgradeFailed &&
		cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning {
		logger.Infof(ctx, "Cluster %s phase=%s, skip set cluster running", clusterID, cluster.Status.ClusterPhase)
		return nil
	}

	// 获取关联 Workflow
	workflowList, err := r.clientset.MetaClient.ListWorkflowsByClusterID(ctx, consts.MetaClusterDefaultNamespace, clusterID)
	if err != nil {
		logger.Infof(ctx, "Cluster %s phase=%s, skip set cluster running", clusterID, cluster.Status.ClusterPhase)
		return nil
	}

	// 所有 Workflow 都为 Succeeded
	set := true
	for _, w := range workflowList.Items {
		// 排除自己
		if w.GetName() == workflowID {
			continue
		}

		// 非前置检查类型
		if _, ok := ccetypes.WorkflowsAllowMultipleExistWithinCluster[w.Spec.WorkflowType]; ok {
			continue
		}

		if w.Status.WorkflowPhase != ccetypes.WorkflowPhaseSucceeded {
			logger.Warnf(ctx, "Workflow %s not %s, skip set cluster running", w.GetName(), ccetypes.WorkflowPhaseSucceeded)
			set = false
			break
		}
	}

	// 设置为 Running
	if set {
		logger.Infof(ctx, "Set cluster %s to running", clusterID)

		if err := r.ensureClusterStatusUpdated(ctx, clusterID, clusterDefaultHandler, ccetypes.ClusterPhaseRunning); err != nil {
			logger.Errorf(ctx, "ensureClusterStatusUpdated failed: %s", err)
			return err
		}
	}

	return nil
}

// updateWorkflow - 更新 Workflow
//
// PARAMS:
//   - ctx: The context to trace request
//   - old: WorkflowCRD
//   - workflow: WorkflowCRD
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) updateWorkflow(ctx context.Context, old, workflow *ccev1.Workflow) error {
	if old == nil || workflow == nil {
		logger.Infof(ctx, "old or workflow is nil")
		return nil
	}

	// 更新 Phase
	if err := r.reconcilePhase(ctx, workflow); err != nil {
		logger.Errorf(ctx, "reconcilePhase failed: %v", err)
	}

	// 检查是否更新 ETCD
	if cmp.Equal(old, workflow) {
		logger.Infof(ctx, "Status not change, skip update etcd")
		return nil
	}

	logger.Infof(ctx, "UpdateWorkflow Diff=%s", cmp.Diff(old, workflow))

	// 更新 ETCD
	if err := r.metaclient.UpdateWorkflow(ctx, workflow.GetNamespace(), workflow.GetName(), workflow); err != nil {
		logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
		return err
	}

	// 重置 old
	workflow.DeepCopyInto(old)

	return nil
}

// reconcilePhase - 根据 Task 的 Phase 来整体更新 Workflow.Status.Phase
//
// PARAMS:
//   - ctx: The context to trace request
//   - workflow: WorkflowCRD
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) reconcilePhase(ctx context.Context, workflow *ccev1.Workflow) error {
	if workflow == nil {
		return fmt.Errorf("reconcilePhase failed: workflow is nil")
	}

	// 更新 FinishedTaskCount
	totalCount, finishedCount, err := finishedTaskCount(ctx, workflow.Status.TaskGroupList)
	if err != nil {
		logger.Errorf(ctx, "finishedTaskCount failed: %s", err)

		if workflow.Status.TotalTaskCount > 0 {
			totalCount = workflow.Status.TotalTaskCount
		}

		if workflow.Status.FinishedTaskCount > 0 {
			finishedCount = workflow.Status.FinishedTaskCount
		}
	}
	workflow.Status.TotalTaskCount = totalCount
	workflow.Status.FinishedTaskCount = finishedCount

	// Paused 状态 Workflow 处理
	if workflow.Spec.Paused == true {
		logger.Infof(ctx, "Workflow %s is paused", workflow.GetName())
		workflow.Status.WorkflowPhase = ccetypes.WorkflowPhasePaused
		return nil
	}

	// 达到最大重试次数, 状态为 Failed
	if r.config.DefaultMaxRetryCount > 0 &&
		workflow.Status.RetryCount > 0 &&
		workflow.Status.RetryCount == r.config.DefaultMaxRetryCount {
		workflow.Status.WorkflowPhase = ccetypes.WorkflowPhaseFailed
		return nil
	}

	// 初始化 Workflow 的状态为 pending
	if len(workflow.Status.TaskGroupList) == 0 {
		workflow.Status.WorkflowPhase = ccetypes.WorkflowPhasePending
		return nil
	}

	// 遍历 Task Phase
	// 存在一个 Task Failed, 则 Workflow 及对应 TaskGroup Failed
	// 每一个 Task Succeeded, 则 Workflow 及对应 TaskGroup Succeeded
	workflowFailed := false
	workflowSucceeded := true
	workflowPending := true
	for _, taskgroup := range workflow.Status.TaskGroupList {
		taskGroupFailed := false
		taskGroupSucceeded := true
		taskGroupPending := true

		// TaskGroup 中 Task 为空, 默认为 succeeded
		if len(taskgroup.TaskList) == 0 {
			taskgroup.TaskGroupPhase = ccetypes.WorkflowPhaseSucceeded
		}

		for _, task := range taskgroup.TaskList {
			if task.WorkflowTaskPhase == ccetypes.WorkflowPhaseFailed {
				workflowFailed = true
				taskGroupFailed = true
			}

			if task.WorkflowTaskPhase != ccetypes.WorkflowPhaseSucceeded {
				workflowSucceeded = false
				taskGroupSucceeded = false
			}

			if task.WorkflowTaskPhase != ccetypes.WorkflowPhasePending {
				workflowPending = false
				taskGroupPending = false
			}
		}

		// TaskGroup 状态
		taskgroup.TaskGroupPhase = phase(ctx, taskGroupFailed, taskGroupSucceeded, taskGroupPending)
	}

	// Workflow 状态
	workflow.Status.WorkflowPhase = phase(ctx, workflowFailed, workflowSucceeded, workflowPending)

	return nil
}

func phase(ctx context.Context, failed, succeeded, pending bool) ccetypes.WorkflowPhase {
	if failed {
		// 存在一个 Task 失败即为失败
		return ccetypes.WorkflowPhaseFailed
	} else if succeeded {
		// 所有 Task 成功才成功
		return ccetypes.WorkflowPhaseSucceeded
	} else if pending {
		// 所有 Task 都 pending 为 pending
		return ccetypes.WorkflowPhasePending
	}

	return ccetypes.WorkflowPhaseUpgrading
}

// reconcileWorkflow - 具体执行 Workflow
//
// PARAMS:
//   - ctx: The context to trace request
//   - workflow: WorkflowCRD
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) reconcileWorkflow(ctx context.Context, old, workflow *ccev1.Workflow) error {
	if workflow == nil {
		return fmt.Errorf("workflow is empty")
	}

	clusterID := workflow.Spec.ClusterID
	workflowType := workflow.Spec.WorkflowType
	workflowConfig := workflow.Spec.WorkflowConfig

	logger.Infof(ctx, "reconcileWorkflow begion: clusterID=%s workflowType=%s", clusterID, workflowType)
	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
		"reconcileWorkflow begion: clusterID=%s workflowType=%s", clusterID, workflowType)

	taskBaseConfig, err := r.generateTaskBaseConfig(ctx, workflow)
	if err != nil {
		return err
	}
	// 初始化 Workflow Client
	client, err := workflows.NewWorkflow(ctx, workflowType, taskBaseConfig, workflowConfig, nil)
	if err != nil {
		logger.Errorf(ctx, "NewWorkflow failed: %s", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"NewWorkflow failed: %s", err)
		return err
	}

	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
		"Init WorkflowClient succeeded")

	// 获取 TaskClients
	var taskClients map[ccetypes.TaskGroupName][]tasks.Interface
	taskClients, err = workflows.GetTaskClients(ctx, client)
	if err != nil {
		logger.Errorf(ctx, "GetTaskList failed: %s", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"GetTaskList failed: %s", err)
		return err
	}

	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
		"Init TaskClients succeeded")

	// 初始化 Workflow.Status.TaskList
	if err := r.ensureTaskGroupListInStatus(ctx, old, workflow, taskClients); err != nil {
		logger.Errorf(ctx, "ensureTaskGroupListInStatus failed: %s", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"ensureTaskGroupListInStatus failed: %s", err)
		return err
	}

	r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
		"Init TaskGroupList in WorkflowStatus succeeded")

	// 变更前更新 ClusterPhase & Handler
	needUpdateCluster := client.NeedUpdateCluster(ctx)
	if needUpdateCluster {
		if err := r.ensureClusterStatusUpdated(ctx, clusterID, clusterUpgradingHandler, ccetypes.ClusterPhaseUpgrading); err != nil {
			logger.Errorf(ctx, "ensureClusterStatusUpdated failed: %s", err)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
				"ensureClusterStatusUpdated failed: %s", err)
			return err
		}

		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Update cluster %s to upgrading succeeded", clusterID)
	}

	// 执行具体的 Task, 保证幂等
	for _, taskGroup := range workflow.Status.TaskGroupList {
		taskGroupName := taskGroup.TaskGroupName
		taskClientList, ok := taskClients[taskGroupName]
		if !ok {
			logger.Errorf(ctx, "TaskGroupName %s not exists in TaskClients", taskGroupName)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
				"TaskGroupName %s not exists in TaskClients", taskGroupName)
			return fmt.Errorf("TaskGroupName %s not exists in TaskClients", taskGroupName)
		}

		// 校验 taskClientList 和 TaskGroup 保证对应关系一致, 解决如下风险:
		// TODO:
		// 1. Workflow.Status.TaskGroupList 包含 Task 信息
		// 2. TaskClientList 真正去执行 Task 的 Client, 但是并不由 Task 信息来初始化
		// 3. 如果 Workflow 初始化 TaskClientList 不能保证幂等, 就可能出现 TaskClient 和 Task 不匹配的情况
		// 暂时去修复该问题比较复杂, 所以通过校验和报错提前发现问题
		if err := validateTaskClientList(ctx, taskGroup, taskClientList); err != nil {
			logger.Errorf(ctx, "validateTaskClientList failed: %s", err)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
				"validateTaskClientList failed: %s", err)
			return err
		}

		var errors []error
		wg := sync.WaitGroup{}
		concurrency := client.TaskConcurrency(ctx, taskGroupName)

		for i, task := range taskGroup.TaskList {
			taskType := task.WorkflowTaskType

			// 检查 workflow 是否 Paused
			workflowID := workflow.GetName()
			new, err := r.metaclient.GetWorkflow(ctx, consts.MetaClusterDefaultNamespace, workflowID, &metav1.GetOptions{})
			if err != nil && !meta.IsWorkflowNotExist(err) {
				logger.Errorf(ctx, "GetWorkflow failed: %v", err)
				r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
					"GetWorkflow failed: %v", err)
				return err
			}
			workflow.Spec = new.Spec // 注意 Status 不能覆盖!!!

			if meta.IsWorkflowNotExist(err) || workflow == nil {
				msg := fmt.Sprintf("Workflow %s is nil, skip exec task", workflowID)
				logger.Warnf(ctx, msg)
				r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType, msg)
				return nil
			}

			if workflow.Spec.Paused == true {
				workflow.Status.WorkflowPhase = ccetypes.WorkflowPhasePaused
				logger.Infof(ctx, "Workflow %s is paused, stop exec task", workflowID)
				r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
					"Workflow %s is paused, stop exec task", workflowID)
				return nil
			}

			// Succeeded 直接跳过
			if task.WorkflowTaskPhase == ccetypes.WorkflowPhaseSucceeded {
				logger.Infof(ctx, "Task %s succeed, skip execute: %s", taskType, utils.ToJSON(task))
				continue
			}

			// 更新状态为 Upgrading
			if task.WorkflowTaskPhase == ccetypes.WorkflowPhasePending ||
				task.WorkflowTaskPhase == ccetypes.WorkflowPhaseFailed || // 重试后更新状态
				task.WorkflowTaskPhase == "" {
				task.WorkflowTaskPhase = ccetypes.WorkflowPhaseUpgrading
			}

			// 初始化 StartTime
			if task.StartTime == nil {
				now := metav1.NewTime(time.Now())
				task.StartTime = &now
			}

			// 更新 Workflow
			if err := r.updateWorkflow(ctx, old, workflow); err != nil {
				logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
				r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
					"UpdateWorkflow failed: %s", err)
				return err
			}

			// 执行 Task
			taskClient := taskClientList[i]
			if concurrency {
				// 并行执行 Task
				wg.Add(1)
				go r.concurrencyTask(ctx, &wg, &errors, old, workflow, task, taskClient)
			} else {
				// 串行执行 Task
				if err := r.executeTask(ctx, task, taskClient); err != nil {
					logger.Errorf(ctx, "Task %s execute failed: %s", taskClient.Name(ctx), err)
					r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
						"Task %s execute failed: %s", taskClient.Name(ctx), err)
					return err
				}

				// 更新 Workflow
				if err := r.updateWorkflow(ctx, old, workflow); err != nil {
					logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
					r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
						"UpdateWorkflow failed: %s", err)
					return err
				}
			}
		}

		wg.Wait()

		if len(errors) > 0 {
			return fmt.Errorf("%v", errors)
		}
	}

	// 变更后更新 ClusterPhase & Handler
	if needUpdateCluster {
		if err := r.ensureClusterStatusUpdated(ctx, clusterID, clusterDefaultHandler, ccetypes.ClusterPhaseRunning); err != nil {
			logger.Errorf(ctx, "ensureClusterStatusUpdated failed: %s", err)
			return err
		}
	}

	return nil
}

// concurrencyTask - 并行执行 Task
// TODO: 并行更新 Workflow 冲突
func (r *WorkflowReconciler) concurrencyTask(ctx context.Context, wg *sync.WaitGroup, errors *[]error, old, workflow *ccev1.Workflow,
	task *ccetypes.WorkflowTask, taskClient tasks.Interface) {
	defer wg.Done()

	if err := r.executeTask(ctx, task, taskClient); err != nil {
		logger.Errorf(ctx, "Task %s execute failed: %s", taskClient.TaskType(ctx), err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"Task %s execute failed: %s", taskClient.Name(ctx), err)
		*errors = append(*errors, err)
		return
	}

	// 更新 Workflow
	if err := r.updateWorkflow(ctx, old, workflow); err != nil {
		logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"UpdateWorkflow failed: %s", err)
		*errors = append(*errors, err)
		return
	}
}

// generateWorkflowTaskGroupList - 生成 TaskGroup 写入 Status.TaskGroupList
func (r *WorkflowReconciler) ensureTaskGroupListInStatus(ctx context.Context, old, workflow *ccev1.Workflow,
	taskClients map[ccetypes.TaskGroupName][]tasks.Interface) error {
	if workflow == nil {
		return fmt.Errorf("workflow is nil")
	}

	// TODO: workflow.Status.TaskGroupList 和期望的不一致, 允许更新
	if len(workflow.Status.TaskGroupList) != 0 {
		logger.Infof(ctx, "workflow.Status.TaskGroupList exites, skip update")
		return nil
	}

	// 构建 TaskList
	taskGroupList := []*ccetypes.TaskGroup{}

	// TODO: 暂定 4 个阶段, 没有太大的讲究, 主要为增加可读性
	for _, taskGroupName := range []ccetypes.TaskGroupName{
		ccetypes.TaskGroupNamePreCheck,
		ccetypes.TaskGroupNameBackup,
		ccetypes.TaskGroupNameOperate,
		ccetypes.TaskGroupNamePostCheck,
	} {
		logger.Infof(ctx, "TaskGroupName: %s", taskGroupName)

		taskClientList, ok := taskClients[taskGroupName]
		if !ok {
			logger.Errorf(ctx, "TaskGroupName %s not in taskClients, skip", taskGroupName)
			continue
		}

		taskList := []*ccetypes.WorkflowTask{}
		for _, t := range taskClientList {
			taskConfigStr := ""

			taskType := t.TaskType(ctx)
			taskConfig := t.TaskConfig(ctx)

			// 强制所有 Task 实现 TaskConfig 方法, 不能继承 BaseTask 实现, 原因如下:
			// 1. TaskType 和 TaskConfig 唯一标识一个 Task, 并记录在 Status 中;
			// 2. 如果在 Controller 在执行过程中重启, len(workflow.Status.TaskGroupList) != 0;
			// 3. 出现 2 的情况后, Controller 需要可以从 Status 构建出完整的 Task 信息。
			if taskConfig == nil {
				logger.Errorf(ctx, "Task %s TaskConfig is nil", taskType)
				return fmt.Errorf("Task %s TaskConfig is nil", taskType)
			}

			if taskConfig != nil {
				taskConfigStr = utils.ToJSON(taskConfig)
			}

			taskList = append(taskList, &ccetypes.WorkflowTask{
				TaskName:          t.Name(ctx),
				TaskConfig:        []byte(taskConfigStr),
				WorkflowTaskType:  taskType,
				WorkflowTaskPhase: ccetypes.WorkflowPhasePending,
			})
		}

		taskGroupList = append(taskGroupList, &ccetypes.TaskGroup{
			TaskGroupName:  taskGroupName,
			TaskList:       taskList,
			TaskGroupPhase: ccetypes.WorkflowPhasePending,
		})
	}

	workflow.Status.TaskGroupList = taskGroupList

	// 更新 Workflow
	if err := r.updateWorkflow(ctx, old, workflow); err != nil {
		logger.Errorf(ctx, "UpdateWorkflow failed: %v", err)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
			"UpdateWorkflow failed: %s", err)
		return err
	}

	return nil
}

// executeTask - 具体执行 Task, 更新 Task 数据
//
// PARAMS:
//   - ctx: The context to trace request
//   - task: *ccetypes.WorkflowTask
//   - taskClient: tasks.Interface
//
// RETURNS:
//
//	error: nil if succeed, error if fail
func (r *WorkflowReconciler) executeTask(ctx context.Context, task *ccetypes.WorkflowTask, taskClient tasks.Interface) error {
	taskType := task.WorkflowTaskType
	target, _ := taskClient.Targets(ctx)

	logger.Infof(ctx, "Execute Task %s start: %s", taskType, utils.ToJSON(target))

	// 更新 Task 运行状态
	var err error
	var result tasks.TaskExecuteResult
	defer func() {
		logger.Infof(ctx, "Execute Task %s finished", taskType)

		// 成功执行
		if err == nil || models.ErrWorkflowNeedPause.Is(err) {
			now := metav1.NewTime(time.Now())
			task.FinishedTime = &now

			task.WorkflowTaskPhase = ccetypes.WorkflowPhaseSucceeded
		} else if models.ErrWorkflowNeedWait.Is(err) {
			task.ErrorMessage = err.Error()
		} else {
			task.ErrorMessage = err.Error()
			//预付费模式下，余额小于集群创建费用，则WorkflowTaskPhase返回待确定状态
			if strings.Contains(err.Error(), "[Balance needs to be confirmed.]") {
				task.WorkflowTaskPhase = ccetypes.WorkflowPhaseVerifying
			} else {
				task.WorkflowTaskPhase = ccetypes.WorkflowPhaseFailed
			}
		}

		// 更新 TaskExecuteResult
		if result != nil {
			logger.Infof(ctx, "Task %s ExecuteInfo: %v", taskType, result)
			task.TaskExecuteResult = []byte(utils.ToJSON(result))
		}

		// Targets 信息
		targets, err := taskClient.Targets(ctx)
		if err != nil {
			logger.Errorf(ctx, "Targets failed: %s", err)
		}

		// 执行 Clean 策略
		if err := taskClient.Clean(ctx, *task); err != nil {
			logger.Errorf(ctx, "Clean failed: %s", err)
		}

		logger.Infof(ctx, "Task Targets after upgrade: %s", utils.ToJSON(targets))
	}()

	// Targets 信息
	targets, err := taskClient.Targets(ctx)
	if err != nil {
		logger.Errorf(ctx, "Targets failed: %s", err)
		return err
	}

	logger.Infof(ctx, "Task Targets before upgrade: %s", utils.ToJSON(targets))

	// 是否执行变更
	need, err := taskClient.NeedUpgrade(ctx, *task)
	if err != nil {
		logger.Errorf(ctx, "NeedUpgrade failed: %s", err)
		return err
	}

	if !need {
		logger.Infof(ctx, "NeedUpgrade return false, skip upgrade")
		return nil
	}

	logger.Infof(ctx, "NeedUpgrade return true, start upgrade")

	// 前置检查
	err = taskClient.CheckStatusBeforeExecute(ctx, *task)
	if err != nil {
		logger.Errorf(ctx, "CheckStatusBeforeExecute failed: %s", err)
		return err
	}

	// 变更元数据
	err = taskClient.UpdateMetaBeforeExecute(ctx, *task)
	if err != nil {
		logger.Errorf(ctx, "UpdateMetaBeforeExecute failed: %s", err)
		return err
	}

	// 执行 Task
	result, err = taskClient.Execute(ctx, *task)
	if err != nil {
		logger.Errorf(ctx, "Task %s execute failed: %s", taskType, err)
		return err
	}

	if result != nil {
		logger.Infof(ctx, "taskClient.Execute result: %s", utils.ToJSON(result))
	}

	// 后置检查
	err = checkStatusAfterExecute(ctx, task, taskClient)
	if err != nil {
		logger.Errorf(ctx, "checkStatusAfterExecute failed: %s", err)
		return err
	}

	// 变更后更新元数据
	// TODO: 是否支持 upgradeFailed, 否则需要放到 defer 中, 暂时不处理
	err = taskClient.UpdateMetaAfterExecute(ctx, *task)
	if err != nil {
		logger.Errorf(ctx, "UpdateMetaAfterExecute failed: %s", err)
		return err
	}

	return nil
}

// ensureClusterStatusUpdated - 保证 ClusterPhase 和 Handler 状态更新, 实现幂等
func (r *WorkflowReconciler) ensureClusterStatusUpdated(ctx context.Context, clusterID string,
	handler string, clusterPhase ccetypes.ClusterPhase) error {
	if clusterID == "" {
		logger.Infof(ctx, "clusterID is empty")
		return nil
	}

	if handler == "" {
		return fmt.Errorf("handler is empty")
	}

	if clusterPhase != ccetypes.ClusterPhaseRunning && clusterPhase != ccetypes.ClusterPhaseUpgrading {
		return fmt.Errorf("clusterPhase not %s or %s", ccetypes.ClusterPhaseRunning, ccetypes.ClusterPhaseUpgrading)
	}

	// 获取 Cluster
	cluster, err := r.metaclient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "MetaClient.GetCluster failed: %s", err)
		return err
	}

	// 更新 Handler 及 ClusterPhase, 防止 ClusterController 进行处理
	cluster.Spec.Handler = handler
	cluster.Status.ClusterPhase = clusterPhase

	// 更新 Cluster CRD
	if err := r.metaclient.UpdateCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, cluster); err != nil {
		logger.Errorf(ctx, "MetaClient.UpdateCluster failed: %s", err)
		return err
	}

	// 更新 Cluster 数据库
	if cluster.Spec.AccountID == "" {
		logger.Errorf(ctx, "cluster.Spec.AccountID is empty")
		return fmt.Errorf("cluster.Spec.AccountID is empty")
	}

	if err := r.model.UpdateClusterPhase(ctx, clusterID, cluster.Spec.AccountID, clusterPhase); err != nil {
		logger.Errorf(ctx, "Model.UpdateClusterPhase failed: %s", err)
		return err
	}

	return nil
}

func (r *WorkflowReconciler) generateTaskBaseConfig(ctx context.Context, workflow *ccev1.Workflow) (*tasks.TaskBaseConfig, error) {
	clusterID := workflow.Spec.ClusterID
	cceClusterVersion := utils.CCEClusterVersion(ctx, clusterID)
	// 获取 Cluster 和 InstanceList
	var cluster *ccev1.Cluster
	var cceInstanceIDToVPCIP map[string]string
	var cceInstanceIDToHostname map[string]string
	var failedPodOwnerReferencesSet map[types.UID]interface{}
	var err error
	if clusterID != "" && cceClusterVersion == utils.ClusterVersionV2 {
		// Cluster
		cluster, err = r.metaclient.GetCluster(ctx, consts.MetaClusterDefaultNamespace, clusterID, &metav1.GetOptions{})
		if err != nil {
			logger.Errorf(ctx, "GetCluster failed: %s", err)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
				"GetCluster failed: %s", err)
			return nil, err
		}

		logger.Infof(ctx, "Get workflow's associated cluster %s succeeded", clusterID)
		r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeNormal, ReconcileWorkflowEventType,
			"Get workflow's associated cluster %s succeeded", clusterID)

		// InstanceList
		instanceList, err := r.metaclient.ListInstances(ctx, consts.MetaClusterDefaultNamespace, &metav1.ListOptions{
			LabelSelector: fmt.Sprintf("%s=%s", ccetypes.ClusterIDLabelKey, clusterID),
		})
		if err != nil {
			logger.Errorf(ctx, "ListInstances from %s failed: %s", clusterID, err)
			r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
				"ListInstances failed: %s", err)
			return nil, err
		}

		// 构建 CCEInstanceID to VPCIP 映射
		cceInstanceIDToVPCIP = map[string]string{}
		cceInstanceIDToHostname = map[string]string{}
		for _, instance := range instanceList.Items {
			if instance.Status.Machine.VPCIP != "" {
				cceInstanceIDToVPCIP[instance.GetName()] = instance.Status.Machine.VPCIP
			}
			if instance.Status.Machine.Hostname != "" {
				cceInstanceIDToHostname[instance.GetName()] = instance.Status.Machine.Hostname
			}
		}

		// 构建失败 Pod 对应 OwnerReferences 列表
		if needGenerateFailedPodOwnerReferencesSet(ctx, workflow) {
			failedPodOwnerReferencesSet, err = r.generateFailedPodOwnerReferencesSet(ctx, clusterID)
			if err != nil {
				logger.Errorf(ctx, "generateFailedPodOwnerReferencesSet from %s failed: %s", clusterID, err)
				r.recorder.RecordEventf(ctx, workflow, corev1.EventTypeWarning, ReconcileWorkflowEventType,
					"generateFailedPodOwnerReferencesSet failed: %s", err)
				return nil, err
			}
		}

		logger.Infof(ctx, "generate FailedPodOwnerReferencesSet succeeded: %s", utils.ToJSON(failedPodOwnerReferencesSet))
	}

	// TaskBaseConfig
	return &tasks.TaskBaseConfig{
		ClusterID:                   clusterID,
		AccountID:                   workflow.Spec.AccountID,
		WorkflowID:                  workflow.Spec.WorkflowID,
		Cluster:                     cluster,
		DeployerConfig:              r.config.DeployerConfig,
		Clients:                     r.clientset,
		CCEInstanceIDToVPCIP:        cceInstanceIDToVPCIP,
		CCEInstanceIDToHostname:     cceInstanceIDToHostname,
		FailedPodOwnerReferencesSet: failedPodOwnerReferencesSet,
		PluginConfig:                r.config.PluginConfig,
		ProviderConfig:              r.config.ProviderConfig,
	}, nil

}

// generateFailedPodOwnerReferencesSet - 获取失败 Pod OwnerReference 映射
func (r *WorkflowReconciler) generateFailedPodOwnerReferencesSet(ctx context.Context,
	clusterID string) (map[types.UID]interface{}, error) {
	if clusterID == "" {
		return nil, fmt.Errorf("clusterID is nil")
	}

	// 获取用户 K8S Client
	kubeconfig, err := r.model.GetAdminKubeConfig(ctx, clusterID, models.KubeConfigTypeInternal)
	if err != nil {
		logger.Errorf(ctx, "GetAdminKubeConfig failed: %s", err)
		return nil, err
	}

	// 初始化用户集群 kubernetes.Interface
	client, err := utils.NewK8SClient(ctx, kubeconfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "uitls.NewK8SClient failed: %s", err)
		return nil, err
	}

	// 获取 PodList
	podList, err := client.CoreV1().Pods(metav1.NamespaceAll).List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "ListPods failed, use Annotation 'kubernetes.io/cce.workflow.skip-generate-failed-pods' to skip: %s", err)
		return nil, err
	}

	return getPodsOwnerReferencesSet(ctx, podList), nil
}

func getPodsOwnerReferencesSet(ctx context.Context, podList *corev1.PodList) map[types.UID]interface{} {
	result := map[types.UID]interface{}{}
	if podList == nil {
		return result
	}

	for _, pod := range podList.Items {
		if !pod.DeletionTimestamp.IsZero() {
			continue
		}

		ready, err := tasks.CheckPodReady(ctx, &pod)
		if err != nil || ready == false {
			if len(pod.ObjectMeta.OwnerReferences) >= 1 {
				// TODO: [0] 够用么
				ownerReferenceUID := pod.ObjectMeta.OwnerReferences[0].UID
				result[ownerReferenceUID] = nil
			}
		}
	}

	return result
}

// checkStatusAfterExecute - 变更完成后置检查, 支持轮询和超时
func checkStatusAfterExecute(ctx context.Context, task *ccetypes.WorkflowTask, taskClient tasks.Interface) error {
	tickerInterval := taskClient.CheckStatusAfterExecuteInterval(ctx)
	if tickerInterval == 0 {
		return nil
	}

	// 便于测试注入, 增加 Exec 后执行 checkStatusAfterExecute 间隔, 为手动构建异常场景提供时间
	interval := os.Getenv("CheckStatusInterval")
	if interval != "" {
		seconds, err := strconv.Atoi(interval)
		if err == nil {
			logger.Infof(ctx, "Env CheckStatusInterval set")
			tickerInterval = time.Duration(seconds) * time.Second
		}
	}

	ticker := time.NewTicker(tickerInterval)
	defer ticker.Stop()

	timer := time.NewTimer(taskClient.CheckStatusAfterExecuteTimeout(ctx))
	defer timer.Stop()

	var err error
	for {
		select {
		case <-ticker.C:
			err = taskClient.CheckStatusAfterExecute(ctx, *task)
			if err == nil {
				logger.Infof(ctx, "CheckStatusAfterExecute %s succeeded", utils.ToJSON(task))
				return nil
			}

			task.ErrorMessage = err.Error()
			logger.Warnf(ctx, "CheckStatusAfterExecute %s failed, retry: %s", utils.ToJSON(task), err)
		case <-timer.C:
			taskName := task.TaskName
			logger.Errorf(ctx, "timeout waiting for Task %s succeeded: %s", taskName, err)
			return fmt.Errorf("timeout waiting for Task %s succeeded: %s", taskName, err)
		}
	}
}

// finishedTaskCount - 已经完成 Task 数
func finishedTaskCount(ctx context.Context, taskGroupList []*ccetypes.TaskGroup) (int, int, error) {
	total := 0
	finished := 0

	for _, tg := range taskGroupList {
		for _, task := range tg.TaskList {
			total = total + 1

			if task.WorkflowTaskPhase == ccetypes.WorkflowPhaseSucceeded {
				finished = finished + 1
			}
		}
	}

	return total, finished, nil
}

// validateTaskClientList - 校验 TaskGroup.TaskList 和 TaskClientList 一一对应
// TODO: 修改代码可能会导致不一致, 暂时不处理
func validateTaskClientList(ctx context.Context, taskGroup *ccetypes.TaskGroup, taskClientList []tasks.Interface) error {
	if taskGroup == nil {
		return fmt.Errorf("taskGroup is nil")
	}

	if len(taskGroup.TaskList) != len(taskClientList) {
		return fmt.Errorf("len(taskGroup.TaskList) != len(taskClientList)")
	}

	for i, task := range taskGroup.TaskList {
		client := taskClientList[i]

		// TaskType
		gotTaskType := client.TaskType(ctx)
		if gotTaskType != task.WorkflowTaskType {
			return fmt.Errorf("client.TaskType() %s != task.WorkflowTaskType %s", gotTaskType, task.WorkflowTaskType)
		}

		// TaskName
		gotTaskName := client.Name(ctx)
		if gotTaskName != task.TaskName {
			return fmt.Errorf("client.Name() %s != task.TaskName %s", gotTaskName, task.TaskName)
		}
	}

	return nil
}

func needGenerateFailedPodOwnerReferencesSet(ctx context.Context, workflow *ccev1.Workflow) bool {
	if workflow == nil {
		return false
	}

	if workflow.Annotations == nil {
		return true
	}

	if _, ok := workflow.Annotations[ccev1.AnnotationSkipGenerateFailedPods]; ok {
		return false
	}

	return true
}
func (r *WorkflowReconciler) fillConf(ctx context.Context) {
	logger.Infof(ctx, "Fill DeployerV2Config")
	// sts 认证可能需要以下参数
	if r.config.DeployerConfig.Region == "" {
		logger.Errorf(ctx, "DeployerV2Config.Region is empty")
		r.config.DeployerConfig.Region = r.config.ProviderConfig.Region
	}
	if r.config.DeployerConfig.STSEndpoint == "" {
		logger.Errorf(ctx, "DeployerV2Config.STSEndpoint is empty")
		r.config.DeployerConfig.STSEndpoint = r.config.ProviderConfig.STSEndpoint
	}
	if r.config.DeployerConfig.IAMEndpoint == "" {
		logger.Errorf(ctx, "DeployerV2Config.IAMEndpoint is empty")
		r.config.DeployerConfig.IAMEndpoint = r.config.ProviderConfig.IAMEndpoint
	}
	if r.config.DeployerConfig.ServiceRoleName == "" {
		logger.Errorf(ctx, "DeployerV2Config.ServiceRoleName is empty")
		r.config.DeployerConfig.ServiceRoleName = r.config.ProviderConfig.ServiceRoleName
	}
	if r.config.DeployerConfig.ServiceName == "" {
		logger.Errorf(ctx, "DeployerV2Config.ServiceName is empty")
		r.config.DeployerConfig.ServiceName = r.config.ProviderConfig.ServiceName
	}
	if r.config.DeployerConfig.ServicePassword == "" {
		logger.Errorf(ctx, "DeployerV2Config.ServicePassword is empty")
		r.config.DeployerConfig.ServicePassword = r.config.ProviderConfig.ServicePassword
	}
	if r.config.DeployerConfig.Timeout == 0 {
		logger.Errorf(ctx, "DeployerV2Config.Timeout is empty")
		r.config.DeployerConfig.Timeout = r.config.ProviderConfig.Timeout
	}
}
