// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/12/13 14:18:00, by <EMAIL>, create
*/
/*
WorkflowController 通用配置
*/

package controllers

import (
	"context"
	"fmt"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/clientset"
	dptypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/clients"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/cluster/cluster-controller/provider"
)

// Configuration - WorkflowController 通用配置
type Configuration struct {
	MetaClusterKubeConfig string `json:"MetaClusterKubeConfig"` // MetaCluster kubeconfig 路径

	MySQLConnConfig string `json:"MySQLConnConfig"` // MySQL 的连接配置

	WorkflowHandlerAnnotation string `json:"WorkflowHandlerAnnotation"` // Controller 匹配 Workflow, 默认 "default"

	// Reconcile 流程失败, 最大重试次数
	// MaxRetryCount <=0 表示不作限制, 无限重试
	DefaultMaxRetryCount int `json:"DefaultMaxRetryCount"`

	// 部署依赖配置
	DeployerConfig *dptypes.Config `json:"DeployerConfig"`

	// 三方 Client Config
	ClientSetConfig *clientset.Config `json:"ClientSetConfig"`

	// PluginConfig helm config
	PluginConfig *clients.Config `json:"PluginConfig"`

	ProviderConfig *provider.Config `json:"ProviderConfig"`
}

// CheckConfig 检查配置是否合法
func CheckConfig(ctx context.Context, config Configuration) error {
	if config.MySQLConnConfig == "" {
		return fmt.Errorf("config.MySQLConnConfig is empty")
	}

	if config.DeployerConfig == nil {
		return fmt.Errorf("config.DeployerConfig is nil")
	}

	if config.DeployerConfig.CCEAgentImage == "" {
		return fmt.Errorf("config.DeployerConfig.CCEAgentImage is empty")
	}

	if config.ClientSetConfig == nil {
		return fmt.Errorf("config.ClientSetConfig is nil")
	}

	if config.WorkflowHandlerAnnotation == "" {
		config.WorkflowHandlerAnnotation = "default"
	}

	return nil
}
