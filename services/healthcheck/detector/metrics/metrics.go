// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2022/07/07 09:54, by <EMAIL>, create
*/
/*
DESCRIPTION
CCE HealthCheck Metrics 指标定义。
*/

package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

const (
	// MetricsCCEK8SPluginImageVersion - Workflow 数量
	MetricsCCEK8SPluginImageVersion string = "cce_k8s_plugin_image_version"
)

const (
	// LabelAccountID - Account ID
	LabelAccountID = "accountID"

	// LabelClusterID - Cluster ID
	LabelClusterID = "clusterID"

	// LabelImageID -  Image ID
	LabelImageID = "imageID"

	// LabelReplicas - Replicas
	LabelReplicas = "replicas"
)

func init() {
	prometheus.MustRegister(cceK8SPluginImageVersion)
}

var (
	// 统计 K8S 插件镜像版本
	cceK8SPluginImageVersion = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Name: MetricsCCEK8SPluginImageVersion,
			Help: "cce k8s plugin image version",
		},
		cceK8SPluginImageVersionLabels,
	)

	// 统计 Workflow 数量 Labels
	cceK8SPluginImageVersionLabels = []string{
		LabelAccountID,
		LabelClusterID,
		LabelImageID,
		LabelReplicas,
	}
)

// CCEK8SPluginImageVersionSetCount - 设置 Metrics 值
func CCEK8SPluginImageVersionSetCount(labels prometheus.Labels, count int) {
	ls := ensureKey(cceK8SPluginImageVersionLabels, labels)

	cceK8SPluginImageVersion.With(ls).Set(float64(count))
}

func ensureKey(keys []string, labels prometheus.Labels) prometheus.Labels {
	ls := prometheus.Labels{}
	for _, key := range keys {
		ls[key] = labels[key]
	}

	return ls
}
