/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @File:  tpl
 * @Version: 1.0.0
 * @Date: 2020-10-14 13:36
 */

package consts

var InstanceCheckCronJobTpl = `
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: instance-hc
  namespace: health-check
  annotations:
    version: "{{.CronJobVersion}}"
spec:
  schedule: "*/{{.CheckSchedule}} * * * *"
  concurrencyPolicy: "Forbid" # 不允许并发执行
  startingDeadlineSeconds: 300   # 300 秒内失败会重试重试
  failedJobsHistoryLimit: 5      # 保留失败的 Job 数量
  successfulJobsHistoryLimit: 5  # 保留成功的 Job 数量
  jobTemplate:
    spec:
      backoffLimit: 2
      template:
        spec:
          serviceAccount: health-check
          restartPolicy: OnFailure
          containers:
            - name: instance-detector
              image: hub.baidubce.com/cce/instance-detector:{{.CronJobVersion}}
              imagePullPolicy: Always
              args:
                - -prometheusURL={{.PrometheusURL}}
                - -cceServiceDB={{.CCEServiceDB}}
`

var ClusterCheckCronJobTpl = `
apiVersion: batch/v1beta1
kind: CronJob
metadata:
  name: cluster-hc
  namespace: health-check
  annotations:
    version: "{{.CronJobVersion}}"
spec:
  schedule: "*/{{.CheckSchedule}} * * * *"
  concurrencyPolicy: "Forbid" # 不允许并发执行
  startingDeadlineSeconds: 300   # 300 秒内失败会重试重试
  failedJobsHistoryLimit: 5      # 保留失败的 Job 数量
  successfulJobsHistoryLimit: 5  # 保留成功的 Job 数量
  jobTemplate:
    spec:
      backoffLimit: 2
      template:
        spec:
          serviceAccount: health-check
          restartPolicy: OnFailure
          containers:
            - name: cluster-detector
              image: hub.baidubce.com/cce/cluster-detector:{{.CronJobVersion}}
              imagePullPolicy: Always
              args:
                - -prometheusURL={{.PrometheusURL}}
                - -cceServiceDB={{.CCEServiceDB}}
`
