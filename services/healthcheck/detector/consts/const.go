/**
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Description:
 * @File:  const
 * @Version: 1.0.0
 * @Date: 2020-10-26 16:34
 */

package consts

const (
	MetaClusterKubeConfig = ""
	DefaultNamespace      = "health-check"
	ClusterNamespace      = "default"
	InstanceNamespace     = "default"
	ClusterName           = "clusterName"
	ClusterID             = "clusterID"
	Region                = "region"
	InstanceHCJob         = "instance-hc"
	ClusterHCJob          = "cluster-hc"
	CronJobVersionName    = "version"
	CronJobVersionValue   = "v1"
)

type JobSpec struct {
	CheckSchedule  int64
	PrometheusURL  string
	CCEServiceDB   string
	CronJobVersion string
}
