// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/07 18:31:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CPromAgent 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"
	"strings"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/kubernetes"
)

// CPromAgent -
type cpromAgent struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCPromAgent -
func NewCPromAgent(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &cpromAgent{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *cpromAgent) SetPluginCondition(_ context.Context, _ *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *cpromAgent) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := make([]ccev1.PluginImage, 0)

	pods, err := c.userK8SClusterClient.CoreV1().Pods("cprom-system").List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "list pod from cprom-system err: %v", err)
		return nil, err

	}

	set := sets.NewString()

	for _, item := range pods.Items {
		// cprom agent name 开头都是 agent-xxx
		if !strings.HasPrefix(item.Name, "agent-") {
			continue
		}

		res, err := getImageIDFromPod(ctx, item)
		if err != nil {
			logger.Warnf(ctx, "getImageIDFromPod err: %v", err)
			continue
		}

		set.Insert(res...)
	}

	for _, item := range set.List() {
		result = append(result, ccev1.PluginImage{
			ImageID: item,
		})
	}

	return result, nil
}
