package plugins

import (
	"context"
	"fmt"

	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// APIServerExternalAuditor -
type APIServerExternalAuditor struct {
	userK8SClusterClient kubernetes.Interface
}

// NewAPIServerExternalAuditor -
func NewAPIServerExternalAuditor(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}

	c := &APIServerExternalAuditor{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *APIServerExternalAuditor) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	exists := false

	// 集群中存在 kube-system/audit-config 的 configmap 则证明开启
	configmap, err := c.userK8SClusterClient.CoreV1().ConfigMaps("kube-system").Get(ctx, "audit-config", metav1.GetOptions{})
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Errorf(ctx, "Get ConfigMaps kube-system/audit-config failed: %s", err)
		return err
	}

	// 审计未开启
	if kerrors.IsNotFound(err) {
		logger.Infof(ctx, "Configmap kube-system/audit-config not exists")
		pluginConditions.APIServerExternalAuditor = &ccev1.APIServerExternalAuditor{
			EnableExternalAuditor: &exists,
		}
		return nil
	}

	// 审计开启
	exists = true
	auditEndpoint, ok := configmap.Data["endpoint"]
	if !ok {
		pluginConditions.APIServerExternalAuditor = &ccev1.APIServerExternalAuditor{
			EnableExternalAuditor: &exists,
			BCTEndpoint:           "endpoint not exists",
		}
		return nil
	}

	pluginConditions.APIServerExternalAuditor = &ccev1.APIServerExternalAuditor{
		EnableExternalAuditor: &exists,
		BCTEndpoint:           auditEndpoint,
	}
	return nil
}

// GetPluginImages -
func (c *APIServerExternalAuditor) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	return nil, nil
}
