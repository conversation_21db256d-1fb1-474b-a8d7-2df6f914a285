package plugins

import (
	"fmt"

	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/addon"
)

// DefaultPluginsToCheck - 检查的 Plugin
func DefaultPluginsToCheck() []addon.Type {
	plugins := []addon.Type{
		"prometheus",
		"cce-log-agent",
		"api-server-external-auditor",
		"cce-csi-pfs",
		"cce-flex-volume-cds",
		"cce-gpu-manager",
		"cce-fluid-controller",
		"cce-ai-scheduler",
		"cce-rdma-plugin",
		"cprom-agent",
		addon.AddOnCCECSIBOSPlugin,
		addon.AddOnCCECSICDSPlugin,
		addon.AddonIPMasqAgent,
		addon.AddonTelegraf,
		addon.AddOnCCELBController,
		addon.AddonCCEIngressController,
		addon.AddonCoreDNS,
		addon.Addon<PERSON>luster<PERSON>utosca<PERSON>,
		addon.AddOnCCECloudNodeController,
		addon.AddonVPCNative,
		addon.AddonVPCRoute,
		addon.AddonNvidiaDevicePlugin,
	}
	return plugins
}

// PluginClient -
type PluginClient struct {
	userK8SClusterClient kubernetes.Interface
}

// NewPluginClient -
func NewPluginClient(userK8SClusterClient kubernetes.Interface) (*PluginClient, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}

	client := &PluginClient{
		userK8SClusterClient: userK8SClusterClient,
	}
	return client, nil
}

// GetPluginClient -
func (client *PluginClient) GetPluginClient(pluginType addon.Type) (Interface, error) {
	switch string(pluginType) {
	case string(addon.AddOnCCELBController):
		return NewCCELBController(client.userK8SClusterClient)
	case string(addon.AddonCCEIngressController):
		return NewCCEIngressController(client.userK8SClusterClient)
	case string(addon.AddonCoreDNS):
		return NewCoreDNS(client.userK8SClusterClient)
	case string(addon.AddonClusterAutoscaler):
		return NewClusterAutoScaler(client.userK8SClusterClient)
	case string(addon.AddOnCCECloudNodeController):
		return NewCCECloudNodeController(client.userK8SClusterClient)
	case string(addon.AddonVPCNative):
		return NewCCEVPCNativeCNI(client.userK8SClusterClient)
	case string(addon.AddonVPCRoute):
		return NewCCEVPCRouteCNI(client.userK8SClusterClient)
	case string(addon.AddonIPMasqAgent):
		return NewCCEIPMASQAgent(client.userK8SClusterClient)
	case string(addon.AddonTelegraf):
		return NewCCETelegraf(client.userK8SClusterClient)
	case string(addon.AddonNvidiaDevicePlugin):
		return NewNividiaDevicePlugin(client.userK8SClusterClient)
	case string(addon.AddOnCCECSIBOSPlugin):
		return NewCCECSIBOS(client.userK8SClusterClient)
	case string(addon.AddOnCCECSICDSPlugin):
		return NewCCECSICDS(client.userK8SClusterClient)
	case "cce-flex-volume-cds":
		return NewCCEFlexVolumeCDS(client.userK8SClusterClient)
	case "cce-csi-pfs":
		return NewCCECSIPFS(client.userK8SClusterClient)
	case "prometheus":
		return NewPrometheus(client.userK8SClusterClient)
	case "cce-log-agent":
		return NewCCELogAgent(client.userK8SClusterClient)
	case "api-server-external-auditor":
		return NewAPIServerExternalAuditor(client.userK8SClusterClient)
	case "cce-gpu-manager":
		return NewCCEGPUManager(client.userK8SClusterClient)
	case "cce-fluid-controller":
		return NewCCEFluidController(client.userK8SClusterClient)
	case "cce-ai-scheduler":
		return NewCCEAIScheduler(client.userK8SClusterClient)
	case "cce-rdma-plugin":
		return NewCCERDMAPlugin(client.userK8SClusterClient)
	case "cprom-agent":
		return NewCPromAgent(client.userK8SClusterClient)
	default:
		return nil, fmt.Errorf("unsupported plugin %s", pluginType)
	}
}
