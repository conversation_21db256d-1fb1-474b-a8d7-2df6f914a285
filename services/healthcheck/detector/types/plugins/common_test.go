package plugins

import (
	"context"
	"reflect"
	"testing"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	api "k8s.io/kubernetes/pkg/apis/core"
)

func Test_getImageIDFromPodTemplate(t *testing.T) {
	type args struct {
		ctx     context.Context
		podSpec api.PodTemplateSpec
	}
	tests := []struct {
		name    string
		args    args
		want    []string
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常情况",
			args: args{
				ctx: context.TODO(),
				podSpec: api.PodTemplateSpec{
					Spec: api.PodSpec{
						InitContainers: []api.Container{
							{
								Image: "chenhuan",
							},
						},
						Containers: []api.Container{
							{
								Image: "chen",
							},
							{
								Image: "huan",
							},
						},
					},
				},
			},
			want: []string{
				"chenhuan",
				"chen",
				"huan",
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getImageIDFromPodTemplate(tt.args.ctx, tt.args.podSpec)
			if (err != nil) != tt.wantErr {
				t.Errorf("getImageIDFromPodTemplate() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getImageIDFromPodTemplate() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getPluginImagesFromDeployment(t *testing.T) {
	type args struct {
		ctx       context.Context
		client    kubernetes.Interface
		namespace string
		name      string
	}

	var replicas int32 = 3

	tests := []struct {
		name    string
		args    args
		want    []ccev1.PluginImage
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常情况",
			args: func() args {
				ctx := context.TODO()
				clientset := fake.NewSimpleClientset()

				clientset.AppsV1().Deployments("kube-system").Create(ctx, &appsv1.Deployment{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "apps/v1",
						Kind:       "Deployment",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "kube-system",
						Name:      "cce-lb-controller",
					},
					Spec: appsv1.DeploymentSpec{
						Replicas: &replicas,
						Template: corev1.PodTemplateSpec{
							Spec: corev1.PodSpec{
								InitContainers: []corev1.Container{
									{
										Image: "chenhuan",
									},
								},
								Containers: []corev1.Container{
									{
										Image: "chen",
									},
									{
										Image: "huan",
									},
								},
							},
						},
					},
				}, metav1.CreateOptions{})

				return args{
					ctx:       ctx,
					client:    clientset,
					namespace: "kube-system",
					name:      "cce-lb-controller",
				}
			}(),
			want: []ccev1.PluginImage{
				{
					ImageID:  "chenhuan",
					Replicas: 3,
				},
				{
					ImageID:  "chen",
					Replicas: 3,
				},
				{
					ImageID:  "huan",
					Replicas: 3,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getPluginImagesFromDeployment(tt.args.ctx, tt.args.client, tt.args.namespace, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("getPluginImagesFromDeployment() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getPluginImagesFromDeployment() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_getPluginImagesFromDaemonSet(t *testing.T) {
	type args struct {
		ctx       context.Context
		client    kubernetes.Interface
		namespace string
		name      string
	}
	tests := []struct {
		name    string
		args    args
		want    []ccev1.PluginImage
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "正常情况",
			args: func() args {
				ctx := context.TODO()
				clientset := fake.NewSimpleClientset()

				clientset.AppsV1().DaemonSets("kube-system").Create(ctx, &appsv1.DaemonSet{
					TypeMeta: metav1.TypeMeta{
						APIVersion: "apps/v1",
						Kind:       "Deployment",
					},
					ObjectMeta: metav1.ObjectMeta{
						Namespace: "kube-system",
						Name:      "cce-lb-controller",
					},
					Spec: appsv1.DaemonSetSpec{
						Template: corev1.PodTemplateSpec{
							Spec: corev1.PodSpec{
								InitContainers: []corev1.Container{
									{
										Image: "chenhuan",
									},
								},
								Containers: []corev1.Container{
									{
										Image: "chen",
									},
									{
										Image: "huan",
									},
								},
							},
						},
					},
				}, metav1.CreateOptions{})

				return args{
					ctx:       ctx,
					client:    clientset,
					namespace: "kube-system",
					name:      "cce-lb-controller",
				}
			}(),
			want: []ccev1.PluginImage{
				{
					ImageID: "chenhuan",
				},
				{
					ImageID: "chen",
				},
				{
					ImageID: "huan",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := getPluginImagesFromDaemonSet(tt.args.ctx, tt.args.client, tt.args.namespace, tt.args.name)
			if (err != nil) != tt.wantErr {
				t.Errorf("getPluginImagesFromDaemonSet() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("getPluginImagesFromDaemonSet() = %v, want %v", got, tt.want)
			}
		})
	}
}
