package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

type CCERDMAPlugin struct {
	userK8SClusterClient kubernetes.Interface
}

func NewCCERDMAPlugin(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	return &CCERDMAPlugin{userK8SClusterClient: userK8SClusterClient}, nil
}

// SetPluginCondition -
func (c *CCERDMAPlugin) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCERDMAPlugin) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	// rdma-device-plugin
	fluidImages, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "rdma-device-plugin")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet rdma-device-plugin failed: %s", err)
	}
	if err == nil {
		result = append(result, fluidImages...)
	}

	return result, nil
}
