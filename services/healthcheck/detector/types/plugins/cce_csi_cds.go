// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/07 18:31:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCECSICDS 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// CCECSICDS -
type CCECSICDS struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCCECSICDS -
func NewCCECSICDS(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &CCECSICDS{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *CCECSICDS) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCECSICDS) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	// csi-cdsplugin-node-server-0
	images, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "csi-cdsplugin-node-server-0")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet csi-cdsplugin-node-server-0 failed: %s", err)
	}
	if err == nil {
		result = append(result, images...)
	}

	return result, nil
}
