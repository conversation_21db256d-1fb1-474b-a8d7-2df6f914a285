package plugins

import (
	"golang.org/x/net/context"
	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/fake"
	"testing"
)

func Test_CCEGPUManager(t *testing.T) {
	type fields struct {
		userK8SClusterClient kubernetes.Interface
	}
	type args struct {
		pluginClient     Interface
		err              error
		pluginConditions *ccev1.PluginCondition
	}

	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "gpu manager",
			args: func() args {
				client := fake.NewSimpleClientset()
				p, err := NewCCEGPUManager(client)
				return args{
					pluginClient:     p,
					pluginConditions: nil,
					err:              err,
				}
			}(),
			wantErr: false,
		},
		{
			name: "ai scheduler",
			args: func() args {
				client := fake.NewSimpleClientset()
				p, err := NewCCEAIScheduler(client)
				return args{
					pluginClient:     p,
					pluginConditions: nil,
					err:              err,
				}
			}(),
			wantErr: false,
		},
		{
			name: "fluid controller",
			args: func() args {
				client := fake.NewSimpleClientset()
				p, err := NewCCEFluidController(client)
				return args{
					pluginClient:     p,
					pluginConditions: nil,
					err:              err,
				}
			}(),
			wantErr: false,
		},
		{
			name: "rdma plugin",
			args: func() args {
				client := fake.NewSimpleClientset()
				p, err := NewCCERDMAPlugin(client)
				return args{
					pluginClient:     p,
					pluginConditions: nil,
					err:              err,
				}
			}(),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.args.err
			if err != nil && !tt.wantErr {
				t.Errorf("%s test new plugin failed with error: %s", tt.name, err.Error())
			}
			p := tt.args.pluginClient
			if _, err := p.GetPluginImages(context.TODO()); (err != nil) != tt.wantErr {
				t.Errorf("%s test get plugin image failed with error: %s", tt.name, err.Error())
			}
			if err := p.SetPluginCondition(context.TODO(), tt.args.pluginConditions); (err != nil) != tt.wantErr {
				t.Errorf("%s test get plugin condtions failed with error: %s", tt.name, err.Error())
			}
		})
	}
}
