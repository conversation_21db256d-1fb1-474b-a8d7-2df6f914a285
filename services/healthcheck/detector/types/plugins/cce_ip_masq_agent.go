// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/11 17:26:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCEIPMASQAgent 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// CCEIPMASQAgent -
type CCEIPMASQAgent struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCCEIPMASQAgent -
func NewCCEIPMASQAgent(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &CCEIPMASQAgent{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *CCEIPMASQAgent) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCEIPMASQAgent) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	ipMASQAgent, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-ip-masq-agent")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-ip-masq-agent failed: %s", err)
	}
	if err == nil {
		result = append(result, ipMASQAgent...)
	}

	return result, nil
}
