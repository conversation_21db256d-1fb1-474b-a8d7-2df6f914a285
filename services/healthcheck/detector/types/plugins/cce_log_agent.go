// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/07 18:31:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCELogAgent 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// CCELogAgent -
type CCELogAgent struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCCELogAgent -
func NewCCELogAgent(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &CCELogAgent{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *CCELogAgent) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCELogAgent) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	logAgent, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-log-agent")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-log-agent failed: %s", err)
	}
	if err == nil {
		result = append(result, logAgent...)
	}

	logAgentV2, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-log-agent-v2")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-log-agent-v2 failed: %s", err)
	}
	if err == nil {
		result = append(result, logAgentV2...)
	}

	logOperator, err := getPluginImagesFromDeployment(ctx, c.userK8SClusterClient, "kube-system", "cce-log-operator")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-vpc-route-cni failed: %s", err)
	}
	if err == nil {
		result = append(result, logOperator...)
	}

	return result, nil
}
