// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/07 15:31:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CoreDNS 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
)

const (
	cceCoreDNSDeploymentName = "coredns"
)

// CoreDNS -
type CoreDNS struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCoreDNS -
func NewCoreDNS(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &CoreDNS{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *CoreDNS) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CoreDNS) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	return getPluginImagesFromDeployment(ctx, c.userK8SClusterClient, "kube-system", cceCoreDNSDeploymentName)
}
