package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

type CCEFluidController struct {
	userK8SClusterClient kubernetes.Interface
}

func NewCCEFluidController(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	return &CCEFluidController{userK8SClusterClient: userK8SClusterClient}, nil
}

// SetPluginCondition -
func (c *CCEFluidController) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCEFluidController) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	// dataset-controller
	fluidImages, err := getPluginImagesFromDeployment(ctx, c.userK8SClusterClient, "fluid-system", "dataset-controller")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet dataset-controller failed: %s", err)
	}
	if err == nil {
		result = append(result, fluidImages...)
	}

	// rapidfsruntime-controller
	rapidfsImages, err := getPluginImagesFromDeployment(ctx, c.userK8SClusterClient, "fluid-system", "rapidfsruntime-controller")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDeployment rapidfsruntime-controller failed: %s", err)
	}
	if err == nil {
		result = append(result, rapidfsImages...)
	}

	return result, nil
}
