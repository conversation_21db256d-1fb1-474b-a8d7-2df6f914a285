package plugins

import (
	"context"
	"fmt"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
)

const (
	cceIngressControllerDpName = "cce-ingress-controller"
)

// CCEIngressController -
type CCEIngressController struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCCEIngressController -
func NewCCEIngressController(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &CCEIngressController{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *CCEIngressController) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	status := &ccev1.CCEIngressController{}

	// 统计WorkLoad数量和状态 - 副本数、状态、镜像地址
	if err := c.checkWorkLoad(ctx, status); err != nil {
		return err
	}

	// TODO 检查资源完整性

	pluginConditions.CCEIngressController = status
	return nil
}

// GetPluginImages -
func (c *CCEIngressController) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	return getPluginImagesFromDeployment(ctx, c.userK8SClusterClient, "kube-system", cceIngressControllerDpName)
}

func (c *CCEIngressController) checkWorkLoad(ctx context.Context, status *ccev1.CCEIngressController) error {
	// 工作负载是否存在
	deployment, err := getDeployment(ctx, c.userK8SClusterClient, "kube-system", cceIngressControllerDpName)
	if err != nil {
		if apierrors.IsNotFound(err) {
			status.WorkLoad = ccev1.WorkLoad{
				Reason:  ccev1.NotFoundWorkLoadReason,
				Message: fmt.Sprintf("Deployment not found %s", cceIngressControllerDpName),
			}
			return nil
		}
		return err
	}

	// 记录镜像ID
	imageID := ""
	if len(deployment.Spec.Template.Spec.Containers) == 1 {
		imageID = deployment.Spec.Template.Spec.Containers[0].Image
	} else {
		status.WorkLoad = ccev1.WorkLoad{
			Reason:  ccev1.InvalidWorkLoadReason,
			Message: fmt.Sprintf("Deployment content is not as expected"),
		}
		return nil
	}

	// 统计Pod状态
	status.WorkLoad = ccev1.WorkLoad{
		Image:       imageID,
		TargetCount: int(deployment.Spec.Replicas),
		ReadyCount:  int(deployment.Status.ReadyReplicas),
	}
	if status.WorkLoad.TargetCount != status.WorkLoad.ReadyCount {
		status.WorkLoad.Reason = ccev1.NotReadyWorkLoadReason
		status.WorkLoad.Message = fmt.Sprintf("Target replicas is %d, now ready %d", status.WorkLoad.TargetCount, status.WorkLoad.ReadyCount)
	} else {
		status.WorkLoad.Reason = ccev1.NormalWorkLoadReason
	}

	return nil
}
