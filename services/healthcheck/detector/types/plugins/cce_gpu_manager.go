package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

type CCEGPUManager struct {
	userK8SClusterClient kubernetes.Interface
}

func NewCCEGPUManager(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	return &CCEGPUManager{userK8SClusterClient: userK8SClusterClient}, nil
}

// SetPluginCondition -
func (c *CCEGPUManager) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCEGPUManager) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	// cce-cgpu-exclusive-device-plugin
	cgpuExclusiveImages, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-cgpu-exclusive-device-plugin")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-cgpu-exclusive-device-plugin failed: %s", err)
	}
	if err == nil {
		result = append(result, cgpuExclusiveImages...)
	}

	// cce-cgpu-share-device-plugin
	cgpuShareImages, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-cgpu-share-device-plugin")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-cgpu-share-device-plugin failed: %s", err)
	}
	if err == nil {
		result = append(result, cgpuShareImages...)
	}

	// cce-sgpu-device-plugin
	sgpuImages, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-sgpu-device-plugin")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-sgpu-device-plugin failed: %s", err)
	}
	if err == nil {
		result = append(result, sgpuImages...)
	}

	return result, nil
}
