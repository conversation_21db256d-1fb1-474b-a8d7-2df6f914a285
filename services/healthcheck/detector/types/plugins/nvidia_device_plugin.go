// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/11 17:26:00, by <EMAIL>, create
*/
/*
DESCRIPTION
NividiaDevicePlugin 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// NividiaDevicePlugin -
type NividiaDevicePlugin struct {
	userK8SClusterClient kubernetes.Interface
}

// NewNividiaDevicePlugin -
func NewNividiaDevicePlugin(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &NividiaDevicePlugin{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *NividiaDevicePlugin) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *NividiaDevicePlugin) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	images, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "nvidia-device-plugin-daemonset")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet nvidia-device-plugin-daemonset failed: %s", err)
	}
	if err == nil {
		result = append(result, images...)
	}

	return result, nil
}
