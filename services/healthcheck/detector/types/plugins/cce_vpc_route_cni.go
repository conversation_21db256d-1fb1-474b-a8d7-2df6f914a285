// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/07/07 18:31:00, by <EMAIL>, create
*/
/*
DESCRIPTION
CCEVPCRouteCNI 实现 plugins.Interface
*/

package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// CCEVPCRouteCNI -
type CCEVPCRouteCNI struct {
	userK8SClusterClient kubernetes.Interface
}

// NewCCEVPCRouteCNI -
func NewCCEVPCRouteCNI(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	c := &CCEVPCRouteCNI{
		userK8SClusterClient: userK8SClusterClient,
	}
	return c, nil
}

// SetPluginCondition -
func (c *CCEVPCRouteCNI) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCEVPCRouteCNI) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	// cce-cni-node-agent
	nodeAgentImages, err := getPluginImagesFromDaemonSet(ctx, c.userK8SClusterClient, "kube-system", "cce-vpc-route-cni")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDaemonSet cce-vpc-route-cni failed: %s", err)
		return nil, err
	}
	result = append(result, nodeAgentImages...)

	return result, nil
}
