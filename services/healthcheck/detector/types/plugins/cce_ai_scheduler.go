package plugins

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

type CCEAIScheduler struct {
	userK8SClusterClient kubernetes.Interface
}

func NewCCEAIScheduler(userK8SClusterClient kubernetes.Interface) (Interface, error) {
	if userK8SClusterClient == nil {
		return nil, fmt.Errorf("user k8s client is nil")
	}
	return &CCEAIScheduler{userK8SClusterClient: userK8SClusterClient}, nil
}

// SetPluginCondition -
func (c *CCEAIScheduler) SetPluginCondition(ctx context.Context, pluginConditions *ccev1.PluginCondition) error {
	return nil
}

// GetPluginImages -
func (c *CCEAIScheduler) GetPluginImages(ctx context.Context) ([]ccev1.PluginImage, error) {
	result := []ccev1.PluginImage{}

	// volcano-scheduler
	fluidImages, err := getPluginImagesFromDeployment(ctx, c.userK8SClusterClient, "volcano-system", "volcano-scheduler")
	if err != nil {
		logger.Errorf(ctx, "getPluginImagesFromDeployment volcano-scheduler failed: %s", err)
	}
	if err == nil {
		result = append(result, fluidImages...)
	}

	return result, nil
}
