package plugins

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/kubernetes/pkg/apis/apps"
	v1 "k8s.io/kubernetes/pkg/apis/apps/v1"
	api "k8s.io/kubernetes/pkg/apis/core"
	"k8s.io/kubernetes/pkg/apis/extensions/v1beta1"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

func getDeployment(ctx context.Context, k8sClient kubernetes.Interface, ns string, name string) (*apps.Deployment, error) {
	deployment := &apps.Deployment{}

	// 1. 先找 apps/v1 版本的 Deployment 并转换为内部版本返回
	deploymentAppsV1, err := k8sClient.AppsV1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, err
	}
	if err == nil {
		if err := v1.Convert_v1_Deployment_To_apps_Deployment(deploymentAppsV1, deployment, nil); err != nil {
			return nil, err
		}
		return deployment, nil
	}

	// 2. 再找 extensions/v1beta1 版本的 Deployment(早期的部署) 并转换为内部版本返回
	deploymentExtensionsV1Beta1, err := k8sClient.ExtensionsV1beta1().Deployments(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	if err := v1beta1.Convert_v1beta1_Deployment_To_apps_Deployment(deploymentExtensionsV1Beta1, deployment, nil); err != nil {
		return nil, err
	}
	return deployment, nil
}

// getPluginImagesFromDeployment - 从 Deployment 解析 PluginImage
func getPluginImagesFromDeployment(ctx context.Context, client kubernetes.Interface, namespace, name string) ([]ccev1.PluginImage, error) {
	deployment, err := getDeployment(ctx, client, namespace, name)
	if err != nil {
		logger.Errorf(ctx, "getDeployment %s/%s failed: %s", namespace, name, err)
		return nil, err
	}

	images, err := getImageIDFromPodTemplate(ctx, deployment.Spec.Template)
	if err != nil {
		logger.Errorf(ctx, "getImageIDFromPodTemplate failed: %s", err)
		return nil, err
	}

	result := []ccev1.PluginImage{}
	for _, image := range images {
		result = append(result, ccev1.PluginImage{
			ImageID:  image,
			Replicas: int(deployment.Spec.Replicas),
		})
	}

	return result, nil
}

func getDaemonSet(ctx context.Context, k8sClient kubernetes.Interface, ns string, name string) (*apps.DaemonSet, error) {
	daemonSet := &apps.DaemonSet{}

	// 1. 先找 apps/v1 版本的 DaemonSet 并转换为内部版本返回
	daemonSetAppsV1, err := k8sClient.AppsV1().DaemonSets(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil && !apierrors.IsNotFound(err) {
		return nil, err
	}
	if err == nil {
		if err := v1.Convert_v1_DaemonSet_To_apps_DaemonSet(daemonSetAppsV1, daemonSet, nil); err != nil {
			return nil, err
		}
		return daemonSet, nil
	}

	// 2. 再找 extensions/v1beta1 版本的 DaemonSet(早期的部署) 并转换为内部版本返回
	daemonSetExtensionsV1Beta1, err := k8sClient.ExtensionsV1beta1().DaemonSets(ns).Get(ctx, name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	if err := v1beta1.Convert_v1beta1_DaemonSet_To_apps_DaemonSet(daemonSetExtensionsV1Beta1, daemonSet, nil); err != nil {
		return nil, err
	}

	return daemonSet, nil
}

// getPluginImagesFromDaemonSet - 从 DaemonSet 解析 PluginImage
func getPluginImagesFromDaemonSet(ctx context.Context, client kubernetes.Interface, namespace, name string) ([]ccev1.PluginImage, error) {
	daemonSet, err := getDaemonSet(ctx, client, namespace, name)
	if err != nil {
		logger.Errorf(ctx, "getDaemonSet %s/%s failed: %s", namespace, name, err)
		return nil, err
	}

	images, err := getImageIDFromPodTemplate(ctx, daemonSet.Spec.Template)
	if err != nil {
		logger.Errorf(ctx, "getImageIDFromPodTemplate failed: %s", err)
		return nil, err
	}

	result := []ccev1.PluginImage{}
	for _, image := range images {
		result = append(result, ccev1.PluginImage{
			ImageID: image,
		})
	}

	return result, nil
}

func getImageIDFromPod(ctx context.Context, pod corev1.Pod) ([]string, error) {
	result := make([]string, 0)

	// InitContainers
	for _, container := range pod.Spec.InitContainers {
		image := container.Image
		if image != "" {
			result = append(result, container.Image)
		}
	}

	// Containers
	for _, container := range pod.Spec.Containers {
		image := container.Image
		if image != "" {
			result = append(result, container.Image)
		}
	}

	return result, nil
}

func getImageIDFromPodTemplate(ctx context.Context, podSpec api.PodTemplateSpec) ([]string, error) {
	result := []string{}

	// InitContainers
	for _, container := range podSpec.Spec.InitContainers {
		image := container.Image
		if image != "" {
			result = append(result, container.Image)
		}
	}

	// Containers
	for _, container := range podSpec.Spec.Containers {
		image := container.Image
		if image != "" {
			result = append(result, container.Image)
		}
	}

	return result, nil
}
