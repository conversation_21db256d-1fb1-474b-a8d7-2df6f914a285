// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/02/08 16:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Cluster 实现 types.Interface
*/

package types

import (
	"context"
	"testing"
	"time"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Test_firstErrorTime(t *testing.T) {
	now := metav1.NewTime(time.Now())

	type args struct {
		ctx       context.Context
		oldReady  corev1.ConditionStatus
		currReady corev1.ConditionStatus
		oldTime   *metav1.Time
	}
	tests := []struct {
		name    string
		args    args
		want    *metav1.Time
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name: "old=True, current=True",
			args: args{
				ctx:       context.TODO(),
				oldReady:  corev1.ConditionTrue,
				currReady: corev1.ConditionTrue,
				oldTime:   nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "old=True, current=false",
			args: args{
				ctx:       context.TODO(),
				oldReady:  corev1.ConditionTrue,
				currReady: corev1.ConditionFalse,
				oldTime:   nil,
			},
			want:    &now,
			wantErr: false,
		},
		{
			name: "old=false, current=true",
			args: args{
				ctx:       context.TODO(),
				oldReady:  corev1.ConditionFalse,
				currReady: corev1.ConditionTrue,
				oldTime:   nil,
			},
			want:    nil,
			wantErr: false,
		},
		{
			name: "old=false, current=false oldTime=nil",
			args: args{
				ctx:       context.TODO(),
				oldReady:  corev1.ConditionFalse,
				currReady: corev1.ConditionFalse,
				oldTime:   nil,
			},
			want:    &now,
			wantErr: false,
		},
		{
			name: "old=false, current=false oldTime!=nil",
			args: args{
				ctx:       context.TODO(),
				oldReady:  corev1.ConditionFalse,
				currReady: corev1.ConditionFalse,
				oldTime:   &now,
			},
			want:    &now,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := firstErrorTime(tt.args.ctx, tt.args.oldReady, tt.args.currReady, tt.args.oldTime)
			if (err != nil) != tt.wantErr {
				t.Errorf("firstErrorTime() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if tt.want == nil && got != nil {
				t.Errorf("firstErrorTime() = %v, want %v", got, tt.want)
			}

			if tt.want != nil && got == nil {
				t.Errorf("firstErrorTime() = %v, want %v", got, tt.want)
			}

			if tt.name == "old=false, current=false oldTime!=nil" {
				if tt.want != got {
					t.Errorf("firstErrorTime() = %v, want %v", got, tt.want)
				}
			}
		})
	}
}

func Test_cluster_clusterHealthCheckSetLabels(t *testing.T) {
	type fields struct {
		model      models.Interface
		metaClient meta.Interface
	}
	type args struct {
		ctx context.Context
		hc  *ccev1.ClusterHealthCheck
	}

	enableExternalAuditor := true
	disableExternalAuditor := false

	tests := []struct {
		name       string
		fields     fields
		args       args
		wantLabels map[string]string
		wantErr    bool
	}{
		// TODO: Add test cases.
		{
			name:   "开启审计推送, 设置 Labels",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				hc: &ccev1.ClusterHealthCheck{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{},
					},
					Spec: ccev1.ClusterHealthCheckSpec{},
					Status: ccev1.ClusterHealthCheckStatus{
						PluginCondition: ccev1.PluginCondition{
							APIServerExternalAuditor: &ccev1.APIServerExternalAuditor{
								EnableExternalAuditor: &enableExternalAuditor,
							},
						},
					},
				},
			},
			wantLabels: map[string]string{
				clusterLabelEnableAPIServerExternalAuditor: "true",
			},
			wantErr: false,
		},
		{
			name:   "未开启审计推送",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				hc: &ccev1.ClusterHealthCheck{
					ObjectMeta: metav1.ObjectMeta{
						Labels: map[string]string{},
					},
					Spec: ccev1.ClusterHealthCheckSpec{},
					Status: ccev1.ClusterHealthCheckStatus{
						PluginCondition: ccev1.PluginCondition{
							APIServerExternalAuditor: &ccev1.APIServerExternalAuditor{
								EnableExternalAuditor: &disableExternalAuditor,
							},
						},
					},
				},
			},
			wantLabels: map[string]string{},
			wantErr:    false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &cluster{
				model:      tt.fields.model,
				metaClient: tt.fields.metaClient,
			}
			if err := c.clusterHealthCheckSetLabels(tt.args.ctx, tt.args.hc); (err != nil) != tt.wantErr {
				t.Errorf("cluster.clusterHealthCheckSetLabels() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
