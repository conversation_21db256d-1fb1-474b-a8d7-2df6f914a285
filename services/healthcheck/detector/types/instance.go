// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/03/02 10:54:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Instance 实现 types.Interface
*/

package types

import (
	"context"
	"fmt"
	"os"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	stackutils "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/consts"
)

const (
	instanceCRDNamespace         = "default"
	instanceHealthCheckNamespace = "health-check"
)

// instance - CCE Instance 对象的 types.Interface 实现
type instance struct {
	model      models.Interface
	metaClient meta.Interface
}

func newInstance(ctx context.Context, model models.Interface, metaClient meta.Interface) (Interface, error) {
	if model == nil {
		return nil, fmt.Errorf("model is nil")
	}

	if metaClient == nil {
		return nil, fmt.Errorf("metaClient is nil")
	}

	return &instance{
		model:      model,
		metaClient: metaClient,
	}, nil
}

func (c *instance) GetClusterID(ctx context.Context, crd interface{}) (string, error) {
	if crd == nil {
		return "", nil
	}

	instance, ok := crd.(*ccev1.Instance)
	if !ok {
		logger.Errorf(ctx, "crd type not *ccev1.Instance")
		return "", fmt.Errorf("crd type not *ccev1.Instance")
	}

	return instance.Spec.ClusterID, nil
}

func (c *instance) GetCRDSetFromMetaCluster(ctx context.Context) (map[string]interface{}, error) {
	InstanceList, err := c.metaClient.ListInstances(ctx, instanceCRDNamespace, &metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "ListInstances failed: %s", err)
		return nil, err
	}

	result := map[string]interface{}{}
	for _, c := range InstanceList.Items {
		instance := c

		// 过滤掉 Master
		if instance.Spec.ClusterRole == ccetypes.ClusterRoleMaster {
			logger.Infof(ctx, "Instance %s clusterRole = master, skip reconcile HealthCheck", c.GetName())
			continue
		}

		// 过滤掉非 Running
		if instance.Status.InstancePhase != ccetypes.InstancePhaseRunning {
			logger.Infof(ctx, "Instance %s phase != %s, skip reconcile HealthCheck", c.GetName(),
				ccetypes.InstancePhaseRunning)
			continue
		}

		result[c.GetName()] = &instance
	}

	return result, nil
}

func (c *instance) GetHeatlCheckSetFromMetaCluster(ctx context.Context) (map[string]interface{}, error) {
	InstanceList, err := c.metaClient.ListInstanceHealthCheck(ctx, instanceHealthCheckNamespace, &metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "ListInstances failed: %s", err)
		return nil, err
	}

	result := map[string]interface{}{}
	for _, c := range InstanceList.Items {
		result[c.GetName()] = nil
	}

	return result, nil
}

func (c *instance) GetHealthCheck(ctx context.Context, namespace, name string) (interface{}, error) {
	hc, err := c.metaClient.GetInstanceHealthCheck(ctx, namespace, name, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetInstanceHealthCheck failed: %s", err)
		return nil, err
	}

	return hc, nil
}

func (c *instance) CreateHealthCheck(ctx context.Context, namespace, name string, crd interface{}) error {
	if crd == nil {
		return fmt.Errorf("crd is nil")
	}

	// 获取 Instance CRD 对象
	instance, ok := crd.(*ccev1.Instance)
	if !ok {
		logger.Errorf(ctx, "crd type not *cce1.Instance")
		return fmt.Errorf("crd type not *cce1.Instance")
	}

	// 新建 InstanceHealthCheck
	if _, err := c.metaClient.CreateInstanceHealthCheck(ctx, namespace, &ccev1.InstanceHealthCheck{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: consts.DefaultNamespace,
			Annotations: map[string]string{
				consts.Region:      os.Getenv("REGION"),
				consts.ClusterName: instance.ClusterName,
			},
			Labels: map[string]string{
				consts.ClusterID: instance.Spec.ClusterID,
			},
		},
		Spec: ccev1.InstanceHealthCheckSpec{
			ClusterID:     instance.Spec.ClusterID,
			CCEInstanceID: name,
			Handler:       "",
		},
	}); err != nil {
		logger.Errorf(ctx, "CreateInstanceHealthCheck failed: %s", err)
		return err
	}

	return nil
}

func (c *instance) DeleteHealthCheck(ctx context.Context, namespace, name string) error {
	if err := c.metaClient.DeleteInstanceHealthCheck(ctx, namespace, name, &metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "DeleteInstanceHealthCheck failed: %s", err)
		return err
	}

	return nil
}

func (c *instance) GetHealthCheckStatus(ctx context.Context, client kubernetes.Interface, namespace, name string, crd interface{}) (interface{}, error) {
	// 获取 Instance CRD 对象
	instance, ok := crd.(*ccev1.Instance)
	if !ok {
		logger.Errorf(ctx, "crd type not *cce1.Instance")
		return nil, fmt.Errorf("crd type not *cce1.Instance")
	}

	// 获取 Cluster CRD 对象
	cluster, err := c.metaClient.GetCluster(ctx, instanceCRDNamespace, instance.Spec.ClusterID, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetCluster %s failed: %s", instance.Spec.ClusterID, err)
		return nil, err
	}

	// 获取 K8S NodeName
	nodeName := stackutils.GetNodeName(ctx, instance.Status.Machine.VPCIP,
		instance.Status.Machine.Hostname,
		instance.Spec.InstanceName,
		cluster.Spec.K8SCustomConfig.EnableHostname,
	)
	if nodeName == "" {
		logger.Infof(ctx, "instance.Status.Machine.VPCIP not exists, means kubectl get nodes may not found, skip ")
		return nil, fmt.Errorf("%s K8S NodeName not exist", name)
	}

	// 获取用户集群 NODE
	node, err := client.CoreV1().Nodes().Get(ctx, nodeName, metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "Get %s K8S Node failed: %s", name, err)

		// 直接返回异常 ClusterHealthCheckStatus
		return &ccev1.InstanceHealthCheckStatus{
			Ready: corev1.ConditionFalse,
			Conditions: []corev1.NodeCondition{
				{
					Type:               "kubectl get node",
					Status:             corev1.ConditionFalse,
					Reason:             err.Error(),
					Message:            err.Error(),
					LastHeartbeatTime:  metav1.NewTime(time.Now()),
					LastTransitionTime: metav1.NewTime(time.Now()),
				},
			},
		}, nil
	}

	nodeConditions := filterNodeConditions(node.Status.Conditions, func(c *corev1.NodeCondition) bool {
		// 过滤 时间戳为 空 的conditions
		if c.LastHeartbeatTime.IsZero() || c.LastHeartbeatTime.IsZero() {
			return false
		}
		return true
	})
	// 构建 InstanceHealthCheck.Status
	status := &ccev1.InstanceHealthCheckStatus{
		Conditions: nodeConditions,
	}

	// 设置 status.Ready
	networkUnavailable := true
	networkUnavailableExists := false
	for _, con := range node.Status.Conditions {
		if con.Type == corev1.NodeReady {
			status.Ready = con.Status
		}

		if con.Type == corev1.NodeNetworkUnavailable {
			networkUnavailableExists = true
		}

		if con.Type == corev1.NodeNetworkUnavailable && con.Status == corev1.ConditionFalse {
			networkUnavailable = false
		}
	}

	// NodeNetworkUnavailable 不存在或为 True 状态统一为 ConditionFalse
	if networkUnavailable {
		status.Ready = corev1.ConditionFalse

		if !networkUnavailableExists {
			status.Conditions = append(status.Conditions, corev1.NodeCondition{
				Type:               corev1.NodeNetworkUnavailable,
				Status:             corev1.ConditionTrue,
				Reason:             "kubelet network not ready",
				Message:            "kubelet network not ready",
				LastHeartbeatTime:  metav1.NewTime(time.Now()),
				LastTransitionTime: metav1.NewTime(time.Now()),
			})
		}
	}

	// ConditionUnknown 状态统一为 ConditionFalse
	if status.Ready == corev1.ConditionUnknown {
		status.Ready = corev1.ConditionFalse
	}

	return status, nil
}

func (c *instance) UpdateHealthCheckStatus(ctx context.Context, namespace, name string, status interface{}) error {
	if name == "" {
		return fmt.Errorf("name is empty")
	}

	if status == nil {
		return fmt.Errorf("status is nil")
	}

	// 获取 InstanceHealthCheck
	hc, err := c.metaClient.GetInstanceHealthCheck(ctx, namespace, name, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetInstanceHealthCheck %s failed: %s", name, err)
		return err
	}

	// 更新 Status
	hcStatus, ok := status.(*ccev1.InstanceHealthCheckStatus)
	if !ok {
		return fmt.Errorf("Instance %s status not *ccev1.InstanceHealthCheckStatus", name)
	}

	hc.Status = *hcStatus

	// 更新 InstanceHealthCheck
	if _, err := c.metaClient.UpdateInstanceHealthCheck(ctx, namespace, name, hc); err != nil {
		logger.Errorf(ctx, "UpdateInstanceHealthCheck %s failed: %s", name, err)
		return err
	}

	return nil
}

func filterNodeConditions(conditions []corev1.NodeCondition, f func(c *corev1.NodeCondition) bool) []corev1.NodeCondition {
	var filtered []corev1.NodeCondition
	for i := range conditions {
		if !f(&conditions[i]) {
			continue
		}
		filtered = append(filtered, conditions[i])
	}
	return filtered
}
