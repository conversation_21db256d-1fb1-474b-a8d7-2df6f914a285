// Code generated by MockGen. DO NOT EDIT.
// Source: icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/types (interfaces: Interface)

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	gomock "github.com/golang/mock/gomock"
	kubernetes "k8s.io/client-go/kubernetes"
	reflect "reflect"
)

// MockInterface is a mock of Interface interface
type MockInterface struct {
	ctrl     *gomock.Controller
	recorder *MockInterfaceMockRecorder
}

// MockInterfaceMockRecorder is the mock recorder for MockInterface
type MockInterfaceMockRecorder struct {
	mock *MockInterface
}

// NewMockInterface creates a new mock instance
func NewMockInterface(ctrl *gomock.Controller) *MockInterface {
	mock := &MockInterface{ctrl: ctrl}
	mock.recorder = &MockInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use
func (m *MockInterface) EXPECT() *MockInterfaceMockRecorder {
	return m.recorder
}

// CreateHealthCheck mocks base method
func (m *MockInterface) CreateHealthCheck(arg0 context.Context, arg1, arg2 string, arg3 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateHealthCheck", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateHealthCheck indicates an expected call of CreateHealthCheck
func (mr *MockInterfaceMockRecorder) CreateHealthCheck(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateHealthCheck", reflect.TypeOf((*MockInterface)(nil).CreateHealthCheck), arg0, arg1, arg2, arg3)
}

// DeleteHealthCheck mocks base method
func (m *MockInterface) DeleteHealthCheck(arg0 context.Context, arg1, arg2 string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteHealthCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteHealthCheck indicates an expected call of DeleteHealthCheck
func (mr *MockInterfaceMockRecorder) DeleteHealthCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteHealthCheck", reflect.TypeOf((*MockInterface)(nil).DeleteHealthCheck), arg0, arg1, arg2)
}

// GetCRDSetFromMetaCluster mocks base method
func (m *MockInterface) GetCRDSetFromMetaCluster(arg0 context.Context) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCRDSetFromMetaCluster", arg0)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCRDSetFromMetaCluster indicates an expected call of GetCRDSetFromMetaCluster
func (mr *MockInterfaceMockRecorder) GetCRDSetFromMetaCluster(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCRDSetFromMetaCluster", reflect.TypeOf((*MockInterface)(nil).GetCRDSetFromMetaCluster), arg0)
}

// GetClusterID mocks base method
func (m *MockInterface) GetClusterID(arg0 context.Context, arg1 interface{}) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterID", arg0, arg1)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterID indicates an expected call of GetClusterID
func (mr *MockInterfaceMockRecorder) GetClusterID(arg0, arg1 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterID", reflect.TypeOf((*MockInterface)(nil).GetClusterID), arg0, arg1)
}

// GetHealthCheck mocks base method
func (m *MockInterface) GetHealthCheck(arg0 context.Context, arg1, arg2 string) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthCheck", arg0, arg1, arg2)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHealthCheck indicates an expected call of GetHealthCheck
func (mr *MockInterfaceMockRecorder) GetHealthCheck(arg0, arg1, arg2 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthCheck", reflect.TypeOf((*MockInterface)(nil).GetHealthCheck), arg0, arg1, arg2)
}

// GetHealthCheckStatus mocks base method
func (m *MockInterface) GetHealthCheckStatus(arg0 context.Context, arg1 kubernetes.Interface, arg2, arg3 string, arg4 interface{}) (interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHealthCheckStatus", arg0, arg1, arg2, arg3, arg4)
	ret0, _ := ret[0].(interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHealthCheckStatus indicates an expected call of GetHealthCheckStatus
func (mr *MockInterfaceMockRecorder) GetHealthCheckStatus(arg0, arg1, arg2, arg3, arg4 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHealthCheckStatus", reflect.TypeOf((*MockInterface)(nil).GetHealthCheckStatus), arg0, arg1, arg2, arg3, arg4)
}

// GetHeatlCheckSetFromMetaCluster mocks base method
func (m *MockInterface) GetHeatlCheckSetFromMetaCluster(arg0 context.Context) (map[string]interface{}, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHeatlCheckSetFromMetaCluster", arg0)
	ret0, _ := ret[0].(map[string]interface{})
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHeatlCheckSetFromMetaCluster indicates an expected call of GetHeatlCheckSetFromMetaCluster
func (mr *MockInterfaceMockRecorder) GetHeatlCheckSetFromMetaCluster(arg0 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHeatlCheckSetFromMetaCluster", reflect.TypeOf((*MockInterface)(nil).GetHeatlCheckSetFromMetaCluster), arg0)
}

// UpdateHealthCheckStatus mocks base method
func (m *MockInterface) UpdateHealthCheckStatus(arg0 context.Context, arg1, arg2 string, arg3 interface{}) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateHealthCheckStatus", arg0, arg1, arg2, arg3)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateHealthCheckStatus indicates an expected call of UpdateHealthCheckStatus
func (mr *MockInterfaceMockRecorder) UpdateHealthCheckStatus(arg0, arg1, arg2, arg3 interface{}) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateHealthCheckStatus", reflect.TypeOf((*MockInterface)(nil).UpdateHealthCheckStatus), arg0, arg1, arg2, arg3)
}
