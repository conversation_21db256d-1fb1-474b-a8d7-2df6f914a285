// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/02/08 16:59:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Cluster 实现 types.Interface
*/

package types

import (
	"context"
	"fmt"
	"os"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	ccev1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/crd/api/v1"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/devops/cce-devops-backend/models/customer"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/consts"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/types/plugins"
)

const (
	clusterCRDNamespace         = "default"
	clusterHealthCheckNamespace = "health-check"

	// clusterHealthCheck 上设置 Label, 表明开启 APIServerExternalAuditor, 方便查询
	clusterLabelEnableAPIServerExternalAuditor = "kubernetes.io/cce.cluster.enable-apiserver-external-auditor"
)

// cluster - CCE Cluster 对象的 types.Interface 实现
type cluster struct {
	model      models.Interface
	metaClient meta.Interface
}

func newCluster(ctx context.Context, model models.Interface, metaClient meta.Interface) (Interface, error) {
	if model == nil {
		return nil, fmt.Errorf("model is nil")
	}

	if metaClient == nil {
		return nil, fmt.Errorf("metaClient is nil")
	}

	return &cluster{
		model:      model,
		metaClient: metaClient,
	}, nil
}

func (c *cluster) GetClusterID(ctx context.Context, crd interface{}) (string, error) {
	if crd == nil {
		return "", nil
	}

	cluster, ok := crd.(*ccev1.Cluster)
	if !ok {
		logger.Errorf(ctx, "crd type not *ccev1.Cluster")
		return "", fmt.Errorf("crd type not *ccev1.Cluster")
	}

	return cluster.GetName(), nil
}

func (c *cluster) GetCRDSetFromMetaCluster(ctx context.Context) (map[string]interface{}, error) {
	clusterList, err := c.metaClient.ListClusters(ctx, clusterCRDNamespace, &metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "ListClusters failed: %s", err)
		return nil, err
	}

	result := map[string]interface{}{}
	for _, c := range clusterList.Items {
		cluster := c

		// 过滤掉非 Running 集群
		if cluster.Status.ClusterPhase != ccetypes.ClusterPhaseRunning {
			logger.Infof(ctx, "Cluster %s phase != %v, skip reconcile HealthCheck", c.GetName(), ccetypes.ClusterPhaseRunning)
			continue
		}

		result[c.GetName()] = &cluster
	}

	return result, nil
}

func (c *cluster) GetHeatlCheckSetFromMetaCluster(ctx context.Context) (map[string]interface{}, error) {
	clusterList, err := c.metaClient.ListClusterHealthCheck(ctx, clusterHealthCheckNamespace, &metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "ListClusters failed: %s", err)
		return nil, err
	}

	result := map[string]interface{}{}
	for _, c := range clusterList.Items {
		result[c.GetName()] = nil
	}

	return result, nil
}

func (c *cluster) GetHealthCheck(ctx context.Context, namespace, name string) (interface{}, error) {
	hc, err := c.metaClient.GetClusterHealthCheck(ctx, namespace, name, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetClusterHealthCheck failed: %s", err)
		return nil, err
	}

	return hc, nil
}

func (c *cluster) CreateHealthCheck(ctx context.Context, namespace, name string, crd interface{}) error {
	cluster, ok := crd.(*ccev1.Cluster)
	if !ok {
		return fmt.Errorf("crd is not *ccev1.Cluster in CreateHealthCheck")
	}

	// 获取 User 详情
	var accountID string
	var userName string
	var company string

	accountID = cluster.Spec.AccountID
	if accountID != "" {
		info, err := customer.CRMCustomerInfoByAccountID(ctx, accountID)
		if err != nil {
			logger.Errorf(ctx, "CRMCustomerInfoByAccountID faild, skip set userInfo: %s", err)
		}

		if err == nil {
			userName = info.UserName
			company = info.Profile.Company
		}
	}

	if _, err := c.metaClient.CreateClusterHealthCheck(ctx, namespace, &ccev1.ClusterHealthCheck{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: namespace,
			Annotations: map[string]string{
				consts.Region:      os.Getenv("REGION"),
				consts.ClusterName: name,
			},
		},
		Spec: ccev1.ClusterHealthCheckSpec{
			Handler:   "",
			ClusterID: name,
			AccountID: accountID,
			UserName:  userName,
			Company:   company,
		},
	}); err != nil {
		logger.Errorf(ctx, "CreateClusterHealthCheck failed: %s", err)
		return err
	}

	return nil
}

func (c *cluster) DeleteHealthCheck(ctx context.Context, namespace, name string) error {
	if err := c.metaClient.DeleteClusterHealthCheck(ctx, namespace, name, &metav1.DeleteOptions{}); err != nil {
		logger.Errorf(ctx, "DeleteClusterHealthCheck failed: %s", err)
		return err
	}

	return nil
}

func (c *cluster) GetHealthCheckStatus(ctx context.Context, client kubernetes.Interface, namespace, name string, crd interface{}) (interface{}, error) {
	var err error
	var status *ccev1.ClusterHealthCheckStatus

	// TODO: 循环等待时间过长, 导致整体运行时间增加, 暂时去掉
	// for i := 0; i < 3; i++ {
	// 	status, err = c.generateHealthCheckStatus(ctx, client, namespace, name, crd)
	// 	if err != nil {
	// 		logger.Errorf(ctx, "generateHealthCheckStatus failed: %s", err)
	// 		return nil, err
	// 	}

	// 	if status.Ready == corev1.ConditionTrue {
	// 		return status, nil
	// 	}

	// 	logger.Warnf(ctx, "Cluster %s Ready=False, wait 10 seconds and retry", name)
	// 	time.Sleep(10 * time.Second)
	// }

	status, err = c.generateHealthCheckStatus(ctx, client, namespace, name, crd)
	if err != nil {
		logger.Errorf(ctx, "generateHealthCheckStatus failed: %s", err)
		return nil, err
	}

	if status.Ready == corev1.ConditionTrue {
		return status, nil
	}

	logger.Warnf(ctx, "Cluster %s Ready=False, wait 10 seconds and retry", name)

	return status, err
}

func (c *cluster) UpdateHealthCheckStatus(ctx context.Context, namespace, name string, status interface{}) error {
	if name == "" {
		return fmt.Errorf("name is empty")
	}

	if status == nil {
		return fmt.Errorf("status is nil")
	}

	// MetaCluster ClusterHealthCheck
	hc, err := c.metaClient.GetClusterHealthCheck(ctx, namespace, name, &metav1.GetOptions{})
	if err != nil {
		logger.Errorf(ctx, "GetClusterHealthCheck %s failed: %s", name, err)
		return err
	}

	logger.Infof(ctx, "GetClusterHealthCheck name=%s success: %s", name, utils.ToJSON(hc))

	// Target ClusterHealthCheck.Status
	hcStatus, ok := status.(*ccev1.ClusterHealthCheckStatus)
	if !ok {
		return fmt.Errorf("cluster %s status not *ccev1.ClusterHealthCheckStatus", name)
	}

	// 更新第一次出现错误时间 Status.FirstErrorTime
	firstTime, err := firstErrorTime(ctx, hc.Status.Ready, hcStatus.Ready, hc.Status.FirstErrorTime)
	if err != nil {
		logger.Errorf(ctx, "firstErrorTime %s failed: %s", name, err)
		return err
	}

	hc.Status = *hcStatus
	hc.Status.FirstErrorTime = firstTime

	// 设置必要的 Labels, 访问运维查询操作
	if err := c.clusterHealthCheckSetLabels(ctx, hc); err != nil {
		logger.Errorf(ctx, "clusterHealthCheckSetLabels failed: %s", err)
		return err
	}

	// 更新 ClusterHealthCheck
	if _, err := c.metaClient.UpdateClusterHealthCheck(ctx, namespace, name, hc); err != nil {
		logger.Errorf(ctx, "UpdateClusterHealthCheck %s failed: %s", name, err)
		return err
	}

	return nil
}

func firstErrorTime(ctx context.Context, oldReady, currReady corev1.ConditionStatus, oldTime *metav1.Time) (*metav1.Time, error) {
	now := metav1.NewTime(time.Now())

	if oldReady == corev1.ConditionTrue && currReady == corev1.ConditionTrue {
		logger.Infof(ctx, "oldReady=%v currReady=%v", corev1.ConditionTrue, corev1.ConditionTrue)
		return nil, nil
	} else if oldReady == corev1.ConditionTrue && currReady == corev1.ConditionFalse {
		logger.Infof(ctx, "oldReady=%v currReady=%v", corev1.ConditionTrue, corev1.ConditionFalse)
		return &now, nil
	} else if oldReady == corev1.ConditionFalse && currReady == corev1.ConditionTrue {
		logger.Infof(ctx, "oldReady=%v currReady=%v", corev1.ConditionFalse, corev1.ConditionTrue)
		return nil, nil
	} else if oldReady == corev1.ConditionFalse && currReady == corev1.ConditionFalse {
		if oldTime == nil {
			logger.Infof(ctx, "oldReady=%v currReady=%v oldTime=nil", corev1.ConditionFalse, corev1.ConditionFalse)
			return &now, nil
		} else {
			logger.Infof(ctx, "oldReady=%v currReady=%v oldTime!=nil", corev1.ConditionFalse, corev1.ConditionFalse)
			return oldTime, nil
		}
	}

	return nil, nil
}

// generateHealthCheckStatus - 统计 Cluster 当前健康状态
func (c *cluster) generateHealthCheckStatus(ctx context.Context, client kubernetes.Interface,
	namespace, name string, crd interface{}) (*ccev1.ClusterHealthCheckStatus, error) {
	// 获取 ConditionStatus, []ccev1.ClusterCondition
	ready, conditionList, err := c.generateClusterCondition(ctx, client, namespace, name, crd)
	if err != nil {
		logger.Errorf(ctx, "generateClusterCondition failed: %s", err)

		// 直接返回异常 ClusterHealthCheckStatus
		return &ccev1.ClusterHealthCheckStatus{
			Ready: corev1.ConditionFalse,
			Conditions: []ccev1.ClusterCondition{
				{
					Type:               "kubectl get cs",
					Status:             corev1.ConditionFalse,
					Reason:             err.Error(),
					Message:            err.Error(),
					LastHeartbeatTime:  metav1.NewTime(time.Now()),
					LastTransitionTime: metav1.NewTime(time.Now()),
				},
			},
		}, nil
	}

	// 获取 Cluster Plugins 相关信息
	pluginConditon, err := c.generatePluginCondition(ctx, client)
	if err != nil {
		logger.Errorf(ctx, "generatePluginCondition failed: %s", err)

		return &ccev1.ClusterHealthCheckStatus{
			Ready:      ready,
			Conditions: conditionList,
		}, nil
	}

	return &ccev1.ClusterHealthCheckStatus{
		Ready:           ready,
		Conditions:      conditionList,
		PluginCondition: pluginConditon,
	}, nil
}

// generateClusterCondition - 类似 kubect get cs
func (c *cluster) generateClusterCondition(ctx context.Context, client kubernetes.Interface,
	namespace, name string, crd interface{}) (corev1.ConditionStatus, []ccev1.ClusterCondition, error) {
	var ready corev1.ConditionStatus
	var conditionList []ccev1.ClusterCondition

	// kubectl get cs
	componentList, err := client.CoreV1().ComponentStatuses().List(ctx, metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, fmt.Sprintf("Cluster %s get ComponentStatuses failed: %s", name, err))
		return ready, conditionList, err
	}

	// K8S Component Status -> ClusterHealthCheck.Status
	healthCount := 0
	items := componentList.Items

	for i := 0; i < len(items); i++ {
		conditionStatus := corev1.ConditionUnknown
		message := ""
		errorMSG := ""

		for j := 0; j < len(items[i].Conditions); j++ {
			if items[i].Conditions[j].Type == "Healthy" && items[i].Conditions[j].Status == "True" {
				conditionStatus = corev1.ConditionTrue
				healthCount = healthCount + 1
			} else {
				message = items[i].Conditions[j].Message
				errorMSG = items[i].Conditions[j].Error
			}
		}

		condition := ccev1.ClusterCondition{
			Type:               items[i].ObjectMeta.Name,
			Status:             conditionStatus,
			Reason:             message,
			Message:            errorMSG,
			LastHeartbeatTime:  metav1.NewTime(time.Now()),
			LastTransitionTime: metav1.NewTime(time.Now()),
		}

		conditionList = append(conditionList, condition)
	}

	// 增加 APIServer
	conditionList = append(conditionList, ccev1.ClusterCondition{
		Type:               ccev1.Apiserver,
		Status:             corev1.ConditionTrue,
		Reason:             "",
		Message:            "",
		LastHeartbeatTime:  metav1.NewTime(time.Now()),
		LastTransitionTime: metav1.NewTime(time.Now()),
	})

	// Cluster 总状态
	ready = corev1.ConditionFalse
	if healthCount == len(items) {
		ready = corev1.ConditionTrue
	}

	return ready, conditionList, nil
}

// generatePluginCondition - 生成 PluginCondition
func (c *cluster) generatePluginCondition(ctx context.Context, client kubernetes.Interface) (ccev1.PluginCondition, error) {
	result := ccev1.PluginCondition{}

	pluginClient, err := plugins.NewPluginClient(client)
	if err != nil {
		return result, err
	}

	// 获取所有 Plugin Image
	pluginImages := []ccev1.PluginImage{}
	for _, pluginType := range plugins.DefaultPluginsToCheck() {
		logger.Infof(ctx, "Check plugin %s begin", pluginType)

		plugin, err := pluginClient.GetPluginClient(pluginType)
		if err != nil {
			return result, err
		}

		// 设置 Plugin 内容
		if err := plugin.SetPluginCondition(ctx, &result); err != nil {
			logger.Errorf(ctx, "SetPluginCondition %s failed: %s", pluginType, err)
			continue
		}

		// 设置 PluginImages
		images, err := plugin.GetPluginImages(ctx)
		if err != nil {
			logger.Errorf(ctx, "GetPluginImages %s failed: %s", pluginType, err)
			continue
		}

		if images != nil && len(images) != 0 {
			pluginImages = append(pluginImages, images...)
		}
	}

	// 获取全量 PluinImages
	result.PluginImages = pluginImages

	return result, nil
}

// clusterHealthCheckSetLabels - 为 ClusterHealthCheck 设置 Labels 方便运维操作
func (c *cluster) clusterHealthCheckSetLabels(ctx context.Context, hc *ccev1.ClusterHealthCheck) error {
	// 设置是否开启审计: kubernetes.io/cce.cluster.enable-apiserver-external-auditor
	pluginConditon := hc.Status.PluginCondition
	if pluginConditon.APIServerExternalAuditor != nil && pluginConditon.APIServerExternalAuditor.EnableExternalAuditor != nil &&
		*pluginConditon.APIServerExternalAuditor.EnableExternalAuditor == true {

		if hc.Labels == nil {
			hc.Labels = map[string]string{}
		}

		hc.Labels[clusterLabelEnableAPIServerExternalAuditor] = "true"
	}

	return nil
}
