// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/02/08 15:43:00, by <EMAIL>, create
*/
/*
DESCRIPTION
定义 CCE HealthCheck 扫描的对象支持的方法: Cluster, Instance
*/

package types

import (
	"context"
	"fmt"

	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
)

//go:generate mockgen -destination=./mock/mock_client.go -package=mock icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/types Interface

// Interface - 定义 CCE 支持 HealthCheck 对象方法
type Interface interface {
	GetClusterID(ctx context.Context, crd interface{}) (string, error)

	GetCRDSetFromMetaCluster(ctx context.Context) (map[string]interface{}, error)
	GetHeatlCheckSetFromMetaCluster(ctx context.Context) (map[string]interface{}, error)

	GetHealthCheck(ctx context.Context, namespace, name string) (interface{}, error)
	CreateHealthCheck(ctx context.Context, namespace, name string, crd interface{}) error
	DeleteHealthCheck(ctx context.Context, namespace, name string) error

	GetHealthCheckStatus(ctx context.Context, client kubernetes.Interface,
		namespace, name string, crd interface{}) (interface{}, error)
	UpdateHealthCheckStatus(ctx context.Context, namespace, name string, status interface{}) error
}

// NewClient - 初始化 Interface
func NewClient(ctx context.Context, collectType string,
	model models.Interface, metaClient meta.Interface) (Interface, error) {

	switch collectType {
	case "cluster":
		return newCluster(ctx, model, metaClient)
	case "instance":
		return newInstance(ctx, model, metaClient)
	default:
		return nil, fmt.Errorf("unsupport collectType %s", collectType)
	}
}
