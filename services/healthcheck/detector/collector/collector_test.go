// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/02/08 14:07:00, by <EMAIL>, create
*/

package collector

import (
	"context"
	"testing"

	"github.com/golang/mock/gomock"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	stackmodels "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/types"
	typesmock "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/types/mock"
)

func TestCollector_metaClusterHealthCheckGC(t *testing.T) {
	type fields struct {
		model      stackmodels.Interface
		metaClient meta.Interface
	}
	type args struct {
		ctx    context.Context
		client types.Interface
		dbSet  map[string]interface{}
		k8sSet map[string]interface{}
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		// TODO: Add test cases.
		{
			name:   "多余 HealthCheck 需要 GC",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				client: func() types.Interface {
					ctx := context.TODO()
					ctl := gomock.NewController(t)

					client := typesmock.NewMockInterface(ctl)

					gomock.InOrder(
						client.EXPECT().DeleteHealthCheck(ctx, healthCheckNamespace, "cluster-id-5").Return(nil),
					)

					return client
				}(),
				dbSet: map[string]interface{}{
					"cluster-id-0": nil,
					"cluster-id-1": nil,
					"cluster-id-2": nil,
					"cluster-id-3": nil,
					"cluster-id-4": nil,
				},
				k8sSet: map[string]interface{}{
					"cluster-id-0": nil,
					"cluster-id-1": nil,
					"cluster-id-2": nil,
					"cluster-id-3": nil,
					"cluster-id-4": nil,
					"cluster-id-5": nil,
				},
			},
			wantErr: false,
		},
		{
			name:   "无多余 HealthCheck 需要 GC",
			fields: fields{},
			args: args{
				ctx: context.TODO(),
				client: func() types.Interface {
					ctl := gomock.NewController(t)

					client := typesmock.NewMockInterface(ctl)

					return client
				}(),
				dbSet: map[string]interface{}{
					"cluster-id-0": nil,
					"cluster-id-1": nil,
					"cluster-id-2": nil,
					"cluster-id-3": nil,
					"cluster-id-4": nil,
				},
				k8sSet: map[string]interface{}{
					"cluster-id-0": nil,
					"cluster-id-1": nil,
					"cluster-id-2": nil,
					"cluster-id-3": nil,
					"cluster-id-4": nil,
				},
			},
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := &Collector{
				model:      tt.fields.model,
				metaClient: tt.fields.metaClient,
			}
			if err := c.metaClusterHealthCheckGC(tt.args.ctx, tt.args.client, tt.args.dbSet, tt.args.k8sSet); (err != nil) != tt.wantErr {
				t.Errorf("Collector.metaClusterHealthCheckGC() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}
