// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/02/08 14:07:00, by <EMAIL>, create
*/

package collector

import (
	"context"
	"fmt"

	"github.com/prometheus/client_golang/prometheus"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/k8s/meta"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	stackmodels "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/metrics"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/types"
)

// Collector - 将 Cluster 状态写入 ClusterHealthCheck
type Collector struct {
	model      stackmodels.Interface
	metaClient meta.Interface
}

const (
	clusterNamespace     = "default"
	healthCheckNamespace = "health-check"
)

// NewCollector - 初始化 Collector
func NewCollector(ctx context.Context, cceServiceDB, kubeConfig string) (*Collector, error) {
	if cceServiceDB == "" {
		return nil, fmt.Errorf("cceServiceDB is empty")
	}

	// 初始化 model client
	model, err := stackmodels.NewClient(ctx, cceServiceDB)
	if err != nil {
		logger.Errorf(ctx, "stackmodels.NewClient failed: %s", err)
		return nil, err
	}

	// 初始化 meta client
	metaclient, err := meta.NewClient(ctx, kubeConfig)
	if err != nil {
		logger.Errorf(ctx, "meta.NewClient failed: %s", err)
		return nil, err
	}

	return &Collector{
		model:      model,
		metaClient: metaclient,
	}, nil
}

// Run - 数据库对象同步至 HealthCheck CRD, 并更新状态
func (c *Collector) Run(ctx context.Context, collectType string) error {
	client, err := types.NewClient(ctx, collectType, c.model, c.metaClient)
	if err != nil {
		logger.Errorf(ctx, "NewClient failed: %s", err)
		return err
	}

	// 从 MetaCluster 获取 CRD 对象 Set
	crdSet, err := client.GetCRDSetFromMetaCluster(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetCRDSetFromMetaCluster failed: %s", err)
		return err
	}

	logger.Infof(ctx, "CRD %s in MetaCluster length=%d", collectType, len(crdSet))

	// 从 MetaCluster 获取 HealthCheck 对象 Set
	hcSet, err := client.GetHeatlCheckSetFromMetaCluster(ctx)
	if err != nil {
		logger.Errorf(ctx, "GetSetFromK8S failed: %s", err)
		return err
	}

	logger.Infof(ctx, "HealthCheck %s in MetaCluster length=%d", collectType, len(hcSet))

	// 删除 metacluster 无用 HealthCheck
	if err := c.metaClusterHealthCheckGC(ctx, client, crdSet, hcSet); err != nil {
		logger.Errorf(ctx, "metaClusterHealthCheckGC failed: %s", err)
		return err
	}

	// 更新状态
	for id, crd := range crdSet {
		if err := c.ensureCCEHealthCheck(ctx, client, id, crd); err != nil {
			logger.Errorf(ctx, "ensureCCEHealthCheck failed: %s", err)
			// 单个实例的 HealthCheck 失败，不影响其他实例的健康检查
			continue
		}
	}

	return nil
}

// metaClusterHealthCheckGC - 删除 MetaCluster 中多余 HealthCheck CRD
func (c *Collector) metaClusterHealthCheckGC(ctx context.Context, client types.Interface,
	crdSet, hcSet map[string]interface{}) error {
	if hcSet == nil {
		return fmt.Errorf("hcSet is nil")
	}

	if crdSet == nil {
		return fmt.Errorf("crdSet is nil")
	}

	for name := range hcSet {
		if _, ok := crdSet[name]; !ok {
			logger.Infof(ctx, "HealthCheck CRD %s exists but CRD not, delete HealthCheckCRD", name)

			if err := client.DeleteHealthCheck(ctx, healthCheckNamespace, name); err != nil {
				logger.Errorf(ctx, "DeleteHealthCheck failed: %s", err)
				return err
			}
		}
	}

	return nil
}

// ensureCCEHealthCheck - 创建 HealthCheck CRD 并更新状态
func (c *Collector) ensureCCEHealthCheck(ctx context.Context, client types.Interface, name string, crd interface{}) error {
	// 检查 HealthCheck CRD 是否存在
	_, err := client.GetHealthCheck(ctx, healthCheckNamespace, name)
	if err != nil && !kerrors.IsNotFound(err) {
		logger.Errorf(ctx, "HealthCheckExists %s failed: %s", err)
		return err
	}

	// 不存在则新建
	if kerrors.IsNotFound(err) {
		logger.Infof(ctx, "%s HealthCheck not in MetaCluster, create one", name)

		if err := client.CreateHealthCheck(ctx, healthCheckNamespace, name, crd); err != nil {
			logger.Errorf(ctx, "CreateHealthCheck %s failed: %s", err)
			return err
		}
	}

	// 获取对象对应 Cluster
	clusterID, err := client.GetClusterID(ctx, crd)
	if err != nil {
		logger.Errorf(ctx, "GetClusterID %s failed: %s", name, err)
		return err
	}

	// 获取 Cluster KubeConfig
	kubeConfig, err := c.model.GetAdminKubeConfigCompatibility(ctx, clusterID, stackmodels.KubeConfigTypeInternal)
	if err != nil {
		logger.Errorf(ctx, "GetAdminKubeConfigCompatibility %s failed: %s", name, err)
		return err
	}

	// 初始化用户 K8S Client
	k8sClient, err := utils.NewK8SClient(ctx, kubeConfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "NewK8SClient failed: %s", err)
		return err
	}

	// 获取对象 Status
	status, err := client.GetHealthCheckStatus(ctx, k8sClient, healthCheckNamespace, name, crd)
	if err != nil {
		logger.Errorf(ctx, "getHealthCheckStatus %s failed, skip update status: %s", name, err)
		return nil
	}

	if status == nil {
		logger.Errorf(ctx, "%s getHealthCheckStatus return nil, skip update", name)
		return nil
	}

	logger.Infof(ctx, "GetHealthCheckStatus %s success: %s", name, utils.ToJSON(status))

	// 更新 HealthCheck Status
	if err := client.UpdateHealthCheckStatus(ctx, healthCheckNamespace, name, status); err != nil {
		logger.Errorf(ctx, "UpdateHealthCheck %s failed: %s", name, err)
		return err
	}

	return nil
}

// Metrics - 采集 Metrics 指标
func (c *Collector) Metrics(ctx context.Context) error {
	clusterHealthCheckList, err := c.metaClient.ListClusterHealthCheck(ctx, healthCheckNamespace, &metav1.ListOptions{})
	if err != nil {
		logger.Errorf(ctx, "ListClusterHealthCheck failed: %s", err)
		return err
	}

	for _, chc := range clusterHealthCheckList.Items {
		accountID := chc.Spec.AccountID
		clusterID := chc.Spec.ClusterID

		for _, image := range chc.Status.PluginCondition.PluginImages {
			imageID := image.ImageID
			replicas := image.Replicas

			metrics.CCEK8SPluginImageVersionSetCount(prometheus.Labels{
				metrics.LabelAccountID: accountID,
				metrics.LabelClusterID: clusterID,
				metrics.LabelImageID:   imageID,
				metrics.LabelReplicas:  fmt.Sprintf("%d", replicas),
			}, 1)
		}
	}

	return nil
}
