// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2021/02/08 14:07:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Cluster Health Check
*/

package main

import (
	"context"
	"flag"
	"net/http"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"k8s.io/apimachinery/pkg/util/wait"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger/beego"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/collector"
	_ "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/healthcheck/detector/metrics"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/network/lb-controller/signals"
)

func main() {
	var (
		cceServiceDB  string
		kubeConfig    string
		periodSeconds int
	)

	flag.StringVar(&cceServiceDB, "cceServiceDB", "", "CCEServiceDB Endpoint")
	flag.StringVar(&kubeConfig, "kubeconfig", "", "CCE MetaCluster KubeConfig")
	flag.IntVar(&periodSeconds, "periodSeconds", 600, "采集间隔")

	flag.Parse()

	// 默认 10 min
	if periodSeconds <= 0 {
		periodSeconds = 600
	}

	// 初始化 Logger
	logger.SetLogger(beego.NewLogger(""))

	ctx := context.TODO()

	c, err := collector.NewCollector(ctx, cceServiceDB, kubeConfig)
	if err != nil {
		logger.Errorf(ctx, "NewClusterCollector failed: %s", err)
		os.Exit(-1)
	}

	stopCh := signals.SetupSignalHandler()

	// 采集 Cluster
	go wait.Until(func() {
		ctx := context.WithValue(ctx, logger.RequestID, logger.GetUUID())
		logger.Infof(ctx, "--------------------------Collect Cluster begin--------------------------")

		if err := c.Run(ctx, "cluster"); err != nil {
			logger.Errorf(ctx, "collector.Run failed: %s", err)
			logger.Infof(ctx, "-----------------------Collect Cluster failed------------------------")
		}

		logger.Infof(ctx, "--------------------------Collect Cluster end--------------------------")
	}, time.Duration(periodSeconds)*time.Second, stopCh)

	// 采集 Instance
	go wait.Until(func() {
		ctx := context.WithValue(ctx, logger.RequestID, logger.GetUUID())

		logger.Infof(ctx, "--------------------------Collect Instance begin--------------------------")
		if err := c.Run(ctx, "instance"); err != nil {
			logger.Errorf(ctx, "collector.Run failed: %s", err)
			logger.Infof(ctx, "------------------------Collect Instance failed-----------------------")
		}

		logger.Infof(ctx, "--------------------------Collect Instance end--------------------------")
	}, time.Duration(periodSeconds)*time.Second, stopCh)

	// 开启 http 服务
	http.Handle("/metrics", promhttp.Handler())
	go http.ListenAndServe("0.0.0.0:9500", nil)

	// 采集 Metrics
	wait.Until(func() {
		ctx := context.WithValue(ctx, logger.RequestID, logger.GetUUID())

		if err := c.Metrics(ctx); err != nil {
			logger.Errorf(ctx, "collector.Metrics failed: %s", err)
		}

	}, 30*time.Second, stopCh)
}
