# 健康检查

健康检查组件会探测集群真实状态，并将状态回写到 meta 集群中，异常状态会报警会自愈，当前有两个 crd

* InstanceHealthCheck
* ClusterHealthCheck

以 node 健康检查 为例，处理流程是：

1. 提交健康检查资源 InstanceHealthCheck： 每个节点一个，包括集群 ID、节点 ID
2. cce-health-controller 中的 instance-controller 逻辑监听 InstanceHealthCheck 变化，下发 检查用的cronjob，执行探测逻辑
3. health-detector中的 instance-detector会 执行检测任务，是单独的 docker 镜像
4. 检测完成后，将 node ready/磁盘压力等 信息更新到 meta cluster 的 InstanceHealthCheck status 中
5. 每次探测如果 node not ready, 会发送 hi 报警，目前的health-processor是被调用包，不是单独的 crd

## InstanceHealthCheck

node 健康检查: ready状态、node conditions 列表（原生conditions + npd)

## ClusterHealthCheck

cluster 健康检查: ready状态、kubectl get cs 的 conditions 列表

## 示例：

* kubectl get instancehealthcheck -n health-check
* kubectl get clusterhealthcheck -n health-check

```yaml
apiVersion: cce.baidubce.com/v1
kind: ClusterHealthCheck
metadata:
  annotations:
    clusterName: test
    region: gztest
  name: cce-ozxi1oq0
  namespace: health-check
spec:
  clusterID: cce-ozxi1oq0
status:
  clusterCondition:
  - lastHeartbeatTime: "2020-11-05T09:44:05Z"
    lastTransitionTime: "2020-11-05T09:44:05Z"
    status: true
    type: scheduler
  - lastHeartbeatTime: "2020-11-05T09:44:05Z"
    lastTransitionTime: "2020-11-05T09:44:05Z"
    status: true
    type: controller-manager
  - lastHeartbeatTime: "2020-11-05T09:44:05Z"
    lastTransitionTime: "2020-11-05T09:44:05Z"
    status: true
    type: etcd-0
  - lastHeartbeatTime: "2020-11-05T09:44:05Z"
    lastTransitionTime: "2020-11-05T09:44:05Z"
    status: true
    type: apiserver
  ready: true
```


```yaml
apiVersion: cce.baidubce.com/v1
kind: InstanceHealthCheck
metadata:
  annotations:
    clusterName: test
    region: gz-test
  name: cce-ozxi1oq0-tju0v0rq
  namespace: health-check
spec:
  cceInstanceID: cce-ozxi1oq0-tju0v0rq
  clusterID: cce-ozxi1oq0
status:
  instanceCondition:
  - lastHeartbeatTime: "2020-11-04T08:26:29Z"
    lastTransitionTime: "2020-11-04T08:26:29Z"
    message: RouteController created a route
    reason: RouteCreated
    status: "False"
    type: NetworkUnavailable
  - lastHeartbeatTime: "2020-11-05T09:48:47Z"
    lastTransitionTime: "2020-11-05T08:15:33Z"
    message: kubelet has sufficient memory available
    reason: KubeletHasSufficientMemory
    status: "False"
    type: MemoryPressure
  - lastHeartbeatTime: "2020-11-05T09:48:47Z"
    lastTransitionTime: "2020-11-05T08:15:33Z"
    message: kubelet has no disk pressure
    reason: KubeletHasNoDiskPressure
    status: "False"
    type: DiskPressure
  - lastHeartbeatTime: "2020-11-05T09:48:47Z"
    lastTransitionTime: "2020-11-05T08:15:33Z"
    message: kubelet has sufficient PID available
    reason: KubeletHasSufficientPID
    status: "False"
    type: PIDPressure
  - lastHeartbeatTime: "2020-11-05T09:48:47Z"
    lastTransitionTime: "2020-11-05T08:15:43Z"
    message: kubelet is posting ready status
    reason: KubeletReady
    status: "True"
    type: Ready
  ready: true
```