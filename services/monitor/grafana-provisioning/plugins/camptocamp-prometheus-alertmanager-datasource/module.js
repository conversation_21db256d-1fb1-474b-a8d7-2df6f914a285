/*! For license information please see module.js.LICENSE.txt */
define(["react","@grafana/data","@grafana/ui","@grafana/runtime"],(function(t,e,r,n){return function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="/",r(r.s=8)}([function(e,r){e.exports=t},function(t,r){t.exports=e},function(t,e){t.exports=r},function(t,e){t.exports=n},function(t,e,r){var n=r(5),o=r(6);"string"==typeof(o=o.__esModule?o.default:o)&&(o=[[t.i,o,""]]);var i={insert:"head",singleton:!1},a=(n(o,i),o.locals?o.locals:{});t.exports=a},function(t,e,r){"use strict";var n,o=function(){return void 0===n&&(n=Boolean(window&&document&&document.all&&!window.atob)),n},i=function(){var t={};return function(e){if(void 0===t[e]){var r=document.querySelector(e);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}t[e]=r}return t[e]}}(),a=[];function c(t){for(var e=-1,r=0;r<a.length;r++)if(a[r].identifier===t){e=r;break}return e}function u(t,e){for(var r={},n=[],o=0;o<t.length;o++){var i=t[o],u=e.base?i[0]+e.base:i[0],s=r[u]||0,f="".concat(u," ").concat(s);r[u]=s+1;var l=c(f),p={css:i[1],media:i[2],sourceMap:i[3]};-1!==l?(a[l].references++,a[l].updater(p)):a.push({identifier:f,updater:v(p,e),references:1}),n.push(f)}return n}function s(t){var e=document.createElement("style"),n=t.attributes||{};if(void 0===n.nonce){var o=r.nc;o&&(n.nonce=o)}if(Object.keys(n).forEach((function(t){e.setAttribute(t,n[t])})),"function"==typeof t.insert)t.insert(e);else{var a=i(t.insert||"head");if(!a)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");a.appendChild(e)}return e}var f,l=(f=[],function(t,e){return f[t]=e,f.filter(Boolean).join("\n")});function p(t,e,r,n){var o=r?"":n.media?"@media ".concat(n.media," {").concat(n.css,"}"):n.css;if(t.styleSheet)t.styleSheet.cssText=l(e,o);else{var i=document.createTextNode(o),a=t.childNodes;a[e]&&t.removeChild(a[e]),a.length?t.insertBefore(i,a[e]):t.appendChild(i)}}function h(t,e,r){var n=r.css,o=r.media,i=r.sourceMap;if(o?t.setAttribute("media",o):t.removeAttribute("media"),i&&btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleSheet)t.styleSheet.cssText=n;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(n))}}var d=null,y=0;function v(t,e){var r,n,o;if(e.singleton){var i=y++;r=d||(d=s(e)),n=p.bind(null,r,i,!1),o=p.bind(null,r,i,!0)}else r=s(e),n=h.bind(null,r,e),o=function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(r)};return n(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap)return;n(t=e)}else o()}}t.exports=function(t,e){(e=e||{}).singleton||"boolean"==typeof e.singleton||(e.singleton=o());var r=u(t=t||[],e);return function(t){if(t=t||[],"[object Array]"===Object.prototype.toString.call(t)){for(var n=0;n<r.length;n++){var o=c(r[n]);a[o].references--}for(var i=u(t,e),s=0;s<r.length;s++){var f=c(r[s]);0===a[f].references&&(a[f].updater(),a.splice(f,1))}r=i}}}},function(t,e,r){(e=r(7)(!0)).push([t.i,".grafana-json-datasource-editor{width:100%}.grafana-json-datasource-editor div{width:100%}","",{version:3,sources:["json-editor.css"],names:[],mappings:"AAAA,gCAAgC,UAAU,CAAC,oCAAoC,UAAU",file:"json-editor.css",sourcesContent:[".grafana-json-datasource-editor{width:100%}.grafana-json-datasource-editor div{width:100%}"]}]),t.exports=e},function(t,e,r){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r=function(t,e){var r=t[1]||"",n=t[3];if(!n)return r;if(e&&"function"==typeof btoa){var o=(a=n,c=btoa(unescape(encodeURIComponent(JSON.stringify(a)))),u="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(c),"/*# ".concat(u," */")),i=n.sources.map((function(t){return"/*# sourceURL=".concat(n.sourceRoot||"").concat(t," */")}));return[r].concat(i).concat([o]).join("\n")}var a,c,u;return[r].join("\n")}(e,t);return e[2]?"@media ".concat(e[2]," {").concat(r,"}"):r})).join("")},e.i=function(t,r,n){"string"==typeof t&&(t=[[null,t,""]]);var o={};if(n)for(var i=0;i<this.length;i++){var a=this[i][0];null!=a&&(o[a]=!0)}for(var c=0;c<t.length;c++){var u=[].concat(t[c]);n&&o[u[0]]||(r&&(u[2]?u[2]="".concat(r," and ").concat(u[2]):u[2]=r),e.push(u))}},e}},function(t,e,r){"use strict";r.r(e);var n=r(1),o=r(2),i=r(0),a=r.n(i);function c(t,e,r,n){return new(r||(r=Promise))((function(o,i){function a(t){try{u(n.next(t))}catch(t){i(t)}}function c(t){try{u(n.throw(t))}catch(t){i(t)}}function u(t){var e;t.done?o(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(a,c)}u((n=n.apply(t,e||[])).next())}))}Object.create;Object.create;var u=r(3),s={active:!0};function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function l(t){return function(t){if(Array.isArray(t))return p(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(!t)return;if("string"==typeof t)return p(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);"Object"===r&&t.constructor&&(r=t.constructor.name);if("Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,e)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function h(){h=function(){return t};var t={},e=Object.prototype,r=e.hasOwnProperty,n="function"==typeof Symbol?Symbol:{},o=n.iterator||"@@iterator",i=n.asyncIterator||"@@asyncIterator",a=n.toStringTag||"@@toStringTag";function c(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{c({},"")}catch(t){c=function(t,e,r){return t[e]=r}}function u(t,e,r,n){var o=e&&e.prototype instanceof p?e:p,i=Object.create(o.prototype),a=new C(n||[]);return i._invoke=function(t,e,r){var n="suspendedStart";return function(o,i){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw i;return A()}for(r.method=o,r.arg=i;;){var a=r.delegate;if(a){var c=O(a,r);if(c){if(c===l)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var u=s(t,e,r);if("normal"===u.type){if(n=r.done?"completed":"suspendedYield",u.arg===l)continue;return{value:u.arg,done:r.done}}"throw"===u.type&&(n="completed",r.method="throw",r.arg=u.arg)}}}(t,r,a),i}function s(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}t.wrap=u;var l={};function p(){}function d(){}function y(){}var v={};c(v,o,(function(){return this}));var m=Object.getPrototypeOf,g=m&&m(m(S([])));g&&g!==e&&r.call(g,o)&&(v=g);var b=y.prototype=p.prototype=Object.create(v);function w(t){["next","throw","return"].forEach((function(e){c(t,e,(function(t){return this._invoke(e,t)}))}))}function j(t,e){var n;this._invoke=function(o,i){function a(){return new e((function(n,a){!function n(o,i,a,c){var u=s(t[o],t,i);if("throw"!==u.type){var l=u.arg,p=l.value;return p&&"object"==f(p)&&r.call(p,"__await")?e.resolve(p.__await).then((function(t){n("next",t,a,c)}),(function(t){n("throw",t,a,c)})):e.resolve(p).then((function(t){l.value=t,a(l)}),(function(t){return n("throw",t,a,c)}))}c(u.arg)}(o,i,n,a)}))}return n=n?n.then(a,a):a()}}function O(t,e){var r=t.iterator[e.method];if(void 0===r){if(e.delegate=null,"throw"===e.method){if(t.iterator.return&&(e.method="return",e.arg=void 0,O(t,e),"throw"===e.method))return l;e.method="throw",e.arg=new TypeError("The iterator does not provide a 'throw' method")}return l}var n=s(r,t.iterator,e.arg);if("throw"===n.type)return e.method="throw",e.arg=n.arg,e.delegate=null,l;var o=n.arg;return o?o.done?(e[t.resultName]=o.value,e.next=t.nextLoc,"return"!==e.method&&(e.method="next",e.arg=void 0),e.delegate=null,l):o:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,l)}function E(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function x(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function C(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(E,this),this.reset(!0)}function S(t){if(t){var e=t[o];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var n=-1,i=function e(){for(;++n<t.length;)if(r.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=void 0,e.done=!0,e};return i.next=i}}return{next:A}}function A(){return{value:void 0,done:!0}}return d.prototype=y,c(b,"constructor",y),c(y,"constructor",d),d.displayName=c(y,a,"GeneratorFunction"),t.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===d||"GeneratorFunction"===(e.displayName||e.name))},t.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,y):(t.__proto__=y,c(t,a,"GeneratorFunction")),t.prototype=Object.create(b),t},t.awrap=function(t){return{__await:t}},w(j.prototype),c(j.prototype,i,(function(){return this})),t.AsyncIterator=j,t.async=function(e,r,n,o,i){void 0===i&&(i=Promise);var a=new j(u(e,r,n,o),i);return t.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},w(b),c(b,a,"Generator"),c(b,o,(function(){return this})),c(b,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var e=[];for(var r in t)e.push(r);return e.reverse(),function r(){for(;e.length;){var n=e.pop();if(n in t)return r.value=n,r.done=!1,r}return r.done=!0,r}},t.values=S,C.prototype={constructor:C,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(x),!t)for(var e in this)"t"===e.charAt(0)&&r.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=void 0)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var e=this;function n(r,n){return a.type="throw",a.arg=t,e.next=r,n&&(e.method="next",e.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],a=i.completion;if("root"===i.tryLoc)return n("end");if(i.tryLoc<=this.prev){var c=r.call(i,"catchLoc"),u=r.call(i,"finallyLoc");if(c&&u){if(this.prev<i.catchLoc)return n(i.catchLoc,!0);if(this.prev<i.finallyLoc)return n(i.finallyLoc)}else if(c){if(this.prev<i.catchLoc)return n(i.catchLoc,!0)}else{if(!u)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return n(i.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,l):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),l},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),x(r),l}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;x(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,e,r){return this.delegate={iterator:S(t),resultName:e,nextLoc:r},"next"===this.method&&(this.arg=void 0),l}},t}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function y(t,e){return(y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function v(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=g(t);if(e){var o=g(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return m(this,r)}}function m(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function g(t){return(g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var b=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&y(t,e)}(a,t);var e,r,o,i=v(a);function a(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),(e=i.call(this,t)).url=void 0===t.url?"":t.url,e.withCredentials=void 0!==t.withCredentials,e.headers={"Content-Type":"application/json"},"string"==typeof t.basicAuth&&t.basicAuth.length>0&&(e.headers.Authorization=t.basicAuth),e}return e=a,(r=[{key:"query",value:function(t){return c(this,void 0,void 0,h().mark((function e(){var r,o=this;return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=t.targets.map((function(e){if((e=Object.assign(Object.assign({},s),e)).hide)return Promise.resolve(new n.MutableDataFrame);var r=[],i=e.active?"true":"false",a=e.silenced?"true":"false",c=e.inhibited?"true":"false";return r.push("active=".concat(i)),r.push("silenced=".concat(a)),r.push("inhibited=".concat(c)),void 0!==e.receiver&&e.receiver.length>0&&r.push("receiver=".concat(e.receiver)),void 0!==e.filters&&e.filters.length>0&&(e.filters=Object(u.getTemplateSrv)().replace(e.filters,t.scopedVars,o.interpolateQueryExpr),e.filters.split(",").forEach((function(t){r.push("filter=".concat(encodeURIComponent(t)))}))),o.doRequest({url:"".concat(o.url,"/api/v2/alerts?").concat(r.join("&")),method:"GET"}).then((function(t){return t.toPromise()})).then((function(t){return o.retrieveData(e,t)}))})),e.abrupt("return",Promise.all(r).then((function(t){return{data:t}})));case 2:case"end":return e.stop()}}),e)})))}},{key:"testDatasource",value:function(){return c(this,void 0,void 0,h().mark((function t(){return h().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt("return",this.doRequest({url:this.url,method:"GET"}).then((function(t){return t.toPromise().then((function(t){return void 0!==t?t.ok?{status:"success",message:"Datasource is working",title:"Success"}:{status:"error",message:"Datasource is not working: ".concat(t.data),title:"Error"}:{status:"error",message:"Unknown error in datasource",title:"Error"}}))})));case 1:case"end":return t.stop()}}),t,this)})))}},{key:"doRequest",value:function(t){return c(this,void 0,void 0,h().mark((function e(){return h().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return t.withCredentials=this.withCredentials,t.headers=this.headers,e.abrupt("return",Object(u.getBackendSrv)().fetch(t));case 3:case"end":return e.stop()}}),e,this)})))}},{key:"buildDataFrame",value:function(t,e){var r=[{name:"Time",type:n.FieldType.time},{name:"SeverityValue",type:n.FieldType.number}];if(e.length>0){var o=e.map((function(t){return Object.keys(t.annotations)})).flat(),i=e.map((function(t){return Object.keys(t.labels)})).flat();l(new Set([].concat(l(o),l(i),["alertstatus","alertstatus_code"]))).forEach((function(t){r.push({name:t,type:n.FieldType.string})}))}return new n.MutableDataFrame({refId:t,fields:r})}},{key:"parseAlertAttributes",value:function(t,e){var r=4;switch(t.labels.severity){case"critical":r=1;break;case"warning":r=2;break;case"info":r=3}var n=[t.startsAt,r];return e.slice(2).forEach((function(e){n.push(t.annotations[e.name]||t.labels[e.name]||"")})),n}},{key:"retrieveData",value:function(t,e){var r=this,n=this.buildDataFrame(t.refId,e.data);return e.data.forEach((function(t){var e=r.parseAlertAttributes(t,n.fields);n.appendRow(e)})),Promise.resolve(n)}},{key:"interpolateQueryExpr",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1?arguments[1]:void 0;if(!e.multi&&!e.includeAll)return w(t);if("string"==typeof t)return j(t);var r=t.map((function(t){return j(t)}));return 1===r.length?r[0]:"("+r.join("|")+")"}}])&&d(e.prototype,r),o&&d(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(n.DataSourceApi);function w(t){return"string"==typeof t?t.replace(/\\/g,"\\\\").replace(/'/g,"\\\\'"):t}function j(t){return"string"==typeof t?t.replace(/\\/g,"\\\\\\\\").replace(/[$^*{}\[\]\'+?()|]/g,"\\\\$&"):t}r(4);function O(t){return(O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function E(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function x(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function C(t,e){return(C=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function S(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=_(t);if(e){var o=_(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return A(this,r)}}function A(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}function _(t){return(_=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var P=o.LegacyForms.FormField,k=o.LegacyForms.Switch,L=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&C(t,e)}(i,t);var e,r,n,o=S(i);function i(){var t;return E(this,i),(t=o.apply(this,arguments)).onReceiverChange=function(e){var r=t.props,n=r.onChange,o=r.query,i=r.onRunQuery;n(Object.assign(Object.assign({},o),{receiver:e.target.value})),i()},t.onFiltersChange=function(e){var r=t.props,n=r.onChange,o=r.query,i=r.onRunQuery;n(Object.assign(Object.assign({},o),{filters:e.target.value})),i()},t.onActiveChange=function(){var e=t.props,r=e.onChange,n=e.query,o=e.onRunQuery;n.active=!n.active,r(Object.assign({},n)),o()},t.onSilencedChange=function(){var e=t.props,r=e.onChange,n=e.query,o=e.onRunQuery;n.silenced=!n.silenced,r(Object.assign({},n)),o()},t.onInhibitedChange=function(){var e=t.props,r=e.onChange,n=e.query,o=e.onRunQuery;n.inhibited=!n.inhibited,r(Object.assign({},n)),o()},t}return e=i,(r=[{key:"render",value:function(){var t=Object.assign(Object.assign({},s),this.props.query),e=t.receiver,r=t.filters,n=t.active,o=t.silenced,i=t.inhibited;return a.a.createElement(a.a.Fragment,null,a.a.createElement("div",{className:"gf-form-inline"},a.a.createElement("div",{className:"gf-form"},a.a.createElement(P,{value:e,inputWidth:10,onChange:this.onReceiverChange,labelWidth:5,label:"Receiver"})),a.a.createElement("div",{className:"gf-form"},a.a.createElement(P,{value:r,inputWidth:30,onChange:this.onFiltersChange,labelWidth:15,label:"Filters (comma separated key=value)"})),a.a.createElement("div",{className:"gf-form"},a.a.createElement(k,{label:"Active",checked:n,onChange:this.onActiveChange})),a.a.createElement("div",{className:"gf-form"},a.a.createElement(k,{label:"Silenced",checked:o,onChange:this.onSilencedChange})),a.a.createElement("div",{className:"gf-form"},a.a.createElement(k,{label:"Inhibited",checked:i,onChange:this.onInhibitedChange}))))}}])&&x(e.prototype,r),n&&x(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(i.PureComponent);function R(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}function T(t,e,r){return e&&R(t.prototype,e),r&&R(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}r.d(e,"plugin",(function(){return N}));var F=T((function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}));F.templateUrl="partials/annotations.editor.html";var N=new n.DataSourcePlugin(b).setAnnotationQueryCtrl(F).setConfigEditor((function(t){var e=t.options,r=t.onOptionsChange;return a.a.createElement(a.a.Fragment,null,a.a.createElement(o.DataSourceHttpSettings,{defaultUrl:"http://localhost:8080",dataSourceConfig:e,showAccessOptions:!0,onChange:r}))})).setQueryEditor(L)}])}));
//# sourceMappingURL=module.js.map