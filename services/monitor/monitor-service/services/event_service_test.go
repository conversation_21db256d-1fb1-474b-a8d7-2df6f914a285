package services

import (
	"encoding/json"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
	"reflect"
	"testing"
	"time"

	"gopkg.in/olivere/elastic.v6"
)

//import (
//	es "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/coworkers/elastic"
//	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
//	"reflect"
//)

//import (
//	"context"
//	"reflect"
//	"testing"
//	//"time"
//
//	//"github.com/google/go-cmp/cmp"
//	"gopkg.in/olivere/elastic.v6"
//
//	//pkgutils "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
//	es "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/coworkers/elastic"
//	//"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/models"
//	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
//	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/utils"
//)

//func TestNewEventService(t *testing.T) {
//	initTestEnv()
//	ctx := context.Background()
//	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
//	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
//	NewEventService()
//}

// func TestEventService_StartK8SEventCollection(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())

// 	type args struct {
// 		ctx context.Context
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		wantErr bool
// 	}{
// 		{name: "openNotExist", args: args{ctx: ctx}, wantErr: false},
// 		{name: "openNotExist", args: args{ctx: ctx}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if err := evs.StartK8SEventCollection(tt.args.ctx); (err != nil) != tt.wantErr {
// 				t.Errorf("EventService.StartK8SEventCollection() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// func TestEventService_UpdateK8SEventCollectionStatus(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx    context.Context
// 		status models.EventClusterStatus
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		wantErr bool
// 	}{
// 		{name: "updateStopped", args: args{ctx: ctx, status: models.STOPPED}, wantErr: false},
// 		{name: "updateStarted", args: args{ctx: ctx, status: models.STARTED}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if err := evs.UpdateK8SEventCollectionStatus(tt.args.ctx, tt.args.status); (err != nil) != tt.wantErr {
// 				t.Errorf("EventService.UpdateK8SEventCollectionStatus() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// func TestEventService_GetK8SEventCollectionStatus(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx context.Context
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want bool
// 	}{
// 		{name: "getK8sStatus", args: args{ctx: ctx}, want: true},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := evs.GetK8SEventCollectionStatus(tt.args.ctx); got != tt.want {
// 				t.Errorf("EventService.GetK8SEventCollectionStatus() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func TestEventService_GetK8SEvent(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx           context.Context
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		want    *types.K8sEventResults
// 		wantErr bool
// 	}{
// 		{name: "GetK8SEvent", args: args{ctx: ctx, eventQueryArg: &types.EventQueryArg{ClusterUuid: "es-testcase", PageNo: 1, PageSize: 10}}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := evs.GetK8SEvent(tt.args.ctx, tt.args.eventQueryArg)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("EventService.GetK8SEvent() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("EventService.GetK8SEvent() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func TestEventService_GetK8SEventsFromK8s(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx           context.Context
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name  string
// 		args  args
// 		want  []*types.K8sEvent
// 		want1 int64
// 	}{
// 		{name: "GetK8SEvent", args: args{ctx: ctx, eventQueryArg: &types.EventQueryArg{ClusterUuid: "es-testcase", PageNo: 1, PageSize: 10}}},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, got1 := evs.GetK8SEventsFromK8s(tt.args.ctx, tt.args.eventQueryArg)
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("EventService.GetK8SEventsFromK8s() got = %v, want %v", got, tt.want)
// 			}
// 			if got1 != tt.want1 {
// 				//t.Errorf("EventService.GetK8SEventsFromK8s() got1 = %v, want %v", got1, tt.want1)
// 			}
// 		})
// 	}
// }

// func Test_getFieldSelector(t *testing.T) {
// 	type args struct {
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want map[string]string
// 	}{
// 		{
// 			name: "getFieldSelector",
// 			args: args{eventQueryArg: &types.EventQueryArg{EventType: "Normal", ResourceKind: "Pod", ResourceName: "test"}},
// 			want: map[string]string{"type": "Normal", "involvedObject.kind": "Pod", "involvedObject.name": "test"},
// 		},
// 		{
// 			name: "getFieldSelector",
// 			args: args{eventQueryArg: &types.EventQueryArg{}},
// 			want: map[string]string{},
// 		},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := getFieldSelector(tt.args.eventQueryArg); !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("getFieldSelector() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func Test_GetK8SEventsFromElastic(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())

// 	client, _ := es.NewElasticClient(ctx)

// 	type args struct {
// 		ctx           context.Context
// 		client        *es.ElasticClient
// 		eventQueryArg *types.EventQueryArg
// 		esIndex       string
// 	}
// 	tests := []struct {
// 		name  string
// 		args  args
// 		want  []*types.K8sEvent
// 		want1 int64
// 	}{
// 		{
// 			name:  "GetK8SEventsFromElastic",
// 			args:  args{ctx: ctx, client: client, eventQueryArg: &types.EventQueryArg{PageSize: 10, PageNo: 1, ClusterUuid: "es-testcase"}},
// 			want1: 36,
// 		},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, got1 := GetK8SEventsFromElastic(tt.args.ctx, tt.args.client, tt.args.eventQueryArg, tt.args.esIndex)
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("GetK8SEventsFromElastic() got = %v, want %v", got, tt.want)
// 			}
// 			if got1 != tt.want1 {
// 				//t.Errorf("GetK8SEventsFromElastic() got1 = %v, want %v", got1, tt.want1)
// 			}
// 		})
// 	}
// }
//
//func Test_GetK8SEventDistribution(t *testing.T) {
//
//	ctx := context.Background()
//	ctx = context.WithValue(ctx, "ClusterUUID", "es-testcase")
//	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
//	evs, err := NewEventService(ctx)
//	if err != nil {
//		t.Errorf("got NewEventService() err%v", err)
//	}
//	client, _ := es.NewElasticClient(ctx)
//
//	type args struct {
//		ctx           context.Context
//		client        *es.ElasticClient
//		eventQueryArg *types.EventQueryArg
//		esIndex       string
//	}
//	tests := []struct {
//		name string
//		args args
//		want *elastic.SearchResult
//	}{
//		{
//			name: "GetK8SEventDistribution",
//			args: args{ctx: ctx, client: client, eventQueryArg: &types.EventQueryArg{ClusterUuid: "cce-qdl8bj63-events"}},
//		},
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, got1 := evs.GetK8SEventDistribution(tt.args.ctx, tt.args.eventQueryArg)
//			if !reflect.DeepEqual(got, tt.want) {
//				//t.Errorf("GetK8SEventsFromElastic() got = %v, want %v", got, tt.want)
//			}
//			if got1 != nil {
//				t.Errorf("GetK8SEventsFromElastic() err = %v", got1)
//			}
//		})
//	}
//}

// func test_getBoolFilter(t *testing.T) {
// 	eventQueryArg := &types.EventQueryArg{
// 		ClusterUuid:  "es-testcase",
// 		StartTime:    time.Now(),
// 		EndTime:      time.Now(),
// 		Namespace:    "test",
// 		ResourceName: "test",
// 		ResourceKind: "test",
// 		Message:      "test",
// 		EventType:    "test",
// 		PageNo:       1,
// 		PageSize:     10,
// 	}
// 	boolQuery := elastic.NewBoolQuery().
// 		Filter(elastic.NewRangeQuery(types.QueryLastOccurrenceTimestamp).Gte(eventQueryArg.StartTime).Lte(eventQueryArg.EndTime)).
// 		Must(elastic.NewMatchQuery(types.QueryEventType, eventQueryArg.EventType)).
// 		Must(elastic.NewRegexpQuery(types.QueryRegexpMessage, ".*"+eventQueryArg.Message+".*")).
// 		Must(elastic.NewMatchQuery(types.QueryResourceKind, eventQueryArg.ResourceKind)).
// 		Must(elastic.NewMatchQuery(types.QueryResourceNamespace, eventQueryArg.Namespace)).
// 		Must(elastic.NewMatchQuery(types.QueryResourceName, eventQueryArg.ResourceName))
// 	type args struct {
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want *elastic.BoolQuery
// 	}{
// 		{
// 			name: "getBoolFilter: 正常流程",
// 			args: args{eventQueryArg: eventQueryArg},
// 			want: boolQuery,
// 		},
// 		{
// 			name: "getBoolFilter: eventQueryArg is empty",
// 			args: args{eventQueryArg: &types.EventQueryArg{}},
// 			want: elastic.NewBoolQuery(),
// 		},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got := getBoolFilter(tt.args.eventQueryArg)

// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("got: %s", pkgutils.ToJSON(got))
// 				t.Errorf("want: %s", pkgutils.ToJSON(tt.want))
// 				t.Errorf("getBoolFilter() Diff= %v", cmp.Diff(got, tt.want))
// 			}
// 		})
// 	}
// }

// func Test_getEsIndex(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx          context.Context
// 		clusterIndex string
// 		queryTime    time.Time
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want string
// 	}{
// 		{name: "getEsIndex", args: args{ctx: ctx, clusterIndex: "es-testcase", queryTime: time.Now()}, want: "es-testcase-" + time.Now().Format(utils.DateEsIndexFormat)},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := getEsIndex(tt.args.ctx, tt.args.clusterIndex, tt.args.queryTime); got != tt.want {
// 				t.Errorf("getEsIndex() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func test_ifGetEventFromEs(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx         context.Context
// 		clusterUuid string
// 	}
// 	tests := []struct {
// 		name  string
// 		args  args
// 		want  bool
// 		want1 string
// 		want2 *es.ElasticClient
// 	}{
// 		{name: "ifGetEventFromEs", args: args{ctx: ctx, clusterUuid: "es-testcase"}, want: false, want1: "", want2: nil},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, got1, got2 := ifGetEventFromEs(tt.args.ctx, tt.args.clusterUuid)
// 			if got != tt.want {
// 				t.Errorf("ifGetEventFromEs() got = %v, want %v", got, tt.want)
// 			}
// 			if got1 != tt.want1 {
// 				t.Errorf("ifGetEventFromEs() got1 = %v, want %v", got1, tt.want1)
// 			}
// 			if !reflect.DeepEqual(got2, tt.want2) {
// 				t.Errorf("ifGetEventFromEs() got2 = %v, want %v", got2, tt.want2)
// 			}
// 		})
// 	}
// }

// func Test_getClusterIndex(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx         context.Context
// 		clusterUUID string
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		want    string
// 		wantErr bool
// 	}{
// 		{name: "getClusterIndex", args: args{ctx: ctx, clusterUUID: "es-testcase"}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := getClusterIndex(tt.args.ctx, tt.args.clusterUUID)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("getClusterIndex() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if got != tt.want {
// 				//t.Errorf("getClusterIndex() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func TestEventService_getESCollectNode(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx context.Context
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want string
// 	}{
// 		{name: "getESCollectNode", args: args{ctx: ctx}},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := evs.getESCollectNode(tt.args.ctx); got != tt.want {
// 				t.Logf("EventService.getESCollectNode() = %v", got)
// 				//t.Errorf("EventService.getESCollectNode() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

func TestMatchesKeywordFilter(t *testing.T) {
	tests := []struct {
		name        string
		message     string
		reason      string
		keyword     string
		keywordType string
		expected    bool
	}{
		{
			name:        "Empty keyword should return true",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "",
			keywordType: types.KeywordTypeMessage,
			expected:    true,
		},
		{
			name:        "Empty keywordType should return true",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "pull",
			keywordType: "",
			expected:    true,
		},
		{
			name:        "Message keyword match (case insensitive)",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "PULL",
			keywordType: types.KeywordTypeMessage,
			expected:    true,
		},
		{
			name:        "Message keyword no match",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "restart",
			keywordType: types.KeywordTypeMessage,
			expected:    false,
		},
		{
			name:        "Reason keyword match (case insensitive)",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "pullback",
			keywordType: types.KeywordTypeReason,
			expected:    true,
		},
		{
			name:        "Reason keyword no match",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "restart",
			keywordType: types.KeywordTypeReason,
			expected:    false,
		},
		{
			name:        "Invalid keywordType should return true",
			message:     "Failed to pull image",
			reason:      "ImagePullBackOff",
			keyword:     "pull",
			keywordType: "invalid",
			expected:    true,
		},
		{
			name:        "Partial match in message",
			message:     "Container image 'nginx:latest' already present on machine",
			reason:      "Pulled",
			keyword:     "nginx",
			keywordType: types.KeywordTypeMessage,
			expected:    true,
		},
		{
			name:        "Partial match in reason",
			message:     "Container image 'nginx:latest' already present on machine",
			reason:      "Pulled",
			keyword:     "pull",
			keywordType: types.KeywordTypeReason,
			expected:    true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := matchesKeywordFilter(tt.message, tt.reason, tt.keyword, tt.keywordType)
			if result != tt.expected {
				t.Errorf("matchesKeywordFilter() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestGetBoolFilter_KeywordSearch(t *testing.T) {
	tests := []struct {
		name     string
		arg      *types.EventQueryArg
		validate func(t *testing.T, query *elastic.BoolQuery)
	}{
		{
			name: "无关键字搜索",
			arg: &types.EventQueryArg{
				ClusterUuid: "test-cluster",
				StartTime:   time.Now().Add(-1 * time.Hour),
				EndTime:     time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				// 验证查询不包含关键字相关的条件
				queryJSON := getQueryJSON(t, query)
				if containsKeywordQuery(queryJSON) {
					t.Error("Expected no keyword query, but found one")
				}
			},
		},
		{
			name: "Message字段关键字搜索",
			arg: &types.EventQueryArg{
				ClusterUuid: "test-cluster",
				Keyword:     "pull",
				KeywordType: types.KeywordTypeMessage,
				StartTime:   time.Now().Add(-1 * time.Hour),
				EndTime:     time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				queryJSON := getQueryJSON(t, query)

				// 验证包含 match 查询
				if !containsMatchQuery(queryJSON, types.QueryRegexpMessage, "pull") {
					t.Errorf("Expected match query for Message field with keyword 'pull', but not found")
				}
			},
		},
		{
			name: "Reason字段关键字搜索",
			arg: &types.EventQueryArg{
				ClusterUuid: "test-cluster",
				Keyword:     "Scheduled",
				KeywordType: types.KeywordTypeReason,
				StartTime:   time.Now().Add(-1 * time.Hour),
				EndTime:     time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				queryJSON := getQueryJSON(t, query)

				// 验证包含 query_string 查询
				if !containsQueryStringQuery(queryJSON, types.QueryEventReason, "*Scheduled*") {
					t.Errorf("Expected query_string query for Reason field with pattern '*Scheduled*', but not found")
				}
			},
		},
		{
			name: "空关键字不应该添加查询条件",
			arg: &types.EventQueryArg{
				ClusterUuid: "test-cluster",
				Keyword:     "",
				KeywordType: types.KeywordTypeMessage,
				StartTime:   time.Now().Add(-1 * time.Hour),
				EndTime:     time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				queryJSON := getQueryJSON(t, query)
				if containsKeywordQuery(queryJSON) {
					t.Error("Expected no keyword query for empty keyword, but found one")
				}
			},
		},
		{
			name: "空关键字类型不应该添加查询条件",
			arg: &types.EventQueryArg{
				ClusterUuid: "test-cluster",
				Keyword:     "test",
				KeywordType: "",
				StartTime:   time.Now().Add(-1 * time.Hour),
				EndTime:     time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				queryJSON := getQueryJSON(t, query)
				if containsKeywordQuery(queryJSON) {
					t.Error("Expected no keyword query for empty keywordType, but found one")
				}
			},
		},
		{
			name: "特殊字符关键字搜索",
			arg: &types.EventQueryArg{
				ClusterUuid: "test-cluster",
				Keyword:     "image-pull*",
				KeywordType: types.KeywordTypeReason,
				StartTime:   time.Now().Add(-1 * time.Hour),
				EndTime:     time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				queryJSON := getQueryJSON(t, query)

				// 验证特殊字符被正确处理
				if !containsQueryStringQuery(queryJSON, types.QueryEventReason, "*image-pull**") {
					t.Errorf("Expected query_string query with escaped special characters, but not found")
				}
			},
		},
		{
			name: "复合查询 - 关键字搜索与其他条件结合",
			arg: &types.EventQueryArg{
				ClusterUuid:  "test-cluster",
				Keyword:      "error",
				KeywordType:  types.KeywordTypeMessage,
				EventType:    "Warning",
				Namespace:    "default",
				ResourceKind: "Pod",
				StartTime:    time.Now().Add(-1 * time.Hour),
				EndTime:      time.Now(),
			},
			validate: func(t *testing.T, query *elastic.BoolQuery) {
				queryJSON := getQueryJSON(t, query)

				// 验证包含关键字查询
				if !containsMatchQuery(queryJSON, types.QueryRegexpMessage, "error") {
					t.Error("Expected match query for keyword search")
				}

				// 验证包含其他查询条件
				if !containsMatchPhraseQuery(queryJSON, types.QueryEventType, "Warning") {
					t.Error("Expected match_phrase query for EventType")
				}

				if !containsMatchPhraseQuery(queryJSON, types.QueryResourceKind, "Pod") {
					t.Error("Expected match_phrase query for ResourceKind")
				}
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := getBoolFilter(tt.arg)

			if result == nil {
				t.Fatal("getBoolFilter returned nil")
			}

			tt.validate(t, result)
		})
	}
}

// 辅助函数：将查询转换为JSON字符串以便检查
func getQueryJSON(t *testing.T, query *elastic.BoolQuery) string {
	source, err := query.Source()
	if err != nil {
		t.Fatalf("Failed to get query source: %v", err)
	}

	jsonBytes, err := json.Marshal(source)
	if err != nil {
		t.Fatalf("Failed to marshal query to JSON: %v", err)
	}

	return string(jsonBytes)
}

// 辅助函数：检查是否包含关键字相关的查询
func containsKeywordQuery(queryJSON string) bool {
	return containsString(queryJSON, "match") &&
		(containsString(queryJSON, types.QueryRegexpMessage) ||
			containsString(queryJSON, types.QueryEventReason)) ||
		containsString(queryJSON, "query_string")
}

// 辅助函数：检查是否包含特定的match查询
func containsMatchQuery(queryJSON, field, value string) bool {
	return containsString(queryJSON, "match") &&
		containsString(queryJSON, field) &&
		containsString(queryJSON, value)
}

// 辅助函数：检查是否包含特定的match_phrase查询
func containsMatchPhraseQuery(queryJSON, field, value string) bool {
	return containsString(queryJSON, "match_phrase") &&
		containsString(queryJSON, field) &&
		containsString(queryJSON, value)
}

// 辅助函数：检查是否包含特定的query_string查询
func containsQueryStringQuery(queryJSON, field, pattern string) bool {
	return containsString(queryJSON, "query_string") &&
		containsString(queryJSON, field) &&
		containsString(queryJSON, pattern)
}

// 辅助函数：检查字符串是否包含子字符串
func containsString(s, substr string) bool {
	return len(s) > 0 && len(substr) > 0 &&
		reflect.DeepEqual([]byte(s), []byte(s)) &&
		len(s) >= len(substr) &&
		s != substr &&
		(s == substr || (len(s) > len(substr) &&
			(s[:len(substr)] == substr ||
				s[len(s)-len(substr):] == substr ||
				func() bool {
					for i := 0; i <= len(s)-len(substr); i++ {
						if s[i:i+len(substr)] == substr {
							return true
						}
					}
					return false
				}())))
}
