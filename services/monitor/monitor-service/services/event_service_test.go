package services

//import (
//	es "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/coworkers/elastic"
//	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
//	"reflect"
//)

//import (
//	"context"
//	"reflect"
//	"testing"
//	//"time"
//
//	//"github.com/google/go-cmp/cmp"
//	"gopkg.in/olivere/elastic.v6"
//
//	//pkgutils "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
//	es "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/coworkers/elastic"
//	//"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/models"
//	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
//	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/utils"
//)

//func TestNewEventService(t *testing.T) {
//	initTestEnv()
//	ctx := context.Background()
//	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
//	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
//	NewEventService()
//}

// func TestEventService_StartK8SEventCollection(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())

// 	type args struct {
// 		ctx context.Context
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		wantErr bool
// 	}{
// 		{name: "openNotExist", args: args{ctx: ctx}, wantErr: false},
// 		{name: "openNotExist", args: args{ctx: ctx}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if err := evs.StartK8SEventCollection(tt.args.ctx); (err != nil) != tt.wantErr {
// 				t.Errorf("EventService.StartK8SEventCollection() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// func TestEventService_UpdateK8SEventCollectionStatus(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx    context.Context
// 		status models.EventClusterStatus
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		wantErr bool
// 	}{
// 		{name: "updateStopped", args: args{ctx: ctx, status: models.STOPPED}, wantErr: false},
// 		{name: "updateStarted", args: args{ctx: ctx, status: models.STARTED}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if err := evs.UpdateK8SEventCollectionStatus(tt.args.ctx, tt.args.status); (err != nil) != tt.wantErr {
// 				t.Errorf("EventService.UpdateK8SEventCollectionStatus() error = %v, wantErr %v", err, tt.wantErr)
// 			}
// 		})
// 	}
// }

// func TestEventService_GetK8SEventCollectionStatus(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "testuuid")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx context.Context
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want bool
// 	}{
// 		{name: "getK8sStatus", args: args{ctx: ctx}, want: true},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := evs.GetK8SEventCollectionStatus(tt.args.ctx); got != tt.want {
// 				t.Errorf("EventService.GetK8SEventCollectionStatus() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func TestEventService_GetK8SEvent(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx           context.Context
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		want    *types.K8sEventResults
// 		wantErr bool
// 	}{
// 		{name: "GetK8SEvent", args: args{ctx: ctx, eventQueryArg: &types.EventQueryArg{ClusterUuid: "es-testcase", PageNo: 1, PageSize: 10}}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := evs.GetK8SEvent(tt.args.ctx, tt.args.eventQueryArg)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("EventService.GetK8SEvent() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("EventService.GetK8SEvent() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func TestEventService_GetK8SEventsFromK8s(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx           context.Context
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name  string
// 		args  args
// 		want  []*types.K8sEvent
// 		want1 int64
// 	}{
// 		{name: "GetK8SEvent", args: args{ctx: ctx, eventQueryArg: &types.EventQueryArg{ClusterUuid: "es-testcase", PageNo: 1, PageSize: 10}}},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, got1 := evs.GetK8SEventsFromK8s(tt.args.ctx, tt.args.eventQueryArg)
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("EventService.GetK8SEventsFromK8s() got = %v, want %v", got, tt.want)
// 			}
// 			if got1 != tt.want1 {
// 				//t.Errorf("EventService.GetK8SEventsFromK8s() got1 = %v, want %v", got1, tt.want1)
// 			}
// 		})
// 	}
// }

// func Test_getFieldSelector(t *testing.T) {
// 	type args struct {
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want map[string]string
// 	}{
// 		{
// 			name: "getFieldSelector",
// 			args: args{eventQueryArg: &types.EventQueryArg{EventType: "Normal", ResourceKind: "Pod", ResourceName: "test"}},
// 			want: map[string]string{"type": "Normal", "involvedObject.kind": "Pod", "involvedObject.name": "test"},
// 		},
// 		{
// 			name: "getFieldSelector",
// 			args: args{eventQueryArg: &types.EventQueryArg{}},
// 			want: map[string]string{},
// 		},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := getFieldSelector(tt.args.eventQueryArg); !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("getFieldSelector() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func Test_GetK8SEventsFromElastic(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())

// 	client, _ := es.NewElasticClient(ctx)

// 	type args struct {
// 		ctx           context.Context
// 		client        *es.ElasticClient
// 		eventQueryArg *types.EventQueryArg
// 		esIndex       string
// 	}
// 	tests := []struct {
// 		name  string
// 		args  args
// 		want  []*types.K8sEvent
// 		want1 int64
// 	}{
// 		{
// 			name:  "GetK8SEventsFromElastic",
// 			args:  args{ctx: ctx, client: client, eventQueryArg: &types.EventQueryArg{PageSize: 10, PageNo: 1, ClusterUuid: "es-testcase"}},
// 			want1: 36,
// 		},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, got1 := GetK8SEventsFromElastic(tt.args.ctx, tt.args.client, tt.args.eventQueryArg, tt.args.esIndex)
// 			if !reflect.DeepEqual(got, tt.want) {
// 				//t.Errorf("GetK8SEventsFromElastic() got = %v, want %v", got, tt.want)
// 			}
// 			if got1 != tt.want1 {
// 				//t.Errorf("GetK8SEventsFromElastic() got1 = %v, want %v", got1, tt.want1)
// 			}
// 		})
// 	}
// }
//
//func Test_GetK8SEventDistribution(t *testing.T) {
//
//	ctx := context.Background()
//	ctx = context.WithValue(ctx, "ClusterUUID", "es-testcase")
//	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
//	evs, err := NewEventService(ctx)
//	if err != nil {
//		t.Errorf("got NewEventService() err%v", err)
//	}
//	client, _ := es.NewElasticClient(ctx)
//
//	type args struct {
//		ctx           context.Context
//		client        *es.ElasticClient
//		eventQueryArg *types.EventQueryArg
//		esIndex       string
//	}
//	tests := []struct {
//		name string
//		args args
//		want *elastic.SearchResult
//	}{
//		{
//			name: "GetK8SEventDistribution",
//			args: args{ctx: ctx, client: client, eventQueryArg: &types.EventQueryArg{ClusterUuid: "cce-qdl8bj63-events"}},
//		},
//		// TODO: Add test cases.
//	}
//	for _, tt := range tests {
//		t.Run(tt.name, func(t *testing.T) {
//			got, got1 := evs.GetK8SEventDistribution(tt.args.ctx, tt.args.eventQueryArg)
//			if !reflect.DeepEqual(got, tt.want) {
//				//t.Errorf("GetK8SEventsFromElastic() got = %v, want %v", got, tt.want)
//			}
//			if got1 != nil {
//				t.Errorf("GetK8SEventsFromElastic() err = %v", got1)
//			}
//		})
//	}
//}

// func test_getBoolFilter(t *testing.T) {
// 	eventQueryArg := &types.EventQueryArg{
// 		ClusterUuid:  "es-testcase",
// 		StartTime:    time.Now(),
// 		EndTime:      time.Now(),
// 		Namespace:    "test",
// 		ResourceName: "test",
// 		ResourceKind: "test",
// 		Message:      "test",
// 		EventType:    "test",
// 		PageNo:       1,
// 		PageSize:     10,
// 	}
// 	boolQuery := elastic.NewBoolQuery().
// 		Filter(elastic.NewRangeQuery(types.QueryLastOccurrenceTimestamp).Gte(eventQueryArg.StartTime).Lte(eventQueryArg.EndTime)).
// 		Must(elastic.NewMatchQuery(types.QueryEventType, eventQueryArg.EventType)).
// 		Must(elastic.NewRegexpQuery(types.QueryRegexpMessage, ".*"+eventQueryArg.Message+".*")).
// 		Must(elastic.NewMatchQuery(types.QueryResourceKind, eventQueryArg.ResourceKind)).
// 		Must(elastic.NewMatchQuery(types.QueryResourceNamespace, eventQueryArg.Namespace)).
// 		Must(elastic.NewMatchQuery(types.QueryResourceName, eventQueryArg.ResourceName))
// 	type args struct {
// 		eventQueryArg *types.EventQueryArg
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want *elastic.BoolQuery
// 	}{
// 		{
// 			name: "getBoolFilter: 正常流程",
// 			args: args{eventQueryArg: eventQueryArg},
// 			want: boolQuery,
// 		},
// 		{
// 			name: "getBoolFilter: eventQueryArg is empty",
// 			args: args{eventQueryArg: &types.EventQueryArg{}},
// 			want: elastic.NewBoolQuery(),
// 		},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got := getBoolFilter(tt.args.eventQueryArg)

// 			if !reflect.DeepEqual(got, tt.want) {
// 				t.Errorf("got: %s", pkgutils.ToJSON(got))
// 				t.Errorf("want: %s", pkgutils.ToJSON(tt.want))
// 				t.Errorf("getBoolFilter() Diff= %v", cmp.Diff(got, tt.want))
// 			}
// 		})
// 	}
// }

// func Test_getEsIndex(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx          context.Context
// 		clusterIndex string
// 		queryTime    time.Time
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want string
// 	}{
// 		{name: "getEsIndex", args: args{ctx: ctx, clusterIndex: "es-testcase", queryTime: time.Now()}, want: "es-testcase-" + time.Now().Format(utils.DateEsIndexFormat)},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := getEsIndex(tt.args.ctx, tt.args.clusterIndex, tt.args.queryTime); got != tt.want {
// 				t.Errorf("getEsIndex() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func test_ifGetEventFromEs(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx         context.Context
// 		clusterUuid string
// 	}
// 	tests := []struct {
// 		name  string
// 		args  args
// 		want  bool
// 		want1 string
// 		want2 *es.ElasticClient
// 	}{
// 		{name: "ifGetEventFromEs", args: args{ctx: ctx, clusterUuid: "es-testcase"}, want: false, want1: "", want2: nil},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, got1, got2 := ifGetEventFromEs(tt.args.ctx, tt.args.clusterUuid)
// 			if got != tt.want {
// 				t.Errorf("ifGetEventFromEs() got = %v, want %v", got, tt.want)
// 			}
// 			if got1 != tt.want1 {
// 				t.Errorf("ifGetEventFromEs() got1 = %v, want %v", got1, tt.want1)
// 			}
// 			if !reflect.DeepEqual(got2, tt.want2) {
// 				t.Errorf("ifGetEventFromEs() got2 = %v, want %v", got2, tt.want2)
// 			}
// 		})
// 	}
// }

// func Test_getClusterIndex(t *testing.T) {
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx         context.Context
// 		clusterUUID string
// 	}
// 	tests := []struct {
// 		name    string
// 		args    args
// 		want    string
// 		wantErr bool
// 	}{
// 		{name: "getClusterIndex", args: args{ctx: ctx, clusterUUID: "es-testcase"}, wantErr: false},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			got, err := getClusterIndex(tt.args.ctx, tt.args.clusterUUID)
// 			if (err != nil) != tt.wantErr {
// 				t.Errorf("getClusterIndex() error = %v, wantErr %v", err, tt.wantErr)
// 				return
// 			}
// 			if got != tt.want {
// 				//t.Errorf("getClusterIndex() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }

// func TestEventService_getESCollectNode(t *testing.T) {
// 	evs := NewEventService()
// 	ctx := context.Background()
// 	ctx = context.WithValue(ctx, utils.ClusterUUID, "es-testcase")
// 	ctx = context.WithValue(ctx, utils.RequestID, utils.GetRandom())
// 	type args struct {
// 		ctx context.Context
// 	}
// 	tests := []struct {
// 		name string
// 		args args
// 		want string
// 	}{
// 		{name: "getESCollectNode", args: args{ctx: ctx}},
// 		// TODO: Add test cases.
// 	}
// 	for _, tt := range tests {
// 		t.Run(tt.name, func(t *testing.T) {
// 			if got := evs.getESCollectNode(tt.args.ctx); got != tt.want {
// 				t.Logf("EventService.getESCollectNode() = %v", got)
// 				//t.Errorf("EventService.getESCollectNode() = %v, want %v", got, tt.want)
// 			}
// 		})
// 	}
// }
