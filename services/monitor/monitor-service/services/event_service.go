// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/05/08 10:40:00, by <EMAIL>, update
*/
/*
EventService 方法封装为 EventServiceInterface
*/

package services

import (
	"context"
	"fmt"
	"math/rand"
	"sort"
	"strings"
	"time"

	"github.com/astaxie/beego"
	"github.com/pkg/errors"
	"gopkg.in/olivere/elastic.v6"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	stackmodels "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	es "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/coworkers/elastic"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/coworkers/k8s"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/utils"
)

// EventServiceInterface - 定义 CCE Event 服务 Interface
type EventServiceInterface interface {
	GetK8SEvent(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventResults, error)

	StartK8SEventCollection(ctx context.Context, clusterID string) error
	GetK8SEventCollectionStatus(ctx context.Context, clusterID string) (bool, error)
	UpdateK8SEventCollectionStatus(ctx context.Context, clusterID string, status stackmodels.EventClusterStatus) error

	GetK8SEventDistribution(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
	GetK8SAbResourceDistribution(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
	GetK8SAbObjectDistribution(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
	GetK8SMountVolumeFailedCount(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
	GetK8SPullImageFailedCount(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
	GetK8SEventTypeTrend(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
	GetK8SAbEventTrend(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error)
}

// eventService definition
type eventService struct {
	base *BaseService

	nodes []string
}

// NewEventService function
func NewEventService(ctx context.Context) (EventServiceInterface, error) {
	// 初始化 BaseService
	base, err := NewBaseService(ctx)
	if err != nil {
		logger.Errorf(ctx, "NewBaseService failed: %s", err)
		return nil, err
	}

	nodes := beego.AppConfig.String("EventCollectorTags")
	if nodes == "" {
		return nil, fmt.Errorf("EventCollectorTags in config is empty")
	}

	return &eventService{
		base:  base,
		nodes: strings.Split(nodes, ","),
	}, nil
}

// StartK8SEventCollection - 对集群开启事件采集
// TODO: 开启采集是否需要 RBAC 权限
func (s *eventService) StartK8SEventCollection(ctx context.Context, clusterID string) error {
	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	// 检查集群是否被采集
	exist, err := s.base.models.IfK8SEventClusterExists(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "IfK8SEventClusterExists failed: %s", err)
		return err
	}

	if exist {
		err := s.UpdateK8SEventCollectionStatus(ctx, clusterID, stackmodels.EventClusterStatusStarted)
		if err != nil {
			logger.Errorf(ctx, "UpdateK8SEventCollectionStatus %s failed: %s", clusterID, err)
			return err
		}

		return nil
	}

	// 获取集群 Admin KubeConfig
	kubeConfig, err := s.base.models.GetAdminKubeConfigCompatibility(ctx, clusterID, stackmodels.KubeConfigTypeInternal)
	if err != nil {
		logger.Errorf(ctx, "GetKubeConfigCompatibility failed: %s", err)
		return err
	}

	// 获取 ClusterIndex
	clusterIndex, err := s.getClusterIndex(ctx, clusterID)
	if err != nil {
		logger.Errorf(ctx, "getClusterIndex failed: %s", err)
		return err
	}

	// 新增集群采集
	if err := s.base.models.AddK8SEventCluster(ctx, &stackmodels.K8SEventCluster{
		ClusterID:    clusterID,
		ClusterIndex: clusterIndex,
		Status:       stackmodels.EventClusterStatusStarted,
		EventTag:     s.getESCollectNode(ctx),
		KubeConfig:   kubeConfig.KubeConfigFile,
	}); err != nil {
		logger.Errorf(ctx, "AddK8SEventCluster failed: %s", err)
		return err
	}

	return nil
}

// UpdateK8SEventCollectionStatus - 更新集群采集状态
func (s *eventService) UpdateK8SEventCollectionStatus(ctx context.Context, clusterID string, status stackmodels.EventClusterStatus) error {
	if clusterID == "" {
		return fmt.Errorf("clusterID is empty")
	}

	err := s.base.models.UpdateEventClusterStatus(ctx, clusterID, status)
	if err != nil && !stackmodels.IsNotExist(err) {
		logger.Errorf(ctx, "UpdateEventClusterStatus failed: %s", err)
		return err
	}

	// Cluster 不存在, Started 报错
	if stackmodels.IsNotExist(err) && status == stackmodels.EventClusterStatusStarted {
		logger.Errorf(ctx, "Cluster %s not exist in t_k8s_event_cluster, update to start failed")
		return fmt.Errorf("")
	}

	// Cluster 不存在, 跳过 Stopped
	if stackmodels.IsNotExist(err) && status == stackmodels.EventClusterStatusStopped {
		logger.Infof(ctx, "Cluster %s not exist in t_k8s_event_cluster, skip update stopped")
		return nil
	}

	return nil
}

// GetK8SEventCollectionStatus - 获取集群采集状态
func (s *eventService) GetK8SEventCollectionStatus(ctx context.Context, clusterID string) (bool, error) {
	if clusterID == "" {
		return false, fmt.Errorf("clusterID is empty")
	}

	eventCluster, err := s.base.models.GetK8SEventCluster(ctx, clusterID)
	if err != nil && !stackmodels.IsNotExist(err) {
		logger.Errorf(ctx, "QueryK8SEventCluster failed: %s", err)
		return false, err
	}

	if stackmodels.IsNotExist(err) || eventCluster == nil {
		logger.Infof(ctx, "GetK8SEventCluster return nil")
		return false, nil
	}

	return eventCluster.Status == stackmodels.EventClusterStatusStarted, nil
}

// GetK8SEvent function
func (s *eventService) GetK8SEvent(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventResults, error) {
	var k8sEvents []*types.K8sEvent
	var totalCount int64

	fromES, esIndex, client, err := s.ifGetEventFromES(ctx, eventQueryArg.ClusterUuid)
	if err != nil {
		logger.Errorf(ctx, "ifGetEventFromES failed, try get from K8S: %s", err)
		fromES = false
	}

	// 获取 K8S Event
	if !fromES {
		logger.Infof(ctx, "Get K8S Event from K8S")

		k8sEvents, totalCount, err = s.getK8SEventsFromK8S(ctx, eventQueryArg)
		if err != nil {
			logger.Errorf(ctx, "GetK8SEventsFromK8S failed: %s", err)
			return nil, err
		}
	} else {
		logger.Infof(ctx, "Get K8S Event from Elastic")
		k8sEvents, totalCount = getK8SEventsFromElastic(ctx, client, eventQueryArg, esIndex)
	}

	logger.Infof(ctx, "GetK8SEvent k8s event %+v, totalCount %v", k8sEvents, totalCount)

	eventsResult := &types.K8sEventResults{
		Result:     k8sEvents,
		PageNo:     eventQueryArg.PageNo,
		PageSize:   eventQueryArg.PageSize,
		TotalCount: totalCount,
	}

	return eventsResult, nil
}

func (s *eventService) getK8SEventsFromK8S(ctx context.Context, eventQueryArg *types.EventQueryArg) (types.K8SEvents, int64, error) {
	var result types.K8SEvents

	clusterID := eventQueryArg.ClusterUuid

	// 获取 Admin 的 kubeconfig
	kubeconfig, err := s.base.models.GetAdminKubeConfigCompatibility(ctx, clusterID, stackmodels.KubeConfigTypeInternal)
	if err != nil && !stackmodels.IsNotExist(err) {
		logger.Errorf(ctx, "GetKubeConfigCompatibility failed: %s", err)
		return result, 0, err
	}

	if stackmodels.IsNotExist(err) || kubeconfig == nil {
		return result, 0, fmt.Errorf("GetKubeConfigCompatibility %s return nil", clusterID)
	}

	// 初始化 Coworker
	coworker, err := k8s.NewCoworkerByKubeConfig(ctx, clusterID, kubeconfig.KubeConfigFile)
	if err != nil {
		logger.Errorf(ctx, "NewCoworkerByKubeConfig failed: %s", err)
		return result, 0, err
	}

	// 获取 K8S Event
	eventList, err := coworker.GetK8SEvents(ctx, eventQueryArg.Namespace, getFieldSelector(eventQueryArg))
	if err != nil {
		logger.Errorf(ctx, "GetK8SEvents failed: %s", err)
		return result, 0, err
	}

	var k8sEvents []*types.K8sEvent
	var lastOccurrenceTimestamp time.Time
	var firstOccurrenceTimestamp time.Time

	for _, event := range eventList {
		if eventQueryArg.Message != "" && !strings.Contains(event.Message, eventQueryArg.Message) {
			continue
		}

		// 部分k8s资源这个FirstOccurrenceTimestamp/LastOccurrenceTimestamp为null
		if event.LastTimestamp.UTC().IsZero() {
			lastOccurrenceTimestamp = event.CreationTimestamp.Time.UTC()
		} else {
			lastOccurrenceTimestamp = event.LastTimestamp.Time.UTC()
		}

		if event.FirstTimestamp.UTC().IsZero() {
			firstOccurrenceTimestamp = event.CreationTimestamp.Time.UTC()
		} else {
			firstOccurrenceTimestamp = event.FirstTimestamp.Time.UTC()
		}

		if lastOccurrenceTimestamp.After(eventQueryArg.EndTime) || lastOccurrenceTimestamp.Before(eventQueryArg.StartTime) {
			beego.Info(utils.Message(ctx, fmt.Sprintf("GetK8SEventsFromK8s event.LastTimestamp %v", lastOccurrenceTimestamp)))
			continue
		}

		// 添加 keyword 过滤
		if !matchesKeywordFilter(event.Message, event.Reason, eventQueryArg.Keyword, eventQueryArg.KeywordType) {
			continue
		}

		k8sEvent := types.K8sEvent{
			Count: event.Count,
			EventTags: types.EventTags{
				EventID:   string(event.UID),
				ClusterID: event.ClusterName,
			},
			FirstOccurrenceTimestamp: firstOccurrenceTimestamp,
			LastOccurrenceTimestamp:  lastOccurrenceTimestamp,
			Message:                  event.Message,
			Reason:                   event.Reason,
			Type:                     event.Type,
			Metadata:                 event.ObjectMeta,
			InvolvedObject:           event.InvolvedObject,
			Source:                   event.Source,
		}
		k8sEvents = append(k8sEvents, &k8sEvent)
		beego.Info(utils.Message(ctx, fmt.Sprintf("GetK8SEventsFromK8s event item %+v", &k8sEvent)))
	}

	beego.Info(utils.Message(ctx, fmt.Sprintf("GetK8SEventsFromK8s filter event length %v filterEvents %+v", len(k8sEvents), k8sEvents)))

	sliceStartIndex := (eventQueryArg.PageNo - 1) * eventQueryArg.PageSize
	sliceEndIndex := eventQueryArg.PageNo * eventQueryArg.PageSize

	if (len(k8sEvents) == 0) || (sliceStartIndex > len(k8sEvents)) {
		sliceStartIndex = 0
		sliceEndIndex = 0
	} else if sliceEndIndex > len(k8sEvents) {
		sliceEndIndex = len(k8sEvents)
	}

	sort.Sort(types.K8SEvents(k8sEvents))

	sliceEvents := k8sEvents[sliceStartIndex:sliceEndIndex]
	beego.Info(utils.Message(ctx, fmt.Sprintf("GetK8SEventsFromK8s sliceEvents %+v from start %v, end %v",
		sliceEvents, sliceStartIndex, sliceEndIndex)))

	return sliceEvents, int64(len(k8sEvents)), nil
}

func getFieldSelector(eventQueryArg *types.EventQueryArg) map[string]string {
	fields := map[string]string{}
	if eventQueryArg.EventType != "" {
		fields["type"] = eventQueryArg.EventType
	}

	if eventQueryArg.ResourceKind != "" {
		fields["involvedObject.kind"] = eventQueryArg.ResourceKind
	}

	if eventQueryArg.ResourceName != "" {
		fields["involvedObject.name"] = eventQueryArg.ResourceName
	}

	return fields
}

// getK8SEventsFromElastic - 从 ES 集群获取 K8S Event
func getK8SEventsFromElastic(ctx context.Context, client *es.ElasticClient, eventQueryArg *types.EventQueryArg,
	esIndex string) ([]*types.K8sEvent, int64) {
	boolQuery := getBoolFilter(eventQueryArg)

	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"

	from := (eventQueryArg.PageNo - 1) * eventQueryArg.PageSize

	total, k8sEvents, _ := client.GetElasticLog(
		ctx, clusterIndexAlias, boolQuery, from, eventQueryArg.PageSize)

	return k8sEvents, total
}

func getBoolFilter(eventQueryArg *types.EventQueryArg) *elastic.BoolQuery {
	boolQuery := elastic.NewBoolQuery()

	if !eventQueryArg.StartTime.IsZero() && !eventQueryArg.EndTime.IsZero() {
		boolQuery = boolQuery.Filter(
			elastic.NewRangeQuery(types.QueryLastOccurrenceTimestamp).Gte(eventQueryArg.StartTime).Lte(eventQueryArg.EndTime))
	}

	if eventQueryArg.EventType != "" {
		boolQuery = boolQuery.Must(elastic.NewMatchPhraseQuery(types.QueryEventType, eventQueryArg.EventType))
	}

	if eventQueryArg.Message != "" {
		boolQuery = boolQuery.Must(elastic.NewMatchPhraseQuery(types.QueryRegexpMessage, eventQueryArg.Message))
	}

	if eventQueryArg.ResourceKind != "" {
		boolQuery = boolQuery.Must(elastic.NewMatchPhraseQuery(types.QueryResourceKind, eventQueryArg.ResourceKind))
	}

	if eventQueryArg.Namespace != "" {
		boolQuery = boolQuery.Must(elastic.NewMatchPhraseQuery(types.QueryResourceNamespace, eventQueryArg.Namespace))
	}

	if eventQueryArg.ResourceName != "" {
		boolQuery = boolQuery.Must(elastic.NewMatchPhraseQuery(types.QueryResourceName, eventQueryArg.ResourceName))
	}

	// 添加 keyword 模糊搜索支持 - 性能优化和大小写不敏感修复
	if eventQueryArg.Keyword != "" && eventQueryArg.KeywordType != "" {
		switch eventQueryArg.KeywordType {
		case types.KeywordTypeMessage:
			// 对 Message 字段进行模糊搜索
			// Message 字段在 ES mapping 中是 analyzed 的，使用 match 查询
			// match 查询原生支持大小写不敏感和分词，性能优于 wildcard
			boolQuery = boolQuery.Must(elastic.NewMatchQuery(types.QueryRegexpMessage, eventQueryArg.Keyword))
		case types.KeywordTypeReason:
			// 对 Reason 字段进行模糊搜索，同时支持大小写不敏感
			// 使用 wildcard 查询的组合方式，覆盖常见的大小写情况
			// 使用 query_string 查询，配合 wildcard 实现大小写不敏感的模糊搜索
			queryString := "*" + eventQueryArg.Keyword + "*"
			boolQuery = boolQuery.Must(
				elastic.NewQueryStringQuery(queryString).
					Field(types.QueryEventReason).
					AnalyzeWildcard(true).
					DefaultOperator("AND"))
		}
	}

	return boolQuery
}

func (s *eventService) ifGetEventFromES(ctx context.Context, clusterID string) (bool, string, *es.ElasticClient, error) {
	var fromES bool
	var esIndex string
	var client *es.ElasticClient

	if clusterID == "" {
		return fromES, esIndex, client, fmt.Errorf("clusterID is empty")
	}

	eventCluster, err := s.base.models.GetK8SEventCluster(ctx, clusterID)
	if err != nil && !stackmodels.IsNotExist(err) {
		logger.Errorf(ctx, "GetK8SEventCluster failed: %s", err)
		return fromES, esIndex, client, err
	}

	// 记录不存在
	if stackmodels.IsNotExist(err) || eventCluster == nil {
		logger.Infof(ctx, "clusterID=%s not exists in t_k8s_event_cluster", clusterID)
		return false, "", nil, nil
	}

	// 存在当天索引，事件从 ES 取, 不存在从 K8S 取数据
	clusterIndex := eventCluster.ClusterIndex
	esIndex = getESIndex(ctx, clusterIndex, time.Now())
	client, err = es.NewElasticClient(ctx)
	if err != nil {
		beego.Error(fmt.Sprintf("ifGetEventFromES NewElasticClient err %v", err))
		return fromES, esIndex, client, err
	}

	// 如果采集开启, 则从 ES 获取 Event
	if eventCluster.Status == stackmodels.EventClusterStatusStarted {
		return true, esIndex, client, nil
	}

	return false, "", nil, nil
}

func (s *eventService) getClusterIndex(ctx context.Context, clusterID string) (string, error) {
	for i := 0; i < types.IndexRetryCount; i++ {
		clusterIndex := strings.ToLower(utils.GenerateShortUuid(clusterID + "-"))

		exist, err := s.base.models.IfK8SEventClusterIndexExists(ctx, clusterIndex)
		if err != nil {
			logger.Errorf(ctx, "IfK8SEventClusterIndexExists failed: %s", err)
			return "", err
		}

		if !exist {
			return clusterIndex, nil
		}

		logger.Infof(ctx, "clusterIndex already exist and retry, time: %d", i)
	}

	return "", errors.Errorf("create backupID failed")
}

func (s *eventService) getESCollectNode(ctx context.Context) string {
	rand.Seed(time.Now().Unix())
	random := rand.Intn(len(s.nodes))

	randomNode := s.nodes[random]

	beego.Info(utils.Message(ctx, fmt.Sprintf("getESCollectNode random %v randomNode %v", random, randomNode)))

	return randomNode
}

func getESIndex(ctx context.Context, clusterIndex string, queryTime time.Time) string {
	esTimePostfix := queryTime.Format(utils.DateEsIndexFormat)

	esIndex := strings.Join([]string{clusterIndex, esTimePostfix}, "-")

	return esIndex
}

func (s *eventService) CheckESStatus(ctx context.Context, eventQueryArg *types.EventQueryArg) (*es.ElasticClient, *elastic.BoolQuery, error) {

	fromES, _, client, err := s.ifGetEventFromES(ctx, eventQueryArg.ClusterUuid)
	if err != nil {
		logger.Errorf(ctx, "ifGetEventDistributionFromES failed: %s", err)
		return nil, nil, err
	}

	// 获取 K8S Event 的统计数据
	// ES Cluster id 不存在、无ES记录、创建新的ES_Client 失败，均会导致fromES == false
	if !fromES {
		logger.Infof(ctx, "GetK8SEventDistributionFromES Failed: clusterID=%s not exists in t_k8s_event_cluster", eventQueryArg.ClusterUuid)
		return nil, nil, nil
	}
	if eventQueryArg.EndTime.IsZero() {
		eventQueryArg.EndTime = time.Now()
	}

	//增加全局筛选条件
	boolQuery := getBoolFilter(eventQueryArg)
	return client, boolQuery, nil
}

// GetK8SEventDistribution - 获取事件分布
func (s *eventService) GetK8SEventDistribution(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {

	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || boolQuery == nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Event Distribution from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	logger.Infof(ctx, "Get K8S Event Distribution from Elastic Search")
	var searchResult *elastic.SearchResult
	agg := elastic.NewTermsAggregation().
		Field(types.QueryEventType).
		Size(20)

	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"

	searchResult, err = client.GetElasticTermsAgg(ctx, clusterIndexAlias, boolQuery, agg)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Get K8S Event Distribution failed: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Get K8S Event Distribution failed: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	result := make(map[string]int64)
	aggRes, found := searchResult.Aggregations.Terms(types.AggTerms)

	if !found {
		logger.Errorf(ctx, fmt.Sprintf("Extract %s Event Distribution not found", types.QueryEventType))
		return &types.K8sEventStatistic{}, nil
	}
	for _, bucket := range aggRes.Buckets {
		result[(bucket.Key.(string))] = bucket.DocCount
	}
	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}

	return eventStatistic, nil

}

// GetK8SAbResourceDistribution - 异常资源对象分布 | 异常节点数(包含)
func (s *eventService) GetK8SAbResourceDistribution(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {
	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || boolQuery == nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Resource Distribution from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}

	var searchResult *elastic.SearchResult
	if eventQueryArg.EventType == "" {
		logger.Errorf(ctx, "Error happened when Get K8S Abnormal Resource Distribution from Elastic Search: eventQueryArg.eventType cannot be empty")
		return &types.K8sEventStatistic{}, err
	}
	//构造ES聚合语句
	agg := elastic.NewTermsAggregation().Field(types.QueryResourceKind).Size(100)

	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"

	searchResult, err = client.GetElasticTermsAgg(ctx, clusterIndexAlias, boolQuery, agg)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Get K8S Abnormal Resource Distribution failed: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Get K8S Abnormal Resource Distribution failed: %s", err))

		return &types.K8sEventStatistic{}, err

	}
	result := make(map[string]int64)
	aggRes, found := searchResult.Aggregations.Terms(types.AggTerms)

	if !found {
		logger.Infof(ctx, fmt.Sprintf("Extract %s Abnormal Resource Distribution not found", types.QueryEventType))
		return &types.K8sEventStatistic{}, nil
	}
	for _, bucket := range aggRes.Buckets {
		result[(bucket.Key.(string))] = bucket.DocCount
	}
	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}
	return eventStatistic, nil
}

// GetK8SAbObjectDistribution - 异常节点/Pod对象分布
func (s *eventService) GetK8SAbObjectDistribution(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {
	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || boolQuery == nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Object Distribution from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	beego.Info(utils.Message(ctx, fmt.Sprintf("Get K8S Abnormal Object Distribution from Elastic Search")))
	var searchResult *elastic.SearchResult

	//构造ES查询语句
	if len(eventQueryArg.Reasons) == 0 {
		logger.Errorf(ctx, "Get K8S Abnormal Object Distribution Error: Reasons must have value.")
		return &types.K8sEventStatistic{}, nil
	}

	agg := elastic.NewFiltersAggregation()
	for _, term := range eventQueryArg.Reasons {
		agg = agg.FilterWithName(term, elastic.NewTermQuery(types.QueryEventReasons, term))
	}

	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"
	searchResult, err = client.GetElasticFiltersAgg(ctx, clusterIndexAlias, boolQuery, agg)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Get K8S Abnormal Object Distribution failed: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Get K8S Abnormal Object Distribution failed: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	result := make(map[string]int64)
	aggRes, found := searchResult.Aggregations.Filters(types.AggFilters)

	if !found {
		logger.Infof(ctx, fmt.Sprintf("Extract %s Abnormal Object Distribution not found", types.QueryResourceKind))
		return &types.K8sEventStatistic{}, nil
	}

	for _, reason := range eventQueryArg.Reasons {
		result[reason] = aggRes.NamedBuckets[reason].DocCount

	}

	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}
	return eventStatistic, nil
}

// GetK8SMountVolumeFailedCount - 获取挂载宗卷失败的数量
func (s *eventService) GetK8SMountVolumeFailedCount(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {
	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || boolQuery == nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Mount Volume Failed Count from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	beego.Info(utils.Message(ctx, fmt.Sprintf("Get K8S Mount Volume Failed Count from Elastic Search")))
	var searchResult int64

	//构造ES查询语句
	if len(eventQueryArg.Reasons) == 0 {
		logger.Infof(ctx, "Get K8S Mount Volume Failed Count Error: Reasons must have value.")

		return &types.K8sEventStatistic{}, nil
	}
	for _, term := range eventQueryArg.Reasons {
		boolQuery = boolQuery.Should(elastic.NewTermQuery(types.QueryEventReasons, term))
	}
	boolQuery = boolQuery.MinimumNumberShouldMatch(types.QueryMinimumNumberShouldMatch)

	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"
	searchResult, err = client.GetElasticCount(ctx, clusterIndexAlias, boolQuery)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Get K8S Mount Volume Failed Count failed: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Get K8S Mount Volume Failed Count failed: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	result := make(map[string]int64)

	result["mountVolumeFailedCount"] = searchResult
	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}
	return eventStatistic, nil
}

// GetK8SPullImageFailedCount - 获取拉镜像失败的数量
func (s *eventService) GetK8SPullImageFailedCount(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {
	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || boolQuery == nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Pull Image Failed Count from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	logger.Infof(ctx, "Get K8S Pull Image Failed Count from Elastic Search")
	var searchResult int64

	//构造ES查询语句
	if len(eventQueryArg.EventMessages) == 0 {
		logger.Errorf(ctx, "Get K8S Pull Image Failed Count Error: EventMessages must have value.")

		return &types.K8sEventStatistic{}, nil
	}
	innerBoolQuery := elastic.NewBoolQuery()
	for _, term := range eventQueryArg.EventMessages {
		innerBoolQuery = innerBoolQuery.Should(elastic.NewTermQuery(types.QueryRegexpMessage, term))
	}
	innerBoolQuery = innerBoolQuery.MinimumNumberShouldMatch(types.QueryMinimumNumberShouldMatch)

	for _, term := range eventQueryArg.Reasons {
		boolQuery = boolQuery.Must(elastic.NewTermQuery(types.QueryEventMessages, term))
	}
	boolQuery = boolQuery.Must(innerBoolQuery)

	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"
	searchResult, err = client.GetElasticCount(ctx, clusterIndexAlias, boolQuery)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Get K8S Pull Image Failed Count failed: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Get K8S Pull Image Failed Count failed: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	result := make(map[string]int64)

	result["pullImageFailedCount"] = searchResult
	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}
	return eventStatistic, nil
}

// GetK8SEventTypeTrend - 获取K8S Warning/Normal事件的趋势
func (s *eventService) GetK8SEventTypeTrend(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {
	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Event Trend from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	logger.Infof(ctx, fmt.Sprintf("Get K8S Event Trend from Elastic Search"))
	var searchResult *elastic.SearchResult

	if eventQueryArg.StartTime.IsZero() {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Event Trend from Elastic Search: StartTime is null"))
		return &types.K8sEventStatistic{}, fmt.Errorf("error happened when Get K8S Event Trend from Elastic Search: StartTime is null")
	}

	interval := "1m"
	if 3 <= eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() && eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() <= 6 {
		interval = "5m"
	} else if 6 < eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() && eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() <= 72 {
		interval = "1h"
	} else if 72 < eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() {
		interval = "1d"
	}
	dateHistogramAgg := elastic.NewDateHistogramAggregation().
		Field(types.QueryLastOccurrenceTimestamp).
		Interval(interval).
		MinDocCount(0).
		ExtendedBounds(eventQueryArg.StartTime.Format(time.RFC3339), eventQueryArg.EndTime.Format(time.RFC3339))
	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"

	result := make(map[string][]types.TrendResult)

	searchResult, err = client.GetElasticDateHistogramAgg(ctx, clusterIndexAlias, boolQuery, dateHistogramAgg)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Get K8S Event Trend Failed Count failed: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Get K8S Event Trend Failed Count failed: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	aggResult, found := searchResult.Aggregations.DateHistogram(types.AggTrends)
	if !found {
		logger.Infof(ctx, "DateHistogram not found")
	}
	for _, bucket := range aggResult.Buckets {
		result[eventQueryArg.EventType] = append(result[eventQueryArg.EventType],
			types.TrendResult{TimeSlice: *bucket.KeyAsString, Count: bucket.DocCount})
	}
	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}
	return eventStatistic, nil
}

// GetK8SAbEventTrend - 获取K8S 异常事件中不同异常原因的趋势
func (s *eventService) GetK8SAbEventTrend(ctx context.Context, eventQueryArg *types.EventQueryArg) (*types.K8sEventStatistic, error) {
	client, boolQuery, err := s.CheckESStatus(ctx, eventQueryArg)
	if err != nil || client == nil {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Event Trend Count from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	logger.Infof(ctx, fmt.Sprintf("Get K8S Abnormal Event Trend from Elastic Search"))
	var searchResult *elastic.SearchResult

	if eventQueryArg.StartTime.IsZero() {
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Event Trend from Elastic Search: StartTime is null"))
		return &types.K8sEventStatistic{}, fmt.Errorf("error happened when Get K8S Abnormal Event Trend from Elastic Search: StartTime is null")
	}

	interval := "1m"
	if 3 <= eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() && eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() <= 6 {
		interval = "5m"
	} else if 6 < eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() && eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() <= 72 {
		interval = "1h"
	} else if 72 < eventQueryArg.EndTime.Sub(eventQueryArg.StartTime).Hours() {
		interval = "1d"
	}
	dateHistogramAgg := elastic.NewDateHistogramAggregation().
		Field(types.QueryLastOccurrenceTimestamp).
		Interval(interval).
		MinDocCount(0).
		ExtendedBounds(eventQueryArg.StartTime.Format(time.RFC3339), eventQueryArg.EndTime.Format(time.RFC3339))

	agg := elastic.NewTermsAggregation().
		Field(types.QueryEventReasons).
		Size(100).
		SubAggregation(types.AggTrends, dateHistogramAgg)
	clusterIndexAlias := eventQueryArg.ClusterUuid + "-events"

	result := make(map[string][]types.TrendResult)

	searchResult, err = client.GetElasticTermsAgg(ctx, clusterIndexAlias, boolQuery, agg)
	if err != nil {
		if strings.Contains(err.Error(), "no such index") {
			logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Event Trend from Elastic Search: %s", err))
			return &types.K8sEventStatistic{}, nil
		}
		logger.Errorf(ctx, fmt.Sprintf("Error happened when Get K8S Abnormal Event Trend from Elastic Search: %s", err))
		return &types.K8sEventStatistic{}, err
	}
	termsAggResult, found := searchResult.Aggregations.Terms(types.AggTerms)
	if !found {
		logger.Infof(ctx, "Abnormal Event Trend DateHistogram not found")
		return &types.K8sEventStatistic{}, nil
	}

	for _, bucket := range termsAggResult.Buckets {
		trendAgg, foundHis := bucket.DateHistogram(types.AggTrends)
		if !foundHis {
			logger.Infof(ctx, fmt.Sprintf("Abnormal Event Trend DateHistogram not found: %s", bucket.Key.(string)))
			continue
		}
		for _, term := range trendAgg.Buckets {
			result[bucket.Key.(string)] = append(result[bucket.Key.(string)],
				types.TrendResult{TimeSlice: *term.KeyAsString, Count: term.DocCount})
		}
	}
	eventStatistic := &types.K8sEventStatistic{
		Result: result,
	}
	return eventStatistic, nil
}

// matchesKeywordFilter 检查事件是否匹配关键字过滤条件
// 支持大小写不敏感的模糊搜索
func matchesKeywordFilter(message, reason, keyword, keywordType string) bool {
	// 如果没有设置关键字或关键字类型，则不进行过滤
	if keyword == "" || keywordType == "" {
		return true
	}

	// 转换为小写进行大小写不敏感比较
	lowerKeyword := strings.ToLower(keyword)

	switch keywordType {
	case types.KeywordTypeMessage:
		return strings.Contains(strings.ToLower(message), lowerKeyword)
	case types.KeywordTypeReason:
		return strings.Contains(strings.ToLower(reason), lowerKeyword)
	default:
		// 如果 keywordType 不是支持的类型，则不进行过滤
		return true
	}
}
