package types

import (
	"strings"
	"testing"
	"time"
)

func TestK8SEvents_Len(t *testing.T) {
	tests := []struct {
		name string
		k    K8SEvents
		want int
	}{
		{
			name: "获取数组长度",
			k: K8SEvents{
				{
					Type:    "Normal",
					Reason:  "Pulling",
					Count:   1,
					Message: "pulling image nginx:v0",
				},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.k.Len(); got != tt.want {
				t.<PERSON>rrorf("K8SEvents.Len() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestK8SEvents_Less(t *testing.T) {
	type args struct {
		i int
		j int
	}

	todayZero, _ := time.ParseInLocation("2006-01-02", "2019-01-01 15:22:22", time.Local)
	tomorrowZero, _ := time.ParseInLocation("2006-01-02", "2019-01-02 15:22:22", time.Local)
	tests := []struct {
		name string
		k    K8SEvents
		args args
		want bool
	}{
		{
			name: "排序",
			k: K8SEvents{
				{
					Type:                    "Normal",
					Reason:                  "Pulling",
					Count:                   1,
					Message:                 "pulling image nginx:v0",
					LastOccurrenceTimestamp: todayZero,
				},
				{
					Type:                    "Normal",
					Reason:                  "Pulling",
					Count:                   2,
					Message:                 "pulling image nginx:v0",
					LastOccurrenceTimestamp: tomorrowZero,
				},
			},
			args: args{
				i: 0,
				j: 1,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.k.Less(tt.args.i, tt.args.j); got != tt.want {
				t.Errorf("K8SEvents.Less() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestK8SEvents_Swap(t *testing.T) {
	type args struct {
		i int
		j int
	}
	tests := []struct {
		name string
		k    K8SEvents
		args args
	}{
		{
			name: "替换",
			k: K8SEvents{
				{
					Type:    "Normal",
					Reason:  "Pulling",
					Count:   1,
					Message: "pulling image nginx:v0",
				},
				{
					Type:    "Normal",
					Reason:  "Pulling",
					Count:   2,
					Message: "pulling image nginx:v0",
				},
			},
			args: args{
				i: 0,
				j: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.k.Swap(tt.args.i, tt.args.j)
		})
	}
}

func TestEventQueryArg_Validate(t *testing.T) {
	tests := []struct {
		name    string
		arg     *EventQueryArg
		wantErr bool
		errMsg  string
	}{
		// 1. 测试必填字段验证
		{
			name: "缺少ClusterUuid",
			arg: &EventQueryArg{
				PageNo:   1,
				PageSize: 10,
			},
			wantErr: true,
			errMsg:  "clusterUuid is required",
		},
		{
			name: "ClusterUuid为空字符串",
			arg: &EventQueryArg{
				ClusterUuid: "",
				PageNo:      1,
				PageSize:    10,
			},
			wantErr: true,
			errMsg:  "clusterUuid is required",
		},
		{
			name: "ClusterUuid为空白字符",
			arg: &EventQueryArg{
				ClusterUuid: "   ",
				PageNo:      1,
				PageSize:    10,
			},
			wantErr: true,
			errMsg:  "clusterUuid is required",
		},

		// 2. 测试分页参数验证
		{
			name: "PageNo为0",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      0,
				PageSize:    10,
			},
			wantErr: true,
			errMsg:  "pageNo must be greater than 0, got: 0",
		},
		{
			name: "PageNo为负数",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      -1,
				PageSize:    10,
			},
			wantErr: true,
			errMsg:  "pageNo must be greater than 0, got: -1",
		},
		{
			name: "PageSize为0",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    0,
			},
			wantErr: true,
			errMsg:  "pageSize must be greater than 0, got: 0",
		},
		{
			name: "PageSize为负数",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    -1,
			},
			wantErr: true,
			errMsg:  "pageSize must be greater than 0, got: -1",
		},
		{
			name: "PageSize超过最大值",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    MaxPageSize + 1,
			},
			wantErr: true,
			errMsg:  "pageSize must not exceed 100, got: 101",
		},

		// 3. 测试时间范围验证
		{
			name: "StartTime晚于EndTime",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				StartTime:   time.Now(),
				EndTime:     time.Now().Add(-1 * time.Hour),
			},
			wantErr: true,
			errMsg:  "startTime",
		},
		{
			name: "时间范围超过30天",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				StartTime:   time.Now().Add(-31 * 24 * time.Hour),
				EndTime:     time.Now(),
			},
			wantErr: true,
			errMsg:  "time range cannot exceed 30 days",
		},
		{
			name: "只设置StartTime",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				StartTime:   time.Now().Add(-1 * time.Hour),
			},
			wantErr: false,
		},
		{
			name: "只设置EndTime",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				EndTime:     time.Now(),
			},
			wantErr: false,
		},

		// 4. 测试关键字搜索参数验证
		{
			name: "Keyword为空但KeywordType不为空",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     "",
				KeywordType: KeywordTypeMessage,
			},
			wantErr: false, // 根据代码逻辑，这种情况会清空KeywordType并返回nil
		},
		{
			name: "Keyword不为空但KeywordType为空",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     "test",
				KeywordType: "",
			},
			wantErr: true,
			errMsg:  "keywordType is required when keyword is set",
		},
		{
			name: "Keyword长度超过限制",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     strings.Repeat("a", MaxKeywordLen+1),
				KeywordType: KeywordTypeMessage,
			},
			wantErr: true,
			errMsg:  "keyword length must not exceed 200 characters, got: 201",
		},
		{
			name: "无效的KeywordType",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     "test",
				KeywordType: "invalid",
			},
			wantErr: true,
			errMsg:  "keywordType must be 'message' or 'reason', got: 'invalid'",
		},
		{
			name: "KeywordType大写转小写",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     "test",
				KeywordType: "MESSAGE",
			},
			wantErr: false,
		},
		{
			name: "同时使用keyword和message字段",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     "test",
				KeywordType: KeywordTypeMessage,
				Message:     "test message",
			},
			wantErr: true,
			errMsg:  "cannot use both 'message' field and 'keyword+keywordType' fields simultaneously",
		},

		// 5. 测试其他字段长度验证
		{
			name: "Namespace长度超过限制",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Namespace:   strings.Repeat("a", 254),
			},
			wantErr: true,
			errMsg:  "namespace length must not exceed 253 characters",
		},
		{
			name: "ResourceName长度超过限制",
			arg: &EventQueryArg{
				ClusterUuid:  "test-cluster",
				PageNo:       1,
				PageSize:     10,
				ResourceName: strings.Repeat("a", 254),
			},
			wantErr: true,
			errMsg:  "resourceName length must not exceed 253 characters",
		},

		// 6. 测试正常情况
		{
			name: "所有参数都正确",
			arg: &EventQueryArg{
				ClusterUuid:  "test-cluster",
				PageNo:       1,
				PageSize:     10,
				StartTime:    time.Now().Add(-1 * time.Hour),
				EndTime:      time.Now(),
				Keyword:      "test",
				KeywordType:  KeywordTypeMessage,
				Namespace:    "default",
				ResourceName: "test-pod",
				ResourceKind: "Pod",
				EventType:    "Warning",
			},
			wantErr: false,
		},
		{
			name: "最小有效参数",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    1,
			},
			wantErr: false,
		},
		{
			name: "最大PageSize",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    MaxPageSize,
			},
			wantErr: false,
		},
		{
			name: "最大Keyword长度",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Keyword:     strings.Repeat("a", MaxKeywordLen),
				KeywordType: KeywordTypeReason,
			},
			wantErr: false,
		},
		{
			name: "最大Namespace长度",
			arg: &EventQueryArg{
				ClusterUuid: "test-cluster",
				PageNo:      1,
				PageSize:    10,
				Namespace:   strings.Repeat("a", 253),
			},
			wantErr: false,
		},
		{
			name: "最大ResourceName长度",
			arg: &EventQueryArg{
				ClusterUuid:  "test-cluster",
				PageNo:       1,
				PageSize:     10,
				ResourceName: strings.Repeat("a", 253),
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.arg.Validate()

			if tt.wantErr {
				if err == nil {
					t.Errorf("EventQueryArg.Validate() error = nil, wantErr %v", tt.wantErr)
					return
				}
				if tt.errMsg != "" && !strings.Contains(err.Error(), tt.errMsg) {
					t.Errorf("EventQueryArg.Validate() error = %v, want error containing %v", err, tt.errMsg)
				}
			} else {
				if err != nil {
					t.Errorf("EventQueryArg.Validate() error = %v, wantErr %v", err, tt.wantErr)
				}
			}

			// 特殊验证：检查KeywordType是否被正确转换为小写
			if tt.arg.KeywordType == "MESSAGE" && err == nil {
				if tt.arg.KeywordType != KeywordTypeMessage {
					t.Errorf("KeywordType should be converted to lowercase, got %v", tt.arg.KeywordType)
				}
			}
		})
	}
}

func TestEventQueryArg_SetDefaults(t *testing.T) {
	arg := &EventQueryArg{
		ClusterUuid: "test-cluster",
		PageNo:      0,
		PageSize:    0,
	}

	arg.SetDefaults()

	if arg.PageNo != 1 {
		t.Errorf("SetDefaults() PageNo = %v, expected 1", arg.PageNo)
	}
	if arg.PageSize != DefaultPageSize {
		t.Errorf("SetDefaults() PageSize = %v, expected %v", arg.PageSize, DefaultPageSize)
	}
	if arg.EndTime.IsZero() {
		t.Errorf("SetDefaults() EndTime should be set to current time")
	}
}

func TestEventQueryArg_SetDefaults_maxPageSize(t *testing.T) {

	arg := &EventQueryArg{
		ClusterUuid: "test-cluster",
		PageNo:      0,
		PageSize:    200,
	}
	arg.SetDefaults()
	if arg.PageSize != MaxPageSize {
		t.Errorf("SetDefaults() PageSize = %v, expected %v", arg.PageSize, MaxPageSize)
	}
	if arg.PageNo != 1 {
		t.Errorf("SetDefaults() PageNo = %v, expected 1", arg.PageNo)
	}
}
