package types

import (
	"testing"
	"time"
)

func TestK8SEvents_Len(t *testing.T) {
	tests := []struct {
		name string
		k    K8SEvents
		want int
	}{
		{
			name: "获取数组长度",
			k: K8SEvents{
				{
					Type:    "Normal",
					Reason:  "Pulling",
					Count:   1,
					Message: "pulling image nginx:v0",
				},
			},
			want: 1,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.k.Len(); got != tt.want {
				t.<PERSON>("K8SEvents.Len() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestK8SEvents_Less(t *testing.T) {
	type args struct {
		i int
		j int
	}

	todayZero, _ := time.ParseInLocation("2006-01-02", "2019-01-01 15:22:22", time.Local)
	tomorrowZero, _ := time.ParseInLocation("2006-01-02", "2019-01-02 15:22:22", time.Local)
	tests := []struct {
		name string
		k    K8SEvents
		args args
		want bool
	}{
		{
			name: "排序",
			k: K8SEvents{
				{
					Type:                    "Normal",
					Reason:                  "Pulling",
					Count:                   1,
					Message:                 "pulling image nginx:v0",
					LastOccurrenceTimestamp: todayZero,
				},
				{
					Type:                    "Normal",
					Reason:                  "Pulling",
					Count:                   2,
					Message:                 "pulling image nginx:v0",
					LastOccurrenceTimestamp: tomorrowZero,
				},
			},
			args: args{
				i: 0,
				j: 1,
			},
			want: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.k.Less(tt.args.i, tt.args.j); got != tt.want {
				t.Errorf("K8SEvents.Less() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestK8SEvents_Swap(t *testing.T) {
	type args struct {
		i int
		j int
	}
	tests := []struct {
		name string
		k    K8SEvents
		args args
	}{
		{
			name: "替换",
			k: K8SEvents{
				{
					Type:    "Normal",
					Reason:  "Pulling",
					Count:   1,
					Message: "pulling image nginx:v0",
				},
				{
					Type:    "Normal",
					Reason:  "Pulling",
					Count:   2,
					Message: "pulling image nginx:v0",
				},
			},
			args: args{
				i: 0,
				j: 1,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			tt.k.Swap(tt.args.i, tt.args.j)
		})
	}
}
