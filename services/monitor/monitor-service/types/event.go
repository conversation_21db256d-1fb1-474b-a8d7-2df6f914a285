package types

import (
	"time"

	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	QueryLastOccurrenceTimestamp  = "LastOccurrenceTimestamp"
	QueryRegexpMessage            = "Message"
	QueryEventType                = "Type.keyword"
	QueryResourceKind             = "InvolvedObject.kind.keyword"
	QueryResourceName             = "InvolvedObject.name"
	QueryResourceNamespace        = "InvolvedObject.namespace"
	QueryEventReasons             = "Reason.keyword"
	QueryEventMessages            = "Reason.keyword"
	AggTerms                      = "terms"
	AggFilters                    = "filters"
	AggTrends                     = "trends"
	IndexRetryCount               = 30
	QueryMinimumNumberShouldMatch = 1
)

type EventQueryArg struct {
	StartTime     time.Time `json:"startTime,omitempty"`
	EndTime       time.Time `json:"endTime,omitempty"`
	ClusterUuid   string    `json:"clusterUuid"`
	Namespace     string    `json:"namespace,omitempty"`
	ResourceName  string    `json:"resourceName,omitempty"`
	ResourceKind  string    `json:"resourceKind,omitempty"`
	Message       string    `json:"message,omitempty"`
	EventType     string    `json:"eventType,omitempty"`
	PageNo        int       `json:"pageNo"`
	PageSize      int       `json:"pageSize"`
	Reasons       []string  `json:"reasons"`
	EventMessages []string  `json:"eventMessages"`
	OrderBy       string    `json:"orderBy,omitempty"`
	Order         string    `json:"order,omitempty"`
}

type K8sEvent struct {
	Count                    int32              `json:"count"`
	EventTags                EventTags          `json:"EventTags"`
	FirstOccurrenceTimestamp time.Time          `json:"FirstOccurrenceTimestamp"`
	InvolvedObject           v1.ObjectReference `json:"InvolvedObject"`
	LastOccurrenceTimestamp  time.Time          `json:"LastOccurrenceTimestamp"`
	Message                  string             `json:"Message"`
	Metadata                 metav1.ObjectMeta  `json:"Metadata"`
	Reason                   string             `json:"Reason"`
	Source                   v1.EventSource     `json:"Source"`
	Type                     string             `json:"Type"`
}

type EventTags struct {
	EventID   string `json:"eventID"`
	ClusterID string `json:"clusterID"`
}

type K8sEventResults struct {
	Result     []*K8sEvent `json:"result"`
	Order      string      `json:"order"`
	OrderBy    string      `json:"orderBy"`
	PageNo     int         `json:"pageNo"`
	PageSize   int         `json:"pageSize"`
	TotalCount int64       `json:"totalCount"`
}

type K8sEventStatistic struct {
	Result interface{} `json:"result"`
}

type TrendResult struct {
	TimeSlice string `json:"timeSlice"`
	Count     int64  `json:"docCount"`
}

// 定义K8sEvent数组类型，进行排序
type K8SEvents []*K8sEvent

func (k K8SEvents) Len() int { return len(k) }
func (k K8SEvents) Less(i, j int) bool {
	return k[i].LastOccurrenceTimestamp.String() > k[j].LastOccurrenceTimestamp.String()
}
func (k K8SEvents) Swap(i, j int) { k[i], k[j] = k[j], k[i] }
