package types

import (
	"fmt"
	"strings"
	"time"

	"k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	QueryLastOccurrenceTimestamp  = "LastOccurrenceTimestamp"
	QueryRegexpMessage            = "Message"
	QueryEventType                = "Type.keyword"
	QueryResourceKind             = "InvolvedObject.kind.keyword"
	QueryResourceName             = "InvolvedObject.name"
	QueryResourceNamespace        = "InvolvedObject.namespace"
	QueryEventReasons             = "Reason.keyword"
	QueryEventReason              = "Reason"
	QueryEventMessages            = "Reason.keyword"
	AggTerms                      = "terms"
	AggFilters                    = "filters"
	AggTrends                     = "trends"
	IndexRetryCount               = 30
	QueryMinimumNumberShouldMatch = 1

	// KeywordType 常量定义
	KeywordTypeMessage = "message"
	KeywordTypeReason  = "reason"

	// 验证常量
	MaxPageSize     = 100 // 最大页面大小
	DefaultPageSize = 10  // 默认页面大小
	MaxKeywordLen   = 200 // 最大关键字长度
)

type EventQueryArg struct {
	StartTime     time.Time `json:"startTime,omitempty"`
	EndTime       time.Time `json:"endTime,omitempty"`
	ClusterUuid   string    `json:"clusterUuid"`
	Namespace     string    `json:"namespace,omitempty"`
	ResourceName  string    `json:"resourceName,omitempty"`
	ResourceKind  string    `json:"resourceKind,omitempty"`
	Message       string    `json:"message,omitempty"`
	EventType     string    `json:"eventType,omitempty"`
	PageNo        int       `json:"pageNo"`
	PageSize      int       `json:"pageSize"`
	Reasons       []string  `json:"reasons"`
	EventMessages []string  `json:"eventMessages"`
	OrderBy       string    `json:"orderBy,omitempty"`
	Order         string    `json:"order,omitempty"`
	Keyword       string    `json:"keyword,omitempty"`     // 关键字搜索
	KeywordType   string    `json:"keywordType,omitempty"` // 关键字类型：message 或 reason
}

type K8sEvent struct {
	Count                    int32              `json:"count"`
	EventTags                EventTags          `json:"EventTags"`
	FirstOccurrenceTimestamp time.Time          `json:"FirstOccurrenceTimestamp"`
	InvolvedObject           v1.ObjectReference `json:"InvolvedObject"`
	LastOccurrenceTimestamp  time.Time          `json:"LastOccurrenceTimestamp"`
	Message                  string             `json:"Message"`
	Metadata                 metav1.ObjectMeta  `json:"Metadata"`
	Reason                   string             `json:"Reason"`
	Source                   v1.EventSource     `json:"Source"`
	Type                     string             `json:"Type"`
}

type EventTags struct {
	EventID   string `json:"eventID"`
	ClusterID string `json:"clusterID"`
}

type K8sEventResults struct {
	Result     []*K8sEvent `json:"result"`
	Order      string      `json:"order"`
	OrderBy    string      `json:"orderBy"`
	PageNo     int         `json:"pageNo"`
	PageSize   int         `json:"pageSize"`
	TotalCount int64       `json:"totalCount"`
}

type K8sEventStatistic struct {
	Result interface{} `json:"result"`
}

type TrendResult struct {
	TimeSlice string `json:"timeSlice"`
	Count     int64  `json:"docCount"`
}

// 定义K8sEvent数组类型，进行排序
type K8SEvents []*K8sEvent

func (k K8SEvents) Len() int { return len(k) }
func (k K8SEvents) Less(i, j int) bool {
	return k[i].LastOccurrenceTimestamp.String() > k[j].LastOccurrenceTimestamp.String()
}
func (k K8SEvents) Swap(i, j int) { k[i], k[j] = k[j], k[i] }

// Validate 验证 EventQueryArg 参数的合法性
func (e *EventQueryArg) Validate() error {
	// 1. 验证必填字段
	if strings.TrimSpace(e.ClusterUuid) == "" {
		return fmt.Errorf("clusterUuid is required")
	}

	// 2. 验证分页参数
	if e.PageNo <= 0 {
		return fmt.Errorf("pageNo must be greater than 0, got: %d", e.PageNo)
	}
	if e.PageSize <= 0 {
		return fmt.Errorf("pageSize must be greater than 0, got: %d", e.PageSize)
	}
	if e.PageSize > MaxPageSize {
		return fmt.Errorf("pageSize must not exceed %d, got: %d", MaxPageSize, e.PageSize)
	}

	// 3. 验证时间范围
	if !e.StartTime.IsZero() && !e.EndTime.IsZero() {
		if e.StartTime.After(e.EndTime) {
			return fmt.Errorf("startTime (%v) must be before endTime (%v)", e.StartTime, e.EndTime)
		}
		// 检查时间范围是否过大（比如超过30天）
		if e.EndTime.Sub(e.StartTime) > 30*24*time.Hour {
			return fmt.Errorf("time range cannot exceed 30 days")
		}
	}

	// 4. 验证关键字搜索参数
	if e.Keyword != "" || e.KeywordType != "" {
		// 如果设置了关键字或关键字类型，两者都必须设置
		if e.Keyword == "" {
			e.KeywordType = ""
			return nil
		}
		if e.KeywordType == "" {
			return fmt.Errorf("keywordType is required when keyword is set")
		}

		// 验证关键字长度
		if len(e.Keyword) > MaxKeywordLen {
			return fmt.Errorf("keyword length must not exceed %d characters, got: %d", MaxKeywordLen, len(e.Keyword))
		}

		// 验证关键字类型
		e.KeywordType = strings.ToLower(e.KeywordType)
		if e.KeywordType != KeywordTypeMessage && e.KeywordType != KeywordTypeReason {
			return fmt.Errorf("keywordType must be '%s' or '%s', got: '%s'",
				KeywordTypeMessage, KeywordTypeReason, e.KeywordType)
		}

		// 5. 验证字段冲突：新的 keyword 搜索与旧的 message 字段不能同时使用
		if e.Message != "" {
			return fmt.Errorf("cannot use both 'message' field and 'keyword+keywordType' fields simultaneously, please use keyword search instead")
		}
	}

	// 6. 验证其他字符串字段长度
	if len(e.Namespace) > 253 { // Kubernetes namespace 最大长度
		return fmt.Errorf("namespace length must not exceed 253 characters")
	}
	if len(e.ResourceName) > 253 { // Kubernetes resource name 最大长度
		return fmt.Errorf("resourceName length must not exceed 253 characters")
	}

	return nil
}

// SetDefaults 设置默认值
func (e *EventQueryArg) SetDefaults() {
	if e.PageNo <= 0 {
		e.PageNo = 1
	}
	if e.PageSize <= 0 {
		e.PageSize = DefaultPageSize
	}
	if e.PageSize > MaxPageSize {
		e.PageSize = MaxPageSize
	}
	if e.EndTime.IsZero() {
		e.EndTime = time.Now()
	}
}
