package controllers

import (
	"encoding/json"
	"strings"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/errorcode"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	stackmodels "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/models"
	stackutils "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/utils"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/services"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/monitor-service/utils"
)

// EventController definition
type EventController struct {
	BaseController

	eventService services.EventServiceInterface
}

// Prepare - 初始化 eventService
func (c *EventController) Prepare() {
	c.BaseController.Prepare()

	ctx := c.ctx

	// 初始化 eventService
	eventService, err := services.NewEventService(ctx)
	if err != nil {
		logger.Errorf(ctx, "NewEventService failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "new event-service failed")
	}

	c.eventService = eventService
}

// GetK8SEvent - 获取集群 Events
// @Title GetK8SEvent
// @Description get k8s event from elastic or k8s
// @Success 200 {obj} types.K8sEventResults
// @router / [post]
func (c *EventController) GetK8SEvent() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "GetK8SEvent params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SEvent(c.ctx, eventQueryArg)
	if err != nil {
		c.TimeoutCheck(err)
		logger.Errorf(c.ctx, "GetK8SEvent failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "GetK8SEvent failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// StartK8SEventCollection - 启动集群事件采集
// @Title StartK8SEventCollection
// @Description start collect k8s event
// @Success 200 {boolean} if opened
// @router /open [put]
func (c *EventController) StartK8SEventCollection() {
	clusterID := c.BaseController.clusterID
	if clusterID == "" {
		c.errorHandler(utils.NewInternalServerError(), "clusterID is empty")
	}

	err := c.eventService.StartK8SEventCollection(c.ctx, clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "StartK8SEventCollection failed: %s", err)
		if stackmodels.IsNotExist(err) {
			c.errorHandler(utils.NewNoSuchObject(), "StartK8SEventCollection failed")
		}
		c.errorHandler(utils.NewInternalServerError(), "StartK8SEventCollection failed")
	}

	c.Data["json"] = true
	c.ServeJSON()
}

// CloseK8SEventCollection - 关闭集群采集
// @Title CloseK8SEventCollection
// @Description close collect k8s event
// @Success 200 {boolean} if closed
// @router /close [put]
func (c *EventController) CloseK8SEventCollection() {
	clusterID := c.BaseController.clusterID
	if clusterID == "" {
		c.errorHandlerV2(errorcode.NewInvalidParam(), errorcode.LevelByAdmin, "clusterID is empty")
	}

	if err := c.eventService.UpdateK8SEventCollectionStatus(c.ctx, clusterID, stackmodels.EventClusterStatusStopped); err != nil {
		logger.Errorf(c.ctx, "UpdateK8SEventCollectionStatus failed: %s", err)
		c.errorHandlerV2(errorcode.NewInternalServerError(), errorcode.LevelByAdmin, "")
	}

	c.Data["json"] = true
	c.ServeJSON()
}

// GetK8SEventCollectionStatus - 获取集群采集状态
// @Title GetK8SEventCollectionStatus
// @Description get collect k8s event status
// @Success 200 {string} k8s event status
// @router /status [get]
func (c *EventController) GetK8SEventCollectionStatus() {
	clusterID := c.BaseController.clusterID
	if clusterID == "" {
		c.errorHandler(utils.NewInternalServerError(), "clusterID is empty")
	}

	status, err := c.eventService.GetK8SEventCollectionStatus(c.ctx, clusterID)
	if err != nil {
		logger.Errorf(c.ctx, "GetK8SEventCollectionStatus failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "GetK8SEventCollectionStatus failed")
	}

	c.Data["json"] = status
	c.ServeJSON()
}

// GetK8SEventDistribution - 获取事件分布
// @Title GetK8SEventDistribution
// @Description get event distribution
// @Success 200 {string} K8S Event Distribution
// @router /event_distribution [post]
func (c *EventController) GetK8SEventDistribution() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Event Distribution params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SEventDistribution(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "Get K8S Event Distribution failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Event Distribution failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// GetK8SAbResourceDistribution - 异常资源对象分布
// @Title GetK8SAbResourceDistribution
// @Description get abnormal resource distribution
// @Success 200 {string} K8S Abnormal Resource Distribution
// @router /abnormal_resource [post]
func (c *EventController) GetK8SAbResourceDistribution() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Abnormal Resource Distribution Params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SAbResourceDistribution(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "K8S Abnormal Resource Distribution failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Abnormal Resource Distribution failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// GetK8SAbObjectDistribution - 异常节点/Pod对象分布
// @Title GetK8SAbObjectDistribution
// @Description get K8S abnormal object distribution
// @Success 200 {string} K8S Abnormal Object Distribution
// @router /abnormal_object [post]
func (c *EventController) GetK8SAbObjectDistribution() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Abnormal Object Distribution params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SAbObjectDistribution(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "Get K8S Abnormal Object Distribution failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Abnormal Object Distribution failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// GetK8SMountVolumeFailedCount - 获取挂载宗卷失败的数量
// @Title GetK8SMountVolumeFailedCount
// @Description get K8S mount volume failed count
// @Success 200 {string} K8S Mount Volume Failed Count
// @router /failed_volume [post]
func (c *EventController) GetK8SMountVolumeFailedCount() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Mount Volume Failed Count params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SMountVolumeFailedCount(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "Get K8S Mount Volume Failed Count failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Mount Volume Failed Count: failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// GetK8SPullImageFailedCount - 获取 K8S Warning/Normal 事件的趋势
// @Title GetK8SPullImageFailedCount
// @Description get K8S pull image failed count
// @Success 200 {string} K8S Pull Image Failed Count
// @router /failed_image [post]
func (c *EventController) GetK8SPullImageFailedCount() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Pull Image Failed Count params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SPullImageFailedCount(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "Get K8S Pull Image Failed Count failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Pull Image Failed Count failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// GetK8SEventTypeTrend - 获取 K8S 异常事件中不同异常原因的趋势
// @Title GetK8SEventTypeTrend
// @Description get K8S event type trend
// @Success 200 {string} K8S Event Type Trend
// @router /event_trend [post]
func (c *EventController) GetK8SEventTypeTrend() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Event Type Trend params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SEventTypeTrend(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "Get K8S Event Type Trend failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Event Type Trend failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}

// GetK8SAbEventTrend - 获取集群采集状态
// @Title GetK8SAbEventTrend
// @Description get K8S abnormal event reasons trend
// @Success 200 {string} K8S Abnormal Event Reasons Trend
// @router /abnormal_trend [post]
func (c *EventController) GetK8SAbEventTrend() {
	eventQueryArg := new(types.EventQueryArg)
	err := json.Unmarshal(c.Ctx.Input.RequestBody, eventQueryArg)

	logger.Infof(c.ctx, "Get K8S Abnormal Event Trend params: %s", stackutils.ToJSON(eventQueryArg))

	eventsRes, err := c.eventService.GetK8SAbEventTrend(c.ctx, eventQueryArg)
	if err != nil {
		logger.Errorf(c.ctx, "Get K8S Abnormal Event Reasons Trend failed: %s", err)
		c.errorHandler(utils.NewInternalServerError(), "Get K8S Abnormal Event Trend failed")
	}

	c.Data["json"] = eventsRes
	c.ServeJSON()
}
func (c *EventController) TimeoutCheck(err error) {
	if strings.Contains(err.Error(), "Client.Timeout exceeded while awaiting headers") {
		c.errorHandlerV2(errorcode.NewClientNotSupported(), errorcode.LevelByAdmin, "Timeout while awaiting APIServer response")
	}
	if strings.Contains(err.Error(), "connect: connection refused") {
		c.errorHandlerV2(errorcode.NewClientNotSupported(), errorcode.LevelByAdmin, "APIServer connect: connection refused")
	}
}
