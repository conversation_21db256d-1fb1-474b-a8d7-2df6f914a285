# Kubernetes 事件关键字搜索功能

## 概述

本文档描述了 `GetK8SEvent` 方法新增的关键字搜索功能，支持在 Kubernetes 事件的 Message 和 Reason 字段中进行大小写不敏感的模糊搜索。

## 功能特性

### 1. 新增字段

在 `EventQueryArg` 结构体中新增了两个字段：

- `Keyword` (string): 搜索关键字
- `KeywordType` (string): 关键字类型，支持以下值：
  - `"message"`: 在事件的 Message 字段中搜索
  - `"reason"`: 在事件的 Reason 字段中搜索

### 2. 搜索特性

- **模糊搜索**: 支持部分匹配，无需完全匹配整个字段内容
- **大小写不敏感**: 搜索时忽略大小写差异
- **双数据源支持**: 同时支持从 Elasticsearch 和 Kubernetes API 获取数据时的关键字过滤

## 实现细节

### 1. 常量定义

```go
const (
    KeywordTypeMessage = "message"
    KeywordTypeReason  = "reason"
)
```

### 2. 结构体更新

```go
type EventQueryArg struct {
    // ... 其他字段
    Keyword     string `json:"keyword,omitempty"`     // 关键字搜索
    KeywordType string `json:"keywordType,omitempty"` // 关键字类型：message 或 reason
}
```

### 3. Elasticsearch 查询

对于从 Elasticsearch 获取数据的场景，使用优化的查询方式实现高性能的模糊搜索：

```go
// 在 Message 字段中搜索 - 使用 match 查询（性能优化）
// Message 字段是 analyzed 的，match 查询原生支持大小写不敏感和分词
boolQuery = boolQuery.Must(elastic.NewMatchQuery(types.QueryRegexpMessage, eventQueryArg.Keyword))

// 在 Reason 字段中搜索 - 使用 regexp 查询（大小写不敏感修复）
// Reason 字段是 not_analyzed 的，使用正则表达式实现大小写不敏感搜索
escapedKeyword := regexp.QuoteMeta(eventQueryArg.Keyword)
regexPattern := "(?i).*" + escapedKeyword + ".*"
boolQuery = boolQuery.Must(elastic.NewRegexpQuery(types.QueryEventReasons, regexPattern))
```

### 4. Kubernetes API 查询

对于从 Kubernetes API 获取数据的场景，在客户端进行过滤：

```go
func matchesKeywordFilter(message, reason, keyword, keywordType string) bool {
    if keyword == "" || keywordType == "" {
        return true
    }

    lowerKeyword := strings.ToLower(keyword)

    switch keywordType {
    case types.KeywordTypeMessage:
        return strings.Contains(strings.ToLower(message), lowerKeyword)
    case types.KeywordTypeReason:
        return strings.Contains(strings.ToLower(reason), lowerKeyword)
    default:
        return true
    }
}
```

## 使用示例

### 1. 基本用法

```go
// 在 Message 字段中搜索包含 "pull" 的事件
query := &types.EventQueryArg{
    ClusterUuid: "your-cluster-id",
    StartTime:   time.Now().Add(-24 * time.Hour),
    EndTime:     time.Now(),
    PageNo:      1,
    PageSize:    10,
    Keyword:     "pull",
    KeywordType: types.KeywordTypeMessage,
}

result, err := eventService.GetK8SEvent(ctx, query)
```

### 2. 复合查询

```go
// 结合其他过滤条件
query := &types.EventQueryArg{
    ClusterUuid:  "your-cluster-id",
    Namespace:    "default",
    EventType:    "Warning",
    ResourceKind: "Pod",
    Keyword:      "image",
    KeywordType:  types.KeywordTypeMessage,
    PageNo:       1,
    PageSize:     10,
}
```

### 3. 大小写不敏感搜索

```go
// 使用大写关键字搜索小写内容
query := &types.EventQueryArg{
    ClusterUuid: "your-cluster-id",
    Keyword:     "IMAGEPULLBACKOFF",  // 大写
    KeywordType: types.KeywordTypeReason,
    PageNo:      1,
    PageSize:    10,
}
// 能够匹配 reason 为 "ImagePullBackOff" 的事件
```

## API 请求示例

### HTTP 请求体

```json
{
    "clusterUuid": "your-cluster-id",
    "startTime": "2024-01-01T00:00:00Z",
    "endTime": "2024-01-02T00:00:00Z",
    "pageNo": 1,
    "pageSize": 10,
    "keyword": "pull",
    "keywordType": "message"
}
```

### 响应示例

```json
{
    "result": [
        {
            "Message": "Failed to pull image 'nginx:latest'",
            "Reason": "ImagePullBackOff",
            "Type": "Warning",
            "LastOccurrenceTimestamp": "2024-01-01T12:00:00Z"
        }
    ],
    "pageNo": 1,
    "pageSize": 10,
    "totalCount": 1
}
```

## 参数验证

### 新增的验证规则

1. **必填字段验证**：
   - `clusterUuid` 不能为空

2. **分页参数验证**：
   - `pageNo` 必须大于 0
   - `pageSize` 必须大于 0 且不超过 1000

3. **时间范围验证**：
   - `startTime` 不能晚于 `endTime`
   - 时间范围不能超过 30 天

4. **关键字搜索验证**：
   - 如果设置了 `keyword` 或 `keywordType`，两者都必须设置
   - `keywordType` 只能是 `"message"` 或 `"reason"`
   - `keyword` 长度不能超过 500 字符
   - 不能同时使用旧的 `message` 字段和新的 `keyword+keywordType` 字段

5. **字符串长度验证**：
   - `namespace` 长度不能超过 253 字符
   - `resourceName` 长度不能超过 253 字符

### 使用验证

```go
// 在 controller 中的使用
eventQueryArg.SetDefaults()  // 设置默认值
if err := eventQueryArg.Validate(); err != nil {
    // 处理验证错误
    return err
}
```

## 注意事项

1. **性能优化**:
   - Message 字段使用 `match` 查询，性能优于 `wildcard` 查询
   - Reason 字段使用 `regexp` 查询实现大小写不敏感，避免了客户端字符串转换
   - 建议结合时间范围等其他过滤条件使用以进一步提高性能
2. **字段冲突**: 新的 `keyword+keywordType` 搜索与旧的 `message` 字段互斥，不能同时使用
3. **参数验证**: 所有参数都会在 controller 层进行严格验证，确保数据安全性
4. **数据源兼容**: 功能同时支持 Elasticsearch 和 Kubernetes API 两种数据源
5. **默认值处理**: 系统会自动设置合理的默认值（如 pageSize=10, pageNo=1）
6. **大小写不敏感**:
   - Message 字段通过 Elasticsearch 的 analyzed 特性原生支持
   - Reason 字段通过正则表达式的 `(?i)` 标志实现大小写不敏感

## 测试

项目包含完整的单元测试，覆盖以下场景：
- 关键字匹配逻辑测试
- 参数验证测试
- 默认值设置测试
- 边界条件测试

运行测试：
```bash
go test -v ./services/monitor/monitor-service/services/ -run "TestEventQueryArg|TestMatchesKeywordFilter"
```
