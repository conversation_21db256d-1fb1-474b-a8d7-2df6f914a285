package v1

import metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

const (
	BLSDstType DstType = "BLS"

	// BESDstType BES传输任务
	BESDstType DstType = "BES"

	// JournaldTaskType journald service 类型 task
	JournaldTaskType TaskType = "journald"

	// HostSrcType 主机 src 类型
	HostSrcType SrcType = "host"
	// ContainerSrcType 容器 src 类型
	ContainerSrcType SrcType = "container"

	// StdoutLogType 容器 stdout 输出日志
	StdoutLogType ContainerLogType = "stdout"
	// InternalLogType 容器内部文件日志
	InternalLogType ContainerLogType = "internal"

	// SystemTime 日志使用系统时间
	SystemTime LogTimeType = "system"
	// LogTime 日志解析到的时间
	LogTime LogTimeType = "logTime"

	// NoneProcessType 不解析
	NoneProcessType ProcessType = "none"
	// JsonProcessType json 格式解析
	JsonProcessType ProcessType = "json"
	// SeparatorProcessType 分隔符解析
	SeparatorProcessType ProcessType = "separator"
	// RegexProcessType 正则解析
	RegexProcessType ProcessType = "regex"

	AutoDeleteLogStoreAnnotation = "cce.baidubce.com/auto-delete-logstore"
)

type DstType string

type SrcType string

type TaskType string

type ContainerLogType string

type LogTimeType string

type ProcessType string

// LogConfig
//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:resource:scope=Namespaced,shortName=lgc
//+kubebuilder:object:root=true
//+kubebuilder:printcolumn:name="TYPE",type=string,JSONPath=`.spec.srcConfig.srcType`
//+kubebuilder:printcolumn:name="STORAGE",type=string,JSONPath=`.spec.dstConfig.dstType`
//+kubebuilder:printcolumn:name="PHASE",type=string,JSONPath=`.status.phase`
//+kubebuilder:printcolumn:name="Age",type="date",JSONPath=".metadata.creationTimestamp"

type LogConfig struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   LogConfigSpec   `json:"spec,omitempty"`
	Status LogConfigStatus `json:"status,omitempty"`
}

// LogConfigList
// +kubebuilder:object:root=true
type LogConfigList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata"`

	Items []LogConfig `json:"items"`
}

type LogConfigSpec struct {
	SrcConfig SrcConfig `json:"srcConfig"`
	DstConfig DstConfig `json:"dstConfig"`
}

type SrcConfig struct {
	SrcType SrcType `json:"srcType"`

	// TaskType 目前只有 journald service 类型的 task 填该字段
	// 可选值 journald
	// +optional
	TaskType TaskType `json:"taskType,omitempty"`

	// LogType 只有 SrcType 字段为 container 时，LogType 才有用
	// 可选值 stdout，internal，分别表示标准输出日志和容器内部日志
	// +optional
	LogType ContainerLogType `json:"logType,omitempty"`

	// SrcDir 日志采集目录，注意是目录，当 SrcType=Container, LogType=stdout 时
	// 不需要指定 SrcDir
	// +optional
	SrcDir string `json:"srcDir,omitempty"`
	// MatchPattern SrcDir 下日志文件匹配规则
	// +optional
	MatchPattern string `json:"matchPattern,omitempty"`
	// MetaEnv 自定义环境变量，在日志集日志中以@tag_元数据形式展示
	// +optional
	MetaEnv []string `json:"metaEnv,omitempty"`
	// MetaLabel 自定义标签，在日志集日志中以@tag_元数据形式展示
	// +optional
	MetaLabel []string `json:"metaLabel,omitempty"`
	// IgnorePattern SrcDir 下日志文件就忽略规则
	// +optional
	IgnorePattern string `json:"ignorePattern,omitempty"`
	// TimeFormat 用于投 BOS 时，原文件路径日期解析
	// +optional
	TimeFormat string `json:"timeFormat,omitempty"`

	// TTL agent 采集日志时间范围
	TTL int `json:"ttl"`
	// UseMultiline 是否启用多行模式
	// +optional
	UseMultiline bool `json:"useMultiline,omitempty"`
	// MultilineRegex 多行模式首行模式
	// +optional
	MultilineRegex string `json:"multilineRegex,omitempty"`
	// RecursiveDir 是否递归采集 SrcDir 下满足 MatchPattern 的所有文件，包括子目录
	RecursiveDir bool `json:"recursiveDir,omitempty"`

	// LogTime 日志时间，可选system, logTime, 分别表示使用系统时间和使用日志时间
	// +optional
	LogTime LogTimeType `json:"logTime,omitempty"`
	// TimestampKey 指定解析后的字段作为日志时间
	// +optional
	TimestampKey string `json:"timestampKey,omitempty"`
	// DateFormat 指定时间戳字段的时间解析格式，format格式参考https://docs.oracle.com/javase/7/docs/api/java/text/SimpleDateFormat.html
	// "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'Z"
	// +optional
	DateFormat string `json:"dateFormat,omitempty"`
	// FilterExpr 日志匹配表达式，符合规则的日志，将被采集
	FilterExpr string `json:"filterExpr,omitempty"`
	// ProcessType 解析类型
	// +optional
	ProcessType ProcessType `json:"processType,omitempty"`
	// ProcessConfig 对应 ProcessType 的 process 参数
	// +optional
	ProcessConfig ProcessConfig `json:"processConfig,omitempty"`
	// MatchLabels 容器container 标签匹配 labels，这里的 label 对应 docker inspect 上的 label, 不是 pod 上的 label
	// +optional
	MatchLabels []KVPair `json:"matchLabels,omitempty"`
	// IgnoreLabels 容器container 标签忽略 labels，具体同上
	// +optional
	IgnoreLabels []KVPair `json:"ignoreLabels,omitempty"`
	// MatchPodLabels Pod标签匹配
	// +optional
	MatchPodLabels []KVPair `json:"matchPodLabels,omitempty"`
	// IgnorePodLabels Pod标签忽略
	// +optional
	IgnorePodLabels []KVPair `json:"ignorePodLabels,omitempty"`
	// MatchEnvs 容器环境匹配的 env
	// +optional
	MatchEnvs []KVPair `json:"matchEnvs,omitempty"`
	// MatchEnvs 容器环境忽略匹配的 env
	// +optional
	IgnoreEnvs []KVPair `json:"ignoreEnvs,omitempty"`
	// Units journald service
	// +optional
	Units []string `json:"units,omitempty"`
	// CloseInactive 指定关闭非活跃文件句柄的时间
	// +optional
	CloseInactive string `json:"closeInactive,omitempty"`
	// HarvesterLimit 日志采集器数量限制，默认为 1
	// +optional
	HarvesterLimit int `json:"harvesterLimit,omitempty"`
}

type KVPair struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type ProcessConfig struct {
	// Regex 正则规则
	// +optional
	Regex string `json:"regex,omitempty"`
	// Separator 分隔符
	// +optional
	Separator string `json:"separator,omitempty"`
	// Quote 分隔符场景可指定引用符，可选值包括：空，双引号"，单引号'和自定义
	// +optional
	Quote string `json:"quote,omitempty"`
	// SampleLog 解析日志样例, 解析后用于在 console 配置 keys 与 dataType
	// +optional
	SampleLog string `json:"sampleLog,omitempty"`
	// Keys 解析结果的列名；@message 为系统保留字，不允许设置为 key
	// +optional
	Keys string `json:"keys,omitempty"`
	// DataType 解析结果每列对应的数据类型，支持string/int/float/bool, 必须要与 Keys 一一对应
	// +optional
	DataType string `json:"dataType,omitempty"`
	// DiscardOnFailure 日志解析失败是否丢弃 true: 丢弃 false: 返回原值 （投递 BES 需要解析日志内容并以 JSON 格式投递）
	// +optional
	DiscardOnFailure bool `json:"discardOnFailure,omitempty"`
	// KeepOriginal 是否保留原日志 true: 保留原日志到 kafka 中的 @message 字段，bls 日志集中的 @raw 字段  false: 解析成功则不保留原日志
	// +optional
	KeepOriginal bool `json:"KeepOriginal,omitempty"`
}

type DstConfig struct {
	// DstType -
	DstType DstType `json:"dstType"`

	// LogProject 日志组名称，默认为 "default"
	// +optional
	LogProject string `json:"logProject,omitempty"`

	// LogStore
	// +kubebuilder:validation:MaxLength=64
	LogStore string `json:"logStore,omitempty"`

	// AccountID account id
	// +optional
	AccountID string `json:"accountID,omitempty"`

	// Retention retention
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:validation:Maximum=90
	Retention int `json:"retention,omitempty"`

	// RateLimit 单位为 MB
	// +kubebuilder:validation:Minimum=1
	// +kubebuilder:validation:Maximum=100
	RateLimit int `json:"rateLimit"`

	// bes clusterId
	BESClusterID string `json:"besClusterID,omitempty"`

	// bes user
	BESUser string `json:"besUser,omitempty"`

	// bes password
	BESPasswd string `json:"besPasswd,omitempty"`

	// bes index prefix
	BESIndexPrefix string `json:"besIndexPrefix,omitempty"`

	// 是否按设定频率生成新的 index, 可选值为 none:不滚动生成 index; day:每天0时生成; week:每周一0时生成; month:每月1号0时生成
	BESIndexRolling string `json:"besIndexRolling,omitempty"`

	// bes 是否启用密码
	BESIsPwChange bool `json:"besIsPwChange,omitempty"`
}

type LogConfigPhase string

const (
	PhaseCreating    LogConfigPhase = "Creating"
	PhasePending     LogConfigPhase = "Pending"
	PhaseTerminating LogConfigPhase = "Terminating"
	PhaseFailed      LogConfigPhase = "Failed"
	Phasesucceeded   LogConfigPhase = "Succeeded"
)

type Agent struct {
	// +optional
	HostID string `json:"hostId,omitempty"`
	// +optional
	HostName string `json:"hostName,omitempty"`
	// +optional
	IP string `json:"ip,omitempty"`
	// +optional
	IsAvailableForNewConfig bool `json:"isAvailableForNewConfig,omitempty"`
	// +optional
	Status string `json:"status,omitempty"`
	// +optional
	UpdatedTime string `json:"updatedTime,omitempty"`
}

type LogConfigStatus struct {
	// +optional
	Phase LogConfigPhase `json:"phase"`
	// +optional
	Ready bool `json:"ready" yaml:"ready"`
	// +optional
	Message string `json:"message,omitempty"`
	// +optional
	LastProbeTime metav1.Time `json:"lastProbeTime,omitempty" protobuf:"bytes,3,opt,name=lastProbeTime" hash:"ignore"`
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty" protobuf:"bytes,4,opt,name=lastTransitionTime" hash:"ignore"`
	// TaskID bls 创建 task 成功后返回的 id
	// +optional
	TaskID string `json:"taskID,omitempty"`
	// Hosts 当前 task 已绑定的 agent 列表
	// +optional
	Hosts []Agent `json:"hosts,omitempty"`

	// LastApplyHostHash
	// +optional
	LastApplyHostHash string `json:"lastApplyHostHash,omitempty"`

	// LastApplyHash
	// +optional
	LastApplyHash string `json:"lastApplyHash,omitempty"`

	// LastApplySpecHash
	// +optional
	LastApplySpecHash string `json:"lastApplySpecHash,omitempty"`
}
