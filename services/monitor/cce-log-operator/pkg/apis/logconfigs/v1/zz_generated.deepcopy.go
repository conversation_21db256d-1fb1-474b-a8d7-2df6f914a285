//go:build !ignore_autogenerated

// Code generated by controller-gen. DO NOT EDIT.

package v1

import (
	"k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Agent) DeepCopyInto(out *Agent) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Agent.
func (in *Agent) DeepCopy() *Agent {
	if in == nil {
		return nil
	}
	out := new(Agent)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DstConfig) DeepCopyInto(out *DstConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DstConfig.
func (in *DstConfig) DeepCopy() *DstConfig {
	if in == nil {
		return nil
	}
	out := new(DstConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *KVPair) DeepCopyInto(out *KVPair) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new KVPair.
func (in *KVPair) DeepCopy() *KVPair {
	if in == nil {
		return nil
	}
	out := new(KVPair)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LogConfig) DeepCopyInto(out *LogConfig) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LogConfig.
func (in *LogConfig) DeepCopy() *LogConfig {
	if in == nil {
		return nil
	}
	out := new(LogConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *LogConfig) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LogConfigList) DeepCopyInto(out *LogConfigList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]LogConfig, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LogConfigList.
func (in *LogConfigList) DeepCopy() *LogConfigList {
	if in == nil {
		return nil
	}
	out := new(LogConfigList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *LogConfigList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LogConfigSpec) DeepCopyInto(out *LogConfigSpec) {
	*out = *in
	in.SrcConfig.DeepCopyInto(&out.SrcConfig)
	out.DstConfig = in.DstConfig
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LogConfigSpec.
func (in *LogConfigSpec) DeepCopy() *LogConfigSpec {
	if in == nil {
		return nil
	}
	out := new(LogConfigSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LogConfigStatus) DeepCopyInto(out *LogConfigStatus) {
	*out = *in
	in.LastProbeTime.DeepCopyInto(&out.LastProbeTime)
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	if in.Hosts != nil {
		in, out := &in.Hosts, &out.Hosts
		*out = make([]Agent, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LogConfigStatus.
func (in *LogConfigStatus) DeepCopy() *LogConfigStatus {
	if in == nil {
		return nil
	}
	out := new(LogConfigStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Pipeline) DeepCopyInto(out *Pipeline) {
	*out = *in
	if in.Processors != nil {
		in, out := &in.Processors, &out.Processors
		*out = make([]ProcessorConfig, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Pipeline.
func (in *Pipeline) DeepCopy() *Pipeline {
	if in == nil {
		return nil
	}
	out := new(Pipeline)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProcessConfig) DeepCopyInto(out *ProcessConfig) {
	*out = *in
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProcessConfig.
func (in *ProcessConfig) DeepCopy() *ProcessConfig {
	if in == nil {
		return nil
	}
	out := new(ProcessConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ProcessorConfig) DeepCopyInto(out *ProcessorConfig) {
	*out = *in
	out.Config = in.Config
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ProcessorConfig.
func (in *ProcessorConfig) DeepCopy() *ProcessorConfig {
	if in == nil {
		return nil
	}
	out := new(ProcessorConfig)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SrcConfig) DeepCopyInto(out *SrcConfig) {
	*out = *in
	if in.MetaEnv != nil {
		in, out := &in.MetaEnv, &out.MetaEnv
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.MetaLabel != nil {
		in, out := &in.MetaLabel, &out.MetaLabel
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	in.Pipeline.DeepCopyInto(&out.Pipeline)
	out.ProcessConfig = in.ProcessConfig
	if in.MatchLabels != nil {
		in, out := &in.MatchLabels, &out.MatchLabels
		*out = make([]KVPair, len(*in))
		copy(*out, *in)
	}
	if in.IgnoreLabels != nil {
		in, out := &in.IgnoreLabels, &out.IgnoreLabels
		*out = make([]KVPair, len(*in))
		copy(*out, *in)
	}
	if in.MatchPodLabels != nil {
		in, out := &in.MatchPodLabels, &out.MatchPodLabels
		*out = make([]KVPair, len(*in))
		copy(*out, *in)
	}
	if in.IgnorePodLabels != nil {
		in, out := &in.IgnorePodLabels, &out.IgnorePodLabels
		*out = make([]KVPair, len(*in))
		copy(*out, *in)
	}
	if in.MatchEnvs != nil {
		in, out := &in.MatchEnvs, &out.MatchEnvs
		*out = make([]KVPair, len(*in))
		copy(*out, *in)
	}
	if in.IgnoreEnvs != nil {
		in, out := &in.IgnoreEnvs, &out.IgnoreEnvs
		*out = make([]KVPair, len(*in))
		copy(*out, *in)
	}
	if in.Units != nil {
		in, out := &in.Units, &out.Units
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SrcConfig.
func (in *SrcConfig) DeepCopy() *SrcConfig {
	if in == nil {
		return nil
	}
	out := new(SrcConfig)
	in.DeepCopyInto(out)
	return out
}
