package operator

import (
	"reflect"
	"testing"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalbls"
	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalbls/apis/v1"
	logconfigv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/cce-log-operator/pkg/apis/logconfigs/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func Test_specHash(t *testing.T) {
	a := logconfigv1.LogConfig{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "zihua-test",
			Labels: map[string]string{"abc": "bbc"},
		},
		Spec:   logconfigv1.LogConfigSpec{},
		Status: logconfigv1.LogConfigStatus{},
	}

	b := logconfigv1.LogConfig{
		ObjectMeta: metav1.ObjectMeta{
			Name:   "zihua-test",
			Labels: map[string]string{"abc": "bbc"},
		},
		Spec: logconfigv1.LogConfigSpec{
			SrcConfig: logconfigv1.SrcConfig{
				SrcType: "test",
			},
			DstConfig: logconfigv1.DstConfig{},
		},
		Status: logconfigv1.LogConfigStatus{},
	}

	if specHash(&a) == specHash(&b) {
		t.Errorf("got equal")
	}
}

func Test_toCreateTaskRequest(t *testing.T) {
	cases := []struct {
		Name      string
		ClusterID string
		Input     logconfigv1.LogConfig
		Want      internalbls.CreateTaskRequest
	}{
		{
			Name:      "default",
			ClusterID: "cce-test",
			Input: logconfigv1.LogConfig{
				TypeMeta:   metav1.TypeMeta{},
				ObjectMeta: metav1.ObjectMeta{},
				Spec:       logconfigv1.LogConfigSpec{},
				Status:     logconfigv1.LogConfigStatus{},
			},
			Want: internalbls.CreateTaskRequest{Name: "cce-test//", Config: v1.Config{
				SrcConfig: v1.SrcConfig{
					LabelWhite: make([]v1.Label, 0),
					LabelBlack: make([]v1.Label, 0),
					EnvWhite:   make([]v1.Label, 0),
					EnvBlack:   make([]v1.Label, 0),
				},
			}},
		},
		{
			Name:      "normal",
			ClusterID: "cce-test",
			Input: logconfigv1.LogConfig{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "zihua-test",
					Namespace: "default",
					Labels: map[string]string{
						"zihua-test": "yes",
					},
				},
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						MatchLabels: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
						IgnoreLabels: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
						MatchPodLabels: []logconfigv1.KVPair{{
							Key:   "sns-test",
							Value: "test",
						}},
						MetaLabel: []string{"sns-test"},
						IgnorePodLabels: []logconfigv1.KVPair{{
							Key:   "sns-test",
							Value: "test",
						}},
						MatchEnvs: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
						IgnoreEnvs: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
					},
					DstConfig: logconfigv1.DstConfig{},
				},
				Status: logconfigv1.LogConfigStatus{
					Hosts: []logconfigv1.Agent{{
						HostID:                  "testID",
						HostName:                "testName",
						IP:                      "127.0.0.1",
						IsAvailableForNewConfig: false,
						Status:                  "running",
						UpdatedTime:             "00",
					}},
				},
			},

			Want: internalbls.CreateTaskRequest{
				Name: "cce-test/default/zihua-test",
				Config: v1.Config{
					SrcConfig: v1.SrcConfig{
						LabelWhite: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
						LabelBlack: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
						PodLabelWhite: []v1.Label{{
							Key:   "sns-test",
							Value: "test",
						}},
						MetaLabel: []string{"sns-test"},
						PodLabelBlack: []v1.Label{{
							Key:   "sns-test",
							Value: "test",
						}},
						EnvWhite: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
						EnvBlack: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
					},
					DestConfig: v1.DestConfig{},
				},
				Hosts: []v1.Host{{
					HostID:                  "testID",
					HostName:                "testName",
					IP:                      "127.0.0.1",
					UpdatedTime:             "00",
					IsAvailableForNewConfig: false,
					Status:                  "running",
				}},
				Tags: []v1.Tag{{
					TagValue: "yes",
					TagKey:   "zihua-test",
				}},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.Name, func(t *testing.T) {

			got := ToCreateTaskRequest(c.Input, c.ClusterID)

			if !reflect.DeepEqual(got, c.Want) {
				t.Errorf("want not equal got")
				return
			}
		})
	}

}

func Test_toUpdateTaskRequest(t *testing.T) {
	cases := []struct {
		Name      string
		Input     logconfigv1.LogConfig
		ClusterID string
		Want      internalbls.UpdateTaskRequest
	}{
		{
			Name:      "default",
			ClusterID: "cce-test",
			Input: logconfigv1.LogConfig{
				TypeMeta:   metav1.TypeMeta{},
				ObjectMeta: metav1.ObjectMeta{},
				Spec:       logconfigv1.LogConfigSpec{},
				Status:     logconfigv1.LogConfigStatus{},
			},
			Want: internalbls.UpdateTaskRequest{Name: "cce-test//"},
		},
		{
			Name:      "normal",
			ClusterID: "cce-test",
			Input: logconfigv1.LogConfig{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "zihua-test",
					Namespace: "default",
					Labels: map[string]string{
						"zihua-test": "yes",
					},
				},
				Spec: logconfigv1.LogConfigSpec{
					SrcConfig: logconfigv1.SrcConfig{
						MatchLabels: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
						IgnoreLabels: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
						MetaLabel: []string{"sns-test"},
						MatchPodLabels: []logconfigv1.KVPair{{
							Key:   "sns-test",
							Value: "test",
						}},
						IgnorePodLabels: []logconfigv1.KVPair{{
							Key:   "sns-test",
							Value: "test",
						}},
						MatchEnvs: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
						IgnoreEnvs: []logconfigv1.KVPair{{
							Key:   "zihua-test",
							Value: "test",
						}},
					},
					DstConfig: logconfigv1.DstConfig{},
				},
				Status: logconfigv1.LogConfigStatus{
					Hosts: []logconfigv1.Agent{{
						HostID:                  "testID",
						HostName:                "testName",
						IP:                      "127.0.0.1",
						IsAvailableForNewConfig: false,
						Status:                  "running",
						UpdatedTime:             "00",
					}},
				},
			},

			Want: internalbls.UpdateTaskRequest{
				Name: "cce-test/default/zihua-test",
				Config: v1.Config{
					SrcConfig: v1.SrcConfig{
						LabelWhite: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
						LabelBlack: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
						PodLabelWhite: []v1.Label{{
							Key:   "sns-test",
							Value: "test",
						}},
						PodLabelBlack: []v1.Label{{
							Key:   "sns-test",
							Value: "test",
						}},
						MetaLabel: []string{"sns-test"},
						EnvWhite: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
						EnvBlack: []v1.Label{{
							Key:   "zihua-test",
							Value: "test",
						}},
					},
					DestConfig: v1.DestConfig{},
				},
				Tags: []v1.Tag{{
					TagValue: "yes",
					TagKey:   "zihua-test",
				}},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.Name, func(t *testing.T) {

			got := toUpdateTaskRequest(c.Input, c.ClusterID)

			if !reflect.DeepEqual(got, c.Want) {
				t.Errorf("want not equal got")
				return
			}
		})
	}

}
