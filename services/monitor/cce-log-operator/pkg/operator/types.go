package operator

import (
	"fmt"

	"github.com/mitchellh/hashstructure/v2"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalbls"
	v1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/internalbls/apis/v1"
	logconfigv1 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/cce-log-operator/pkg/apis/logconfigs/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/runtime"
)

const LogOperatorFinalizer corev1.FinalizerName = "log-operator.baidubce.com/finalizer"

func hostsHash(lgc *logconfigv1.LogConfig) string {
	h1, _ := hashstructure.Hash(lgc.Status.Hosts, hashstructure.FormatV2, nil)
	return fmt.Sprintf("%v", h1)
}

func specHash(lgc *logconfigv1.LogConfig) string {
	s := struct {
		Labels map[string]string
		Spec   logconfigv1.LogConfigSpec
	}{
		Labels: lgc.Labels,
		Spec:   lgc.Spec,
	}

	h1, _ := hashstructure.Hash(s, hashstructure.FormatV2, nil)
	return fmt.Sprintf("%v", h1)
}

func objHash(obj runtime.Object) string {
	h1, _ := hashstructure.Hash(obj, hashstructure.FormatV2, nil)
	return fmt.Sprintf("%v", h1)
}

func toUpdateTaskRequest(lgc logconfigv1.LogConfig, clusterID string) internalbls.UpdateTaskRequest {
	r := internalbls.UpdateTaskRequest{
		TaskID: lgc.Status.TaskID,
		Name:   fmt.Sprintf("%s/%s/%s", clusterID, lgc.Namespace, lgc.Name),
		Config: v1.Config{
			SrcConfig: v1.SrcConfig{
				TaskType:       string(lgc.Spec.SrcConfig.TaskType),
				SrcType:        string(lgc.Spec.SrcConfig.SrcType),
				LogType:        string(lgc.Spec.SrcConfig.LogType),
				SrcDir:         lgc.Spec.SrcConfig.SrcDir,
				MatchedPattern: lgc.Spec.SrcConfig.MatchPattern,
				MetaEnv:        lgc.Spec.SrcConfig.MetaEnv,
				MetaLabel:      lgc.Spec.SrcConfig.MetaLabel,
				IgnorePattern:  lgc.Spec.SrcConfig.IgnorePattern,
				TimeFormat:     lgc.Spec.SrcConfig.TimeFormat,
				TTL:            lgc.Spec.SrcConfig.TTL,
				UseMultiline:   lgc.Spec.SrcConfig.UseMultiline,
				MultilineRegex: lgc.Spec.SrcConfig.MultilineRegex,
				RecursiveDir:   lgc.Spec.SrcConfig.RecursiveDir,
				ProcessType:    string(lgc.Spec.SrcConfig.ProcessType),
				ProcessConfig: v1.ProcessConfig{
					Regex:            lgc.Spec.SrcConfig.ProcessConfig.Regex,
					Separator:        lgc.Spec.SrcConfig.ProcessConfig.Separator,
					Quote:            lgc.Spec.SrcConfig.ProcessConfig.Quote,
					SampleLog:        lgc.Spec.SrcConfig.ProcessConfig.SampleLog,
					Keys:             lgc.Spec.SrcConfig.ProcessConfig.Keys,
					DataType:         lgc.Spec.SrcConfig.ProcessConfig.DataType,
					DiscardOnFailure: lgc.Spec.SrcConfig.ProcessConfig.DiscardOnFailure,
					KeepOriginal:     lgc.Spec.SrcConfig.ProcessConfig.KeepOriginal,
				},
				LogTime:        string(lgc.Spec.SrcConfig.LogTime),
				TimestampKey:   lgc.Spec.SrcConfig.TimestampKey,
				DateFormat:     lgc.Spec.SrcConfig.DateFormat,
				FilterExpr:     lgc.Spec.SrcConfig.FilterExpr,
				CloseInactive:  lgc.Spec.SrcConfig.CloseInactive,
				HarvesterLimit: lgc.Spec.SrcConfig.HarvesterLimit,
			},
			DestConfig: v1.DestConfig{
				LogStore:  lgc.Spec.DstConfig.LogStore,
				AccountID: lgc.Spec.DstConfig.AccountID,
				DestType:  string(lgc.Spec.DstConfig.DstType),
				RateLimit: lgc.Spec.DstConfig.RateLimit,
				Project:   lgc.Spec.DstConfig.LogProject,
			},
		},
	}
	for _, item := range lgc.Spec.SrcConfig.MatchLabels {
		r.Config.SrcConfig.LabelWhite = append(r.Config.SrcConfig.LabelWhite, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.IgnoreLabels {
		r.Config.SrcConfig.LabelBlack = append(r.Config.SrcConfig.LabelBlack, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.MatchPodLabels {
		r.Config.SrcConfig.PodLabelWhite = append(r.Config.SrcConfig.PodLabelWhite, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.IgnorePodLabels {
		r.Config.SrcConfig.PodLabelBlack = append(r.Config.SrcConfig.PodLabelBlack, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.MatchEnvs {
		r.Config.SrcConfig.EnvWhite = append(r.Config.SrcConfig.EnvWhite, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.IgnoreEnvs {
		r.Config.SrcConfig.EnvBlack = append(r.Config.SrcConfig.EnvBlack, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for k, v := range lgc.Labels {
		r.Tags = append(r.Tags, v1.Tag{
			TagValue: v,
			TagKey:   k,
		})
	}
	return r
}

func ToCreateTaskRequest(lgc logconfigv1.LogConfig, clusterID string) internalbls.CreateTaskRequest {
	r := internalbls.CreateTaskRequest{
		Name: fmt.Sprintf("%s/%s/%s", clusterID, lgc.Namespace, lgc.Name),
		Config: v1.Config{
			SrcConfig: v1.SrcConfig{
				TaskType:       string(lgc.Spec.SrcConfig.TaskType),
				SrcType:        string(lgc.Spec.SrcConfig.SrcType),
				LogType:        string(lgc.Spec.SrcConfig.LogType),
				SrcDir:         lgc.Spec.SrcConfig.SrcDir,
				Units:          lgc.Spec.SrcConfig.Units,
				MatchedPattern: lgc.Spec.SrcConfig.MatchPattern,
				MetaEnv:        lgc.Spec.SrcConfig.MetaEnv,
				MetaLabel:      lgc.Spec.SrcConfig.MetaLabel,
				IgnorePattern:  lgc.Spec.SrcConfig.IgnorePattern,
				TimeFormat:     lgc.Spec.SrcConfig.TimeFormat,
				TTL:            lgc.Spec.SrcConfig.TTL,
				UseMultiline:   lgc.Spec.SrcConfig.UseMultiline,
				MultilineRegex: lgc.Spec.SrcConfig.MultilineRegex,
				RecursiveDir:   lgc.Spec.SrcConfig.RecursiveDir,
				ProcessType:    string(lgc.Spec.SrcConfig.ProcessType),
				ProcessConfig: v1.ProcessConfig{
					Regex:            lgc.Spec.SrcConfig.ProcessConfig.Regex,
					Separator:        lgc.Spec.SrcConfig.ProcessConfig.Separator,
					Quote:            lgc.Spec.SrcConfig.ProcessConfig.Quote,
					SampleLog:        lgc.Spec.SrcConfig.ProcessConfig.SampleLog,
					Keys:             lgc.Spec.SrcConfig.ProcessConfig.Keys,
					DataType:         lgc.Spec.SrcConfig.ProcessConfig.DataType,
					DiscardOnFailure: lgc.Spec.SrcConfig.ProcessConfig.DiscardOnFailure,
					KeepOriginal:     lgc.Spec.SrcConfig.ProcessConfig.KeepOriginal,
				},
				LogTime:        string(lgc.Spec.SrcConfig.LogTime),
				TimestampKey:   lgc.Spec.SrcConfig.TimestampKey,
				DateFormat:     lgc.Spec.SrcConfig.DateFormat,
				FilterExpr:     lgc.Spec.SrcConfig.FilterExpr,
				CloseInactive:  lgc.Spec.SrcConfig.CloseInactive,
				HarvesterLimit: lgc.Spec.SrcConfig.HarvesterLimit,
			},
			DestConfig: v1.DestConfig{
				LogStore:        lgc.Spec.DstConfig.LogStore,
				AccountID:       lgc.Spec.DstConfig.AccountID,
				DestType:        string(lgc.Spec.DstConfig.DstType),
				RateLimit:       lgc.Spec.DstConfig.RateLimit,
				Project:         lgc.Spec.DstConfig.LogProject,
				BESClusterID:    lgc.Spec.DstConfig.BESClusterID,
				BESUser:         lgc.Spec.DstConfig.BESUser,
				BESPasswd:       lgc.Spec.DstConfig.BESPasswd,
				BESIndexPrefix:  lgc.Spec.DstConfig.BESIndexPrefix,
				BESIndexRolling: lgc.Spec.DstConfig.BESIndexRolling,
				BESIsPwChange:   lgc.Spec.DstConfig.BESIsPwChange,
			},
		},
	}

	for _, item := range lgc.Spec.SrcConfig.MatchLabels {
		r.Config.SrcConfig.LabelWhite = append(r.Config.SrcConfig.LabelWhite, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.IgnoreLabels {
		r.Config.SrcConfig.LabelBlack = append(r.Config.SrcConfig.LabelBlack, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.MatchPodLabels {
		r.Config.SrcConfig.PodLabelWhite = append(r.Config.SrcConfig.PodLabelWhite, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.IgnorePodLabels {
		r.Config.SrcConfig.PodLabelBlack = append(r.Config.SrcConfig.PodLabelBlack, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.MatchEnvs {
		r.Config.SrcConfig.EnvWhite = append(r.Config.SrcConfig.EnvWhite, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for _, item := range lgc.Spec.SrcConfig.IgnoreEnvs {
		r.Config.SrcConfig.EnvBlack = append(r.Config.SrcConfig.EnvBlack, v1.Label{
			Key:   item.Key,
			Value: item.Value,
		})
	}

	for k, v := range lgc.Labels {
		r.Tags = append(r.Tags, v1.Tag{
			TagValue: v,
			TagKey:   k,
		})
	}

	for _, item := range lgc.Status.Hosts {
		r.Hosts = append(r.Hosts, v1.Host{
			HostID:                  item.HostID,
			HostName:                item.HostName,
			IP:                      item.IP,
			UpdatedTime:             item.UpdatedTime,
			IsAvailableForNewConfig: item.IsAvailableForNewConfig,
			Status:                  item.Status,
		})
	}

	if r.Config.SrcConfig.LabelWhite == nil {
		r.Config.SrcConfig.LabelWhite = make([]v1.Label, 0)
	}

	if r.Config.SrcConfig.LabelBlack == nil {
		r.Config.SrcConfig.LabelBlack = make([]v1.Label, 0)
	}
	if r.Config.SrcConfig.EnvWhite == nil {
		r.Config.SrcConfig.EnvWhite = make([]v1.Label, 0)
	}
	if r.Config.SrcConfig.EnvBlack == nil {
		r.Config.SrcConfig.EnvBlack = make([]v1.Label, 0)
	}

	return r
}

type LogbeatConfig struct {
	Management Management `json:"management" yaml:"management"`
	Logging    Logging    `json:"logging" yaml:"logging"`
	MaxProcs   int        `json:"max_procs" yaml:"max_procs"`
	Http       Http       `json:"http" yaml:"http"`
}

type Management struct {
	Enabled   bool     `json:"enabled" yaml:"enabled"`
	Token     string   `json:"token" yaml:"token"`
	TokenList []string `json:"token_list" yaml:"token_list"`
	Address   string   `json:"bls_server_address" yaml:"bls_server_address"`
	Tasks     []string `json:"tasks" yaml:"tasks"`
}

type Metrics struct {
	Enabled bool `json:"enabled" yaml:"enabled"`
}

type Files struct {
	Name             string `json:"name" yaml:"name"`
	Rotateeverybytes int    `json:"rotateeverybytes" yaml:"rotateeverybytes"`
}

type Logging struct {
	Metrics   Metrics  `json:"metrics" yaml:"metrics"`
	Selectors []string `json:"selectors" yaml:"selectors"`
	Level     string   `json:"level" yaml:"level"`
	ToStderr  bool     `json:"to_stderr" yaml:"to_stderr"`
	ToFiles   bool     `json:"to_files" yaml:"to_files"`
	Files     Files    `json:"files" yaml:"files"`
}

type Http struct {
	Enabled bool   `json:"enabled" yaml:"enabled"`
	Host    string `json:"host" yaml:"host"`
	Port    int    `json:"port" yaml:"port"`
}
