apiVersion: cce.baidubce.com/v1
kind: LogConfig
metadata:
  name: pipeline-example
  namespace: default
spec:
  # Pipeline 配置示例
  pipelineConfig:
    sampleLog: "[DEBUG] [2023-01-29/15:10:29] [/root/ONLINE_SERVICE/other/ferry/task_workspace/baidu/bce/bls/server/api/middlewares/permission.go:70]"
    processors:
      # json解析处理器
      - name: "json"
        config:
          field: "@raw"
          discardOnFailure: false
          keepOriginal: false
          dataType: "string,int"
          keys: "path,ip"
      
      # 分隔符解析处理器
      - name: "separator"
        config:
          field: "@raw"
          separator: "custom"
          customSeparator: "aa"
          quote: ""
          discardOnFailure: false
          keepOriginal: false
          dataType: "string,int"
          keys: "path,ip"
      
      # 完全正则解析处理器
      - name: "regex"
        config:
          field: "@raw"
          regex: "(.*)Level(.*)Logtime(.*)"
          discardOnFailure: false
          keepOriginal: false
          dataType: "string,int"
          keys: "path,ip"
      
      # kv解析处理器
      - name: "kv"
        config:
          field: "@raw"
          regex: "(.*)Level(.*)Logtime(.*)"
          kvKeyIndex: 1
          kvValueIndex: 2
          discardOnFailure: false
          keepOriginal: false
          dataType: "string,int"
          keys: "path,ip"
      
      # 时间解析处理器
      - name: "timestamp"
        config:
          timestampKey: "@raw"
          dateFormat: "yyyy-MM-dd HH:mm:ss.SSS"
      
      # 过滤处理器
      - name: "filter"
        config:
          filterExpr: "$status>=400"
      
      # 添加字段处理器
      - name: "addFields"
        config:
          fields:
            key1: "value1"
            key2: "value2"
      
      # 丢弃字段处理器
      - name: "dropFields"
        config:
          fields: ["key1", "key2"]
  
  srcConfig:
    srcType: container
    logType: stdout
  
  dstConfig:
    dstType: BLS
    logStore: pipeline-example
    retention: 10
    rateLimit: 10
