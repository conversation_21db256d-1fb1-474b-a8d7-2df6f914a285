package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"time"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/controller"
)

var (
	kubeconfig = flag.String("kubeconfig", "", "absolute path to the kubeconfig file")
	action     = flag.String("action", "status", "action to perform: status, pods, unlock, wait, health, stats")
	format     = flag.String("format", "text", "output format: text, json")
	timeout    = flag.Duration("timeout", 30*time.Minute, "timeout for wait action")
	autoClean  = flag.Bool("auto-clean", false, "auto clean expired locks during health check")
)

func main() {
	flag.Parse()

	// 构建Kubernetes客户端
	config, err := buildConfig()
	if err != nil {
		fmt.Printf("Error building kubeconfig: %v\n", err)
		os.Exit(1)
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		fmt.Printf("Error creating Kubernetes client: %v\n", err)
		os.Exit(1)
	}

	// 创建工具实例
	utils := controller.NewMutexUtils(clientset)
	ctx := context.Background()

	// 设置自动清理标志
	if *autoClean {
		ctx = context.WithValue(ctx, "auto_clean_expired_lock", true)
	}

	// 执行相应的操作
	switch *action {
	case "status":
		showLockStatus(ctx, utils)
	case "pods":
		showPodsInfo(ctx, utils)
	case "unlock":
		forceUnlock(ctx, utils)
	case "wait":
		waitForUnlock(ctx, utils)
	case "health":
		checkHealth(ctx, utils)
	case "stats":
		showStats(ctx, utils)
	default:
		fmt.Printf("Unknown action: %s\n", *action)
		fmt.Println("Available actions: status, pods, unlock, wait, health, stats")
		os.Exit(1)
	}
}

func buildConfig() (*rest.Config, error) {
	if *kubeconfig != "" {
		return clientcmd.BuildConfigFromFlags("", *kubeconfig)
	}

	// 尝试使用默认的kubeconfig路径
	if home := homedir.HomeDir(); home != "" {
		defaultKubeconfig := filepath.Join(home, ".kube", "config")
		if _, err := os.Stat(defaultKubeconfig); err == nil {
			return clientcmd.BuildConfigFromFlags("", defaultKubeconfig)
		}
	}

	// 尝试使用集群内配置
	return rest.InClusterConfig()
}

func showLockStatus(ctx context.Context, utils *controller.MutexUtils) {
	if *format == "json" {
		info, err := utils.GetLockInfo(ctx)
		if err != nil {
			fmt.Printf("Error getting lock info: %v\n", err)
			os.Exit(1)
		}

		if info == nil {
			fmt.Println(`{"status": "unlocked"}`)
		} else {
			data, _ := json.MarshalIndent(info, "", "  ")
			fmt.Println(string(data))
		}
		return
	}

	// 文本格式输出
	utils.PrintLockStatus(ctx)
}

func showPodsInfo(ctx context.Context, utils *controller.MutexUtils) {
	if *format == "json" {
		pods, err := utils.GetAuditPodsInfo(ctx)
		if err != nil {
			fmt.Printf("Error getting pods info: %v\n", err)
			os.Exit(1)
		}

		data, _ := json.MarshalIndent(pods, "", "  ")
		fmt.Println(string(data))
		return
	}

	// 文本格式输出
	utils.PrintAuditPodsInfo(ctx)
}

func forceUnlock(ctx context.Context, utils *controller.MutexUtils) {
	// 先显示当前锁状态
	fmt.Println("Current lock status:")
	utils.PrintLockStatus(ctx)

	// 确认操作
	fmt.Print("\nAre you sure you want to force unlock? This may interrupt ongoing operations. (y/N): ")
	var response string
	fmt.Scanln(&response)

	if response != "y" && response != "Y" && response != "yes" && response != "YES" {
		fmt.Println("Force unlock cancelled.")
		return
	}

	err := utils.ForceReleaseLock(ctx)
	if err != nil {
		fmt.Printf("Error force unlocking: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Lock force released successfully.")
}

func waitForUnlock(ctx context.Context, utils *controller.MutexUtils) {
	fmt.Printf("Waiting for lock to be released (timeout: %v)...\n", *timeout)

	err := utils.WaitForLockRelease(ctx, *timeout)
	if err != nil {
		fmt.Printf("Error waiting for unlock: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Lock has been released.")
}

func checkHealth(ctx context.Context, utils *controller.MutexUtils) {
	fmt.Println("Checking coordination health...")

	err := utils.CheckCoordinationHealth(ctx)
	if err != nil {
		fmt.Printf("Health check failed: %v\n", err)
		os.Exit(1)
	}

	fmt.Println("Coordination health check passed.")

	// 显示当前状态
	fmt.Println("\nCurrent status:")
	utils.PrintLockStatus(ctx)
	utils.PrintAuditPodsInfo(ctx)
}

func showStats(ctx context.Context, utils *controller.MutexUtils) {
	stats, err := utils.GetCoordinationStats(ctx)
	if err != nil {
		fmt.Printf("Error getting stats: %v\n", err)
		os.Exit(1)
	}

	if *format == "json" {
		data, _ := json.MarshalIndent(stats, "", "  ")
		fmt.Println(string(data))
		return
	}

	// 文本格式输出
	fmt.Println("=== Coordination Statistics ===")
	fmt.Printf("Total Pods: %v\n", stats["total_pods"])
	fmt.Printf("Running Pods: %v\n", stats["running_pods"])
	fmt.Printf("Coordination Needed: %v\n", stats["coordination_needed"])
	fmt.Printf("Lock Exists: %v\n", stats["lock_exists"])

	if stats["lock_exists"].(bool) {
		fmt.Printf("Lock Holder: %v\n", stats["lock_holder"])
		fmt.Printf("Lock Operation: %v\n", stats["lock_operation"])
		fmt.Printf("Lock Expired: %v\n", stats["lock_expired"])
		fmt.Printf("Lock Duration: %.2f seconds\n", stats["lock_duration"])
	}
	fmt.Println("===============================")
}

func init() {
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "Usage: %s [options]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "\nAPIServer Restart Coordination Mutex Tool\n\n")
		fmt.Fprintf(os.Stderr, "Actions:\n")
		fmt.Fprintf(os.Stderr, "  status  - Show current lock status (default)\n")
		fmt.Fprintf(os.Stderr, "  pods    - Show audit pods information\n")
		fmt.Fprintf(os.Stderr, "  unlock  - Force release the lock (dangerous)\n")
		fmt.Fprintf(os.Stderr, "  wait    - Wait for lock to be released\n")
		fmt.Fprintf(os.Stderr, "  health  - Check coordination health\n")
		fmt.Fprintf(os.Stderr, "  stats   - Show coordination statistics\n")
		fmt.Fprintf(os.Stderr, "\nOptions:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nExamples:\n")
		fmt.Fprintf(os.Stderr, "  %s -action=status\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -action=pods -format=json\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -action=unlock\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -action=wait -timeout=10m\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -action=health -auto-clean\n", os.Args[0])
	}
}
