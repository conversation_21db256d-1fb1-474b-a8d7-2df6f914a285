package utils

import (
	"bufio"
	"context"
	"io"
	"os"
	"os/exec"
	"strings"
	"time"

	"github.com/baidu/baiducloud-sdk-go/bce"
	corev1 "k8s.io/api/core/v1"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/cce"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/filter"
	utilconfig "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/utils/config"
)

const (
	Resource           = "resource"
	SubResource        = "subresource"
	ResourceName       = "resourcename"
	Verb               = "verb"
	NormalDateFormat   = "Mon, 02 Jan 2006 15:04:05 MST"
	XBceDateFormat     = "2006-01-02T15:04:05Z"
	BackoffNormal      = "normal"
	BackoffExponential = "exponential"
)

var (
	DefaultResourceFilters = []string{"nodes", "pods", "deployments", "statefulsets", "replicasets", "jobs",
		"daemonsets", "cronjobs", "persistentvolumes", "persistentvolumeclaims", "secrets",
		"services", "configmaps"}

	DefaultVerbFilters         = []string{"create", "update", "delete", "patch"}
	DefaultResourceNameFilters = []string{"cloud-controller-manager"}
	DefaultSubResourceFilters  = []string{"status"}

	DefaultFiltersMap = map[string][]string{
		Resource:     DefaultResourceFilters,
		ResourceName: DefaultResourceNameFilters,
		SubResource:  DefaultSubResourceFilters,
		Verb:         DefaultVerbFilters,
	}

	DefaultFiltersList = []string{
		Resource,
		ResourceName,
		SubResource,
		Verb,
	}

	AuditConf = []string{
		"        - --audit-log-maxage=14",
		"        - --audit-log-maxbackup=10",
		"        - --audit-log-maxsize=100",
		"        - --audit-log-path=/etc/kubernetes/kubernetes.audit",
		"        - --audit-webhook-mode=batch",
		"        - --audit-webhook-batch-max-size=40",
		"        - --audit-webhook-config-file=/etc/kubernetes/audit-webhook.conf",
	}
	apiservicePath     = "/etc/kubernetes/manifests/"
	apiserviceFile     = "kube-apiserver.yaml"
	apiserviceTempFile = "kube-apiserver-temp.yaml"
)

var (
	ClusterID string
	Secret    *corev1.Secret
)

// BuildFilter -
func BuildFilter(ctx context.Context, conf map[string]string) []filter.AuditFilter {
	afs := make([]filter.AuditFilter, 0)
	for _, fl := range DefaultFiltersList {
		filters := make([]string, 0)
		FilterStr, ok := conf[fl]
		if !ok {
			filters = DefaultFiltersMap[fl]
		} else {
			if len(FilterStr) == 0 {
				logger.Errorf(ctx, "Get empty filters value: len(FilterStr) == 0")
				continue
			}
			subFilterStr := strings.Split(FilterStr, ",")

			for _, flt := range subFilterStr {
				filters = append(filters, flt)
			}
		}
		logger.Infof(ctx, "%s filter: %q", fl, filters)

		var auditFilter filter.AuditFilter
		switch fl {
		case Resource:
			auditFilter = filter.NewResourceFilter(filters)

		case ResourceName:
			auditFilter = filter.NewResourceNameFilter(filters)

		case SubResource:
			auditFilter = filter.NewSubResourceFilter(filters)

		case Verb:
			auditFilter = filter.NewVerbFilter(filters)
		}
		afs = append(afs, auditFilter)
	}
	afs = append(afs, filter.NewUserFilter([]string{"system:"}))
	return afs
}

func cceSvcSign(req *bce.Request, option *bce.SignOption) {
	ctx := context.TODO()

	headers := option.Headers
	headers["Host"] = req.Host
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	cloudConfig, err := utilconfig.GetCloudConfig()
	if err != nil {
		logger.Errorf(ctx, "GetCloudConfig failed: %s", err)
		return
	}

	// req may have contain auth headers on bce retry, which should not be in headers to sign
	req.Header.Del("Authorization")
	req.Header.Del("x-bce-security-token")
	queries := req.URL.Query()
	keyOnlyQueries := make([]string, 0)
	for k, v := range queries {
		if len(v) == 0 || v[0] == "" {
			keyOnlyQueries = append(keyOnlyQueries, k)
			delete(queries, k)
		}
	}

	if cloudConfig.Endpoint != "" {
		logger.Infof(ctx, "use cloud config endpoint %s", cloudConfig.Endpoint)
	}

	if cloudConfig.Endpoint == "*************:8693" {
		logger.Infof(ctx, "Run in sandbox, use 'bcc.bce-api.baidu.com' as bcc api endpoint")
	}

	logger.Infof(ctx, "Sending request for signature")

	cceClient := cce.NewClient(cce.NewConfig(
		&bce.Config{
			Credentials: bce.NewCredentials("", ""),
			Checksum:    true,
			Timeout:     3 * time.Second,
			Region:      cloudConfig.Region,
			Endpoint:    cloudConfig.Endpoint,
		}),
	)

	args := &cce.GenerateSignatureArgs{
		ClusterID:      cloudConfig.ClusterId,
		Method:         req.Method,
		URI:            strings.Split(req.URL.RequestURI(), "?")[0],
		Headers:        req.Header,
		Queries:        queries,
		KeyOnlyQueries: keyOnlyQueries,
	}

	signature, err := cceClient.GenerateSignature(ctx, args)
	if err != nil {
		logger.Infof(ctx, "GenerateSignature failed: %s", err)
		return
	}

	req.Header.Set("Authorization", signature.Authorization)
	req.Header.Set("x-bce-security-token", signature.SecurityToken)
}

// ValidatePolicyName -
func ValidatePolicyName(policyName string) bool {
	if policyName == BackoffNormal || policyName == BackoffExponential {
		return true
	}
	return false
}

// GenSignOption -
func GenSignOption() *bce.SignOption {
	current := time.Unix(time.Now().Unix(), 0).UTC()
	date := current.Format(NormalDateFormat)
	xBceDate := current.Format(XBceDateFormat)
	headers := map[string]string{
		"Content-Type": "application/json",
		"Date":         date,
		"x-bce-date":   xBceDate,
	}

	signOption := &bce.SignOption{
		CustomSignFunc: cceSvcSign,
		Headers:        headers,
	}
	return signOption
}

func ConfigFilter(config *corev1.ConfigMap) bool {
	if config == nil || config.Labels == nil {
		return false
	}

	v, ok := config.Labels["type"]
	if !ok || v != "audit-configmap" {
		return false
	}
	return true
}

func PrepareAuditYaml(flag bool) bool {
	logger.Infof(context.TODO(), "prepareAuditYaml")
	changeFlag := true
	auditLineCount := 0

	originFile, err := os.Open(apiservicePath + apiserviceFile)
	if err != nil {
		panic(err)
	}
	defer originFile.Close()
	checkReader := bufio.NewReader(originFile)

	// 统计当前文件中的审计配置行数
	for {
		line, _, err := checkReader.ReadLine()
		if err == io.EOF {
			break
		}
		if err != nil {
			panic(err)
		}
		if strings.Contains(string(line), "audit-log") || strings.Contains(string(line), "audit-webhook") {
			auditLineCount++
		}
	}

	// 检查是否需要修改
	if flag {
		// 开启审计：如果审计配置已完整存在，无需修改
		if auditLineCount == len(AuditConf) {
			logger.Infof(context.TODO(), "Audit configuration already exists, no need to modify")
			changeFlag = false
			return changeFlag
		}
	} else {
		// 关闭审计：如果没有审计配置，无需修改
		if auditLineCount == 0 {
			logger.Infof(context.TODO(), "Audit configuration already removed, no need to modify")
			changeFlag = false
			return changeFlag
		}
	}
	tempFile, err := os.Create(apiservicePath + apiserviceTempFile)
	if err != nil {
		panic(err)
	}
	defer tempFile.Close()

	lines := []string{}
	_, err = originFile.Seek(0, io.SeekStart)
	if err != nil {
		panic(err)
	}
	r := bufio.NewReader(originFile)
	for {
		bytes, _, err := r.ReadLine()
		if err == io.EOF {
			break
		}
		if err != nil {
			panic(err)
		}
		if flag && strings.Contains(string(bytes), "service-node-port-range") {
			for _, line := range AuditConf {
				lines = append(lines, line)
			}
		}

		if !flag && (strings.Contains(string(bytes), "audit-log") || strings.Contains(string(bytes), "audit-webhook")) {
			continue
		}
		lines = append(lines, string(bytes))
	}
	_, err = tempFile.Write([]byte(strings.Join(lines, "\n")))
	if err != nil {
		panic(err)
	}
	return changeFlag
}

func ChangeAuditYaml(ctx context.Context) error {
	cmd := exec.Command("mv", apiservicePath+apiserviceTempFile, apiservicePath+apiserviceFile)
	_, err := cmd.CombinedOutput()
	if err != nil {
		logger.Errorf(ctx, "Failed to modify apiservice: %s", err)
		return err
	}

	return nil
}

func IsStandalone() bool {
	_, err := os.Stat(apiservicePath + apiserviceFile)
	if err != nil {
		return false
	}
	return true
}
