package utils

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

// Global variables to mock in tests
var (
	originalApiservicePath string
	originalApiserviceFile string
	originalAuditConf      []string
	originalTempFile       string
)

// SetupGlobals mocks global variables for testing
func SetupGlobals(t *testing.T, tmpDir string) {
	originalApiservicePath = apiservicePath
	originalApiserviceFile = apiserviceFile
	originalTempFile = apiserviceTempFile

	apiservicePath = tmpDir + "/"
	apiserviceFile = "test.conf"
	apiserviceTempFile = "temp.conf"

}

// RestoreGlobals restores original global variables
func RestoreGlobals() {
	apiservicePath = originalApiservicePath
	apiserviceFile = originalApiserviceFile

	apiserviceTempFile = originalTempFile
}

// WriteTestFile writes content to a test file
func WriteTestFile(t *testing.T, filePath string, content []string) {
	err := os.WriteFile(filePath, []byte(strings.Join(content, "\n")), 0644)
	if err != nil {
		t.Fatal(err)
	}
}

// CapturePanic captures panic to verify error paths
func CapturePanic(t *testing.T, f func()) {
	defer func() {
		if r := recover(); r == nil {
			t.Errorf("Expected panic but did not occur")
		}
	}()
	f()
}

func TestPrepareAuditYaml_FlagTrue_AuditLinesEnough(t *testing.T) {
	tmpDir := t.TempDir()
	SetupGlobals(t, tmpDir)
	defer RestoreGlobals()

	// File contains audit lines equal to AuditConf length
	testContent := []string{
		"        - --audit-log-maxage=14",
		"        - --audit-log-maxbackup=10",
		"        - --audit-log-maxsize=100",
		"        - --audit-log-path=/etc/kubernetes/kubernetes.audit",
		"        - --audit-webhook-mode=batch",
		"        - --audit-webhook-batch-max-size=40",
		"        - --audit-webhook-config-file=/etc/kubernetes/audit-webhook.conf",
	}
	auditFilePath := filepath.Join(tmpDir, apiserviceFile)
	WriteTestFile(t, auditFilePath, testContent)

	result := PrepareAuditYaml(true)
	if result {
		t.Errorf("Expected false, got %v", result)
	}

	// Temp file should not exist
	tempFilePath := filepath.Join(tmpDir, apiserviceTempFile)
	if _, err := os.Stat(tempFilePath); !os.IsNotExist(err) {
		t.Errorf("Temp file should not exist")
	}
}

func TestPrepareAuditYaml_FlagTrue_InsertAuditConf(t *testing.T) {
	tmpDir := t.TempDir()
	SetupGlobals(t, tmpDir)
	defer RestoreGlobals()

	// File contains service-node-port-range line
	testContent := []string{
		"        - --service-node-port-range",
		"        - --audit-log-maxage=14",
		"        - --audit-log-maxbackup=10",
		"        - --audit-log-maxsize=100",
		"        - --audit-log-path=/etc/kubernetes/kubernetes.audit",
	}
	auditFilePath := filepath.Join(tmpDir, apiserviceFile)
	WriteTestFile(t, auditFilePath, testContent)

	result := PrepareAuditYaml(true)
	if !result {
		t.Errorf("Expected true, got %v", result)
	}

	// Read temp file content
	tempFilePath := filepath.Join(tmpDir, apiserviceTempFile)
	content, err := os.ReadFile(tempFilePath)
	if err != nil {
		t.Fatal(err)
	}

	expectedStrings := []string{
		"        - --audit-log-maxage=14",
		"        - --audit-log-maxbackup=10",
		"        - --audit-log-maxsize=100",
		"        - --audit-log-path=/etc/kubernetes/kubernetes.audit",
		"        - --audit-webhook-mode=batch",
		"        - --audit-webhook-batch-max-size=40",
		"        - --audit-webhook-config-file=/etc/kubernetes/audit-webhook.conf",
		"        - --service-node-port-range",
		"        - --audit-log-maxage=14",
		"        - --audit-log-maxbackup=10",
		"        - --audit-log-maxsize=100",
		"        - --audit-log-path=/etc/kubernetes/kubernetes.audit",
	}
	expected := strings.Join(expectedStrings, "\n")
	if string(content) != expected {
		t.Errorf("Expected temp file content:\n%s\nGot:\n%s", expected, string(content))
	}
}

func TestPrepareAuditYaml_FlagFalse_RemoveAuditLines(t *testing.T) {
	tmpDir := t.TempDir()
	SetupGlobals(t, tmpDir)
	defer RestoreGlobals()

	// File contains audit lines
	testContent := []string{
		"audit-log=/tmp/log.log",
		"normal line",
		"audit-webhook=config.yaml",
	}
	auditFilePath := filepath.Join(tmpDir, apiserviceFile)
	WriteTestFile(t, auditFilePath, testContent)

	result := PrepareAuditYaml(false)
	if !result {
		t.Errorf("Expected true, got %v", result)
	}

	// Read temp file content
	tempFilePath := filepath.Join(tmpDir, apiserviceTempFile)
	content, err := os.ReadFile(tempFilePath)
	if err != nil {
		t.Fatal(err)
	}

	expected := "normal line"
	if string(content) != expected {
		t.Errorf("Expected temp file content:\n%s\nGot:\n%s", expected, string(content))
	}
}

func TestPrepareAuditYaml_FlagFalse_NoAuditLines(t *testing.T) {
	tmpDir := t.TempDir()
	SetupGlobals(t, tmpDir)
	defer RestoreGlobals()

	// File contains no audit lines
	testContent := []string{
		"normal line 1",
		"normal line 2",
		"service-node-port-range",
	}
	auditFilePath := filepath.Join(tmpDir, apiserviceFile)
	WriteTestFile(t, auditFilePath, testContent)

	result := PrepareAuditYaml(false)
	if result {
		t.Errorf("Expected false (no modification needed), got %v", result)
	}

	// Temp file should not be created
	tempFilePath := filepath.Join(tmpDir, apiserviceTempFile)
	if _, err := os.Stat(tempFilePath); !os.IsNotExist(err) {
		t.Errorf("Temp file should not be created when no modification is needed")
	}
}

func TestPrepareAuditYaml_FileOpenError(t *testing.T) {
	tmpDir := t.TempDir()
	SetupGlobals(t, tmpDir)
	defer RestoreGlobals()

	// Set file path to non-existent
	apiserviceFile = "nonexistent.conf"

	// Capture panic
	CapturePanic(t, func() {
		PrepareAuditYaml(true)
	})
}
