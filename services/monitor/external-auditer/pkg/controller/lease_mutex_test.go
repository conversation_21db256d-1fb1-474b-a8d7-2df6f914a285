package controller

import (
	"context"
	"testing"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
)

func TestLeaseMutex_BasicAcquireRelease(t *testing.T) {
	// 创建fake客户端
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试获取Lease
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 验证Lease是否存在
	if !leaseMutex.IsLocked(ctx) {
		t.Error("Lease should exist after AcquireLease() call")
	}

	// 测试释放Lease
	err = leaseMutex.ReleaseLease(ctx)
	if err != nil {
		t.Fatalf("Failed to release lease: %v", err)
	}

	// 验证Lease是否已释放
	if leaseMutex.IsLocked(ctx) {
		t.Error("Lease should not exist after ReleaseLease() call")
	}
}

func TestLeaseMutex_ConcurrentLease(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取Lease
	err := leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("First instance failed to acquire lease: %v", err)
	}

	// 第二个实例尝试获取Lease（应该会等待）
	done := make(chan error, 1)
	go func() {
		// 设置较短的超时时间用于测试
		shortCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		done <- leaseMutex2.AcquireLease(shortCtx, "test_operation")
	}()

	// 等待一小段时间确保第二个实例开始等待
	time.Sleep(1 * time.Second)

	// 释放第一个Lease
	err = leaseMutex1.ReleaseLease(ctx)
	if err != nil {
		t.Fatalf("Failed to release first lease: %v", err)
	}

	// 检查第二个实例是否能获取Lease
	select {
	case err := <-done:
		if err != nil {
			t.Fatalf("Second instance failed to acquire lease after first was released: %v", err)
		}
	case <-time.After(10 * time.Second):
		t.Fatal("Second instance timed out waiting for lease")
	}

	// 清理
	leaseMutex2.ReleaseLease(ctx)
}

func TestLeaseMutex_LeaseExpiration(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取Lease
	err := leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 手动创建一个过期的Lease
	lease, err := fakeClient.CoordinationV1().Leases(LeaseNamespace).Get(ctx, LeaseResourceName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get lease: %v", err)
	}

	// 设置Lease为1小时前创建（模拟过期）
	expiredTime := metav1.MicroTime{Time: time.Now().Add(-1 * time.Hour)}
	lease.Spec.RenewTime = &expiredTime

	_, err = fakeClient.CoordinationV1().Leases(LeaseNamespace).Update(ctx, lease, metav1.UpdateOptions{})
	if err != nil {
		t.Fatalf("Failed to update lease: %v", err)
	}

	// 第二个实例应该能够接管过期的Lease
	err = leaseMutex2.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Second instance failed to take over expired lease: %v", err)
	}

	// 验证Lease现在由第二个实例持有
	status, err := leaseMutex2.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status: %v", err)
	}

	if status.Holder != leaseMutex2.GetInstanceID() {
		t.Errorf("Expected lease holder to be %s, got %s", leaseMutex2.GetInstanceID(), status.Holder)
	}

	// 清理
	leaseMutex2.ReleaseLease(ctx)
}

func TestLeaseMutex_ExecuteWithLease(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()

	// 创建一些fake的节点和命名空间，使健康检查能够通过
	fakeClient.CoreV1().Nodes().Create(context.Background(), &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{Name: "test-node"},
	}, metav1.CreateOptions{})

	fakeClient.CoreV1().Namespaces().Create(context.Background(), &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{Name: "test-namespace"},
	}, metav1.CreateOptions{})

	// 创建fake的APIServer Pod
	fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kube-apiserver-test",
			Namespace: "kube-system",
			Labels:    map[string]string{"component": "kube-apiserver"},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
		},
	}, metav1.CreateOptions{})

	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	executed := false
	executeFunc := func() error {
		executed = true
		return nil
	}

	// 测试带健康检查的执行
	err := leaseMutex.ExecuteWithLease(ctx, "test_operation", executeFunc)
	if err != nil {
		t.Fatalf("ExecuteWithLease failed: %v", err)
	}

	// 验证函数被执行
	if !executed {
		t.Error("Execute function was not called")
	}

	// 验证Lease已被释放
	if leaseMutex.IsLocked(ctx) {
		t.Error("Lease should be released after ExecuteWithLease")
	}
}

func TestLeaseMutex_LeaseRenewal(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 获取Lease
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 获取初始的续约时间
	status1, err := leaseMutex.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status: %v", err)
	}

	// 等待一小段时间
	time.Sleep(100 * time.Millisecond)

	// 手动续约
	err = leaseMutex.RenewLease(ctx)
	if err != nil {
		t.Fatalf("Failed to renew lease: %v", err)
	}

	// 获取续约后的时间
	status2, err := leaseMutex.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status after renewal: %v", err)
	}

	// 验证续约时间已更新
	if !status2.RenewedAt.After(status1.RenewedAt) {
		t.Error("Lease renewal time should be updated")
	}

	// 清理
	leaseMutex.ReleaseLease(ctx)
}

func TestLeaseMutex_HealthCheck(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()

	// 创建一个Running且Ready的APIServer Pod
	fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kube-apiserver-test",
			Namespace: "kube-system",
			Labels:    map[string]string{"component": "kube-apiserver"},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
		},
	}, metav1.CreateOptions{})

	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试健康检查
	healthy := leaseMutex.isAPIServerHealthy(ctx)
	if !healthy {
		t.Error("Health check should pass with running and ready APIServer pod")
	}
}

func TestLeaseMutex_GetInstanceID(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)

	instanceID := leaseMutex.GetInstanceID()
	if instanceID == "" {
		t.Error("Instance ID should not be empty")
	}

	// 验证实例ID格式
	if len(instanceID) < 10 {
		t.Error("Instance ID should be reasonably long")
	}
}
