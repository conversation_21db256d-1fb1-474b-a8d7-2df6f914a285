package controller

import (
	"context"
	"errors"
	"testing"
	"time"

	coordinationv1 "k8s.io/api/coordination/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
)

func TestLeaseMutex_BasicAcquireRelease(t *testing.T) {
	// 创建fake客户端
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试获取Lease
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 验证Lease是否存在
	if !leaseMutex.IsLocked(ctx) {
		t.Error("Lease should exist after AcquireLease() call")
	}

	// 测试释放Lease
	err = leaseMutex.ReleaseLease(ctx)
	if err != nil {
		t.Fatalf("Failed to release lease: %v", err)
	}

	// 验证Lease是否已释放
	if leaseMutex.IsLocked(ctx) {
		t.Error("Lease should not exist after ReleaseLease() call")
	}
}

func TestLeaseMutex_ConcurrentLease(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取Lease
	err := leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("First instance failed to acquire lease: %v", err)
	}

	// 验证第一个实例确实持有Lease
	status, err := leaseMutex1.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status: %v", err)
	}
	if status.Holder != leaseMutex1.GetInstanceID() {
		t.Fatalf("Expected lease holder to be %s, got %s", leaseMutex1.GetInstanceID(), status.Holder)
	}

	// 第二个实例尝试获取Lease（应该会等待）
	done := make(chan error, 1)
	go func() {
		// 设置较长的超时时间，但使用更短的轮询间隔
		longCtx, cancel := context.WithTimeout(ctx, 15*time.Second)
		defer cancel()
		done <- leaseMutex2.AcquireLease(longCtx, "test_operation")
	}()

	// 等待足够长的时间确保第二个实例开始等待并至少尝试一次
	time.Sleep(2 * time.Second)

	// 验证第二个实例还没有获取到Lease
	status2, err := leaseMutex2.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status: %v", err)
	}
	if status2.Holder == leaseMutex2.GetInstanceID() {
		t.Fatal("Second instance should not have acquired lease while first instance holds it")
	}

	// 释放第一个Lease
	err = leaseMutex1.ReleaseLease(ctx)
	if err != nil {
		t.Fatalf("Failed to release first lease: %v", err)
	}

	// 检查第二个实例是否能获取Lease
	select {
	case err := <-done:
		if err != nil {
			t.Fatalf("Second instance failed to acquire lease after first was released: %v", err)
		}
		// 验证第二个实例现在持有Lease
		finalStatus, err := leaseMutex2.GetLeaseStatus(ctx)
		if err != nil {
			t.Fatalf("Failed to get final lease status: %v", err)
		}
		if finalStatus.Holder != leaseMutex2.GetInstanceID() {
			t.Fatalf("Expected final lease holder to be %s, got %s", leaseMutex2.GetInstanceID(), finalStatus.Holder)
		}
	case <-time.After(20 * time.Second):
		t.Fatal("Second instance timed out waiting for lease")
	}

	// 清理
	leaseMutex2.ReleaseLease(ctx)
}

func TestLeaseMutex_LeaseExpiration(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取Lease
	err := leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 验证第一个实例持有Lease
	status1, err := leaseMutex1.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get initial lease status: %v", err)
	}
	if status1.Holder != leaseMutex1.GetInstanceID() {
		t.Fatalf("Expected initial lease holder to be %s, got %s", leaseMutex1.GetInstanceID(), status1.Holder)
	}

	// 手动创建一个过期的Lease
	lease, err := fakeClient.CoordinationV1().Leases(LeaseNamespace).Get(ctx, LeaseResourceName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get lease: %v", err)
	}

	// 设置Lease为1小时前续约（模拟过期）
	expiredTime := metav1.MicroTime{Time: time.Now().Add(-1 * time.Hour)}
	lease.Spec.RenewTime = &expiredTime

	_, err = fakeClient.CoordinationV1().Leases(LeaseNamespace).Update(ctx, lease, metav1.UpdateOptions{})
	if err != nil {
		t.Fatalf("Failed to update lease: %v", err)
	}

	// 验证Lease确实过期了
	updatedStatus, err := leaseMutex1.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get updated lease status: %v", err)
	}
	if !updatedStatus.IsExpired {
		t.Fatal("Lease should be expired after setting old renew time")
	}

	// 第二个实例应该能够接管过期的Lease
	err = leaseMutex2.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Second instance failed to take over expired lease: %v", err)
	}

	// 验证Lease现在由第二个实例持有
	finalStatus, err := leaseMutex2.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get final lease status: %v", err)
	}

	if finalStatus.Holder != leaseMutex2.GetInstanceID() {
		t.Errorf("Expected final lease holder to be %s, got %s", leaseMutex2.GetInstanceID(), finalStatus.Holder)
	}

	if finalStatus.IsExpired {
		t.Error("New lease should not be expired")
	}

	// 清理
	leaseMutex2.ReleaseLease(ctx)
}

func TestLeaseMutex_ExecuteWithLease(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()

	// 创建fake的APIServer Pod，使健康检查能够通过
	fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kube-apiserver-test",
			Namespace: "kube-system",
			Labels:    map[string]string{"component": "kube-apiserver"},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
		},
	}, metav1.CreateOptions{})

	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	executed := false
	executeFunc := func() error {
		executed = true
		return nil
	}

	// 测试带健康检查的执行
	err := leaseMutex.ExecuteWithLease(ctx, "test_operation", executeFunc)
	if err != nil {
		t.Fatalf("ExecuteWithLease failed: %v", err)
	}

	// 验证函数被执行
	if !executed {
		t.Error("Execute function was not called")
	}

	// 验证Lease已被释放
	if leaseMutex.IsLocked(ctx) {
		t.Error("Lease should be released after ExecuteWithLease")
	}
}

func TestLeaseMutex_LeaseRenewal(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 获取Lease
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 获取初始的续约时间
	status1, err := leaseMutex.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status: %v", err)
	}

	// 等待一小段时间
	time.Sleep(100 * time.Millisecond)

	// 手动续约
	err = leaseMutex.RenewLease(ctx)
	if err != nil {
		t.Fatalf("Failed to renew lease: %v", err)
	}

	// 获取续约后的时间
	status2, err := leaseMutex.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lease status after renewal: %v", err)
	}

	// 验证续约时间已更新
	if !status2.RenewedAt.After(status1.RenewedAt) {
		t.Error("Lease renewal time should be updated")
	}

	// 清理
	leaseMutex.ReleaseLease(ctx)
}

func TestLeaseMutex_HealthCheck(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()

	// 创建一个Running且Ready的APIServer Pod
	fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kube-apiserver-test",
			Namespace: "kube-system",
			Labels:    map[string]string{"component": "kube-apiserver"},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
		},
	}, metav1.CreateOptions{})

	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试健康检查
	healthy := leaseMutex.isAPIServerHealthy(ctx)
	if !healthy {
		t.Error("Health check should pass with running and ready APIServer pod")
	}
}

func TestLeaseMutex_GetInstanceID(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)

	instanceID := leaseMutex.GetInstanceID()
	if instanceID == "" {
		t.Error("Instance ID should not be empty")
	}

	// 验证实例ID格式
	if len(instanceID) < 10 {
		t.Error("Instance ID should be reasonably long")
	}
}

// 测试连接拒绝错误检查
func TestLeaseMutex_IsConnectionRefused(t *testing.T) {
	tests := []struct {
		name     string
		err      error
		expected bool
	}{
		{
			name:     "ConnectionRefused",
			err:      errors.New("dial tcp 192.168.1.1:6443: connect: connection refused"),
			expected: true,
		},
		{
			name:     "SimpleConnectionRefused",
			err:      errors.New("connection refused"),
			expected: true,
		},
		{
			name:     "OtherError",
			err:      errors.New("timeout"),
			expected: false,
		},
		{
			name:     "NetworkError",
			err:      errors.New("network unreachable"),
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isConnectionRefused(tt.err)
			if result != tt.expected {
				t.Errorf("isConnectionRefused() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// 测试Pod Ready状态检查
func TestLeaseMutex_IsPodReady(t *testing.T) {
	tests := []struct {
		name     string
		pod      *corev1.Pod
		expected bool
	}{
		{
			name: "PodRunningAndReady",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase: corev1.PodRunning,
					Conditions: []corev1.PodCondition{
						{
							Type:   corev1.PodReady,
							Status: corev1.ConditionTrue,
						},
					},
				},
			},
			expected: true,
		},
		{
			name: "PodRunningButNotReady",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase: corev1.PodRunning,
					Conditions: []corev1.PodCondition{
						{
							Type:   corev1.PodReady,
							Status: corev1.ConditionFalse,
						},
					},
				},
			},
			expected: false,
		},
		{
			name: "PodNotRunning",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase: corev1.PodPending,
					Conditions: []corev1.PodCondition{
						{
							Type:   corev1.PodReady,
							Status: corev1.ConditionTrue,
						},
					},
				},
			},
			expected: false,
		},
		{
			name: "PodNoReadyCondition",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase:      corev1.PodRunning,
					Conditions: []corev1.PodCondition{},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isPodReady(tt.pod)
			if result != tt.expected {
				t.Errorf("isPodReady() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// 测试APIServer健康检查的各种场景
func TestLeaseMutex_IsAPIServerHealthy_EdgeCases(t *testing.T) {
	tests := []struct {
		name     string
		pods     []*corev1.Pod
		expected bool
	}{
		{
			name:     "NoPods",
			pods:     []*corev1.Pod{},
			expected: false,
		},
		{
			name: "SinglePodNotReady",
			pods: []*corev1.Pod{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "kube-apiserver-1",
						Namespace: "kube-system",
						Labels:    map[string]string{"component": "kube-apiserver"},
					},
					Status: corev1.PodStatus{
						Phase: corev1.PodRunning,
						Conditions: []corev1.PodCondition{
							{
								Type:   corev1.PodReady,
								Status: corev1.ConditionFalse,
							},
						},
					},
				},
			},
			expected: false,
		},
		{
			name: "MultiplePodsMixed",
			pods: []*corev1.Pod{
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "kube-apiserver-1",
						Namespace: "kube-system",
						Labels:    map[string]string{"component": "kube-apiserver"},
					},
					Status: corev1.PodStatus{
						Phase: corev1.PodRunning,
						Conditions: []corev1.PodCondition{
							{
								Type:   corev1.PodReady,
								Status: corev1.ConditionTrue,
							},
						},
					},
				},
				{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "kube-apiserver-2",
						Namespace: "kube-system",
						Labels:    map[string]string{"component": "kube-apiserver"},
					},
					Status: corev1.PodStatus{
						Phase: corev1.PodRunning,
						Conditions: []corev1.PodCondition{
							{
								Type:   corev1.PodReady,
								Status: corev1.ConditionFalse,
							},
						},
					},
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()

			// 创建测试Pod
			for _, pod := range tt.pods {
				fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), pod, metav1.CreateOptions{})
			}

			leaseMutex := NewLeaseMutex(fakeClient)
			result := leaseMutex.isAPIServerHealthy(context.Background())

			if result != tt.expected {
				t.Errorf("isAPIServerHealthy() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// 测试强制释放Lease
func TestLeaseMutex_ForceRelease(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 先获取一个Lease
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 验证Lease存在
	if !leaseMutex.IsLocked(ctx) {
		t.Error("Lease should exist before force release")
	}

	// 强制释放
	err = leaseMutex.ForceRelease(ctx)
	if err != nil {
		t.Fatalf("Failed to force release lease: %v", err)
	}

	// 验证Lease已被删除
	if leaseMutex.IsLocked(ctx) {
		t.Error("Lease should not exist after force release")
	}

	// 测试强制释放不存在的Lease（应该不报错）
	err = leaseMutex.ForceRelease(ctx)
	if err != nil {
		t.Fatalf("Force release of non-existent lease should not error: %v", err)
	}
}

// 测试更新Lease注解
func TestLeaseMutex_UpdateLeaseAnnotation(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 先获取Lease
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 更新注解
	err = leaseMutex.updateLeaseAnnotation(ctx, "test_key", "test_value")
	if err != nil {
		t.Fatalf("Failed to update lease annotation: %v", err)
	}

	// 验证注解已更新 - 我们通过直接获取Lease来检查

	// 获取原始Lease来检查注解
	lease, err := fakeClient.CoordinationV1().Leases(LeaseNamespace).Get(ctx, LeaseResourceName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get lease: %v", err)
	}

	if lease.Annotations["test_key"] != "test_value" {
		t.Errorf("Expected annotation test_key=test_value, got %s", lease.Annotations["test_key"])
	}

	// 清理
	leaseMutex.ReleaseLease(ctx)
}

// 测试更新不存在的Lease注解
func TestLeaseMutex_UpdateLeaseAnnotation_NotFound(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 尝试更新不存在的Lease注解
	err := leaseMutex.updateLeaseAnnotation(ctx, "test_key", "test_value")
	if err == nil {
		t.Error("Expected error when updating annotation of non-existent lease")
	}
}

// 测试更新其他实例持有的Lease注解
func TestLeaseMutex_UpdateLeaseAnnotation_WrongHolder(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取Lease
	err := leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 第二个实例尝试更新注解（应该失败）
	err = leaseMutex2.updateLeaseAnnotation(ctx, "test_key", "test_value")
	if err == nil {
		t.Error("Expected error when updating annotation of lease held by another instance")
	}

	// 清理
	leaseMutex1.ReleaseLease(ctx)
}

// 测试日志状态函数
func TestLeaseMutex_LogLeaseStatus(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试没有Lease时的日志
	leaseMutex.logLeaseStatus(ctx) // 应该不会panic

	// 获取Lease后测试日志
	err := leaseMutex.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 测试有Lease时的日志
	leaseMutex.logLeaseStatus(ctx) // 应该不会panic

	// 清理
	leaseMutex.ReleaseLease(ctx)
}

// 测试Lease过期检查的边界情况
func TestLeaseMutex_IsLeaseExpired_EdgeCases(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)

	tests := []struct {
		name     string
		lease    *coordinationv1.Lease
		expected bool
	}{
		{
			name: "NoRenewTime",
			lease: &coordinationv1.Lease{
				Spec: coordinationv1.LeaseSpec{
					LeaseDurationSeconds: func() *int32 { i := int32(300); return &i }(),
				},
			},
			expected: true,
		},
		{
			name: "NoDurationSeconds",
			lease: &coordinationv1.Lease{
				Spec: coordinationv1.LeaseSpec{
					RenewTime: &metav1.MicroTime{Time: time.Now()},
				},
			},
			expected: true,
		},
		{
			name: "JustExpired",
			lease: &coordinationv1.Lease{
				Spec: coordinationv1.LeaseSpec{
					RenewTime:            &metav1.MicroTime{Time: time.Now().Add(-301 * time.Second)},
					LeaseDurationSeconds: func() *int32 { i := int32(300); return &i }(),
				},
			},
			expected: true,
		},
		{
			name: "NotExpired",
			lease: &coordinationv1.Lease{
				Spec: coordinationv1.LeaseSpec{
					RenewTime:            &metav1.MicroTime{Time: time.Now().Add(-100 * time.Second)},
					LeaseDurationSeconds: func() *int32 { i := int32(300); return &i }(),
				},
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := leaseMutex.isLeaseExpired(tt.lease)
			if result != tt.expected {
				t.Errorf("isLeaseExpired() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

// 测试尝试接管Lease的各种场景
func TestLeaseMutex_TryTakeOverLease(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试接管不存在的Lease
	acquired, err := leaseMutex1.tryTakeOverLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("tryTakeOverLease should handle non-existent lease: %v", err)
	}
	if !acquired {
		t.Error("Should acquire lease when none exists")
	}

	// 清理
	leaseMutex1.ReleaseLease(ctx)

	// 测试接管未过期的Lease
	err = leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	acquired, err = leaseMutex2.tryTakeOverLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("tryTakeOverLease failed: %v", err)
	}
	if acquired {
		t.Error("Should not acquire non-expired lease held by another instance")
	}

	// 清理
	leaseMutex1.ReleaseLease(ctx)
}

// 测试续约失败的场景
func TestLeaseMutex_RenewLease_Failures(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex1 := NewLeaseMutex(fakeClient)
	leaseMutex2 := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试续约不存在的Lease
	err := leaseMutex1.RenewLease(ctx)
	if err == nil {
		t.Error("Expected error when renewing non-existent lease")
	}

	// 第一个实例获取Lease
	err = leaseMutex1.AcquireLease(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lease: %v", err)
	}

	// 第二个实例尝试续约（应该失败）
	err = leaseMutex2.RenewLease(ctx)
	if err == nil {
		t.Error("Expected error when renewing lease held by another instance")
	}

	// 清理
	leaseMutex1.ReleaseLease(ctx)
}

// 测试健康检查超时场景
func TestLeaseMutex_WaitForAPIServerHealthy_Timeout(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)

	// 创建一个不健康的APIServer Pod
	fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kube-apiserver-unhealthy",
			Namespace: "kube-system",
			Labels:    map[string]string{"component": "kube-apiserver"},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodPending, // 不健康状态
		},
	}, metav1.CreateOptions{})

	// 使用短超时测试 - 通过context控制超时

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	err := leaseMutex.waitForAPIServerHealthy(ctx)
	if err == nil {
		t.Error("Expected timeout error when APIServer is not healthy")
	}
}

// 测试ExecuteWithLease的错误场景
func TestLeaseMutex_ExecuteWithLease_ExecuteError(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()

	// 创建健康的APIServer Pod
	fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "kube-apiserver-test",
			Namespace: "kube-system",
			Labels:    map[string]string{"component": "kube-apiserver"},
		},
		Status: corev1.PodStatus{
			Phase: corev1.PodRunning,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
		},
	}, metav1.CreateOptions{})

	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试执行函数返回错误的情况
	executeFunc := func() error {
		return errors.New("execution failed")
	}

	err := leaseMutex.ExecuteWithLease(ctx, "test_operation", executeFunc)
	if err == nil {
		t.Error("Expected error when execute function fails")
	}

	// 验证Lease已被释放
	if leaseMutex.IsLocked(ctx) {
		t.Error("Lease should be released even when execution fails")
	}
}

// 测试GetLeaseStatus的边界情况
func TestLeaseMutex_GetLeaseStatus_EdgeCases(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	leaseMutex := NewLeaseMutex(fakeClient)
	ctx := context.Background()

	// 测试获取不存在的Lease状态
	status, err := leaseMutex.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("GetLeaseStatus should not error for non-existent lease: %v", err)
	}
	if status != nil {
		t.Error("Status should be nil for non-existent lease")
	}

	// 创建一个没有完整信息的Lease
	lease := &coordinationv1.Lease{
		ObjectMeta: metav1.ObjectMeta{
			Name:      LeaseResourceName,
			Namespace: LeaseNamespace,
		},
		Spec: coordinationv1.LeaseSpec{
			LeaseDurationSeconds: func() *int32 { i := int32(300); return &i }(),
		},
	}

	_, err = fakeClient.CoordinationV1().Leases(LeaseNamespace).Create(ctx, lease, metav1.CreateOptions{})
	if err != nil {
		t.Fatalf("Failed to create test lease: %v", err)
	}

	// 获取状态
	status, err = leaseMutex.GetLeaseStatus(ctx)
	if err != nil {
		t.Fatalf("GetLeaseStatus failed: %v", err)
	}
	if status == nil {
		t.Error("Status should not be nil for existing lease")
	}
	if status.Holder != "" {
		t.Error("Holder should be empty when not set")
	}
}
