package controller

import (
	"context"
	"fmt"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	coreinformers "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	corelisters "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/sinks"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/sinks/register"
	utils "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/utils"
)

const (
	// maxRetries is the maximum number of times a configMap will be retried before it is dropped out of the queue.
	maxRetries = 15

	Cluster    = "cluster"
	Standalone = "standalone"
	Managed    = "managed"
)

var (
	clusterID = ""
)

// Controller is the controller implementation for configMap resources.
type Controller struct {
	sync.Mutex
	// kubeClient is a standard kubernetes clientset.
	kubeClient kubernetes.Interface
	// clusterKubeClient is a standard kubernetes clientset for managed cluster.
	managedKubeClient kubernetes.Interface
	// queue is a rate limited work queue. This is used to queue work to be
	// processed instead of performing it as soon as a change happens. This
	// means we can ensure we only process a fixed amount of resources at a
	// time, and makes it easy to ensure we are never processing the same item
	// simultaneously in two different workers.
	queue workqueue.RateLimitingInterface
	// recorder is an event recorder for recording Event resources to the
	// Kubernetes API.
	configmapSynced cache.InformerSynced
	configmapLister corelisters.ConfigMapLister

	clients map[register.SinkType]sinks.Interface

	// mutex 简单互斥锁
	mutex *SimpleMutex
}

// NewController returns a new configMap controller.
func NewController(
	kubeClient kubernetes.Interface,
	configmapInformer coreinformers.ConfigMapInformer,
	clients map[register.SinkType]sinks.Interface) *Controller {

	controller := &Controller{
		kubeClient:      kubeClient,
		configmapLister: configmapInformer.Lister(),
		configmapSynced: configmapInformer.Informer().HasSynced,
		queue: workqueue.NewNamedRateLimitingQueue(workqueue.DefaultControllerRateLimiter(),
			"external-auditor"),
		clients: clients,
		mutex:   NewSimpleMutex(kubeClient),
	}

	logger.Infof(context.TODO(), "Setting up event handlers")
	// Set up an event handler for when configMap resources change.
	configmapInformer.Informer().AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			objCM := obj.(*corev1.ConfigMap)
			if utils.ConfigFilter(objCM) {
				controller.enqueue(objCM)
			}
		},
		UpdateFunc: func(old, new interface{}) {
			newCM := new.(*corev1.ConfigMap)
			oldCM := old.(*corev1.ConfigMap)
			if newCM.ResourceVersion == oldCM.ResourceVersion {
				return
			}
			if utils.ConfigFilter(newCM) {
				controller.enqueue(newCM)
			}
		},
		DeleteFunc: func(obj interface{}) {
			var objCM *corev1.ConfigMap
			var ok bool

			// 处理 DeletedFinalStateUnknown 类型
			if objCM, ok = obj.(*corev1.ConfigMap); !ok {
				if deletedState, ok := obj.(cache.DeletedFinalStateUnknown); ok {
					objCM, ok = deletedState.Obj.(*corev1.ConfigMap)
					if !ok {
						logger.Errorf(context.TODO(), "DeletedFinalStateUnknown contained object that is not a ConfigMap: %T", deletedState.Obj)
						return
					}
				} else {
					logger.Errorf(context.TODO(), "Object is not a ConfigMap or DeletedFinalStateUnknown: %T", obj)
					return
				}
			}

			if utils.ConfigFilter(objCM) {
				controller.enqueue(objCM)
			}
		},
	})

	return controller
}

func (c *Controller) Run(threadiness int, stopCh <-chan struct{}) error {
	defer runtime.HandleCrash()
	defer c.queue.ShutDown()

	// Wait for the caches to be synced before starting workers.
	if ok := cache.WaitForCacheSync(stopCh, c.configmapSynced); !ok {
		return fmt.Errorf("failed to wait for caches to sync")
	}

	// Launch workers to process configMap resources.
	for i := 0; i < threadiness; i++ {
		go wait.Until(c.runWorker, time.Second, stopCh)
	}

	<-stopCh

	return nil
}

// runWorker is a long-running function that will continually call the
// processNextWorkItem function in order to read and process a message on the
// work queue.
func (c *Controller) runWorker() {
	for c.processNextWorkItem() {
	}
}

// processNextWorkItem will read a single work item off the work queue and
// attempt to process it, by calling the syncHandler.
func (c *Controller) processNextWorkItem() bool {
	ctx := context.WithValue(context.TODO(), logger.RequestID, logger.GetUUID())

	obj, shutdown := c.queue.Get()
	if shutdown {
		return false
	}

	// We wrap this block in a func so we can defer c.queue.Done.
	err := func(obj interface{}) error {
		// We call Done here so the work queue knows we have finished
		// processing this item. We also must remember to call Forget if we
		// do not want this work item being re-queued. For example, we do
		// not call Forget if a transient error occurs, instead the item is
		// put back on the work queue and attempted again after a back-off
		// period.
		defer c.queue.Done(obj)
		var key string
		var ok bool
		// We expect strings to come off the work queue. These are of the
		// form namespace/name. We do this as the delayed nature of the
		// work queue means the items in the informer cache may actually be
		// more up to date that when the item was initially put onto the
		// work queue.
		if key, ok = obj.(string); !ok {
			// As the item in the work queue is actually invalid, we call
			// Forget here else we'd go into a loop of attempting to
			// process a work item that is invalid.
			c.queue.Forget(obj)
			runtime.HandleError(fmt.Errorf("expected string in workqueue but got %#v", obj))
			return nil
		}
		// Run the syncHandler, passing it the namespace/name string of the
		// configMap resource to be synced.
		if err := c.syncHandler(ctx, key); err != nil {
			return fmt.Errorf("error syncing '%s': %s", key, err.Error())
		}
		// Finally, if no error occurs we Forget this item so it does not
		// get queued again until another change happens.
		c.queue.Forget(obj)
		logger.Infof(ctx, "Successfully synced: %s", key)
		return nil
	}(obj)

	if err != nil {
		runtime.HandleError(err)
	}

	return true
}

// syncHandler compares the actual state with the desired, and attempts to
// converge the two. It then updates the Status block of the config resource
// with the current status of the resource.
func (c *Controller) syncHandler(ctx context.Context, key string) error {
	logger.Infof(ctx, "syncHandler key:%s", key)
	// Convert the namespace/name string into a distinct namespace and name.

	namespace, name, err := cache.SplitMetaNamespaceKey(key)
	if err != nil {
		runtime.HandleError(fmt.Errorf("invalid resource key: %s", key))
		return err
	}

	// Get the MPIJob with this namespace/name.
	config, err := c.configmapLister.ConfigMaps(namespace).Get(name)
	logger.Infof(ctx, "Get config %s, %s", key, namespace)

	// The MPIJob may no longer exist, in which case we stop processing.
	if errors.IsNotFound(err) {
		c.Lock()
		defer c.Unlock()

		logger.Infof(ctx, "Delete %s from clients", name)
		delete(c.clients, register.SinkType(name))

		if clusterID != "" && utils.IsStandalone() {
			if err = c.modifyStandAloneApiServe(ctx, false); err != nil {
				runtime.HandleError(fmt.Errorf("config '%s' in work queue no longer exists", key))
			}
		}
		return nil
	}

	if err != nil {
		return err
	}

	if !utils.ConfigFilter(config) {
		return nil
	}

	// 判断是否是推送BLS
	if config.Data["clientType"] == "bls" {
		if clusterID == "" {
			clusterID = config.Data["clusterID"]
		}
		if utils.IsStandalone() {
			logger.Infof(ctx, "isStandalone %s", true)
			if err = c.modifyStandAloneApiServe(ctx, true); err != nil {
				logger.Errorf(ctx, "modifyApiParams failed: %s", err)
				return err
			}
		}
	}

	logger.Infof(ctx, "Syncing config %s", key)
	err = register.RegisterClient(context.TODO(), config, c.clients)
	if err != nil {
		logger.Errorf(ctx, "RegisterClient failed: %s", err)
		return err
	}
	return nil
}

func (c *Controller) modifyStandAloneApiServe(ctx context.Context, isOpen bool) error {
	// 检查是否需要协调
	if !c.needsCoordination(ctx) {
		logger.Infof(ctx, "Single instance environment, executing directly")
		return c.executeAPIServerModification(ctx, isOpen)
	}

	operation := "enable_audit"
	if !isOpen {
		operation = "disable_audit"
	}

	logger.Infof(ctx, "Multi-instance environment detected, using coordination mechanism for operation: %s", operation)

	// 使用带健康检查的执行方法
	return c.mutex.ExecuteWithHealthCheck(ctx, operation, func() error {
		return c.executeAPIServerModification(ctx, isOpen)
	})
}

// executeAPIServerModification 执行实际的APIServer修改（原有逻辑）
func (c *Controller) executeAPIServerModification(ctx context.Context, isOpen bool) error {
	logger.Infof(ctx, "[executeAPIServerModification] openOrClose: %s", isOpen)
	var err error = nil
	changeFlag := true

	// 关闭审计
	if !isOpen {
		// 判断是否是旧集群第一次开启审计服务，是直接返回
		changeFlag = utils.PrepareAuditYaml(false)
		if changeFlag {
			err = utils.ChangeAuditYaml(ctx)
		}
		return err
	} else {
		logger.Infof(ctx, "isOldFalse %s", true)
		changeFlag = utils.PrepareAuditYaml(true)
		if changeFlag {
			if err := utils.ChangeAuditYaml(ctx); err != nil {
				logger.Errorf(ctx, "[executeAPIServerModification] failed to change AuditYaml: %s", err.Error())
				return err
			}
		}
	}
	return nil
}

// needsCoordination 检查是否需要协调
func (c *Controller) needsCoordination(ctx context.Context) bool {

	// 简单检查：如果能查询到多个审计Pod，则需要协调
	pods, err := c.kubeClient.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{
		LabelSelector: "component=kube-external-auditor",
	})
	if err != nil {
		logger.Warnf(ctx, "Failed to check coordination need: %v", err)
		return false
	}

	runningPods := 0
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning {
			runningPods++
		}
	}

	logger.Infof(ctx, "Found %d running audit pods", runningPods)
	return runningPods > 1
}

// enqueue takes a config resource and converts it into a namespace/name
// string which is then put onto the work queue. This method should *not* be
// passed resources of any type other than configmap.
func (c *Controller) enqueue(obj interface{}) {
	var key string
	var err error
	if key, err = cache.MetaNamespaceKeyFunc(obj); err != nil {
		runtime.HandleError(err)
		return
	}

	c.queue.AddRateLimited(key)
}
