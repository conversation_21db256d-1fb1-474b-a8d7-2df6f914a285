package controller

import (
	"context"
	"testing"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"
)

func TestSimpleMutex_BasicLockUnlock(t *testing.T) {
	// 创建fake客户端
	fakeClient := fake.NewSimpleClientset()
	mutex := NewSimpleMutex(fakeClient)
	ctx := context.Background()

	// 测试获取锁
	err := mutex.Lock(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lock: %v", err)
	}

	// 验证锁是否存在
	if !mutex.IsLocked(ctx) {
		t.Error("Lock should exist after Lock() call")
	}

	// 测试释放锁
	err = mutex.Unlock(ctx)
	if err != nil {
		t.Fatalf("Failed to release lock: %v", err)
	}

	// 验证锁是否已释放
	if mutex.IsLocked(ctx) {
		t.<PERSON>rror("Lock should not exist after Unlock() call")
	}
}

func TestSimpleMutex_ConcurrentLock(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	mutex1 := NewSimpleMutex(fakeClient)
	mutex2 := NewSimpleMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取锁
	err := mutex1.Lock(ctx, "test_operation")
	if err != nil {
		t.Fatalf("First instance failed to acquire lock: %v", err)
	}

	// 第二个实例尝试获取锁（应该会等待）
	done := make(chan error, 1)
	go func() {
		// 设置较短的超时时间用于测试
		shortCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
		defer cancel()
		done <- mutex2.Lock(shortCtx, "test_operation")
	}()

	// 等待一小段时间确保第二个实例开始等待
	time.Sleep(1 * time.Second)

	// 释放第一个锁
	err = mutex1.Unlock(ctx)
	if err != nil {
		t.Fatalf("Failed to release first lock: %v", err)
	}

	// 检查第二个实例是否能获取锁
	select {
	case err := <-done:
		if err != nil {
			t.Fatalf("Second instance failed to acquire lock after first was released: %v", err)
		}
	case <-time.After(10 * time.Second):
		t.Fatal("Second instance timed out waiting for lock")
	}

	// 清理
	mutex2.Unlock(ctx)
}

func TestSimpleMutex_LockExpiration(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	mutex1 := NewSimpleMutex(fakeClient)
	mutex2 := NewSimpleMutex(fakeClient)
	ctx := context.Background()

	// 第一个实例获取锁
	err := mutex1.Lock(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Failed to acquire lock: %v", err)
	}

	// 手动创建一个过期的锁
	cm, err := fakeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx, LockConfigMapName, metav1.GetOptions{})
	if err != nil {
		t.Fatalf("Failed to get lock ConfigMap: %v", err)
	}

	// 设置锁为1小时前创建（模拟过期）
	expiredTime := time.Now().Add(-1 * time.Hour)
	cm.Data["locked_at"] = expiredTime.Format(time.RFC3339)
	cm.Data["lock_timeout"] = "900" // 15分钟超时

	_, err = fakeClient.CoreV1().ConfigMaps(LockNamespace).Update(ctx, cm, metav1.UpdateOptions{})
	if err != nil {
		t.Fatalf("Failed to update lock ConfigMap: %v", err)
	}

	// 第二个实例应该能够接管过期的锁
	err = mutex2.Lock(ctx, "test_operation")
	if err != nil {
		t.Fatalf("Second instance failed to take over expired lock: %v", err)
	}

	// 验证锁现在由第二个实例持有
	status, err := mutex2.GetLockStatus(ctx)
	if err != nil {
		t.Fatalf("Failed to get lock status: %v", err)
	}

	if status["lock_holder"] != mutex2.GetInstanceID() {
		t.Errorf("Expected lock holder to be %s, got %s", mutex2.GetInstanceID(), status["lock_holder"])
	}

	// 清理
	mutex2.Unlock(ctx)
}

func TestSimpleMutex_ExecuteWithHealthCheck(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	
	// 创建一些fake的节点和命名空间，使健康检查能够通过
	fakeClient.CoreV1().Nodes().Create(context.Background(), &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{Name: "test-node"},
	}, metav1.CreateOptions{})
	
	fakeClient.CoreV1().Namespaces().Create(context.Background(), &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{Name: "test-namespace"},
	}, metav1.CreateOptions{})

	mutex := NewSimpleMutex(fakeClient)
	ctx := context.Background()

	executed := false
	executeFunc := func() error {
		executed = true
		return nil
	}

	// 测试带健康检查的执行
	err := mutex.ExecuteWithHealthCheck(ctx, "test_operation", executeFunc)
	if err != nil {
		t.Fatalf("ExecuteWithHealthCheck failed: %v", err)
	}

	// 验证函数被执行
	if !executed {
		t.Error("Execute function was not called")
	}

	// 验证锁已被释放
	if mutex.IsLocked(ctx) {
		t.Error("Lock should be released after ExecuteWithHealthCheck")
	}
}

func TestSimpleMutex_HealthCheck(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	
	// 创建一些资源使健康检查通过
	fakeClient.CoreV1().Nodes().Create(context.Background(), &corev1.Node{
		ObjectMeta: metav1.ObjectMeta{Name: "test-node"},
	}, metav1.CreateOptions{})

	mutex := NewSimpleMutex(fakeClient)
	ctx := context.Background()

	// 测试健康检查
	healthy := mutex.isAPIServerHealthy(ctx)
	if !healthy {
		t.Error("Health check should pass with fake client and test node")
	}
}

func TestSimpleMutex_GetInstanceID(t *testing.T) {
	fakeClient := fake.NewSimpleClientset()
	mutex := NewSimpleMutex(fakeClient)

	instanceID := mutex.GetInstanceID()
	if instanceID == "" {
		t.Error("Instance ID should not be empty")
	}

	// 验证实例ID格式
	if len(instanceID) < 10 {
		t.Error("Instance ID should be reasonably long")
	}
}
