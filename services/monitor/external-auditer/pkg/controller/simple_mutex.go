package controller

import (
	"context"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

const (
	LockConfigMapName      = "apiserver-restart-lock"
	LockNamespace          = "kube-system"
	DefaultLockTimeout     = 15 * time.Minute // 默认锁超时时间
	MaxWaitTime            = 45 * time.Minute // 最大等待时间
	APIServerHealthTimeout = 10 * time.Minute // APIserver健康检查超时时间
	HealthCheckInterval    = 10 * time.Second // 健康检查间隔
	AcquireLockInterval    = 10 * time.Second
)

// SimpleMutex 简单互斥锁
type SimpleMutex struct {
	kubeClient kubernetes.Interface
	instanceID string // 当前实例的唯一ID
}

// NewSimpleMutex 创建简单互斥锁
func NewSimpleMutex(kubeClient kubernetes.Interface) *SimpleMutex {
	// 生成唯一实例ID（基于时间戳 + 随机数）
	instanceID := fmt.Sprintf("instance-%d-%d",
		time.Now().UnixNano(),
		rand.Intn(10000))

	return &SimpleMutex{
		kubeClient: kubeClient,
		instanceID: instanceID,
	}
}

// Lock 获取锁
func (m *SimpleMutex) Lock(ctx context.Context, operation string) error {
	logger.Infof(ctx, "Instance %s attempting to acquire lock for operation: %s",
		m.instanceID, operation)

	// 等待获取锁
	if err := m.waitAndAcquireLock(ctx, operation); err != nil {
		return fmt.Errorf("failed to acquire lock: %v", err)
	}

	logger.Infof(ctx, "Instance %s successfully acquired lock", m.instanceID)
	return nil
}

// Unlock 释放锁
func (m *SimpleMutex) Unlock(ctx context.Context) error {
	logger.Infof(ctx, "Instance %s releasing lock", m.instanceID)

	// 删除锁ConfigMap
	err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Delete(ctx,
		LockConfigMapName, metav1.DeleteOptions{})

	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to release lock: %v", err)
	}

	logger.Infof(ctx, "Instance %s successfully released lock", m.instanceID)
	return nil
}

// waitAndAcquireLock 等待并获取锁
func (m *SimpleMutex) waitAndAcquireLock(ctx context.Context, operation string) error {
	timeout := MaxWaitTime
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 随机初始延迟，避免雷群效应
	initialDelay := time.Duration(rand.Intn(10000)) * time.Millisecond
	time.Sleep(initialDelay)

	ticker := time.NewTicker(AcquireLockInterval)
	defer ticker.Stop()

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("timeout waiting for lock")
		case <-ticker.C:
			// 尝试获取锁
			acquired, err := m.tryAcquireLock(ctx, operation)
			if err != nil {
				logger.Warnf(ctx, "Error trying to acquire lock: %v", err)
				continue
			}

			if acquired {
				return nil
			}

			// 检查当前锁状态
			m.checkLockStatus(ctx)
		}
	}
}

// tryAcquireLock 尝试获取锁
func (m *SimpleMutex) tryAcquireLock(ctx context.Context, operation string) (bool, error) {
	lockData := map[string]string{
		"lock_holder":  m.instanceID,
		"locked_at":    time.Now().Format(time.RFC3339),
		"operation":    operation,
		"lock_timeout": strconv.Itoa(int(DefaultLockTimeout.Seconds())),
		"status":       "executing",
	}

	lockCM := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      LockConfigMapName,
			Namespace: LockNamespace,
		},
		Data: lockData,
	}

	// 尝试创建锁
	_, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Create(ctx,
		lockCM, metav1.CreateOptions{})

	if err == nil {
		// 创建成功，获得锁
		return true, nil
	}

	if !errors.IsAlreadyExists(err) {
		return false, err
	}

	// 锁已存在，检查是否可以抢占
	return m.tryTakeOverLock(ctx, operation)
}

// tryTakeOverLock 尝试接管锁
func (m *SimpleMutex) tryTakeOverLock(ctx context.Context, operation string) (bool, error) {
	// 获取现有锁
	existingCM, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx,
		LockConfigMapName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// 锁已被删除，重新尝试创建
			return m.tryAcquireLock(ctx, operation)
		}
		return false, err
	}

	// 检查锁是否超时
	if m.isLockExpired(existingCM) {
		logger.Infof(ctx, "Lock expired, taking over from %s",
			existingCM.Data["lock_holder"])

		// 更新锁信息
		existingCM.Data["lock_holder"] = m.instanceID
		existingCM.Data["locked_at"] = time.Now().Format(time.RFC3339)
		existingCM.Data["operation"] = operation
		existingCM.Data["status"] = "executing"

		_, err = m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Update(ctx,
			existingCM, metav1.UpdateOptions{})

		return err == nil, err
	}

	// 锁未超时，无法获取
	return false, nil
}

// isLockExpired 检查锁是否过期
func (m *SimpleMutex) isLockExpired(cm *corev1.ConfigMap) bool {
	lockedAtStr := cm.Data["locked_at"]
	timeoutStr := cm.Data["lock_timeout"]

	if lockedAtStr == "" {
		return true // 无效锁，可以接管
	}

	lockedAt, err := time.Parse(time.RFC3339, lockedAtStr)
	if err != nil {
		return true // 时间格式错误，可以接管
	}

	timeout := DefaultLockTimeout
	if timeoutStr != "" {
		if t, err := strconv.Atoi(timeoutStr); err == nil {
			timeout = time.Duration(t) * time.Second
		}
	}

	return time.Since(lockedAt) > timeout
}

// checkLockStatus 检查锁状态（用于日志）
func (m *SimpleMutex) checkLockStatus(ctx context.Context) {
	cm, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx,
		LockConfigMapName, metav1.GetOptions{})
	if err != nil {
		return
	}

	holder := cm.Data["lock_holder"]
	lockedAt := cm.Data["locked_at"]
	operation := cm.Data["operation"]

	logger.Infof(ctx, "Lock held by %s since %s for operation %s",
		holder, lockedAt, operation)
}

// GetInstanceID 获取实例ID
func (m *SimpleMutex) GetInstanceID() string {
	return m.instanceID
}

// GetLockStatus 获取当前锁状态
func (m *SimpleMutex) GetLockStatus(ctx context.Context) (map[string]string, error) {
	cm, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx,
		LockConfigMapName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return map[string]string{"status": "unlocked"}, nil
		}
		return nil, err
	}

	return cm.Data, nil
}

// IsLocked 检查是否有锁存在
func (m *SimpleMutex) IsLocked(ctx context.Context) bool {
	_, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx,
		LockConfigMapName, metav1.GetOptions{})
	return err == nil
}

// ForceUnlock 强制释放锁（紧急情况使用）
func (m *SimpleMutex) ForceUnlock(ctx context.Context) error {
	logger.Warnf(ctx, "Force unlocking by instance %s", m.instanceID)

	err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Delete(ctx,
		LockConfigMapName, metav1.DeleteOptions{})

	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to force unlock: %v", err)
	}

	logger.Infof(ctx, "Force unlock completed by instance %s", m.instanceID)
	return nil
}

// ExecuteWithHealthCheck 执行操作并等待APIserver健康
func (m *SimpleMutex) ExecuteWithHealthCheck(ctx context.Context, operation string, executeFunc func() error) error {
	// 获取锁
	if err := m.Lock(ctx, operation); err != nil {
		return fmt.Errorf("failed to acquire lock: %v", err)
	}

	// 确保释放锁
	defer func() {
		if err := m.Unlock(ctx); err != nil {
			logger.Warnf(ctx, "Failed to release lock: %v", err)
		}
	}()

	// 更新锁状态为执行中
	if err := m.updateLockStatus(ctx, "executing"); err != nil {
		logger.Warnf(ctx, "Failed to update lock status: %v", err)
	}

	// 执行操作
	logger.Infof(ctx, "Instance %s executing operation: %s", m.instanceID, operation)
	if err := executeFunc(); err != nil {
		m.updateLockStatus(ctx, "failed")
		return fmt.Errorf("operation failed: %v", err)
	}

	// 更新状态为等待健康检查
	if err := m.updateLockStatus(ctx, "waiting_health"); err != nil {
		logger.Warnf(ctx, "Failed to update lock status: %v", err)
	}

	// 等待APIserver健康
	logger.Infof(ctx, "Instance %s waiting for APIServer to become healthy", m.instanceID)
	if err := m.waitForAPIServerHealthy(ctx); err != nil {
		logger.Warnf(ctx, "APIServer health check failed: %v", err)
		m.updateLockStatus(ctx, "health_check_failed")
		// 不返回错误，因为操作可能已经成功，只是健康检查超时
	} else {
		logger.Infof(ctx, "Instance %s APIServer health check passed", m.instanceID)
		m.updateLockStatus(ctx, "completed")
	}

	return nil
}

// updateLockStatus 更新锁状态
func (m *SimpleMutex) updateLockStatus(ctx context.Context, status string) error {
	cm, err := m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx,
		LockConfigMapName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	cm.Data["status"] = status
	cm.Data["last_updated"] = time.Now().Format(time.RFC3339)

	_, err = m.kubeClient.CoreV1().ConfigMaps(LockNamespace).Update(ctx,
		cm, metav1.UpdateOptions{})
	return err
}

// waitForAPIServerHealthy 等待APIserver健康
func (m *SimpleMutex) waitForAPIServerHealthy(ctx context.Context) error {
	timeout := APIServerHealthTimeout
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(HealthCheckInterval)
	defer ticker.Stop()

	logger.Infof(ctx, "Starting APIServer health check with timeout %v", timeout)

	for {
		select {
		case <-timeoutCtx.Done():
			// 超时，尝试强制释放锁
			err := m.ForceUnlock(ctx)
			return fmt.Errorf("timeout waiting for APIServer to become healthy after %v, error: %v", timeout, err)
		case <-ticker.C:
			if m.isAPIServerHealthy(ctx) {
				logger.Infof(ctx, "APIServer health check passed")
				// 健康检查通过后，再等待一个健康检查间隔后释放锁
				time.Sleep(HealthCheckInterval)
				return nil
			}
			// 健康检查失败是正常的，不需要每次都打印错误日志
		}
	}
}

// isAPIServerHealthy 检查APIserver是否健康
func (m *SimpleMutex) isAPIServerHealthy(ctx context.Context) bool {
	// 获取所有APIServer Pod
	pods, err := m.kubeClient.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{
		LabelSelector: "component=kube-apiserver",
	})
	if err != nil {
		// 如果是连接被拒绝，说明当前APIServer正在重启，这是正常的
		if isConnectionRefused(err) {
			logger.Warnf(ctx, "APIServer connection refused (likely restarting): %v", err)
		} else {
			logger.Warnf(ctx, "Failed to list APIServer pods: %v", err)
		}
		return false
	}

	if len(pods.Items) == 0 {
		logger.Errorf(ctx, "No APIServer pods found")
		return false
	}

	runningCount := 0
	// 检查所有APIServer Pod是否都在Running状态
	for _, pod := range pods.Items {
		if pod.Status.Phase == corev1.PodRunning {
			// 进一步检查Pod是否Ready
			if isPodReady(&pod) {
				runningCount++
			} else {
				logger.Errorf(ctx, "APIServer pod %s is running but not ready", pod.Name)
			}
		} else {
			logger.Errorf(ctx, "APIServer pod %s is not running: %s", pod.Name, pod.Status.Phase)
		}
	}

	// 所有Pod都在运行且Ready才认为健康
	allHealthy := runningCount == len(pods.Items)
	logger.Infof(ctx, "APIServer health check: %d/%d pods running and ready", runningCount, len(pods.Items))

	return allHealthy
}

// isConnectionRefused 检查错误是否为连接被拒绝
func isConnectionRefused(err error) bool {
	return strings.Contains(err.Error(), "connection refused") ||
		strings.Contains(err.Error(), "connect: connection refused")
}

// isPodReady 检查Pod是否Ready
func isPodReady(pod *corev1.Pod) bool {
	if pod.Status.Phase != corev1.PodRunning {
		return false
	}

	for _, condition := range pod.Status.Conditions {
		if condition.Type == corev1.PodReady {
			return condition.Status == corev1.ConditionTrue
		}
	}
	return false
}
