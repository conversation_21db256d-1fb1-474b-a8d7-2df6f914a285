package controller

import (
	"context"
	"fmt"
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes/fake"

	utils2 "icode.baidu.com/baidu/jpaas-caas/cce-stack/services/monitor/external-auditer/pkg/utils"
)

func Test_filter(t *testing.T) {
	type args struct {
		config *corev1.ConfigMap
	}
	tests := []struct {
		name string
		args args
		want bool
	}{
		{
			name: "configMap为空",
			args: args{
				config: nil,
			},
			want: false,
		},
		{
			name: "configMap没有设置Labels",
			args: args{
				config: &corev1.ConfigMap{},
			},
			want: false,
		},
		{
			name: "configMap没有设置type标签",
			args: func() args {
				labelsMap := make(map[string]string)
				labelsMap["name"] = "baidu"

				return args{
					config: &corev1.ConfigMap{ObjectMeta: metav1.ObjectMeta{Name: "audit", Labels: labelsMap}},
				}
			}(),
			want: false,
		},
		{
			name: "configMap没有设置type=audit-configmap标签",
			args: func() args {
				labelsMap := make(map[string]string)
				labelsMap["type"] = "baidu"

				return args{
					config: &corev1.ConfigMap{ObjectMeta: metav1.ObjectMeta{Name: "audit", Labels: labelsMap}},
				}
			}(),
			want: false,
		},
		{
			name: "configMap设置type=audit-configmap标签",
			args: func() args {
				labelsMap := make(map[string]string)
				labelsMap["type"] = "audit-configmap"

				return args{
					config: &corev1.ConfigMap{ObjectMeta: metav1.ObjectMeta{Name: "audit", Labels: labelsMap}},
				}
			}(),
			want: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := utils2.ConfigFilter(tt.args.config); got != tt.want {
				t.Errorf("filter() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestModifyStandAloneApiServe_SingleInstance(t *testing.T) {
	// 测试单实例环境下的行为
	fakeClient := fake.NewSimpleClientset()

	// 创建一个Controller实例，但不创建任何审计Pod（模拟单实例）
	controller := &Controller{
		kubeClient: fakeClient,
		leaseMutex: NewLeaseMutex(fakeClient),
	}

	ctx := context.Background()

	// 测试needsCoordination应该返回false（没有审计Pod）
	needsCoord := controller.needsCoordination(ctx)
	if needsCoord {
		t.Error("needsCoordination should return false when no audit pods exist")
	}

	// 由于没有mock utils函数，这里只测试协调逻辑
	// 实际的executeAPIServerModification会调用utils函数，但我们主要测试协调逻辑
}

func TestModifyStandAloneApiServe_MultiInstance(t *testing.T) {
	// 测试多实例环境下的协调行为
	fakeClient := fake.NewSimpleClientset()

	// 创建多个审计Pod来模拟多实例环境
	for i := 0; i < 3; i++ {
		pod := &corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      fmt.Sprintf("external-auditor-%d", i),
				Namespace: "kube-system",
				Labels:    map[string]string{"component": "kube-external-auditor"},
			},
			Status: corev1.PodStatus{
				Phase: corev1.PodRunning,
			},
		}
		fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), pod, metav1.CreateOptions{})
	}

	controller := &Controller{
		kubeClient: fakeClient,
		leaseMutex: NewLeaseMutex(fakeClient),
	}

	ctx := context.Background()

	// 测试needsCoordination应该返回true（有多个审计Pod）
	needsCoord := controller.needsCoordination(ctx)
	if !needsCoord {
		t.Error("needsCoordination should return true when multiple audit pods exist")
	}
}

func TestNeedsCoordination(t *testing.T) {
	tests := []struct {
		name        string
		podCount    int
		expected    bool
		description string
	}{
		{
			name:        "NoPods",
			podCount:    0,
			expected:    false,
			description: "No coordination needed when no pods exist",
		},
		{
			name:        "SinglePod",
			podCount:    1,
			expected:    false,
			description: "No coordination needed for single pod",
		},
		{
			name:        "MultiplePods",
			podCount:    3,
			expected:    true,
			description: "Coordination needed for multiple pods",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			fakeClient := fake.NewSimpleClientset()

			// 创建指定数量的审计Pod
			for i := 0; i < tt.podCount; i++ {
				pod := &corev1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Name:      fmt.Sprintf("external-auditor-%d", i),
						Namespace: "kube-system",
						Labels:    map[string]string{"component": "kube-external-auditor"},
					},
					Status: corev1.PodStatus{
						Phase: corev1.PodRunning,
					},
				}
				fakeClient.CoreV1().Pods("kube-system").Create(context.Background(), pod, metav1.CreateOptions{})
			}

			controller := &Controller{
				kubeClient: fakeClient,
				leaseMutex: NewLeaseMutex(fakeClient),
			}

			result := controller.needsCoordination(context.Background())
			if result != tt.expected {
				t.Errorf("%s: expected %v, got %v", tt.description, tt.expected, result)
			}
		})
	}
}
