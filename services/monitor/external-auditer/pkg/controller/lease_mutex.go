package controller

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	coordinationv1 "k8s.io/api/coordination/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

const (
	// Lease相关常量
	LeaseResourceName      = "apiserver-restart-lock"
	LeaseNamespace         = "kube-system"
	DefaultLeaseDuration   = int32(300) // 5分钟租约时间
	LeaseRenewInterval     = 100        // 续约间隔（秒）
	MaxLeaseWaitTime       = 30 * time.Minute
	APIServerHealthTimeout = 5 * time.Minute
	HealthCheckInterval    = 10 * time.Second
)

// LeaseMutex 基于Lease的互斥锁
type LeaseMutex struct {
	client        kubernetes.Interface
	instanceID    string
	leaseDuration int32
}

// LeaseStatus Lease状态信息
type LeaseStatus struct {
	Holder       string    `json:"holder"`
	AcquiredAt   time.Time `json:"acquired_at"`
	RenewedAt    time.Time `json:"renewed_at"`
	Duration     int32     `json:"duration_seconds"`
	IsExpired    bool      `json:"is_expired"`
	Operation    string    `json:"operation,omitempty"`
	Status       string    `json:"status,omitempty"`
}

// NewLeaseMutex 创建基于Lease的互斥锁
func NewLeaseMutex(client kubernetes.Interface) *LeaseMutex {
	// 生成唯一实例ID
	instanceID := fmt.Sprintf("instance-%d-%d",
		time.Now().UnixNano(),
		rand.Intn(10000))

	return &LeaseMutex{
		client:        client,
		instanceID:    instanceID,
		leaseDuration: DefaultLeaseDuration,
	}
}

// ExecuteWithLease 使用Lease锁执行操作
func (l *LeaseMutex) ExecuteWithLease(ctx context.Context, operation string, executeFunc func() error) error {
	logger.Infof(ctx, "Instance %s attempting to acquire lease for operation: %s", l.instanceID, operation)

	// 1. 获取Lease锁
	if err := l.AcquireLease(ctx, operation); err != nil {
		return fmt.Errorf("failed to acquire lease: %v", err)
	}

	logger.Infof(ctx, "Instance %s successfully acquired lease", l.instanceID)

	// 2. 启动续约goroutine
	renewCtx, cancelRenew := context.WithCancel(ctx)
	defer cancelRenew()
	go l.startLeaseRenewal(renewCtx)

	// 3. 确保释放锁
	defer func() {
		if err := l.ReleaseLease(context.Background()); err != nil {
			logger.Warnf(ctx, "Failed to release lease: %v", err)
		}
	}()

	// 4. 更新状态为执行中
	l.updateLeaseAnnotation(ctx, "status", "executing")

	// 5. 执行操作
	logger.Infof(ctx, "Instance %s executing operation: %s", l.instanceID, operation)
	if err := executeFunc(); err != nil {
		l.updateLeaseAnnotation(ctx, "status", "failed")
		return fmt.Errorf("operation failed: %v", err)
	}

	// 6. 更新状态为等待健康检查
	l.updateLeaseAnnotation(ctx, "status", "waiting_health")

	// 7. 等待APIServer健康检查
	logger.Infof(ctx, "Instance %s waiting for APIServer to become healthy", l.instanceID)
	if err := l.waitForAPIServerHealthy(ctx); err != nil {
		logger.Warnf(ctx, "APIServer health check failed: %v", err)
		l.updateLeaseAnnotation(ctx, "status", "health_check_failed")
		// 不返回错误，允许继续流程
	} else {
		logger.Infof(ctx, "Instance %s APIServer health check passed", l.instanceID)
		l.updateLeaseAnnotation(ctx, "status", "completed")
	}

	return nil
}

// AcquireLease 获取Lease锁
func (l *LeaseMutex) AcquireLease(ctx context.Context, operation string) error {
	timeout := MaxLeaseWaitTime
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	// 随机初始延迟，避免雷群效应
	initialDelay := time.Duration(rand.Intn(5000)) * time.Millisecond
	time.Sleep(initialDelay)

	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("timeout waiting for lease")
		case <-ticker.C:
			acquired, err := l.tryAcquireLease(ctx, operation)
			if err != nil {
				logger.Warnf(ctx, "Error trying to acquire lease: %v", err)
				continue
			}

			if acquired {
				return nil
			}

			// 检查当前Lease状态
			l.logLeaseStatus(ctx)
		}
	}
}

// tryAcquireLease 尝试获取Lease
func (l *LeaseMutex) tryAcquireLease(ctx context.Context, operation string) (bool, error) {
	now := metav1.MicroTime{Time: time.Now()}

	lease := &coordinationv1.Lease{
		ObjectMeta: metav1.ObjectMeta{
			Name:      LeaseResourceName,
			Namespace: LeaseNamespace,
			Annotations: map[string]string{
				"operation": operation,
				"status":    "initializing",
			},
		},
		Spec: coordinationv1.LeaseSpec{
			HolderIdentity:       &l.instanceID,
			LeaseDurationSeconds: &l.leaseDuration,
			AcquireTime:          &now,
			RenewTime:            &now,
		},
	}

	// 尝试创建Lease
	_, err := l.client.CoordinationV1().Leases(LeaseNamespace).Create(ctx, lease, metav1.CreateOptions{})
	if err == nil {
		// 创建成功，获得锁
		return true, nil
	}

	if !errors.IsAlreadyExists(err) {
		return false, err
	}

	// Lease已存在，检查是否可以接管
	return l.tryTakeOverLease(ctx, operation)
}

// tryTakeOverLease 尝试接管Lease
func (l *LeaseMutex) tryTakeOverLease(ctx context.Context, operation string) (bool, error) {
	// 获取现有Lease
	existingLease, err := l.client.CoordinationV1().Leases(LeaseNamespace).Get(ctx,
		LeaseResourceName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			// Lease已被删除，重新尝试创建
			return l.tryAcquireLease(ctx, operation)
		}
		return false, err
	}

	// 检查Lease是否过期
	if l.isLeaseExpired(existingLease) {
		logger.Infof(ctx, "Lease expired, taking over from %s",
			*existingLease.Spec.HolderIdentity)

		// 更新Lease信息
		now := metav1.MicroTime{Time: time.Now()}
		existingLease.Spec.HolderIdentity = &l.instanceID
		existingLease.Spec.AcquireTime = &now
		existingLease.Spec.RenewTime = &now

		// 更新annotations
		if existingLease.Annotations == nil {
			existingLease.Annotations = make(map[string]string)
		}
		existingLease.Annotations["operation"] = operation
		existingLease.Annotations["status"] = "initializing"

		_, err = l.client.CoordinationV1().Leases(LeaseNamespace).Update(ctx,
			existingLease, metav1.UpdateOptions{})

		return err == nil, err
	}

	// Lease未过期，无法获取
	return false, nil
}

// isLeaseExpired 检查Lease是否过期
func (l *LeaseMutex) isLeaseExpired(lease *coordinationv1.Lease) bool {
	if lease.Spec.RenewTime == nil || lease.Spec.LeaseDurationSeconds == nil {
		return true // 无效Lease，可以接管
	}

	expireTime := lease.Spec.RenewTime.Add(time.Duration(*lease.Spec.LeaseDurationSeconds) * time.Second)
	return time.Now().After(expireTime)
}

// startLeaseRenewal 启动Lease续约
func (l *LeaseMutex) startLeaseRenewal(ctx context.Context) {
	renewInterval := time.Duration(LeaseRenewInterval) * time.Second
	ticker := time.NewTicker(renewInterval)
	defer ticker.Stop()

	logger.Debugf(ctx, "Starting lease renewal with interval %v", renewInterval)

	for {
		select {
		case <-ctx.Done():
			logger.Debugf(ctx, "Lease renewal stopped")
			return
		case <-ticker.C:
			if err := l.RenewLease(ctx); err != nil {
				logger.Errorf(ctx, "Failed to renew lease: %v", err)
				return
			}
		}
	}
}

// RenewLease 续约Lease
func (l *LeaseMutex) RenewLease(ctx context.Context) error {
	lease, err := l.client.CoordinationV1().Leases(LeaseNamespace).Get(ctx,
		LeaseResourceName, metav1.GetOptions{})
	if err != nil {
		return err
	}

	// 检查是否还是当前实例持有
	if lease.Spec.HolderIdentity == nil || *lease.Spec.HolderIdentity != l.instanceID {
		return fmt.Errorf("lease no longer held by current instance")
	}

	// 更新续约时间
	now := metav1.MicroTime{Time: time.Now()}
	lease.Spec.RenewTime = &now

	_, err = l.client.CoordinationV1().Leases(LeaseNamespace).Update(ctx, lease, metav1.UpdateOptions{})
	if err == nil {
		logger.Debugf(ctx, "Lease renewed successfully")
	}
	return err
}

// ReleaseLease 释放Lease
func (l *LeaseMutex) ReleaseLease(ctx context.Context) error {
	logger.Infof(ctx, "Instance %s releasing lease", l.instanceID)

	err := l.client.CoordinationV1().Leases(LeaseNamespace).Delete(ctx,
		LeaseResourceName, metav1.DeleteOptions{})

	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to release lease: %v", err)
	}

	logger.Infof(ctx, "Instance %s successfully released lease", l.instanceID)
	return nil
}
