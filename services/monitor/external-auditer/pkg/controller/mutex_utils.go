package controller

import (
	"context"
	"fmt"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
)

// MutexUtils 互斥锁工具类
type MutexUtils struct {
	kubeClient kubernetes.Interface
}

// NewMutexUtils 创建互斥锁工具
func NewMutexUtils(kubeClient kubernetes.Interface) *MutexUtils {
	return &MutexUtils{
		kubeClient: kubeClient,
	}
}

// LockInfo 锁信息
type LockInfo struct {
	Holder      string    `json:"holder"`
	LockedAt    time.Time `json:"locked_at"`
	LastUpdated time.Time `json:"last_updated"`
	Operation   string    `json:"operation"`
	Status      string    `json:"status"`
	Timeout     int       `json:"timeout_seconds"`
	IsExpired   bool      `json:"is_expired"`
}

// GetLockInfo 获取锁信息
func (u *MutexUtils) GetLockInfo(ctx context.Context) (*LockInfo, error) {
	cm, err := u.kubeClient.CoreV1().ConfigMaps(LockNamespace).Get(ctx,
		LockConfigMapName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return nil, nil // 没有锁
		}
		return nil, fmt.Errorf("failed to get lock info: %v", err)
	}

	info := &LockInfo{
		Holder:    cm.Data["lock_holder"],
		Operation: cm.Data["operation"],
		Status:    cm.Data["status"],
	}

	// 解析锁定时间
	if lockedAtStr := cm.Data["locked_at"]; lockedAtStr != "" {
		if lockedAt, err := time.Parse(time.RFC3339, lockedAtStr); err == nil {
			info.LockedAt = lockedAt
		}
	}

	// 解析最后更新时间
	if lastUpdatedStr := cm.Data["last_updated"]; lastUpdatedStr != "" {
		if lastUpdated, err := time.Parse(time.RFC3339, lastUpdatedStr); err == nil {
			info.LastUpdated = lastUpdated
		}
	}

	// 解析超时时间
	if timeoutStr := cm.Data["lock_timeout"]; timeoutStr != "" {
		if timeout, err := time.ParseDuration(timeoutStr + "s"); err == nil {
			info.Timeout = int(timeout.Seconds())
			// 检查是否过期
			info.IsExpired = time.Since(info.LockedAt) > timeout
		}
	}

	return info, nil
}

// IsLocked 检查是否有锁
func (u *MutexUtils) IsLocked(ctx context.Context) (bool, error) {
	info, err := u.GetLockInfo(ctx)
	if err != nil {
		return false, err
	}
	return info != nil, nil
}

// ForceReleaseLock 强制释放锁
func (u *MutexUtils) ForceReleaseLock(ctx context.Context) error {
	logger.Warnf(ctx, "Force releasing lock")

	err := u.kubeClient.CoreV1().ConfigMaps(LockNamespace).Delete(ctx,
		LockConfigMapName, metav1.DeleteOptions{})

	if err != nil && !errors.IsNotFound(err) {
		return fmt.Errorf("failed to force release lock: %v", err)
	}

	logger.Infof(ctx, "Lock force released successfully")
	return nil
}

// WaitForLockRelease 等待锁释放
func (u *MutexUtils) WaitForLockRelease(ctx context.Context, timeout time.Duration) error {
	timeoutCtx, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()

	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeoutCtx.Done():
			return fmt.Errorf("timeout waiting for lock release")
		case <-ticker.C:
			locked, err := u.IsLocked(ctx)
			if err != nil {
				return err
			}
			if !locked {
				return nil
			}
		}
	}
}

// PrintLockStatus 打印锁状态
func (u *MutexUtils) PrintLockStatus(ctx context.Context) {
	info, err := u.GetLockInfo(ctx)
	if err != nil {
		logger.Errorf(ctx, "Failed to get lock info: %v", err)
		return
	}

	if info == nil {
		logger.Infof(ctx, "=== Lock Status: UNLOCKED ===")
		return
	}

	logger.Infof(ctx, "=== Lock Status ===")
	logger.Infof(ctx, "Holder: %s", info.Holder)
	logger.Infof(ctx, "Operation: %s", info.Operation)
	logger.Infof(ctx, "Status: %s", info.Status)
	logger.Infof(ctx, "Locked At: %s", info.LockedAt.Format("2006-01-02 15:04:05"))
	if !info.LastUpdated.IsZero() {
		logger.Infof(ctx, "Last Updated: %s", info.LastUpdated.Format("2006-01-02 15:04:05"))
	}
	logger.Infof(ctx, "Timeout: %d seconds", info.Timeout)
	logger.Infof(ctx, "Is Expired: %t", info.IsExpired)
	if info.IsExpired {
		logger.Warnf(ctx, "Lock has expired and can be taken over")
	}

	// 根据状态提供更详细的信息
	switch info.Status {
	case "executing":
		logger.Infof(ctx, "Status Detail: APIServer modification in progress")
	case "waiting_health":
		logger.Infof(ctx, "Status Detail: Waiting for APIServer to become healthy")
	case "completed":
		logger.Infof(ctx, "Status Detail: Operation completed successfully")
	case "failed":
		logger.Warnf(ctx, "Status Detail: Operation failed")
	case "health_check_failed":
		logger.Warnf(ctx, "Status Detail: Health check failed (operation may have succeeded)")
	}

	logger.Infof(ctx, "==================")
}

// GetAuditPodsInfo 获取审计Pod信息
func (u *MutexUtils) GetAuditPodsInfo(ctx context.Context) ([]PodInfo, error) {
	pods, err := u.kubeClient.CoreV1().Pods("kube-system").List(ctx, metav1.ListOptions{
		LabelSelector: "component=kube-external-auditor",
	})
	if err != nil {
		return nil, fmt.Errorf("failed to list audit pods: %v", err)
	}

	var podInfos []PodInfo
	for _, pod := range pods.Items {
		podInfos = append(podInfos, PodInfo{
			Name:      pod.Name,
			NodeName:  pod.Spec.NodeName,
			Phase:     string(pod.Status.Phase),
			PodIP:     pod.Status.PodIP,
			CreatedAt: pod.CreationTimestamp.Time,
		})
	}

	return podInfos, nil
}

// PodInfo Pod信息
type PodInfo struct {
	Name      string    `json:"name"`
	NodeName  string    `json:"node_name"`
	Phase     string    `json:"phase"`
	PodIP     string    `json:"pod_ip"`
	CreatedAt time.Time `json:"created_at"`
}

// PrintAuditPodsInfo 打印审计Pod信息
func (u *MutexUtils) PrintAuditPodsInfo(ctx context.Context) {
	pods, err := u.GetAuditPodsInfo(ctx)
	if err != nil {
		logger.Errorf(ctx, "Failed to get audit pods info: %v", err)
		return
	}

	logger.Infof(ctx, "=== Audit Pods Info ===")
	logger.Infof(ctx, "Total Pods: %d", len(pods))
	
	runningCount := 0
	for _, pod := range pods {
		if pod.Phase == "Running" {
			runningCount++
		}
		logger.Infof(ctx, "Pod: %s, Node: %s, Phase: %s, IP: %s, Created: %s",
			pod.Name, pod.NodeName, pod.Phase, pod.PodIP,
			pod.CreatedAt.Format("2006-01-02 15:04:05"))
	}
	
	logger.Infof(ctx, "Running Pods: %d", runningCount)
	logger.Infof(ctx, "Coordination Needed: %t", runningCount > 1)
	logger.Infof(ctx, "======================")
}

// CheckCoordinationHealth 检查协调机制健康状态
func (u *MutexUtils) CheckCoordinationHealth(ctx context.Context) error {
	// 检查是否有过期的锁
	info, err := u.GetLockInfo(ctx)
	if err != nil {
		return fmt.Errorf("failed to check lock info: %v", err)
	}

	if info != nil && info.IsExpired {
		logger.Warnf(ctx, "Found expired lock held by %s since %s",
			info.Holder, info.LockedAt.Format("2006-01-02 15:04:05"))
		
		// 可以选择自动清理过期锁
		if autoClean := ctx.Value("auto_clean_expired_lock"); autoClean == true {
			logger.Infof(ctx, "Auto-cleaning expired lock")
			return u.ForceReleaseLock(ctx)
		}
	}

	return nil
}

// GetCoordinationStats 获取协调统计信息
func (u *MutexUtils) GetCoordinationStats(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 获取Pod信息
	pods, err := u.GetAuditPodsInfo(ctx)
	if err != nil {
		return nil, err
	}

	runningPods := 0
	for _, pod := range pods {
		if pod.Phase == "Running" {
			runningPods++
		}
	}

	stats["total_pods"] = len(pods)
	stats["running_pods"] = runningPods
	stats["coordination_needed"] = runningPods > 1

	// 获取锁信息
	lockInfo, err := u.GetLockInfo(ctx)
	if err != nil {
		return nil, err
	}

	if lockInfo != nil {
		stats["lock_exists"] = true
		stats["lock_holder"] = lockInfo.Holder
		stats["lock_operation"] = lockInfo.Operation
		stats["lock_expired"] = lockInfo.IsExpired
		stats["lock_duration"] = time.Since(lockInfo.LockedAt).Seconds()
	} else {
		stats["lock_exists"] = false
	}

	return stats, nil
}
