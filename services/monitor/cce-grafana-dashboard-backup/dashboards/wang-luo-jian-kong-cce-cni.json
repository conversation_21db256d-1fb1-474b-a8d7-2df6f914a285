{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 72, "iteration": 1657079078216, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 21, "panels": [], "title": "Overview", "type": "row"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 3, "w": 24, "x": 0, "y": 1}, "id": 28, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum by (api,code) (increase(bce_openapi_latency_count{region=\"$region\",clusterID=~\"$clusterID\",api=~\"AddPrivateIP|DeletePrivateIP|CreateENI|DeleteENI\", code=\"200\"}[$__interval]))", "interval": "1m", "legendFormat": "{{api}}", "queryType": "randomWalk", "refId": "A"}], "title": "Network Resource Changes", "transparent": true, "type": "stat"}, {"datasource": "${datasource}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "red", "value": null}]}}, "overrides": []}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 4}, "id": 29, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["sum"], "fields": "", "values": false}, "text": {}, "textMode": "value_and_name"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(increase(bce_openapi_latency_count{region=\"$region\",clusterID=~\"$clusterID\",api=~\"AddPrivateIP|DeletePrivateIP|CreateENI|DeleteENI\", code!~\"200|404\"}[$__interval])) by (api) ", "interval": "1m", "legendFormat": "{{api}} Failed", "queryType": "randomWalk", "refId": "A"}], "transparent": true, "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": true, "min": true, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(cni_rpc_error_count{region=\"$region\",clusterID=~\"$clusterID\", rpc_api=\"AllocateIP\"}[$__rate_interval]))by(rpc_api)", "interval": "", "legendFormat": "Failed", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(irate(cni_rpc_latency_count{region=\"$region\",clusterID=~\"$clusterID\",error=\"false\",rpc_api=\"AllocateIP\"}[$__interval])) by (error)", "hide": false, "interval": "", "legendFormat": "Succ", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "sum(rate(cni_rpc_rejected_count{region=\"$region\",clusterID=~\"$clusterID\", rpc_api=\"AllocateIP\"}[$__rate_interval]))by(rpc_api)", "hide": false, "interval": "", "legendFormat": "Ratelimited", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CNI Alloc QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1111", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1112", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 6}, "hiddenSeries": false, "id": 30, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideZero": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "total", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(irate(bce_openapi_latency_count{region=\"$region\",clusterID=~\"$clusterID\",code=\"200\"}[$__interval]))", "interval": "1m", "legendFormat": "Succ", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(irate(bce_openapi_latency_count{region=\"$region\",clusterID=~\"$clusterID\", code!~\"200|421|404\"}[$__interval]))", "hide": false, "interval": "1m", "legendFormat": "Failed", "queryType": "randomWalk", "refId": "B"}, {"exemplar": true, "expr": "sum(irate(bce_openapi_latency_count{region=\"$region\",clusterID=~\"$clusterID\", code=\"421\"}[$__interval]))", "hide": false, "interval": "", "legendFormat": "Ratelimited", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BCE Open API QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:513", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:514", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "max by (vpc,subnet) (subnet_available_ip_count{region=\"$region\",clusterID=~\"$clusterID\"})", "hide": false, "interval": "", "legendFormat": "{{vpc}}/{{subnet}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Subnet Available IP", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1566", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1567", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(multi_eni_multi_ip_eniip_count{region=\"$region\",clusterID=~\"$clusterID\"})", "hide": false, "interval": "", "legendFormat": "eni ip count", "refId": "C"}, {"exemplar": true, "expr": "sum(multi_eni_multi_ip_eni_count{region=\"$region\",clusterID=~\"$clusterID\"})", "hide": false, "interval": "", "legendFormat": "eni count", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node ENI Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1566", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1567", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 12, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "hiddenSeries": false, "id": 10, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": true, "min": true, "rightSide": false, "show": true, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(cni_rpc_error_count{region=\"$region\",clusterID=~\"$clusterID\"}[$__rate_interval]))by(rpc_api)", "interval": "", "legendFormat": "{{rpc_api}} Error", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "sum(rate(cni_rpc_rejected_count{region=\"$region\",clusterID=~\"$clusterID\"}[1m]))by(rpc_api)", "hide": false, "interval": "", "legendFormat": "{{rpc_api}} Rejected", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CNI Error QPS", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1111", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1112", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 2}, "hiddenSeries": false, "id": 17, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.50,\n          sum by (le, rpc_api, error)(\n          rate(cni_rpc_latency_bucket{region=\"$region\",clusterID=~\"$clusterID\"}[$__rate_interval]))\n        )", "hide": false, "interval": "", "legendFormat": "{{rpc_api}}/err={{error}} P50", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CNI Latency P50", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1111", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:1112", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": true, "hideZero": false, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, rpc_api, error)(\n          rate(cni_rpc_latency_bucket{region=\"$region\",clusterID=~\"$clusterID\"}[$__rate_interval]))\n        )", "interval": "", "legendFormat": "{{rpc_api}}/err={{error}} P99", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CNI Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1111", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:1112", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 25, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kube_pod_status_phase{job=\"kubernetes-service\",app=\"user-cluster-kube-state-metrics\",clusterID=~\"$clusterID\",namespace=\"kube-system\",pod=~\"cce-eni-ipam.+\"}", "interval": "", "legendFormat": "", "queryType": "randomWalk", "refId": "A"}, {"exemplar": true, "expr": "", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "IPAM Pod Status", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:464", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:465", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "CNI", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 14, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 3}, "hiddenSeries": false, "id": 6, "legend": {"alignAsTable": true, "avg": true, "current": true, "hideZero": true, "max": true, "min": true, "rightSide": false, "show": true, "sort": "total", "sortDesc": true, "total": true, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (api,code) (increase(bce_openapi_latency_count{region=\"$region\",clusterID=~\"$clusterID\"}[$__interval]))", "interval": "1m", "legendFormat": "{{api}} {{code}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BCE Open API Request ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:513", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:514", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 4, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, api)(\n          rate(bce_openapi_latency_bucket{region=\"$region\",clusterID=~\"$clusterID\"}[5m]))\n        )", "hide": false, "interval": "", "legendFormat": "{{api}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BCE Open API Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1566", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:1567", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 23, "legend": {"alignAsTable": true, "avg": true, "current": true, "max": true, "min": true, "show": true, "sort": "avg", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.50,\n          sum by (le, api)(\n          rate(bce_openapi_latency_bucket{region=\"$region\",clusterID=~\"$clusterID\"}[5m]))\n        )", "hide": false, "interval": "", "legendFormat": "{{api}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BCE Open API Latency P50", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1566", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:1567", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 21}, "hiddenSeries": false, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.99,\n          sum by (le, api, code)(\n          rate(bce_openapi_latency_bucket{region=\"$region\",clusterID=~\"$clusterID\", api=~\"BBC.*\"}[5m]))\n        )", "hide": false, "interval": "", "legendFormat": "{{api}}/{{code}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BBC 主网卡辅助 IP Open API Latency P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1566", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:1567", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 21}, "hiddenSeries": false, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n          0.50,\n          sum by (le, api, code)(\n          rate(bce_openapi_latency_bucket{region=\"$region\",clusterID=~\"$clusterID\", api=~\"BBC.*\"}[5m]))\n        )", "hide": false, "interval": "", "legendFormat": "{{api}}/{{code}}", "queryType": "randomWalk", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "BBC 主网卡辅助 IP Open API Latency P50", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1566", "format": "ms", "logBase": 1, "show": true}, {"$$hashKey": "object:1567", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "OpenAPI", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["plugin", "cni", "CCE-USER-K8S"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bj", "value": "bj"}, "datasource": "${datasource}", "definition": "label_values(bce_openapi_latency_count, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "region", "options": [], "query": {"query": "label_values(bce_openapi_latency_count, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": true, "tags": [], "text": ["/cce-qxpixa2x", "/cce-mfhy9jot", "/cce-520adkqr"], "value": ["cce-qxpixa2x", "cce-mfhy9jot", "cce-520adkqr"]}, "datasource": "${datasource}", "definition": "label_values(bce_openapi_latency_count{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": true, "name": "clusterID", "options": [], "query": {"query": "label_values(bce_openapi_latency_count{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 2, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "网络监控 / CCE CNI", "uid": "aaa", "version": 11}