{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 163, "iteration": 1657078897177, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 0}, "hiddenSeries": false, "id": 55, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(kube_pod_container_status_ready{region=\"$region\", clusterID=\"$clusterID\", container=\"kube-proxy\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "total", "refId": "A"}, {"exemplar": true, "expr": "count(kube_pod_container_status_ready{region=\"$region\", clusterID=\"$clusterID\", container=\"kube-proxy\"} == 1)", "hide": false, "interval": "", "legendFormat": "ready", "refId": "B"}, {"exemplar": true, "expr": "count(kube_pod_container_status_ready{region=\"$region\", clusterID=\"$clusterID\", container=\"kube-proxy\"} == 0)", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KubeProxy Ready", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 105, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kube_pod_container_status_ready{region=\"$region\", clusterID=\"$clusterID\", container=\"kube-proxy\"} == 0", "hide": false, "interval": "", "legendFormat": "", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "KubeProxy Not Ready", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dateTimeAsSystem"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "last_queued_timestamp"}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 6}, "id": 106, "links": [], "maxDataPoints": 100, "options": {"showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kubeproxy_sync_proxy_rules_last_queued_timestamp_seconds{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"} * 1000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "kubeproxy_sync_proxy_rules_last_queued_timestamp_seconds", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["clusterID", "k8s_app", "namespace_name", "pod_name", "host_ip", "Value #B"]}}}], "type": "table"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "dateTimeAsSystem"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "displayName", "value": "last_timestamp"}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 6}, "id": 108, "links": [], "maxDataPoints": 100, "options": {"showHeader": true}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kubeproxy_sync_proxy_rules_last_timestamp_seconds{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"} * 1000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "kubeproxy_sync_proxy_rules_last_timestamp_seconds", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["clusterID", "k8s_app", "namespace_name", "pod_name", "host_ip", "Value #B"]}}}], "type": "table"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 98, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubeproxy_network_programming_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (le, instance)\n)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_network_programming_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 12}, "hiddenSeries": false, "id": 99, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubeproxy_sync_proxy_rules_duration_seconds_bucket{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (le, instance)\n)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_sync_proxy_rules_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 18}, "hiddenSeries": false, "id": 100, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kubeproxy_sync_proxy_rules_endpoint_changes_pending{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_sync_proxy_rules_endpoint_changes_pending", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 18}, "hiddenSeries": false, "id": 101, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(kubeproxy_sync_proxy_rules_endpoint_changes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_sync_proxy_rules_endpoint_changes_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 24}, "hiddenSeries": false, "id": 103, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kubeproxy_sync_proxy_rules_service_changes_pending{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_sync_proxy_rules_service_changes_pending", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 104, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(kubeproxy_sync_proxy_rules_service_changes_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_sync_proxy_rules_service_changes_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "ops"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 30}, "hiddenSeries": false, "id": 102, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": false}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "irate(kubeproxy_sync_proxy_rules_iptables_restore_failures_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubeproxy_sync_proxy_rules_iptables_restore_failures_total", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "ops", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["node", "kubelet", "CCE-USER-K8S"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bd", "value": "bd"}, "datasource": "${datasource}", "definition": "label_values(kubeproxy_sync_proxy_rules_endpoint_changes_pending, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(kubeproxy_sync_proxy_rules_endpoint_changes_pending, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-87et03rc", "value": "cce-87et03rc"}, "datasource": "${datasource}", "definition": "label_values(kubeproxy_sync_proxy_rules_endpoint_changes_pending{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(kubeproxy_sync_proxy_rules_endpoint_changes_pending{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "************:10249", "value": "************:10249"}, "datasource": "${datasource}", "definition": "label_values(kubeproxy_sync_proxy_rules_endpoint_changes_pending{region=\"$region\",  clusterID=\"$clusterID\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": "instance", "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(kubeproxy_sync_proxy_rules_endpoint_changes_pending{region=\"$region\",  clusterID=\"$clusterID\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "节点监控 / KubeProxy", "uid": "sLeWHE87zbbb-kube-proxy", "version": 13}