{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 126, "iteration": 1657078738459, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 26, "panels": [], "title": "SLO", "type": "row"}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 1}, "id": 59, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) ", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Node NotReady", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 4, "y": 1}, "hiddenSeries": false, "id": 55, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"}) by (node))", "hide": false, "instant": false, "interval": "", "legendFormat": "total", "refId": "A"}, {"exemplar": true, "expr": "count(count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 1) by (node)) ", "hide": false, "interval": "", "legendFormat": "ready", "refId": "B"}, {"exemplar": true, "expr": "count(count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) by (node)) ", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 14, "y": 1}, "hiddenSeries": false, "id": 48, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) by (node)", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node NotReady", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 6}, "id": 60, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 0)", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Pod NotReady", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 4, "y": 6}, "hiddenSeries": false, "id": 56, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "total", "refId": "A"}, {"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 1)", "hide": false, "interval": "", "legendFormat": "ready", "refId": "C"}, {"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 0)", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pod", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 5, "w": 10, "x": 14, "y": 6}, "hiddenSeries": false, "id": 97, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_status_ready{region=\"$region\", clusterID=\"$clusterID\", condition=\"true\"} == 0) by (instance)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pod NotReady", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 7, "w": 4, "x": 0, "y": 11}, "id": 99, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "max(histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\"}[$__rate_interval])) by (le, instance)\n))\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "timeFrom": null, "timeShift": null, "title": "Max(Pod Start Duration) P99", "type": "stat"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 20, "x": 4, "y": 11}, "hiddenSeries": false, "id": 98, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\"}[$__rate_interval])) by (le, instance)\n)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 18}, "id": 35, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 24, "x": 0, "y": 8}, "hiddenSeries": false, "id": 77, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", condition=\"Ready\", status=\"true\"} == 0) by (node)", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node NotReady", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 14}, "hiddenSeries": false, "id": 63, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\", status=\"true\"}", "hide": false, "interval": "", "legendFormat": "{{condition}} ", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Conditions: $node", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "filterable": false}, "mappings": [{"from": "", "id": 1, "text": "false", "to": "", "type": 1, "value": "0"}, {"from": "", "id": 2, "text": "true", "to": "", "type": 1, "value": "1"}], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #C"}, "properties": [{"id": "displayName", "value": "true"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 14}, "id": 62, "links": [], "maxDataPoints": 100, "options": {"showHeader": true, "sortBy": [{"desc": true, "displayName": "true"}]}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\", status=\"true\"}", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Node Conditions: $node", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["condition", "node", "status", "Value #C"]}}}], "type": "table"}], "title": "Node Ready Conditons", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 19}, "id": 65, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 69, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "kube_node_status_condition{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\", condition=\"Ready\", status=\"true\"}", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Ready", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 68, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "COUNT(kube_pod_info{region=\"$region\", clusterID=\"$clusterID\", node=\"$node\"}) by (node)", "hide": false, "interval": "", "legendFormat": "{{node}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pods on Node", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"Idle - Waiting for something to happen": "#052B51", "guest": "#9AC48A", "idle": "#052B51", "iowait": "#EAB839", "irq": "#BF1B00", "nice": "#C15C17", "softirq": "#E24D42", "steal": "#FCE2DE", "system": "#508642", "user": "#5195CE"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus-cce-87et03rc", "decimals": 2, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 15}, "hiddenSeries": false, "id": 114, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": 250, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode=\"system\",region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "10s", "intervalFactor": 1, "legendFormat": "System - Processes executing in kernel mode", "refId": "A", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='user',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "User - Normal processes executing in user mode", "refId": "B", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='nice',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Nice - Niced processes executing in user mode", "refId": "C", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='idle',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Idle - Waiting for something to happen", "refId": "D", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='iowait',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Iowait - Waiting for <PERSON><PERSON><PERSON> to complete", "refId": "E", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='irq',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Irq - Servicing interrupts", "refId": "F", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='softirq',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Softirq - Servicing softirqs", "refId": "G", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='steal',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Steal - Time spent in other operating systems when running in a virtualized environment", "refId": "H", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='guest',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Guest - Time spent running a virtual CPU for a guest operating system", "refId": "I", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "percentage", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap - Swap memory usage": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839", "Unused - Free memory unassigned": "#052B51"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus-cce-87et03rc", "decimals": 2, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 15}, "hiddenSeries": false, "id": 115, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": 350, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Hardware Corrupted - *./", "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "node_memory_MemTotal_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_MemFree_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_Buffers_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_Cached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_Slab_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_PageTables_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_SwapCached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Apps - Memory used by user-space applications", "refId": "A", "step": 240}, {"exemplar": true, "expr": "node_memory_PageTables_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "PageTables - Memory used to map between virtual and physical memory addresses", "refId": "B", "step": 240}, {"exemplar": true, "expr": "node_memory_SwapCached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "SwapCache - Memory that keeps track of pages that have been fetched from swap but not yet been modified", "refId": "C", "step": 240}, {"exemplar": true, "expr": "node_memory_Slab_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Slab - Memory used by the kernel to cache data structures for its own use (caches like inode, dentry, etc)", "refId": "D", "step": 240}, {"exemplar": true, "expr": "node_memory_Cached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Cache - Parked file data (file content) cache", "refId": "E", "step": 240}, {"exemplar": true, "expr": "node_memory_Buffers_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Buffers - Block device (e.g. harddisk) cache", "refId": "F", "step": 240}, {"exemplar": true, "expr": "node_memory_MemFree_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Unused - Free memory unassigned", "refId": "G", "step": 240}, {"exemplar": true, "expr": "(node_memory_SwapTotal_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_SwapFree_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Swap - Swap space used", "refId": "H", "step": 240}, {"exemplar": true, "expr": "node_memory_HardwareCorrupted_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working", "refId": "I", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Stack", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "bytes", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 15}, "hiddenSeries": false, "id": 107, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "node_filefd_allocated{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "legendFormat": "{{host_ip}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "node_filefd_allocated", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "custom.width", "value": 209}, {"id": "unit", "value": "dateTimeAsLocal"}, {"id": "displayName", "value": "create_time"}, {"id": "custom.filterable", "value": true}]}]}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 22}, "id": 31, "links": [], "maxDataPoints": 100, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "create_time"}]}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kube_pod_created{region=\"$region\", clusterID=\"$clusterID\"} * on(namespace, pod) group_left(node) kube_pod_info{region=\"$region\", clusterID=\"$clusterID\", node=~\"$node\"} * 1000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Pods  create_time on Node: $node", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["namespace", "node", "pod", "Value #B"]}}}], "type": "table"}], "title": "Node NotReady by <PERSON>ad", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 20}, "id": 67, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 10}, "hiddenSeries": false, "id": 95, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pleg_relist_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pleg_relist_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 10}, "hiddenSeries": false, "id": 96, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pleg_relist_interval_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pleg_relist_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Node NotReady by PLEG", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 21}, "id": 118, "panels": [{"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 5}, "id": 119, "links": [], "maxDataPoints": 100, "options": {"colorMode": "value", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "text": {}, "textMode": "auto"}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "sum(kubelet_running_pods{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$node\"})", "hide": false, "interval": "", "legendFormat": "not ready", "refId": "C"}], "timeFrom": null, "timeShift": null, "title": "Pods Count", "type": "stat"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 5}, "hiddenSeries": false, "id": 120, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(kubelet_running_containers{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$node\"})  by (container_state)", "hide": false, "interval": "", "legendFormat": "{{container_state}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Containers Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:3039", "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:3040", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Pod", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 22}, "id": 104, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 5}, "hiddenSeries": false, "id": 102, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\"}[$__rate_interval])) by (le, instance)\n)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 12}, "hiddenSeries": false, "id": 105, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", operation_type=\"run_podsandbox\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "run_podsandbox_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 19}, "hiddenSeries": false, "id": 106, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "node_filefd_allocated{job=\"kubernetes-pods\", host_ip!=\"\"}", "format": "time_series", "hide": false, "instant": false, "interval": "", "legendFormat": "{{host_ip}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:96", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"$$hashKey": "object:97", "format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "title": "Pod Create Latency", "type": "row"}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 58, "panels": [{"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 6}, "hiddenSeries": false, "id": 91, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": null, "displayMode": "auto", "filterable": true}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Value #B"}, "properties": [{"id": "custom.width", "value": 209}, {"id": "unit", "value": "dateTimeAsLocal"}, {"id": "displayName", "value": "create_time"}, {"id": "custom.filterable", "value": true}]}]}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 6}, "id": 100, "links": [], "maxDataPoints": 100, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "create_time"}]}, "pluginVersion": "7.5.0", "targets": [{"exemplar": true, "expr": "kube_pod_created{region=\"$region\", clusterID=\"$clusterID\"} * on(pod) group_left(node) kube_pod_info{region=\"$region\", clusterID=\"$clusterID\", node=~\"$node\"} * 1000", "format": "table", "hide": false, "instant": true, "interval": "", "legendFormat": "", "refId": "B"}], "title": "Pods  create_time on Node", "transformations": [{"id": "filterFieldsByName", "options": {"include": {"names": ["pod", "Value #B", "node"]}}}], "type": "table"}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 13}, "hiddenSeries": false, "id": 101, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_worker_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)", "hide": false, "interval": "", "legendFormat": "pod_worker_start", "refId": "A"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_cgroup_manager_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=~\"create|update\"}[$__rate_interval])) by (le, instance,operation_type)\n)", "hide": false, "interval": "", "legendFormat": "cgroup_manager {{operation_type}}", "refId": "B"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"run_podsandbox\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "runtime_operations {{operation_type}}", "refId": "C"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_network_plugin_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"set_up_pod\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "network_plugin_operations", "refId": "D"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"pull_image\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "runtime_operations {{operation_type}}", "refId": "E"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"pull_image\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "docker_operations {{operation_type}}", "refId": "F"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"create_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "runtime_operations {{operation_type}}", "refId": "G"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"create_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "docker_operations {{operation_type}}", "refId": "H"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"start_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "runtime_operations {{operation_type}}", "refId": "I"}, {"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"start_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "docker_operations {{operation_type}}", "refId": "J"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_start_duration_seconds  by step P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"Idle - Waiting for something to happen": "#052B51", "guest": "#9AC48A", "idle": "#052B51", "iowait": "#EAB839", "irq": "#BF1B00", "nice": "#C15C17", "softirq": "#E24D42", "steal": "#FCE2DE", "system": "#508642", "user": "#5195CE"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus-cce-87et03rc", "decimals": 2, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 0, "y": 20}, "hiddenSeries": false, "id": 111, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": 250, "sort": "current", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": true, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "repeat": null, "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode=\"system\",region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "10s", "intervalFactor": 1, "legendFormat": "System - Processes executing in kernel mode", "refId": "A", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='user',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "User - Normal processes executing in user mode", "refId": "B", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='nice',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Nice - Niced processes executing in user mode", "refId": "C", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='idle',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Idle - Waiting for something to happen", "refId": "D", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='iowait',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Iowait - Waiting for <PERSON><PERSON><PERSON> to complete", "refId": "E", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='irq',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Irq - Servicing interrupts", "refId": "F", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='softirq',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Softirq - Servicing softirqs", "refId": "G", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='steal',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Steal - Time spent in other operating systems when running in a virtualized environment", "refId": "H", "step": 240}, {"exemplar": true, "expr": "sum by (mode)(rate(node_cpu_seconds_total{mode='guest',region=\"$region\", clusterID=\"$clusterID\",host_ip=\"$node\"}[$__rate_interval])) * 100", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "Guest - Time spent running a virtual CPU for a guest operating system", "refId": "I", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CPU", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": "percentage", "logBase": 1, "max": "100", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {"Apps": "#629E51", "Buffers": "#614D93", "Cache": "#6D1F62", "Cached": "#511749", "Committed": "#508642", "Free": "#0A437C", "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working": "#CFFAFF", "Inactive": "#584477", "PageTables": "#0A50A1", "Page_Tables": "#0A50A1", "RAM_Free": "#E0F9D7", "Slab": "#806EB7", "Slab_Cache": "#E0752D", "Swap": "#BF1B00", "Swap - Swap memory usage": "#BF1B00", "Swap_Cache": "#C15C17", "Swap_Free": "#2F575E", "Unused": "#EAB839", "Unused - Free memory unassigned": "#052B51"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "prometheus-cce-87et03rc", "decimals": 2, "description": "", "fieldConfig": {"defaults": {"links": []}, "overrides": []}, "fill": 4, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 8, "y": 20}, "hiddenSeries": false, "id": 113, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sideWidth": 350, "sort": "max", "sortDesc": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxPerRow": 6, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "/.*Hardware Corrupted - *./", "stack": false}], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"exemplar": true, "expr": "node_memory_MemTotal_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_MemFree_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_Buffers_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_Cached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_Slab_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_PageTables_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_SwapCached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Apps - Memory used by user-space applications", "refId": "A", "step": 240}, {"exemplar": true, "expr": "node_memory_PageTables_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "PageTables - Memory used to map between virtual and physical memory addresses", "refId": "B", "step": 240}, {"exemplar": true, "expr": "node_memory_SwapCached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "interval": "", "intervalFactor": 1, "legendFormat": "SwapCache - Memory that keeps track of pages that have been fetched from swap but not yet been modified", "refId": "C", "step": 240}, {"exemplar": true, "expr": "node_memory_Slab_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Slab - Memory used by the kernel to cache data structures for its own use (caches like inode, dentry, etc)", "refId": "D", "step": 240}, {"exemplar": true, "expr": "node_memory_Cached_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Cache - Parked file data (file content) cache", "refId": "E", "step": 240}, {"exemplar": true, "expr": "node_memory_Buffers_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Buffers - Block device (e.g. harddisk) cache", "refId": "F", "step": 240}, {"exemplar": true, "expr": "node_memory_MemFree_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Unused - Free memory unassigned", "refId": "G", "step": 240}, {"exemplar": true, "expr": "(node_memory_SwapTotal_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"} - node_memory_SwapFree_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"})", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Swap - Swap space used", "refId": "H", "step": 240}, {"exemplar": true, "expr": "node_memory_HardwareCorrupted_bytes{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 1, "legendFormat": "Hardware Corrupted - Amount of RAM that the kernel identified as corrupted / not working", "refId": "I", "step": 240}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Memory Stack", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "bytes", "label": "bytes", "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": false}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 8, "x": 16, "y": 20}, "hiddenSeries": false, "id": 116, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "node_filefd_allocated{region=\"$region\", clusterID=\"$clusterID\", host_ip=\"$node\"}", "format": "time_series", "hide": false, "interval": "", "legendFormat": "{{host_ip}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "node_filefd_allocated", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 27}, "hiddenSeries": false, "id": 78, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_worker_start_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_worker_start_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 27}, "hiddenSeries": false, "id": 81, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"run_podsandbox\"}[$__rate_interval])) by (le, instance, operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_runtime_operations_duration_seconds P99 run_podsandbox", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 33}, "hiddenSeries": false, "id": 86, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_network_plugin_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"set_up_pod\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_network_plugin_operations_duration_seconds P99 set_up_pod", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 33}, "hiddenSeries": false, "id": 79, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_cgroup_manager_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=~\"create|update\"}[$__rate_interval])) by (le, instance,operation_type)\n)\n", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kube<PERSON>_cgroup_manager_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 40}, "hiddenSeries": false, "id": 92, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"pull_image\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " kubelet_runtime_operations_duration_seconds P99 pull_image", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 40}, "hiddenSeries": false, "id": 84, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"pull_image\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_docker_operations_duration_seconds P99 pull_image", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 46}, "hiddenSeries": false, "id": 82, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"create_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " kubelet_runtime_operations_duration_seconds P99 create_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 46}, "hiddenSeries": false, "id": 93, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"create_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_docker_operations_duration_seconds P99 create_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 52}, "hiddenSeries": false, "id": 88, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_runtime_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"start_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": " kubelet_runtime_operations_duration_seconds P99 start_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 52}, "hiddenSeries": false, "id": 85, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_docker_operations_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\", operation_type=\"start_container\"}[$__rate_interval])) by (le, instance, operation_type)\n)", "hide": false, "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_docker_operations_duration_seconds P99 start_container", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 58}, "hiddenSeries": false, "id": 90, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(kubelet_pod_worker_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kubelet_pod_worker_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "s"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 58}, "hiddenSeries": false, "id": 94, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": true, "min": false, "rightSide": false, "show": true, "sort": "min", "sortDesc": false, "total": false, "values": true}, "lines": false, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "histogram_quantile (\n    0.99,\n    sum(irate(storage_operation_duration_seconds_bucket{job=\"kubelet\", clusterID=\"$clusterID\", instance=~\"$node\"}[$__rate_interval])) by (le, instance)\n)\n", "hide": false, "interval": "", "legendFormat": "{{instance}}", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "storage_operation_duration_seconds P99", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "s", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "title": "Pod Create Latency Step", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["node", "kubelet", "CCE-USER-K8S"], "templating": {"list": [{"current": {"selected": true, "text": "prometheus-cce-87et03rc", "value": "prometheus-cce-87et03rc"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "bd", "value": "bd"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(apiserver_audit_level_total, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-87et03rc", "value": "cce-87et03rc"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "***********", "value": "***********"}, "datasource": "${datasource}", "definition": "label_values(kube_node_status_condition{region=\"$region\",  clusterID=\"$clusterID\"}, node)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "node", "multi": false, "name": "node", "options": [], "query": {"query": "label_values(kube_node_status_condition{region=\"$region\",  clusterID=\"$clusterID\"}, node)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-30m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "节点监控 / Kubelet", "uid": "sLeWHE87zbbb-kubelet", "version": 8}