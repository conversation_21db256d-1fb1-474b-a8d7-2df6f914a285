{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 107, "iteration": 1657078316461, "links": [], "panels": [{"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 26, "panels": [], "title": "SLO", "type": "row"}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "description": "", "fieldConfig": {"defaults": {"unit": "none"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 1}, "hiddenSeries": false, "id": 2, "legend": {"alignAsTable": false, "avg": false, "current": true, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "maxDataPoints": 100, "nullPointMode": "null", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "count(procstat_num_threads{job=\"master-metrics\", region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\", process_name=\"auditer\"} > 0)", "hide": false, "interval": "", "legendFormat": "up", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "kube-extender-auditor  Up", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:1986", "format": "none", "logBase": 1, "show": true}, {"$$hashKey": "object:1987", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"2xx": "green", "4xx": "yellow", "5xx": "red", "total": "blue"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 1}, "hiddenSeries": false, "id": 28, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(apiserver_audit_requests_rejected_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_audit_requests_rejected_total", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:117", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:118", "format": "ops", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": false, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 8}, "id": 30, "panels": [], "title": "APIServer Audit", "type": "row"}, {"aliasColors": {"2xx": "green", "4xx": "yellow", "5xx": "red", "total": "blue"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 9}, "hiddenSeries": false, "id": 3, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(apiserver_audit_event_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_audit_event_total", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:117", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:118", "format": "ops", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {"2xx": "green", "4xx": "yellow", "5xx": "red", "total": "blue"}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "${datasource}", "fieldConfig": {"defaults": {}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 9}, "hiddenSeries": false, "id": 27, "legend": {"alignAsTable": false, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 2, "links": [], "nullPointMode": "null as zero", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "7.5.0", "pointradius": 5, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"exemplar": true, "expr": "sum(rate(apiserver_audit_level_total{region=\"$region\", clusterID=\"$clusterID\", instance=~\"$instance\"}[$__rate_interval])) by (instance)", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{instance}}", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "apiserver_audit_level_total", "tooltip": {"shared": true, "sort": 2, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:117", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:118", "format": "ops", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"collapsed": true, "datasource": null, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 16}, "id": 32, "panels": [], "title": "Extender Auditor", "type": "row"}], "refresh": false, "schemaVersion": 27, "style": "dark", "tags": ["master", "apiserver", "CCE-USER-K8S"], "templating": {"list": [{"current": {"selected": false, "text": "cce-vm-cluster", "value": "cce-vm-cluster"}, "description": null, "error": null, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "current": {"selected": false, "text": "hkg", "value": "hkg"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total, region)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "region", "multi": false, "name": "region", "options": [], "query": {"query": "label_values(apiserver_audit_level_total, region)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "cce-ygxcbgvf", "value": "cce-ygxcbgvf"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "description": null, "error": null, "hide": 0, "includeAll": false, "label": "clusterID", "multi": false, "name": "clusterID", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\"}, clusterID)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": "${datasource}", "definition": "label_values(apiserver_audit_level_total{region=\"$region\", clusterID=\"$clusterID\"}, instance)", "description": null, "error": null, "hide": 0, "includeAll": true, "label": null, "multi": false, "name": "instance", "options": [], "query": {"query": "label_values(apiserver_audit_level_total{region=\"$region\", clusterID=\"$clusterID\"}, instance)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-5m", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "核心组件 / APIServer / ExtenderAuditor", "uid": "sLeWHE87zbbbbb", "version": 9}