# CCE 服务组件介绍

- [CCE 服务组件介绍](#cce-服务组件介绍)
  - [简介](#简介)
  - [集群管理](#集群管理)
    - [简介](#简介-1)
    - [架构](#架构)
    - [组件](#组件)
  - [集群变更](#集群变更)
    - [简介](#简介-2)
    - [架构](#架构-1)
    - [组件](#组件-1)
  - [可观测性服务](#可观测性服务)
    - [简介](#简介-3)
    - [架构](#架构-2)
    - [组件](#组件-2)
  - [K8S 原生资源管理](#k8s-原生资源管理)
    - [简介](#简介-4)
    - [架构](#架构-3)
    - [组件](#组件-3)
  - [Helm 应用市场](#helm-应用市场)
    - [简介](#简介-5)
    - [架构](#架构-4)
    - [组件](#组件-4)
  - [Serverless K8S](#serverless-k8s)
    - [简介](#简介-6)
    - [架构](#架构-5)
    - [组件](#组件-5)
  - [巡检采集服务](#巡检采集服务)
    - [简介](#简介-7)
    - [架构](#架构-6)
    - [组件](#组件-6)
  - [容器网络](#容器网络)
  - [容器存储](#容器存储)
## 简介

CCE 服务以两种方式进行部署:

* B 区: 所有后端组件组成一个 Helm Chart, 部署在 B 区 K8S 集群(简称 MetaCluster)
* A 区: CloudControllerManager/容器网络/容器存储等以插件方式部署在 A 区用户 K8S 集群, CCE 插件路径

CCE 组件日志和监控看板:

* CCE 后端服务 Helm Chart: infra/service/chart
* CCE K8S 插件: pkg/plugin/helm/charts
* [组件日志](http://10.133.155.27:8601/app/kibana) changeme/elastic
* [CCE监控](http://gzns-cce-events00.gzns.baidu.com:8790/?query=cce-vm&search=open&folder=cce-vm&orgId=1) admin/cce@admin

## 集群管理

### 简介

提供声明式的集群管理功能, 用户 A 区 K8S 对应 CCE MetaCluster 上 Cluster/Instance 等各种 CRD, Controller 通过实现幂等来保证用户 K8S 状态的最终一致。

### 架构

```md
                    console
                       │
                       ▼
              cce-cluster-service
                       │
                       ▼
Create ClusterCRD/InstanceCRD/InstanceGroupCRD in MetaCluster
                       ▲
                       │ List/Watch
             cce-cluster-controller
                       │
                       ▼ 创建
                User K8S Cluster
```

### 组件

* cluster-service:
  * 作用: 对接 console 及 openapi, CCE 服务的主要入口, 采用 beego 框架
  * 代码: cluster/cluster-service
* cluster-controller:
  * 作用: 监听 Cluster CRD, 完成集群生命周期管理
  * 代码: cluster/cluster-controller/controllers/cluster-controller.go
* instance-controller:
  * 作用: 监听 Instance CRD, 完成节点生命周期管理
  * 代码: cluster/cluster-controller/controllers/instance-controller.go
* instance-group-controller:
  * 作用: 监听 InstanceGroup CRD, 完成节点组生命周期管理
  * 代码: cluster/cluster-controller/controllers/instancegroup/instancegroup-controller.go
* cce-db-rync-controller:
  * 作用: 实现 InstanceGroup CRD 到数据库的同步
  * 代码: cluster/db-sync-controller
* cce-instance-gc-manager:
  * 作用: 支持竞价实例竞价/退场, 创建失败或删除失败实例的自动回收
  * 代码: cluster/instance-gc-manager
* cce-validate-webhook:
  * 作用: 提供对 MetaCluster 各类 CRD 操作的校验
  * webhook/cce-validate-webhook

## 集群变更

### 简介

CCE 管理大量 K8S 集群, 随着 CCE 产品迭代, 用户 K8S 集群环境和配置会不一致, 包括不限于 K8S 版本/K8S 插件/组件配置等, 未来是希望具备所有集群配置打平的能力，将自动化运维操作通过产品化的方式对外进行输出。

### 架构

```md
          console
             │
             ▼
    cce-cluster-service
             │
             ▼
Create WorkflowCRD in MetaCluster
             ▲
             │ List/Watch
  cce-workflow-controller
             │
             ▼ 升级
      User K8S Cluster
```

### 组件

* cce-workflow-controller:
  * 作用: Workflow 定义一个自动化变更流程, controller 具体执行变更并保证最终一致
  * 代码: workflow/workflow-controller
* cce-agent:
  * 作用: 特权容器, 调度到用户 K8S Node 上执行变更
  * 代码: workflow/cce-agent

## 可观测性服务

### 简介

在百度云 Console 上提供监控/日志/审计/K8S Event/服务画像等服务

### 架构

### 组件

* cce-monitor-service:
  * 作用: 对接 console, 提供监控/日志/审计/K8S Event/服务画像等服务的后端 API
  * 代码: monitor/monitor-service
* cce-event-collector:
  * 作用: 采集用户 K8S 集群 Event, 并推送至 ES
  * 代码: monitor/k8s-event-collector
* cce-report-collector:
  * 作用: 用户 K8S 集群服务画像, 提供资源或服务优化建议
  * 代码: monitor/k8s-report-collector
* cce-alert-webhook:
  * 作用: AlertManager 报警 Webhook, 用户 K8S 集群 AlertManager 通过 Webhook 消息中心发送报警
  * 代码: monitor/cce-alert-webhook
* external-auditor:
  * 作用: 部署在用户 Master 节点, APIServer 推送审计日志至 BCT
  * 代码: monitor/external-auditer
* cce-log-agent:
  * 作用: 部署在用户 K8S 集群, 采集容器 stdout/指定目录的日志
  * 代码: monitor/cce-log-agent
* cce-log-operator:
  * 作用: 日志采集 V2 版, 使用 CRD 作为采集配置
  * 代码: monitor/cce-log-operator
* master-service-discovery:
  * 作用: 轮询用户 K8S 集群, 在 B 区 MetaCluster 建立 Endpoint, 供 B 区统一监控 VM 采集
  * 代码: monitor/master-service-discovery
* vm-agent:
  * 作用: 部署在 B 区 MetaCluster, 采用用户 K8S 集群 Metrics 指标, 推送至资源账号下 VM 集群
  * 代码: 开源组件

## K8S 原生资源管理

### 简介

百度云 Console 上提供 K8S 原生资源管理:

* 工作负载: Deployment/Pod/Job/CronJob/DaemonSet/StatefulSet/HPA;
* 流量接入: Service/Ingress;
* 存储配置: Configmap/Secret/PV/PVC/StorageClass。

### 架构

### 组件

* cce-app-service:
  * 作用: 对接 console, 提供用户表单化管理 K8S 原生资源
  * 代码: app/app-service

## Helm 应用市场

### 简介

百度云 Console 上提供 Helm 模板市场及 Helm 实例管理。

### 架构

### 组件

* cce-helm-service:
  * 作用: Helm 应用市场后端服务
  * 代码: helm/helm-service
* cce-helm-chartmuseum:
  * 作用: Helm 仓库
  * 代码: helm/chartmuseum

## Serverless K8S

### 简介

基于 BCI 及 VirtualKubelet 创建 K8S 集群, 提供原生 K8S 语义来使用容器实例 BCI 服务。

### 架构


### 组件

* etcd-manager:
  * 作用: ServerlessK8S 集群复用 etcd, 提供 etcd 管理接口
  * 代码: serverless/etcd-manager
* serverless-master:
  * 作用: ServerlessK8S 集群目前使用 1.16.8 版本 Master 的镜像
  * 代码: serverless/master-1.16.8
* virtual-kubelet:
  * 作用: virtual-kubelet 对 BCI 的实现
  * 代码: serverless/virtual-kubelet

## 巡检采集服务

### 简介

定期轮询用户 K8S 集群状态, 并同步至 MetaCluster 中的 ClusterHealthCheck 和 InstanceHealthCheck CRD。集群插件版本等信息也由该组件完成, 后续需要定期从用户集群拿状态的逻辑都收敛至该组件。

### 架构

### 组件

* cce-health-controller:
  * 作用: 定期轮询用户 K8S 集群状态, 插件版本等
  * 代码: healthcheck/detector

## 容器网络

## 容器存储