# 项目名称  

## 技术全景

![CCE_2021_技术全景图](DOC/重点规划/2021/CCE_技术全景图_2021_Q3.jpg)

## 技术架构

![CCE_2022_技术架构](DOC/重点规划/2022/CCE技术架构.jpg)

## 监控地址

* [CCE-VM Grafana 监控大盘](http://gzns-cce-events00.gzns.baidu.com:8790/?query=cce-vm&search=open&folder=cce-vm&orgId=1)
* CCE-VM 监控系统所在资源账号/地区：cce-nbdzyfsb/gz

> grafana 用户名/密码  "<EMAIL>"/"UXU9LAXOhctKXuHIzTxRlzz64/Y5ceR487ZS31S1Txk="
  
## 目录结构

CCE mono-repo

* DOC/ - 文档;
* CHANGELOG/ - ChangeLog 一周两次发布;
* pkg/ - Shared codes, or libraries common across the repo;
* services/ - Basically, long running services;
* infra/ - 包含 CCE 依赖, MetaCluster, 服务 Helm Chart, 自动化回归的完整描述.

## CCE 周报

* [CCE 自动周报说明](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/README.md)
* [CCE 团队成员及分工](https://console.cloud.baidu-int.com/devops/icode/repos/baidu/jpaas-caas/cce-stack/blob/master:services/devops/cce-weekly-report/okr/conf.json)

## 常用命令

检查代码格式

```bash
$ make -f Makefile format
```

更新 CRD

```bash
$ make -f Makefile update_crd
```

go generate

```bash
$ make -f Makefile generate
```

## 主干开发

1. 本地新建分支

```bash
$ git checkout -b feature-123 origin/master
$ git commit
```

2. 向 master 提交评审

```bash
$ git push origin HEAD:refs/for/master
```

3. 在 iCode 查看 diff，将代码合入 master

4. 在 iCode 或 Agile上 发布 master (标记 tag)

5. 取消 commit

```bash
$ git reset --soft origin/master
```

6. 取消 add

```badh
$ git reset HEAD
```

参考:

* CCE 迭代发布: http://wiki.baidu.com/pages/viewpage.action?pageId=1117572998
* ICode 工作流: http://wiki.baidu.com/pages/viewpage.action?pageId=193045668

## 测试

Package 覆盖率检查

```bash
$ go test -v -covermode=count -coverprofile=./chen.cover ./pkg/models/... && go tool cover -html=chen.cover -o chen.html
$ go test -v -covermode=count -coverprofile=./chen.cover $(go list ./... | grep -v ./pkg/models/) && go tool cover -html=chen.cover -o chen.html
```

## 流水线使用
请先了解iPipe基本概念：构建、阶段、任务
<br>
https://cloud.baidu-int.com/icloud/ipipe/%E5%BF%AB%E9%80%9F%E5%85%A5%E9%97%A8/%E6%B5%81%E6%B0%B4%E7%BA%BF%E7%AE%80%E4%BB%8B/
<br>
https://cloud.baidu-int.com/icloud/ipipe/%E4%BA%A7%E5%93%81%E6%8F%8F%E8%BF%B0/%E5%90%8D%E8%AF%8D%E8%A7%A3%E9%87%8A

### 编译
编译阶段根据编译任务场景进行编译，B区组件编译采用new_build.sh脚本，A区插件编译采用plugin_build.sh脚本

编译任务执行go build生成二进制，发布到产出。目前产出中内容包含：所有组件的二进制、Dockerfile、Chart文件
### 镜像构建、Chart发布
#### 镜像构建
镜像构建任务从产出中获取打镜像所需的二进制和Dockerfile，并根据流水线上构建参数BUILD_INFO构建对应摸的镜像，推送到仓库中。

BUILD_INFO格式如下："image_name=path/to/Dockerfile image_name=path/to/Dockerfile"
其中：
* image_name：目标镜像名称，也就是推送到仓库的镜像名称
* path/to/Dockerfile：Dockerfile在产出中的相对路径
  
举例：
如a镜像在产出中路径为output/a/Dcokerfile，b镜像类似为output/b/Dcokerfile-1，则可以写为BUILD_INFO="a=a/Dcokerfile b=b/Dcokerfile-1"

对于image_name=image_name/Dcokerfile情况也可以简写为image_name，如上述例子，可写为BUILD_INFO="a b=b/Dcokerfile-1"

镜像构建任务打出的镜像信息会传递到流水线构建参数IMAGE_INFO中

#### Chart发布
Chart发布任务基于上游任务的IMAGE_INFO对Chart文件中的镜像地址占位符做替换后推送CCR仓库，Chart版本号生成规则见工具说明。

对于CCE的chart，占位符格式为：{imagename}-IMAGEID, 其他占位符使用见工具说明。

#### 工具说明
工具详细说明见：https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/pKzJfZczuc/0WdZp1XYoL/knu-jIOSalXuPm

### CCE插件开发测试发布流水线使用
#### 开发测试发布模式
目前插件开发模式为<font color=#229954>__主干开发，分支测试发布__</font>，即在master开发，提测时将代码合入对应的插件分支，分支命名规则为cce-stack-plugin-{插件名}：如cce-virtual-kubelet插件，则对应分支为cce-stack-plugin-cce-virtual-kubelet；cce-lb-controller插件对应的分支为cce-stack-plugin-cce-lb-controller。

#### 流水线操作说明：自测、提测、发布
插件开发测试发布流程中，涉及两个流水线：K8SPluginChangePipeline、K8SPluginReleasePipeline
<br>

__自测时__，代码提交到master分支（不合入），会自动触发K8SPluginChangePipeline.<br>
step1. 默认情况下，代码提交后，K8SPluginChangePipeline会默认跳过测试镜像和测试Chart构建任务, 因此当需要自测插件功能时，需要点击**重新执行，选择对应的插件**，ENV可以默认不改。
![K8SPluginChangePipeline 默认跳过各阶段](DOC/工程规范/流水线/插件开发测试发布/K8SPluginChangePipeline1.png "K8SPluginChangePipeline 默认跳过各阶段")

![K8SPluginChangePipeline发布插件测试Chart](DOC/工程规范/流水线/插件开发测试发布/K8SPluginChangePipeline2.png "K8SPluginChangePipeline发布插件测试Chart")
<br>
step2. 等待任务执行完成，即可在CCE沙盒（sandbox）/测试（gztest/bjtest/bdtest）环境Helm模版处看到该插件新版本模版
![CCE Helm模版仓库](DOC/工程规范/流水线/插件开发测试发布/CCE_Helm_模版仓库.png "CCE_Helm_模版仓库")

<br>

<font color=#E74C3C>**注意**: 自测时一旦发现插件引入核心功能不可用问题，请及时删除测试环境该插件发布包，以免影响该测试环境稳定性，影响整个团队使用。</font>
```
删除方式:
jpaas账户下，BOS chartmuseum-qa-data bucket下private-charts/admin目录，找到本次自测发布的插件版本，进行删除
```


__提测时__，代码合入到master分支后，再合入插件分支，会自动触发K8SPluginReleasePipeline, 研发触发【提测】阶段，点击提测任务的【执行提测】

__测试时__，QA点击【提测】阶段的提测任务的【确认提测】，则代表接受提测，进入测试环节，QA在测试时触发【测试Chart发布】部署测试环境，之后可进行测试，测试完成通过后，触发【测试准出】阶段，点击【执行准出】

__上线时__，RD触发【版本号发布】，则进入上线环境，完成【生产Chart发布】阶段后，则可以在线上CCE Helm模版里看到新版本的插件模版

#### 插件版本管理
插件的Chart版本号需要研发自行管理，每次功能变更在Chart.yaml中修改CHART_VERSION，并记录changelog


### 新增模块如何添加到流水线的自动构建流程里
#### 新增B区组件
1、new_build.sh中serviceMap添加一项，并保证构建镜像、Chart所需的文件都发布到产出

2、流水线Agile构建参数BUILD_INFO添加对应的镜像名称和dockerfile路径信息

3、Chart文件中占位符写好（主要在是values文件中）

#### 新增A区插件
1、plugin_build.sh中添加相应的记录，保证构建镜像、Chart所需的文件都发布到产出

如cce-virtual-kubelet插件，根据其Dockerfile，确定以下几个文件需要发布到产出：
![cce-virtual-kubelet Dockerfile](DOC/工程规范/流水线/插件开发测试发布/示例_cce-virtual-kubelet_Dockerfile.png "cce-virtual-kubelet Dockerfile")

根据原plugin_build.sh（默认只编译二进制，将二进制和Dockerfile发布到产出），无法满足cce-virtual-kubelet的镜像构建所需的文件
![原plugin-build脚本](DOC/工程规范/流水线/插件开发测试发布/原plugin-build脚本.png "原plugin-build脚本")

那么修改plugin_build.sh，针对cce-virtual-kubelet进行产出发布：
![plugin-build cce-virtual-kubelet](DOC/工程规范/流水线/插件开发测试发布/示例_cce-virtual-kubelet_plugin_build.png "plugin-build cce-virtual-kubelet")

pluginMap的key可以以组件名/镜像名命名，一条记录构建出一个二进制，最终二进制是发布到output/{pluginMap的key}/ 目录下

2、plugin_build_info中添加新的插件记录，如果对已有插件新增镜像，则在已有插件处进行新增

把插件和所需的n个镜像按照如下格式填写，以下路径是针对产出ouput目录的相对路径
```bash
[插件名称]
目标镜像1名称=path/to/Dockerfile1
目标镜像2名称=path/to/Dockerfile2
```
如cce-virtual-kubelet：
```
[cce-virtual-kubelet]
bci-virtual-kubelet=cce-virtual-kubelet/Dockerfile
```
bci-virtual-kubelet是想要构建的镜像名称，cce-virtual-kubelet/Dockerfile为第一步中相对产出ouput目录的相对路径，里面是二进制文件、Dockerfile以及其他所需文件

3、Chart文件中占位符写好（主要在是values文件中）

如cce-virtual-kubelet Chart values.yaml:<br>
![cce-virtual-kubelet Chart values.yaml](DOC/工程规范/流水线/插件开发测试发布/示例_cce-virtual-kubelet_Chart_valuesyaml.png "cce-virtual-kubelet Chart values.yaml")


4、新建该插件分支，名称按照规范格式cce-stack-plugin-{插件名称}写

## 链接

* [百度golang代码库组织和引用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=515622823)
* [百度内Go Module使用指南](http://wiki.baidu.com/pages/viewpage.action?pageId=917601678)
