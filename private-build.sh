#!/bin/bash
set -eu
set -o pipefail

# download pack shell
bcloud local -U

RELEASE_VERSION=${1:-cce-20210302}

function package_deploy()
{
    WORKROOT=$(cd ../../../ && pwd)

    # make sub module dir
    # see: http://wiki.baidu.com/pages/viewpage.action?pageId=1241676235

    echo "$WORKROOT"

    cd $WORKROOT/baidu/jpaas-caas/cce-stack
    mkdir -p output
    cd output

    ####### cce-stack package, path: $WORKROOT/baidu/jpaas-caas/cce-stack/output/ #######

    # kubelet 以及相关工具
    wget http://baidu-container-gztest.gz.bcebos.com/private-cloud/kubelet-meta.tar.gz

    cd $WORKROOT/baidu/jpaas-caas/cce-stack
    # cp cce-stack helm charts
    mkdir -p ./output/service/chart
    mkdir -p ./output/service/chart_regression
    cp -r ./infra/service/chart/templates ./output/service/chart/
    cp ./infra/service/chart/Chart.yaml ./output/service/chart/
    cp -r ./infra/service/chart_regression/templates ./output/service/chart_regression/
    cp ./infra/service/chart_regression/Chart.yaml ./output/service/chart_regression/

    # 不起作用，charm 构建限制了包的大小需要小于1G...
    # 部署包
    #    mkdir -p ./output/cce-package
    #
    #    while read line
    #    do
    #        wget -P ./output/cce-package/ ${line}
    #        if [[ $? -ne 0 ]]; then
    #        echo "[----------'download bos package' failed!----------]"
    #        echo "Command: wget -P ./output/cce-package/ \"${line}\""
    #        exit -1
    #        fi
    #    done < './infra/private_cloud/bos_package'
    ####### cce-stack package end #######

    # 这里替换暂时不起作用，因为 charm 构建是直接git clone 拉取代码库，不是用的 ci output
    # 替换 values-private.yaml ImageID, 镜像tag为B区镜像构建的tag
    echo "RELEASE_VERSION: ${RELEASE_VERSION}"
    old_image_tag="IMAGETAG"
    new_image_tag=${RELEASE_VERSION}

    cd $WORKROOT/baidu/jpaas-caas/cce-stack/abcstack/charm/layer-cce-stack/templates/kolla/service
    echo "$(pwd)"
    cat chart/values-private.yaml

    sed -i "s|${old_image_tag}|${new_image_tag}|g" chart/values-private.yaml
    sed -i "s|${old_image_tag}|${new_image_tag}|g" chart_regression/values-private.yaml

    cat chart/values-private.yaml

    # https://console.cloud.baidu-int.com/devops/icode/repos/baidu/industry-cloud/templates/blob/master:tools/pack_charm_sub_module.sh
    cd $WORKROOT/baidu/jpaas-caas/cce-stack
    sh ../../industry-cloud/templates/tools/pack_charm_sub_module.sh
}

package_deploy
