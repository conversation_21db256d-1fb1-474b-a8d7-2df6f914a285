[cce-ingress-controller]
cce-ingress-controller=cce-ingress-controller/Dockerfile
cce-ingress-controller-arm64=cce-ingress-controller/Dockerfile-arm64

[cce-network-inspector]
cce-network-inspector=cce-network-inspector/Dockerfile

[cce-lb-controller]
cce-lb-controller=cce-lb-controller/Dockerfile
cce-lb-controller-arm64=cce-lb-controller/Dockerfile-arm64

[cce-cloud-node-controller]
cce-cloud-node-controller=cce-cloud-node-controller/Dockerfile
cce-cloud-node-controller-arm64=cce-cloud-node-controller/Dockerfile-arm64

[cce-virtual-kubelet]
bci-virtual-kubelet=cce-virtual-kubelet/Dockerfile

[cce-onepilot]
cce-onepilot=cce-onepilot/Dockerfile
agent-downloader=agent-downloader/Dockerfile

[cce-log-operator]
cce-log-operator=cce-log-operator/Dockerfile

[cce-pro-external-auditor]
kube-external-auditor=kube-external-auditor/Dockerfile