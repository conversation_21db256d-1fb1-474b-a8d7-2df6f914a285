{{- if .Values.KubeStateMetrics.Enable }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: kube-state-metrics
  name: kube-state-metrics
  namespace: cce-monitor
spec:
  replicas: {{.Values.KubeStateMetrics.Replicas }}
  selector:
    matchLabels:
      app: kube-state-metrics
  serviceName: kube-state-metrics
  template:
    metadata:
      labels:
        app: kube-state-metrics
        cce-app: "true"
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "8123"
    spec:
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - topologyKey: "kubernetes.io/hostname"
              labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - kube-state-metrics
      containers:
        - args:
            - --port=8123
            - --telemetry-port=8124
            - --pod=$(POD_NAME)
            - --pod-namespace=$(POD_NAMESPACE)
            - --resources=networkpolicies,nodes,persistentvolumeclaims,replicationcontrollers,storageclasses,daemonsets,certificatesigningrequests,configmaps,secrets,services,limitranges,ingresses,poddisruptionbudgets,deployments,resourcequotas,statefulsets,horizontalpodautoscalers,namespaces,jobs,persistentvolumes,pods,replicasets,volumeattachments,cronjobs,endpoints
            {{if .Values.KubeStateMetrics.KSMCRDEnable}}
            - --custom-resource-state-config-file=/etc/cce-resource-state-config.yaml
            {{end}}
          env:
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
          image: registry.baidubce.com/cce-plugin-dev/kube-state-metrics:cce-v2.10.1
          resources:
            limits:
              memory: 4Gi
              cpu: 4000m
            requests:
              cpu: 100m
              memory: 100Mi
          livenessProbe:
            httpGet:
              path: /healthz
              port: 8123
            initialDelaySeconds: 5
            timeoutSeconds: 5
          name: kube-state-metrics
          ports:
            - containerPort: 8123
              name: http-metrics
              hostPort: 8123
            - containerPort: 8124
              name: telemetry
              hostPort: 8124
          readinessProbe:
            httpGet:
              path: /
              port: 8124
            initialDelaySeconds: 5
            timeoutSeconds: 5
          securityContext:
            runAsUser: 65534
          {{if .Values.KubeStateMetrics.KSMCRDEnable}}
          volumeMounts:
            - name: custom-resource-state-config
              mountPath: /etc/cce-resource-state-config.yaml
              subPath: cce-resource-state-config.yaml
              readOnly: true
          {{end}}
      hostNetwork: true
      {{if .Values.KubeStateMetrics.KSMCRDEnable}}
      volumes:
        - name: custom-resource-state-config
          configMap:
            name: cce-custom-resource-state-config
            items:
              - key: cce-resource-state-config.yaml
                path: cce-resource-state-config.yaml
      {{end}}
      serviceAccountName: kube-state-metrics
---
{{if .Values.KubeStateMetrics.KSMCRDEnable}}
apiVersion: v1
kind: ConfigMap
metadata:
  name: cce-custom-resource-state-config
  namespace: cce-monitor
data:
  cce-resource-state-config.yaml: |
    spec:
      resources:
        - groupVersionKind:
            group: cce.baidubce.com
            version: "v1"
            kind: Instance
          metricNamePrefix: cce_instance
          labelsFromPath:
            name: [metadata, name]
            clusterID: [metadata, labels, cluster-id]
            accountID: [spec, accountID]
            instanceGroupID: [spec,instanceGroupID]
            machineSpec: [spec,instanceResource,machineSpec]
            imageType: [spec,instanceOS,imageType]
            osName: [spec,instanceOS,osName]
            osVersion: [spec,instanceOS,osVersion]
            floatingIP: [status,machine,floatingIP]
            vpcIP: [status,machine,vpcIP]
          metrics:
            - name: "create_time"
              help: "cce instance create timestamp"
              each:
                type: Gauge
                gauge:
                  path: [metadata]
                  valueFrom: [creationTimestamp]
            - name: "phase"
              help: "cce instance phase"
              each:
                type: StateSet
                stateSet:
                  labelName: phase
                  path: [status,instancePhase]
                  list: [pending,bidding,provisioning,provisioned,deployed,running,create_failed,deleting,deleted,delete_failed,expired,upgrading,upgrade_failed,unknown]
            - name: "step_cost_seconds"
              help: "cce instance step cost time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [costSeconds]
                  nilIsZero: true
            - name: "step_retry_count"
              help: "cce instance step cost time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [retryCount]
                  nilIsZero: true
            - name: "deploy_status"
              help: "cce instance deploy status"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [infrastructureReady]
            - name: "retry_count"
              help: "cce instance retry count"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [retryCount]
            - name: "step_start_time"
              help: "cce instance step start time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [startTime]
                  nilIsZero: true
            - name: "step_finish_time"
              help: "cce instance step finish time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [finishedTime]
            - name: "step_ready"
              help: "cce instance step ready"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [ready]
                  nilIsZero: true
        - groupVersionKind:
            group: cce.baidubce.com
            version: "v1"
            kind: Task
          metricNamePrefix: cce_task
          labelsFromPath:
            name: [metadata, name]
            clusterID: [metadata, labels, cluster-id]
            accountID: [spec, accountID]
          metrics:
            - name: "process_start_time"
              help: "Start time of task processes"
              each:
                type: Gauge
                gauge:
                  path: [status, processes]
                  labelsFromPath:
                    processName: [name]
                  valueFrom: [startTime]
            - name: "process_finish_time"
              help: "Finish time of task processes"
              each:
                type: Gauge
                gauge:
                  path: [status, processes]
                  labelsFromPath:
                    processName: [name]
                  valueFrom: [finishTime]
            - name: "create_time"
              help: "cce task create timestamp"
              each:
                type: Gauge
                gauge:
                  path: [metadata]
                  valueFrom: [creationTimestamp]
            - name: "finishTime"
              help: "cce task finish timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [finishTime]
            - name: "phase"
              help: "cce task phase"
              each:
                type: StateSet
                stateSet:
                  labelName: phase
                  path: [status, phase]
                  list: [Pending, Processing, Collecting, Done, Aborted]
        - groupVersionKind:
            group: cce.baidubce.com
            version: "v1"
            kind: Cluster
          metricNamePrefix: cce_cluster
          labelsFromPath:
            name: [metadata, name]
            accountID: [spec, accountID]
            clusterID: [spec, clusterID]
            clusterName: [spec, clusterName]
            k8sVersion: [spec, k8sVersion]
            masterType: [spec, masterConfig, masterType]
            vpcID: [spec, vpcID]
            networkMode: [spec, containerNetworkConfig, mode]
            clusterType: [spec, clusterType]
          metrics:
            - name: "create_timestamp"
              help: "cce cluster create timestamp"
              each:
                type: Gauge
                gauge:
                  path: [metadata]
                  valueFrom: [creationTimestamp]
            - name: "phase"
              help: "cce cluster phase"
              each:
                type: StateSet
                stateSet:
                  labelName: phase
                  path: [status,clusterPhase]
                  list: [pending,provisioning,provisioned,running,create_failed,deleting,deleted,delete_failed,upgrading,upgrade_failed,eip_opening,eip_open_failed,eip_closing,eip_close_failed]
            - name: "step_cost_seconds"
              help: "cce cluster step cost time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [costSeconds]
                  nilIsZero: true
            - name: "step_retry_count"
              help: "cce cluster step retry count"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [retryCount]
                  nilIsZero: true
            - name: "retry_count"
              help: "cce cluster retry count"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [retryCount]
            - name: "step_start_timestamp"
              help: "cce cluster step start timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [startTime]
                  nilIsZero: true
            - name: "step_finish_timestamp"
              help: "cce cluster step finish timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [finishedTime]
            - name: "step_ready"
              help: "cce cluster step ready"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileSteps]
                  labelFromKey: step
                  valueFrom: [ready]
                  nilIsZero: true
            - name: "infra_status"
              help: "cce cluster infrastructure status"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [infrastructureReady]
            - name: "access_success"
              help: "cce cluster access status"
              each:
                type: Gauge
                gauge:
                  path: [status]
                  valueFrom: [apiServerAccessSuccess]
            - name: "delete_step_cost_seconds"
              help: "cce cluster delete step cost time"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileDeleteSteps]
                  labelFromKey: step
                  valueFrom: [costSeconds]
                  nilIsZero: true
            - name: "delete_step_retry_count"
              help: "cce cluster delete step retry count"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileDeleteSteps]
                  labelFromKey: step
                  valueFrom: [retryCount]
                  nilIsZero: true
            - name: "delete_step_start_timestamp"
              help: "cce cluster delete step start timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileDeleteSteps]
                  labelFromKey: step
                  valueFrom: [startTime]
                  nilIsZero: true
            - name: "delete_step_finish_timestamp"
              help: "cce cluster delete step finish timestamp"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileDeleteSteps]
                  labelFromKey: step
                  valueFrom: [finishedTime]
                  nilIsZero: true
            - name: "delete_step_ready"
              help: "cce cluster delete step ready"
              each:
                type: Gauge
                gauge:
                  path: [status,reconcileDeleteSteps]
                  labelFromKey: step
                  valueFrom: [ready]
                  nilIsZero: true
{{end}}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kube-state-metrics
rules:
  - apiGroups:
      - "*"
    resources:
      - "*"
    verbs:
      - list
      - watch
      - get
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kube-state-metrics
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kube-state-metrics
subjects:
  - kind: ServiceAccount
    name: kube-state-metrics
    namespace: cce-monitor
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: kube-state-metrics
  namespace: cce-monitor
rules:
  - apiGroups:
      - ""
    resources:
      - pods
    verbs:
      - get
  - apiGroups:
      - extensions
    resourceNames:
      - kube-state-metrics
    resources:
      - deployments
    verbs:
      - get
      - update
  - apiGroups:
      - apps
    resourceNames:
      - kube-state-metrics
    resources:
      - deployments
    verbs:
      - get
      - update


---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: kube-state-metrics
  namespace: cce-monitor
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kube-state-metrics
subjects:
  - kind: ServiceAccount
    name: kube-state-metrics

---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kube-state-metrics
  namespace: cce-monitor
{{- end}}