# CCE 2021 重点规划

## 企业级容器平台

目标: **用户能自助地接入使用, 异常成为小概率事件且能自助处理, 在性能测评中不落下风**。

### 闭环

云 K8S 服务对比用户自建 K8S 三大优势:

1. IaaS 支持;
2. K8S 集群运维;
3. K8S 生产经验最佳实践。

形成确定的方法论和可行实现路径, 能引导同学持续的对上述三点进行优化, **形成闭环, 一劳永逸**:

* IaaSProvision: 对 BCC/BBC/BEC 的 MachineType/InstanceType/OS 等支持形成方法论, 并且实现一劳永逸;
  * 前端新建 BCC 页面复用 BCC SDK;
  * 已有节点支持批量查询及后置校验;
  * CCE 对关联 IaaS 资源进行打标和跟踪;
  * 构建完整 InstanceType/OS 回归测试, 实现 InstanceType/OS 等快速支持;
  * ......
* 运维能力产品化: 集群管理形成"进度展示->标准错误->自助处理->主动技改"的闭环, 提高操作成功率, 符合企业级产品定位;
  * 丰富标准错误码展示;
  * 支持 Update Cluster/Instance, 实现声明式语义;
  * Quota/余额/实名认证等检查前置, 减少错误;
  * 通过允许选择多可用区等方式解决资源不足导致失败问题
  * ......
* K8S 服务画像: 依托 K8S 插件管理, 以低成本和低风险的方式, 不断沉淀 K8S 管理经验, 将生产实践经验通对用户进行输出。
  * 基于 node-problem-detector 检查 K8S Node 异常;
  * 基于 cce-health-check 及节点组实现异常的自动恢复;

### 统一

"问题场景理解<->产品交互设计<->后端架构实现"三者相辅相成, 好的设计三者是相通的, 通过实现如下几点, **解决 CCE 用户体验和可扩展性面临的系统性风险**:

* ClusterProvider: 产品设计和后端实现具备支持 BCC/BBC/BEC/非百度云节点等能力;
* K8SDeployer: 统一远程部署(类 Ansible)和原地部署(类 kubeadm), 突破当前 Console 限制, 提供更加完整表达能力;
* 权限审计: 对"IAM/K8S RBAC/BCC身份授权/K8S 插件鉴权"等形成一致的权限模式设计, 不再依赖非标接口, 形成业界标准方案.
* 插件管理: 基于 Helm Chart 实现 K8S 插件管理, 达到用户视角和管理员视角的统一, 为 CCE 基于 K8S 插件输出最佳实践打好基础;
* 公共云和行业云代码统一, 行业云 = 公共云 Region.

上述完成后上会有更好的产品体验.

### 性能

* 容器网络: 打通VPC路由、ENI数量配额、ENI辅助IP配额诉求，为客户根据节点规模一步到位解决各种配额限制。提升ENI挂载速度与鲁棒性，推动容器引擎ENI方案成为首选方案.
* 作业帮 OS 优化形成最佳实践, 推出容器专有 OS/Kernal。

### 安全

* 提供企业级安全方案，在镜像安全、容器运行时安全、VPC安全组、容器网络策略、ETCD加密等方向提供全面的安全加固能力，重点针对容器运行时安全实现从零到一的突破;