# 按优先级划分

## P0 线上隐患

* 规模化: 单集群 5K Node/5W Pod
  * 容器网络: 支持 IP 池
  * 容器网络: 去 IaaS List 全量资源依赖
  * NodeLocalDNS 默认开启
  * Console/插件的控制面优化
  * 集群压测中各种问题的优化
* 集群管理: IaaS 结合/易用性/扩展性/稳定性优化
  * IaaS 操作审计优化
  * BCC 资源导致失败
  * 节点组支持多可用区
  * IaaS 调用统一为 OpenAPI
  * Cluster/Instance/节点组更新
  * 集群操作的前置检查/异常展示/失败重试
* Master/CCM 容器化部署改造
  * 托管 Master 静态容器部署
  * external-auditor 容器化
  * cloud-controller-manager 集群 Pod
  * NodeController/LBController/IngressController 优化, 线上版本升级
* 用户体验相关问题优化

## P1 客户急需的核心能力

* K8S 升级
* V1 升级 V2 (剩斗鱼)
* 去 SSH Root 密码依赖, 支持本地部署
* CCE 私有化（ABC-Stack）环境交付/运维/升级/业务支持/问题处理
  * 部署交接 DET
  * 回归交接私有化 QA
  * 银商环境改造
  * 吉利 AI 私有化
  * ...

## P1 重点布局的核心能力

* CProm
  * 上线
* 云原生 AI
  * AI-Native 内外协同机制落地，并完成基本能力交付(多实例 GPU 虚拟化/昆仑芯片调度)
* 在离线混布
  * 在离线混部产品力完整（离线调度、分时调度)
* 企业版 CCR
  * 上线
* BCI & Serverless
  * BCI 支持 CDS

* 容器网络
  * 支持 RDMA RoCE 容器网络
  * 支持 BBC ENI, 统一 VPC-CNI 架构
* 插件管理
  * B 区服务/CCE 插件 Metrics 建设
  * 插件版本升级变更(合并入集群变更中)
* CCE 可观测性建设
  * K8S Metrics 及 Dashboard
  * B 区组件 Metrics 及 Dashboard
  * A 区插件 Metrics 及 Dashboard
* K8S Event 报警

## P2 各方向重点能力

* 集群管理
  * 支持 K8S 1.22
  * 细粒度安全组优化
  * 托管 Master 容器化改造
  * NPD 支持默认部署, 基于 NPD 支持故障感知/报警/自愈
* 资源弹性:
  * HPA
  * CronHPA
  * 竞价实例
  * 成本分析
  * 扩容弹性速度优化
* 网络数据面优化:
  * 基于 eBPF 加速容器网络数据面
  * 基于 eBPF 实现 NetworkPolicy
  * 基于 eBPF 实现混布系统 QoS 及干扰分析
* 网络可观测性:
  * 对接 Cilium Hubble，实现无侵入网络流量、网络策略可视化
* 容器存储:
  * CDS-CSI 性能优化
  * RapidFS 接入 Fluid
  * BOS-CSI 支持 Containerd
* 安全隔离:
  * CCE 支持最小粒度安全组重构
  * 产品 IAM/RBAC 越权风险优化
  * 集群/主机/容器/网络等风险扫描功能
  * NetworkPolicy 产品化支持(放在容器网络中)
* 权限管理:
  * IAM 权限关联
  * RBAC & IAM 权限联动
  * 支持生产短时 kubeconfig;
  * Role/ClusterRole/RoleBinding/ServiceAccount 等白屏配置
* 审计日志:
  * Master 日志推 BLS
  * 容器日志采集支持 BLS
  * external-auditor 审计功能优化
* 运维运营
  * ShowX 运营数据丰富(GPU 统计/插件统计等)
* CCE@BEC