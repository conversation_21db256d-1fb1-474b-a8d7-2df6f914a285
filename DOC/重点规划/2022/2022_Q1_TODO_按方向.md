# 2022_整体规划

## 纪要补充

* 分类:
  * P0 BUG
  * P1 客户需求
* 可观测性需要单独 Owner
* 插件的日志/健康检查等规范
* 运营数据
* 内部上云修改 K8S 配置, 定制化集群模板

## CCE 基础能力

### 集群管理

* 支持 K8S 1.22
* 托管 Master 容器化改造
* IaaS 调用统一为 OpenAPI
* Master 容器化部署改造
  * 托管 master 静态容器
  * external-audit 容器化
  * cloud-controller-manager 集群 Pod
* CCE 节点组
  * 集群创建默认使用节点组
  * 节点组支持多可用区
  * 节点组直接已有节点移入
  * 节点组异常状态支持用户触发最终一致
* 集群操作失败问题
  * BCC 资源不足导致失败
  * 前置检查/配置更新/进度展示/失败重试
* NPD 支持默认部署, 基于 NPD 支持故障感知/报警/自愈

### 变更管控

* 支持集群变更: 包含 Master 容器化/插件容器化/系统配置优化/K8S 版本变更等
  * 操作前置校验
  * K8S 集群升级
  * K8S 插件升级
  * 优化执行流程的可视化展示、支持用户自助更新重入
* 支持以 K8S 原生方式管理 Cluster/Instance/InstanceGroup:
  * 支持 Cluster/Instance/InstanceGroup 配置修改
  * 支持 Cluster/Instance/InstanceGroup 异常重试, 最终一致
* 基于 ClusterHealthCheck/InstanceHealthCheck 建立元数据大盘
* 大客户集群 V1 升级 V2（4*车和家+2*知乎+2*斗鱼）

### 规模化

* 单集群 5K/5W Pod
* 支持 4000 Pods/min、300 节点/min 扩容速度

规模化可能涉及改造:

```bash
* 产品页
* 集群操作性能
* 容器网络控制面
* 容器网络数据面
* Master 节点规格
* CCE K8S 插件性能
* 单 Node  支撑 Pod 数
* CCE 去 IaaS List 接口依赖
* 节点组功能持续优化，作为用户管理集群默认对象
```

### 多云纳管

* 去远程 ssh 依赖，支持云/边缘节点自助加入 CCE 集群

参考:

```md
目标
* 底层集群：基于任意 IaaS 构建标准 CCE K8S 集群
* 上层服务：纳管任意标准 K8S 提供通用上层服务能力：应用管理/监控/日志/审计/事件/AI/在离线混布等
功能
* 支持 ARM 机型
* 去远程 ssh 依赖，支持云/边缘节点自助加入 CCE 集群
* 去云依赖，支持基于任意节点构建 CCE 标准 K8S 集群
* 去云依赖，支持任意标准 K8S 集群接入 CCE 并使用上层能力
* 为"PaaS/解决方案 on CCE"提供技术架构和产品层面的抽象，方便三方的合作集成
```

### 流量接入

* CCM 容器化改造(基本完成)
* LB Service 兼容普通型/应用型 BLB
* Ingress Controller 兼容老版本的转发规则排序
* 旧版本集群 Ingress/LB Controller 统一升级(合并入集群变更中实现)

### 节点组

* 自动扩缩容
* 支持配置更新
* 支持多可用区
* 产品使用体验优化

### 资源弹性

* HPA
* CronHPA
* 竞价实例
* 成本分析
* 扩容弹性速度优化

### 插件管理

* 插件管理产品化
* 插件 Metrics 相关建设
* 插件版本升级变更(合并入集群变更中)

### 容器网络

基础能力: 控制面优化

* 支持 IP 池
* 去 IaaS List 全量资源依赖
* 支持 5K 节点规模
* 支持 RDMA RoCE 容器网络
* 支持 BBC ENI, 统一 VPC-CNI 架构

可观测性能力:

* 对接 Cilium Hubble，实现无侵入网络流量、网络策略可视化

高级能力: 数据面优化

* 基于 eBPF 加速容器网络数据面
* 基于 eBPF 实现 NetworkPolicy
* 基于 eBPF 实现混布系统 QoS 及干扰分析

### 容器存储

* CDS-CSI 性能优化
* RapidFS 接入 Fluid
* BOS-CSI 支持 Containerd

### 安全隔离

* CCE 支持最小粒度安全组重构
* 产品 IAM/RBAC 越权风险优化
* 集群/主机/容器/网络等风险扫描功能
* NetworkPolicy 产品化支持(放在容器网络中)

### 权限管理

* IAM 权限
* RBAC & IAM 权限联动
* 支持生产短时 kubeconfig;
* Role/ClusterRole/RoleBinding/ServiceAccount 等白屏配置

### 审计日志

* external-auditor 审计功能优化
* Master 日志推 BLS
* K8S Event 报警

### 可观测性

* CCE 服务/插件 Metrics&Dashboard 建设

参考:

```md
* B 区服务：Metrics 暴露 + Grafana Dashboard + 报警
* K8S 集群：Metrics 暴露 + Grafana Dashboard + 报警
* CNI 插件：Metrics 暴露 + Grafana Dashboard + 报警
* LoadBalancerController/IngressController：Metrics 暴露 + Grafana Dashboard + 报警
* 其他插件：Metrics 暴露 + Grafana Dashboard + 报警
```

### 运营运维

* ShowX 运营数据丰富(GPU 统计/插件统计等)

## CCE ABCStack 私有化

* CCE 私有化（ABC-Stack）环境部署/运维/升级/业务支持/问题处理
* 项目: 吉利、上汽、路特斯、银商（需额外开发适配）

## 容器实例

### Serverless K8S

```bash
K8S 版本迭代
1.20

存储
* volume subPath
* CDS 动态/静态 多可用区 支持
* CFS 动态/静态 支持

日志
* 对齐原生集群方案，支持stdout/stderr 或 Pod内指定日志文件 采集并推送BLS/BOS
* 集群审计能力

监控
* BCI Pod接入prometheus/vm生态
* VK暴露metrics
* metrics server

事件
* 基于BCM事件，实现BCI Pod Event

弹性
基于VK的Cluster Autoscaler，弹性资源以BCI Pod创建

性能指标
* 并发创建能力
* 单VK管理Pod数量
* Pod status回写性能（实现VK NotifyPods接口主动状态回写而非基于轮询）

稳定性
* BCI Pod通过webhook自动添加 NotReady/Unreachable toleration，容忍vk异常
* serverless master基于meta cluster的失败容错/滚动升级能力 http://wiki.baidu.com/pages/viewpage.action?pageId=1282779727
```

### BCI

```bash
基础能力
* BCC/BCI混部
* 基于CDS快照的镜像缓存能力
* 镜像拉取进VPC
* 免密拉取镜像
* 机型套餐支持自定义
* 高性能/GPU实例
* 主子账号，IAM权限

性能指标
* 端到端创建时间p90, p99
* 并发下发能力（console/nova/neutron），并发状态回传

稳定性
* 自定义订单超时
* 基于CDS的宕机迁移能力
* 自管资源池兜底
* 运维体系/监控报警体系建设
```

## CCE@BEC

* 支持边缘 K8S 功能

## CProm

* 产品上线

## CCR

* 产品上线, 融合内外 CCR
* 实现 P2P 镜像加速能力，开始迁移内部业务

## 云原生 AI

* AI-Native内外协同机制落地，并完成基本能力交付（多实例GPU虚拟化、昆仑芯片调度）

## 在离线混布

* 在离线混部产品力完整（离线调度、分时调度)