# CCE 2022 Q3 once again

## 集群管理

* IaaS 形态
  * 公共云
  * 边缘集群
  * Serverless
  * 纳管三方集群/节点
  * Kubernetes 发行版
* 管理界面
  * Console
  * OpenAPI/SDK
  * Client 工具
  * ClusterAPI (kubectl)
* 多样化配置
  * 集群类型: 独立/托管
  * K8S 版本: 1.16/1.18/1.20/1.22/1.24
  * 运行时: Docker/Containerd
  * 机型: ARM/Windows
  * 计费方式: 后付费/预付费/竞价实例/潮汐算力
  * 自定义 K8S: 自定义 K8S 参数/前置/后置脚本
  * 容器专属操作系统
  * 自定义集群证书 SAN
* 集群运维
  * K8S 版本升级
  * 操作系统批量升级
  * 安全漏洞批量修复
  * 运维工作流 Workflow 统一管理
* 集群操作
  * 集群/节点查看更新
  * 批量搜索/删除/变更节点
  * 批量管理 Node 标签/污点
  * CCE 产品配额及依赖管理
  * CCE 关联 IaaS 资源及 TAG 管理
  * 不依赖 SSH 及 Root 密码
* 高可用
  * 支持多可用区容灾
  * 支持部署集: 宿主机/机架/交换机分布
  * 集群备份/迁移/恢复/删除保护
  * 提供 Master SLA 保障
* 性能
  * 支持最大集群规模

## Serverless 集群

* 兼容原生 K8S 语义
* 流量接入
* 本地临时存储
* 持久化存储
* 镜像缓存
* 日志
* 监控
* 规格套餐
* 虚机和容器实例混合调度

## 节点组

* 节点组作为管理 Node 默认方式
* 节点组配置查看/更新/异常恢复
* 节点组支持多可用区
* 节点组伸缩记录
* 节点组支持自动扩缩容
* 节点组支持扩容策略/自定义规则
* 节点组支持配置抢占和固定实例配比
* 支持实例库存查询和展示
* 全托管节点池

## 集群自动伸缩

* 支持竞价实例/潮汐实例

## 服务自动伸缩

* 支持 HPA/VPA/CronHPA 可视化管理
* 基于自定义指标 HPA
* 基于事件驱动 HPA
* 支持 HPA 弹性预测

## 容器网络

* 跨节点网络: VPC 路由、VPC-CNI、裸金属容器网络、容器网络参数校验和推荐
* 宿主机到容器: IPVlan、Veth、Bridge+Veth  …
* Service 网络: iptables/ipvs/cilium
* VPC-CNI 配置：execl 里面内容
* 隔离: NetworkPolicy
* RDMA: IB、RoCE
* DNS: NodeLocalDNS、CoreDNS Autoscaler
* 对外: MasqAgent、NAT、ENI 绑 EIP、跨 VPC 通信
* IPv6

## 容器存储

* 文件存储
* 对象存储
* 快存储
* 本地 LVM
* 并行文件系统

## 可观测性

* 监控
  * 接入 Prometheus 标准云产品
  * 提供 K8S 集群/节点/工作负载等监控报警
  * 提供拓扑感知/应用性能/容器网络等可观测能力
* 日志
  * 接入日志采集标准云产品
  * 支持容器日志采集
  * 支持集群控制平面/系统插件日志采集
  * 支持日志采集分析及可视化展示
* 审计
  * 支持 K8S 审计日志持久化
  * 支持 K8S 审计日志可视化分析
* 事件
  * 支持 K8S Event 持久化
  * 支持 K8S Event 异常报警
  * 支持 K8S Event 可视化分析
* 巡检
  * 支持集群巡检/服务画像等功能

## 流量接入

* Service
  * LB 类型 Service
  * 支持 LB 直连 Pod
  * 支持多 Service 复用 LB
* Ingress
  * LB 类型 Ingress
  * Nginx 类型 Ingress
  * API 网关类型 Ingress
  * 支持 LB 直连 Pod
* 易用性
  * 支持表单创建及管理
  * 支持 YAML 创建及管理
  * LB/Ingress 等可观测性
* 性能
  * BLB 挂载实例数
  * BLB 挂载实例变更延迟

## 组件管理

* 组件丰富程度
* 组件可观测性
* 组件升级变更
* 创建集群可选组件

## 应用管理

* K8S 原生资源管理
* Helm 实例管理
* Kubectl WebTerminal
* 灰度发布解决方案

## 权限管理

* 容器产品 IAM 访问控制
* 集群粒度 IAM 访问控制
* 支持 K8S RBAC 授权
* 支持自定义 KubeConfig/吊销
* 支持 OIDC Kubeconfig
* RBAC 资源可视化管理
* 基于 IAM 权限审计功能

## 安全隔离

* 容器安全服务
* ETCD Secret 落盘加密
* NetworkPolicy 产品化

## 解决方案

* 多云混合云
* 成本分析
* 在离线混布
* Knative
* 混沌工程

