# CCE 2020 Q3 重点规划

## 1. 产品能力

### 集群管理

目标: 标准产品满足定制化需求, 开放声明式 API 能力

TODO: 按优先级排序

1. 细节完善(最最最最高优);
2. 开放 V2 版本OpenAPI/SDK;
3. V2 兼容 V1 集群;
4. 支持表单转 JSON 编辑器，开放声明式 API 能力;
5. 节点组 + CA 功能上线;
6. Instance 拥有独立 Event;
7. 支持各对象表单编辑，开放自动恢复及声明式 API 能力;

感觉可能走开源的路线会更加灵活一点.

### Serverless

目标: 支持常用 K8S 原生资源使用.

### 混合云

目标:

希望通过混合云产品形式, 串联几个事情:

1. MetaCluster 使用 CCE 产品进行管理和运维;
2. CCE CNI 开源(提供一种和云 IaaS 解耦方案，类似腾讯 Galaxy);
3. 集成权限管理能力, 参考阿里云多云纳管, MetaCluster 有类似需求;
4. 多云集群的东西和南北流量最佳实践或产品化能力：知乎、一点资讯场景:
5. 开放 CCE 集群部署能力, 以何种形式输出部署能力, 需要再思考下?
    1. 其他 IDC 节点接入 CCE 集群;
    2. 任意节点，CCE 提供 K8S 部署(表单输入);
    3. 任意节点，CCE 提供类似 kubeadm 部署(二进制工具), 解耦部署和纳管;
    4. 用户在节点上执行二进制，节点作为 kubelet 注册到 CCE 集群;
    5. ....

### 权限审计

目标: 支持 K8S RBAC 对 IAM 对象的认证和授权.

关键: CCE 调 IAM 接口做认证，用户利用 IAM 提供唯一标识，在 K8S 绑定不同Role 或 ClusterRole 做授权.

这种方案既能保证 CCE 做的足够简单, 又具备很强扩展性:

1. IAM 无论后续支持什么其他对象, 都无需额外工作;
2. 用户授权能使用到 K8S RBAC 的全部能力.

### 插件管理

目标: 插件更新集成至 CI/CD 流程; 提供 CCE 插件升级解决方案.

1. CCM 的升级的解决方法.

### 容器网络

目标:

1. IPv6;
2. CCE CNI;
3. ServiceController;
4. eBPF + Cillium.

### 控制台升级

目标:

## 2. 基础建设

### CCE on CCE

目标: CCE 产品功能满足 CCE MetaCluster 管理和运维.

TODO: 按优先级排序

1. 集群/进程监控报警;
2. meta-cluster 导入资源账号;
3. 计费和 meta 数据接入 backend;
4. meta-cluster KubeConfig & BRAC 收敛;
5. meta-cluster 监控，复用 CCE 产品功能;
6. meta-cluster 日志和 Event 采集，问题排查由机器变为平台操作;
7. 用户操作主动感知（集群操作，集群失败等通过 Hi/微信/钉钉群通告）;
8. CCE meta-cluster 的服务画像（可以做的很多，性能，失败率，失败原因等统计）.

### CCE 自动化测试

目标: 稳定, 覆盖率, 性能.

能持续稳定运行, 真正起到作用是第一优先级, 可能达到这个目标都需要很长时间.

## 3. 合作方式

任务队列
