# CCE 2020 年终总结

2020 年 CCE 即是云原生的推广者, 也是云原生的实践者和受益者。云原生是一种技术架构, 更是一种组织文化, 组织文化 -> 产品功能 -> 客户支持。

## 组织文化

### 能效
------------

CCE 团队通过 **MonoRepo + 主干开发 + Infra as Code + CCE on K8S** 完成研发模式向 DevOps 转型, 实现约 20+ 组件的统一发版, 每周二四定期发布。

其中, **主干开发+短周期定期发布** 的研发模式倒逼团队同学提升需求拆解能力， 提高代码工程质量, 培养阶段性产出意识。CCE 逐步形成"后端先上线 + 自动化接口测试 + 前端再灰度上线 + QA/RD 分批测试"的上线模式, 以真正交付客户使用为首要标, 让自动化测试的质量保障形成闭环。在后端:QA/PM/前端人力比 >= 10:1 情况下, 探索了一条保证产品顺利迭代及研发自测质量的道路, 为释放研发同学自主性和创造力提供基础。

例子: 部分功能早期用户关注功能完整性, 不太在乎易用性, 所以采取后端上线 + Console JSON 编辑器方式提供:

* 节点组: 8 月后端上线提供 A 客户通过 Terraform 使用, 前端在 12 月提测;
* CCE@BEC: 11 月后端上线提供 Aibee POC 使用, 前端产品设计在 12 月完成;
* GPUSharing: 11 月后端上线提供数字人/EasyDL 使用, 前端产品设计 12 月完成。

### 闭环
------------

用户经常问的一个问题: 使用 CCE K8S 和用户基于 BCC 自建的 K8S 有什么不同, 其实 K8S 本身使用起来完全一致, 这也是我们追求的目标, 使用 CCE 有三方面优势:

1. 和百度云 IaaS 有更加紧密的结合, 能最大程度的释放百度云 IaaS 能力;
2. 提供专业运维能力, 简化集群操作流程, 并且提供简单一致的异常处理方法;
3. 具备丰富 K8S 实践经验, 为用户提供最佳实践，助定位和解决各种使用中问题。

上述三个核心能力都无法一蹴而就, 需要在机制或产品上形成闭环, 长期打磨:

* 从来一个支持一个, 遇到一个解决一个, 到自动适配, 主动发现, 主动出击;
* 从依赖某位同学个人能力, 到即使同学有调整, 整套流程依然能够正常运行。

#### IaaS Provision

CCE 基于云 BCC/BBC/BLB/EIP/VPC 等部署 K8S 集群, 用户通过 K8S API 使用百度云资源, 天然实现多云, 对百度云新增资源的快速支持是 CCE 核心能力之一。

长期以来 CCE 模式都是用户要用哪个机型/OS, 我们被动去支持。在私有化环境更加突出, 保密项目是普通四机型, 公共云才支持普通三, 前后端连带改不少代码。

* 新资源主动快速支持: "前端尽量复用 IaaS 页面 + 后端提供 Support 或 Unsppourted 接口 + CronJob 每天检查, 主动去支持";
* 对 CCE 关联资源跟踪记录。

完成度: 30%

#### 运维能力产品化

K8S 集群的增删查改是 CCE 用户使用最频繁功能, 使用错误/资源不足/账户Quota/代码BUG/三方抖动等都可能导致异常, 随着 K8S on K8S 声明式集群管理的上线, 所有异常处理都收敛成一种方法: "改对配置, 触发重试", 由 CCE 来保证最终一致。

* 过渡:
* 长期:

* 异常来源: Cluster/Instance 中预留失败信息, 每天同步到 Palo, 每周总结优化 1~2 项;
* 处理方法: 短期文档提供错误码及自助解决方法, 长期从产品上引导用户避免错误使用。

完成度: 70%

#### K8S 服务画像

#### 自动化回归测试

### 统一
------------

闭环体现在做事情的方法, 统一体现在对业务的抽象和代码设计上, 把相似事情统一, 把复杂的事情做简单。A 同学负责的项目, 定义好 Interface, 其他同学能随时插入支持, 完成不同的实现。即使 A 同学走后, 其他同学遵照代码设计的显示约束, 能非常自然地做到和 A 一样好。

#### 公共云和行业云

#### ClusterProvider

#### K8SDeployer

#### CCE 插件管理

#### LoadBalancer Controller

## 产品功能

------------

* [集群管理]
  * 声明式集群管理: K8S on K8S;
  * 新增节点组功能;
  * 支持手动同步虚机状态;
  * 支持选择开启公网访问;
  * 支持集群删除可选保留/释放资源;
  * 支持以 BCC 实例规格族形式选择节点配置;
  * 增加 Cluster/Instance 操作事件展示, 提示标准错误码;
* [K8S管理]
  * Docker 升级至 19.3.5;
  * K8S 支持 1.16.8 和 1.18.6 版本;
  * 支持 K8S 节点封锁 cordon;
  * 支持部署前/后注入初始化脚本;
  * 支持自定义 NodePort 端口范围;
  * 支持自定义 Node Label 和 Taint;
  * 支持 Hostname/IP 两种 Node 模式;
  * 支持自定义 etcd/kubelet/docker 组件参数;
* [集群类型]
  * 支持已有实例创建集群;
  * CCE@BEC: 支持基于 BEC 边缘虚机创建 K8S 集群;
  * CCE@BCI: 支持基于 BCI 容器实例创建 K8S 集群;
  * CCE@Kunlan: 支持基于昆仑 TPU 机器创建 K8S 集群;
  * GPUSharing K8S: 将孔明 GPUSharing 方案对外输出;
  * 自定义 Master 集群支持 1/3/5 副本, 支持跨可用区容灾;
* [Serverless]
  * 支持基于 BCI 的 Serverless K8S;
  * 支持 K8S Master BCI 形式容器化部署;
  * 支持多租户 etcd 管理 Serverless K8S 元数据;
  * 支持在 BCI Pod 内解析集群 Service Name;
  * 支持直接从 BCI 后端拉取 BCI Pod 内容器日志;
  * 支持使用 BLB 实现 ClusterIP Service;
  * LBController 支持挂载 BCI Pod 作为后端;
  * Pod 监控页面接入 BCI 实例监控数据;
  * 支持单虚拟节点创建多可用区 BCI Pod;
  * BCI Pod exec 支持 Terminal Resize;
  * 虚拟节点支持 ephemeral-storage quota;
  * 创建集群自动 Ensure Serverless 集群 Master 安全组;
  * 创建 Serverles 集群按钮支持 BCI 是否开通/地域是否可用检查;
  * 支持 Serverless Master 对用户不可见并将计费转移到资源账号;
  * 支持 BCI Pod 中注入 KubeProxy SideCar，并自动完成 iptables chain ensure;
* [容器网络]
  * CCE 支持 IPv6;
  * CNI 新增 veth、ipvlan 两种虚拟网卡类型的支持，提升东西向网络性能;
  * VPC-CNI 支持多 ENI 多辅助 IP, 提升单节点支持 Pod 数量;
  * VPC-CNI 支持固定 PodIP;
  * Service BLB 直挂 PodIP/NodeIP 统一，提升南北向网络性能;
  * 集成基于 Calico Felix 的 NetworkPolicy，支持网络隔离功能;
  * 上线 ip-masq-agent 支持知乎跨集群互访 PodIP;
  * 支持 NodeLocalDNS, 提升集群内 DNS 查询性能;
  * 支持 PodCIDR/ClusterIPCIDR 推荐配置, 提升网络规划体验;
  * kube-proxy 容器化部署改造，提高运维可观察性和可配置性;
  * 支持集团容器 Overlay 网络一期功能，CCE 与 EKS 实现 CNI 架构统一;
  * 支持内部上云用户 NodePort 的配置推荐，遵循公司安全规定;
* [容器存储]
  * CSI CDS Plugin 迭代，支持 Feature 包括:
    * NodeUnpublicVolume 幂等实现;
    * CDSPlugin 增加 xfs 支持;
    * CDSPlugin 支持 1.16 集群;
    * CDSPlugin 支持 helm 管理;
    * CDSPlugin 增加对 mount options 的支持;
    * CDSPlugin 支持在 kubelet 数据目录异构集群中的部署;
  * CSI BOSPlugin 迭代，支持 Feature 包括:
    * 跟随 bosfs 版本升级，支持新增的 bosfs 参数;
    * BOSPlugin 支持 1.16 集群;
    * BOSPlugin 支持 helm 管理;
    * BOSPlugin 支持在 kubelet 数据目录异构集群中的部署;
* [监控日志]
  * Pod 监控支持 GPU 指标;
  * 支持 K8S Event 持久化;
  * AlertManager 支持短信报警;
  * 服务画像支持 K8S 工作负载规范性检查;
  * A 区 K8S 集群新增 Thanos 统一监控;
  * K8S Pod 监控数据持久化至 Thanos, 支持历史查询;
  * 支持 node-problem-detector 实现 K8S Node 健康检查;
* [插件管理]
  * CCE K8S 插件接入 CI/CD 流程, 以 Helm Chart 方式交付;
* [应用管理]
  * 支持 Pod WebShell;
  * 支持 YAML 模板管理和自定义;
  * 支持 Console 表单部署工作负载, PV, PVC;
  * 控制台目录升级, 以集群为视角, 兼容 Serverless/BEC 等不同 K8S 集群;
* [资源弹性]
  * 支持应用自动扩缩容 HPA;
  * 支持应用定时伸缩 CronHPA;
  * 集群自动扩缩容支持 BCC Tags;
  * 集群自动扩缩容: 支持竞价实例; 扩容失败冷却策略;
  * 集群自动扩缩容: 后端实现切换为节点组, 实现声明式语义;
* [安全权限]
  * CCE 支持 OIDC 认证;
  * etcd 支持 KMS Secret 落盘加密;
  * CCE 支持多安全组: CCE 必选 + CCE 可选 + 用户自定义;
* [用户体验]
  * 客户集群升级 SOP;
  * 控制台新功能公告;
  * 支持国际化 Console;
  * 提示说明与文档链接优化;
  * 错误码格式化及报错提示优化;
* [OpenAPI/SDK]
  * V2 版本 OpenAPI/SDK 上线;
  * CCE 支持 Terraform, 满足 A 客户生产使用;
* [行业云私有化]
  * 公私架构统一(私有化环境 = 公共云 Region);

## 大客户支持

------------

* 知乎
* 斗鱼
* 车和家
* A 客户
* 一点资讯
