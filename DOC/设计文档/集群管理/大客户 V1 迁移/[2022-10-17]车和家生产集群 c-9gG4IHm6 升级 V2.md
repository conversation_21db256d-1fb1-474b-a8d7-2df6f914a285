# 车和家集群 c-9gG4IHm6 升级

## 升级影响

升级内容：北京 c-9gG4IHm6 升级为 cce-9gg4ihm6v1 Pro 版集群
升级原因：方便使用 CCE 最新功能，是升级 1.20 必要前提
升级时间：2023-03-29 22:00 ~ 23:00

升级影响:

1. 升级过程中对已有 K8S 集群及服务无影响
2. 升级过程中, LBService/Ingress 等变更会暂时不生效, 禁止集群扩缩容
3. 升级主要涉及元数据修改, 操作不可逆, 集群 ID 从 c-9gG4IHm6 变为 cce-9gg4ihm6v1
4. 升级完成后, 原先自动扩缩容组会失效, 后续推荐使用节点组来进行自动扩缩容: https://cloud.baidu.com/doc/CCE/s/zkispwj0p
5. 升级完成后, 无法使用 CCE V1 OpenAPI 进行扩缩容, 需要使用 V2 版本 API: https://cloud.baidu.com/doc/CCE/s/Mkgajonnp
6. 升级完成后, 原先的 K8S Event 持久化失效, 可在 Console 重新开启: https://cloud.baidu.com/doc/CCE/s/Xkh4gdrl8

## 升级步骤

### 前置检查

节点数量记录:

```md
# SELECT step_status,count(1) from t_instance where cluster_uuid = "c-9gG4IHm6" group by step_status;
+------------------+----------+
| step_status      | count(1) |
+------------------+----------+
| DELETED          |      818 |
| READY            |      785 |
| RUNNING          |        3 |
| VM_CREATE_FAILED |        5 |
+------------------+----------+

# kubectl get nodes | wc -l
786
```

LoadBalancer Service 记录:

```bash
# kubectl get services --all-namespaces=true | grep LoadBalancer | awk '{print $1,$2,$3,$4,$5}'
base apollo-as-prod-default-lb LoadBalancer 172.16.130.40 172.21.120.38
base apollo-cs-prod-default-lb LoadBalancer 172.16.228.190 172.21.120.37
base eureka-prod-cnhb01-cus-0-lb LoadBalancer 172.16.104.227 172.21.118.81
base eureka-prod-cnhb01-cus-1-lb LoadBalancer 172.16.155.227 172.21.118.83
base eureka-prod-cnhb01-cus-2-lb LoadBalancer 172.16.179.144 172.21.118.82
base eureka-prod-default-as-0-lb LoadBalancer 172.16.51.32 172.21.120.29
base eureka-prod-default-as-1-lb LoadBalancer 172.16.135.116 172.21.120.27
base eureka-prod-default-as-2-lb LoadBalancer 172.16.166.125 172.21.120.28
base eureka-prod-default-base-0-lb LoadBalancer 172.16.253.150 172.21.118.119
base eureka-prod-default-base-1-lb LoadBalancer 172.16.72.233 172.21.118.118
base eureka-prod-default-base-2-lb LoadBalancer 172.16.252.203 172.21.118.117
base eureka-prod-default-csc-0-lb LoadBalancer 172.16.175.231 172.21.118.58
base eureka-prod-default-csc-1-lb LoadBalancer 172.16.142.71 172.21.120.33
base eureka-prod-default-csc-2-lb LoadBalancer 172.16.189.219 172.21.120.34
base eureka-prod-default-lcp-0-lb LoadBalancer 172.16.54.88 172.21.120.32
base eureka-prod-default-lcp-1-lb LoadBalancer 172.16.115.52 172.21.118.56
base eureka-prod-default-lcp-2-lb LoadBalancer 172.16.7.47 172.21.120.31
base eureka-prod-default-oms-0-lb LoadBalancer 172.16.178.106 172.21.120.26
base eureka-prod-default-oms-1-lb LoadBalancer 172.16.100.179 172.21.118.57
base eureka-prod-default-oms-2-lb LoadBalancer 172.16.137.198 172.21.120.24
base eureka-prod-default-sales-0-lb LoadBalancer 172.16.186.184 172.21.120.25
base eureka-prod-default-sales-1-lb LoadBalancer 172.16.223.137 172.21.118.55
base eureka-prod-default-sales-2-lb LoadBalancer 172.16.15.124 172.21.120.30
base eureka-prod-default-srm-0-lb LoadBalancer 172.16.26.202 172.21.120.36
base eureka-prod-default-srm-1-lb LoadBalancer 172.16.41.178 172.21.120.35
base eureka-prod-default-srm-2-lb LoadBalancer 172.16.199.14 172.21.118.59
base eureka-prod-default-vrdos-0-lb LoadBalancer 172.16.246.185 172.21.118.72
base eureka-prod-default-vrdos-1-lb LoadBalancer 172.16.210.150 172.21.118.73
base eureka-prod-default-vrdos-2-lb LoadBalancer 172.16.243.42 172.21.118.74
base idaas-nacos-lb LoadBalancer 172.16.118.40 172.21.118.152
base lse-public-nacos-darcy-lb LoadBalancer 172.16.78.64 172.21.105.86
base op-k8s-scheduler-extender-lb LoadBalancer 172.16.149.138 172.21.25.20
base thanos-query-frontend-lb LoadBalancer 172.16.212.186 172.21.107.7
cnhb01-prod-as bcs-saas-gateway-as-service-lb LoadBalancer 172.16.241.51 ***********5
cnhb01-prod-csc bcs-saas-gateway-csc-lb LoadBalancer 172.16.174.203 ***********6
cnhb01-prod-csc gateway-csc-service-lb LoadBalancer 172.16.129.170 172.21.118.145
cnhb01-prod-cus gateway-cus-service-lb LoadBalancer 172.16.218.136 172.21.118.149
cnhb01-prod-deffi bcs-saas-gateway-deffi-lb LoadBalancer 172.16.189.147 172.21.118.14
cnhb01-prod-deffi eureka-prod-deffi-0 LoadBalancer 172.16.214.44 172.21.118.11
cnhb01-prod-deffi eureka-prod-deffi-1 LoadBalancer 172.16.201.123 172.21.105.120
cnhb01-prod-deffi eureka-prod-deffi-2 LoadBalancer 172.16.157.230 172.21.105.121
cnhb01-prod-dme eureka-dme-0-lb LoadBalancer 172.16.44.40 172.21.107.2
cnhb01-prod-dme eureka-dme-1-lb LoadBalancer 172.16.26.107 172.21.106.193
cnhb01-prod-dme eureka-dme-2-lb LoadBalancer 172.16.56.249 172.21.107.1
cnhb01-prod-dme gateway-dme-service-lb LoadBalancer 172.16.154.134 172.21.106.192
cnhb01-prod-lcp bcs-saas-gateway-lcp-lb LoadBalancer 172.16.93.173 *************1
cnhb01-prod-lcp gateway-lcp-service-lb LoadBalancer 172.16.105.171 172.21.118.146
cnhb01-prod-lcp iot-cloud-proxy-lb LoadBalancer 172.16.48.175 180.76.119.220
cnhb01-prod-lcp iot-cloud-rust-lb LoadBalancer 172.16.207.191 172.21.106.48
cnhb01-prod-lcp iot-cloud-rust-out-lb LoadBalancer 172.16.99.140 120.48.140.131
cnhb01-prod-lcp lcp-bd-siteselection-ui-lb LoadBalancer 172.16.170.125 172.21.118.170
cnhb01-prod-lcp lcp-bff-hmi-lb LoadBalancer 172.16.145.18 172.21.118.188
cnhb01-prod-lcp lcp-charge-ui-lb LoadBalancer 172.16.28.69 172.21.118.171
cnhb01-prod-lcp lcp-collie-ui-lb LoadBalancer 172.16.145.83 172.21.118.172
cnhb01-prod-lcp lcp-feature-probe-api-lb LoadBalancer 172.16.212.63 172.21.105.158
cnhb01-prod-lcp lcp-feature-probe-server-lb LoadBalancer 172.16.144.187 172.21.118.10
cnhb01-prod-lcp lcp-feature-probe-ui-lb LoadBalancer 172.16.215.208 172.21.118.9
cnhb01-prod-lcp lcp-ops-service-lb LoadBalancer 172.16.172.90 172.21.106.190
cnhb01-prod-lcp lcp-ops-ui-lb LoadBalancer 172.16.178.9 ***********9
cnhb01-prod-lcp saos-lcp-front-ui-lb LoadBalancer 172.16.127.28 172.21.118.173
cnhb01-prod-oms bcs-saas-gateway-oms-lb LoadBalancer 172.16.120.19 172.21.25.51
cnhb01-prod-oms gateway-oms-service-lb LoadBalancer 172.16.21.84 172.21.118.147
cnhb01-prod-sales bcs-saas-gateway-sales-lb LoadBalancer 172.16.168.6 172.21.25.52
cnhb01-prod-sales gateway-sales-service-lb LoadBalancer 172.16.14.94 172.21.118.148
cnhb01-prod-shanghaitrial eureka-prod-sh-0-lb LoadBalancer 172.16.171.57 172.21.106.49
cnhb01-prod-shanghaitrial eureka-prod-sh-1-lb LoadBalancer 172.16.111.213 172.21.118.23
cnhb01-prod-shanghaitrial eureka-prod-sh-2-lb LoadBalancer 172.16.20.31 172.21.118.22
emqx-mqtt emqx-lb LoadBalancer 172.16.109.214 120.48.15.205
emqx-mqtt emqx-mqtt-lb LoadBalancer 172.16.91.164 ***********4
prod aisp-account-b-ui-lb LoadBalancer 172.16.75.14 172.21.27.182
prod aisp-account-service-lb LoadBalancer 172.16.175.208 172.21.27.147
prod aisp-admin-web-lb LoadBalancer 172.16.178.26 172.21.27.210
prod aisp-amp-app-lb LoadBalancer 172.16.68.91 172.21.27.125
prod aisp-app-web-lb LoadBalancer 172.16.17.98 172.21.27.1
prod aisp-auth-service-lb LoadBalancer 172.16.30.203 172.21.27.146
prod aisp-charging-web-lb LoadBalancer 172.16.219.223 172.21.27.2
prod aisp-cms-web-lb LoadBalancer 172.16.193.214 172.21.27.0
prod aisp-comment-api-lb LoadBalancer 172.16.64.134 172.21.25.23
prod aisp-community-cms-lb LoadBalancer 172.16.156.65 172.21.27.5
prod aisp-cs-web-lb LoadBalancer 172.16.137.166 172.21.27.3
prod aisp-islands-cms-lb LoadBalancer ************* *************
prod aisp-kama-web-lb LoadBalancer ************** ***********
prod aisp-msc-service-lb LoadBalancer ************ *************
prod aisp-open-ui-lb LoadBalancer ************* *************
prod aisp-osp-web-lb LoadBalancer ************* *************
prod aisp-ownerservice-api-lb LoadBalancer ************* *************
prod aisp-questionnaire-api-lb LoadBalancer ************* ***********
prod aisp-starlink-api-lb LoadBalancer ************* *************
prod aisp-tripartite-service-lb LoadBalancer ************** *************
prod aisp-vqa-ui-lb LoadBalancer ************* *************
prod aisp-workflow-ui-lb LoadBalancer ************** *************
prod api-gateway-vehicle-lb LoadBalancer ************* ************
prod arcweld-bs1-czn-lb LoadBalancer ************* **************
prod autopilot-operation-platform-lb LoadBalancer ************** *************
prod autopilot-poseidon-service-lb LoadBalancer ************** ************
prod bcs-dataflow-kvproxy-service-lb LoadBalancer ************** ***********5
prod bcs-dijun-app-service-lb LoadBalancer ************** ************
prod bcs-dijun-boss-service-lb LoadBalancer ************** ************
prod bcs-dijun-openapi-service-lb LoadBalancer ************* ************
prod bcs-dijun-sap-lb LoadBalancer ************ **************
prod bcs-dijun-sap-service-lb LoadBalancer ************** ************
prod bcs-dijun-vehicle-service-lb LoadBalancer ************** ************
prod bcs-dijun-web-service-lb LoadBalancer **********41 172.21.24.49
prod bcs-jedi-app-service-lb LoadBalancer 172.16.214.119 172.21.27.229
prod bcs-jedi-app-slave-service-lb LoadBalancer 172.16.231.241 172.21.105.63
prod bcs-jedi-boss-service-lb LoadBalancer 172.16.255.50 172.21.27.225
prod bcs-jedi-fty-service-lb LoadBalancer 172.16.221.15 172.21.24.27
prod bcs-jedi-openapi-service-lb LoadBalancer 172.16.56.91 172.21.27.240
prod bcs-jedi-osd-service-lb LoadBalancer 172.16.136.83 172.21.25.24
prod bcs-jedi-sap-service-lb LoadBalancer 172.16.146.8 172.21.27.239
prod bcs-jedi-vehicle-service-lb LoadBalancer 172.16.54.147 172.21.27.235
prod bcs-jedi-web-service-lb LoadBalancer 172.16.226.190 172.21.27.230
prod bcs-jedi-web-slave-service-lb LoadBalancer 172.16.104.89 172.21.105.62
prod bcs-job-trace-api-c80-lb LoadBalancer 172.16.242.191 172.21.105.88
prod bcs-job-trace-api-lb LoadBalancer 172.16.7.190 172.21.27.224
prod bcs-job-trace-api-lb-80 LoadBalancer 172.16.55.166 172.21.24.181
prod bcs-k8s-java-demo-service-lb LoadBalancer 172.16.48.47 172.21.24.207
prod bcs-mms-api-base-service-lb LoadBalancer 172.16.129.49 172.21.27.17
prod bcs-mms-api-iot-service-lb LoadBalancer 172.16.103.161 172.21.26.239
prod bcs-mms-api-trigger-lb LoadBalancer 172.16.144.2 172.21.25.127
prod bcs-mms-saas-lb LoadBalancer 172.16.14.165 172.21.118.243
prod bcs-ois-base-api-lb LoadBalancer 172.16.80.228 ***********8
prod bcs-route-es-gateway-service-lb LoadBalancer 172.16.182.148 ***********1
prod bcs-route-gateway-service-lb LoadBalancer 172.16.217.139 ************8
prod bcs-saas-gateway-as-service-lb LoadBalancer 172.16.87.190 172.21.25.26
prod bcs-saas-gateway-csc-service-lb LoadBalancer 172.16.128.172 172.21.25.27
prod bcs-saas-gateway-cus-service-lb LoadBalancer 172.16.65.153 172.21.118.93
prod bcs-saas-gateway-iam-80-lb LoadBalancer 172.16.204.107 172.21.118.162
prod bcs-saas-gateway-iam-service-lb LoadBalancer 172.16.7.102 ***********3
prod bcs-saas-gateway-lcp-service-lb LoadBalancer 172.16.244.25 172.21.25.28
prod bcs-saas-gateway-oms-service-lb LoadBalancer 172.16.5.84 172.21.25.29
prod bcs-saas-gateway-sales-service-lb LoadBalancer 172.16.202.48 ***********0
prod bcs-saas-gateway-ssp-service-lb LoadBalancer 172.16.135.163 ************9
prod bcs-saas-gateway-vrdos-service-lb LoadBalancer ************* *************
prod bcs-short-url-service-lb LoadBalancer ************** ************
prod bcs-tokenserver-web-lb LoadBalancer ************** *************
prod bsp-cal-service-lb LoadBalancer ************ *************
prod bsp-dap-service-lb LoadBalancer ************** *************
prod bsp-diag-service-lb LoadBalancer ************ *************
prod bsp-vcp-pile-lb LoadBalancer ************* **************
prod cfe-bc-next-ui-lb LoadBalancer ************* *************
prod cfe-dip-api-connector-ui-lb LoadBalancer ************ *************
prod cfe-dmp-integration-lb LoadBalancer ************* *************
prod cfe-dmp-origin-lb LoadBalancer ************* **************
prod cfe-icp-lb LoadBalancer ************** **************
prod cfe-idaas-mgmt-ui-lb LoadBalancer ************** *************
prod cfe-lpai-web-manage-lb LoadBalancer ************* **************
prod cfe-metadata-ui-lb LoadBalancer ************** *************
prod chehejia-event-admin-lb LoadBalancer ************** ***********8
prod chehejia-key-admin-web-lb LoadBalancer ************** ************
prod chehejia-saos-cms-web-lb LoadBalancer *********** ************
prod chehejia-service-asbom-app-lb LoadBalancer ************* ************
prod chehejia-service-ats-api-lb LoadBalancer ********** ************
prod chehejia-service-bks-app-lb LoadBalancer ************** ************
prod chehejia-service-cv-lb LoadBalancer ************** ************
prod chehejia-service-diks-app-lb LoadBalancer 172.16.26.179 172.21.24.61
prod chehejia-service-dkms-app-lb LoadBalancer 172.16.107.221 172.21.118.17
prod chehejia-service-ota-web-lb LoadBalancer 172.16.85.123 ************6
prod chehejia-service-pcs-app-lb LoadBalancer 172.16.49.42 172.21.27.11
prod chehejia-service-vl-lb LoadBalancer 172.16.196.55 172.21.27.52
prod chehejia-task-boot-tsa-lb LoadBalancer 172.16.58.254 172.21.27.54
prod chj-service-esb-web-lb LoadBalancer 172.16.3.41 172.21.27.50
prod chj-service-rb-api-lb LoadBalancer 172.16.28.18 ***********7
prod chj-service-workflow-web-lb LoadBalancer 172.16.21.186 ***********8
prod club-lb LoadBalancer 172.16.53.65 172.21.27.220
prod cockpit-casemanager-backend-lb LoadBalancer 172.16.229.88 172.21.118.71
prod cockpit-li-newtest-fe-lb LoadBalancer 172.16.24.121 172.21.118.244
prod cockpit-monitor-web-service-lb LoadBalancer 172.16.9.199 172.21.24.192
prod cockpit-monitor-web-ui-lb LoadBalancer 172.16.106.85 172.21.24.179
prod cockpit-performance-web-lb LoadBalancer 172.16.172.183 172.21.106.255
prod cockpit-server-service-lb LoadBalancer 172.16.13.188 172.21.25.17
prod cockpit-taskmanager-backend-lb LoadBalancer 172.16.110.19 172.21.118.245
prod cockpit-voicemanager-web-lb LoadBalancer 172.16.230.190 172.21.24.35
prod cockpit-yidao-service-lb LoadBalancer 172.16.205.9 *************4
prod cpdop-april-tcp-lb LoadBalancer ************22 172.21.25.76
prod cpdop-april-web-lb LoadBalancer 172.16.223.54 172.21.25.77
prod cpdop-december-lb LoadBalancer 172.16.214.150 172.21.118.129
prod cpdop-goddess-lb LoadBalancer 172.16.29.13 172.21.118.130
prod cpdop-stream-lb LoadBalancer 172.16.61.254 172.21.118.131
prod crs-cds-front-lb LoadBalancer 172.16.87.38 172.21.105.85
prod crs-mr-api-lb LoadBalancer 172.16.103.8 ************1
prod da-adrobot-service-lb LoadBalancer 172.16.44.66 172.21.24.77
prod da-app-recommend-api-lb LoadBalancer 172.16.145.77 172.21.24.18
prod da-app-recommend-shield-api-lb LoadBalancer 172.16.115.126 ***********6
prod da-cdp-lb LoadBalancer 172.16.158.79 172.21.25.55
prod da-cxo-mobilepreview-ui-lb LoadBalancer 172.16.51.242 ************2
prod da-cxo2-admin-lb LoadBalancer 172.16.132.168 ************0
prod da-dispatch-service-lb LoadBalancer 172.16.41.156 ***********9
prod da-nlp-robot-lb LoadBalancer 172.16.202.219 172.21.118.127
prod da-nlp-services-lb LoadBalancer 172.16.21.80 ***********0
prod da-oei-service-lb LoadBalancer 172.16.25.151 172.21.24.234
prod da-pb-api-lb LoadBalancer 172.16.47.147 172.21.25.209
prod da-pb-ui-lb LoadBalancer 172.16.68.233 172.21.24.26
prod da-recommend-action-80-lb LoadBalancer 172.16.224.193 172.21.118.163
prod da-recommend-action-api-lb LoadBalancer 172.16.181.15 172.21.24.112
prod da-store-service-lb LoadBalancer 172.16.193.119 172.21.25.71
prod da-tms-services-lb LoadBalancer 172.16.42.152 172.21.118.133
prod dave-ac-lb LoadBalancer 172.16.36.253 ***********6
prod dave-air-suspension-czb-lb LoadBalancer 172.16.184.64 172.21.24.141
prod dave-air-suspension-czn-lb LoadBalancer 172.16.59.85 172.21.25.122
prod dave-air-suspension-x01-lb LoadBalancer 172.16.178.90 172.21.24.103
prod dave-airconditioner-x01-lb LoadBalancer 172.16.177.253 ************6
prod dave-as-rear-axle-lb LoadBalancer 172.16.129.220 ************4
prod dave-battery-fault-analysis-lb LoadBalancer 172.16.26.189 172.21.24.137
prod dave-bus-zdt-czb-lb LoadBalancer 172.16.149.235 ***********6
prod dave-child-safety-lb LoadBalancer 172.16.6.172 172.21.24.22
prod dave-cockpitauxiliary-m01-lb LoadBalancer 172.16.120.88 172.21.24.233
prod dave-czn-isv-lb LoadBalancer 172.16.11.120 172.21.24.105
prod dave-doornclosure-x01-lb LoadBalancer 172.16.66.224 ************0
prod dave-doors-and-closure-lb LoadBalancer 172.16.106.87 172.21.25.147
prod dave-ds-torque-huixiang-lb LoadBalancer 172.16.58.13 172.21.105.211
prod dave-electric-powertrain-lb LoadBalancer 172.16.125.46 ***********9
prod dave-ems-czn-lb LoadBalancer 172.16.24.41 ************0
prod dave-energy-monitors-lb LoadBalancer 172.16.209.169 ************3
prod dave-evr-evs-audit-czb-lb LoadBalancer 172.16.163.43 172.21.25.129
prod dave-factory-dashboard-lb LoadBalancer 172.16.232.242 172.21.25.12
prod dave-hemming-czb-lb LoadBalancer 172.16.35.238 172.21.25.128
prod dave-hemming-czn-lb LoadBalancer 172.16.235.118 172.21.106.57
prod dave-hpc-st-lb LoadBalancer 172.16.45.156 172.21.107.6
prod dave-ilad-rule-engine-lb LoadBalancer 172.16.36.230 172.21.24.99
prod dave-isv-weld-cz-lb LoadBalancer 172.16.177.246 ************6
prod dave-key-doors-lock-lb LoadBalancer 172.16.172.33 ***********0
prod dave-lianshan-adas-lb LoadBalancer 172.16.19.75 172.21.25.73
prod dave-lianshan-adas-x01-lb LoadBalancer 172.16.235.248 ************1
prod dave-lianshan-airconditioner-lb LoadBalancer 172.16.87.121 172.21.25.65
prod dave-lianshan-airsuspension-lb LoadBalancer 172.16.39.255 172.21.25.66
prod dave-lianshan-autoparking-lb LoadBalancer 172.16.151.52 ***********9
prod dave-lianshan-batteryfault-lb LoadBalancer 172.16.94.87 172.21.25.67
prod dave-lianshan-chargingnet-lb LoadBalancer 172.16.247.67 172.21.25.68
prod dave-lianshan-cockpit-lb LoadBalancer 172.16.170.96 172.21.24.143
prod dave-lianshan-cockpitauxiliary-lb LoadBalancer 172.16.55.13 ************5
prod dave-lianshan-cycletime-lb LoadBalancer 172.16.177.237 172.21.24.145
prod dave-lianshan-doornclosure-lb LoadBalancer 172.16.146.24 172.21.24.239
prod dave-lianshan-ecoat-lb LoadBalancer 172.16.140.33 ************2
prod dave-lianshan-lb LoadBalancer 172.16.86.197 172.21.24.210
prod dave-lianshan-lv-battery-lb LoadBalancer 172.16.238.217 ************3
prod dave-lianshan-paintmotor-lb LoadBalancer 172.16.9.51 172.21.24.146
prod dave-lianshan-qtools-lb LoadBalancer 172.16.229.152 ************1
prod dave-lianshan-quality-warning-lb LoadBalancer 172.16.170.34 ************4
prod dave-lianshan-range-extender-lb LoadBalancer 172.16.76.195 172.21.25.5
prod dave-lianshan-saftyscore-lb LoadBalancer 172.16.144.160 ************6
prod dave-lianshan-saftyscore-m01-lb LoadBalancer 172.16.133.71 ************2
prod dave-lianshan-seat-lb LoadBalancer 172.16.96.137 172.21.24.240
prod dave-lianshan-seat-x01-lb LoadBalancer 172.16.54.127 ************7
prod dave-lianshan-spotwelding-lb LoadBalancer 172.16.135.207 ************8
prod dave-lianshan-steerwheel-lb LoadBalancer 172.16.197.220 172.21.25.69
prod dave-lianshan-test-lb LoadBalancer 172.16.220.168 172.21.24.211
prod dave-lianshan-testvehiclemgmt-lb LoadBalancer 172.16.244.160 ************3
prod dave-lianshan-torque-lb LoadBalancer 172.16.6.166 172.21.118.189
prod dave-lianshan-userhabits-lb LoadBalancer 172.16.91.139 ************9
prod dave-lianshan-vehiclebattery-lb LoadBalancer 172.16.67.102 172.21.25.70
prod dave-liquid-filling-czb-lb LoadBalancer 172.16.118.173 172.21.24.193
prod dave-liquid-filling-czn-lb LoadBalancer 172.16.163.38 172.21.25.134
prod dave-low-volt-apparatus-lb LoadBalancer 172.16.208.53 172.21.24.176
prod dave-low-volt-lb LoadBalancer 172.16.101.179 ***********1
prod dave-lowbat-project-lb LoadBalancer 172.16.78.236 ***********5
prod dave-me-toolkit-lb LoadBalancer 172.16.97.180 172.21.118.239
prod dave-mes-glue-lb LoadBalancer 172.16.160.187 ************1
prod dave-paint-para-czb-lb LoadBalancer 172.16.130.68 ***********2
prod dave-positive-pressure-czb-lb LoadBalancer 172.16.84.113 172.21.25.102
prod dave-positive-pressure-czn-lb LoadBalancer 172.16.60.63 172.21.25.171
prod dave-quality-meeting-lb LoadBalancer 172.16.208.54 172.21.25.107
prod dave-sca-glue-cz-lb LoadBalancer 172.16.213.241 172.21.24.194
prod dave-sca-glue-czn-lb LoadBalancer 172.16.165.6 ************8
prod dave-screen-and-display-lb LoadBalancer 172.16.106.162 ***********7
prod dave-seat-system-lb LoadBalancer 172.16.16.192 ***********3
prod dave-spotwelding-czn-lb LoadBalancer 172.16.102.221 172.21.25.136
prod dave-sprwelding-cz-lb LoadBalancer 172.16.26.250 120.48.83.12
prod dave-sprwelding-czn-lb LoadBalancer 172.16.93.206 172.21.25.133
prod dave-stamping-cz-lb LoadBalancer 172.16.77.1 172.21.24.111
prod dave-stamping-czn-lb LoadBalancer 172.16.53.39 172.21.25.123
prod dave-steering-braking-system-lb LoadBalancer 172.16.251.93 172.21.25.50
prod dave-steering-system-lb LoadBalancer 172.16.4.8 172.21.24.38
prod dave-studwelding-cz-lb LoadBalancer 172.16.104.44 ***********8
prod dave-studwelding-czn-lb LoadBalancer 172.16.177.75 172.21.25.22
prod dave-system-personal-ac-lb LoadBalancer 172.16.210.3 172.21.24.229
prod dave-testline-czb-lb LoadBalancer 172.16.132.67 ************5
prod dave-testline-czn-lb LoadBalancer 172.16.160.101 172.21.25.170
prod dave-user-feedback-lb LoadBalancer 172.16.59.174 172.21.24.135
prod dave-veh-control-lb LoadBalancer 172.16.105.225 172.21.24.253
prod dave-veh-energy-lb LoadBalancer 172.16.60.120 172.21.25.149
prod dave-vehicle-control-lb LoadBalancer 172.16.144.132 172.21.24.136
prod dave-vehicle-crash-lb LoadBalancer 172.16.98.213 172.21.25.148
prod dave-vehicle-energy-x01-lb LoadBalancer 172.16.136.110 *************0
prod dave-vehicleenergyconsumption-lb LoadBalancer 172.16.6.127 172.21.24.232
prod dave-x03-cycletime-czn-lb LoadBalancer 172.16.251.141 ************0
prod dave-zdt-czn-lb LoadBalancer 172.16.246.113 172.21.107.104
prod db-mysql-proxy-iamacc-lb LoadBalancer 172.16.27.81 172.21.25.13
prod db-mysql-proxy-mallorder-lb LoadBalancer 172.16.182.111 172.21.24.24
prod db-mysql-proxy-mallpro-lb LoadBalancer 172.16.120.104 ************
prod db-mysql-proxy-mallref-lb LoadBalancer ************** ************
prod db-mysql-proxy-mallveh-lb LoadBalancer ************* ************
prod db-mysql-proxy-service-lb LoadBalancer ************ ************
prod db-mysql-proxy-ssp-vcs-service-lb LoadBalancer ************* ************
prod devops-sentinel-web-lb LoadBalancer ************* *************
prod dgraph-alpha-lb LoadBalancer ************ *************
prod dgraph-zero-lb LoadBalancer ************* *************
prod dip-ai-idaas-security-lb LoadBalancer *********** *************
prod dip-data-api-collect-api-lb LoadBalancer ************ *************
prod dip-dmp-opscenter-lb LoadBalancer ************ *************
prod dip-dmp-streaming-ui-lb LoadBalancer ************** *************
prod dip-log-service-lb LoadBalancer ************ **************
prod dip-owl-monitor-ui-lb LoadBalancer ************ *************
prod dip-project-service-lb LoadBalancer ************ ************
prod dip-saos-api-lb LoadBalancer ************** *************
prod dip-saos-web-lb LoadBalancer ************** *************
prod dip-veh-signal-matrix-service-lb LoadBalancer ************* ***********
prod factory-bs-kanban-app-lb LoadBalancer ************* ************
prod factory-cmc-web-lb LoadBalancer ************* ************
prod factory-cms-service-lb LoadBalancer ************** *************
prod factory-cms-web-lb LoadBalancer ************** *************
prod factory-cop-web-lb LoadBalancer ************ ************
prod factory-csp-web-lb LoadBalancer ************* ***********7
prod factory-cspm-web-lb LoadBalancer ************** ************
prod factory-das-app-web-lb LoadBalancer ***********4 *************
prod factory-das-wechat-web-lb LoadBalancer ************** *************
prod factory-edi-service-eip-lb LoadBalancer ************** *************
prod factory-ems-app-web-lb LoadBalancer ************** *************
prod factory-ems-service-lb LoadBalancer ************** *************
prod factory-hold-app-web-lb LoadBalancer ************* ************
prod factory-sdm-web-lb LoadBalancer ************* ***********6
prod factory-sip-web-lb LoadBalancer ************ ************
prod factory-spc-admin-lb LoadBalancer ************* *************
prod factory-spm-app-web-lb LoadBalancer ************* *************
prod factory-spm-service-lb LoadBalancer ************** *************
prod factory-spm-web-lb LoadBalancer ************** *************
prod factory-tms-app-web-lb LoadBalancer ************* ************
prod factory-tms-pc-web-lb LoadBalancer ************** **************
prod factory-vas-app-web-lb LoadBalancer ************ *************
prod factory-vas-web-lb LoadBalancer ************* *************
prod fe-app-web-lb LoadBalancer ************ ************9
prod fe-appmonitor-web-lb LoadBalancer ************ *************
prod fe-autoinfo-web-lb LoadBalancer ************ *************
prod fe-carhelp-web-lb LoadBalancer ************ *************
prod fe-carservice-web-lb LoadBalancer ************ *************
prod fe-community-web-lb LoadBalancer ************ *************
prod fe-cxoui-web-lb LoadBalancer ************** *************
prod fe-lixiangos-web-lb LoadBalancer ************** *************
prod fed-appcenter-web-lb LoadBalancer ************** *************
prod fed-appminiprogram-web-lb LoadBalancer ************* 172.21.24.131
prod fed-b-saos-ab-lb LoadBalancer 172.16.13.183 172.21.105.200
prod fed-bhub-web-lb LoadBalancer 172.16.192.10 172.21.24.177
prod fed-blockchain-api-lb LoadBalancer 172.16.121.244 172.21.118.135
prod fed-blockchain-portal-web-lb LoadBalancer 172.16.209.117 172.21.118.136
prod fed-blockchain-web-lb LoadBalancer 172.16.144.73 172.21.118.134
prod fed-blog-web-lb LoadBalancer 172.16.180.68 172.21.24.101
prod fed-budgetpanorama-web-lb LoadBalancer 172.16.159.58 172.21.25.142
prod fed-cdn-web-lb LoadBalancer 172.16.77.29 172.21.24.174
prod fed-cdnmanage-web-lb LoadBalancer 172.16.22.227 172.21.24.118
prod fed-chub-web-lb LoadBalancer 172.16.198.20 172.21.24.25
prod fed-coi-web-lb LoadBalancer 172.16.213.81 172.21.24.241
prod fed-cxo-materials-lb LoadBalancer 172.16.232.25 172.21.25.7
prod fed-cxoplus-web-lb LoadBalancer 172.16.64.13 ************1
prod fed-cxoui-web-lb LoadBalancer 172.16.76.53 172.21.24.127
prod fed-displace-web-lb LoadBalancer 172.16.113.239 172.21.118.164
prod fed-displacement-b-web-lb LoadBalancer 172.16.204.45 ***********4
prod fed-displacement-web-lb LoadBalancer 172.16.166.11 172.21.24.71
prod fed-employeeselfservice-web-lb LoadBalancer 172.16.88.209 172.21.105.72
prod fed-exception-web-lb LoadBalancer 172.16.246.174 172.21.25.226
prod fed-hub-web-lb LoadBalancer 172.16.132.101 172.21.27.31
prod fed-iconfont-web-lb LoadBalancer 172.16.72.207 172.21.24.223
prod fed-ide-web-lb LoadBalancer 172.16.167.72 172.21.24.235
prod fed-idealcollege-web-lb LoadBalancer 172.16.212.5 ***********2
prod fed-indicator-web-lb LoadBalancer 172.16.110.30 172.21.24.4
prod fed-ir-web-lb LoadBalancer 172.16.204.38 *************2
prod fed-lbc-web-lb LoadBalancer 172.16.137.80 172.21.24.231
prod fed-linote-export-api-lb LoadBalancer 172.16.51.150 172.21.25.78
prod fed-linote-web-lb LoadBalancer 172.16.63.118 172.21.24.180
prod fed-lisearch-web-lb LoadBalancer 172.16.170.255 172.21.24.33
prod fed-liui-api-lb LoadBalancer 172.16.74.88 172.21.27.64
prod fed-liui-bigdata-lb LoadBalancer 172.16.148.153 172.21.118.138
prod fed-liui-charts-lb LoadBalancer 172.16.225.18 172.21.118.137
prod fed-liui-mobile-web-lb LoadBalancer 172.16.4.148 172.21.25.79
prod fed-liui-pro-web-lb LoadBalancer 172.16.238.85 172.21.105.198
prod fed-liui-web-lb LoadBalancer 172.16.196.56 172.21.27.79
prod fed-liuimaterials-web-lb LoadBalancer 172.16.105.145 172.21.24.96
prod fed-liuiversion-web-lb LoadBalancer 172.16.109.80 172.21.24.106
prod fed-lixiangos-web-lb LoadBalancer 172.16.225.170 172.21.24.220
prod fed-manage-ui-lb LoadBalancer 172.16.67.120 172.21.27.78
prod fed-manual-web-lb LoadBalancer 172.16.49.198 ***********5
prod fed-marketinfor-web-lb LoadBalancer 172.16.247.240 172.21.27.185
prod fed-materials-web-lb LoadBalancer 172.16.121.112 172.21.24.115
prod fed-mktmanage-web-lb LoadBalancer 172.16.137.162 172.21.24.114
prod fed-onelinetouch-web-lb LoadBalancer 172.16.52.142 ***********5
prod fed-pb-web-lb LoadBalancer 172.16.206.113 ************0
prod fed-portal-web-lb LoadBalancer 172.16.57.84 172.21.27.126
prod fed-rbstore-web-lb LoadBalancer 172.16.20.255 172.21.24.6
prod fed-robotmanage-web-lb LoadBalancer 172.16.42.3 172.21.24.117
prod fed-screenshot-api-lb LoadBalancer 172.16.73.56 172.21.24.113
prod fed-server-api-lb LoadBalancer 172.16.190.128 172.21.27.184
prod fed-share-platform-web-lb LoadBalancer 172.16.145.113 172.21.104.23
prod fed-shorturl-web-lb LoadBalancer 172.16.190.218 172.21.27.60
prod fed-sight-web-lb LoadBalancer 172.16.136.40 172.21.24.212
prod fed-spread-web-lb LoadBalancer 172.16.35.160 172.21.27.102
prod fed-threejs-web-lb LoadBalancer 172.16.85.203 172.21.105.79
prod fed-tm-web-lb LoadBalancer 172.16.175.35 172.21.24.170
prod fed-tracker-web-lb LoadBalancer 172.16.128.147 172.21.24.36
prod fed-uploadmanage-web-lb LoadBalancer 172.16.26.14 172.21.24.116
prod fed-visitor-web-lb LoadBalancer 172.16.77.93 172.21.105.84
prod fed-warningrobot-web-lb LoadBalancer 172.16.9.141 172.21.24.129
prod fed-workinghours-web-lb LoadBalancer 172.16.177.41 *************3
prod fed-yapi-web-lb LoadBalancer 172.16.60.187 172.21.24.178
prod hx-airtight-lb LoadBalancer 172.16.4.130 172.21.106.56
prod iam-amp-ui-lb LoadBalancer 172.16.80.56 172.21.24.208
prod iam-idaas-account-ui-lb LoadBalancer 172.16.233.159 172.21.24.128
prod iam-idaas-auth-api-lb-new LoadBalancer 172.16.144.99 172.21.118.216
prod iam-idaas-auth-ui-lb LoadBalancer 172.16.245.171 ***********6
prod iam-idaas-document-web-lb LoadBalancer 172.16.183.135 ************4
prod iam-idaas-mgmt-api-lb LoadBalancer 172.16.246.205 172.21.24.250
prod iam-idaas-status-api-lb LoadBalancer 172.16.98.212 172.21.118.242
prod iam-li-partner-service-lb LoadBalancer 172.16.145.74 172.21.24.251
prod iam-li-user-api-lb LoadBalancer 172.16.26.251 172.21.105.151
prod iam-sso-b-ui-lb LoadBalancer 172.16.119.114 172.21.27.248
prod icn-gns-lb LoadBalancer 172.16.165.127 172.21.25.6
prod initializr-service-lb LoadBalancer 172.16.199.17 172.21.105.89
prod intc-kyc-autornrapigw-lb LoadBalancer 172.16.190.105 172.21.24.100
prod iot-admin-lb LoadBalancer 172.16.18.40 172.21.27.246
prod iot-cloud-ntp-service-lb LoadBalancer 172.16.97.121 172.21.118.217
prod iot-console-app-lb LoadBalancer 172.16.12.249 172.21.27.245
prod iot-device-shadow-lb LoadBalancer 172.16.94.47 ***********2
prod iot-rtc-gateway-service-lb LoadBalancer 172.16.101.162 172.21.24.242
prod it-a12n-evcs-web-lb LoadBalancer 172.16.4.165 172.21.105.209
prod it-hr-recruitment-web-lb LoadBalancer 172.16.86.109 *************7
prod it-js-purchase-srm-front-core-lb LoadBalancer 172.16.113.146 172.21.24.237
prod it-robot-api-lb LoadBalancer 172.16.88.201 172.21.25.120
prod it-vds-alteration-web-lb LoadBalancer 172.16.80.193 172.21.24.78
prod it-vds-perception-api-lb LoadBalancer 172.16.161.23 172.21.24.246
prod it-vds-perception-web-lb LoadBalancer 172.16.238.165 172.21.24.245
prod it-vds-qpp-api-lb LoadBalancer 172.16.199.64 172.21.24.252
prod it-vds-qpp-web-lb LoadBalancer 172.16.74.84 172.21.24.219
prod lcp-bd-siteselection-ui-lb LoadBalancer 172.16.246.228 ***********2
prod lcp-bff-app-lb LoadBalancer 172.16.253.206 172.21.118.132
prod lcp-charge-ui-lb LoadBalancer 172.16.121.85 172.21.118.168
prod lcp-collie-ui-lb LoadBalancer 172.16.5.184 172.21.118.169
prod m01-lb LoadBalancer 172.16.226.89 172.21.27.160
prod mall-activity-ui-lb LoadBalancer 172.16.152.192 172.21.118.12
prod mall-library-api-lb LoadBalancer 172.16.69.69 172.21.118.18
prod mall-market-ui-lb LoadBalancer 172.16.237.45 172.21.27.33
prod mall-robot-api-lb LoadBalancer ************** *************
prod mms-api-lb LoadBalancer ************* *************
prod mno-open-api-lx-lb LoadBalancer ************* **************
prod op-base-gns-api-lb LoadBalancer ************ *************
prod openchat-lb LoadBalancer ************** ************
prod osd-file-deal-server-lb LoadBalancer ************* *************2
prod osd-hr-attendance-web-lb LoadBalancer ************* **************
prod osd-hr-budget-web-lb LoadBalancer ************** *************
prod osd-hr-partnership-vendor-web-lb LoadBalancer ************ *************
prod route-gateway-service-lb-1 LoadBalancer ***********16 ************
prod saos-ab-ui-lb LoadBalancer ************* ***********7
prod saos-aes-api-lb LoadBalancer ************ *************
prod saos-as-web-lb LoadBalancer ************** ************
prod saos-atsback-web-lb LoadBalancer ************* ************
prod saos-atsfront-api-lb LoadBalancer ************ ************
prod saos-atsfront-ui-lb LoadBalancer ************ ************
prod saos-bd-ui-lb LoadBalancer ************** ************
prod saos-bd-web-lb LoadBalancer ************** *************
prod saos-bi-web-lb LoadBalancer ************* *************
prod saos-cbd-web-lb LoadBalancer ************** ***********
prod saos-cib-web-lb LoadBalancer ************** *************
prod saos-commons-charts-api-lb LoadBalancer ************** *************
prod saos-connect-web-lb LoadBalancer ************* *************
prod saos-cp-api-lb LoadBalancer ************* 172.21.27.75
prod saos-cp-web-lb LoadBalancer 172.16.210.171 172.21.27.12
prod saos-crm-call-api-lb LoadBalancer 172.16.219.76 172.21.27.142
prod saos-crm-rt-ui-lb LoadBalancer 172.16.85.12 172.21.105.128
prod saos-crm-ui-lb LoadBalancer 172.16.250.7 172.21.27.6
prod saos-cs-web-lb LoadBalancer 172.16.23.192 172.21.27.108
prod saos-cs-web-ui-lb LoadBalancer 172.16.75.152 172.21.105.159
prod saos-csm-ui-lb LoadBalancer 172.16.123.79 172.21.25.2
prod saos-csp-fssc-mobile-lb LoadBalancer 172.16.6.220 172.21.118.104
prod saos-csp-legal-web-lb LoadBalancer 172.16.46.199 172.21.25.75
prod saos-cts-api-lb LoadBalancer 172.16.163.83 172.21.27.61
prod saos-cv-ui-lb LoadBalancer 172.16.159.216 172.21.27.51
prod saos-cxo-free-api-lb LoadBalancer 172.16.67.22 172.21.27.110
prod saos-db-ui-lb LoadBalancer 172.16.129.219 ***********9
prod saos-dqueue-boot-admin-lb LoadBalancer 172.16.5.174 172.21.27.111
prod saos-dsd-web-lb LoadBalancer 172.16.137.95 172.21.27.233
prod saos-dss-front-lb LoadBalancer 172.16.185.223 172.21.27.18
prod saos-fdp-web-lb LoadBalancer 172.16.203.172 172.21.27.62
prod saos-feature-probe-server-lb LoadBalancer 172.16.142.152 ************2
prod saos-feature-probe-ui-lb LoadBalancer 172.16.220.230 172.21.105.201
prod saos-fed-seb-web-lb LoadBalancer 172.16.238.198 172.21.27.252
prod saos-fib-web-lb LoadBalancer 172.16.60.148 172.21.27.106
prod saos-inv-web-lb LoadBalancer 172.16.120.243 172.21.27.100
prod saos-lcp-front-ui-lb LoadBalancer 172.16.140.124 172.21.24.34
prod saos-legal-manage-web-lb LoadBalancer 172.16.140.243 172.21.25.74
prod saos-mall-admin-ui-lb LoadBalancer 172.16.162.42 172.21.27.32
prod saos-mall-web-lb LoadBalancer 172.16.144.180 172.21.27.35
prod saos-mam-ui-lb LoadBalancer 172.16.212.175 172.21.27.82
prod saos-mids-web-lb LoadBalancer 172.16.119.211 172.21.27.13
prod saos-pcs-web-lb LoadBalancer 172.16.163.96 172.21.27.243
prod saos-pdi-web-lb LoadBalancer 172.16.92.249 172.21.25.16
prod saos-pic-web-lb LoadBalancer 172.16.93.169 172.21.27.162
prod saos-pl-api-lb LoadBalancer 172.16.183.55 172.21.27.70
prod saos-pl-web-lb LoadBalancer 172.16.82.101 172.21.27.71
prod saos-print-service-50000-lb LoadBalancer 172.16.236.140 172.21.24.202
prod saos-print-service-lb LoadBalancer 172.16.149.151 172.21.24.201
prod saos-ptp-ui-lb LoadBalancer 172.16.187.83 172.21.24.186
prod saos-rb-front-lb LoadBalancer 172.16.73.112 172.21.27.20
prod saos-rb-game-ui-lb LoadBalancer 172.16.224.166 172.21.24.30
prod saos-rb-resource-front-lb LoadBalancer 172.16.25.42 172.21.105.199
prod saos-rcp-web-lb LoadBalancer 172.16.115.47 172.21.105.91
prod saos-report-web-lb LoadBalancer 172.16.252.112 172.21.27.19
prod saos-resources-ui-lb LoadBalancer 172.16.155.18 172.21.27.81
prod saos-rls-web-lb LoadBalancer ***********88 172.21.25.159
prod saos-sale-wo-ui-lb LoadBalancer 172.16.31.34 172.21.27.34
prod saos-scf-api-lb LoadBalancer 172.16.143.15 ***********8
prod saos-service-print-api-lb LoadBalancer 172.16.225.200 172.21.27.113
prod saos-service-wx-api-lb LoadBalancer 172.16.4.110 172.21.27.57
prod saos-store-dashboard-ui-lb LoadBalancer 172.16.39.229 172.21.118.241
prod saos-store-ui-lb LoadBalancer 172.16.66.94 172.21.25.207
prod saos-sup-ui-lb LoadBalancer 172.16.106.84 172.21.104.24
prod saos-talent-ui-lb LoadBalancer 172.16.41.181 172.21.118.240
prod saos-task-router-api-lb LoadBalancer 172.16.218.203 172.21.27.80
prod saos-tmt-web-lb LoadBalancer 172.16.139.204 172.21.27.254
prod saos-tp-web-lb LoadBalancer 172.16.238.50 172.21.27.107
prod saos-user-book-lb LoadBalancer ************** **************
prod saos-utp-api-lb LoadBalancer ************ **************
prod saos-vl-gateway-lb LoadBalancer ************* ***********4
prod saos-vl-ui-lb LoadBalancer ************* *************
prod saos-vl-web-lb LoadBalancer ************* ************
prod saos-wo-web-lb LoadBalancer ************ *************
prod saos-wp-gateway-service-lb LoadBalancer ************ *************
prod saos-wpserver-provid-service-lb LoadBalancer ************** *************
prod scm-sip-web-lb LoadBalancer ************* *************
prod sec-devsecops-aeacus-admin-lb LoadBalancer ************* ************
prod sec-pia-ui-lb LoadBalancer ************* *************
prod sec-pick-api-lb LoadBalancer ************* *************
prod sec-pick-ui-lb LoadBalancer ************** *************5
prod sec-soc-ui-lb LoadBalancer ************** ************
prod sec-src-api-lb LoadBalancer ************** *************
prod sec-src-ui-lb LoadBalancer ************ *************
prod sec-vsoc-ui-lb LoadBalancer ************* **************
prod sec-waf-bewaf-service-lb LoadBalancer ************** ************
prod smartspace-ssrde-lb LoadBalancer ************** ***********
prod srm-gateway-lb LoadBalancer ************** *************
prod ssai-qa-template-lb LoadBalancer ************ **************
prod ssp-admin-lb LoadBalancer ************* *************
prod ssp-app-config-service-lb LoadBalancer ************* *************
prod ssp-cloud-topic-light-service-lb LoadBalancer 172.16.197.249 172.21.118.140
prod ssp-control-config-service-lb LoadBalancer 172.16.238.115 172.21.24.198
prod ssp-faas-lb LoadBalancer 172.16.159.189 172.21.118.16
prod ssp-limesh-web-lb LoadBalancer 172.16.134.50 *************8
prod ssp-ns-admin-lb LoadBalancer ************** *************
prod ssp-push-task-service-lb LoadBalancer ************* **************
prod ssp-rnr-lb LoadBalancer ************* *************
prod ssp-vehcloud-web-lb LoadBalancer ************ ***********
prod ssp-vrds-service-lb LoadBalancer ************* *************6
prod store-webview-lb LoadBalancer ************** **************
prod vfsc-dp-service-lb LoadBalancer ************** **************
prod vfsc-vehicletestportal-web-lb LoadBalancer ************** *************
prod vrdos-ipd-quality-robot-web-lb LoadBalancer ************** **************
prod vrdos-ipd-spec-api-lb LoadBalancer ************ ************
prod vrdos-ipd-spec-web-lb LoadBalancer ************** ***********
prod vrdos-trial-production-wms-web-lb LoadBalancer ************* *************
prod vrdos-workbench-ultimate-web-lb LoadBalancer ************** ************
```

Ingress 记录:

```bash
# kubectl get ingress  --all-namespaces=true | grep 172.21
ingress-nginx       ingress-nginx                             *.prod.k8s.lixiang.com,*.prod.k8s.chehejia.com,*.prod-verify.k8s.chj.cloud + 1 more...   **************   80        2y286d
```

检查 account_id 确保一致:

```sql
$SELECT cluster_uuid,account_id,user_id FROM t_cluster WHERE cluster_uuid = "c-9gG4IHm6";
+--------------+----------------------------------+----------------------------------+
| cluster_uuid | account_id                       | user_id                          |
+--------------+----------------------------------+----------------------------------+
| c-9gG4IHm6   | 8ba8fe2d049948d88d04918b78ba820c | 8ba8fe2d049948d88d04918b78ba820c |
+--------------+----------------------------------+----------------------------------+

$SELECT instance_short_id,cluster_uuid,account_id,user_id,step_status,role FROM t_instance WHERE cluster_uuid = "c-9gG4IHm6" and step_status != "DELETED";

| i-UNivv5Dn        | c-9gG4IHm6   | 8ba8fe2d049948d88d04918b78ba820c | 2e3c69ed13fc4dd19afe1453d7dad9e2 | READY       | slave  |
| i-PjHgQdBF        | c-9gG4IHm6   | 8ba8fe2d049948d88d04918b78ba820c | 2e3c69ed13fc4dd19afe1453d7dad9e2 | READY       | slave  |
| i-jeUMgzc9        | c-9gG4IHm6   | 8ba8fe2d049948d88d04918b78ba820c | 2e3c69ed13fc4dd19afe1453d7dad9e2 | READY       | slave  |
| i-5Q3YVVG3        | c-9gG4IHm6   | 8ba8fe2d049948d88d04918b78ba820c | 2e3c69ed13fc4dd19afe1453d7dad9e2 | READY       | slave  |


$SELECT cluster_id,user_id FROM cluster_info WHERE cluster_id = "c-9gG4IHm6";
+------------+----------------------------------+
| cluster_id | user_id                          |
+------------+----------------------------------+
| c-9gG4IHm6 | 8ba8fe2d049948d88d04918b78ba820c |
+------------+----------------------------------+


$SELECT cluster_id,subuser_id FROM kubeconfig_info WHERE cluster_id = "c-9gG4IHm6";
+------------+----------------------------------+
| cluster_id | subuser_id                       |
+------------+----------------------------------+
| c-9gG4IHm6 | 99b53b35093f4eb4bd181f07431e7591 |
| c-9gG4IHm6 | a60c6c95bfd443bbaa5fb62753e0335d |
+------------+----------------------------------+

$select * from t_k8s_event_cluster where cluster_id = "c-9gG4IHm6"\G;
```

备份 /etc/kubernetes/cloud.config:

```json
{
    "AccessKeyID":"8e2fdc833cf44b4895afd0bce14f43cf",
    "SecretAccessKey":"7ae4ae1828694bbc814bb06fa87a43fa",
    "Region":"bj",
    "ClusterId":"c-9gG4IHm6",
    "ClusterName":"k8s-prod",
    "MasterId":"i-K8ORCcvh",
    "Endpoint":"*************:8693"
}
```

检查 hostname 匹配情况:

```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-v1-to-v2-pre-check-c-9gg4ihm6
  namespace: cce-trans
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    metadata:
      name: cce-trans
    spec:
      hostNetwork: true
      serviceAccount: cce-trans-serviceaccount
      imagePullSecrets:
      - name: ccr-registry-secret
      containers:
      - name: cce-trans
        image: registry.baidubce.com/cce-service-dev/v1-to-v2-pre-check:chenhuan
        imagePullPolicy: Always
        args:
        - -region=bj
        - -cluster-id=c-9gG4IHm6
        - -account-id=8ba8fe2d049948d88d04918b78ba820c
        - -enable-hostname=false
      restartPolicy: Never
```

### 停止插件

* ccm
* cluster-autoscaler
* cce-ingress-controller

```bash
$ systemctl stop kube-cloud-controller.service

$ kubectl scale --replicas=0 -n kube-system deployment/cce-ingress-controller
```

```bash
# kubectl get svc --all-namespaces | grep Load | grep pend
没有在 Pending 的 Service

# kubectl get svc --all-namespaces -o yaml > service-backup.txt
# kubectl get ingress --all-namespaces -o yaml > ingress-backup.txt
```

### 导出需要变更 annotation 的指令

该集群 LB Service BLB ID Annotation 为 service.beta.kubernetes.io/cce-load-balancer-id， 无需此步骤. 

### 元数据迁移

MetaCluster 运行如下 Job:

```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-trans-c-9gg4ihm6
  namespace: cce-trans
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    metadata:
      name: cce-trans
    spec:
      hostNetwork: true
      serviceAccount: cce-trans-serviceaccount
      imagePullSecrets:
      - name: ccr-registry-secret
      containers:
      - name: cce-trans
        image: registry.baidubce.com/cce-service-dev/cce-cluster-trans:chenhuan
        imagePullPolicy: Always
        args:
        - -region=bj
        - -cluster-id=c-9gG4IHm6
        - -account-id=8ba8fe2d049948d88d04918b78ba820c
        - -enable-hostname=false
        - -runtime-version=18.9.2
        - -lb-service-subnet-id=sbn-saug5w5iud0a
        - -node-port-range-min=30000
        - -node-port-range-max=50000
        - -cluster-pod-cidr=*********/17
        - -cluster-ip-service-cidr=**********/16
        - -max-pods-per-node=32
        - -kube-proxy-mode=ipvs
        - -enable-node-local-dns=false
        - -cluster-blb-id=lb-f8afef45
      restartPolicy: Never
```

检查:

* 检查 Cluster Spec;
* 容器网络模式为 kubenet;
* cluster-controller 日志, 不更新;
* instance-controller 日志, 不更新;

```sql
$SELECT * FROM t_cce_cluster WHERE cluster_id = "cce-9gg4ihm6v1";

$SELECT cluster_role,hostname FROM t_cce_instance WHERE cluster_id = "cce-9gg4ihm6v1";

$SELECT cluster_id,subuser_id FROM kubeconfig_info WHERE cluster_id = "cce-9gg4ihm6v1";
```

### 替换 cloud.config

Master 节点替换:

```bash
{
    "Region":"bj",
    "ClusterId":"cce-9gg4ihm6v1",
    "MasterID":"master-id",
    "ClusterName":"k-devops",
    "Endpoint":"*************:8693",
    "CCEV2Endpoint":"cce.bj.baidubce.com/api/cce/service/v2"
}
```

Node 节点替换, 用户集群运行如下 Job:

```yaml
kind: DaemonSet
apiVersion: apps/v1
metadata:
  name: cce-modify-cloud-config
  namespace: kube-system
spec:
  selector:
    matchLabels:
      app: cce-modify-cloud-config
  template:
    metadata:
      labels:
        app: cce-modify-cloud-config
    spec:
      restartPolicy: Always
      hostNetwork: true
      tolerations:
        - key: CriticalAddonsOnly
          operator: Exists
        - operator: Exists
      hostPID: true
      initContainers:
        - name: cce-system-config-init
          image: registry.baidubce.com/cce-plugin-dev/cce-system-config-init:chenhuan
          imagePullPolicy: Always
          command:
          - ./cce-system-config-init
          - --syncCloudConfig=true
          - --oldClusterID=c-9gG4IHm6
          - --newClusterID=cce-9gg4ihm6v1
          - --ccev2Endpoint=cce.bj.baidubce.com/api/cce/service/v2
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /etc/kubernetes
              name: cloud-config-dir
      containers:
        - name: cce-modify-cloud-config
          image: registry.baidubce.com/cce-public/busybox:latest
          command:
            - sleep
            - "inf"
          imagePullPolicy: IfNotPresent
      volumes:
        - name: cloud-config-dir
          hostPath:
            path: /etc/kubernetes
            type: Directory
```

```bash
$kubectl get pods -n kube-system -o wide | grep modify
```

检查项:

* Master cloud.config 正确;
* Node cloud.config 正确.

删除 ds:

```bash
$kubectl delete ds cce-modify-cloud-config -n kube-system
```

### 冻结 V1 集群

MetaCluster 执行如下 Job:

```yaml
apiVersion: batch/v1
kind: Job
metadata:
  name: cce-freeze-v1-c-9gg4ihm6
  namespace: cce-trans
spec:
  completions: 1
  parallelism: 1
  backoffLimit: 0
  template:
    metadata:
      name: cce-trans
    spec:
      hostNetwork: true
      serviceAccount: cce-trans-serviceaccount
      imagePullSecrets:
      - name: ccr-registry-secret
      containers:
      - name: cce-trans
        image: registry.baidubce.com/cce-service-dev/cce-freeze-cluster:chenhuan
        imagePullPolicy: Always
        args:
        - -region=bj
        - -cluster-id=c-9gG4IHm6
        - -account-id=8ba8fe2d049948d88d04918b78ba820c
      restartPolicy: Never
```

检查数据库对应状态:

```bash
$select cluster_uuid,status,step_status FROM t_cluster WHERE cluster_uuid = "c-9gG4IHm6";

$select cluster_uuid,instance_short_id,step_status FROM t_instance WHERE cluster_uuid = "c-9gG4IHm6";

$select cluster_id,is_deleted FROM cluster_info  WHERE cluster_id = "c-9gG4IHm6";
```

### 等待 cce-gateway Token 刷新

等待 kube-system 下 token 刷新:

```bash
$kubectl get secret -n kube-system cce-plugin-token -o yaml

# https://base64.supfree.net/
$mysql> select * FROM t_cluster_token WHERE  cluster_id = "cce-9gg4ihm6v1"\G;
```

### Cluster 新增字段

```bash
  k8sCustomConfig:
    nonMasqueradeCIDR: *********/17
```

更新数据库:

```sql
$SELECT * FROM t_cce_cluster WHERE cluster_id = "cce-9gg4ihm6v1"\G;

$UPDATE t_cce_cluster SET k8s_custom_config = '{"nonMasqueradeCIDR":"*********/17"}' WHERE cluster_id = "cce-9gg4ihm6v1";

$SELECT * FROM t_cce_cluster WHERE cluster_id = "cce-9gg4ihm6v1"\G;
```

### 重启 kube-external-auditer

```bash
systemctl restart kube-external-auditer.service
```

### 升级插件

升级 CCM:

```bash
$cd /opt/kube/bin/ && cp kube-cloud-controller-manager kube-cloud-controller-manager.back.2023.03.29

$wget -q -O  ./kube-cloud-controller-manager https://baidu-container.bj.bcebos.com/packages/cloudprovider/kube-cloud-controller-manager

$ md5sum kube-cloud-controller-manager
2ec3ebf57bfd8a1cff01ded9e1b74c20  kube-cloud-controller-manager

$ vi /etc/systemd/system/kube-cloud-controller.service
在 [Service] 的下增加一行: Environment=NODE_WORKERS=10

$ chmod +x kube-cloud-controller-manager; systemctl daemon-reload; systemctl restart kube-cloud-controller.service
$ journalctl -f -u kube-cloud-controller
```

升级 cce-ingress-controller:

```yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cce-ingress-clusterrole
rules:
  - apiGroups:
      - ""
    resources:
      - nodes
      - services
      - endpoints
      - configmaps
      - secrets
    verbs:
      - get
      - list
      - watch
      - create
      - update
      - patch
      - delete
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses
    verbs:
      - get
      - list
      - watch
      - update
      - patch
  - apiGroups:
      - ""
    resources:
      - events
    verbs:
      - create
      - update
      - patch
  - apiGroups:
      - extensions
      - networking.k8s.io
    resources:
      - ingresses/status
    verbs:
      - update
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cce-ingress-controller
  namespace: kube-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cce-ingress-controller
  template:
    metadata:
      labels:
        app: cce-ingress-controller
    spec:
      containers:
        - args:
            - '--region=bj'
            - '--cluster-id=cce-9gg4ihm6v1'
            - '--metrics-port=10302'
          env:
            - name: CONTROLLER_IMAGE
              value: registry.baidubce.com/cce-plugin-pro/cce-ingress-controller:2023.03.21.1727
            - name: SET_DEBUG
              value: 'true'
            - name: CCE_GATEWAY_ENDPOINT
              value: cce-gateway.bj.baidubce.com
            - name: BLB_ENDPOINT
              value: blb.bj.baidubce.com
            - name: EIP_ENDPOINT
              value: eip.bj.baidubce.com
            - name: VPC_ENDPOINT
              value: bcc.bj.baidubce.com
            - name: CCE_V2_ENDPOINT
              value: cce.bj.baidubce.com/api/cce/service/v2
            - name: TAG_ENDPOINT
              value: tag.baidubce.com/v1
            - name: EIP_PURCHASE_TYPE
          image: registry.baidubce.com/cce-plugin-pro/cce-ingress-controller:2023.03.21.1727
          imagePullPolicy: Always
          name: ingress-controller
          resources:
            limits:
              cpu: 500m
              memory: 500Mi
          volumeMounts:
            - mountPath: /etc/kubernetes/
              name: etc-volume
              readOnly: true
            - mountPath: /var/run/secrets/cce/cce-plugin-token
              name: cce-plugin-token
              readOnly: true
      dnsPolicy: ClusterFirst
      hostNetwork: true
      restartPolicy: Always
      serviceAccount: cce-ingress-serviceaccount
      serviceAccountName: cce-ingress-serviceaccount
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoSchedule
          key: node.cloudprovider.kubernetes.io/uninitialized
          value: 'true'
        - effect: NoSchedule
          key: node-role.kubernetes.io/master
          operator: Equal
      volumes:
        - hostPath:
            path: /etc/kubernetes/
            type: ''
          name: etc-volume
        - name: cce-plugin-token
          secret:
            defaultMode: 256
            secretName: cce-plugin-token
```

为 CCE Ingress 添加 Finalizer

```bash
# kubectl get ingress --all-namespaces | grep 172
ingress-nginx   ingress-nginx-chj-cloud             *.prod-devops.k8s.chj.cloud,do2.chj.cloud,do.chj.cloud                                              **************   80        2y110d

# kubectl edit ingress ingress-nginx-chj-cloud  -n ingress-nginx

在与 annotation 同缩进位置添加:
finalizers:
  - ingress.kubernetes.io/load-balancer-cleanup
```

### 更新 kubeconfig_info 记录

```bash
$mysql -u cce_service_w -p8bqvaLui3Pp3_nFt -h ************* -P 6202 -D cce_service

$UPDATE kubeconfig_info SET cluster_id = "cce-9gg4ihm6v1" WHERE cluster_id = "c-9gG4IHm6";

$SELECT *  FROM kubeconfig_info WHERE cluster_id = "c-9gG4IHm6";
$SELECT *  FROM kubeconfig_info WHERE cluster_id = "cce-9gg4ihm6v1";
```

### 后置检查

检查 Cluster CRD 状态 Running:

```bash
$kubectl get cluster cce-9gg4ihm6v1 -o yaml
```

检查 Instance CRD 状态:

```bash
$kubectl get instance |grep cce-9gg4ihm6v1 |wc -l
```

检查所有 Node Route 信息:

```bash
$kubectl get instancehealthcheck -n health-check   -l clusterID=cce-9gg4ihm6v1 -o custom-columns=name:.metadata.name,ready:.status.ready
```
