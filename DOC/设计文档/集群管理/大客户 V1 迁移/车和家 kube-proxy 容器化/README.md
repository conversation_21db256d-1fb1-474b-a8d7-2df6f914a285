## 1 背景

车和家的 v1 的集群还是采用二进制部署 kube-proxy 组件，在迁移至 v2 版本后，需要替换为容器化部署的 kube-proxy，否则在后续扩容中新加入节点中不包含 kube-proxy 。

## 2 升级流程

- 客户需要手工配合停二进制的 kube-proxy
- CCE 部署 kube-proxy 相关 yaml

## 3 升级方案

以 bj cce-hbnthrffv1 为例，二进制部署的 kube-proxy systemd 配置：

```
[Unit]
Description=Kubernetes Proxy
After=network.target

[Service]
ExecStart=/opt/kube/bin/kube-proxy \
--bind-address=************** \
--cluster-cidr=***********/19 \
--proxy-mode=ipvs \
--masquerade-all=true \
--hostname-override=************** \
--metrics-bind-address=************** \
--kubeconfig=/etc/kubernetes/kube-proxy.conf \
--logtostderr=true \
--master=https://*************:6443 \
--v=6
Restart=always
Type=simple
LimitNOFILE=65536

[Install]
WantedBy=multi-user.target
```

### 3.1 生成 kube-proxy 所需配置文件

修改 yaml 中 cluster-cidr 字段为集群中 pod 网段

```yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kube-proxy-config
  namespace: kube-system
  labels:
    app: kube-proxy-config
spec:
  selector:
    matchLabels:
      app: kube-proxy-config
  template:
    metadata:
      labels:
        app: kube-proxy-config
    spec:
      nodeSelector:
        beta.kubernetes.io/arch: amd64
      tolerations:
          - operator: "Exists"
      restartPolicy: Always
      hostNetwork: true
      containers:
        - name: busybox
          image: registry.baidubce.com/cce-plugin-pro/cce-cni:v1.3.3
          command:
            - bash
            - /tmp/update-proxy-yaml.sh
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
            - name: NODE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: etc-k8s
              mountPath: /etc/kubernetes/
            - name: shell
              mountPath: /tmp/

      terminationGracePeriodSeconds: 0
      volumes:
        - name: etc-k8s
          hostPath:
            path: /etc/kubernetes/
            type: "DirectoryOrCreate"
        - name: shell
          configMap:
            name: update-proxy-yaml-shell
            optional: true
            items:
              - key: update-proxy-yaml.sh
                path: update-proxy-yaml.sh

---
apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    addonmanager.kubernetes.io/mode: EnsureExists
  name: update-proxy-yaml-shell
  namespace: kube-system
data:
  update-proxy-yaml.sh: |-
    #!/bin/bash

    if [[ ! -e /etc/kubernetes/proxy.yaml ]]; then
      echo "/etc/kubernetes/proxy.yaml not exists, start creating."
      cat << EOF > /etc/kubernetes/proxy.yaml
    apiVersion: kubeproxy.config.k8s.io/v1alpha1
    bindAddress: ${NODE_IP}
    clientConnection:
      acceptContentTypes: ""
      burst: 10
      contentType: application/vnd.kubernetes.protobuf
      kubeconfig: /etc/kubernetes/kube-proxy.conf
      qps: 5
    clusterCIDR: **********/16
    configSyncPeriod: 15m0s
    conntrack:
      max: 0
      maxPerCore: 32768
      min: 131072
      tcpCloseWaitTimeout: 1h0m0s
      tcpEstablishedTimeout: 24h0m0s
    enableProfiling: false
    healthzBindAddress: 0.0.0.0:10256
    hostnameOverride: ${NODE_NAME}
    iptables:
      masqueradeAll: true
      masqueradeBit: 14
      minSyncPeriod: 0s
      syncPeriod: 30s
    ipvs:
      excludeCIDRs: null
      minSyncPeriod: 0s
      scheduler: ""
      strictARP: false
      syncPeriod: 30s
    kind: KubeProxyConfiguration
    mode: ipvs
    nodePortAddresses:
    - ${NODE_IP}/32
    - 127.0.0.1/32
    oomScoreAdj: -999
    portRange: ""
    resourceContainer: /kube-proxy
    udpIdleTimeout: 250ms
    featureGates:
      APIListChunking: true
      CSIDriverRegistry: true
      CSINodeInfo: true
      NodeLease: true
      VolumeSnapshotDataSource: true
      WatchBookmark: true
    EOF
    echo "/etc/kubernetes/proxy.yaml created, exit."
    else
      echo "/etc/kubernetes/proxy.yaml already exists, exit."
    fi
    sleep infinity
```

### 3.2 部署 kube-proxy daemonset

修改 kube-proxy 镜像版本为集群版本

```yaml
apiVersion: apps/v1
kind: DaemonSet
metadata:
  labels:
    k8s-app: kube-proxy
  name: kube-proxy
  namespace: kube-system
spec:
  selector:
    matchLabels:
      k8s-app: kube-proxy
  template:
    metadata:
      labels:
        k8s-app: kube-proxy
    spec:
      initContainers:
        - image: registry.baidubce.com/cce-plugin-pro/ipvs-modprobe:v1.0.0
          imagePullPolicy: Always
          name: kube-proxy-ipvs
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /lib/modules
              name: lib-modules
      containers:
        - command:
            - /usr/local/bin/kube-proxy
            - --config=/etc/kubernetes/proxy.yaml
            - --logtostderr=true
            - --v=2
          env:
            - name: NODE_NAME
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: spec.nodeName
            - name: NODE_IP
              valueFrom:
                fieldRef:
                  apiVersion: v1
                  fieldPath: status.hostIP
          image:  registry.baidubce.com/cce-plugin-pro/kube-proxy:v1.16.8
          imagePullPolicy: Always
          livenessProbe:
            failureThreshold: 3
            httpGet:
              path: /healthz
              port: 10256
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 5
            timeoutSeconds: 3
          name: kube-proxy
          securityContext:
            privileged: true
          volumeMounts:
            - mountPath: /run/xtables.lock
              name: xtables-lock
            - mountPath: /lib/modules
              name: lib-modules
              readOnly: true
            - mountPath: /etc/kubernetes/kube-proxy.conf
              name: kubeconfig
              readOnly: true
            - mountPath: /etc/kubernetes/proxy.yaml
              name: proxyconfig
              readOnly: true
      hostNetwork: true
      nodeSelector:
        beta.kubernetes.io/os: linux
      priorityClassName: system-node-critical
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      tolerations:
        - key: CriticalAddonsOnly
          operator: Exists
        - operator: Exists
      volumes:
        - hostPath:
            path: /run/xtables.lock
            type: FileOrCreate
          name: xtables-lock
        - hostPath:
            path: /lib/modules
            type: ""
          name: lib-modules
        - hostPath:
            path: /etc/kubernetes/kube-proxy.conf
            type: "File"
          name: kubeconfig
        - hostPath:
            path: /etc/kubernetes/proxy.yaml
            type: File
          name: proxyconfig
```
