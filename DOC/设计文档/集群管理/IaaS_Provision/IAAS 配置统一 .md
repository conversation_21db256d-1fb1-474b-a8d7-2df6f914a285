# IAAS 机型、OS、EIP线路类型 统一

## 第一期
### 背景

用户在新建集群、添加节点时，会遇到以下问题：

* 【新建】找不到某种机型；
* 【新建】找不到OS；
* 【新建】后端报错不支持该机型；
* 【新建】后端报错不支持该OS；
* 【移入】移入找不到实例；
* 【移入】移入BBC没有对应的套餐（白名单）。

### 预期

* 当用户遇到问题时，能临时绕过，将问题暴露在后端部署阶段（机型、OS影响的主要就是部署）；
* 过滤条件尽量收敛在后端，减少前端的校验、过滤逻辑。

### 方案

* 新建节点页面，前端从BCC接口获取机型（规格组）列表，只要没有额外过滤，就可以做到与BCC及时同步（有些架构下的机型暂时不支持）；
* CCE后端提供可使用的虚机OS列表、可移入节点列表，新OS、机型需要测试，通过上线更新；也`支持通过指定参数返回全部信息`;
* 后端新建集群、添加集群接口，`不再对 OS、机型做校验`；
* 前端需要支持`临时绕过的交互`，通过指定参数，从后端获取全部信息；由于用户绕过的成本低，需要给警告提示。

### 成果
* 移入节点列表：使用粗粒度参数代替具体机型、规格；
* 后端提供 OS 接口；
* json 编辑可以替代临时绕过方案；

# 2020-12-01 更新
### 目标：
* 能够主动、快速地自动支持 IaaS(BCC/BLB/EIP) 新产品或类型；
* 公有云、私有云同步更新。

### 短期：
1. 机型和 OS 自动化回归测试，把不需要改代码就能支持的，先开放 @qa；
2. 把应该支持，但还没支持的，主动实现(ARM、2代裸金属之类的）.

### 长期：

前端全开放，后端只维护 Unsupported，并且 Unsupported 有意义的主动解决.

### 实现：

* instance os 列表：由后端返回 supported os； 接口参考bcc；
* 规格组列表： bcc sdk + 后端unsupported instanceType；
* 对于kunlun这种极个别的特殊情况，暂时不在设计上考虑；
* 新增机型的识别与开放由后端定期轮询、更新；
* 前端：每个类型组需要有固定的默认值；例如 Ubuntu 默认值为16.04 （考虑到不同场景下对默认值的需求不同，可能需要后端返回？？？）。

### 后端分级：
* supported：正常支持的类型
* unsupported：不支持、未验证的类型
* forbidden：某些情况下，明确禁止使用的类型或组合

### 接口设计

#### 不支持的机型

**描述**

不支持的机型

**请求结构**

    POST /v1/instance/unsupported_instance_type HTTP/1.1
    Host: cce.bj.baidubce.com
    Authorization: authorization string

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称               | 类型                                                                                               | 是否必需 | 参数位置          | 描述                                                                |
| ------------------ | ------------------------------------------------------------------------------------------------ | ---- | ------------- | ----------------------------------------------------------------- |


**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称        | 类型           | 描述     |
| ----------- | ------------ | ------ |
| instanceTypes     | List\<String\> | 不支持的instanceType,例如：[1,2] |


#### 支持的os

**描述**

支持的os

**请求结构**

    POST /v1/image/supported_os HTTP/1.1
    Host: cce.bj.baidubce.com
    Authorization: authorization string

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称               | 类型   | 是否必需 | 参数位置          | 描述                                                                |
| ------------------ | --------------- | ---- | ------------- | ------|
| imageTypes     | List\<String\> | 镜像类型 |body 参数 | ------|

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称        | 类型           | 描述     |
| ----------- | ------------ | ------ |
| images     | List\<Image\> | 镜像列表 |


#### 不支持的线路类型

**描述**

不支持的线路类型

**请求结构**

    POST /v1/eip/unsupported_route_type HTTP/1.1
    Host: cce.bj.baidubce.com
    Authorization: authorization string

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称               | 类型                                                                                               | 是否必需 | 参数位置          | 描述                                                                |
| ------------------ | ------------------------------------------------------------------------------------------------ | ---- | ------------- | ----------------------------------------------------------------- |


**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称        | 类型           | 描述     |
| ----------- | ------------ | ------ |
| types     | List\<String\> | 线路类型列表：ChinaMobile、ChinaTelcom、ChinaUnicom、Static|