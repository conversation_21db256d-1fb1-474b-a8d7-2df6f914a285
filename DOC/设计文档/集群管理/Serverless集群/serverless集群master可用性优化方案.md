# Serverless 集群Master可用性优化设计

## 一、背景

在gztest环境中，观察到一种现象：serverless集群的master pod被删除。
考虑到serverless集群的master是通过pod进行管理的，通过vk来联动到bci pod，从而serverless集群的master比普通集群使用bcc部署的方式更为脆弱，影响因素：

 * 更容易被误删；
 * vk自身的bug导致master pod被删；
 * vk pod被驱逐从而影响master pod；
 * kubelet重启影响vk pod从而影响master pod；
 * docker重启影响vk pod从而影响master pod；
 * meta集群node not ready 影响vk pod从而影响master pod；

这些因素都可能会对serverless集群的master pod产生稳定性影响，从而影响整个集群的master的可用性。因此，需要对此进行优化。

## 二、改造的目标

* master pod 可自动恢复；

从而就要求：

 * 异常master pod可删除；
 * master pod被删除后，能够自动拉起新pod；
 * 新pod替换老pod后，blb后端解绑老pod ip，绑定新pod ip；
 * master pod 可通过自动化的探活机制来删除异常pod；（留拓展空间，可和节点自愈结合）
 
## 三、方案

### 3.1 架构图

![架构图](serverless集群master可用性优化.png)

### 3.2 cluster controller 改造

 * 原本负责创建BLB，改为负责创建LoadBalancer的service；
 * service中通过annotations来添加accountID、userID；（service.kubernetes.io/account-id，service.kubernetes.io/user-id）
 * service和pod间通过label：cluster-id 来进行关联；
 
### 3.3 instance controller 改造
 
 * 原本负责创建pod，改为创建deployment；
 * deployment中需要保证pod具备label：cluster-id；
 
### 3.4 service controller 改造
 
 * 需要支持多租户模式；
 * 从service 的annotations中获取accountID，通过临时sts授权来创建用户的blb/eip资源；
