# Terraform CCEv2 删除指定节点

## 原理    
Terraform 的 CCEv2 使用 InstanceGroup 来管理 Worker 节点。    
每个节点都具有一个 Priority 值，在 InstanceGroup 缩容（即减小replicas值）的时候，Priority 低的节点将优先被删除。    
因此，想要删除一个 InstanceGroup 里某个指定节点的时候，需要先更新这个节点的 Priority 为最低，然后再修改 InstanceGroup 的 Replicas 值。        
节点的默认 Priority 是 5。  

## CCEv2 使用实例    
示例文件：https://github.com/baidubce/terraform-provider-baiducloud/blob/master/examples/baiducloud-ccev2/main.tf     

## 操作步骤     
1. 找到要删除的节点是在哪个 InstanceGroup    
   如果找不到的话可以使用 data baiducloud_ccev2_instance_group_instances 来列出节点组的节点找找看     

2. 使用 baiducloud_ccev2_instance 资源更新指定删除节点的 Priority 为 1  （如示例文件 38 ~ 44 行）       
```
resource "baiducloud_ccev2_instance" "default" {
  cluster_id        = "Your-Cluster-ID"
  instance_id       = "cce_instance_id-You-Want-To-Delete"
  spec {
    cce_instance_priority = 1 
  }
}
```
  
3. 执行 terraform apply 完成 instance priority 的更新    

4. 修改第一步记下的那个 InstanceGroup 的 tf 配置里的 spec.replicas 为之前的值减 1（表示减少 1 个节点） （如示例文件 49~51 行与 342 行）    

5. 执行 terraform apply 完成 InstanceGroup 缩容    

6. 删除掉第二步的 baiducloud_ccev2_instance 资源的 tf 配置 （因为资源已经删除了）   