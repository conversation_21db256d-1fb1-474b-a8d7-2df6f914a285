# 值班报警处理方法速查

## 本文件中未查询到的报警
### 到这里看看有没有
[CCE运维SOP](http://wiki.baidu.com/pages/viewpage.action?pageId=902821488)
[Wing运维SOP](http://wiki.baidu.com/pages/viewpage.action?pageId=917578650)

## DISK_MNT_USED_PERCENT=97.11	
### 原因
机器的 /mnt 分区磁盘快写满了。一般是磁盘上塞了大量日志文件。

### 解决方法
登录到机器上，进入 **/mnt/work**文件夹中寻找占用空间较大的几个文件夹，按图索骥，进入到它们的**log**文件夹里删掉一些时间最早的日志。
一般/mnt所在磁盘剩余100G暂时是比较安全的。

### 参考指令 
查看目录下各个文件和文件夹的空间占用量  
```
du --max-depth=1 -h*
```
查看各个磁盘的空间余量
```
df -h
```
删除日志文件
```
rm -rf 20201030.log
```

### 比较容易发生此问题的机器
ssh <EMAIL>-cq02
ssh <EMAIL>-bjyz
ssh <EMAIL>-szth
ssh <EMAIL>-gzns
ssh <EMAIL>-bjyz
ssh <EMAIL>-cq02



## 	DISK_MAX_PARTITION_USED_PERCENT=98.02 
### 原因
机器有些磁盘快写满了，一般是日志文件太多。

### 解决方法
登录到机器上，从根目录开始查占用较大空间的目录，去其中的log目录删除掉一些最早的日志

### 参考指令 
查看目录下各个文件和文件夹的空间占用量  
```
du --max-depth=1 -h*
```
查看各个磁盘的空间余量
```
df -h
```
删除日志文件
```
rm -rf 20201030.log
```


### 参考：以下目录经常持有大量日志文件
```
/mnt/work/gnat
/mnt/work/register_manager
/mnt/work/gobridge
```


## dm_space_avail=14.94   dm_space_avail=0
### 原因
Docker DeviceMapper 空间不足。
ocker的所有镜像、缓存资源都会存储在devicemapper这个目录下，所以会导致这个目录占用磁盘极大，甚至会耗尽所有的服务器硬盘。
需要设法清理掉机器上的一些容器。
详细参考[wing机器节点docker device mapper空间低](http://wiki.baidu.com/pages/viewpage.action?pageId=921736729)

### 解决方法 
**第一步**：执行机器上空洞收缩脚本（脚本幂等）， 清理尸体容器 
```
bash /home/<USER>/opbin/fstrim_containers.sh
docker ps -a | grep Dead |awk '{print $1}' | xargs docker rm
```
**第二步**：如果此时Docker DeviceMapper空间还是比较小（低于50G）,需要将一些磁盘占用较高容器踢出本节点
```
注意事项
1. docker ps 检查Docker是否已经Hang住，如果docker hang住，首先解决docker hang的问题
2. 删除Pod后需要检查新的Pod已经启动并正常运行
3. 删除Pod后Pod经常会有300秒的terminating状态，需要等待
4. 一次操作一个Pod 
```


将一些磁盘占用率高的Pod漂移到别的节点上
```
# 查看业务容器占用磁盘的空间 
docker ps | grep dumb | awk '{print $1}' | while read cid; do docker exec $cid df.old -h | grep rootfs ; echo $cid; done

# 当dm_space_avail大于0时
##   挑选占用率较大的容器，获取其全名，以便提取Pod名称和命名空间
docker ps | grep e1801778
##   获取Pod的Namespace和Pod名称
docker ps | grep e1801778 | awk -F "_" '{print $3" -n "$4}'
##   从当前节点删除该Pod
kubectl delete pod POD_NAME -n POD_NAMESPACES
##   清理尸体容器（有时DEAD容器不会自动删除）
docker ps -a | grep Dead |awk '{print $1}' | xargs docker rm

# 当dm_space_avail为0时
##  找到被挂载为read-only的容器的全名
grep read-only /var/log/messages | awk -F '(' '{print $2}' | awk -F ')' '{print $1}' | sort | uniq | while read dmid; do cid=$(lsblk| grep $dmid | head -1 | awk -F ':' '{print $2}' | awk -F '-' '{print $3}' | awk '{print $1}'); echo $cid; docker ps | grep ${cid:0:7}; done
##   获取Pod的Namespace和Pod名称
docker ps | grep e1801778 | awk -F "_" '{print $3" -n "$4}'
##   从当前节点删除该Pod
kubectl delete pod POD_NAME -n POD_NAMESPACES
##   清理尸体容器（有时DEAD容器不会自动删除）
docker ps -a | grep Dead |awk '{print $1}' | xargs docker rm
```



