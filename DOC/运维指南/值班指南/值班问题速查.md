# 值班问题速查 

##  Cluster 类
### 用户无法连接集群
**现象1**    
用户使用kubeconfig无法连接到集群  
**排查**    
检查用户有没有使用正确的Kubeconfig。例如在公网环境使用vpc内部kubeconfig会无法连接。
登录用户机器检查kubectl能否正常使用，api-server是否都在正常运行  
**处置**    
如果api-server没在运行。可能需要重启服务。
如果虚拟机本身故障，可能需要联系Op重启机器。  

## Instance 类
### 移入节点报错
**现象1**    
查节点的Event事件发现 SSH 错误  
```
'ssh: handshake failed: read tcp 10.63.99.48:40041->100.73.7.200:22: read: connection reset by peer'
或
'ssh: handshake failed: ssh: unable to authenticate, attempted methods [none:password], no supports methods remain.'
```
**排查**    
注意检查一下节点的CRD数据，检查此节点是否是用户移入集群的已有节点。
如果是且用户未勾选重装系统的话，可能是用户把机器密码输错了。   
在少数情况下BCC那边会出现节点不稳定现象，也会出现类似报错。  
**处置**    
请用户移出错误节点，再移入。并建议用户在不影响的情况下勾选『重装系统』。  
  
**现象2**    
发现节点部署K8S 相关组件报错，检查日志发现  
```
....
E: Unmet dependencies. Try 'apt-get -f install' with no packages (or specify a solution). 、
....
```
**排查**    
包安装失败。
用户可能是移入的已有节点并且没有重装系统，系统中有一些之前就存在的包和k8s需要的包互相冲突导致安装失败。  
**处置**    
登录机器解决软件包冲突。  

**现象3**    
创建节点失败，卡在EIP那一步。
检查日志等信息发现报错  
```
Account has no permission to bind EIP.", Error Code: "Instance.EipOperationDenied", Status Code: 403
```
**排查**    
检查用户是否是公司内部上云账号。
这些账号对EIP的使用收到限制。  
**处置**    
根据百度内部业务上云规范(http://security.baidu.com/ssp/web/#/require/work/detail?id=105&from=page)，已将全部内部业务上云账号加入3类黑名单（BLB、EIP、DNAT），从控制台层面直接禁止相关高危操作，已发过邮件通告所有的产品安全负责人和主账号负责人。若对外提供服务请使用BLB(https://cloud.baidu.com/doc/BLB/s/kk0d9jrvo), 若需要访问外网请使用SNAT(https://cloud.baidu.com/doc/VPC/s/Cjwvyu10x ).如后续有业务存在特殊情况需要脱离黑名单，需进行安全备案（http://security.baidu.com/myflaw/web/#/report/apply），说明业务场景并完成相应的加固措施，如安全部@张智鹏02 审核通过，再将对应帐号从对应黑名单删除。黑名单说明：http://wiki.baidu.com/pages/viewpage.action?pageId=1053673557
  

## Pod 类
### Pod访问网络出现问题
**现象1**    
Pod访问一个对等链接VPC中的BLB是不通的，但是在Pod所在节点中访问是可以通的。  
**排查**    
Pod访问对等链接网络没有做SNAT，会导致返回Pod的包被对等链接VPC判定为内部地址而无法返回给Pod。  
需要对Pod做SNAT让包返回到Pod来。
**处置**    
修改IP Masq Agent的yml配置，使得其可以对对等链接VPC的网段做SNAT。  
![图片](./pics/IPMasqAgent修改SNAT配置.png)
   
**现象2**    
用户的集群部署在具有内网专线的vpc，通过node机器可以访问内网，但是通过pod不行   
**排查**    
Pod访问内网网段但是没做SNAT，返回Pod的包可能回不来。  
**处置**    
修改IP Masq Agent的yml配置，使得其可以对访问内网的网段做SNAT。
注意内网网段与集群的Service ClusterIP可能有冲突,需要特别处理。    

**现象3**    
某个Pod无法ping通，但是Ping其他pod和节点是通的。   
**排查**    
检查用户在Pod内有无手动配置iptables，某些iptable会drop掉非TCP类型包导致pod的ping返回包被丢弃。  
![图片](./pics/丢弃ICMP包的iptables.png)
   
**现象3**    
Pod连不上外网   
**排查**    
在排除公网连通性的情况下，需要留意MTU的问题。
存量的 VPC/BCC 没有做升级导致一些网络设备或BCC的MTU仍为1400。
而Pod一般是1500，可能导致包被丢弃。   
**处置**    
修改MTU为合适的值。  

**现象4**    
节点上的 Allocatable GPU 不能满足 Pod 的 Request，报错如图    
![图片](./pics/20210104-Pod无法获得Request的GPU.png)    

**排查**     
登录 Master 查看节点 GPU 信息发现节点共有两个 GPU 但只有一个可分配 
```
# kubectl describe instance 
```      
![图片](./pics/20210104-Pod无法获得Request的GPU-节点信息.png)    
登录出错 Pod 所在节点发现并无进程在使用 GPU    
```
# nvidia-smi
``` 
![图片](./pics/20210104-Pod无法获得Request的GPU-节点GPU信息.png)      
查看出错节点的 kubelet 信息发现只注册了一个 GPU，可以判定不知什么原因只有一个GPU成功注册     
```
# cat /var/lib/kubelet/device-plugins/kubelet_internal_checkpoint   
```    
![图片](./pics/20210104-Pod无法获得Request的GPU-插件信息记录.png)       
**处置**     
重启节点的 nvidia-device-plugin 这个插件是 daemonset 形式部署的，因此杀掉 Pod 即可             
![图片](./pics/20210104-Pod无法获得Request的GPU-处理完成插件信息.png)     


### Pod无法启动
**现象1**    
查询k8s event事件发现报错   
```
...
no IP addresses available in network
...
```   
**排查**    
查看错误Pod所在的节点上有多少个Pod。
数量比较多的时候可能是节点的Pod太多了，耗尽了节点所被分配的Pod网段     
**处置**    
单节点能容纳的最大Pod无法调大，建议用户向集群增加节点。  


## PV、PVC 类
### 用户创建动态PVC但是一直Pending
**现象1**    
用户在非1.11和1.13版本k8s使用helm部署pvc但一直pending 。
检查PVC的Event后发现报错信息   
```
waiting for a volumn to be created, either by external provisioner "csi-cdsplugin" or manually created by system administrator.
```    
**排查**    
截止到2020-11-03的时候CCE helm对pvc的支持仅有1.11和1.13。
查看一下用户的k8s版本。   
**处置**    
如果用户是1.16.8，告知用户helm暂时没支持到这个版本，请用户按照CCE PVC的文档进行处理。
[动态PVC挂载CDS](https://cloud.baidu.com/doc/CCE/s/Tjxppp24p#动态pvpvc方式挂载cds)  
   
**现象2**    
用户在1.13版本k8s使用helm部署pvc但一直pending 。
检查PVC的Event后发现报错信息   
```
failed to provision volumn with StorageClass "cds-hp1": rpc error: code = Internal desc = zone name can not be empty
```    
**排查**    
检查用户是否是cce-v2的新集群，即集群ID以cce-开头。
截止到2020-11-03的时候，CCEv2集群有遗漏CRD。    
**处置**    
帮用户部署两个CRD。   
```
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  name: csinodeinfos.csi.storage.k8s.io
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
spec:
  group: csi.storage.k8s.io
  names:
    kind: CSINodeInfo
    plural: csinodeinfos
  scope: Cluster
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: Specification of CSINodeInfo
          properties:
            drivers:
              description: List of CSI drivers running on the node and their specs.
              type: array
              items:
                properties:
                  name:
                    description: The CSI driver that this object refers to.
                    type: string
                  nodeID:
                    description: The node from the driver point of view.
                    type: string
                  topologyKeys:
                    description: List of keys supported by the driver.
                    items:
                      type: string
                    type: array
        status:
          description: Status of CSINodeInfo
          properties:
            drivers:
              description: List of CSI drivers running on the node and their statuses.
              type: array
              items:
                properties:
                  name:
                    description: The CSI driver that this object refers to.
                    type: string
                  available:
                    description: Whether the CSI driver is installed.
                    type: boolean
                  volumePluginMechanism:
                    description: Indicates to external components the required mechanism
                      to use for any in-tree plugins replaced by this driver.
                    pattern: in-tree|csi
                    type: string
  version: v1alpha1
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  name: csidrivers.csi.storage.k8s.io
  labels:
    addonmanager.kubernetes.io/mode: Reconcile
spec:
  group: csi.storage.k8s.io
  names:
    kind: CSIDriver
    plural: csidrivers
  scope: Cluster
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: Specification of the CSI Driver.
          properties:
            attachRequired:
              description: Indicates this CSI volume driver requires an attach operation,
                and that Kubernetes should call attach and wait for any attach operation
                to complete before proceeding to mount.
              type: boolean
            podInfoOnMountVersion:
              description: Indicates this CSI volume driver requires additional pod
                information (like podName, podUID, etc.) during mount operations.
              type: string
  version: v1alpha1
```   

## Service、BLB 类

### 访问Load Balancer Service不通
**现象1**    
访问LoadBalancer Service不通，但是通过LoadBalancer NodePort可以访问通   
**排查**    
查看这个Service 的 BLB ID，找BLB的值班同学看看这个BLB是不是在正常运行状态   
**处置**    
请用户恢复BLB或是重建该Service   

**现象2**    
创建用户自定义声明EIP的LoadBalancer Service但是一直Pending   
**排查**    
检查创建Service的配置文件有无错误。
例如一些Annotation是否写在了正确的部署文件中。  
**处置**    
告诉用户正确的格式，以及如何修改部署文件   

**现象3**    
查看LoadBalancer 的Event报错  
```
Error syncing load balancer: failed to ensure load balancer: Error Message: "No authorization for this operation!", Error Code: "InternalUserDeniedException", Status Code: 403
```  
**排查**    
检查用户是否是内部用户。
内部用户使用BLB暴露机器的端口是受到一定限制的
详情了解[nodeport默认范围(30000-32767)导致BLB绑定EIP失败](http://wiki.baidu.com/pages/viewpage.action?pageId=1062727900)   
**处置**    
修改NodePort端口范围。
修改集群各个Master机器的Api-Server的启动参数，将NodePort端口范围改成8000-9000，并重启。
注意，每台机器都要修改。


### Load Balancer Service 一直没有 Ready
**现象1**
Service 的 Event 事件中出现如下报错
```
Error syncing load balancer: failed to ensure load balancer: create BLB for service osc/crts-tagger-svc-v2-1 failed:  Can't get VPC info for BLB: no suitable subnet found for BLB, maybe you should use this annotation service.beta.kubernetes.io/cce-load-balancer-subnet-id   
```    
**排查**      
检查 CCM 代码报错位置以及 CCM 运行日志       
**原理**      
集群 cce-3wkxwr8q 是完全建在 VPC 的 BCC_NAT 类型的子网里      
LB Service 的实现基于 BLB      
CCE 策略是, 默认随机选择一个节点所在的子网创建 BLB，但不允许在 BCC_NAT 类型子网里创建 BLB     
以上原因导致Service没有Ready    
**处置**     
在创建 Service 的时候使用 service.beta.kubernetes.io/cce-load-balancer-subnet-id: "sbn-123456" Annotation 来指定一个普通BCC子网    
指定 VPC 中其它 BCC 子网