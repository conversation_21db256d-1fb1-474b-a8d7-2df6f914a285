# BCI/Serverless集群问题排查

[TOC]

## 概念和整体架构

### 基本概念

什么是virtual-kubelet (vk)？vk可以理解为将任意的容器运行时接入k8s的中间层，通过创建一个virtual node虚拟节点的方式，将调度到virtual node上的pod以任意形式运行。在我们的场景下，vk将调度到对应 virtual node 上的pod以bci的形式启动。基本上vk的主要操作就是不断调用bci console提供的各种api，保证k8s中pod和bci中pod保持一致。这里并不要求vk和vk管理的pod在网络上是联调的，因为bci console本身是一个地域级别的服务，对于vk来说只要能正常调用bci console的api，就能正常管理pod。

一个集群中可以存在一个或多个virtual node，每个vk汇报的node名称由env指定。

![-w586](./pics/16239441094610.png)

什么是BCI？容器实例，整体的生命周期和bcc比较类似，但是它接受一个pod spec作为创建请求，启动后会运行pod spec中指定的容器，本身的状态也是和k8s pod对齐的。

![-w414](./pics/16240059915990.jpg)


bci在技术架构上和bcc类似，从上到下是console+qin/han+nova最后到单机组件，bci和bcc的区别主要在单机组件上。单机组件上，bcc是由nova compute直接去建虚机；而在bci场景下，我们在中间插入了一个组件containermanager来转接请求。这个组件在功能上比较类似于kubelet，由我们这边维护，watch的对象从kube-apiserver换成了nova compute，拿到本机上预期的pod列表，然后通过cri调用containerd去完成pod操作和状态计算，保持最终一致。containermanager中计算的很多字段和状态只对于k8s集群才有意义，因此containermanager和vk之间通过一些string字段通过json序列化/反序列化的方式进行复杂信息的交互，链路上的组件不感知也不需要理解这些信息，这样可以最大程度减少对链路上的组件依赖。

### BCI Pod状态流转

一个bci pod正常的状态流转大概是:

1. Pending
2. Creating, pod创建请求已经发出，轮询bci状态，等待pod状态变为Running
3. Running, pod运行中
4. Succeeded/Failed (optional)
5. Terminating, 发起删除后，尝试删除对应的bci pod。

正常来说，bci pod能够在30s-1m之间Running，影响时间的因素比较多，上述时间是在需要拉取的镜像较小情况下的经验值。

![](./pics/16239457364525.jpg)



![](./pics/16240113440070.jpg)


基本上bci pod在kubectl显示的状态已经和原生k8s对齐了，如ImagePullBackoff/CrashLoopBackoff这些都可能会出现。


### Serverless集群部署

serverless集群的模式，就是集群中没有物理节点，只有master组件和虚拟节点，默认只有一个虚拟节点。

serverless集群master和vk的的运行方式：容器化master + master pod以bci形式运行，与用户serverless集群内架构基本一致。

serverless集群master pod内包含的组件

- kube-apiserver
- kube-controller-manager
- kube-scheduler
- virtual-kubelet
- service-controller(optional)

service-controller是基于lb-controller，为serverless集群专门开发的组件，这个组件的功能是通过blb来实现ClusterIP Service，在serverless集群这种没有统一kube-proxy的环境下有非常重要的应用场景，可以参考用户文档 <https://cloud.baidu.com/doc/CCE/s/Qkhfxcry7>。

在meta集群中，serverless集群的部署方式如下

![-w507](./pics/16239436066030.jpg)

serverless集群的master pod在meta集群中，以独立pod的形式运行在bci namespace下，每个pod对应一个master instance，当前master是两副本pod运行。没有deployment来管理这些pod，因为master重启ip会变，当前还没有实现master blb根据pod ip自动更改backend，因此重新拉起也没有意义。这个是一个稳定性优化项todo。

![](./pics/16239438992370.jpg)

要进入serverless集群master pod中，直接kubectl exec即可。一个master pod中包含5个容器，name如下

- apiserver
- controller-manager
- scheduler
- virtual-kubelet
- service-controller(optional)
- debug

前面5个容器就是集群功能性组件，debug容器是为了在功能性组件有异常，导致容器无法正常启动时，仍有办法进入pod所在环境排查日志和环境的兜底容器。

一般推荐进入scheduler容器排查，这个容器对于serverless集群来说负载比较低，可以比较安全地进行grep日志这种比较耗cpu的操作，不行的话就进debug，但是debug给的cpu request比较少，grep性能不行，bci exec的session保持时间较短，可能grep到一半就退出了。

exec到容器里，就是一个用户集群的kubectl环境了，每个容器内都有kubectl。

![](./pics/16239447803118.jpg)


## 排查手段

### 查看pod annotation

vk给bci console发创建请求，如果创建请求返回200，则bci console会连带返回order id和pod短id，这两个信息会被写回到对应的bci pod的annotation中。如果请求不成功，则这两个信息不会被写入，且在一定时间内不断重试，但凡创建请求成功过，就会将这两个信息写入annotation。这两个id在iaas侧有问题是非常重要的信息。

annotation中也会写当前这个pod的vpc/可用区/子网/安全组的信息。在bci的情况下，每个pod都可以绑定安全组，在iaas层面，bci pod其实和bcc比较类似，只是里面运行的内容和可能出现的状态不同罢了。

![](./pics/16239419054038.jpg)


### 查看vk日志

对于nodeName相同的vk，多副本的情况下是选主的机制，serverless里是两副本，meta集群中是daemonset，所以先tail下每个vk的日志，找到哪个vk是主，然后到主对应的中控机上/master pod里查看完整日志。

meta集群找主的命令，基本逻辑就是tail日志，主vk的日志和其他vk不同

```
kubectl get po -n cce-system | grep cce-virtual-kubelet | awk '{print $1}' | while read po; do echo $po; kubectl -n cce-system logs $po --tail 1; done
```

![](./pics/16239420905769.jpg)

kubectl logs输出的日志比较有限，到pod对应的机器上看日志比较方便。日志路径在对应机器的 /home/<USER>/cce/cce-virtual-kubelet/log/

![](./pics/16239429923529.jpg)


如果是用户serverless集群中的vk，需要exec到对应的master pod中去看日志，推荐到scheduler容器中去看日志，对于serverless集群来说scheduler负载比较低，如果有grep日志等费cpu的操作对集群影响较小。serverless master pod中vk的日志在 /etc/kubernetes/logs/virtual-kubelet.log*

![](./pics/16239428435489.jpg)

vk的日志由vk pod中的logrotate负责切割，现在的策略都是固定大小切割，保证日志磁盘用量可控，根据最后修改时间找合适的日志文件进去看就行。

容器内日志时间都是UTC。


### 查看serverless集群master日志

和查看serverless集群中vk日志类似，exec到master pod中，日志目录在 /etc/kubernetes/logs ，同样推荐到scheduler容器中查看日志。容器内日志时间都是UTC。

![](./pics/16239431230004.jpg)


### 查看containermanager日志

vk本身只是将底层containermanager上报的信息转换成k8s标准结构，操作本身和状态的计算还是由containermanager完成的。因此如果真的出现了不符合预期的操作或者状态，最终还是需要到对应cn上的containermanager去定位问题。containermanager日志在cn机器上的路径为 /home/<USER>/containermanager/log，日志的内容和kubelet比较类似。对于特定的Pod，可以通过`grep <pod-uuid> containermanager_stderr.log | grep Event` 来确认一下pod的event。不过需要强调，cn上的操作需要非常慎重，因为cn其实就是BCC/BCI所在的物理机，上面可能同时有多个用户的BCC和BCI存在，任何危险操作都可能会导致线上有损。

cn本身只有单机同学和op有对应权限，登录排查的话需要申请权限。

日志内容示例
![](./pics/16240063252444.jpg)

grep Event的内容示例
![35b7cd9b76cc3c450f98c954bcdca496](./pics/35b7cd9b76cc3c450f98c954bcdca496.png)


## 案例分析

### bci pod异常

常规的异常和普通cce集群的pod类似，按照类似思路处理即可，需要注意的是pod调度到vk节点之后的状态实际是containermanager执行并且汇报给vk的，视情况可能要去对应cn上的containermanager排查。

下面是几种BCI Pod特有的异常的异常情况。

#### `get pod`显示为`ProviderFailed`，`describe pod`显示有warning event

![](./pics/16239405385593.jpg)

![](./pics/16239403248777.jpg)

这种情况一般就是创建bci的参数有问题导致的，比如上图就是因为填写的可用区资源售罄了。master的可用区是从用户选中的BCI可用区中根据一定的策略挑选的，一般来说通过前端页面创建不太会可用区售罄有这个问题，这个用户可能是通过api传参创建的，所以可用区选择有问题。

对于ProviderFailed的pod，event中一般会打印请求bci console出错的resp和request id，如果不确定语义，可以找启龙或者我确认下。

这种情况下，当前vk的策略是在30m内不断backoff重试，如果超过30m，pod会被置为Failed phase。
  
#### `get pod`显示为一直卡在Creating（>5m），`describe pod`有成功创建的pod的event，没有其他warning event

![](./pics/16239407432653.jpg)

![](./pics/16239407137273.jpg)

超过5m仍然在Creating这种，可能的情况有

- pod创建异常，如image没拉下来，启动Pod有异常
- 状态汇报链路异常，一般是BCI console有异常
- 订单失败

排查的思路上，首先先确认订单是否失败，因为订单失败是不会无法自行恢复的，对于线上出现的问题可能需要手动介入。可以通过 `get pod -o yaml | grep order-id` 查询创建的订单。拿到order id后，可以到amis上看是否是订单失败了，直接在下面这俩地方输入订单号查询即可，和bcc订单查询的方式基本一致。

失败订单查询: <http://amis.baidu.com/group/bcets/bccbackinfo/failedorder> 这里能查到的话就说明订单已经失败了

订单详情查询: <http://amis.baidu.com/group/bcets/bccbackinfo/bcctransactioninfo_normal> 如果失败订单没查到，可以到这里查看订单当前的状态

![](./pics/16239408587785.jpg)
  
![eec8ca0b51be63e767be8f2eeef3825e](./pics/eec8ca0b51be63e767be8f2eeef3825e.jpg)

订单失败的场景，当前vk没有重试。这种场景需要监控+人工兜底来重建。

如果确认订单并未失败，则开始从状态汇报链路自上往下排查，先看vk是否正确拉取了bci console的状态，vk对于接收到的每个pod的每次状态变化，打印的日志是 `podCache receives update`，对于pod的删除，打印的日志关键字为 `pods removed from podCache`，可以通过`grep 关键字 | grep podName` 来查看vk查询到的某个pod的状态变化。

![](./pics/16242590062878.jpg)

这里有一点需要注意的是，这里展示的status是bci console汇报的状态，这个状态Running仅代表订单成功，实际上k8s中pod phase的计算还依赖每个容器的状态，因此这个status=Running仅为phase=Running的必要非充分条件，phase=Running还需要在console从后端同步containermanager上报为Running状态才行。

如果vk上看没啥异常，就是pod一直没拿到状态，则通过日志中的长短id，找启龙排查下console侧的状态。

如果console侧也无异常，则需要定位到pod在哪个cn上，到cn的containermanager上排查，containermanager执行逻辑上与kubelet类似，可以用类似方式排查。

根据短id定位cn机器的方法

1. <http://amis.baidu.com/group/bcets/consolelogical/logicalquery> 选BCI，用短id查到长id（`pod_uuid`），如果vk日志里打印了uuid，或者已经从console取到了uuid，这步可以略过。
2. <http://amis.baidu.com/group/bcets/bccbackinfo/bccinfofromnovamaster_normal> 到这里通过长id可以查询到bci所在cn

#### `get pod`显示状态为`NotFound`，`describe pod`无event

一般这种情况就是创建的时间比较长了，现有的策略下，超过30m pod仍未创建成功则pod变为NotFound状态，60m之前的event不再保留。这种场景下，如果`get pod -o yaml | grep order-id`能找到对应的订单号，则说明创建请求是发出去了，但是订单没成功，根据订单号定位问题。如果grep不到订单号，说明创建请求就没被accept，根据pod的名称和vk的日志来定位问题。

![](./pics/16239416596697.jpg)

上图这种grep不到订单号的情况，到vk的日志去grep一下pod名称，一般可以看到发请求具体的失败信息。


比如针对上面这个pod，就可以找到对应的日志

![](./pics/16239425152106.jpg)


serverless集群找vk的日志，需要exec到master中查看。

### 集群创建失败

1. 查看失败集群
2. 查看部署失败原因，如果是非instance原因导致的，按照普通集群流程排查即可
3. 如果是master instance部署失败导致，在meta集群中 `kubectl -n bci get po | grep <instance id>` 定位对应的master pod，排查失败原因。由于当前master pod本身也是通过vk+bci的模式管理的，vk代码也是同一套，因此参考上面的bci pod失败排查方式即可。

![](./pics/16239401543952.jpg)

![](./pics/16239402135976.jpg)

### 火花思维场景

火花思维主要是在线课程的场景，主要的任务分两种：课程录制和课程转码。课程录制对时效性的稳定性要求较高，因为课程本身是固定时间开始，固定时间结束的，如果Pod启动过久或者启动失败就会导致丢数据。课程录制一般是1c4g的单容器pod，运行时间在几十分钟到2-3h不等。转码容器一般是4c8g，运行时间在几分钟到几十分钟不等。

在这种场景下，我们给火花的集群加了单独的监控，检查pod异常/pod创建时间过长的情况，以便及时跟进处理线上的异常。

火花共有两个集群：qa集群跑一些测试/回归任务，online集群跑线上的课程录制/转码任务。online集群的pod在namespace `online`下，qa集群的pod在namespace `qa`下。qa集群一般不超过50个pod，online集群当前在晚上19:00-20:00可能会超过1k pod，周末上午pod的量也可能超过1k，而且有不断增长的趋势，最终的口径是高峰期需要2w核资源。下面是最近的趋势图。

![](./pics/16239469100753.jpg)

火花在业务侧有自己的服务发现逻辑，即pod启动之后会向他们自己部署的注册中心注册，因此逻辑上不太依赖k8s中汇报的pod状态，而是比较在意后端pod是否真正运行起来了。因此如果是状态同步导致的问题通常他们无感，需要处理的问题主要是订单或者Pod启动有问题。当然，状态同步问题也需要排查，只是相对没有这么紧急而已。

同时，由于火花线上的pod数量比较多，单个vk node承载的pod数量是有限的，因此给火花的online集群新增了一个vk node，在online集群排查vk日志的时候需要先判断pod在哪个vk node上，然后到对应的vk pod中去找日志。

![](./pics/16242636658965.jpg)

#### 报警配置

主要依赖群内机器人报警，报警信息显示的是出现异常的集群，以及其中异常pod在kubectl下输出的结果。

火花的集群不可通过eip访问master，需要通过B区的kubeconfig或者exec到master中使用kubectl。

![](./pics/16242573432547.jpg)

![](./pics/16242573942557.jpg)


触发报警的case

1. CreatingTooLong: pod卡在Creating状态超过4m会报警，这个是根据他们的镜像大小和启动时间计算的一个阈值，基本上超过这个时间就可能有异常。排查思路还是先根据annotation中的order-id看看订单是否正常，如果订单是SUCC一般没啥事，订单失败的话需要跟进，先尝试下能否通过下面章节会说的的重下订单策略兜底成功，优先止损，之后排查原因。有一种偶发情况是bci console侧同步后端的状态卡了，要5m左右才会同步到后端最新的状态。超过5m基本就是订单或者iaas组件逻辑有问题了，没法确认原因的话，找子超or启龙看下。

2. Fail: 包含下图中的几种状态，这些其实都是标准的k8s错误，只是操作其实是由containermanager完成，并且把对应状态汇报上来展示的，而不是由vk直接进行操作。镜像相关的失败一般靠后端重试能解决，如果一段时间都没恢复（>5m），可能是ccr有问题，也需要介入排查。容器重启/Error一般是不符合预期的，可能是oom，或者环境问题导致，可能从logs先判断下。如果从logs看不出来具体原因，可能需要登录到cn上定位。
  ![](./pics/16239471813555.jpg)

3. ProviderFailed：下发创建请求就失败了，这个在30m内有不断backoff重试，但仍需要从event看下具体出错的原因，如果是有故障需要及时排除，避免重试一直无法成功，或者影响后续pod创建。


火花集群内出现异常，属于线上失败，如果无法在用户无感的状态下恢复，和线上故障的处理思路一致：

<font color=red>**一通告，二止损，三排查**</font>

#### 订单失败的处理

对于订单失败的情况下，和bcc订单失败的情况类似，创建后查询bci状态一直为Pending，然后突然在某一时刻消失。订单失败在报警中的表现模式是pod一直卡在Creating，直到30m后（最大创建重试容忍）不再重试，phase进入Failed。

因此如果出现CreatingTooLong的情况，先拿到订单号，到amis上查一下对应订单是否已经失败。如果订单失败，在30m内重新下一个相同名称和spec的订单并且执行成功的话，用户是无感的，vk也会转而同步新订单对应的pod状态。

这里为了快速止损，针对火花的集群写了一个快捷工具[`rebuild-huohua-failed-order`](https://bos-csi-test-bj-jpaas.bj.bcebos.com/rebuild-huohua-failed-order.linux-amd64)，用来快速查询特定pod的订单号，和为特定的pod重下订单，集群信息和namespace已经写死，在B区任意有kubectl的机器上执行即可。

![](./pics/16242598977962.jpg)


查询火花online集群名为`3cee2fffa4a545e99a6b096a1a275573`的pod的订单号（这个名称直接用报警信息里的名称即可）


```
./rebuild-huohua-failed-order -env online -name 3cee2fffa4a545e99a6b096a1a275573
```

![](./pics/16242601368699.jpg)


通过订单号确认对应订单已经失败，且pod创建不满30m，则手动执行订单重下，执行过程会打印具体进度，和发送的创建请求。重下的方式是在上述查询的命令后加上 `-action replay`。


```
./rebuild-huohua-failed-order -env online -name 3cee2fffa4a545e99a6b096a1a275573 -action replay
```

![](./pics/16242602207244.jpg)

执行replay后，如果请求发送成功，会打印bci console返回的订单号和短id，拿这个订单号去amis上查询重下的订单是否成功，如果重下的订单依然失败，可能需要重下多次。

这个工具也有通用的版本[rebuild-failed-order](https://bos-csi-test-bj-jpaas.bj.bcebos.com/rebuild-failed-order)，不过依赖meta集群的kubeconfig，需要在对应地域的中控机上执行，参数上把env换成了集群id+namespace来定位pod。

![](./pics/16242622029100.jpg)

