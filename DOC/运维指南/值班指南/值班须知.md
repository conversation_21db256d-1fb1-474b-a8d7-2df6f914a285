# 值班须知

## 历史记录查阅
[可用性与运维](http://wiki.baidu.com/pages/viewpage.action?pageId=768367449)

## 注意事项
所有写操作**必须**经过客户确认并同意，包括但不限于：    
	+  **绝对不能**用 kubeconfig 直接做写操作(create、update、delete）；    
	+  **绝对不能**直接登录用户虚机；    
	+ **绝对不能**直接在用户虚机做写操作；    
	    
报警处理，涉及数据清理等，如果没做过，新同学请一定**要 mentor 确认**（凌晨也务必打电话确认）.    

## 百度通告平台的使用
**报警处理**    
1.在通告平台中看到**{故障进入}**开头的报警，点开链接看详情    
2.如果上述报警你没看到，过一会儿会给你打电话，再去通告平台会看到**{未处理超时通告1}**，点开链接看详情

**回复2**    
在通告平台中接到『故障进入』后多回复几个2，然后再去处理
如果有遗漏的报警，回复2后，过段时间问题如果没有解决报警还会再响，不用担心遗漏

**回复1**    
不建议回复1，回复1之后要在报警页面手动标注解决，否则容易出现过了一段时间再次报警的情况产生，尽管此时报警已经被解决，但还是会被电话打。