# gzns-bce-ccs-control00.gzns Docker Hang

## 问题描述

gzns-bce-ccs-control00.gzns Node NotReady, 原因是 PLEG 超时, <PERSON>er Hang 住:

```bash
$docker ps

```

## 问题分析

通过查看 kubelet 日志发现 docker hang 的时间点: 2020-10-16 21:28:11

```bash
20201016/kubelet_stderr.log.2100:35005:I1016 21:28:11.057674   14383 setters.go:520] Node became not ready: {Type:Ready Status:False LastHeartbeatTime:2020-10-16 21:28:11.057446845 +0800 CST m=+5641225.984914704 LastTransitionTime:2020-10-16 21:28:11.057446845 +0800 CST m=+5641225.984914704 Reason:KubeletNotReady Message:PLEG is not healthy: pleg was last seen active 3m5.764045783s ago; threshold is 3m0s}

20201016/kubelet_stderr.log.2100:40370:I1016 21:37:07.167843   14383 kubelet.go:1848] skipping pod synchronization - [PLEG is not healthy: pleg was last seen active 12m1.87442921s ago; threshold is 3m0s]
20201016/kubelet_stderr.log.2100:40375:I1016 21:37:07.267957   14383 kubelet.go:1848] skipping pod synchronization - [PLEG is not healthy: pleg was last seen active 12m1.974519841s ago; threshold is 3m0s]
20201016/kubelet_stderr.log.2100:40378:I1016 21:37:07.468054   14383 kubelet.go:1848] skipping pod synchronization - [PLEG is not healthy: pleg was last seen active 12m2.174615203s ago; threshold is 3m0s]
20201016/kubelet_stderr.log.2100:40381:I1016 21:37:07.868203   14383 kubelet.go:1848] skipping pod synchronization - [PLEG is not healthy: pleg was last seen active 12m2.574766676s ago; threshold is 3m0s]
20201016/kubelet_stderr.log.2100:40388:I1016 21:37:08.668361   14383 kubelet.go:1848] skipping pod synchronization - [PLEG is not healthy: pleg was last seen active 12m3.374923507s ago; threshold is 3m0s]
20201016/kubelet_stderr.log.2100:40400:I1016 21:37:10.268473   14383 kubelet.go:1848] skipping pod synchronization - [PLEG is not healthy: pleg was last seen active 12m4.97503624s ago; threshold is 3m0s]
```

Docker 本身无有效日志, Fluentd 的 CPU 使用率较高

```bash
PID USER      PR  NI  VIRT  RES  SHR S %CPU %MEM    TIME+  COMMAND
1094 work      20   0 9570m 6.7g 3772 S 100.4  5.3  39556:20 ruby
```

/var/log/messages 有内存碎片导致分配失败:

```bash
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.028997] docker-runc: page allocation failure: order:6, mode:0x10c0d0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029003] CPU: 11 PID: 7563 Comm: docker-runc Tainted: G            E  ------------ T 3.10.0_3-0-0-34 #1
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029005] Hardware name: Inspur BJINSPURV2G4Y22-20A/SN6110M4S, BIOS 4.1.8 08/25/2016
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029006] Call Trace:
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029016]  [<ffffffff8171f279>] dump_stack+0x19/0x1b
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029024]  [<ffffffff8113e5d0>] warn_alloc_failed+0xf0/0x160
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029030]  [<ffffffff8171bffd>] ? __alloc_pages_direct_compact+0x185/0x196
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029033]  [<ffffffff8114275c>] __alloc_pages_nodemask+0x86c/0xb10
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029040]  [<ffffffff81180799>] alloc_pages_current+0xa9/0x170
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029043]  [<ffffffff8113d80e>] __get_free_pages+0xe/0x50
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029047]  [<ffffffff8118848e>] kmalloc_order_trace+0x2e/0xa0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029051]  [<ffffffff8119808a>] ? memcg_update_cache_size+0xaa/0xc0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029055]  [<ffffffff8118a849>] __kmalloc+0x219/0x230
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029057]  [<ffffffff8119803d>] memcg_update_cache_size+0x5d/0xc0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029062]  [<ffffffff8115c304>] memcg_update_all_caches+0x54/0x90
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029064]  [<ffffffff811977d0>] memcg_update_cache_sizes+0x40/0xc0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029067]  [<ffffffff81197f47>] mem_cgroup_css_online+0x197/0x1f0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029072]  [<ffffffff810caacf>] online_css.isra.10+0x1f/0x50
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029075]  [<ffffffff810ce962>] cgroup_mkdir+0x582/0x690
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029079]  [<ffffffff811ad197>] vfs_mkdir+0xb7/0x160
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029083]  [<ffffffff811b2eea>] SyS_mkdirat+0xaa/0xe0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029089]  [<ffffffff8172ea09>] system_call_fastpath+0x16/0x1b
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029090] Mem-Info:
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029093] active_anon:5433853 inactive_anon:86579 isolated_anon:0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029093]  active_file:12355536 inactive_file:12116983 isolated_file:0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029093]  unevictable:0 dirty:2681 writeback:0 unstable:0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029093]  free:743826 slab_reclaimable:1461691 slab_unreclaimable:326699
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029093]  mapped:128129 shmem:198618 pagetables:25718 bounce:0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029093]  free_cma:0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029097] Node 0 DMA free:14812kB min:4kB low:16kB high:28kB active_anon:0kB inactive_anon:0kB active_file:0kB inactive_file:0kB unevictable:0kB isolated(anon):0kB isolated(file):0kB present:15972kB managed:15888kB mlocked:0kB dirty:0kB writeback:0kB mapped:0kB shmem:0kB slab_reclaimable:0kB slab_unreclaimable:0kB kernel_stack:0kB pagetables:0kB unstable:0kB bounce:0kB free_cma:0kB writeback_tmp:0kB pages_scanned:0 all_unreclaimable? yes
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029103] lowmem_reserve[]: 0 1576 128547 128547
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029105] Node 0 DMA32 free:522120kB min:560kB low:2172kB high:3784kB active_anon:226496kB inactive_anon:644kB active_file:172276kB inactive_file:156720kB unevictable:0kB isolated(anon):0kB isolated(file):0kB present:1957688kB managed:1615456kB mlocked:0kB dirty:68kB writeback:0kB mapped:3912kB shmem:3156kB slab_reclaimable:369516kB slab_unreclaimable:106160kB kernel_stack:2400kB pagetables:1180kB unstable:0kB bounce:0kB free_cma:0kB writeback_tmp:0kB pages_scanned:0 all_unreclaimable? no
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029110] lowmem_reserve[]: 0 0 126971 126971
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029112] Node 0 Normal free:2438372kB min:45320kB low:175336kB high:305352kB active_anon:21508916kB inactive_anon:345672kB active_file:49249868kB inactive_file:48311212kB unevictable:0kB isolated(anon):0kB isolated(file):0kB present:132120576kB managed:130018616kB mlocked:0kB dirty:10656kB writeback:0kB mapped:508604kB shmem:791316kB slab_reclaimable:5477248kB slab_unreclaimable:1200636kB kernel_stack:79168kB pagetables:101692kB unstable:0kB bounce:0kB free_cma:0kB writeback_tmp:0kB pages_scanned:0 all_unreclaimable? no
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029117] lowmem_reserve[]: 0 0 0 0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029119] Node 0 DMA: 1*4kB (U) 1*8kB (U) 1*16kB (U) 0*32kB 1*64kB (U) 1*128kB (U) 1*256kB (U) 0*512kB 0*1024kB 1*2048kB (R) 3*4096kB (M) = 14812kB
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029128] Node 0 DMA32: 8416*4kB (UEM) 12434*8kB (UEM) 5930*16kB (UEM) 4767*32kB (UEM) 1833*64kB (UEM) 227*128kB (UEM) 0*256kB 0*512kB 0*1024kB 0*2048kB 0*4096kB = 526928kB
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029136] Node 0 Normal: 296727*4kB (UEM) 154567*8kB (UEMR) 1254*16kB (UMR) 1*32kB (U) 1*64kB (U) 5*128kB (U) 0*256kB 0*512kB 0*1024kB 0*2048kB 0*4096kB = 2444244kB
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029145] Node 0 hugepages_total=0 hugepages_free=0 hugepages_surp=0 hugepages_size=2048kB
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029146] 24671139 total pagecache pages
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029148] 0 pages in swap cache
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029149] Swap cache stats: add 0, delete 0, find 0/0
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029150] Free swap  = 0kB
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029151] Total swap = 0kB
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029152] 33523559 pages RAM
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029153] 0 pages HighMem/MovableOnly
Oct 16 21:25:05 gzns-bce-ccs-control00 kernel: [6412798.029154] 611069 pages reserved
```

这个无法分配内存碎片, 应该和 docker hang 有关联, 不然如此巧合.

docker-runc 启动的 Pod 应该是 cluster-status-alert-1602854700-n5jk, 对应 containerID 为:  6a6a2542389bec2065baea09b1025f1f1b8cfb42f097680bf203847d22d41237

```
cat kubelet_stderr.log.2100| grep cluster-status-alert-1602854700-n5jk
I1016 21:25:02.771247   14383 config.go:414] Receiving a new pod "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)"
I1016 21:25:02.772590   14383 kubelet.go:1910] SyncLoop (ADD, "api"): "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)"
I1016 21:25:02.773042   14383 kubelet_pods.go:1317] Generating status for "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)"
I1016 21:25:02.775484   14383 round_trippers.go:438] GET https://**************:6443/api/v1/namespaces/cce-system/pods/cluster-status-alert-1602854700-n5jks 200 OK in 2 milliseconds
......
I1016 21:25:05.307809   14383 kuberuntime_manager.go:608] Stopping PodSandbox for "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)", will start new one
I1016 21:25:05.307873   14383 event.go:221] Event(v1.ObjectReference{Kind:"Pod", Namespace:"cce-system", Name:"cluster-status-alert-1602854700-n5jks", UID:"ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a", APIVersion:"v1", ResourceVersion:"41242253", FieldPath:""}): type: 'Normal' reason: 'SandboxChanged' Pod sandbox changed, i
t will be killed and re-created.
I1016 21:25:05.308908   14383 kuberuntime_manager.go:660] Creating sandbox for pod "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)"
E1016 21:27:05.315862   14383 remote_runtime.go:96] RunPodSandbox from runtime service failed: rpc error: code = Unknown desc = failed to start sandbox container for pod "cluster-status-alert-1602854700-n5jks": operation timeout: context deadline exceeded
E1016 21:27:05.315912   14383 kuberuntime_sandbox.go:68] CreatePodSandbox for pod "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)" failed: rpc error: code = Unknown desc = failed to start sandbox container for pod "cluster-status-alert-1602854700-n5jks": operation timeout: context deadline exceeded
E1016 21:27:05.315943   14383 kuberuntime_manager.go:666] createPodSandbox for pod "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)" failed: rpc error: code = Unknown desc = failed to start sandbox container for pod "cluster-status-alert-1602854700-n5jks": operation timeout: context deadline exceeded
E1016 21:27:05.316006   14383 pod_workers.go:190] Error syncing pod ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a ("cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)"), skipping: failed to "CreatePodSandbox" for "cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)" with CreatePodSandboxError: "CreatePodSandbox for pod \"cluster-status-alert-1602854700-n5jks_cce-system(ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a)\" failed: rpc error: code = Unknown desc = failed to start sandbox container for pod \"cluster-status-alert-1602854700-n5jks\": operation timeout: context deadline exceeded"
I1016 21:27:05.316053   14383 event.go:221] Event(v1.ObjectReference{Kind:"Pod", Namespace:"cce-system", Name:"cluster-status-alert-1602854700-n5jks", UID:"ff59cd5b-0fb2-11eb-9a8a-6c92bf86651a", APIVersion:"v1", ResourceVersion:"41242253", FieldPath:""}): type: 'Warning' reason: 'FailedCreatePodSandBox' Failed create pod sandbox: rpc error: code = Unknown desc = failed to start sandbox container for pod "cluster-status-alert-1602854700-n5jks": operation timeout: context deadline exceeded
```

可以看到 21:25:05 时刻, 调用 Docker 创建 Pod, 随后 messages 日志出现: docker-runc: page allocation failure: order:6, mode:0x10c0d0


只有 docker ps 不行, 但是 inspect 是可以的, 得细看 docker ps 做什么事情了, 其实是 dockerd 存在问题

```bash
curl 127.0.0.1:2375/containers/json?all=1

curl 127.0.0.1:2375/containers/c81211f8e3be1f7109c9abf55e4ba144bceeb009ea490433523cbd58788ed73d/json
```

目前已知有内核问题和 dockerd 版本问题, 考虑先升级到最新 3 系内核, Docker 19 版本后再来处理该问题, 留作记录

## 参考

[container oom would cause docker ps hang]https://github.com/coreos/bugs/issues/2320