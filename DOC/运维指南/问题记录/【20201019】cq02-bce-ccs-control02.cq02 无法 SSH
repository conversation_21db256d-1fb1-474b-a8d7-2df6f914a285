# cq02-bce-ccs-control02.cq02 无法 SSH

## 问题描述

2020-10-19 发现北京 MetaCluster cq02-bce-ccs-control02.cq02.baidu.com SSH 无法登录, 通过 RMS 发单重启恢复服务.

## 问题排查

cq02-bce-ccs-control02.cq02 异常时间点为: 2020-10-18 18:21:53

```bash
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285886] Call Trace:
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285889]  [<ffffffff817250d9>] schedule_preempt_disabled+0x29/0x80
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285891]  [<ffffffff81722f1b>] __mutex_lock_slowpath+0xbb/0x1a0
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285893]  [<ffffffff8172245f>] mutex_lock+0x1f/0x2f
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285895]  [<ffffffff81147d92>] lru_add_drain_all+0x32/0x190
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285898]  [<ffffffff8113d20b>] SyS_fadvise64_64+0x16b/0x240
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285900]  [<ffffffff8113d2ee>] SyS_fadvise64+0xe/0x10
Oct 18 18:21:53 cq02-bce-ccs-control02 kernel: [5274369.285903]  [<ffffffff8172ea09>] system_call_fastpath+0x16/0x1b

Oct 18 22:48:42 cq02-bce-ccs-control02 kernel: [5290390.673475] EXT4-fs (sdb1): error count since last fsck: 4
Oct 18 22:48:42 cq02-bce-ccs-control02 kernel: [5290390.673481] EXT4-fs (sdb1): initial error at time 1597755608: mb_free_blocks:1448: inode 197664219: block 18310963
Oct 18 22:48:42 cq02-bce-ccs-control02 kernel: [5290390.673486] EXT4-fs (sdb1): last error at time 1597755608: ext4_mb_generate_buddy:757
Oct 19 05:44:20 cq02-bce-ccs-control02 kernel: [5315348.819887] lowest memcgroup 5000
Oct 19 05:44:20 cq02-bce-ccs-control02 kernel: [5315348.819903] process-exporte invoked oom-killer: gfp_mask=0xd0, order=0, oom_score_adj=999
Oct 19 05:44:20 cq02-bce-ccs-control02 kernel: [5315348.819905] process-exporte cpuset=977bd7d9fd0b1c47779aeac8ea746c5e37566090a71bfd68757f5cf6ed6ebe6c mems_allowed=0
Oct 19 05:44:20 cq02-bce-ccs-control02 kernel: [5315348.819907] CPU: 16 PID: 29478 Comm: process-exporte Tainted: G        W   E  ------------ T 3.10.0_3-0-0-34 #1
Oct 19 05:44:20 cq02-bce-ccs-control02 kernel: [5315348.819909] Hardware name: Inspur SA5112M4/YZMB-00370-108, BIOS 4.0.6 08/10/2015
Oct 19 05:44:20 cq02-bce-ccs-control02 kernel: [5315348.819910] Call Trace:
```

查看 /var/log/messages 发现大量 OOM 日志, 初步怀疑和 OOM 有关, 导致 sshd 进程被杀掉:

```bash
# cat messages-20201018 |grep "Memory cgroup out of memory"
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420634] Memory cgroup out of memory: Kill process 7561 (coredns) score 1072 or sacrifice child
Oct 17 14:56:57 cq02-bce-ccs-control02 kernel: [5175594.680036] Memory cgroup out of memory: Kill process 23853 (coredns) score 1072 or sacrifice child
Oct 17 15:02:32 cq02-bce-ccs-control02 kernel: [5175929.973758] Memory cgroup out of memory: Kill process 23633 (coredns) score 1071 or sacrifice child
Oct 17 15:08:02 cq02-bce-ccs-control02 kernel: [5176260.253849] Memory cgroup out of memory: Kill process 29634 (coredns) score 1071 or sacrifice child
Oct 17 15:13:37 cq02-bce-ccs-control02 kernel: [5176595.497590] Memory cgroup out of memory: Kill process 2882 (coredns) score 1070 or sacrifice child
Oct 17 15:19:09 cq02-bce-ccs-control02 kernel: [5176927.764035] Memory cgroup out of memory: Kill process 16653 (coredns) score 1069 or sacrifice child
Oct 17 15:24:30 cq02-bce-ccs-control02 kernel: [5177249.000697] Memory cgroup out of memory: Kill process 462 (coredns) score 1069 or sacrifice child
Oct 17 15:29:46 cq02-bce-ccs-control02 kernel: [5177565.253489] Memory cgroup out of memory: Kill process 16746 (coredns) score 1069 or sacrifice child
```

查看日志发现 OOM 开始时间在 Oct 17 14:50:58

```bash
Oct 17 14:43:40 cq02-bce-ccs-control02 kernel: [5174796.287418]   node 0: slabs: 17, objs: 544, free: 0
Oct 17 14:43:57 cq02-bce-ccs-control02 kernel: [5174813.302445] SLUB: Unable to allocate memory on node -1 (gfp=0x20)
Oct 17 14:43:57 cq02-bce-ccs-control02 kernel: [5174813.302449]   cache: nf_conntrack_ffff8816f5e06480(16494:fc90e102ec8908240453267d3086855939afc91f38f21947f1a6976ef34e7858), object size: 312, buffer size: 320, default order: 1, min order: 0
Oct 17 14:43:57 cq02-bce-ccs-control02 kernel: [5174813.302451]   node 0: slabs: 16, objs: 400, free: 0
Oct 17 14:44:07 cq02-bce-ccs-control02 kernel: [5174823.815529] SLUB: Unable to allocate memory on node -1 (gfp=0x20)
Oct 17 14:44:07 cq02-bce-ccs-control02 kernel: [5174823.815533]   cache: kmalloc-1024(16494:fc90e102ec8908240453267d3086855939afc91f38f21947f1a6976ef34e7858), object size: 1024, buffer size: 1024, default order: 3, min order: 0
Oct 17 14:44:07 cq02-bce-ccs-control02 kernel: [5174823.815535]   node 0: slabs: 19, objs: 580, free: 0
Oct 17 14:50:35 cq02-bce-ccs-control02 kernel: [5175212.401921] SLUB: Unable to allocate memory on node -1 (gfp=0x20)
Oct 17 14:50:35 cq02-bce-ccs-control02 kernel: [5175212.401926]   cache: kmalloc-1024(16498:8957fdcb9c43f9721690e6684ed49c13fc848481ff61094c322888818f4d1839), object size: 1024, buffer size: 1024, default order: 3, min order: 0
Oct 17 14:50:35 cq02-bce-ccs-control02 kernel: [5175212.401928]   node 0: slabs: 21, objs: 672, free: 0
Oct 17 14:50:35 cq02-bce-ccs-control02 kernel: [5175212.401945] SLUB: Unable to allocate memory on node -1 (gfp=0x20)
Oct 17 14:50:35 cq02-bce-ccs-control02 kernel: [5175212.401946]   cache: kmalloc-1024(16498:8957fdcb9c43f9721690e6684ed49c13fc848481ff61094c322888818f4d1839), object size: 1024, buffer size: 1024, default order: 3, min order: 0
Oct 17 14:50:35 cq02-bce-ccs-control02 kernel: [5175212.401947]   node 0: slabs: 21, objs: 672, free: 0

Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420110] lowest memcgroup 5000
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420123] oom_kill_process: 24 callbacks suppressed
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420125] coredns invoked oom-killer: gfp_mask=0xd0, order=0, oom_score_adj=999
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420126] coredns cpuset=8957fdcb9c43f9721690e6684ed49c13fc848481ff61094c322888818f4d1839 mems_allowed=0
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420129] CPU: 1 PID: 7561 Comm: coredns Tainted: G        W   E  ------------ T 3.10.0_3-0-0-34 #1
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420131] Hardware name: Inspur SA5112M4/YZMB-00370-108, BIOS 4.0.6 08/10/2015
Oct 17 14:50:58 cq02-bce-ccs-control02 kernel: [5175235.420132] Call Trace:
```

在此之前有大量 SLUB: Unable to allocate memory on node 日志, 该问题之前遇到过, 参考: http://wiki.baidu.com/pages/viewpage.action?pageId=1065555807

## /var/crash 文件内容分析

```
path /var/crash
127.0.0.1-2020-08-18-18:01:08
kernel-3.10.0_3-0-0-34

crash 7.1.6
Copyright (C) 2002-2016  Red Hat, Inc.
Copyright (C) 2004, 2005, 2006, 2010  IBM Corporation
Copyright (C) 1999-2006  Hewlett-Packard Co
Copyright (C) 2005, 2006, 2011, 2012  Fujitsu Limited
Copyright (C) 2006, 2007  VA Linux Systems Japan K.K.
Copyright (C) 2005, 2011  NEC Corporation
Copyright (C) 1999, 2002, 2007  Silicon Graphics, Inc.
Copyright (C) 1999, 2000, 2001, 2002  Mission Critical Linux, Inc.
and you are welcome to change it and/or distribute copies of it under
certain conditions.  Enter "help copying" to see the conditions.
This program has absolutely no warranty.  Enter "help warranty" for details.

GNU gdb (GDB) 7.6
Copyright (C) 2013 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <http://gnu.org/licenses/gpl.html>
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.  Type "show copying"
and "show warranty" for details.
This GDB was configured as "x86_64-unknown-linux-gnu"...

      KERNEL: /var/crash/kernel-3.10.0_3-0-0-34/vmlinux
    DUMPFILE: /var/crash/127.0.0.1-2020-08-18-18:01:08/vmcore  [PARTIAL DUMP]
        CPUS: 24
        DATE: Tue Aug 18 18:01:01 2020
      UPTIME: 1 days, 01:39:36
LOAD AVERAGE: 500.14, 379.20, 212.22
       TASKS: 3229
    NODENAME: cq02-bce-ccs-control02.cq02.baidu.com
     RELEASE: 3.10.0_3-0-0-34
     VERSION: #1 SMP Sun Apr 26 22:58:21 CST 2020
     MACHINE: x86_64  (2394 Mhz)
      MEMORY: 95.9 GB
       PANIC: "Kernel panic - not syncing: softlockup: hung tasks"
         PID: 846
     COMMAND: "umount"
        TASK: ffff8816ab563a20  [THREAD_INFO: ffff8816c8d10000]
         CPU: 1
       STATE: TASK_RUNNING (PANIC)

crash> bt
[92450.864916] Stack:
[92450.864916] Call Trace:
[92450.864920]  [<ffffffff811c0d81>] mntput_no_expire+0x21/0x150
[92450.864923]  [<ffffffff811c0ed4>] mntput+0x24/0x40
[92450.864925]  [<ffffffff811c1007>] namespace_unlock+0x117/0x130
[92450.864928]  [<ffffffff811c20db>] SyS_umount+0x1db/0x3b0
[92450.864931]  [<ffffffff8172ea09>] system_call_fastpath+0x16/0x1b
[92450.864951] Code: 03 04 25 e8 bf 00 00 ba 00 00 02 00 f0 0f c1 10 89 d1 c1 e9 10 66 39 d1 75 02 5d c3 0f b7 10 89 ce 66 39 ca 74 f4 f3 90 0f b7 10 <66> 39 f2 75 f6 5d c3 0f 1f 44 00 00 55 48 89 e5 48 63 f6 48 8b
[92451.120071] BUG: soft lockup - CPU#17 stuck for 134s! [umount:843]
[92451.120094] Modules linked in: nf_conntrack_netlink(E) xt_u32(E) veth(E) ipip(E) tunnel4(E) xt_set(E) iptable_raw(E) xt_multiport(E) ip_set_hash_ip(E) ip_set_hash_net(E) ip_set(E) nfnetlink(E) xt_state(E) xt_nat(E) ip_vs_sh(E) ip_vs_wrr(E) ip_vs_rr(E) ip_vs(E) libcrc32c(E) xt_comment(E) xt_mark(E) ipt_MASQUERADE(E) nf_nat_masquerade_ipv4(E) iptable_filter(E) iptable_nat(E) nf_conntrack_ipv4(E) nf_defrag_ipv4(E) nf_nat_ipv4(E) xt_addrtype(E) xt_conntrack(E) nf_nat(E) nf_conntrack(E) bridge(E) newttm(E) tcp_diag(E) inet_diag(E) iptable_mangle(E) ip_gre(E) ip_tunnel(E) gre(E) ip_tables(E) 8021q(E) mrp(E) garp(E) stp(E) llc(E) ipv6(E) binfmt_misc(E) dm_mirror(E) dm_region_hash(E) dm_log(E) dm_mod(E) pcspkr(E) sb_edac(E) edac_core(E) sg(E) i2c_i801(E) shpchp(E) ioatdma(E) ixgbe(E) vxlan(E) udp_tunnel(E)
[92451.120097]  ip6_udp_tunnel(E) igb(E) dca(E) i2c_algo_bit(E) i2c_core(E) ptp(E) pps_core(E) ipmi_si(E) ipmi_msghandler(E)
[92451.120099] CPU: 17 PID: 843 Comm: umount Tainted: G            EL ------------ T 3.10.0_3-0-0-34 #1
[92451.120100] Hardware name: Inspur SA5112M4/YZMB-00370-108, BIOS 4.0.6 08/10/2015
[92451.120101] task: ffff8816ab565d00 ti: ffff881637404000 task.ti: ffff881637404000
[92451.120106] RIP: 0010:[<ffffffff8108e774>]  [<ffffffff8108e774>] lg_global_lock+0x84/0x90
[92451.120107] RSP: 0018:ffff881637407eb0  EFLAGS: 00000297
[92451.120108] RAX: 000000000000e6c6 RBX: ffff88181ec03700 RCX: ffff88181f20ed30
[92451.120108] RDX: 0000000000000000 RSI: 000000000000e6ca RDI: 000000000000e6c4
[92451.120109] RBP: ffff881637407ec8 R08: ffffffff81b86560 R09: 000000010020001e
[92451.120110] R10: ffffffff811c0539 R11: ffffea000407e780 R12: 0000000000000282
[92451.120111] R13: ffff88140ae8aa80 R14: ffffe8ffcee00cc8 R15: 0000000d00200016
[92451.120113] FS:  00007f038c0b7740(0000) GS:ffff88181f420000(0000) knlGS:0000000000000000
[92451.120114] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033
[92451.120115] CR2: ffffffffff600400 CR3: 0000000883e0a000 CR4: 00000000001407e0
[92451.120116] DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000
[92451.120117] DR3: 0000000000000000 DR6: 00000000ffff0ff0 DR7: 0000000000000400
[92451.120117] Stack:
[92451.120117] Call Trace:
[92451.120121]  [<ffffffff811c0fbf>] namespace_unlock+0xcf/0x130
[92451.120124]  [<ffffffff811c20db>] SyS_umount+0x1db/0x3b0
[92451.120127]  [<ffffffff8172ea09>] system_call_fastpath+0x16/0x1b
[92451.120147] Code: f0 0f c1 19 89 d8 c1 e8 10 66 39 d8 89 c6 75 0e eb b5 0f 1f 44 00 00 5b 41 5c 41 5d 5d c3 0f b7 39 66 39 c7 74 ea f3 90 0f b7 01 <66> 39 f0 75 f6 eb 95 0f 1f 44 00 00 0f 1f 44 00 00 55 39 d6 48
[92451.367610] Hardware name: Inspur SA5112M4/YZMB-00370-108, BIOS 4.0.6 08/10/2015
[92451.375310] Call Trace:
[92451.377914]  <IRQ>  [<ffffffff8171f279>] dump_stack+0x19/0x1b
[92451.383858]  [<ffffffff8171ade5>] panic+0xc8/0x1d7
[92451.388811]  [<ffffffff810ee186>] watchdog_timer_fn+0x1b6/0x1c0
[92451.394889]  [<ffffffff810edfd0>] ? watchdog_enable+0xa0/0xa0
[92451.400795]  [<ffffffff8108a630>] __hrtimer_run_queues+0xc0/0x230
[92451.407049]  [<ffffffff8108ab00>] hrtimer_interrupt+0xb0/0x1d0
[92451.413045]  [<ffffffff81038af7>] local_apic_timer_interrupt+0x37/0x60
[92451.419732]  [<ffffffff81730d7f>] smp_apic_timer_interrupt+0x3f/0x60
[92451.426244]  [<ffffffff8172f65d>] apic_timer_interrupt+0x6d/0x80
[92451.432409]  <EOI>  [<ffffffff811c0539>] ? free_vfsmnt+0x39/0x40
[92451.438615]  [<ffffffff8108e699>] ? lg_PID: 846    TASK: ffff8816ab563a20  CPU: 1   COMMAND: "umount"
 #0 [ffff88181f223d20] machine_kexec at ffffffff810407a8
 #1 [ffff88181f223d70] crash_kexec at ffffffff810c6f23
 #2 [ffff88181f223e38] panic at ffffffff8171adec
 #3 [ffff88181f223eb0] watchdog_timer_fn at ffffffff810ee186
 #4 [ffff88181f223ee8] __hrtimer_run_queues at ffffffff8108a630
 #5 [ffff88181f223f38] hrtimer_interrupt at ffffffff8108ab00
 #6 [ffff88181f223f80] local_apic_timer_interrupt at ffffffff81038af7
 #7 [ffff88181f223f98] smp_apic_timer_interrupt at ffffffff81730d7f
 #8 [ffff88181f223fb0] apic_timer_interrupt at ffffffff8172f65d
--- <IRQ stack> ---
 #9 [ffff8816c8d13de8] apic_timer_interrupt at ffffffff8172f65d
    [exception RIP: lg_local_lock+57]
    RIP: ffffffff8108e699  RSP: ffff8816c8d13e90  RFLAGS: 00000293
    RAX: ffff88181f22ed30  RBX: ffffe8fffd6170e8  RCX: 000000000000d4a6
    RDX: 000000000000d4a4  RSI: 000000000000d4a6  RDI: ffffffff81ae49d0
    RBP: ffff8816c8d13e90   R8: ffffffff81b86560   R9: 000000010020000a
    R10: ffffffff811c0539  R11: ffffea005139f000  R12: ffffffff8115a424
    R13: 000000010020000a  R14: 0000000000000286  R15: ffff8816c8d13e90
    ORIG_RAX: ffffffffffffff10  CS: 0010  SS: 0018
#10 [ffff8816c8d13e98] mntput_no_expire at ffffffff811c0d81
#11 [ffff8816c8d13ec0] mntput at ffffffff811c0ed4
#12 [ffff8816c8d13ed0] namespace_unlock at ffffffff811c1007
#13 [ffff8816c8d13f10] sys_umount at ffffffff811c20db
#14 [ffff8816c8d13f80] system_call_fastpath at ffffffff8172ea09
    RIP: 00007f09fccb9717  RSP: 00007fff1dca47f8  RFLAGS: 00010202
    RAX: 00000000000000a6  RBX: ffffffff8172ea09  RCX: 0000000000000010
    RDX: 0000000000000000  RSI: 0000000000000000  RDI: 00007f09ff731070
    RBP: 00007f09ff731050   R8: 00007f09ff731100   R9: 0000000000000000
    R10: 00007fff1dca4620  R11: 0000000000000246  R12: 0000000000000000
    R13: 0000000000000000  R14: 0000000000000000  R15: 00007f09ff731140
    ORIG_RAX: 00000000000000a6  CS: 0033  SS: 002b
crash> q
local_lock+0x39/0x40
[92451.444346]  [<ffffffff811c0d81>] mntput_no_expire+0x21/0x150
[92451.450254]  [<ffffffff811c0ed4>] mntput+0x24/0x40
[92451.455206]  [<ffffffff811c1007>] namespace_unlock+0x117/0x130
[92451.461199]  [<ffffffff811c20db>] SyS_umount+0x1db/0x3b0
[92451.466674]  [<ffffffff8172ea09>] system_call_fastpath+0x16/0x1b
[<EMAIL> crash]#
```

关闭 kmem 计数
