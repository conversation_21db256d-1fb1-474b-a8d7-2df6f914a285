# whgg-cce-events00.whgg 宕机

## 问题描述

2020-10-19 发现 whgg-cce-events00.whgg 宕机, 无法 ping 及 SSH 联通, 通过 RMS 发单重启恢复.

## 问题分析

whgg-cce-events00.whgg 宕机时间应该在: Oct 19 14:19:18

```bash
 Oct 19 14:14:38 whgg-bcc-online-com0-028 kernel: [29034881.889935] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:14:58 whgg-bcc-online-com0-028 kernel: [29034901.901532] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:15:18 whgg-bcc-online-com0-028 kernel: [29034921.913307] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:15:38 whgg-bcc-online-com0-028 kernel: [29034941.920794] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:15:58 whgg-bcc-online-com0-028 kernel: [29034961.932691] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:16:18 whgg-bcc-online-com0-028 kernel: [29034981.941516] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:16:38 whgg-bcc-online-com0-028 kernel: [29035001.949438] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:16:58 whgg-bcc-online-com0-028 kernel: [29035021.960275] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:17:18 whgg-bcc-online-com0-028 kernel: [29035041.969282] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:17:38 whgg-bcc-online-com0-028 kernel: [29035061.981226] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:17:58 whgg-bcc-online-com0-028 kernel: [29035081.987134] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:18:18 whgg-bcc-online-com0-028 kernel: [29035102.006757] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:18:38 whgg-bcc-online-com0-028 kernel: [29035122.012530] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:18:58 whgg-bcc-online-com0-028 kernel: [29035142.027459] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.
Oct 19 14:19:18 whgg-bcc-online-com0-028 kernel: [29035162.025558] NMI watchdog: enabled on all CPUs, permanently consumes one hw-PMU counter.


Oct 19 14:43:38 whgg-cce-events00 kernel: imklog 5.8.10, log source = /proc/kmsg started.
Oct 19 14:43:38 whgg-cce-events00 rsyslogd: [origin software="rsyslogd" swVersion="5.8.10" x-pid="96586" x-info="http://www.rsyslog.com"] start
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] Initializing cgroup subsys cpuset
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] Initializing cgroup subsys cpu
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] Initializing cgroup subsys cpuacct
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] Initializing cgroup subsys tasks
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] Linux version 3.10.0_3-0-0-12 (<EMAIL>) (gcc version 4.8.2 20140120 (Red Hat 4.8.2-16) (GCC) ) #1 SMP Thu Nov 2 14:22:44 CST 2017
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] Command line: ro root=/dev/sda2 crashkernel=auto biosdevname=0 console=ttyS0,115200 console=tty0
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] e820: BIOS-provided physical RAM map:
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] BIOS-e820: [mem 0x0000000000000000-0x0000000000099fff] usable
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] BIOS-e820: [mem 0x000000000009a000-0x000000000009ffff] reserved
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] BIOS-e820: [mem 0x00000000000e0000-0x00000000000fffff] reserved
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] BIOS-e820: [mem 0x0000000000100000-0x0000000069cf7fff] usable
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] BIOS-e820: [mem 0x0000000069cf8000-0x000000006c595fff] reserved
Oct 19 14:43:38 whgg-cce-events00 kernel: [    0.000000] BIOS-e820: [mem 0x000000006c596000-0x000000006c72dfff] usable
```

机器上有 crash 文件:

```bash
[<EMAIL> crash]# pwd
/var/crash

[<EMAIL> crash]# ls
127.0.0.1-2020-10-19-22:18:23
```

## 查看 crash 文件

1. 下载 crash_analy 工具

```bash
$wget --user=guest01 --password=baidu@123 ftp://cp01-cos-dev01.cp01.baidu.com:/download/crash_analy.tar.gz
```

2. 解压并将相关文件复制到 /var/crash

```
[<EMAIL> crash]# pwd
/var/crash

[<EMAIL> crash]# ls
127.0.0.1-2020-10-19-22:18:23  analysy_sh.sh  crash_sh 
```

3. 执行分析工具

```
$sh analysy_sh.sh
```

4. 查看分析结果

```
$less crash_analy_data
crash 7.1.6
Copyright (C) 2002-2016  Red Hat, Inc.
Copyright (C) 2004, 2005, 2006, 2010  IBM Corporation
Copyright (C) 1999-2006  Hewlett-Packard Co
Copyright (C) 2005, 2006, 2011, 2012  Fujitsu Limited
Copyright (C) 2006, 2007  VA Linux Systems Japan K.K.
Copyright (C) 2005, 2011  NEC Corporation
Copyright (C) 1999, 2002, 2007  Silicon Graphics, Inc.
Copyright (C) 1999, 2000, 2001, 2002  Mission Critical Linux, Inc.
and you are welcome to change it and/or distribute copies of it under
certain conditions.  Enter "help copying" to see the conditions.
This program has absolutely no warranty.  Enter "help warranty" for details.

GNU gdb (GDB) 7.6
Copyright (C) 2013 Free Software Foundation, Inc.
License GPLv3+: GNU GPL version 3 or later <http://gnu.org/licenses/gpl.html>
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.  Type "show copying"
and "show warranty" for details.
This GDB was configured as "x86_64-unknown-linux-gnu"...

      KERNEL: /var/crash/kernel-3.10.0_3-0-0-12/vmlinux
    DUMPFILE: /var/crash/127.0.0.1-2020-10-19-22:18:23/vmcore  [PARTIAL DUMP]
        CPUS: 32
        DATE: Mon Oct 19 14:20:00 2020
      UPTIME: 335 days, 21:29:35
LOAD AVERAGE: 0.88, 0.96, 0.89
       TASKS: 2557
    NODENAME: whgg-cce-events00.whgg.baidu.com
     RELEASE: 3.10.0_3-0-0-12
     VERSION: #1 SMP Thu Nov 2 14:22:44 CST 2017
     MACHINE: x86_64  (2094 Mhz)
      MEMORY: 127.7 GB
       PANIC: "kernel BUG at lib/idr.c:1154!"
         PID: 74058
     COMMAND: "kworker/0:15"
        TASK: ffff881f912fb980  [THREAD_INFO: ffff88005da40000]
         CPU: 0
       STATE: TASK_RUNNING (PANIC)

crash> bt
[29035204.233300] lowmem_reserve[]: 0 0 126952 126952
[29035204.233302] Node 0 Normal free:23695920kB min:45360kB low:56700kB high:68040kB active_anon:2749672kB inactive_anon:243484kB active_file:77391176kB inactive_file:14174396kB unevictable:0kB isolated(anon):0kB isolated(file):0kB present:132120576kB managed:129999664kB mlocked:0kB dirty:572kB writeback:0kB mapped:396936kB shmem:749524kB slab_reclaimable:7837896kB slab_unreclaimable:1530336kB kernel_stack:37456kB pagetables:31148kB unstable:0kB bounce:0kB free_cma:0kB writeback_tmp:0kB pages_scanned:0 all_unreclaimable? no
[29035204.233307] lowmem_reserve[]: 0 0 0 0
[29035204.233309] Node 0 DMA: 0*4kB 0*8kB 1*16kB (U) 0*32kB 2*64kB (U) 1*128kB (U) 1*256kB (U) 0*512kB 1*1024kB (U) 1*2048kB (R) 3*4096kB (M) = 15888kB
[29035204.233318] Node 0 DMA32: 8433*4kB (UEM) 6212*8kB (UEM) 4740*16kB (UEM) 2064*32kB (UEM) 488*64kB (UEM) 81*128kB (UM) 18*256kB (M) 7*512kB (UM) 2*1024kB (M) 5*2048kB (UEM) 54*4096kB (MR) = 508580kB
[29035204.233328] Node 0 Normal: 5921448*4kB (UEM) 254*8kB (UEM) 1*16kB (E) 2*32kB (R) 0*64kB 33*128kB (UR) 9*256kB (UR) 9*512kB (UER) 1*1024kB (R) 1*2048kB (U) 0*4096kB = 23702112kB
[29035204.233337] Node 0 hugepages_total=0 hugepages_free=0 hugepages_surp=0 hugepages_size=2048kB
[29035204.233339] 23129885 total pagecache pages
[29035204.233340] 0 pages in swap cache
[29035204.233342] Swap cache stats: add 0, delete 0, find 0/0
[29035204.233343] Free swap  = 0kB
[29035204.233343] Total swap = 0kB
[29035204.233344] 33465059 pages RAM
[29035204.233345] 0 pages HighMem/MovableOnly
[29035204.233346] 615737 pages reserved
[29035204.254884] ------------[ cut here ]------------
[29035204.254889] kernel BUG at lib/idr.c:1154!
[29035204.254891] invalid opcode: 0000 [#1] SMP
[29035204.254893] Modules linked in: nf_conntrack_netlink(E) xt_u32(E) xt_multiport(E) xt_set(E) iptable_raw(E) ip_set_hash_net(E) ip_set_hash_ip(E) ip_set(E) nfnetlink(E) ipip(E) tunnel4(E) ip_tunnel(E) xt_state(E) sch_htb(E) xt_nat(E) ip_vs_sh(E) ip_vs_wrr(E) ip_vs_rr(E) ip_vs(E) libcrc32c(E) veth(E) xt_mark(E) xt_comment(E) ipt_MASQUERADE(E) nf_nat_masquerade_ipv4(E) iptable_filter(E) iptable_nat(E) nf_conntrack_ipv4(E) nf_defrag_ipv4(E) nf_nat_ipv4(E) xt_addrtype(E) xt_conntrack(E) nf_nat(E) nf_conntrack(E) overlay(E) bridge(E) stp(E) llc(E) ipmi_devintf(E) tcp_diag(E) inet_diag(E) iptable_mangle(E) ip_tables(E) ipv6(E) binfmt_misc(E) dm_mirror(E) dm_region_hash(E) dm_log(E) dm_mod(E) pcspkr(E) sg(E) i40e(E) vxlan(E) ip6_udp_tunnel(E) udp_tunnel(E) ptp(E) pps_core(E) i2c_i801(E) i2c_core(E) shpchp(E)
[29035204.254925]  ipmi_si(E) ipmi_msghandler(E)
[29035204.254929] CPU: 0 PID: 74058 Comm: kworker/0:15 Tainted: G            E  ------------ T 3.10.0_3-0-0-12 #1
[29035204.254931] Hardware name: Inspur SA5112M5/YZMB-00870-101, BIOS 4.0.0 03/16/2018
[29035204.254938] Workqueue: events free_work
[29035204.254939] task: ffff881f912fb980 ti: ffff88005da40000 task.ti: ffff88005da40000
[29035204.254941] RIP: 0010:[<ffffffff8134d451>]  [<ffffffff8134d451>] ida_simple_remove+0x41/0x50
[29035204.254947] RSP: 0018:ffff88005da43db0  EFLAGS: 00010286
[29035204.254949] RAX: 0000000000002ffe RBX: 00000000ffffffff RCX: 0000000000002fff
[29035204.254950] RDX: 0000000000002fff RSI: 00000000ffffffff RDI: ffffffff81f293e0
[29035204.254951] RBP: ffff88005da43dc8 R08: 0000000000000000 R09: 0000000000018c50
[29035204.254952] R10: 000000000000267c R11: 0000000000018c31 R12: ffff8800026b4000
[29035204.254953] R13: 0000000000000400 R14: ffff881ff61de770 R15: 0000000000000004
[29035204.254955] FS:  0000000000000000(0000) GS:ffff881ffde00000(0000) knlGS:0000000000000000
[29035204.254956] CS:  0010 DS: 0000 ES: 0000 CR0: 0000000080050033
[29035204.254957] CR2: ffffffffff600400 CR3: 0000000b0ff74000 CR4: 00000000003407f0
[29035204.254958] DR0: 0000000000000000 DR1: 0000000000000000 DR2: 0000000000000000
[29035204.254960] DR3: 0000000000000000 DR6: 00000000fffe0ff0 DR7: 0000000000000400
[29035204.254961] Stack:
[29035204.254962] Call Trace:
[29035204.254967]  [<ffffffff8118ce94>] __mem_cgroup_free+0x214/0x230
[29035204.254PID: 74058  TASK: ffff881f912fb980  CPU: 0   COMMAND: "kworker/0:15"
 #0 [ffff88005da43a98] machine_kexec at ffffffff8103cb68
 #1 [ffff88005da43ae8] crash_kexec at ffffffff810c25a3
 #2 [ffff88005da43bb0] oops_end at ffffffff816f1b70
 #3 [ffff88005da43bd8] die at ffffffff8100627b
 #4 [ffff88005da43c08] do_trap at ffffffff816f1290
 #5 [ffff88005da43c58] do_invalid_op at ffffffff81003172
 #6 [ffff88005da43d00] invalid_op at ffffffff816fa71e
    [exception RIP: ida_simple_remove+65]
    RIP: ffffffff8134d451  RSP: ffff88005da43db0  RFLAGS: 00010286
    RAX: 0000000000002ffe  RBX: 00000000ffffffff  RCX: 0000000000002fff
    RDX: 0000000000002fff  RSI: 00000000ffffffff  RDI: ffffffff81f293e0
    RBP: ffff88005da43dc8   R8: 0000000000000000   R9: 0000000000018c50
    R10: 000000000000267c  R11: 0000000000018c31  R12: ffff8800026b4000
    R13: 0000000000000400  R14: ffff881ff61de770  R15: 0000000000000004
    ORIG_RAX: ffffffffffffffff  CS: 0010  SS: 0018
 #7 [ffff88005da43dd0] __mem_cgroup_free at ffffffff8118ce94
 #8 [ffff88005da43e18] free_work at ffffffff8118cec5
 #9 [ffff88005da43e28] process_one_work at ffffffff8107ad70
#10 [ffff88005da43e70] worker_thread at ffffffff8107b9dd
#11 [ffff88005da43ed0] kthread at ffffffff81082970
#12 [ffff88005da43f50] ret_from_fork at ffffffff816f8f58
crash> q
971]  [<ffffffff8118cec5>] free_work+0x15/0x20
[29035204.254974]  [<ffffffff8107ad70>] process_one_work+0x180/0x430
[29035204.254977]  [<ffffffff8107b9dd>] worker_thread+0x12d/0x3d0
[29035204.254979]  [<ffffffff8107b8b0>] ? rescuer_thread+0x370/0x370
[29035204.254985]  [<ffffffff81082970>] kthread+0xc0/0xd0
[29035204.254989]  [<ffffffff810828b0>] ? kthread_create_on_node+0x120/0x120
[29035204.254993]  [<ffffffff816f8f58>] ret_from_fork+0x58/0x90
[29035204.254996]  [<ffffffff810828b0>] ? kthread_create_on_node+0x120/0x120
[29035204.254997] Code: 2d f5 81 e8 42 33 3a 00 89 de 49 89 c5 4c 89 e7 e8 65 fe ff ff 4c 89 ee 48 c7 c7 58 2d f5 81 e8 76 2f 3a 00 5b 41 5c 41 5d 5d c3 <0f> 0b 66 66 66 66 2e 0f 1f 84 00 00 00 00 00 55 48 89 e5 41 57
[29035204.255021] RIP  [<ffffffff8134d451>] ida_simple_remove+0x41/0x50
[29035204.255023]  RSP <ffff88005da43db0>
```

参考链接: [内核问题FAQ](http://wiki.baidu.com/pages/viewpage.action?pageId=1026896898)

## 参考

[0015612: kernel BUG at lib/idr.c:1163!](https://bugs.centos.org/view.php?id=15612)





