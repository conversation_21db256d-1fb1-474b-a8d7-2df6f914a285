# memory cgroup泄漏排障指南

## 现象

### 现象一：新建容器报“no space left on device”，memory cgroup num不多

docker新建容器时：

```shell
# docker run --name=hello -d hello-world
d3a26424f27a5703e6207e9000358a3048f865438f258e4db1d3f3a607f7f858
docker: Error response from daemon: invalid header field value "oci runtime error: container_linux.go:247: starting container process caused \"process_linux.go:258: applying cgroup configuration for process caused \\\"mkdir /cgroup/memory/docker/d3a26424f27a5703e6207e9000358a3048f865438f258e4db1d3f3a607f7f858: no space left on device\\\"\"\n".
```

kubernetes新建Pod时：

```shell
E0723 14:10:48.255404    2460 pod_workers.go:186] Error syncing pod eda017cf-acef-11e9-aa46-6c92bf991e1c ("hello-56cd858d87-jqssj_cfc(eda017cf-acef-11e9-aa46-6c92bf991e1c)"), skipping: failed to ensure that the pod: eda017cf-acef-11e9-aa46-6c92bf991e1c cgroups exist and are correctly applied: failed to create container for [kubepods besteffort podeda017cf-acef-11e9-aa46-6c92bf991e1c] : mkdir /cgroup/memory/kubepods/besteffort/podeda017cf-acef-11e9-aa46-6c92bf991e1c: no space left on device
```

cgroups memory num_cgroups并不多：

```shell
# cat /proc/cgroups
#subsys_name	hierarchy	num_cgroups	enabled
cpuset	1	720	1
cpu	2	78	1
cpuacct	3	54	1
memory	4	57	1
devices	5	719	1
freezer	6	55	1
net_cls	7	51	1
blkio	8	54	1
perf_event	16	7	1
hugetlb	29	9	1
tos_cgroup	19	5	1
tcp_throt	18	6	1
tasks	17	6	1
```

### 现象二：新建容器报“no space left on device”，memory cgroup条目多，但是容器个数少

docker新建容器时：

```shell
# docker run --name=hello -d hello-world
d3a26424f27a5703e6207e9000358a3048f865438f258e4db1d3f3a607f7f858
docker: Error response from daemon: invalid header field value "oci runtime error: container_linux.go:247: starting container process caused \"process_linux.go:258: applying cgroup configuration for process caused \\\"mkdir /cgroup/memory/docker/d3a26424f27a5703e6207e9000358a3048f865438f258e4db1d3f3a607f7f858: no space left on device\\\"\"\n".
```

kubernetes新建Pod时：

```shell
E0723 14:10:48.255404    2460 pod_workers.go:186] Error syncing pod eda017cf-acef-11e9-aa46-6c92bf991e1c ("hello-56cd858d87-jqssj_cfc(eda017cf-acef-11e9-aa46-6c92bf991e1c)"), skipping: failed to ensure that the pod: eda017cf-acef-11e9-aa46-6c92bf991e1c cgroups exist and are correctly applied: failed to create container for [kubepods besteffort podeda017cf-acef-11e9-aa46-6c92bf991e1c] : mkdir /cgroup/memory/kubepods/besteffort/podeda017cf-acef-11e9-aa46-6c92bf991e1c: no space left on device
```

cgroups memory num_cgroups多：

```shell
# cat /proc/cgroups
#subsys_name	hierarchy	num_cgroups	enabled
cpuset	1	720	1
cpu	2	78	1
cpuacct	3	54	1
memory	4	65535	1
devices	5	719	1
freezer	6	55	1
net_cls	7	51	1
blkio	8	54	1
perf_event	16	7	1
hugetlb	29	9	1
tos_cgroup	19	5	1
tcp_throt	18	6	1
tasks	17	6	1
```

### 现象三：宿主机slub分配报错

节点的 dmesg 显示slub无法分配内存：SLUB: Unable to allocate memory on node -1 

![img](http://rte.weiyun.baidu.com/api/imageDownloadAddress?attachId=0df97fa19c55f4a07f3d497ee7c55af6&sign=eyJhbGciOiJkaXIiLCJlbmMiOiJBMjU2R0NNIiwiYXBwSWQiOjEsInVpZCI6IlBVeUk5X09IR0giLCJkb2NJZCI6InVxdE1nVnlTaUN2bDhsIn0..Wq_35W8hvA-MGFXW.68LnUhYq_HjUAu_su8Sfakl-6bpHnD78lsoLtNpD2HuUgm6cf9ILdPTiHKRQFwXCSe12JJ0OL2ju0DWA8iETbz1Z04wzUr_uMkCGGgPS6QE3R18CGtvP1uq4GBZ9EMw5Slz0d7agMtPeY77YELi6uEJMHn1pt9ZN8FbPMcu9-ZgQNju3sUTzob9Oyr48yQ4Y5dO7HNEtHY6cCTiPw3F6sP29zQ.Mr22_a4wnrZeKRSK4RXWnw)

## 排查思路和解决方案

### 情况一：kmem泄漏导致的memory cgroup泄漏

#### 排查思路

1. 查看全局kmem配置

```bash
# cat /boot/config-`uname -r`|grep CONFIG_MEMCG
CONFIG_MEMCG=y
CONFIG_MEMCG_SWAP=y
CONFIG_MEMCG_SWAP_ENABLED=y
CONFIG_MEMCG_KMEM=y
```

如果全局未开启kmem，则使用其余思路排查。如果全局开启了kmem，则继续排查。

2. 查看内核版本，内核版本为3系/4系时，则kmem accounting导致memory cgroup泄漏的概率较大

3. 查看容器内是否开启kmem accounting 

**方法一：查看slab信息**

子接口文件：**memory.kmem.slabinfo** 

开启状态，会看到slab信息：

```bash
# cat /cgroups/memory/kubepods/memory.kmem.slabinfo
slabinfo - version: 2.1
# name            <active_objs> <num_objs> <objsize> <objperslab> <pagesperslab> : tunables <limit> <batchcount> <sharedfactor> : slabdata <active_slabs> <num_slabs> <sharedavail>
```

关闭状态：

```bash
# cat /cgroups/memory/kubepods/burstable/pod970d25d1-3196-4df8-9e5b-2124d551eee6/memory.kmem.slabinfo
cat: /cgroups/memory/kubepods/burstable/pod970d25d1-3196-4df8-9e5b-2124d551eee6/memory.kmem.slabinfo: Input/output error
```

**方法二：查看kmem统计**

子接口文件：**memory.kmem.usage_in_bytes** 

开启状态，会看到kmem统计信息中有使用值：

```bash
# cat /sys/fs/cgroup/memory/memory.kmem.usage_in_bytes
12323
```

关闭状态：

```bash
# cat /sys/fs/cgroup/memory/memory.kmem.usage_in_bytes
0
```

#### 解决方案

**解决方案一：重启节点，故障止损**

1. 驱逐节点上的服务

2. 重启机器

3. 重新开启节点调度

**解决方案二：更换关闭kmem功能的kubelet/runc**

1. kubelet/runc编译时关闭kmem功能

runc：

 v1.0.0-rc94之后的版本，默认关闭

 v1.0.0-rc94之前的版本：

```bash
$ make BUILDTAGS="nokmem"
```

kubelet：

v 1.14之后的版本，在编译 kubelet 的时候加上 [Build Tags](https://link.zhihu.com/?target=https%3A//github.com/kubernetes/kubernetes/blob/release-1.14/vendor/github.com/opencontainers/runc/libcontainer/cgroups/fs/kmem_disabled.go%23L1) 可以关闭 kmem account：

```shell
$ make kubelet GOFLAGS="-tags=nokmem"
```

v.1.14以前的版本，则需要修改相关代码[code](https://github.com/kubernetes/kubernetes/blob/release-1.12/vendor/github.com/opencontainers/runc/libcontainer/cgroups/fs/memory.go#L70-L106)：

```go
func EnableKernelMemoryAccounting(path string) error {
		return nil
}
func setKernelMemory(path string, kernelMemoryLimit int64) error {
    return nil
}
```

**解决方案三：全局关闭kmem功能**

1. 修改 grub 中的cgroup.memory=nokmem，让机器启动时直接禁用 cgroup的 kmem。修改/etc/default/grub 为：

```bash
GRUB_CMDLINE_LINUX="crashkernel=auto net.ifnames=0 biosdevname=0 intel_pstate=disable cgroup.memory=nokmem"
```

2. 生成内核启动配置，然后重启机器

```bash
$ /usr/sbin/grub2-mkconfig -o /boot/grub2/grub.cfg
```

### 情况二：pagecache回收慢导致的memory cgroup回收慢

#### 排查思路

1. 查看全局memory cgroup个数，可以看到memory子系统个数达到限额

```bash
# cat /proc/cgroups
#subsys_name	hierarchy	num_cgroups	enabled
cpuset	1	720	1
cpu	2	78	1
cpuacct	3	54	1
memory	4	65535	1
devices	5	719	1
freezer	6	55	1
net_cls	7	51	1
blkio	8	54	1
perf_event	16	7	1
hugetlb	29	9	1
tos_cgroup	19	5	1
tcp_throt	18	6	1
tasks	17	6	1
```

2. 尝试drop_cache，观察memory cgroup子系统是否减少

```bash
$ echo 3 > /proc/sys/vm/drop_caches
$ cat /proc/cgroups
```

如果memory cgroup条目减少，则是由于pagecache回收慢导致的memory cgroup泄漏

#### 解决方案

1. 不做处理，内核会慢慢回收pagecache，当cgroup对应的pagecache被回收完毕，则该cgroup也会被内核回收
2. 升级BaiduLinux 3，BaiduLinux 3优化memory cgroup回收逻辑，删除cgroup后会异步回收该cgroup下的相应资源。



## 原理

1. kmem机制

kernel memory accounting 机制为 cgroup 的内存限制增加了 stack pages（例如新进程创建）、slab pages(SLAB/SLUB分配器使用的内存)、sockets memory pressure、tcp memory pressure等，以保证 kernel memory 不被滥用。

当你开启了kmem 机制，具体体现在 memory.kmem.limit_in_bytes 这个文件上：

```plain
/sys/fs/cgroup/memory/kubepods/pod632f736f-5ef2-11ea-ad9e-fa163e35f5d4/memory.kmem.limit_in_bytes
```

实际使用中，我们一般将 memory.kmem.limit_in_bytes 设置成大于 memory.limit_in_bytes，从而只限制应用的总内存使用。

kmem 的 limit 与普通 mem 的搭配，参考这篇文章：https://lwn.net/Articles/516529/

cgroup 文档:https://www.kernel.org/doc/Documentation/cgroup-v1/memory.txt



2. kmem的漏洞

在4.0以下版本的 Linux 内核对 kernel memory accounting 的支持并不完善，在3.x 的内核版本上，会出现 kernel memory 无法回收，bug 解释：

- https://bugzilla.redhat.com/show_bug.cgi?id=1507149
- https://github.com/kubernetes/kubernetes/issues/61937
- https://support.d2iq.com/s/article/Critical-Issue-KMEM-MSPH-2018-0006