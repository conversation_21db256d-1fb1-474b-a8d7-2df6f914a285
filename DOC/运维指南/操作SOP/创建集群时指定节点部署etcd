背景
==
 
车和家大数据容器团队搭建大规模集群。机器规格更高，etcd、k8s 控制面组件隔离部署。
 
1.创建集群
======
 
自定义参数，指定 handler： not-default
 
```
{
	"cluster": {
		"clusterName": "high",
		"handler": "not-default",
        ...
}
```
 
2.部署etcd相关
==========
 
根据背景里的需求：etcd 存储改为本地盘，加上指定的toleration，根据实际情况设置reuqest/limit 确保能直接调度到对应的节点上。然后根据集群信息，渲染yaml，并部署。（因为没有ca信息，tls secret内容是空的。）
 
```
# Default values.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
namespace: {clusterID}

replicas: 3

etcd:
  clusterName: etcd-{clusterID}
  tlsSecretName: etcd-tls-secret
  initialCluster: etcd-0=https://etcd-{clusterID}-0-0.etcd-{clusterID}.{clusterID}.svc:2380,etcd-1=https://etcd-{clusterID}-1-0.etcd-{clusterID}.{clusterID}.svc:2380,etcd-2=https://etcd-{clusterID}-2-0.etcd-{clusterID}.{clusterID}.svc:2380

image:
  etcd: registry.baidubce.com/cce-plugin-dev/etcd:3.4.3
```
```
cd $gopath/src/icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/plugin/helm/charts/cce-pro-etcd/ 
helm template . > all.yaml
```
```
# check ns
kubectl get ns cce-msfxxb0f 

cd /root/liqilong && vi {clusterID}-etcd.yaml

kubectl create -f {clusterID}-etcd.yaml
```
 
3.继续部署
======
 
修改handler: default， retryCount： 19
 
```
status:  retryCount: 19
```
 
4.创建etcd tls secret
===================
 
进入cluster-controller ：/home/<USER>/cce/certs/{clusterID}
 
```
# 提前确认好 cluster-controller 的主
# kubectl -n cce-system exec -it cce-cluster-controller-msvk2 bash
ca {path-to-ca} && cat >etcd-csr.json <<EOF
{
    "CN": "jpaas",
    "hosts": [
		"127.0.0.1",
		"localhost",
        "etcd-{clusterID}.{clusterID}.svc",
        "etcd-{clusterID}-0-0.etcd-{clusterID}.{clusterID}.svc",
        "etcd-{clusterID}-1-0.etcd-{clusterID}.{clusterID}.svc",
        "etcd-{clusterID}-2-0.etcd-{clusterID}.{clusterID}.svc"
    ],
    "key": {
        "algo": "rsa",
        "size": 2048
    },
    "names": [
        {
            "C": "CN",
            "ST": "BeiJing",
            "L": "BeiJing",
            "O": "jpaas",
            "OU": "cloudnative"
        }
    ]
}
EOF

# generate etcd key pair
cd {path-to-ca} && cfssl gencert -ca=ca.pem -ca-key=ca-key.pem -config=ca-config.json -profile=jpaas etcd-csr.json | cfssljson -bare etcd

```

```
# get content of the pem files then write into pro-meta cluster
cat ca.pem

kubectl -n {clusterID} delete secret etcd-tls-secret

# create etcd tls secret
kubectl -n {clusterID} create secret generic etcd-tls-secret --from-file=ca.pem=ca.pem --from-file=etcd.pem=etcd.pem --from-file=etcd-key.pem=etcd-key.pem
```
 
5.重试集群部署
========
 
直到cluster running
 
6.修改apiserver等组件的配置
===================
 
request/limit， request/limit 不一定足够所有pod调度到目标节点（规格上不能跟常规节点严格隔离）最好配置nodeAffinity
 
```
          resources:
            requests:
              cpu: 8 
              memory: 32Gi
            limits:
              cpu: 32
              memory: 128Gi
```
 
toleration
 
```
      tolerations:
      - key: "user"
        operator: "Equal"
        value: "chehejia"
        effect: "NoSchedule"
      - key: "component"
        operator: "Equal"
        value: "control-plane"
        effect: "NoSchedule"
```
 
亲和性配置
 
```
kubectl label node {nodeName} user=chehejia
```
```
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: user
                operator: In
                values:
                - chehejia
```