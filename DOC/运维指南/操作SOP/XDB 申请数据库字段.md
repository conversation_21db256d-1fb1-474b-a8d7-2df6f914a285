# 申请 CCE 数据库新字段

> 提示：
> XDB mars平台的各项功能都已经在云上百度产品“分布式关系型数据库DRDS”中进行覆盖，对MySQL数据库集群的各项操作都可以在云上百度进行     
> 参考链接：[DRDS常见问题汇总](https://cloud.baidu-int.com/icloud/DRDS/%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98/DRDS%E5%B8%B8%E8%A7%81%E9%97%AE%E9%A2%98%E6%B1%87%E6%80%BB/)    
> 参考链接：[申请DRDS集群资源账号权限](https://cloud.baidu-int.com/icloud/DRDS/%E6%9C%80%E4%BD%B3%E5%AE%9E%E8%B7%B5/%E7%94%B3%E8%AF%B7DRDS%E9%9B%86%E7%BE%A4%E8%B5%84%E6%BA%90%E8%B4%A6%E5%8F%B7%E6%9D%83%E9%99%90/)    


## 参考：CCE 线上数据库集群
bcepublic  
bcepublicgzns  
bcepublicth  
bcepublicbdbl1  
bcepublicfsgb  
bcepublicfsgbjdd  
bcepublichkg03  
bcepublicwhgg  

> 注意：新增的字段要在每个线上数据库集群都申请一遍

## 申请数据库新字段

### 第零步：访问网址
访问XDB网址：[http://dba.baidu.com/mars/#/main/index](http://dba.baidu.com/mars/#/main/index)
如果左边没有出现线上数据库列表的话需要创建工单进行申请

### 第一步 选择线上数据库集群 发起评审
![图片](pic/xdb-step1.png)

### 第二步 准备SQL语句
示例如下
```
/*确认不会在新加字段上进行条件检索*/
alter table t_cce_instance add `cce_instance_priority` int(11) not null default 5 comment 'cce instance priority';![图片](https://agroup-bos-bj.cdn.bcebos.com/bj-4a8f97baf01cf0dd1480d55ed404896dc0807063)
```

### 第三步 填写表单
![图片](pic/xdb-step3.png)

### 第四步 进入无人评审
![图片](pic/xdb-step4.png)

### 第五步 找到自己的评审
![图片](pic/xdb-step5.png)

### 第六步 查看无人评审的结果并提交
![图片](pic/xdb-step6.png)

### 第七步 返回评审列表
![图片](pic/xdb-step7.png)

### 第八步 再次进入评审
![图片](pic/xdb-step8.png)

### 第九步 执行上限SQL
![图片](pic/xdb-step9.png)

### 第十步 查看检查信息并提交 
![图片](pic/xdb-step10.png)

### 第十一步 进入流程管理
![图片](pic/xdb-step11.png)

### 第十二步 复制链接并找人审批
![图片](pic/xdb-step12.png)

### 第十三步 审批通过
审批过了，再点执行+确认

### 所有步骤完成 上线上数据库查看一下字段是否已经存在