# 批量删除 Failed Instance

作用: 在 metacluster 上使用 kubectl admin.conf 批量删除 Failed Instance.

**不到万不得已, 不要执行此操作, 尽量让用户页面删除, 如果一定要执行, 务必找@陈欢确认!!!**

## 获取失败 Instance 列表

下面获取列表务必**手动分批执行**, 不要图快:

```bash
$kubectl get instance |grep cce-s4o3g1u4  |grep create_failed  | head -10 > fuck
```

批量删除脚本 batch_delete_failed_instance.sh :

```bash
#!/bin/bash

for instance_id in $(cat fuck | awk '{print $1}')
do
    echo ${instance_id}

    # delete instance
    kubectl --kubeconfig=./admin.conf delete instance ${instance_id} --wait=false
    sleep 2

    # 修改 retryCount 为 1
    kubectl get instance ${instance_id} -o yaml | sed 's/retryCount: 20/retryCount: 1/g' | kubectl replace -f -
    sleep 2
done
```