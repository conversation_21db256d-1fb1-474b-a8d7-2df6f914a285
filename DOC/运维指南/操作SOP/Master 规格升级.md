# Master机器规格升级


目前只支持托管master的规格升级（cce-，c- 开头的集群均支持），下面是具体操作流程。

## 1.创建 master 规格升级的 workflow

在 pkg/bcesdk/ccev2/workflow_test.go 文件下执行方法 TestCreateResizeMasterWorkflow。执行前需要做些修改，修改内容参考图片。

![图片](pic/create-resizeMasterWorkflow.png)

```go
func TestCreateResizeMasterWorkflow(t *testing.T) {
	// 填写、修改以下信息
	clusterID := "" // 用户集群ID，例：cce-06w1u9nk
	cpuCoreNum := 0 // 预期的 cpu 核数
	memInGB := 0    // 预期的 memory（GB）
	accountID := "" // 账号ID
	region := "" // bj,bd,wh,su,nj,gz,hkg
	
	...
}
```

执行成功会返回 workflowID（类似：workflow-cce-1b1z4s3d-2pc564cr）。

## 2.查看升级过程/结果

根据 workflowID 查看 workflow 执行过程/结果。

```bash
# 到对应地域的 meta cluster 查看 workflow
kubectl describe workflow {workflowID}

```

参考图片

![图片](pic/describe-resizeMasterWorkflow.png)

## 参考

想了解该workflow执行了哪些操作，参考文档：http://wiki.baidu.com/pages/viewpage.action?pageId=*********