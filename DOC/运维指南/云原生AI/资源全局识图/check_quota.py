#!/bin/env python
# coding=utf-8

#
# 检查某个任务的资源是否足够
#

"""
    filename.log 为cce 调度器的日志文件
    使用方式: cat filename.log | grep "min resource" | while read line;do python check_quota.py "${line}"; done

    input:
        任务申请资源详情所在行的日志
    output:
        输出到控制台：
                     job_name    resourse_name     expect_val     actual_val
        不满足的资源:  job[{}], resourse[{:40s}], expect [{:.2f}], actual [{:.2f}], is not satisfy.
        满足的资源:    job[{}], resourse[{:40s}], expect [{:.2f}], actual [{:.2f}] is not satisfy.  ========++++++========"
"""

import sys
import re

def cal_quota(resource_str, quota_map):
    """get json from api"""
    resource_list = resource_str.split(",")
    for resourse_type in resource_list:
        resourse_type_list = resourse_type.strip().split(" ")
        resourse_name = resourse_type_list[0]
        resourse_val = float(resourse_type_list[1])
        if quota_map.__contains__(resourse_name):
            quota_map[resourse_name] = quota_map[resourse_name] + resourse_val
        else:
            quota_map[resourse_name] = resourse_val

args = sys.argv

# 如果只有一个参数，说明没有传入任何参数
if len(args) == 1:
    print("请传入一行日志参数。")
else:
    # 取出第二个参数，即传入的字符串参数
    str = args[1]
    if str.strip() == '':
        exit(0)
    result = re.split(r'[<>]', str)
    job_name = result[0].strip().split(" ")[-3]
    min_resource = result[1]
    capability = result[3]
    allocated = result[5]
    inqueue = result[7]
    elastic = result[9]

    all_quota = {}
    cal_quota(capability, all_quota)

    use_quota = {}
    cal_quota(min_resource, use_quota)
    cal_quota(allocated, use_quota)
    cal_quota(inqueue, use_quota)

    # delete
    for key, value in use_quota.items():
        if not all_quota.__contains__(key):
            print('job[{}], resourse[{:40s}], expect [{:.2f}], actual [{:.2f}],  is not satisfy.\
                 ========++++++========'.
            format(job_name, key, value, 0.0))
        else:
            if all_quota[key] - value >= 0:
                print("job[{}], resourse[{:40s}], expect [{:.2f}], actual [{:.2f}] is satisfy.".
                    format(job_name, key, value, all_quota[key]))
            else:
                print("job[{}], resourse[{:40s}], expect [{:.2f}], actual [{:.2f}] is not satisfy. \
                    ========++++++========".
                    format(job_name, key, value, all_quota[key]))

