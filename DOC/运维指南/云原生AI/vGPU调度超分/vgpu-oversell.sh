#!/bin/bash

NODENAME=$1

if [[ -z "$NODENAME" ]]; then
    echo "Error: NODENAME not specified"
    exit 1
fi

rm -rf $NODENAME
mkdir $NODENAME


declare -A deviceMemMap
declare -A deviceCoreMap

declare -A podMemMap
declare -A podCoreMap


command_output=$(kubectl get pods --all-namespaces -o wide --field-selector spec.nodeName=$NODENAME --no-headers)

echo "$command_output" | while IFS= read -r line
do
    ns=$(echo "$line" | awk '{print $1}')
    pod=$(echo "$line" | awk '{print $2}')
    kubectl -n $ns get pod $pod -o json > $NODENAME/$pod.json
done

# 循环遍历目录下的json后缀文件
for file in $(find "$NODENAME" -type f -name "*.json"); do
    pIdxs=$(cat $file | jq -r '.metadata.annotations.BAIDU_COM_DEVICE_IDX | select(. != null)')
    IFS=',' read -ra idxArr <<< "$pIdxs"
    count=${#idxArr[@]}
    if [[ $count -eq 0 ]]; then
        continue
    fi

    pmem=$(cat $file | jq -r '.metadata.annotations.BAIDU_COM_DEVICE_MEM_POD | select(. != null) | tonumber')
    pcore=$(cat $file | jq -r '.metadata.annotations.BAIDU_COM_DEVICE_CORE_POD | select(. != null) | tonumber')
    dmem=$(cat $file | jq -r '.metadata.annotations.BAIDU_COM_DEVICE_MEM_DEVICE | select(. != null) | tonumber')
    dcore=$(cat $file | jq -r '.metadata.annotations.BAIDU_COM_DEVICE_CORE_DEVICE | select(. != null) | tonumber')
    scheTime=$(grep -B3 "PodSche"  $file | grep "TransitionTime" | awk -F ': ' '{print $2}')

    for idxStr in "${idxArr[@]}"; do
        pIdx=$(expr "$idxStr" + 0)
        
        echo "file $file device count $count device id $pIdx pod memory $pmem pod core $pcore device memory $dmem device core $dcore scheTime $scheTime";

        if [ -z "${deviceMemMap["$pIdx"]+isset}" ]; then
            deviceMemMap["${pIdx}"]=$dmem
            deviceCoreMap["${pIdx}"]=$dcore
            if [[ 0 -eq $pmem ]]; then
                podMemMap["${pIdx}"]=$dmem
            else 
                if [[ $count -ge 2 ]];then
                    podMemMap["${pIdx}"]=$pmem/count
                else
                    podMemMap["${pIdx}"]=$pmem
                fi
            fi
            if [[ 0 -eq $pcore ]]; then
                podCoreMap["${pIdx}"]=$dcore
            else
                if [[ $count -ge 2 ]];then
                    podMemMap["${pIdx}"]=$pcore/count
                else
                    podCoreMap["${pIdx}"]=$pcore
                fi
            fi
        else
            if [[ 0 -eq $pmem ]]; then
                podMemMap["${pIdx}"]=$(( ${podMemMap["${pIdx}"]} + $dmem))
            else
                if [[ $count -ge 2 ]];then
                    podMemMap["${pIdx}"]=$(( ${podMemMap["${pIdx}"]} + $pmem/$count))
                else 
                    podMemMap["${pIdx}"]=$(( ${podMemMap["${pIdx}"]} + $pmem))
                fi
            fi
            if [[ 0 -eq $pcore ]]; then
                podCoreMap["${pIdx}"]=$(( ${podCoreMap["${pIdx}"]} + $dcore))
            else
                if [[ $count -ge 2 ]];then
                    podCoreMap["${pIdx}"]=$(( ${podCoreMap["${pIdx}"]} + $pcore/$count))
                else
                    podCoreMap["${pIdx}"]=$(( ${podCoreMap["${pIdx}"]} + $pcore))
                fi
            fi
        fi
    done    
done


for key in "${!deviceMemMap[@]}"; do
    dmem=${deviceMemMap[$key]}
    dcore=${deviceCoreMap[$key]}
    pmem=${podMemMap[$key]}
    pcore=${podCoreMap[$key]}
    echo "node $NODENAME IDX: $key,device MEM: ${dmem},pod MEM: ${pmem};device CORE: $dcore ,pod CORE: ${pcore}";
    if [[ "$pmem" -gt $dmem ]]; then
        echo "!!!: node $NODENAME IDX: $key mem oversell,device MEM: ${dmem},pod MEM: ${pmem}"
    fi
    if [[ "$pcore" -gt $dcore ]]; then
        echo "!!!: node $NODENAME IDX: $key mem oversell,device CORE: $dcore ,pod CORE: ${pcore}"
    fi
done
