# 适用场景

GPU错卡检测。

# 功能目标

1. 检测并安装jq。
2. 仅校验申请GPU资源的Pod。
3. 对于Pod的GPU检测方法：先通过nvidia-smi获取设备信息；如果获取失败，通过env `NVIDIA_VISIBLE_DEVICES` 获取。
4. 支持通过kubelet checkpoint获取容器的devices设备视图。
5. 支持通过Pod的annotation获取AI调度器分配的devices设备视图。

**限制：**

1. 暂不支持containerd运行时。

# 版本记录
## 版本记录

| 版本号 | 适配集群版本      | 更新时间   | 更新内容                                    | 影响 |
| ------ | -------------- | ---------- | ---------------------------------------  | ---- |
| 0.9.0  | CCE/v1.20-v1.22| 2023.06.09 | 单机GPU错卡检测能力，适用于整卡，未支持虚拟化   | -    |
| 1.0.0  | CCE/v1.20-v1.22| 2023.06.09 | 单机GPU错卡检测能力，支持虚拟化               | -    |
