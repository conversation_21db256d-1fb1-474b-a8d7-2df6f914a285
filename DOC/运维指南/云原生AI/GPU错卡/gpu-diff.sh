#!/bin/bash

# 使用 nvidia-smi 命令获取 GPU 信息并进行处理
gpu_info=$(nvidia-smi --query-gpu=index,uuid --format=csv,noheader | tr -d ' ')


function findUUID() {
	# 在 GPU 信息中查找指定的设备号
	gpu_id=$1
	if [[ "$gpu_id" == "" ]]; then
		echo "empty gpu id, exit"
		return ""
	fi
	uuid=""
	while IFS=',' read -r index gpu_uuid; do
        if [[ "$index" == "$gpu_id" ]]; then
            uuid="$gpu_uuid"
            break
        fi
	done <<< "$gpu_info"
	echo ${uuid}
}


function jq_install() {
    # 检查 jq 是否已安装
    if ! command -v jq &> /dev/null; then
        echo "jq 工具未安装，开始安装..."

        # 执行安装命令
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            brew install jq
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            if command -v apt-get &> /dev/null; then
                sudo apt-get install -y jq
            elif command -v yum &> /dev/null; then
                sudo yum install -y jq
            else
                echo "无法确定 Linux 发行版的包管理器，请手动安装 jq 工具。"
                exit 1
            fi
        else
            echo "不支持的操作系统类型，请手动安装 jq 工具。"
            exit 1
        fi

        echo "jq 工具安装完成。"
    else
        echo "jq 工具已安装。"
    fi
}

function isCGPU() {
	pod_uid=$1
	if [[ "$pod_uid" == "" ]]; then
		echo "empty pod uid, exit"
		exit 1
	fi
	# get pause container id
	pcid=$(docker ps | grep ${uid} | grep pause | awk '{print $1}')
    if [ "$pcid" == "" ]; then
        return 0
    fi
	GPUIndex=$(docker inspect ${pcid} | grep BAIDU_COM_DEVICE_IDX)
	if [ "$GPUIndex" != "" ]; then
		return 1
	fi
	return 0
}

function kubeAllocated() {
	pod_uid=$1
	if [[ "$pod_uid" == "" ]]; then
		echo "empty pod uid, exit"
		exit 1
	fi
    
    devices_str=$(cat /var/lib/kubelet/device-plugins/kubelet_internal_checkpoint |jq --arg pod "$pod_uid" '.Data.PodDeviceEntries[] | select(.PodUID==$pod)' | grep GPU | sed -e 's/[ ",]//g' | awk -F'_' '{print $1}'| sed -e 's/-$//g' | sort)
	if [ "$devices_str" == "" ]; then
		echo ""
        return
	fi
    readarray -t devices <<< $devices_str
    sort_devices=$(printf '%s,' ${devices[@]} | sed  's/,$//g')
	echo $sort_devices
}


function scheAllocated() {
	pod_uid=$1
	if [[ "$pod_uid" == "" ]]; then
		echo "empty pod uid, exit"
		exit 1
	fi
	# get pause container id
	pcid=$(docker ps | grep ${pod_uid} | grep pause | awk '{print $1}')
    if [ "$pcid" == "" ]; then
        echo ""
        return
    fi
	GPU_indexes=$(docker inspect ${pcid} | grep BAIDU_COM_DEVICE_IDX | awk -F':' '{print $NF}' | sed -e 's/[ "]//g'|sed -e 's/,$//g')
	if [ "$GPU_indexes" == "" ]; then
		echo ""
        return
	fi
    IFS=',' read -ra ids <<< $GPU_indexes
    devices=()
    for id in "${ids[@]}";do
        uuid=$(findUUID ${id})
        if [ -z "$devices" ]; then
            devices=("$uuid")
        else
            devices+=($uuid)
        fi
    done
    sort_devices_str=$(printf '%s\n' ${devices[@]} | sort | uniq)
    readarray -t devices <<< $sort_devices_str
    sort_devices=$(printf '%s,' ${devices[@]} | sed  's/,$//g')
    echo $sort_devices
}


function checkpoint_dump() {
	for uid in `cat /var/lib/kubelet/device-plugins/kubelet_internal_checkpoint | jq '.Data.PodDeviceEntries[].PodUID' | sed -e 's/"//g'`;do
        #echo "device uid == ${uid} ==";
		device_in_kubelet=$(cat /var/lib/kubelet/device-plugins/kubelet_internal_checkpoint | jq . | grep -A10 $uid | grep GPU | sed -e 's/[ ",]//g' | awk -F'_' '{print $1}' | sed -e 's/-$//g' | uniq)
		if [ "${device_in_kubelet}" == "" ]; then
			echo "$uid not a gpu pod, ignore it"
			continue
		fi
		isCGPU ${uid}
		is_cgpu=$?
        # echo "is_cgpu == ${uid} is_cgpu ${is_cgpu} =="
		if (( $is_cgpu == 1 )); then
            device_in_kubernetes=$(scheAllocated $uid)
            #echo "device_in_kubernetes in sche == ${device_in_kubernetes} =="
		else
            device_in_kubernetes=$(kubeAllocated $uid)
            # echo "device_in_kubernetes in kube == ${device_in_kubernetes} =="
		fi
        if [ "$device_in_kubernetes" == "" ]; then 
            echo "check pod <none>/$uid failed: got none of device in kubernetes, ignore it"
            continue
        fi
        # echo "device_in_kubernetes == ${device_in_kubernetes} =="

        cid=$(docker ps | grep ${uid} | head -n1 | grep -v pause | awk '{print $1}')
        if [ "$cid" == ""  ];then
            echo "check pod <none>/$uid failed: got none of container, ignore it"
            continue
        fi
        # echo "container cid == ${cid} ==";
        env_in_pod=$(docker exec -it ${cid} nvidia-smi --query-gpu=uuid --format=csv,noheader | sort | uniq)
        res=$?
        if (( $res == 0 )); then
            # env_devices=$(docker inspect ${cid} | grep NVIDIA_VISIBLE_DEVICES | awk -F "=" '{print $NF}'| sed -e 's/",$//g')
            # IFS="," read -ra ids <<< $env_devices
            # env_in_pod=$(printf '%s\n' ${ids[@]} | sort | uniq)
            long_cid=$(docker inspect -f '{{.Id}}' ${cid})
            env_devices=$(cat /var/run/docker/containerd/daemon/io.containerd.runtime.v2.task/moby/${long_cid}/config.json |  jq .process.env | grep NVIDIA_VISIBLE_DEVICES| awk -F "=" '{print $NF}'|sed -e 's/",$//g')
            IFS="," read -ra ids <<< $env_devices
            env_in_pod=$(printf '%s\n' ${ids[@]} | sort | uniq)
        fi
        readarray -t devices <<< $env_in_pod
        device_in_pod=$(printf '%s,' ${devices[@]} | sed  's/,$//g')

        pod_name=$(docker ps | grep $uid | grep pause | awk '{print $NF}' | awk -F '_' '{print $3}')
        # echo "pod name == ${pod_name} ==";

        if [ "$device_in_kubernetes" != "$device_in_pod" ]; then
            echo "check pod ${pod_name}/${uid} gpu cards failed: in kubelet ${device_in_kubernetes}; in pod ${device_in_pod}";
        else 
            echo "check pod ${pod_name}/${uid} gpu cards success";
        fi
	done
}

jq_install
checkpoint_dump