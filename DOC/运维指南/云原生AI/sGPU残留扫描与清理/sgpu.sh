#!/bin/bash
set -e
docker inspect xx4f0fa5 2>&1 | grep "No suc" | wc -l
list() {
    node=$(ip a show eth0 | grep brd | grep "255" | awk '{print $2}' | awk -F '/' '{print $1}')
    for cID in `ls -l /proc/sgpu/container/ | grep d| awk '{print $NF}'`; do 
    	resCnt=$(docker inspect $cID 2>&1 | grep "No suc" | wc -l);
    	if [ "$resCnt" == "1" ]; then
    	 	echo "container $cID not found";
    	else 
    		echo "container $cID found";
    	fi; 
    done > /tmp/sgpu-res.log
}

stats() {
    list
    totalCount=$(cat /tmp/sgpu-res.log | wc -l)
    notFoundCount=$(cat /tmp/sgpu-res.log | grep "not found" | wc -l)
    echo "node $node sgpu container totalCount $totalCount notFoundCount $notFoundCount"
}

cleanDocker() {
    for cID in `ps aux | grep runc-v2 | awk '{print $15}'`;do
    	resCnt=$(docker inspect $cID 2>&1 | grep "No suc" | wc -l);
    	if [ "$resCnt" == "1" ]; then
    	 	docker kill $cID
    	else 
    		echo "container $cID found";
    	fi; 
    done
}

clean() {
    list
    for cID in `cat /tmp/sgpu-res.log | grep "not found" | awk '{print $2}' `;do
        docker kill $cID
        /root/sgpu/sgpu_hook_app -c $cID -o 1;
    done
}

case $1 in
    list)
        list
        cat /tmp/sgpu-res.log
        ;;
    stats)
        stats
        ;;
    clean)
        clean
        ;;
    *)
        echo "options is [list|stats|clean]"
        exit 0
        ;;
esac

