#!/bin/bash

function jq_install() {
    # 检查 jq 是否已安装
    if ! command -v jq &> /dev/null; then
        echo "jq 工具未安装，开始安装..."

        # 执行安装命令
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            brew install jq
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            if command -v apt-get &> /dev/null; then
                sudo apt-get install -y jq
            elif command -v yum &> /dev/null; then
                sudo yum install -y jq
            else
                echo "无法确定 Linux 发行版的包管理器，请手动安装 jq 工具。"
                exit 1
            fi
        else
            echo "不支持的操作系统类型，请手动安装 jq 工具。"
            exit 1
        fi

        echo "jq 工具安装完成。"
    else
        echo "jq 工具已安装。"
    fi
}


jq_install

podslist=$(kubectl get pods --all-namespaces -o json | jq -r '.items[] | select(.spec.schedulerName=="volcano") | "\(.metadata.namespace),\(.metadata.name)"')

for podinfo in $podslist; do
    ns=$(echo $podinfo | awk -F',' '{print $1}')
    pod=$(echo $podinfo | awk -F ','  '{print $2}')
    if [ $ns == "" ] || [ $pod == "" ]; then
        echo "wrong ns or pod: ns/pod => <$ns>/<$pod>"
        continue
    fi
    kubeAllocGiB=$(kubectl -n $ns get pod $pod -o json |jq .metadata.annotations.BAIDU_COM_DEVICE_MEM_POD | sed 's/"//g')
    kubeAlloc=$(echo "$kubeAllocGiB*1024" | bc)
    containerAlloc=$(kubectl -n $ns exec -it $pod -- nvidia-smi --query-gpu=memory.total --format=nounits,csv,noheader| sed 's/[^0-9]//g')
    if [ $kubeAlloc == "" ] || [ $containerAlloc == "" ]; then
        echo "wrong kubealloc or containerAlloc: ns/pod => <$ns>/<$pod>, kubealloc => <$kubeAlloc>, containerAlloc => <$containerAlloc>"
        continue
    fi

    if [ "$kubeAlloc" != "$containerAlloc" ]; then
        echo "<$ns>/<$pod> vgpu config mismatch: from kube $kubeAlloc vs container $containerAlloc"
    fi
done
