## 公共头规格

**公共请求头**

| 公共头部          | 描述                               |
| ------------- | -------------------------------- |
| Authorization | 包含Access Key与请求签名。               |
| Content-Type  | application/json; charset=utf-8。 |
| x-bce-date    | 表示日期的字符串，符合API规范。                |

HTTP协议的标准头域不再这里列出。公共头域将在每个BCC API中出现，是必需的头域。POST、PUT、DELETE等请求数据放在request body中。

**公共响应头**

| 公共头部             | 描述                               |
| ---------------- | -------------------------------- |
| Content-Type     | application/json; charset=utf-8。 |
| X-Bce-Request-Id | VPC后端生成，并自动设置到响应头域中。             |

## 错误返回

当用户访问API出现错误时，会返回给用户相应的错误码和错误信息，便于用户定位问题，并做出适当的处理。请求发生错误时通过Response Body返回详细错误信息，遵循如下格式：

| 参数名       | 类型     | 说明             |
| --------- | ------ | -------------- |
| code      | String | 错误码            |
| message   | String | 错误描述           |
| requestId | String | 本次请求的 requestID |

示例：

    {
        "code":"cce.warning.ClusterNotFound",
        "message":"ClusterID [xxx] belongs to account [xxx] not exists",
        "requestId": "c4abab14-ebc4-4eb0-85d2-03c4d51eed18"
    }

## CCE 集群管理 错误返回码

当错误码前缀为 **cce.error.** 时用户一般需要提交工单进行处理，当错误码前缀为 **cce.warning.** 或 **cce.info.** 时用户可根据错误信息自行处理。

| 错误码                              | 错误描述                                                                                                                                                                    | HTTP状态码 | 中文解释                     |
| -------------------------------- | --------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------- | ------------------------ |
| cce.error.InternalError                    | We encountered an internal error Please try again.                                                                                                                   | 500 Internal Server Error    | 未定义的服务器内部错误。                              |
| cce.warning.AccessDenied                   | Access denied.                                                                                                                                                     | 403 Forbidden                  | 无权限访问对应的资源。                                                                 |
| cce.warning.MalformedJSON                  | The JSON you provided was not well-formed.                                                                                                                         | 400 BadRequest                 | JSON格式不合法。                                                                  |
| cce.warning.InvalidParam                   | Some params are invalid.                                                                                                                                           | 400 BadRequest                 | 输入参数不合法。                                                                  |
| cce.warning.NoSuchObject                   | Target object not found.                                                                                                                                           | 404 NotFound                   | 找不到目标资源。                              |
| cce.warning.ClusterNotFound                | Cluster not found                                                                                                                                                  | 404 NotFound                   | 无法找到该集群。                                                                |
| cce.warning.ClusterNotExposedToPublic      | Cluster not exposed to public                                                                                                                                      | 404 NotFound                   | 集群未开启公网访问。                                                             |
| cce.warning.InstanceAlreadyExists          | Instance already exists                                                                                                                                            | 400 Bad Request                | 节点已经存在。                                                               |
| cce.warning.ExistedInstanceIDDuplicated    | Existed instance ID duplicated                                                                                                                                     | 400 Bad Request                | 与已有的 InstanceID 重复。                                                               |
| cce.warning.InstanceNotFound               | Instance not Found                                                                                                                                                 | 404 NotFound                   | 无法找到该节点。                                                                |
| cce.warning.VPCIDNotExists                 | VPC ID not exists                                                                                                                                                  | 404 NotFound                   | VPC ID 不存在。                                                               |
| cce.warning.ClusterHAInvalid               | ClusterHA invalid                                                                                                                                                  | 400 Bad Request                | 集群的Master节点数不合法。                                                             |
| cce.warning.InstanceGroupNotFound          | Instance group not found                                                                                                                                           | 404 NotFound                   | 无法找到该节点组。                                                          |
| cce.info.RegionNotAvailable                | Region not available                                                                                                                                               | 412 Status Precondition Failed | 指定的地域不可用。                                                           |
| cce.info.BLBNotAvailable                   | BLB not available. You must have at least one bcc, dcc or bbc to create BLB for cluster.                                                                           | 412 Status Precondition Failed | BLB 不可用。VPC 内至少存在一台 BCC DCC 或 BBC 才能建 BLB。               |