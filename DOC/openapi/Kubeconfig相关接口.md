## 查询集群AdminKubeConfig

**描述**

查询集群 Admin KubeConfig

**请求结构**

```
GET /v2/kubeconfig/{clusterID}/admin/{type}  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| clusterID | String | 是 | URL 参数 | 集群 ID |
| type | String | 是 | URL 参数 | Kubeconfig 类型, 可选 [ internal, vpc, public ], 分别表示使用 BLB FloatingIP, 使用 BLB VPCIP, 使用 BLB EIP. |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| kubeConfig | String | 是 | kubeconfig 内容 |
| kubeConfigType | String | 是 | Kubeconfig 类型, 可选 [ internal, vpc, public ], 分别表示使用 BLB FloatingIP, 使用 BLB VPCIP, 使用 BLB EIP. |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
GET /v2/kubeconfig/cce-br0i4kl5/admin/vpc HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: f3da66d2-4c9f-40c9-aa5c-ab5b2d827361
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8


{
	"kubeConfigType": "vpc",
	"kubeConfig": "apiVersion: v1\nclusters:\n- cluster:\n    certificate-authority-data: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURuakNDQW9hZ0F3SUJBZ0lVQit0NWJteWcydjVHMHRFcnVEdTdMVWxQWnpNd0RRWUpLb1pJaHZjTkFRRUwKQlFBd1p6RUxNQWtHQTFVRUJoTUNRMDR4RURBT0JnTlZCQWdUQjBKbGFVcHBibWN4RURBT0JnTlZCQWNUQjBKbAphVXBwYm1jeERqQU1CZ05WQkFvVEJXcHdZV0Z6TVJRd0VnWURWUVFMRXd0amJHOTFaRzVoZEdsMlpURU9NQXdHCkExVUVBeE1GYW5CaFlYTXdIaGNOTWpBd09USTRNRFF5TURBd1doY05NalV3T1RJM01EUXlNREF3V2pCbk1Rc3cKQ1FZRFZRUUdFd0pEVGpFUU1BNEdBMVVFQ0JNSFFtVnBTbWx1WnpFUU1BNEdBMVVFQnhNSFFtVnBTbWx1WnpFTwpNQXdHQTFVRUNoTUZhbkJoWVhNeEZEQVNCZ05WQkFzVEMyTnNiM1ZrYm1GMGFYWmxNUTR3REFZRFZRUURFd1ZxCmNHRmhjekNDQVNJd0RRWUpLb1pJaHZjTkFRRUJCUUFEZ2dFUEFEQ0NBUW9DZ2dFQkFOWGl3SllPL3ZKMEY2RjMKUWZQN05YaEJ2ekJqMEdRdndjbnI2MHQ5WFFON1cwNFdQcUs2QStXZ2g2NE9UbWs4WGx4RDFEenEwSjErSWZHRwpWdXF6K0kwWjhDOE1ZTEtCWWRsNnlVME5Ya2x5dzRVSkJobVpyTnV2QnpTOXBFQjRrU00ra1d5cGpHOTJTdkZaCjhYM2g1SGx4SzM4L0VzVUxzTlBYWi9UV2dWSGNGazRMNG9BZ0R6bVYrZnV5dnpBOU90TnMrWCtOanBlRzV2ancKcFlCdm5ubFIwZ3ZmMnB4cnVuOVhJcFVKYlJnWUdBVFhXdXVRUnNER2dRRlQ0R2taczUvbWZUSzcrSno4ZkpDNgpCSDNoRWRGYUJ4UWg3ck9Udzg2Z2E4czlzYlBXUDFZc2tINTlXYnZpM1F5TmdsNkxQMFRsRDZKbE8wS2Qxa0VBClFpZ2lzQWtDQXdFQUFhTkNNRUF3RGdZRFZSMFBBUUgvQkFRREFnRUdNQThHQTFVZEV3RUIvd1FGTUFNQkFmOHcKSFFZRFZSME9CQllFRk9FMzY5b0ZJTnBxRi94aFV1TG5aV0VrRTh0R01BMEdDU3FHU0liM0RRRUJDd1VBQTRJQgpBUUJFQXhIVWQrZm5wWERyVXovS2VEeXk0MWlySGVSRUFobDNIaTI1U3ZmSTd2V2ROUVFBUDlmQmp6UysrQ0hoCmRHY0dZL3YyQWJCdW5kekJpV2tsbXlsWVgzR2Y0ZDArcTFXLzcyNVFXRFN5MjE0V2QvNHFOVCtJSWZXZTFtRlgKWmhLVlh0SjN2RldrUEIyYUo3Mld2M0htWDhZUzdqNmhtakhDYTIwT0RIOGloTjZ1bVZLbWd6T1FMLy9Keksyago0ZHQ3MVFaZVJ4cVlkRXVyMTdGLzdsUlRuRG9WaEFiT3A0aG5uZGVzMEc0RFBCbFcwZkFKbEVNeHlXNkhMT3dGCmJLcjlRekM3Y0Z0Y2hRNmxsVVVobnhCQW9QbXIra2NQcjFEVTRxQ1JOUVIvNUxxSk5YdWIyWm10NkdlN3BkRU8KSmZmbVFMYUVTYmJjaVNHWkRJQlpxOEY5Ci0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K\n    server: https://*************:6443\n  name: kubernetes\ncontexts:\n- context:\n    cluster: kubernetes\n    user: eca97e148cb74e9683d7b7240829d1ff\n  name: eca97e148cb74e9683d7b7240829d1ff@kubernetes\ncurrent-context: eca97e148cb74e9683d7b7240829d1ff@kubernetes\nkind: Config\npreferences: {}\nusers:\n- name: eca97e148cb74e9683d7b7240829d1ff\n  user:\n    client-certificate-data: 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\n    client-key-data: ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n",
	"requestID": "f3da66d2-4c9f-40c9-aa5c-ab5b2d827361"
}
```