# CCE 组件管理 API

## 获取组件状态

获取组件的基本信息与状态。

如果已经安装，还会返回部署参数等信息。

**请求结构**

```
GET /v2/cluster/{ClusterID}/addon
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称                  | 类型 | 是否必须 | 参数位置    | 描述                                                                                    |
|-----------------------| ---------------------- |------|---------|---------------------------------------------------------------------------------------|
| clusterID | String | 是 | URL参数   | 集群ID。                                                                                 |
| addon                 | String | 否    | Query参数 | 为空时查询所有组件的信息。查询多个组件时按逗号分隔，如cce-ingress-controller,<br/>cce-gpu-controller。取值参考文档末尾附录。 |


**返回头域**

除公共头域，无其它特殊头域。


**返回参数**

| 参数名称      | 类型                                                   | 是否必须 | 描述                       |
|-----------|------------------------------------------------------| -------------------- |--------------------------|
| requestID | String                                               | 是 | 请求ID，问题定位提供该ID。         |
| items     | List&lt;[AddOnInfo](CCE/API_V2参考/附录.md#AddOnInfo)> | 否 | 组件查询结果。每个数组元素是一个组件的查询结果。 |
| code      | String                                               | 否 | 错误代码，仅在请求失败时存在。          |
| message   | String                                               | 否 | 错误信息，仅在请求失败时存在。          |

**请求示例**

```
GET /v2/cluster/cce-5e130ugt/addon?addons=cce-ingress-controller HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: aef503ab-66e2-4b7f-9044-e922389ed03f
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
    "items": [
        {
            "meta": {
                "name": "cce-ingress-controller",
                "type": "Networking",
                "latestVersion": "1.3.5",
                "shortIntroduction": "基于百度云应用型负载均衡产品（应用型BLB）实现K8S Ingress语义，提供七层网络负载均衡能力",
                "defaultParams": "\n# Default values for cce-ingress-controller\n# This is a YAML-formatted file.\n# Declare variables to be passed into your templates.\n\nClusterVersion: v2\nImageID: registry.baidubce.com/cce-plugin-dev/cce-ingress-controller:rc853b4b_20230117\n\nOpenCCEGatewayEndpoint:\nBLBOpenAPIEndpoint:\nEIPOpenAPIEndpoint:\nVPCEndpoint:\nCCEV2Endpoint:\nTagEndpoint:\n\nEIPPurchaseType:\n\nRegion:   # 集群地域\nClusterID: # 集群id\nConcurrentIngressSyncs: # 最多同时处理的 Service 的数量\nDefaultMaxRSCount: # 组件默认为 BLB 挂载的最大后端数\n",
                "installInfo": {
                    "allowInstall": true
                }
            },
            "instance": {
                "name": "cce-ingress-controller",
                "installedVersion": "2022.11.08.1552",
                "params": "# No Params",
                "status": {
                    "phase": "Running"
                },
                "uninstallInfo": {
                    "allowUninstall": true
                },
                "upgradeInfo": {
                    "allowUpgrade": false,
                    "nextVersion": "",
                    "message": "暂不支持升级版本"
                },
                "updateInfo": {
                    "allowUpdate": false,
                    "message": "不支持更新参数"
                }
            }
        }
    ],
    "requestID": "62709f56-3f0b-4fcc-86e0-c18547ba1ce7"
}
```

## 安装组件

向集群中安装组件。

每个组件需要的安装参数不同，部署参数均以字符串的形式传入。

**请求结构**

```
POST /v2/cluster/{ClusterID}/addon 
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述                                          |
|------| ---------------------- |------| ------------------- |---------------------------------------------|
| clusterID | String | 是    | URL参数   | 集群ID。                                       |
| name | String | 是    | Request Body参数 | 要卸载的组件名称 。                                  |
| params | String | 否    | Request Body参数 | 组件安装参数。每个组件需要的参数不同，请参照【附录】部分。部分组件不需要设置安装参数。 |
| version | String | 否    | Request Body参数 | 要指定安装的组件版本。通常不需要设置该参数。                      |

**返回头域**

除公共头域，无其它特殊头域。


**返回参数**

| 参数名称      | 类型 | 是否必须 | 描述               |
|-----------| ---------------------- | -------------------- |------------------|
| requestID | String | 是 | 请求ID，问题定位提供该ID。 |
| code      | String | 否 | 错误代码，仅在请求失败时存在。  |
| message   | String | 否 | 错误信息，仅在请求失败时存在。  |

**请求示例**

```
POST /v2/cluster/cce-5e130ugt/addon HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
    "name": "cce-gpu-manager",
    "params": "EnableHook: true\nEnableSGPU: true\n\n"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 6f593304-6787-45ea-8e0f-426ee331cc8b
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "6f593304-6787-45ea-8e0f-426ee331cc8b"
}
```

## 卸载组件

卸载集群中已经安装的组件。

**请求结构**

```
DELETE /v2/cluster/{ClusterID}/addon 
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述                                               |
|------| ---------------------- |------| ------------------- |--------------------------------------------------|
| clusterID | String | 是    | URL参数   | 集群ID。                                            |
| name | String | 是    | Request Body参数 | 要卸载的组件名称。                                        |
| instanceName | String | 否    | Request Body参数 | 要卸载的组件实例名称。仅在允许多实例部署的组件中，用于指定要卸载的组件实例。通常不会使用到该字段。 |

**返回头域**

除公共头域，无其它特殊头域。


**返回参数**

| 参数名称      | 类型 | 是否必须 | 描述               |
|-----------| ---------------------- | -------------------- |------------------|
| requestID | String | 是 | 请求ID，问题定位提供该ID。 |
| code      | String | 否 | 错误代码，仅在请求失败时存在。  |
| message   | String | 否 | 错误信息，仅在请求失败时存在。  |

**请求示例**

```
DELETE /v2/cluster/cce-5e130ugt/addon HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
    "name": "cce-ingress-controller"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 6f593304-6787-45ea-8e0f-426ee331cc8b
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "6f593304-6787-45ea-8e0f-426ee331cc8b"
}
```

## 更新组件部署参数

更新集群中已经安装的组件的部署参数。

**请求结构**

```
PUT /v2/cluster/{ClusterID}/addon 
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述                                                |
|------| ---------------------- |------| ------------------- |---------------------------------------------------|
| clusterID | String | 是    | URL参数   | 集群ID。                                             |
| name | String | 是    | Request Body参数 | 要更新的组件名称。                                         |
| instanceName | String | 否    | Request Body参数 | 要更新的组件实例名称。仅在允许多实例部署的组件中，用于指定要更新的组件实例。通常不会使用到该字段。 |
| params | String | 是    | Request Body参数 | 要更新的部署参数。                                         |

**返回头域**

除公共头域，无其它特殊头域。


**返回参数**

| 参数名称      | 类型 | 是否必须 | 描述               |
|-----------| ---------------------- | -------------------- |------------------|
| requestID | String | 是 | 请求ID，问题定位提供该ID。 |
| code      | String | 否 | 错误代码，仅在请求失败时存在。  |
| message   | String | 否 | 错误信息，仅在请求失败时存在。  |

**请求示例**

```
PUT /v2/cluster/cce-mehgoi9r/addon HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
    "name": "cce-hybrid-manager",
    "params": "A: a"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 6f593304-6787-45ea-8e0f-426ee331cc8b
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "6f593304-6787-45ea-8e0f-426ee331cc8b"
}
```


## 升级组件

升级集群中已经安装的组件的版本。

**请求结构**

```
POST /v2/cluster/{ClusterID}/addon/upgrade
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述                                                |
|------| ---------------------- |------| ------------------- |---------------------------------------------------|
| clusterID | String | 是    | URL参数   | 集群ID。                                             |
| name | String | 是    | Request Body参数 | 要升级的组件名称。                                         |
| targetVersion | String | 否    | Request Body参数 | 升级的目标版本。通常不会使用到该字段，目标版本由后台自动决定。                   |
| instanceName | String | 否    | Request Body参数 | 要升级的组件实例名称。仅在允许多实例部署的组件中，用于指定要升级的组件实例。通常不会使用到该字段。 |
| params | String | 否    | Request Body参数 | 要在升级时同时更新的参数。通常不会使用到该字段。                          |

**返回头域**

除公共头域，无其它特殊头域。


**返回参数**

| 参数名称      | 类型 | 是否必须 | 描述               |
|-----------| ---------------------- | -------------------- |------------------|
| requestID | String | 是 | 请求ID，问题定位提供该ID。 |
| code      | String | 否 | 错误代码，仅在请求失败时存在。  |
| message   | String | 否 | 错误信息，仅在请求失败时存在。  |

**请求示例**

```
POST /v2/cluster/cce-jx4l7afz/addon/upgrade HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
    "name": "cce-npu-manager"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 6f593304-6787-45ea-8e0f-426ee331cc8b
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"requestID": "6f593304-6787-45ea-8e0f-426ee331cc8b"
}
```


## 组件名称列表

| 组件名称                                  | 参数名称 |
|---------------------------------------| ---------------------- | 
| CCE GPU Manager                       | cce-gpu-manager |
| CCE AI Job Scheduler                  | cce-volcano |
| CCE RDMA Device Plugin                | cce-rdma-plugin |
| CCE PaddleFlow                        | cce-paddleflow |
| CCE Fluid                             | cce-fluid |
| CCE Deep Learning Frameworks Operator | cce-aibox |
| CCE Image Accelerate                  | cce-image-accelerate |
| CCE Hybrid Manager                    | cce-hybrid-manager |
| CCE Ingress NGINX Controller          | cce-ingress-nginx-controller |
| CCE Ingress Controller                | cce-ingress-controller |
| CCE CSI CDS Plugin                    | cce-csi-cds-plugin |
| CCE CSI BOS Plugin                    | cce-csi-bos-plugin |
| CCE CSI PFS Plugin                    | cce-csi-pfs-plugin |
| CCE NPU Manager                       | cce-npu-manager |
| CCE Log Operator                      | cce-log-operator |

## 组件示例参数

### CCE GPU Manager
```yaml
EnableSGPU: false # true 使用内核态隔离，false 使用用户态隔离
GPUShareMemoryUnit: GiB # 显存共享资源上报单位 GiB/MiB
IgnoreDeviceType: false # 是否为不区分卡类型，默认 false 为上报资源中带有卡型号
```

### CCE AI Job Scheduler
```yaml
Binpack: true # Binpack: false 表示启用 spread
Reclaimgang: true # 是否开启队列内抢占
Preemptgang: true # 是否开启队列间抢占
```

### CCE RDMA Device Plugin
该组件不需要用户设置参数

### CCE PaddleFlow 
```yaml
global:
  PF_SELFED_DB_ENABLED: &selfd_mysql true
  PF_DB_DATABASE: &pf_db_database paddleflow
  PF_DB_HOST: &pf_db_host mysql-standalone
  PF_DB_PASSWORD: &mysqlpwd Paddle@2022
  PF_DB_USER: &pf_db_user root
  PF_DB_PORT: &pf_db_port 3306
  PF_SVC_TYPE: &pf_svc_type NodePort
  PF_SERVER_NODE_PORT: &pf_server_node_port 30999
  PF_LB_IP: &eip "0.0.0.0"
  CCE_LB_ID: "lb-12345678"
  CCE_LB_SBN_ID: "sbn-123456789123"
  CCE_LB_INTERNAL: true
  CCE_LB_POD_DERICT: false
  IMAGE_REPOSITORY: registry.baidubce.com/cce-plugin-dev/paddleflow

paddleflow-server:
  out_depend_msg:
    PF_DB_DATABASE: *pf_db_database
    PF_DB_HOST: *pf_db_host
    PF_DB_PASSWORD: *mysqlpwd
    PF_DB_PORT: *pf_db_port
    PF_DB_USER: *pf_db_user
  paddleflow_server:
    service:
      extra_usr_define_services:
        paddleflow-server:
          ports:
            port-0:
              nodePort: *pf_server_node_port
          loadBalancerIP: *eip
          type: *pf_svc_type


paddleflow-db-init:
  out_depend_msg:
    PF_DB_DATABASE: *pf_db_database
    PF_DB_HOST: *pf_db_host
    PF_DB_PASSWORD: *mysqlpwd
    PF_DB_PORT: *pf_db_port
    PF_DB_USER: *pf_db_user


mysql-replication:
  enabled: *selfd_mysql
  fullnameOverride: *pf_db_host
  nameOverride: *pf_db_host
  auth:
    rootPassword: *mysqlpwd
    database: *pf_db_database
    username: *pf_db_user
    password: *mysqlpwd
  primary:
    service:
      port: *pf_db_port

```

### CCE Fluid
该组件不需要用户设置参数

### CCE Deep Learning Frameworks Operator
```yaml
TFOperatorEnable: false
MPIOperatorEnable: false
PyTorchOperatorEnable: false
PaddleOperatorEnable: false
MXNetOperatorEnable: false
AITrainingOperatorEnable: false
```

### CCE Image Accelerate
该组件不需要用户设置参数

### CCE Hybrid Manager 
```yaml
version:
  scheduler: 1.2.8
  hybridlet: 1.2.8

globalSLA:
  cpu:
    highPercent: 65
    bestEffortMaxCores: 60
  memory:
    highPercent: 80
    bestEffortMax: 60
  net:
    inHigh: 10000
    bestEffortInHigh: 10000
    outHigh: 10000
    bestEffortOutHigh: 10000
  coolDownSec: 10
  expulsionDelaySec: 60
  maxInstances: 256
```

### CCE Ingress NGINX Controller
```yaml
controller:
  ingressClass: ads
  kind: DaemonSet
  nodeSelector:
    instance-group-id: cce-ig-ubn1700b
  resources:
    limits:
      cpu: 0.5
      memory: 1024Mi
    requests:
      cpu: 0.25
      memory: 256Mi
  scope:
    enabled: false
    namespace: ""
  service:
    annotations:
      service.beta.kubernetes.io/cce-load-balancer-internal-vpc: true
  tolerations: []
fullnameOverride: ads-ngx-control
isArchArm64: false

```

### CCE Ingress Controller
该组件不需要用户设置参数

### CCE CSI CDS Plugin 
```yaml
maxVolumesPerNode: 5 # maxVolumesPerNode 集群中每个节点最大可以挂载的 CDS PV 数量
cluster:             # kubeletRootPath 用户节点 Kubelet 数据目录。要将集群所有节点出现过的数据目录都列在这里。如果节点没有特别修改过 kubelet 数据目录，这里可以不传。
  nodes:
    - kubeletRootPath: "/home/<USER>/kubelet"
      kubeletRootPathAffinity: true
    - kubeletRootPath: "/data/kubelet"
      kubeletRootPathAffinity: true
    - kubeletRootPath: "/var/lib/kubelet"
      kubeletRootPathAffinity: true
```

### CCE CSI BOS Plugin 
```yaml
maxVolumesPerNode: 5 # maxVolumesPerNode 集群中每个节点最大可以挂载的 BOS PV 数量
cluster:             # kubeletRootPath 用户节点 Kubelet 数据目录。要将集群所有节点出现过的数据目录都列在这里。如果节点没有特别修改过 kubelet 数据目录，这里可以不传。
  nodes: 
  - kubeletRootPath: "/home/<USER>/kubelet"
    kubeletRootPathAffinity: true
  - kubeletRootPath: "/data/kubelet"
    kubeletRootPathAffinity: true
  - kubeletRootPath: "/var/lib/kubelet"
    kubeletRootPathAffinity: true
```

### CCE CSI PFS Plugin 
```yaml
pfs:
  configEndpoint: ""     # configEndpoint PFS 连接地址和端口
  cluster: pfs           # cluster 动态创建的PV在parentDir下的子路径。例如对于多个不同的CCE集群挂载同一个PFS实例的场景，可以在不同CCE集群中安装时指定为不同的值，这样各集群动态创建的PV就在不同的目录下。
  parentDir: /kubernetes # parentDir 代表CSI有权限读写的子路径，需要使用静态PV挂载的路径必须在这个路径之内。最大可以填 /，默认为 /kubernetes。挂载前需确认该路径已经对CCE集群机器授权。
nodes:                   # kubeletRootPath 用户节点 Kubelet 数据目录。要将集群所有节点出现过的数据目录都列在这里。如果节点没有特别修改过 kubelet 数据目录，这里可以不传。
  - kubeletRootPath: "/var/lib/kubelet"
    kubeletRootPathAffinity: true
  - kubeletRootPath: "/home/<USER>/kubelet"
    kubeletRootPathAffinity: true
  - kubeletRootPath: "/data/kubelet"
    kubeletRootPathAffinity: true
```


### CCE NPU Manager
```yaml
XPUUseSriov: false # 开启则使用硬件隔离切分，不支持虚机
XPUSriovNumVfs: 3  # 指定昆仑硬件切分的份数
```

### CCE Log Operator
该组件不需要用户设置参数