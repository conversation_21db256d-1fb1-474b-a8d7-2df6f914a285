## 创建集群

**描述**

创建 CCE K8S 集群

**请求结构**

```
POST /v2/cluster HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| cluster | [ClusterSpec](CCE/API_V2参考/附录.md#ClusterSpec) | 是 | RequestBody 参数 | 集群配置 |
| masters | List&lt;[InstanceSet](CCE/API_V2参考/附录.md#InstanceSet)> | 否 | RequestBody 参数 | 集群 Master 配置, 支持异构组合. 当集群类型为托管型时无需设置该参数 |
| nodes | List&lt;[InstanceSet](CCE/API_V2参考/附录.md#InstanceSet)> | 否 | RequestBody 参数 | 集群 Worker 配置, 支持异构组合. 不设置此参数时将会创建仅有master的集群 |
| options | [CreateClusterOptions](CCE/API_V2参考/附录.md#CreateClusterOptions) | 是 | RequestBody 参数 | 集群创建选项. 用户可以设置强制跳过网段冲突检查 |

**返回头域**

除公共头域，无其它特殊头域。
 
**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| clusterID | String | 是 | 集群 ID |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例: 托管 Master 集群**

```
POST /v2/cluster  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
    "cluster":{
        "clusterName":"test-open-types",
        "k8sVersion":"1.16.8",
        "runtimeType":"docker",
        "vpcID":"vpc-mwbgygrjb72w",
        "masterConfig":{
            "masterType":"managed",
            "clusterHA":1,
            "exposedPublic":false,
            "clusterBLBVPCSubnetID":"sbn-mnbvhnuupv1u",
            "managedClusterMasterOption":{
                "masterVPCSubnetZone":"zoneA"
            }
        },
			"containerNetworkConfig": {
				"mode": "kubenet",
				"lbServiceVPCSubnetID": "sbn-mnbvhnuupv1u",
				"clusterPodCIDR": "**********/16",
				"clusterIPServiceCIDR": "**********/16"
			},
			"k8sCustomConfig": {
				"kubeAPIQPS": 1000,
				"kubeAPIBurst": 2000
			}
    },
    "nodes":[
        {
            "instanceSpec":{
                "instanceName":"instance-name",
                "clusterRole":"node",
                "existed":false,
                "machineType":"BCC",
                "instanceType":"N3",
                "vpcConfig":{
                    "vpcID":"vpc-mwbgygrjb72w",
                    "vpcSubnetID":"sbn-mnbvhnuupv1u",
                    "securityGroupID":"g-xh04bcdkq5n6",
                    "availableZone":"zoneA"
                },
                "instanceResource":{
                    "cpu":4,
                    "mem":8,
                    "rootDiskSize":40,
                    "localDiskSize":0,
                    "cdsList":[]
                },
                "imageID":"m-gTpZ1k6n",
                "instanceOS":{
                    "imageType":"System",
                    "osType":"linux",
                    "osName":"CentOS",
                    "osVersion":"7.5",
                    "osArch":"x86_64 (64bit)"
                },
                "needEIP":false,
                "adminPassword":"test123!T",
                "instanceChargingType":"Postpaid",
                "runtimeType":"docker"
            },
            "count":1
        }
    ]
}
```

**请求示例: 自定义 Master 集群**

```
{
    "cluster":{
        "clusterName":"create-custom-cluster-reg",
        "description":"集群描述",
        "k8sVersion":"1.16.8",
        "runtimeType":"docker",
        "vpcID":"vpc-43zsdm46t9rp",
        "masterConfig":{
            "masterType":"custom",
            "exposedPublic":true,
            "clusterBLBVPCSubnetID":"sbn-vvqsb9b57f24"
        },
        "containerNetworkConfig":{
            "mode":"kubenet",
            "lbServiceVPCSubnetID":"sbn-vvqsb9b57f24",
            "nodePortRangeMin":50000,
            "nodePortRangeMax":51000,
            "clusterPodCIDR":"********/16",
            "clusterIPServiceCIDR":"**********/16",
            "maxPodsPerNode":64,
            "kubeProxyMode":"ipvs"
        }
    },
    "masters":[
        {
            "instanceSpec":{
                "machineType":"BCC",
                "instanceType":"N3",
                "vpcConfig":{
                    "vpcSubnetID":"sbn-vvqsb9b57f24",
                    "securityGroupID":"g-k4tsm0id2g1n"
                },
                "instanceResource":{
                    "CPU":4,
                    "MEM":8
                },
                "instanceOS": {
                    "imageName": "7.5 x86_64 (64bit)",
                    "imageType": "System",
                    "osType": "linux",
                    "osName": "CentOS",
                    "osVersion": "7.5",
                    "osArch": "x86_64 (64bit)"
                },
                "adminPassword":"test123!T"
            },
            "count":1
        }
    ],
    "nodes":[
        {
            "instanceSpec":{
                "machineType":"BCC",
                "instanceType":"N3",
                "vpcConfig":{
                    "vpcSubnetID":"sbn-vvqsb9b57f24",
                    "securityGroupID":"g-k4tsm0id2g1n"
                },
                "instanceResource":{
                    "CPU":4,
                    "MEM":8,
                    "cdsList":[
                        {
                            "diskPath":"/data",
                            "storageType":"cloud_hp1",
                            "cdsSize":200
                        }
                    ]
                },
                "instanceOS": {
                    "imageName": "7.5 x86_64 (64bit)",
                    "imageType": "System",
                    "osType": "linux",
                    "osName": "CentOS",
                    "osVersion": "7.5",
                    "osArch": "x86_64 (64bit)"
                },
                "adminPassword":"test123!T"
            },
            "count":1
        }
    ]
}
```

**请求示例: 已有实例**

```
{
    "cluster":{
        "clusterName":"create-existed-bcc-cluster-reg",
        "description":"集群描述",
        "k8sVersion":"1.16.8",
        "runtimeType":"docker",
        "vpcID":"vpc-43zsdm46t9rp",
        "masterConfig":{
            "masterType":"custom",
            "exposedPublic":true,
            "clusterBLBVPCSubnetID":"sbn-vvqsb9b57f24"
        },
        "containerNetworkConfig":{
            "mode":"kubenet",
            "lbServiceVPCSubnetID":"sbn-vvqsb9b57f24",
            "nodePortRangeMin":30000,
            "nodePortRangeMax":32768,
            "clusterPodCIDR":"10.0.0.0/16",
            "clusterIPServiceCIDR":"**********/16",
            "maxPodsPerNode":256,
            "kubeProxyMode":"ipvs"
        }
    },
    "masters":[
        {
            "instanceSpec":{
                "existed":true,
                "existedOption":{
                    "existedInstanceID":"i-SxeBLkcN",
                    "rebuild":true
                },
                "machineType":"BCC",
                "instanceOS": {
                    "imageName": "7.5 x86_64 (64bit)",
                    "imageType": "System",
                    "osType": "linux",
                    "osName": "CentOS",
                    "osVersion": "7.5",
                    "osArch": "x86_64 (64bit)"
                },
                "adminPassword":"test123!T"
            }
        }
    ],
    "nodes":[
        {
            "instanceSpec":{
                "existed":true,
                "existedOption":{
                    "existedInstanceID":"i-M56Un1DO",
                    "rebuild":true
                },
                "machineType":"BCC",
                "instanceOS": {
                    "imageName": "7.5 x86_64 (64bit)",
                    "imageType": "System",
                    "osType": "linux",
                    "osName": "CentOS",
                    "osVersion": "7.5",
                    "osArch": "x86_64 (64bit)"
                },
                "adminPassword":"test123!T"
            }
        }
    ]
}
```

**返回示例**

```
HTTP/1.1 200 OK
x-bce-request-id: d2ce8f50-529a-4663-9265-ad08c94633c8
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8
{
    "clusterID": "cce-NqYwWEhu",
    "requestID": "d2ce8f50-529a-4663-9265-ad08c94633c8"
}
```


## 集群列表

**描述**

查询用户 CCE K8S 集群列表

**请求结构**

```
GET /v2/clusters  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- | 
| keywordType | String | 否 | Query 参数 | 集群模糊查询字段，可选 [ clusterName, clusterID ]，默认值为 clusterName |
| keyword | String | 否 | Query 参数 | 查询关键词，默认值为空字符串 |
| orderBy | String | 否 | Query 参数 | 集群查询排序字段，可选 [ clusterName, clusterID, createdAt ]，默认值为 clusterName |
| order | String | 否 | Query 参数 | 排序方式，可选 [ ASC, DESC ], ASC 为升序，DESC 为降序，默认值为 ASC |
| pageNo | Integer | 否 | Query 参数 | 页码，默认值为1 |
| pageSize | Integer | 否 | Query 参数 | 单页结果数，默认值为10 |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 描述 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| clusterPage | [ClusterPage](CCE/API_V2参考/附录.md#ClusterPage) | 是 | 集群分页查询返回结果 |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
GET /v2/clusters?keywordType=clusterName&keyword=&orderBy=clusterID&order=ASC&pageNo=1&pageSize=10  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 97342dc7-29a1-4ed9-a75d-904bb293d295
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"clusterPage": {
		"keywordType": "clusterName",
		"keyword": "",
		"orderBy": "clusterID",
		"order": "ASC",
		"pageNo": 1,
		"pageSize": 10,
		"totalCount": 1,
		"clusterList": [
			{
				"spec": {
					"clusterID": "cce-shpdaa9l",
					"clusterName": "sdk-ccev2-test",
					"clusterType": "normal",
					"description": "",
					"k8sVersion": "1.16.8",
					"vpcID": "vpc-aj2rcjm084y5",
					"vpcCIDR": "***********/16",
					"plugins": [
						"ip-masq-agent",
						"core-dns",
						"kube-proxy",
						"metrics-server",
						"nvidia-gpu"
					],
					"masterConfig": {
						"masterType": "managed",
						"clusterHA": 1,
						"clusterBLBVPCSubnetID": "sbn-0dizryuc81c0",
						"managedClusterMasterOption": {
							"masterVPCSubnetZone": "zoneA"
						}
					},
					"containerNetworkConfig": {
						"mode": "kubenet",
						"ipVersion": "ipv4",
						"lbServiceVPCSubnetID": "sbn-0dizryuc81c0",
						"nodePortRangeMin": 30000,
						"nodePortRangeMax": 32767,
						"clusterPodCIDR": "**********/16",
						"clusterIPServiceCIDR": "**********/16",
						"maxPodsPerNode": 128,
						"kubeProxyMode": "ipvs"
					}
				},
				"status": {
					"clusterBLB": {
						"id": "lb-1454d5c4",
						"vpcIP": "*************",
						"eip": ""
					},
					"clusterPhase": "running",
					"nodeNum": 0
				},
				"createdAt": "2020-09-04T01:58:12Z",
				"updatedAt": "2020-09-04T02:00:37Z"
			}
		]
	},
	"requestID": "97342dc7-29a1-4ed9-a75d-904bb293d295"
}
```

## 集群详情

**描述**

查询指定集群详情

**请求结构**

```
GET /v2/cluster/{clusterID} HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| clusterID | String | 是 | URL 参数 | 集群 ID |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| cluster | [Cluster](CCE/API_V2参考/附录.md#Cluster) | 是 | 集群详情查询结果 |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
GET /v2/cluster/cce-zyt88sqy  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**
```
HTTP/1.1 200 OK
X-Bce-Request-Id: 928a21b5-d117-4a83-a274-fd7d6f413524
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"cluster": {
		"spec": {
			"clusterID": "cce-shpdaa9l",
			"clusterName": "sdk-ccev2-test",
			"clusterType": "normal",
			"description": "",
			"k8sVersion": "1.16.8",
			"vpcID": "vpc-aj2rcjm084y5",
			"vpcCIDR": "***********/16",
			"plugins": [
				"ip-masq-agent",
				"core-dns",
				"kube-proxy",
				"metrics-server",
				"nvidia-gpu"
			],
			"masterConfig": {
				"masterType": "managed",
				"clusterHA": 1,
				"clusterBLBVPCSubnetID": "sbn-0dizryuc81c0",
				"managedClusterMasterOption": {
					"masterVPCSubnetZone": "zoneA"
				}
			},
			"containerNetworkConfig": {
				"mode": "kubenet",
				"ipVersion": "ipv4",
				"lbServiceVPCSubnetID": "sbn-0dizryuc81c0",
				"nodePortRangeMin": 30000,
				"nodePortRangeMax": 32767,
				"clusterPodCIDR": "**********/16",
				"clusterIPServiceCIDR": "**********/16",
				"maxPodsPerNode": 128,
				"kubeProxyMode": "ipvs"
			}
		},
		"status": {
			"clusterBLB": {
				"id": "lb-1454d5c4",
				"vpcIP": "*************",
				"eip": ""
			},
			"clusterPhase": "running",
			"nodeNum": 0
		},
		"createdAt": "2020-09-04T01:58:12Z",
		"updatedAt": "2020-09-04T02:00:37Z"
	},
	"requestID": "928a21b5-d117-4a83-a274-fd7d6f413524"
}
```


## 删除集群

**描述**

删除指定集群

**请求结构**

```
DELETE /v2/cluster/{clusterID} HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| clusterID | String | 是 | URL 参数 | 集群 ID |
| deleteResource | Boolean | 否 | Query 参数 | 是否删除相关资源（后付费公网IP和云磁盘），默认值为 false |
| deleteCDSSnapshot | Boolean | 否 | Query 参数 | 是否删除云磁盘快照，默认值为 false |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
DELETE /v2/cluster/cce-uqc4lju1?deleteResource=true&deleteCDSSnapshot=true  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 105ce04b-1a42-4f77-9d22-ab6f413f9d69
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
    "requestID": "105ce04b-1a42-4f77-9d22-ab6f413f9d69"
}
```

