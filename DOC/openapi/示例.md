## 集群管理示例

### 集群相关

#### 使用已有 BEC 节点创建纯边集群
在本例中，我们首先创建 5 个广州区域，中国移动线路的 5 台 BEC服务器

如果希望在部署时执行脚本：
- nodes[n].instanceSpec.deployCustomConfig.preUserScript 部署前执行脚本。内容是 BASE64 编码的指令。例如：本例希望执行 "ls;"，其 BASE64 编码为 "bHM7"，字段值填"bHM7"。
- nodes[n].instanceSpec.deployCustomConfig.postUserScript 部署后执行脚本。

```
{
	"cluster": {
		"clusterName": "jichao",
		"containerNetworkConfig": {
			"mode": "vpc-route-auto-detect",
			"nodePortRangeMax": 32768,
			"nodePortRangeMin": 30000,
			"clusterIPServiceCIDR": "**********/16",
			"clusterIPServiceCIDRIPv6": "",
			"kubeProxyMode": "ipvs",
			"clusterPodCIDR": "10.0.0.0/8",
			"ipVersion": "ipv4",
			"maxPodsPerNode": 256,
			"enableNodeLocalDNS": false,
			"nodeLocalDNSAddr": "*************"
		},
		"k8sVersion": "1.18.9",
		"authenticateMode": "x509",
		"masterConfig": {
			"masterType": "containerizedEdge",
			"exposedPublic": true,
            "edgeMasterOption": {
                "region": "SOUTH_CHINA", 
                "city": "GUANGZHOU", 
                "serviceProvider": "CHINA_MOBILE", 
                "masterLBBandwidthInMbpsLimit": 100 
              }
		},
		"k8sCustomConfig": {
			"etcdDataPath": "/home/<USER>/etcd"
		},
		"clusterType": "edge"
	},
	"nodes": [
		{
			"instanceSpec": {
				"adminPassword": "140311jc!",
				"existed": true,
				"existedOption": {
					"existedInstanceID": "vm-6wlfqsxb-3-m-guangzhou-xi9ni",
					"rebuild": false
				},
				"machineType": "BEC",
				"clusterRole": "node",
				"deployCustomConfig": {
					"dockerConfig": {
						"dockerDataRoot": "/home/<USER>/docker"
					},
					"kubeletRootDir": "/home/<USER>/kubelet",
					"preUserScript": "bHM7",
					"postUserScript": "",
					"enableCordon": false,
					"kubeReserved": {
						"cpu": "50m",
						"memory": "100Mi"
					},
					"systemReserved": {
						"cpu": "50m",
						"memory": "100Mi"
					}
				},
				"instanceResource": {
					"cdsList": []
				},
				"vpcConfig": {
					"securityGroup": {}
				},
				"labels": {
					"cce.baidubce.com/gpu-share-device-plugin": "disable"
				},
				"tags": [],
				"taints": [],
				"becOption": {
					"becRegion": "SOUTH_CHINA",
					"becCity": "GUANGZHOU",
					"becServiceProvider": "CHINA_MOBILE"
				}
			}
		},
		{
			"instanceSpec": {
				"adminPassword": "140311jc!",
				"existed": true,
				"existedOption": {
					"existedInstanceID": "vm-6wlfqsxb-3-m-guangzhou-esjc1",
					"rebuild": false
				},
				"machineType": "BEC",
				"clusterRole": "node",
				"deployCustomConfig": {
					"dockerConfig": {
						"dockerDataRoot": "/home/<USER>/docker"
					},
					"kubeletRootDir": "/home/<USER>/kubelet",
					"preUserScript": "bHM7",
					"postUserScript": "",
					"enableCordon": false,
					"kubeReserved": {
						"cpu": "50m",
						"memory": "100Mi"
					},
					"systemReserved": {
						"cpu": "50m",
						"memory": "100Mi"
					}
				},
				"instanceResource": {
					"cdsList": []
				},
				"vpcConfig": {
					"securityGroup": {}
				},
				"labels": {
					"cce.baidubce.com/gpu-share-device-plugin": "disable"
				},
				"tags": [],
				"taints": [],
				"becOption": {
					"becRegion": "SOUTH_CHINA",
					"becCity": "GUANGZHOU",
					"becServiceProvider": "CHINA_MOBILE"
				}
			}
		}
	],
	"options": {
		"skipNetworkCheck": true
	},
	"masters": [
        {
            "instanceSpec": {
              "machineType": "BEC",
              "existed": true,
              "existedOption": {
                "existedInstanceID": "vm-6wlfqsxb-3-m-guangzhou-edkhk" 
              },
              "adminPassword": "140311jc!"
            }
          },
          {
            "instanceSpec": {
              "machineType": "BEC",
              "existed": true,
              "existedOption": {
                "existedInstanceID": "vm-6wlfqsxb-3-m-guangzhou-xtayl"
              },
              "adminPassword": "140311jc!"
            }
          },
          {
            "instanceSpec": {
              "machineType": "BEC",
              "existed": true,
              "existedOption": {
                "existedInstanceID": "vm-6wlfqsxb-3-m-guangzhou-byh1g"
              },
              "adminPassword": "140311jc!"
            }
          }
      
	]
}
```