## API认证机制

所有API的安全认证一律采用Access Key与请求签名机制。 Access Key由Access Key ID和Secret Access Key组成，均为字符串。 对于每个HTTP请求，使用下面所描述的算法生成一个认证字符串。提交认证字符串放在Authorization头域里。服务端根据生成算法验证认证字符串的正确性。 认证字符串的格式为`bce-auth-v{version}/{accessKeyId}/{timestamp}/{expirationPeriodInSeconds}/{signedHeaders}/{signature}`。

-   version是正整数。
-   timestamp是生成签名时的UTC时间。
-   expirationPeriodInSeconds表示签名有效期限。
-   signedHeaders是签名算法中涉及到的头域列表。头域名之间用分号（;）分隔，如host;x-bce-date。列表按照字典序排列。（本API签名仅使用host和x-bce-date两个header）
-   signature是256位签名的十六进制表示，由64个小写字母组成。

当百度智能云接收到用户的请求后，系统将使用相同的SK和同样的认证机制生成认证字符串，并与用户请求中包含的认证字符串进行比对。如果认证字符串相同，系统认为用户拥有指定的操作权限，并执行相关操作；如果认证字符串不同，系统将忽略该操作并返回错误码。鉴权认证机制的详细内容请参见[鉴权认证机制](Reference/鉴权认证机制/简介.md)。

## 密码加密传输规范定义

所有涉及密码的接口参数都需要加密，禁止明文传输。密码一律采用AES 128位加密算法进行加密，用SK的前16位作为密钥，加密后生成的二进制字节流需要转成十六进制，并以字符串的形式传到服务端。具体步骤如下：

-   byte\[] bCiphertext= AES(明文,SK)
-   String strHex = HexStr(bCiphertext)

## 排版约定

| 排版格式            | 含义   |
| --------------- | ---- |
| &lt; >          | 变量   |
| [ ]             | 可选项  |
| { }             | 必选项  |
| &#124;　　　　　　|  互斥关系   |      
| 等宽字体Courier New | 屏幕输出 |

## 请求参数

数据交换格式为JSON，所有request/response body内容均采用UTF-8编码。

请求参数包括如下4种：

| 参数类型        | 说明                                         |
| ----------- | ------------------------------------------ |
| URL 参数        | 通常用于指明操作实体，如:PUT /v2/cluster/{clusterID} |
| Query 参数     | URL中携带的请求参数                                |
| HEADER      | 通过HTTP头域传入，如:x-bce-date                    |
| RequestBody 参数 | 通过JSON格式组织的请求数据体                           |

## 返回值说明

返回值分为两种形式：

| 返回内容             | 说明                |
| ---------------- | ----------------- |
| HTTP STATUS CODE | 如200,400,403,404等 |
| ResponseBody     | JSON格式组织的响应数据体    |

## API版本号

| 参数      | 类型     | 参数位置  | 描述                 | 是否必须 |
| ------- | ------ | ----- | ------------------ | ---- |
| version | String | URL参数 | API版本号，当前 API 版本v2 | 是 |
