# 普通 BCC

```json
"instanceOS":{
  "imageType":"System",
  "imageName":"6.3 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"6.3",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"7.1 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.1",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"7.2 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.2",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"7.3 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.3",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"7.4 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.4",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"7.5 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.5",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"7.6 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.6",
  "osArch":"x86_64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"8.0 x86_64 (64bit)",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"8.0",
  "osArch":"x86_64 (64bit)"
}

"instanceOS":{
  "imageType":"System",
  "imageName":"16.04 LTS amd64 (64bit)",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)"
}
"instanceOS":{
  "imageType":"System",
  "imageName":"18.04 LTS amd64 (64bit)",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"18.04 LTS",
  "osArch":"amd64 (64bit)"
}
```

# 内部上云
```json
"instanceOS":{
  "imageType":"System",
  "imageName":"7.5 x86_64 (64bit) 内部上云版",
  "osType":"linux",
  "osName":"CentOS",
  "osVersion":"7.5",
  "osArch":"x86_64 (64bit)"
}
```

# GPU

```json
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA8.0 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA8.0"
}
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA9.0-framework-integration 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA9.0-framework-integration"
}
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA9.0 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA9.0"
}
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA9.1 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA9.1"
}
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA9.2 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA9.2"
}
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA10.0 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA10.0"
}
"instanceOS":{
  "imageType":"GpuBccSystem",
  "imageName":"16.04 LTS amd64 (64bit)-CUDA10.1 集成GPU驱动版",
  "osType":"linux",
  "osName":"Ubuntu",
  "osVersion":"16.04 LTS",
  "osArch":"amd64 (64bit)-CUDA10.1"
}
```

# BBC