## 检查集群ClusterIP网段

**描述**

检查集群 ClusterIP 网段

**请求结构**

```
POST /v2/net/check_clusterip_cidr  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| ipVersion | String | 是 | RequestBody 参数 | 容器网络IP地址版本，可选[ipv4, ipv6, dualStack] |
| clusterIPCIDR | String | 否 | RequestBody 参数 | Cluster IPv4 网段. IP版本为IPv4时必填 |
| clusterIPCIDRIPv6 | String | 否 | RequestBody 参数 | Cluster IPv6 网段. IP版本为IPv6时必填 |
| vpcID | String | 是 | RequestBody 参数 | VPC ID |
| vpcCIDR | String | 否 | RequestBody 参数 | VPC IPv4 网段. IP版本为IPv4时必填 |
| vpcCIDRIPv6 | String | 否 | RequestBody 参数 | VPC IPv6 网段. IP版本为IPv6时必填 |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| errMsg | String | 否 | 错误信息 |
| isConflict | String | 是 | 是否冲突 |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
POST /v2/net/check_clusterip_cidr  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
	"vpcID": "vpc-pi9fghaxcpnf",
	"vpcCIDR": "***********/16",
	"vpcCIDRIPv6": "",
	"clusterIPCIDR": "**********/16",
	"clusterIPCIDRIPv6": "",
	"ipVersion": "ipv4"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: d1b66c3d-b16f-4ff2-bedf-af21f6bcd827
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"isConflict": false,
	"errMsg": "",
	"requestID": "40165eb4-0fc7-405e-bcdf-10ba2ffc1ee3"
}
```


## 检查集群容器网络网段

**描述**

检查集群容器网络网段

**请求结构**

```
POST /v2/net/check_container_network_cidr  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| ipVersion | String | 是 | RequestBody 参数 | 容器网络IP地址版本，可选 [ ipv4, ipv6, dualStack ] |
| clusterIPCIDR | String | 否 | RequestBody 参数 | Cluster IPv4 网段. IP 版本为 IPv4 时必填 |
| clusterIPCIDRIPv6 | String | 否 | RequestBody 参数 | Cluster IPv6 网段. IP 版本为 IPv6 时必填 |
| containerCIDR | String | 否 | RequestBody 参数 | 容器网络 IPv4 网段. IP 版本为 IPv4 时必填 |
| containerCIDRIPv6 | String | 否 | RequestBody 参数 | 容器网络 IPv6 网段. IP 版本为 IPv6 时必填 |
| maxPodsPerNode | Integer | 是 | RequestBody 参数 | 单节点最大容器组数量 |
| vpcID | String | 是 | RequestBody 参数 | VPC ID |
| vpcCIDR | String | 否 | RequestBody 参数 | VPC IPv4 网段. IP版本为IPv4时必填 |
| vpcCIDRIPv6 | String | 否 | RequestBody 参数 | VPC IPv6 网段. IP版本为IPv6时必填 |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| clusterIPCIDRConflict | [ClusterIPCIDRConflict](CCE/API_V2参考/附录.md#ClusterIPCIDRConflict) | 否 | ClusterIP 网段冲突信息 |
| containerCIDRConflict | [ContainerCIDRConflict](CCE/API_V2参考/附录.md#ContainerCIDRConflict) | 否 | 容器网段冲突信息 |
| errMsg | String | 否 | 错误信息 |
| isConflict | String | 是 | 是否冲突 |
| maxNodeNum | String | 是 | 最大节点数量 |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
POST /v2/net/check_container_network_cidr HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
	"vpcID": "vpc-pi9fghaxcpnf",
	"vpcCIDR": "***********/16",
	"vpcCIDRIPv6": "",
	"containerCIDR": "**********/16",
	"containerCIDRIPv6": "",
	"clusterIPCIDR": "**********/16",
	"clusterIPCIDRIPv6": "",
	"maxPodsPerNode": 256,
	"ipVersion": "ipv4"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: a2c5b12b-6005-4266-a8e8-f5b3d903a5c7
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"maxNodeNum": 256,
	"isConflict": false,
	"errMsg": "",
	"containerCIDRConflict": null,
	"clusterIPCIDRConflict": null,
	"requestID": "a2c5b12b-6005-4266-a8e8-f5b3d903a5c7"
}
```


## 推荐ClusterIP网络网段

**描述**

推荐 ClusterIP 网络网段

**请求结构**

```
POST /v2/net/recommend_clusterip_cidr  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| clusterMaxNodeNum | Integer | 是 | RequestBody 参数 | 集群最大节点数 |
| ipVersion | String | 是 | RequestBody 参数 | 容器网络IP地址版本，可选 [ ipv4, ipv6, dualStack ] |
| containerCIDR | String | 否 | RequestBody 参数 | 容器网络 IPv4 网段. IP 版本为 IPv4 时必填 |
| containerCIDRIPv6 | String | 否 | RequestBody 参数 | 容器网络 IPv6 网段. IP 版本为 IPv6 时必填 |
| privateNetCIDRs | List&lt;String> | 否 | RequestBody 参数 | IPv4私有网络地址段，可选 [ 10.0.0.0/8, **********/12, ***********/16 ] |
| privateNetCIDRIPv6s | List&lt;String> | 否 | RequestBody 参数 | IPv6私有网络地址段，目前仅支持 [ fd00::/8 ] |
| vpcCIDR | String | 否 | RequestBody 参数 | VPC IPv4 网段. IP版本为IPv4时必填 |
| vpcCIDRIPv6 | String | 否 | RequestBody 参数 | VPC IPv6 网段. IP版本为IPv6时必填 |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| errMsg | String | 否 | 错误信息 |
| isSuccess | Boolean | 是 | 请求是否成功 |
| recommendedClusterIPCIDRs | List&lt;String> | 否 | 推荐 Cluster IP 网段 |
| recommendedClusterIPCIDRIPv6s | List&lt;String> | 否 | 推荐 Cluster IP 网段 IPv6 |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
POST /v2/net/recommend_clusterip_cidr HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
	"vpcCIDR": "***********/16",
	"vpcCIDRIPv6": "",
	"containerCIDR": "**********/16",
	"containerCIDRIPv6": "",
	"clusterMaxServiceNum": 8,
	"privateNetCIDRs": [
		"**********/12"
	],
	"privateNetCIDRIPv6s": null,
	"ipVersion": "ipv4"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 79f1993e-fc76-41c3-8f20-a22bd1010324
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"recommendedClusterIPCIDRs": [
		"**************/29",
		"**************/29",
		"**************/29",
		"**************/29",
		"**************/29"
	],
	"recommendedClusterIPCIDRIPv6s": null,
	"isSuccess": true,
	"errMsg": "",
	"requestID": "79f1993e-fc76-41c3-8f20-a22bd1010324"
}
```


## 推荐容器网络网段

**描述**

推荐容器网络网段

**请求结构**

```
POST /v2/net/recommend_container_cidr  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: authorization string
```

**请求头域**

除公共头域外，无其它特殊头域。

**请求参数**

| 参数名称 | 类型 | 是否必须 | 参数位置 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | -------------------- |
| clusterMaxNodeNum | Integer | 是 | RequestBody 参数 | 集群最大节点数 |
| ipVersion | String | 是 | RequestBody 参数 | 容器网络IP地址版本，可选 [ ipv4, ipv6, dualStack ] |
| k8sVersion | String | 是 | RequestBody 参数 | 集群的K8S版本，可选 [ 1.13.10, 1.16.8 ] |
| maxPodsPerNode | Integer | 是 | RequestBody 参数 | 单节点最大容器组数量 |
| privateNetCIDRs | List&lt;String> | 否 | RequestBody 参数 | IPv4私有网络地址段，可选 [ 10.0.0.0/8, **********/12, ***********/16 ] |
| privateNetCIDRIPv6s | List&lt;String> | 否 | RequestBody 参数 | IPv6私有网络地址段，目前仅支持 [ fd00::/8 ] |
| vpcID | String | 是 | RequestBody 参数 | VPC ID |
| vpcCIDR | String | 否 | RequestBody 参数 | VPC IPv4 网段. IP版本为IPv4时必填 |
| vpcCIDRIPv6 | String | 否 | RequestBody 参数 | VPC IPv6 网段. IP版本为IPv6时必填 |

**返回头域**

除公共头域，无其它特殊头域。

**返回参数**

| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| errMsg | String | 否 | 错误信息 |
| isSuccess | Boolean | 是 | 请求是否成功 |
| recommendedContainerCIDRs | List&lt;String> | 否 | 推荐容器网段 |
| recommendedContainerCIDRIPv6s | List&lt;String> | 否 | 推荐容器网段 IPv6 |
| requestID | String | 是 | 请求 ID, 问题定位提供该 ID |

**请求示例**

```
POST /v2/net/recommend_container_cidr  HTTP/1.1
Host: cce.bj.baidubce.com
Authorization: bce-auth-v1/f81d3b34e48048fbb2634dc7882d7e21/2019-03-11T04:17:29Z/3600/host/74c506f68c65e26c633bfa104c863fffac5190fdec1ec24b7c03eb5d67d2e1de

{
	"vpcID": "vpc-pi9fghaxcpnf",
	"vpcCIDR": "***********/16",
	"vpcCIDRIPv6": "",
	"clusterMaxNodeNum": 2,
	"maxPodsPerNode": 32,
	"privateNetCIDRs": [
		"**********/12"
	],
	"privateNetCIDRIPv6s": null,
	"k8sVersion": "1.16.8",
	"ipVersion": "ipv4"
}
```

**返回示例**

```
HTTP/1.1 200 OK
X-Bce-Request-Id: 162b630e-38f9-4b31-addc-f89339058e70
Date: Thu, 16 Mar 2020 06:29:48 GMT
Content-Type: application/json;charset=UTF-8

{
	"recommendedContainerCIDRs": [
		"**********/26",
		"***********/26",
		"************/26",
		"************/26",
		"**********/26"
	],
	"recommendedContainerCIDRIPv6s": null,
	"isSuccess": true,
	"errMsg": "",
	"requestID": "162b630e-38f9-4b31-addc-f89339058e70"
}
```