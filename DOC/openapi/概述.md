欢迎使用百度智能云的核心产品——百度智能云容器引擎服务CCE（Cloud Container Engine）。您可以使用本文档介绍的API对CCE服务进行灵活的操作。

如果您是初次调用百度智能云产品的API，可以观看[API入门视频指南](https://cloud.baidu.com/doc/APIGUIDE/index.html)，快速掌握调用API的能力。

## 与旧版API的区别
若用户使用旧版API或是Web页面中的『创建集群（旧版）』选项创建集群，则应继续使用旧版API进行操作；
若用户使用新版API或是Web页面中的『创建集群』选项创建集群，则应继续使用新版API进行操作。
两个版本的API互不兼容，旧版的API目前已经停止维护。
我们强烈建议用户使用新版API对集群进行操作。

**Cluster 接口**

| 接口                                        | 请求方式   | 描述                        |
| ------------------------------------------- | ------ | ------------------------- |
| [/v2/cluster](CCE/API_V2参考/Cluster相关接口.md#创建集群)                     | POST   | 创建 CCE K8S 集群       |
| [/v2/clusters](CCE/API_V2参考/Cluster相关接口.md#集群列表)                    | GET    | 查询用户 CCE K8S 集群列表 |
| [/v2/cluster/{clusterID](CCE/API_V2参考/Cluster相关接口.md#集群详情)          | GET    | 查询指定集群详情 |
| [/v2/cluster/{clusterID}](CCE/API_V2参考/Cluster相关接口.md#删除集群)         | DELETE | 删除指定集群 |


**InstanceGroup 接口**

| 接口                                                | 请求方式   | 说明            |
| ------------------------------------------------- | ------ | ------------- |
| [/v2/cluster/{clusterID}/instancegroup](CCE/API_V2参考/InstanceGroup相关接口.md#创建节点组)                       | POST | 创建节点组 |
| [/v2/cluster/{clusterID}/instancegroups](CCE/API_V2参考/InstanceGroup相关接口.md#获取集群节点组列表) | GET | 获取集群的节点组列表 |
| [/v2/cluster/{clusterID}/instancegroup/{instanceGroupID}](CCE/API_V2参考/InstanceGroup相关接口.md#获取节点组详情)     | GET| 获取节点组详情 |
| [/v2/cluster/{clusterID}/instancegroup/{instanceGroupID}](CCE/API_V2参考/InstanceGroup相关接口.md#修改节点组节点副本数)     | PUT | 修改节点组节点副本数 |
| [/v2/cluster/{clusterID}/instancegroup/{instanceGroupID}](CCE/API_V2参考/InstanceGroup相关接口.md#修改节点组节点自动扩缩容配置)  | PUT | 修改节点组节点自动扩缩容配置 |
| [/v2/cluster/{clusterID}/instancegroup/{instanceGroupID}](CCE/API_V2参考/InstanceGroup相关接口.md#删除节点组)  | DELETE | 删除节点组 |


**Instance 接口**

| 接口                                                | 请求方式   | 说明            |
| ------------------------------------------------- | ------ | ------------- |
| [/v2/cluster/{clusterID}/instances](CCE/API_V2参考/Instance相关接口.md#创建节点（集群扩容）)                      | POST | 创建节点（集群扩容） |
| [/v2/cluster/{clusterID}/instance/{instanceID}](CCE/API_V2参考/Instance相关接口.md#获取节点详情) | GET | 获取节点详情 |
| [/v2/cluster/{clusterID}/instances](CCE/API_V2参考/Instance相关接口.md#获取集群节点列表) | GET | 获取集群节点列表 |
| [/v2/cluster/{clusterID}/instancegroup/{instanceGroupID}/instances](CCE/API_V2参考/Instance相关接口.md#获取节点组节点列表) | GET | 获取节点组节点列表 |
| [/v2/cluster/{clusterID}/instance/{instanceID}](CCE/API_V2参考/Instance相关接口.md#更新节点属性) | PUT | 更新节点属性 |
| [/v2/cluster/{clusterID}/instances](CCE/API_V2参考/Instance相关接口.md#删除节点（集群缩容）) | PUT | 删除节点（集群缩容） |


**Network 接口**

| 接口                                                | 请求方式   | 说明            |
| ------------------------------------------------- | ------ | ------------- |
| [/v2/net/check_clusterip_cidr](CCE/API_V2参考/Network相关接口.md#检查集群ClusterIP网段)                     | POST | 检查集群 ClusterIP 网段 |
| [/v2/net/check_container_network_cidr](CCE/API_V2参考/Network相关接口.md#检查集群容器网络网段)                      | POST | 检查集群容器网络网段 |
| [/v2/net/recommend_clusterip_cidr](CCE/API_V2参考/Network相关接口.md#推荐ClusterIP网络网段)                      | POST | 推荐 ClusterIP 网络网段 |
| [/v2/net/recommend_container_cidr](CCE/API_V2参考/Network相关接口.md#推荐容器网络网段)                      | POST | 推荐容器网络网段 |


**Autoscaler 接口**

| 接口                                                | 请求方式   | 说明            |
| ------------------------------------------------- | ------ | ------------- |
| [/v2/autoscaler/{clusterID}](CCE/API_V2参考/Autocaler相关接口.md#创建Autoscaler)                      | POST | 创建集群 Autoscaler |
| [/v2/autoscaler/{clusterID}](CCE/API_V2参考/Autocaler相关接口.md#查询Autoscaler配置)                      | GET | 查询集群 Autoscaler 配置 |
| [/v2/autoscaler/{clusterID}](CCE/API_V2参考/Autocaler相关接口.md#更新Autoscaler配置)                      | PUT | 更新集群 Autoscaler 配置 |


**Kubeconfig 接口**

| 接口                                                | 请求方式   | 说明            |
| ------------------------------------------------- | ------ | ------------- |
| [/v2/kubeconfig/{clusterID}/admin/{type}](CCE/API_V2参考/Kubeconfig相关接口.md#查询集群AdminKubeConfig)                      | GET | 查询集群 Admin KubeConfig |