## 集群管理附录


### ClusterSpec
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| clusterID | String | 否 | 集群ID. 创建集群时不需要传递此字段 |
| clusterName | String | 是 | 集群名称. 集群名称只能包含英文大小写字母、数字、-、.、和_ 名称长度不超过65个字符，不可为空 |
| clusterType | String | 否 | 集群类型，可选 [ normal, serverless, gpuShare, edge, cloudEdge, aihpc ]. 默认值 normal |
| description | String | 否 | 集群描述 |
| k8sVersion | String | 是 | K8S版本号，可选 [ 1.13.10, 1.16.8 ] |
| runtimeType | String | 否 | 容器运行时类型，可选 [docker, bci] 一般集群默认值 docker, Serverless集群默认为BCI |
| runtimeVersion | String | 否 | 容器运行时的版本，目前仅支持 18.9.2. 默认值18.9.2 | 
| vpcID | String | 是 | VPC ID | 
| vpcCIDR | String | 否 | VPC 网段 创建集群时无需设置此值 |
| vpcCIDRIPv6 | String | 否 | VPC IPv6 网段 创建集群时无需设置此值 |
| plugins | List&lt;String> | 否 | 插件列表 支持的插件包括 [ cce-ingress-controller，cluster-autoscaler，core-dns，core-dns-for-serverless，cronhpa，ip-masq-agent，kongming-nvidia，kube-proxy，kunlun-nvidia，metrics-adapter，metrics-server，network-inspector，nvidia-gpu，vpc-cni，vpc-route ] 其中core-dns、kube-proxy, metrics-server会在所有集群默认部署；容器网络模式为kubenet时会默认部署ip-masq-agent；GPU 共享型集群会默认部署kongming-nvidia，否则会部署nvidia-gpu； VPC路由模式CNI时会部署vpc-route，VPC辅助IP模式会部署vpc-cni；|
| masterConfig| [MasterConfig](#MasterConfig) | 是 | Master节点配置 |
| containerNetworkConfig | [ContainerNetworkConfig](#ContainerNetworkConfig) | 是 | 容器网络配置 |
| k8sCustomConfig | [K8SCustomConfig](#K8SCustomConfig) | 否 | K8S自定义配置. |
| authenticateMode | String | 是 | APIServer 认证模式 可选 [ x509, oidc ] |

### CreateClusterOptions
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| skipNetworkCheck | Boolean | 否 | 是否强行跳过容器网络的检查 |


### MasterConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| masterType | String | 是 | Master 机器类型，可选 [ managed, custom, serverless, edge, containerizedCustom, containerizedEdge ] |
| clusterHA | Integer | 否 | Master 副本数，可选 [ 1, 3, 5, 2 ]. 对于托管型集群其值可选[ 1, 3 ],默认值为3. 对于Serverless集群其值仅可为2. 自定义集群无需设置此值.  |
| exposedPublic	| Boolean | 否 | 是否向公网暴露 |
| clusterBLBVPCSubnetID | String | 否 | 集群 BLB 的 VPC 子网 ID. 托管型集群无需设置此值, 自定义集群必须设置此值. |
| managedClusterMasterOption | [ManagedClusterMasterOption](#ManagedClusterMasterOption) | 否 | 托管型集群的 Master 节点选项. 仅在集群类型是托管型时需要设置. |
| serverlessMasterOption | [ServerlessMasterOption](#ServerlessMasterOption) | 否 | Serverless Master 节点选项. 在集群类型是Serverless时需要设置 |
| edgeMasterOption | [EdgeMasterOption](#EdgeMasterOption) | 否 | Edge Master 节点选项. 在集群类型是纯边集群时需要设置  |


### ManagedClusterMasterOption 
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| masterVPCSubnetZone | String | 否 | Master 所在的 VPC 子网区域，可选 [ zoneA, zoneB, zoneC, zoneD, zoneE, zoneF ]. 默认值为zoneA. |


### ServerlessMasterOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| masterSecurityGroupID | String | 否 | 集群master安全组，后台自动覆盖，用户无需手动填写 |
| vkSecurityGroupID | String | 是 | 集群中启动的bci实例的安全组 |
| vkSubnets | List&lt;[VKSubnetType](#VKSubnetType)> | 否 | 集群中启动的bci实例所在的子网列表，如果不传会fallback使用clusterBLBVPCSubnetID |


### EdgeMasterOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| region | String | 是 | 边缘节点所在地区，可选 [ CENTRAL_CHINA, EAST_CHINA, NORTH_CHINA, NORTH_EAST, NORTH_WEST, SOUTH_CHINA, SOUTH_WEST ] |
| city | String | 是 | 边缘节点所在城市，城市拼音大写全拼 |
| serviceProvider | String | 是 | 运营商名称，可选 [ CHINA_MOBILE,CHINA_TELECOM,CHINA_UNICOM,TRIPLE_LINE ] |
| masterLBBandwidthInMbpsLimit | Integer | 否 | Master BLB 的带宽（Mbps）限制. 默认值为 100 |


### ContainerNetworkConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| mode | String | 是 | 容器的网络模式，可选 [ kubenet, vpc-cni, vpc-route-veth, vpc-route-ipvlan, vpc-route-auto-detect, vpc-secondary-ip-veth, vpc-secondary-ip-ipvlan, vpc-secondary-ip-auto-detect ] |
| eniVPCSubnetIDs | Map&lt;String,List&lt;String>> | 否 | ENI VPC 子网 ID |
| eniSecurityGroupID | String | 否 | ENI 安全组ID |
| ipVersion | String | 否 | 容器IP类型，可选 [ipv4, ipv6, dualStack ]，默认值ipv4 |
| lbServiceVPCSubnetID | String | 是 | 关联 BLB 所在子网 ID |
| nodePortRangeMax | Integer | 否 | 指定 NodePort Service 的端口范围，默认值32767, 最大值65536 |
| nodePortRangeMin | Integer | 否 | 指定 NodePort Service 的端口范围，默认值30000, 最大值65536 |
| clusterPodCIDR | String | 否 | 集群 Pod IP 网段, 在 kubenet 网络模式下有效. 网络类型是VPC-CNI时自动使用VPC的CIDR |
| clusterPodCIDRIPv6 | String | 否 | 集群 Pod IPv6 网段, 在 kubenet 网络模式下有效.网络类型是VPC-CNI时自动使用VPC的CIDR |
| clusterIPServiceCIDR | String | 否 | Service ClusterIP 的网段. ipv4时设置 |
| clusterIPServiceCIDRIPv6 | String | 否 | Service ClusterIP 的 IPv6 网段. ipv6时设置 |
| maxPodsPerNode | Integer | 否 | 每个 Node 上最大的 Pod 数，默认值128 |
| kubeProxyMode | String | 否 | kube-proxy 代理模式，可选 [ ipvs, iptables ]，默认值为 ipvs |
| networkPolicyType | String | 否 | Network Policy 实现类型，可选 [ none, felix ]. |
| enableNodeLocalDNS | Boolean | 否 | 是否启用 Node Local DNS 功能. |
| nodeLocalDNSAddr | String | 否 | Node Local DNS 地址. |


### K8SCustomConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| masterFeatureGates | Map&lt;String,Boolean> | 否 | 自定义 MasterFeatureGates |
| nodeFeatureGates | Map&lt;String,Boolean> | 否 | 自定义 NodeFeatureGates |
| admissionPlugins | List&lt;String> | 否 | 自定义 AdmissionPlugins |
| pauseImage | String | 否 | 自定义 PauseImage |
| kubeAPIQPS | Integer | 否 | 自定义 KubeAPIQPS |
| kubeAPIBurst | Integer | 否 | 自定义 KubeAPIBurst |
| schedulerPredicates | List&lt;String> | 否 | 自定义 SchedulerPredicates |
| schedulerPriorities | Map&lt;String,Integer> | 否 | 自定义 SchedulerPrioritiess |
| etcdDataPath | Integer | 否 | 自定义 etcd 数据目录 |
| enableKMSProvider | Boolean | 否 | 是否开启 KMS Provider |
| enableHostname | Boolean | 否 | 是否使用 BCC Instance Name 作为 Node Name |
| kmsKeyID | Integer | 否 | 使用 BCE KMS ID |
| enableLBServiceController | Boolean | 否 | 是否部署 cce-lb-controller. 开启此开关将会部署 cce-lb-controller 并关闭 CCM 组件的 service-controller. 建议设为 true. |
| enableCloudNodeController | Boolean | 否 | 是否部署 cloud-node-controller. 开启此开关将会部署 cce-cloud-node-controller 并关闭 CCM 组件的 node-controller. 建议设为 true.  |
| disableCCM | Integer | 否 | 是否完全关闭 CCM 功能 |
| enableEdgeHub | Integer | 否 | 是否开启边缘自治组件 |
| enableDefaultPluginDeployByHelm | Integer | 否 | 是否让默认插件使用 Helm 进行部署 |
| disableKubeletReadOnlyPort | Integer | 否 | 是否禁用 10255 只读端口. 默认为 false, true 表示禁用 |
| apiServerCertSAN | Integer | 否 | 自定义证书 SAN，支持域名或 IP |


### InstanceSet
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | 
| instanceSpec | [InstanceSpec](#InstanceSpec) | 是 | 节点配置信息 |
| count | Integer | 否 | 使用上述配置的节点数量. 当节点配置是已有节点时无需设置此值 |


### InstanceSpec
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| cceInstanceID | String | 否 | 用于 CCE 唯一标识 Instance 如果用户不指定: CCE 默认生成；如果用户指定: CCE 按照规则生成 |
| instanceName | String | 否 | 节点名称 |
| runtimeType | String | 否 | 容器运行时类型，可选 [docker, bci] 一般集群默认值 docker, Serverless集群默认为BCI |
| runtimeVersion | String | 否 | 容器运行时的版本，目前仅支持18.9.2. 默认值18.9.2  |
| clusterID | String | 否 | 集群 ID. 在创建集群时无需填写 |
| clusterRole | String | 否 | 节点在集群中的角色，可选 [ master, node ]. 创建集群时无需填写. |
| instanceGroupID | String | 否 | 节点所属节点组 ID |
| instanceGroupName | String | 否 | 节点所属节点组名称 |
| masterType | String | 是 | Master 机器来源。可选 [ managed, custom, serverless, edge, containerizedCustom, containerizedEdge ] |
| existed | Boolean | 否 | 是否为已有节点. 仅在节点类型为已有节点时需要设置 |
| existedOption | [ExistedOption](#ExistedOption) | 否 | 已有实例相关配置. 仅在节点类型为已有节点时需要设置. |
| deploySetID | String | 否 | 节点所属部署集 |
| machineType | String | 是 | 机器类型，可选 [ BCC, BBC, Metal, BCI, BEC]. 对于Serverless自动设为BCI. 对于托管型集群的Master自动设为BCC. 其他新建节点自动设为BCC. 已有节点会根据其节点类型自动设为BCC或BBC |
| instanceType | String | 否 | 机器规格，可选 [ N1, N2, N3, N4, N5, C1, C2, S1, G1, F1, ServerlessMaster, BEC ]. 仅自定义新建节点需要设置. 对于Serverless集群的Master自动设为ServerlessMaster. 对于托管型集群的Master使用DefaultMasterConfig中配置. 已有节点使用本节点的节点类型 |
| bbcOption | [BBCOption](#BBCOption) | 否 | BBC 选项. 仅在节点类型为BBC类型已有节点时需要设置 |
| becOption | [BECOption](#BECOption) | 否 | BEC 选项. 仅在节点类型为BEC类型已有节点时需要设置 |
| vpcConfig | [VPCConfig](#VPCConfig) | 否 | VPC 选项. 新建BCC节点时和新建Serverless Master时需要设置. 托管型Master节点组自动使用Master Config配置. 已有节点自动使用自身VPC配置. |
| instanceResource | [InstanceResource](#InstanceResource) | 否 | 集群规格相关配置. 新建BCC节点时必须设置. 托管型Master节点组自动使用后台默认配置. 已有节点自动使用自身资源配置. |
| imageID | String | 否 | 新建BCC节点和已有节点需要重装系统时时需要设置 imageID 和 InstanceOS 二者中的其中一个. 优先使用 ImageID, 如果用户传入 InstanceOS 信息, 由后台计算 ImageID. |
| instanceOS | [InstanceOS](#InstanceOS) | 否 | 新建BCC节点和已有节点需要重装系统时时需要设置 imageID 和 InstanceOS 二者中的其中一个. 优先使用 ImageID, 如果用户传入 InstanceOS 信息, 由后台计算 ImageID. |
| needEIP | Boolean | 否 | 机器是否需要EIP |
| eipOption | [EIPOption](#EIPOption) | 否 | EIP 选项. needEIP为True时必须设置. |
| adminPassword | String | 否 | 管理员密码. 不设置时将由系统自动生成. 已有BCC节点在不重装系统时必须设置. 密码要求8～32位字符, 仅限且必须包含字母、数字和指定符号 !@#$%^*() |
| needGPU | Boolean | 否 | 是否需要 GPU |
| sshKeyID | String | 否 | SSH Key ID |
| instanceChargingType | String | 否 | 节点计费方式，可选 [ Prepaid, Postpaid, bidding ]. 新建节点、托管型集群Master节点、Serverless节点默认且仅限为后付费. 已有节点支持预付费或后付费 |
| instancePreChargingOption | [InstancePreChargingOption](#InstancePreChargingOption) | 否 | 节点预付费选项. 预付费节点需要设置. |
| deleteOption | [DeleteOption](#DeleteOption) | 否 | 删除节点选项. |
| deployCustomConfig | [DeployCustomConfig](#DeployCustomConfig) | 否 | 自定义部署选项 |
| tags | List&lt;[Tag](#Tag)>  | 否 | 节点 Tag 列表.  |
| labels | Map&lt;String,String> | 否 | 节点 Label 列表. 后台会节点自动添加cluster-id和cluster-role两个label |
| taints | List&lt;[Taint](#Taint)> | 否 | 节点 Taint 列表 |
| annotations | Map&lt;String,String> | 否 | 节点 Annotation 列表 |


### ExistedOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| existedInstanceID | String | 是 | 现有节点 ID |
| rebuild | Boolean | 否 | 是否重装系统，默认为true，即重装系统 |


### BBCOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| reserveData | Boolean | 是 | 是否保留数据 |
| raidID | String | 否 | 磁盘阵列类型 ID；reserveData=false 时必填, reserveData=true 时不生效 |
| sysDiskSize | Integer | 否 | 系统盘分配大小，单位 GB；reserveData=false 时必填, reserveData=true 时不生效 |


### BECOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| becRegion | String | 是 | 边缘节点所在地区，可选 [ CENTRAL_CHINA, EAST_CHINA, NORTH_CHINA, NORTH_EAST, NORTH_WEST, SOUTH_CHINA, SOUTH_WEST ] |
| becCity | String | 是 | 边缘节点所在城市，城市拼音大写全拼 |
| becServiceProvider | String | 是 | 运营商名称，可选 [ CHINA_MOBILE,CHINA_TELECOM,CHINA_UNICOM,TRIPLE_LINE ] |


### VPCConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| vpcID | String | 否 | VPC ID. 为空时使用集群的VPC ID |
| vpcSubnetID | String | 否 | VPC 子网 ID. 新建节点必须设置此值. 已有节点无需设置. |
| securityGroupID | String | 否 | 安全组 ID. 新建节点必须设置此值. 已有节点无需设置. |
| vpcSubnetType | String | 否 | VPC 子网类型，可选 [ BBC, BCC, BCC_NAT ]. 创建集群时无需设置，后台根据子网ID自动设置.  |
| vpcSubnetCIDR | String | 否 | VPC 子网网段. 创建集群时无需设置，后台根据子网ID自动设置.|
| vpcSubnetCIDRIPv6 | String | 否 | VPC IPv6 子网网段. 创建集群时无需设置，后台根据子网ID自动设置. |
| availableZone | String | 否 | 可用区，可选 [ zoneA, zoneB, zoneC, zoneD, zoneE, zoneF ]. 创建集群时无需设置，后台根据子网ID自动设置.|


### InstanceResource
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| cpu | Integer | 否 | CPU 核数. 新建节点必须设置此字段 |
| mem | Integer | 否 | 内存大小，单位GB. 新建节点必须设置此字段 |
| rootDiskType | String | 否 | 根磁盘类型，可选 [ hp1, cloud_hp1, hdd, local, sata, ssd ]. 新建节点默认为hp1 已有节点和其本身属性一致 |
| rootDiskSize | Integer | 否 | 根磁盘大小，单位GB. 默认值为40 |
| localDiskSize | Integer | 否 | 本地磁盘大小，GPU 机器必须指定，单位 GB |
| cdsList | List&lt;[CDSConfig](#CDSConfig)> | 否 | CDS 列表，默认第一块盘作为 docker 和 kubelet 数据盘 |
| gpuType | String | 否 | GPU 类型，可选 [ V100-32, V100-16, P40, P4, K40, DLCard ]. 节点类型为G1时必须设定 |
| gpuCount | Integer | 否 | GPU 数量. 节点类型为G1时必须设定 |
| specId | string | 否 | Spec ID |


### CDSConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | 
| diskPath | String | 是 | 磁盘路径 |
| storageType | String | 是 | 存储类型，可选 [ hp1, cloud_hp1, hdd, local, sata, ssd ] |
| cdsSize | Integer | 是 | 磁盘空间大小 |
| snapshotID | String | 否 | 快照ID，支持从快照创建磁盘 |


### InstanceOS
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| imageType | String | 是 | 镜像类型。取值范围包括 [ All, System, Custom, Integration, Sharing, GpuBccSystem, GpuBccCustom, BbcSystem, BbcCustom ] |
| imageName | String | 否 | 镜像名字。例如ubuntu-14.04.1-server-amd64-201506171832 |
| osType | String | 是 | 操作系统类型，可选 [ linux, windows ] |
| osName | 	String | 是 | 操作系统名字，可选 [ CentOS, Ubuntu, Windows Server, Debian, opensuse ] |
| osVersion | String | 是 | 操作系统版本，例如14.04.1 LTS |
| osArch | String | 是 | 操作系统架构。例如x86_64 (64bit) |
| osBuild | String | 否 | 镜像创建时间信息，例如2015061700 |


### EIPOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| eipName | String | 是 | EIP 名称 |
| eipChargeType | String | 是 | EIP的计费方式，可选 [ ByTraffic, ByBandwidth ] |
| eipBandwidth | Integer | 是 | EIP 带宽. 按带宽计费取值范围是1-200. 按流量计费取值范围是1-1000 |


### InstancePreChargingOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| purchaseTime | Integer | 是 | 购买时间 |
| autoRenew | Boolean | 是 | 是 | 否自动续费 |
| autoRenewTimeUnit | String | 是 | 续费单位 |
| autoRenewTime | Integer | 是 | 续费时间 |


### DeleteOption
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| moveOut | Boolean | 否 | 是否移出节点. true表仅将节点移出集群. false代表将节点删除. 创建集群时新建节点默认为false. 已有节点默认为true. |
| deleteResource | Boolean | 否 | 是否删除相关资源. 创建集群时新建节点默认为true. 已有节点默认为false. |
| deleteCDSSnapshot | Boolean | 否 | 是否删除CDS快照. 创建集群时新建节点默认为true. 已有节点默认为false. |


### DeployCustomConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | 
| dockerConfig | List&lt;[DockerConfig](#DockerConfig)> | 否 | Docker 相关配置 |
| kubeletRootDir | String | 否 | kubelet 数据目录 |
| EnableResourceReserved | Boolean | 否 | 是否开启资源预留 | 
| kubeReserved | Map&lt;String,String>  | 否 | k8s进程资源预留配额，例如 { cpu: 100m, memory: 1000Mi } |
| systemReserved | Map&lt;String,String>  | 否 | 系统进程资源预留配额，例如 { cpu: 100m, memory: 1000Mi } |
| enableCordon | Boolean | 否 | 是否封锁节点 | 
| preUserScript | String | 否 | 部署前执行脚本, 前端 base64 编码后再传参 |
| postUserScript | String | 否 | 部署后执行脚本, 前端 base64 编码后再传参 |
| kubeletBindAddressType | String | 否 | Kubelet 绑定地址 |


### DockerConfig
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- | 
| dockerDataRoot | String | 否 | 自定义 docker 数据目录 |
| registryMirrors | List&lt;String> | 否 | 自定义 RegistryMirrors |
| insecureRegistries | List&lt;String> | 否 | 自定义 InsecureRegistries |
| dockerLogMaxSize | String | 否 | docker日志大小，默认值为 20m |
| dockerLogMaxFile | String | 否 | docker日志保留数，默认值为 10 |
| dockerBIP | String | 否 | docker0网桥网段，默认值为 ************/28 |


### Tag
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| tagKey | String | 是 | Tag Key |
| tagValue | String | 是 | Tag Value |


### Taint
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| effect | String | 是 | 当Pod不容忍 Taint 时的行为，可选 [ NoSchedule, PreferNoSchedule, NoExecute ] |
| key | String | 是 | Taint Key |
| timeAdded | Date-time | 否 | 添加污点的时间点，只有effect为NoExecute时使用 |
| value |  String | 是 | Taint Value |


### ClusterPage
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| keywordType | String | 集群模糊查询字段，可选 [ clusterName, clusterID ] |
| keyword | String | 查询关键词 |
| orderBy | String | 集群查询排序字段，可选 [ clusterName, clusterID, createdAt ] |
| order | String | 排序方式，可选 [ ASC, DESC ] |
| pageNo | Integer | 页码 |
| pageSize | Integer | 单页结果数 |
| totalCount | Integer | 集群总数量 |
| clusterList | List&lt;[Cluster](#Cluster)> | 查询到的集群列表 |


### Cluster
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| spec | [ClusterSpec](#ClusterSpec) | 集群属性 |
| status | [ClusterStatus](#ClusterStatus) | 集群状态 |
| createdAt | String | 创建时间 |
| updatedAt | String | 更新时间 |


### ClusterStatus
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| clusterBLB | [BLB](#BLB) | 集群的BLB |
| clusterPhase | String | 集群状态，可选 [ pending, provisioning, provisioned, running, create_failed, deleting, deleted, delete_failed ] |
| nodeNum | Integer | 节点数量 |


### BLB
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| id | String | BLB ID |
| vpcIP | String | VPC IP 地址 
| eip | String | EIP 地址 |

### VKSubnetType
| 参数名称 | 类型 | 是否必须 | 描述 |
| ------------- | ---------------------- | -------------------- | -------------------- |
| availableZone | String | 是 | 可用区名称 |
| subnetID | String | 是 | 子网 ID |


### InstancePage
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| clusterID | String | 集群的ID |
| keywordType | String | 集群模糊查询字段，可选 [ clusterName, clusterID ] |
| keyword | String | 查询关键词 |
| orderBy | String | 集群查询排序字段，可选 [ clusterName, clusterID, createdAt ] |
| order | String | 排序方式，可选 [ ASC, DESC ] |
| pageNo | Integer | 页码 |
| pageSize | Integer | 单页结果数 |
| totalCount | Integer | 节点总数 |
| instanceList | List&lt;[Instance](#Instance)> | 节点列表 |


### Instance
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| createdAt | String | 节点创建时间 |
| spec | [InstanceSpec](#InstanceSpec) | 节点的配置 |
| status | [InstanceStatus](#InstanceStatus) | 节点的状态 |
| updatedAt | String | 节点更新时间 |


### InstanceStatus
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| instancePhase | String | 节点的状态，可选 [ pending, provisioning, provisioned, running, create_failed, deleting, deleted, delete_failed ] |
| machine | [Machine](#Machine) | 虚拟机信息 |
| machineStatus | String | BBC虚机状态，可选 [ ACTIVE, BUILD, REBUILD, DELETED, SNAPSHOT, DELETE_SNAPSHOT, VOLUME_RESIZE, ERROR, EXPIRED, REBOOT, RECHARGE, SHUTOFF, STOPPED, UNKNOWN ] |


### Machine
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| eip | String | EIP IP地址 |
| instanceID | String | 对应节点ID |
| mountList | List&lt;[MountConfig](#MountConfig)> | 磁盘挂载信息列表 |
| orderID | String | 订单号 |
| vpcIP | String | VPC IP 地址 |
| vpcIPIPv6 | String | VPC IPv6地址 |


### MountConfig
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| cdsID | String | CDS磁盘ID |
| cdsSize | Integer | CDS磁盘大小 |
| device | String | 设备路径. 如/dev/vdb |
| diskPath | String | 磁盘路径. 如/data |
| storageType | String | 磁盘存储类型，可选 [ hp1, cloud_hp1, hdd, local, sata, ssd ] |


### ListInstancesByInstanceGroupIDPage
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| pageNo | Integer | 页码 |
| pageSize | Integer | 单页结果数 |
| totalCount | Integer | 节点总数 |
| instanceList | List&lt;[Instance](#Instance)> | 节点列表 |


### InstanceGroup
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| spec | [InstanceGroupSpec](#InstanceGroupSpec) | 节点组的配置 |
| status | [InstanceGroupStatus](#InstanceGroupStatus) | 节点组的状态 |
| createdAt | String | 节点组的创建时间 |


### InstanceGroupSpec
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| cceInstanceGroupID | String | 节点组 ID |
| instanceGroupName | String | 节点组名称 |
| clusterID | String | 集群ID |
| clusterRole | String | 节点在集群中的角色，目前节点组仅支持nod. 默认值为 node |
| shrinkPolicy | String | 节点组收缩规则. 可选 [ Priority, Priority ].  |
| updatePolicy | String | 节点组更新规则. 可选 [ Rolling, Concurrency]. |
| cleanPolicy | String | 节点清理规则. 可选 [ Remain, Delete ].  |
| instanceTemplate | [InstanceTemplate](#InstanceTemplate) | 节点配置 |
| replicas | Integer | 节点副本数 |
| clusterAutoscalerSpec | [ClusterAutoscalerSpec](#ClusterAutoscalerSpec) | 集群的自动伸缩配置 |


### InstanceTemplate
此结构等同于InstanceSpec，参见[InstanceSpec](#InstanceSpec)


### ClusterAutoscalerSpec
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| enabled | Boolean | 是否启用Autoscaler |
| minReplicas | Integer | 最小副本数. 取值范围是自然数集. |
| maxReplicas | Integer |  最大副本数. 取值范围是自然数集, 需大于minReplicas. |
| scalingGroupPriority | Integer | 伸缩组优先级. 取值范围是自然数集. |


### InstanceGroupStatus
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| readyReplicas | Integer | 节点组中处于 Ready 状态的节点数 |
| pause | [Pause](#Pause) | 节点组的暂停状态 |


### Pause
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| paused | Boolean | 节点组是否处于暂停状态 |
| reason | String | 节点组处于暂停状态的原因 |


### ListInstanceGroupPage
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| pageNo | Integer | 页码 |
| pageSize | Integer | 单页结果数 |
| totalCount | Integer | 节点组总数 |
| instanceList | List&lt;[InstanceGroup](#InstanceGroup)> | 节点组列表 |


### Autoscaler
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| clusterID | String | 集群 ID |
| clusterName | String | 集群名称 | 
| caConfig | [ClusterAutoscalerConfig](#ClusterAutoscalerConfig) | 节点组列表 |


### ClusterAutoscalerConfig
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| expander | String | 自动扩缩容选组的策略. 可选 [ random, most-pods, least-waste, priority ], 默认值为 random. |
| instanceGroups | List&lt;[ClusterAutoscalerInstanceGroup](#ClusterAutoscalerInstanceGroup)> | 节点组的 Autoscaler 配置. 用户无需输入此项内容. |
| kubeVersion | String | K8S 版本. 为空时后台会自动查询集群K8S版本号. |
| maxEmptyBulkDelete | Integer | 最大并发缩容数 |
| replicaCount | Integer | 预期副本数量 |
| scaleDownDelayAfterAdd | Integer | 扩容后缩容启动时延, 单位为分钟 |
| scaleDownEnabled | Boolean | 是否启动缩容. 默认值为false |
| scaleDownGPUUtilizationThreshold | Integer | GPU缩容阈值百分比, 取值范围（0, 100）. |
| scaleDownUnneededTime | Integer | 缩容触发时延，单位为分钟. |
| scaleDownUtilizationThreshold | Integer | 缩容阈值百分比, 取值范围（0, 100）. |
| skipNodesWithLocalStorage | Boolean | 是否跳过使用本地存储的节点, 默认值为 true. |
| skipNodesWithSystemPods | Boolean | 是否跳过有部署系统 Pod 的节点, 默认值为 true. |


### ClusterAutoscalerInstanceGroup
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| instanceGroupID | String | 节点组 ID |
| minReplicas | String | 最小副本数 |
| maxReplicas | String | 最大副本数 |
| priority | String | 优先级 |


### ContainerCIDRConflict
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| conflictCluster | [ConflictCluster](#ConflictCluster) | 与容器网段冲突的VPC内集群，当且仅当 NetworkConflictType 为 ContainerCIDRAndExistedClusterContainerCIDRConflict 不为 nil |
| conflictNodeCIDR | [ConflictNodeCIDR](#ConflictNodeCIDR) | 与容器网段冲突的节点网段，当且仅当 NetworkConflictType 为 ContainerCIDRAndNodeCIDRConflict 不为 nil |
| conflictType | String | 网络冲突类型，可选 [ ContainerCIDRAndNodeCIDR, ContainerCIDRAndExistedClusterContainerCIDR, ContainerCIDRAndVPCRoute, ClusterIPCIDRAndNodeCIDR, ClusterIPCIDRAndContainerCIDR ] |
| conflictVPCRoute | [ConflictVPCRoute](#ConflictVPCRoute) | 与容器网段冲突的VPC路由，当且仅当 NetworkConflictType 为 ContainerCIDRAndVPCRouteConflict 不为 nil |


### ClusterIPCIDRConflict
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| conflictContainerCIDR | [ConflictContainerCIDR](#ConflictContainerCIDR) | 容器网段冲突信息 |
| conflictNodeCIDR | [ConflictNodeCIDR](#ConflictNodeCIDR) | 节点网段冲突信息 |
| conflictType | String | 网络冲突类型，可选 [ ContainerCIDRAndNodeCIDR, ContainerCIDRAndExistedClusterContainerCIDR, ContainerCIDRAndVPCRoute, ClusterIPCIDRAndNodeCIDR, ClusterIPCIDRAndContainerCIDR ] |


### ConflictCluster
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| clusterID | String | 集群ID |
| containerCIDR | String | 冲突的容器网段 |


### ConflictContainerCIDR
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| containerCIDR | String | 冲突的容器网段 |


### ConflictNodeCIDR
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| nodeCIDR | String | 冲突的节点网段 |


### ConflictVPCRoute
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| routeRule | [RouteRule](#RouteRule) | 冲突的 VPC 路由 |

### RouteRule
| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| routeRuleId | String | 路由规则 ID |
| routeTableId | String | 路由表 ID |
| sourceAddress | String | 源地址 |
| destinationAddress | String | 目的地址 |
| nexthopId | String | 下一跳 ID |
| nexthopType | String | 下一跳类型 |
| description | String | 描述 |


### AddOnInfo

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| meta          | [Meta](#Meta)  | 组件基础信息 |
| instance      | [AddOnInstance](#AddOnInstance) | 组件安装信息。如果组件未安装，该字段为空值。 |
| multiInstances | List&lt;[AddOnInstance](#AddOnInstance)> | 如果组件允许多实例部署，则为该组件全部部署实例。通常不会使用到该字段。 |


### Meta

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| name | String | 组件名称 |
| type | String | 组件类型。包括CloudNativeAI、Networking、HybridSchedule、Image、Storage、Observability |
| latestVersion | String | 组件的最新版本 |
| shortIntroduction | String | 组件简介 |
| defaultParams | String | 组件默认部署参数 |
| installInfo | [InstallInfo](#InstallInfo)  | 组件是否可以安装 |


### AddOnInstance

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| name | String | 组件名称 |
| installedVersion | String | 已安装组件的版本 |
| params | String | 组件的部署参数 |
| status | [AddonInstanceStatus](#AddonInstanceStatus)  | 组件状态 |
| uninstallInfo | [UninstallInfo](#UninstallInfo)  | 组件是否允许卸载 |
| upgradeInfo | [UpgradeInfo](#UpgradeInfo)  | 组件是否允许升级 |
| updateInfo | [UpdateInfo](#UpdateInfo)  | 组件是否允许更新部署参数 |


### AddonInstanceStatus

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| phase | String | 组件当前的状态 |
| code | String | 组件状态异常时，其错误码内容 |
| message | String | 组件状态异常时，其错误详情 |
| traceID | String | 组件状态异常时，其故障ID |


### InstallInfo

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| allowInstall | String | 组件是否允许安装 |
| message | String | 如果不允许安装，该字段为原因 |


### UninstallInfo

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| allowUninstall | String | 组件是否允许卸载 |
| message | String | 如果不允许卸载，该字段为原因 |


### UpgradeInfo

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| allowUpgrade | String | 组件是否允许升级 |
| nextVersion | String | 如果允许升级，其目标升级版本 |
| message | String | 如果不允许升级，该字段为原因 |


### UpdateInfo

| 参数名称 | 类型 | 描述 |
| ------------- | ---------------------- | -------------------- |
| allowUpdate | String | 组件是否允许更新参数 |
| message | String | 如果不允许更新参数，该字段为原因 |


