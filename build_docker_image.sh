#!/bin/sh

if [ $# -ne 4 ];
then
    echo "Usage: $0 [path] [name] [namespace] [tag]"
    echo "Example: sh build_docker_image.sh ./services/gateway/cce-gateway cce-gateway cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cce-cloud-controller-mamager cce-ccm cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cce-master-lb-controller cce-master-lb-controller cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cluster-controller cce-cluster-controller cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/workflow/workflow-controller cce-workflow-controller cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/observability/monitor-controller monitor-controller cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cluster-service cce-cluster-service cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cluster-sync cce-cluster-sync cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/app/gencert cce-cluster-gencert cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cce-oidc-provider cce-oidc-provider cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/webhook/cce-validate-webhook cce-validate-webhook cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/webhook/cce-validate-webhook cce-validate-webhook cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/instance-gc-manager cce-instance-gc-manager cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/network/network-inspector/cmd/inspector cce-network-inspector cce-service-public chenhuan"
    echo "Example: sh build_docker_image.sh ./services/network/network-inspector/cmd/result cce-network-result cce-service-public chenhuan"
    echo "Example: sh build_docker_image.sh ./services/network/ingress-controller cce-ingress-controller cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/app/dashboard-metrics-scraper metrics-server-scraper cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/network/lb-controller cce-lb-controller cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/helm/helm-service cce-helm-service cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/monitor-service cce-monitor-service cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/cce-log-agent cce-log-agent cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/cce-alert/cmd/webhook cce-alertmanager-webhook cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/cce-alert/cmd/collector cce-alert-collector cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/k8s-event-collector k8s-event-collector cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/master-service-discovery cce-master-service-discovery cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/cce-log-operator cce-log-operator cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/master-service-discovery/cmd/control-plane-targets cce-control-plane-targets cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/external-auditer kube-external-auditor cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/healthcheck/detector cce-health-check cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/app/app-service cce-app-service cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/app/image cce-image-plugin cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./pkg/plugin cce-plugin-helm-chart cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/tests/e2etest cce-e2e-test cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/webhook/cpuoffline-mutation-webhook/cmd/mutation-webhook/ cpuoffline-mutation-webhook cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/webhook/cpuoffline-mutation-webhook/cmd/inject-cpu-offline/ cpuoffline-inject cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/scheduler/cpuoffline-agent cpuoffline-agent cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/cluster-trans cce-cluster-trans cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/upgrade-lb-controller upgrade-lb-controller cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/upgrade-ingress-controller upgrade-ingress-controller cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/freeze-v1-cluster cce-freeze-cluster cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/fix-bid-instance fix-bid-instance cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/v1-to-v2-pre-check v1-to-v2-pre-check cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/change-external-auditor-configmap cce-change-external-auditor-configmap cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/admintask/fix-instance-zero-cpu-mem fix-instance-zero-cpu-mem cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-iaas-check cce-iaas-check cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-artifact-service cce-artifact-service cce-service-dev 1.0.0"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-status-alert cce-status-alert cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-system-config-sync/config-init  cce-system-config-init cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/workflow/cce-agent cce-agent cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-palo-collector cce-palo-collector cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/k8s-report-collector k8s-report-collector cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-config-manager cce-config-manager cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/cce-cloud-node-controller cce-cloud-node-controller cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/stability/demo/apiserver-oom apiserver-oom cce-plugin-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-weekly-report cce-weekly-report cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/devops/cce-devops-backend cce-devops-backend cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/cluster/db-sync-controller cce-db-sync-controller cce-service-dev chenhuan"
    echo "Example: sh build_docker_image.sh ./services/monitor/problem-detector-service problem-detector-service cce-service-dev 1.0.0"
    echo "Example: sh build_docker_image.sh ./services/monitor/problem-detector-service/mockserver bcm-mock-service cce-service-dev 1.0.0"
    echo "Example: sh build_docker_image.sh ./services/monitor/cce-onepilot cce-onepilot cce-plugin-dev v1.0.0"
    echo "Example: sh build_docker_image.sh ./services/monitor/cce-onepilot/agent-downloader agent-downloader cce-plugin-dev v1.0.0"
    echo "Example: cd ./services/cluster/web-console && docker build -f Dockerfile -t registry.baidubce.com/cce-service-public/cce-web-console:chenhuan ./ && docker push registry.baidubce.com/cce-service-public/cce-web-console:chenhuan"
    echo "Example: cd ./services/healthcheck/grafana-config && docker build -f Dockerfile -t registry.baidubce.com/cce-service-public/cce-health-check-grafana-config:chenhuan ./ && docker push registry.baidubce.com/cce-service-public/cce-health-check-grafana-config:chenhuan"
    echo "Example: cd ./services/monitor/grafana-provisioning && docker build -f Dockerfile -t registry.baidubce.com/cce-service-public/cce-grafana-provisioning:chenhuan ./ && docker push registry.baidubce.com/cce-service-public/cce-grafana-provisioning:chenhuan"
    echo "Example: cd ./services/monitor/cce-grafana-dashboard-backup && docker build -f Dockerfile -t registry.baidubce.com/cce-plugin-dev/cce-grafana-provisioning:chenhuan ./ && docker push registry.baidubce.com/cce-plugin-dev/cce-grafana-provisioning:chenhuan"
    echo "Example: cd ./services/ai/nvidia-mps  && docker build -f Dockerfile -t registry.baidubce.com/cce-plugin-dev/cce-nvidia-mps:chenhuan ./ && docker push registry.baidubce.com/cce-plugin-dev/cce-nvidia-mps:chenhuan"
    echo "Example: cd ./services/ai/cgpu-node-exporter  && docker build -f Dockerfile -t registry.baidubce.com/cce-plugin-dev/cce-cgpu-node-exporter:chenhuan ./ && docker push registry.baidubce.com/cce-plugin-dev/cce-cgpu-node-exporter:chenhuan"

    exit -1
fi

registry_domain="ccr-registry.baidubce.com"

path=${1}
name=${2}
namespace=${3}
tag=${4}

if [ ! -d ${path} ]; then
    echo "path: %{path} not exist"
fi

if [[ -z $ARCH ]]; then
  echo "CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o ${path}/${name} ${path} && cd ${path} && docker build -f Dockerfile --platform linux/amd64 -t ${registry_domain}/${namespace}/${name}:${tag} ./ && docker push ${registry_domain}/${namespace}/${name}:${tag}"
  CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -o ${path}/${name} ${path} && cd ${path} && docker build -f Dockerfile --platform linux/amd64  -t ${registry_domain}/${namespace}/${name}:${tag} ./ && docker push ${registry_domain}/${namespace}/${name}:${tag}
else
  echo "CGO_ENABLED=0 GOOS=linux GOARCH=$ARCH go build -a -o ${path}/${name} ${path} && cd ${path} && docker build -f Dockerfile --platform linux/$ARCH -t ${registry_domain}/${namespace}/${name}:${tag} ./ && docker push ${registry_domain}/${namespace}/${name}:${tag}"
  CGO_ENABLED=0 GOOS=linux GOARCH=$ARCH go build -a -o ${path}/${name} ${path} && cd ${path} && docker build -f Dockerfile --platform linux/$ARCH  -t ${registry_domain}/${namespace}/${name}:${tag} ./ && docker push ${registry_domain}/${namespace}/${name}:${tag}
fi
