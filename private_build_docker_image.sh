#!/bin/sh

if [ $# -ne 4 ];
then
    echo "Usage: $0 [path] [name] [namespace] [tag]"
    echo "Example: sh private_build_docker_image.sh ./services/cluster/cluster-controller cce-cluster-controller abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/cluster/cluster-service cce-cluster-service abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/network/network-inspector/cmd/inspector cce-network-inspector cce-service-public chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/network/network-inspector/cmd/result cce-network-result cce-service-public chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/monitor/monitor-service cce-monitor-service abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/app/app-service cce-app-service abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/cluster/cluster-sync cce-cluster-sync abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./pkg/plugin cce-plugin-helm-chart abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/tests/e2etest cce-e2e-test abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/cluster/cce-oidc-provider cce-oidc-provider abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/cluster/cce-validate-webhook cce-validate-webhook abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/admintask/cluster-trans cce-cluster-trans abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/network/ingress-controller cce-ingress-controller cce-plugin-dev chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/devops/cce-status-alert cce-status-alert abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/devops/cce-palo-collector cce-palo-collector abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/healthcheck/node-health-check cce-node-check cce-service-public chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/devops/cce-config-manager cce-config-manager abc-stack chenhuan"
    echo "Example: sh private_build_docker_image.sh ./services/devops/cce-devops-backend cce-devops-backend abc-stack chenhuan && docker tag  iregistry.baidu-int.com/abc-stack/cce-devops-backend:chenhuan hub.baidubce.com/jpaas-public/cce-devops-backend:chenhuan && docker push hub.baidubce.com/jpaas-public/cce-devops-backend:chenhuan"
    echo "Example: cd ./services/cluster/web-console && docker build -f Dockerfile -t iregistry.baidu-int.com/cce-service-public/cce-web-console:chenhuan ./ && docker push iregistry.baidu-int.com/cce-service-public/cce-web-console:chenhuan"
    echo "Example: cd ./services/healthcheck/grafana-config && docker build -f Dockerfile -t iregistry.baidu-int.com/cce-service-public/cce-health-check-grafana-config:chenhuan ./ && docker push iregistry.baidu-int.com/cce-service-public/cce-health-check-grafana-config:chenhuan"

    exit -1
fi

path=${1}
name=${2}
namespace=${3}
tag=${4}

if [ ! -d ${path} ]; then
    echo "path: %{path} not exist"
fi

echo "CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ${path}/${name} ${path} && cd ${path} && docker build -f Dockerfile -t iregistry.baidu-int.com/${namespace}/${name}:${tag} ./ && docker push iregistry.baidu-int.com/${namespace}/${name}:${tag}"
CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ${path}/${name} ${path} && cd ${path} && docker build -f Dockerfile  -t iregistry.baidu-int.com/${namespace}/${name}:${tag} ./ && docker push iregistry.baidu-int.com/${namespace}/${name}:${tag}
