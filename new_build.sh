#!/bin/bash
set -eu
set -o pipefail

function set_go_env() {
    # GO ENV
    go env -w GO111MODULE=on
    go env -w GONOPROXY=\*\*.baidu.com\*\*
    go env -w GONOSUMDB=\*

    unset GOPROXY
    go env -w GOPROXY=https://goproxy.baidu-int.com

    go mod tidy
}

function package_deploy()
{
    set_go_env

    declare -A serviceMap
    # 组件列表，key 是名称，用于镜像名，value 是包路径，用于 go build
    serviceMap["cce-base"]="./base"
    serviceMap["cce-grafana-provisioning"]="./services/monitor/grafana-provisioning"
    serviceMap["cce-master-service-discovery"]="./services/monitor/master-service-discovery"
    serviceMap["cce-control-plane-targets"]="./services/monitor/master-service-discovery/cmd/control-plane-targets"
    serviceMap["cce-monitor-service"]="./services/monitor/monitor-service"
    serviceMap["cce-alert-rules-apply"]="./services/monitor/cce-alert/alert-rules"
    serviceMap["loki-pusher"]="./services/cluster/master-pro/loki-pusher"
    serviceMap["cce-app-service"]="./services/app/app-service"
    serviceMap["cce-helm-service"]="./services/helm/helm-service"
    serviceMap["cce-gateway"]="./services/gateway/cce-gateway"
    serviceMap["cce-cluster-service"]="./services/cluster/cluster-service"
    serviceMap["cce-cluster-controller"]="./services/cluster/cluster-controller"
    serviceMap["cce-etcd-manager"]="./services/serverless/etcd-manager/cmd/etcd-manager"
    serviceMap["k8s-event-collector"]="./services/monitor/k8s-event-collector"
    serviceMap["k8s-report-collector"]="./services/monitor/k8s-report-collector"
    serviceMap["cce-status-alert"]="./services/devops/cce-status-alert"
    serviceMap["cce-config-manager"]="./services/devops/cce-config-manager"
    serviceMap["cce-image-plugin"]="./services/app/image"
    serviceMap["cce-e2e-test"]="./services/tests/e2etest"
    serviceMap["cce-plugin-helm-chart"]="./pkg/plugin"
    serviceMap["cce-cluster-sync"]="./services/cluster/cluster-sync"
    serviceMap["cce-oidc-provider"]="./services/cluster/cce-oidc-provider"
    serviceMap["cce-palo-collector"]="./services/devops/cce-palo-collector"
    serviceMap["cce-validate-webhook"]="./services/webhook/cce-validate-webhook"
    serviceMap["cce-alert-collector"]="./services/monitor/cce-alert/cmd/collector"
    serviceMap["cce-alertmanager-webhook"]="./services/monitor/cce-alert/cmd/webhook"
    serviceMap["cce-instance-gc-manager"]="./services/cluster/instance-gc-manager"
    serviceMap["cce-iaas-check"]="./services/devops/cce-iaas-check"
    serviceMap["cce-health-check"]="./services/healthcheck/detector"
    serviceMap["cce-db-sync-controller"]="./services/cluster/db-sync-controller"
    serviceMap["cce-weekly-report"]="./services/devops/cce-weekly-report"
    serviceMap["cce-workflow-controller"]="./services/workflow/workflow-controller"
    serviceMap["cce-ai-service"]="./services/ai/cce-ai-service"
    serviceMap["cce-master-lb-controller"]="./services/cluster/cce-master-lb-controller"
    serviceMap["cce-artifact-service"]="./services/devops/cce-artifact-service"

    rm -rf output
    mkdir output
    for name in ${!serviceMap[*]}
    do
        path=${serviceMap[${name}]}
        bin_path="${path}/${name}"

        echo "==========Package begin: name=${name} path=${path} bin_path=${bin_path}============"
        mkdir -p output/${name}
        cp ${path}/Dockerfile output/${name}/

        if [ ${name} = "cce-base" ]; then
            cp -r ${path}/bins output/${name}/
        elif [ ${name} = "cce-plugin-helm-chart" ]; then
            mkdir -p output/${name}/helm
            cp -r ${path}/helm/charts output/${name}/helm/
        elif [ ${name} = "cce-grafana-provisioning" ]; then
            cp -r ${path}/dashboards output/${name}/
            cp -r ${path}/datasources output/${name}/
            cp -r ${path}/notifiers output/${name}/
            cp -r ${path}/plugins output/${name}/
        else
            # 编译二进制
            # -trimpath: 去掉该参数, 不同路径相同代码, 编译出来二进制 MD5 不一样
            CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -trimpath -o ${path}/${name} ${path}/
            if [[ $? -ne 0 ]]; then
                echo "[----------'GO build failed!----------]"
                echo "Command: CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o ${path}/${name} ${path}/"
                exit -1
            fi
            cp ${path}/${name} output/${name}/
            if [ ${name} = "k8s-event-collector" -o ${name} = "k8s-report-collector" -o ${name} = "cce-status-alert" ]; then
                cp -r ${path}/conf output/${name}/
            fi
            if [ ${name} = "cce-alert-rules-apply" ]; then
                cp -r ${path}/*.yaml output/${name}/
            fi
            if [ ${name} = "cce-e2e-test" ]; then
                cp ${path}/testlink-3.1.0.tgz output/${name}/
            fi
            if [ ${name} = "cce-monitor-service" ]; then
                cp -r ${path}/configs output/${name}/
            fi
        fi
        echo "==========Package success: name=${name}======="
    done

    echo "==========Package begin: chart files============"
    mkdir -p output/infra
    cp -r infra/service output/infra/

    cp -r infra/managed_pro output/infra/
    echo "==========Package success: chart files======="
}

parameter=${1:-build}
if [[ $parameter == "quick" ]];then
    package_deploy
else
    package_deploy
fi