.cursor
.svn
.tmp
.download
output
.*.swp
.*.swo
.*.xml
*/*.dev.conf
*/*.dev.json
.history/
bin/manager
test/cover_output
.idea
cover.html
.vscode
.DS_Store
chen
bin
config
log
config.json
cce-cluster-controller
cce-hi-webhook
cce-cluster-service
cce-app-service
cce-network-result
monitor-service-discovery
cce-network-inspector-log
cce-monitor-service
cce-cluster-sync
cluster-status-alert
cce-e2etest-client
e2etest-client
conf/*
services/monitor/monitor-service/conf/*
vendor/
chen.cover
chen.html
cce-e2e-test
cce-plugin-client
cce-plugin-helm-charts
health_check
services/monitor/k8s-report-collector/local-run.go
services/cluster/cce-oidc-provider/cce-oidc-provider
services/devops/cce-palo-collector/cce-palo-collector
services/devops/cce-status-alert/cce-status-alert
services/gateway/cce-gateway/cce-gateway
services/monitor/cce-alert/alert-rules/cce-alert-rules-apply
services/monitor/cce-log-agent/cce-log-agent
services/monitor/cce-log-controller/cce-log-controller
services/monitor/cce-log-operator/cce-log-controller
services/monitor/cce-log-operator/cce-log-operator
services/devops/cce-config-manager/cce-config-manager
services/devops/cce-iaas-check/cce-iaas-check
pkg/plugin/helm/charts/cce-ingress-controller/cce-ingress-controller
services/network/ingress-controller/cce-ingress-controller
services/network/lb-controller/cce-lb-controller
services/scheduler/reloader/master-reloader
services/admintask/cluster-trans/cce-cluster-trans
services/admintask/fix-bid-instance/fix-bid-instance
services/admintask/cluster-trans/debug
cce-file-distribution
cce-node-check
services/devops/cce-configmap-manager/cce-config-manager
chenhuan
yezichao
services/network/inspector/client/cce-network-inspector
services/cluster/cce-validate-webhook/cce-validate-webhook
services/healthcheck/detector/cce-health-check
services/alert/reciever/dingding/webhook/alert-dingding-webhook
services/alert/reciever/bcm/webhook/alert-bcm-webhook
services/monitor/master-service-discovery/cce-master-service-discovery
services/cluster/master-pro/proxy/proxy
services/cluster/master-pro/cipher/cipher
_output
services/scheduler/dysched-extender/deploy/bin/*
services/devops/cce-devops-backend/cce-devops-backend
services/monitor/cce-alert/cmd/webhook/cce-alertmanager-webhook
services/monitor/cce-alert/cmd/collector/cce-alert-collector
services/app/image/cce-image-plugin
pkg/plugin/cce-plugin-helm-chart
pkg/bcesdk/internalbls/test/e2e
services/cluster/instance-polling/cce-instance-polling
services/cluster/instance-gc-manager/cce-instance-gc-manager
services/webhook/cce-validate-webhook/cce-validate-webhook
services/webhook/cce-validate-webhook/ai-validate-webhook
services/monitor/master-service-discovery/cmd/control-plane-targets/control-plane-targets
services/monitor/master-service-discovery/cmd/control-plane-targets/cce-control-plane-targets
services/monitor/k8s-event-collector/k8s-event-collector
services/monitor/k8s-report-collector/k8s-report-collector
services/webhook/cpuoffline-mutation-webhook/cmd/mutation-webhook/cpuoffline-mutation-webhook
services/webhook/cpuoffline-mutation-webhook/cmd/inject-cpu-offline/cpuoffline-inject
services/scheduler/cpuoffline-agent/cpuoffline-agent
services/observability/monitor-controller/monitor-controller
services/monitor/external-auditer-v2/external-auditer
services/devops/cce-system-config-sync/config-init
services/admintask/freeze-v1-cluster/cce-freeze-cluster
services/admintask/v1-to-v2-pre-check/v1-to-v2-pre-check
services/admintask/change-external-auditor-configmap/cce-change-external-auditor-configmap
services/admintask/fix-instance-zero-cpu-mem/fix-instance-zero-cpu-mem
services/monitor/master-service-discovery/cmd/telegraf-conf-render/telegraf-conf-render
services/scheduler/cgroup-agent/cgroup-agent
services/hybrid/node-sla-controller/cce-node-sla-controller
services/workflow/workflow-controller/cce-workflow-controller
services/monitor/external-auditer/kube-external-auditor
services/monitor/external-auditer/kube-external-auditer
services/observability/services/cprom-service/cprom-service
services/observability/services/cprom-manager/cprom-manager
services/observability/services/cprom-controller/cprom-controller
services/observability/services/cprom-sidecar/cprom-sidecar
services/observability/services/alert-hook/alert-hook
services/workflow/cce-agent/cce-agent
services/devops/cce-weekly-report/demo/pull_icode/cce-stack
services/devops/cce-weekly-report-code/cce-stack
services/devops/cce-weekly-report-code/ccr-stack
services/devops/cce-weekly-report-code/cluster-autoscaler
services/devops/cce-weekly-report-code/baiducloud-cce-csi-driver
services/devops/cce-weekly-report-code/baiducloud-cce-cni-driver
services/devops/cce-weekly-report-code/api-logic-bci
services/devops/cce-weekly-report/cce-weekly-report
services/devops/cce-artifact-service/cce-artifact-service
services/app/dashboard-metrics-scraper/metrics-server-scraper
services/cluster/db-sync-controller/cce-db-sync-controller
services/cluster/cce-cloud-node-controller/cce-cloud-node-controller
services/monitor/master-service-discovery/cmd/prom-checker/prom-checker
services/stability/demo/apiserver-oom/apiserver-oom
services/helm/helm-service/testdata
services/helm/helm-service/cce-helm-service
services/monitor/cce-alert/alert-rules/alert-rules
services/monitor/cce-log-operator/bin
services/serverless/etcd-manager/cmd/etcd-manager/cce-etcd-manager
services/cluster/master-pro/loki-proxy/loki-proxy
services/cluster/master-pro/loki-pusher/loki-pusher
services/cluster/master-pro/event-forward/event-forward
services/monitor/problem-detector-service/problem-detector-service
services/monitor/problem-detector-service/mockserver/bcm-mock-service
services/ai/volcano-node-controller/volcano-node-controller
services/tests/e2etest/*.log
services/cluster/cce-master-lb-controller/cce-master-lb-controller