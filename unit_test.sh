#!/bin/bash

### 生成目录
moduleFile=chenhuan.file
rm -f ${moduleFile}

for file in $(ls pkg | grep -v models)
do
    path="./pkg/${file}"
    if [ -d ${path} ];
    then
        echo ${path} >> ${moduleFile}
    fi
done

for dir in $(ls services)
do
    for file in $(ls services/${dir})
    do
        path="./services/${dir}/${file}"
        if [ -d ${path} ];
        then
            echo ${path} >> ${moduleFile}
        fi
    done
done

### 按 package 生成单测覆盖
result=unit_test.cover
rm -f ${result}
for module in $(cat ${moduleFile} | grep -v kunlun | grep -v gateway | grep -v helm-service | grep -v cni-suite | grep -v lb-controller-idc | grep -v 'services/storage/util')
do
    echo "UnitTest in ${module}"

    find ${module} -name '*.go' | grep 'go' &> /dev/null
    if [ $? -ne 0 ]; then 
        echo "No go file in ${module}, skip"
        continue
    fi

    go test -v -covermode=count -coverprofile=./chen.cover ${module}/... &>/dev/null 
    if [ $? -ne 0 ]; then
        echo "UnitTest in ${module} failed"
        exit -1
    fi

    percent=$(go tool cover -func=chen.cover  | tail -1 | awk '{print $3}')
    echo "${module}: ${percent}" >> ${result}
done

rm -f chenhuan.file
rm -f chen.cover
