## 组件名称

CCE Log Operator

## 组件介绍

CCE 日志管理组件，支持用户通过 CRD 的方式创建日志规则，并在 container 上添加相关的
环境变量从而实现容器日志的采集，[使用文档参考](https://cloud.baidu.com/doc/CCE/s/gjwvy1a0k) 。

## 组件功能

1. 支持 CRD 创建规则

2. 支持 `百度云 ES`、`用户自建 ES`、`百度云 BOS` 后端存储

3. 支持用户在 container 上添加 `cce_log_internal` 设置环境变量采集指定文件的 log，支持 `cce_log_stdout` 环境变量采集容器终端输出的日志

## 使用场景

集群日志采集。

## 部署情况

对象名称 | 所属namespace | 所占资源 | 类型
--- | --- | --- | ---
cce-log-operator | kube-system | - | Deployment
cce-log-agent-v2 | kube-system | - | DaemonSet
logrules.cce.io | - | - | CRD
logconfig.cce.baidubce.com | - | - | CRD

## 版本记录

版本号 | 适配集群版本 | 更新时间       | 更新内容                                    | 影响
--- | --- |------------|-----------------------------------------| ---
2.0.0 | CCE/v1.14+ | 2021.07.05 | 首次上线                                    | -
2.2.1 | CCE/v1.14+ | 2021.11.15 | 适配 containerd                           | -
2.2.2 | CCE/v1.14+ | 2022.06.27 | 代码重构                                    | -
2.2.3 | CCE/v1.14+ | 2022.10.17 | 升级CRD 版本 v1beta1 => v1                  | -
2.3.1 | CCE/v1.14+ | 2022.12.05 | 支持 BLS                                  | -
2.4.0 | CCE/v1.14+ | 2023.10.18 | 支持 BES                                  | -
2.4.1 | CCE/v1.14+ | 2023.10.20 | 修复日志乱码                                  | -
2.4.2 | CCE/v1.14+ | 2024.05.10 | 新建日志集创建默认索引                             | -
2.4.3 | CCE/v1.14+ | 2024.09.26 | 新增日志组功能                                 | -
2.4.4 | CCE/v1.14+ | 2024.09.26 | 新增容器级别索引：@tag_container_name            | -
2.4.5 | CCE/v1.14+ | 2025.01.26 | 升级logbeat版本至1.6.2                       | -
2.4.6 | CCE/v1.14+ | 2025.03.10 | 修复版本不兼容的问题                              | -
2.4.7 | CCE/v1.14+ | 2025.03.13 | 优化logbeat/logconfig 两部分请求BLS的逻辑         | -
2.4.8 | CCE/v1.14+ | 2025.05.20 | 新增自定义环境变量和配置 closeInactive              | -
2.4.9 | CCE/v1.14+ | 2025.06.20 | 新增 HaervestLimit 字段 BLS 侧日志组 Project 字段 | -
2.4.10 | CCE/v1.14+ | 2025.06.30 | 升级logbeat版本至1.6.7 | -
2.4.11 | CCE/v1.14+ | 2025.07.07 | 新增支持Pod Label | -