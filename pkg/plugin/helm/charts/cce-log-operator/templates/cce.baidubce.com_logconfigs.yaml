apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  annotations:
    controller-gen.kubebuilder.io/version: v0.7.0
  name: logconfigs.cce.baidubce.com
spec:
  group: cce.baidubce.com
  names:
    kind: LogConfig
    listKind: LogConfigList
    plural: logconfigs
    shortNames:
    - lgc
    singular: logconfig
  scope: Namespaced
  versions:
  - additionalPrinterColumns:
    - jsonPath: .spec.srcConfig.srcType
      name: TYPE
      type: string
    - jsonPath: .spec.dstConfig.dstType
      name: STORAGE
      type: string
    - jsonPath: .status.phase
      name: PHASE
      type: string
    - jsonPath: .metadata.creationTimestamp
      name: Age
      type: date
    name: v1
    schema:
      openAPIV3Schema:
        properties:
          apiVersion:
            description: 'APIVersion defines the versioned schema of this representation
              of an object. Servers should convert recognized schemas to the latest
              internal value, and may reject unrecognized values. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#resources'
            type: string
          kind:
            description: 'Kind is a string value representing the REST resource this
              object represents. Servers may infer this from the endpoint the client
              submits requests to. Cannot be updated. In CamelCase. More info: https://git.k8s.io/community/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
            type: string
          metadata:
            type: object
            properties:
              name:
                description: name
                maxLength: 54
                type: string
          spec:
            properties:
              dstConfig:
                properties:
                  accountID:
                    description: AccountID account id
                    type: string
                  besClusterID:
                    description: bes clusterId
                    type: string
                  besIndexPrefix:
                    description: bes index prefix
                    type: string
                  besIndexRolling:
                    description: 是否按设定频率生成新的 index, 可选值为 none:不滚动生成 index; day:每天0时生成;
                      week:每周一0时生成; month:每月1号0时生成
                    type: string
                  besIsPwChange:
                    description: bes 是否启用密码
                    type: boolean
                  besPasswd:
                    description: bes password
                    type: string
                  besUser:
                    description: bes user
                    type: string
                  dstType:
                    description: DstType -
                    type: string
                  logProject:
                    description: LogProject 日志组名称，默认为 "default"
                    type: string
                  logStore:
                    description: LogStore
                    maxLength: 64
                    type: string
                  rateLimit:
                    description: RateLimit 单位为 MB
                    maximum: 100
                    minimum: 1
                    type: integer
                  retention:
                    description: Retention retention
                    maximum: 90
                    minimum: 1
                    type: integer
                required:
                - dstType
                - rateLimit
                type: object
              srcConfig:
                properties:
                  closeInactive:
                    description: CloseInactive 指定关闭非活跃文件句柄的时间
                    type: string
                  dateFormat:
                    description: DateFormat 指定时间戳字段的时间解析格式，format格式参考https://docs.oracle.com/javase/7/docs/api/java/text/SimpleDateFormat.html
                      "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'Z"
                    type: string
                  filterExpr:
                    description: FilterExpr 日志匹配表达式，符合规则的日志，将被采集
                    type: string
                  ignoreEnvs:
                    description: MatchEnvs 容器环境忽略匹配的 env
                    items:
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  ignoreLabels:
                    description: IgnoreLabels 容器环境忽略 labels，具体同上
                    items:
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  ignorePodLabels:
                    description: IgnorePodLabels
                    items:
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  ignorePattern:
                    description: IgnorePattern SrcDir 下日志文件就忽略规则
                    type: string
                  logTime:
                    description: LogTime 日志时间，可选system, logTime, 分别表示使用系统时间和使用日志时间
                    type: string
                  logType:
                    description: LogType 只有 SrcType 字段为 container 时，LogType 才有用 可选值
                      stdout，internal，分别表示标准输出日志和容器内部日志
                    type: string
                  matchEnvs:
                    description: MatchEnvs 容器环境匹配的 env
                    items:
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  matchLabels:
                    description: MatchLabels 容器环境匹配 labels，这里的 label 对应 docker inspect
                      上的 label, 不是 pod 上的 label
                    items:
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  matchPodLabels:
                    description: MatchPodLabels
                    items:
                      properties:
                        key:
                          type: string
                        value:
                          type: string
                      required:
                      - key
                      - value
                      type: object
                    type: array
                  matchPattern:
                    description: MatchPattern SrcDir 下日志文件匹配规则
                    type: string
                  metaEnv:
                    description: MetaEnv 自定义环境变量，在日志集日志中以@tag_元数据形式展示
                    items:
                      type: string
                    type: array
                  metaLabel:
                    description: MetaLabel 自定义标签，在日志集日志中以@tag_元数据形式展示
                    items:
                      type: string
                    type: array
                  multilineRegex:
                    description: MultilineRegex 多行模式首行模式
                    type: string
                  processConfig:
                    description: ProcessConfig 对应 ProcessType 的 process 参数
                    properties:
                      KeepOriginal:
                        description: 'KeepOriginal 是否保留原日志 true: 保留原日志到 kafka 中的 @message
                          字段，bls 日志集中的 @raw 字段  false: 解析成功则不保留原日志'
                        type: boolean
                      customSeparator:
                        description: CustomSeparator 自定义分隔符，当Separator为custom时使用
                        type: string
                      dataType:
                        description: DataType 解析结果每列对应的数据类型，支持string/int/float/bool,
                          必须要与 Keys 一一对应
                        type: string
                      dateFormat:
                        description: DateFormat 时间格式，如yyyy-MM-dd HH:mm:ss.SSS
                        type: string
                      discardOnFailure:
                        description: 'DiscardOnFailure 日志解析失败是否丢弃 true: 丢弃 false:
                          返回原值 （投递 BES 需要解析日志内容并以 JSON 格式投递）'
                        type: boolean
                      field:
                        description: Field 待解析字段名
                        type: string
                      filterExpr:
                        description: FilterExpr 过滤表达式，如$status>=400
                        type: string
                      keys:
                        description: Keys 解析结果的列名，多个字段用逗号分隔；@message 为系统保留字，不允许设置为
                          key
                        type: string
                      kvKeyIndex:
                        description: KVKeyIndex key在正则匹配结果中的索引
                        type: integer
                      kvValueIndex:
                        description: KVValueIndex value在正则匹配结果中的索引
                        type: integer
                      quote:
                        description: Quote 分隔符场景可指定引用符，可选值包括：空，双引号"，单引号'和自定义
                        type: string
                      regex:
                        description: Regex 正则表达式
                        type: string
                      sampleLog:
                        description: SampleLog 解析日志样例, 解析后用于在 console 配置 keys 与 dataType
                        type: string
                      separator:
                        description: Separator 分隔符
                        type: string
                      timestampKey:
                        description: TimestampKey 时间戳字段名
                        type: string
                    type: object
                  processType:
                    description: ProcessType 解析类型
                    type: string
                  pipeline:
                    description: Pipeline 对应 ProcessType == pipeline 时，数据加工的参数
                    properties:
                      processors:
                        description: Processors 处理器列表
                        items:
                          description: ProcessorConfig 处理器配置
                          properties:
                            config:
                              description: Config 处理器配置
                              properties:
                                KeepOriginal:
                                  description: 'KeepOriginal 是否保留原日志 true: 保留原日志到
                                    kafka 中的 @message 字段，bls 日志集中的 @raw 字段  false:
                                    解析成功则不保留原日志'
                                  type: boolean
                                customSeparator:
                                  description: CustomSeparator 自定义分隔符，当Separator为custom时使用
                                  type: string
                                dataType:
                                  description: DataType 解析结果每列对应的数据类型，支持string/int/float/bool,
                                    必须要与 Keys 一一对应
                                  type: string
                                dateFormat:
                                  description: DateFormat 时间格式，如yyyy-MM-dd HH:mm:ss.SSS
                                  type: string
                                discardOnFailure:
                                  description: 'DiscardOnFailure 日志解析失败是否丢弃 true:
                                    丢弃 false: 返回原值 （投递 BES 需要解析日志内容并以 JSON 格式投递）'
                                  type: boolean
                                field:
                                  description: Field 待解析字段名
                                  type: string
                                filterExpr:
                                  description: FilterExpr 过滤表达式，如$status>=400
                                  type: string
                                keys:
                                  description: Keys 解析结果的列名，多个字段用逗号分隔；@message 为系统保留字，不允许设置为
                                    key
                                  type: string
                                kvKeyIndex:
                                  description: KVKeyIndex key在正则匹配结果中的索引
                                  type: integer
                                kvValueIndex:
                                  description: KVValueIndex value在正则匹配结果中的索引
                                  type: integer
                                quote:
                                  description: Quote 分隔符场景可指定引用符，可选值包括：空，双引号"，单引号'和自定义
                                  type: string
                                regex:
                                  description: Regex 正则表达式
                                  type: string
                                sampleLog:
                                  description: SampleLog 解析日志样例, 解析后用于在 console 配置
                                    keys 与 dataType
                                  type: string
                                separator:
                                  description: Separator 分隔符
                                  type: string
                                timestampKey:
                                  description: TimestampKey 时间戳字段名
                                  type: string
                              type: object
                            name:
                              description: Name 处理器名称
                              type: string
                          required:
                            - config
                            - name
                          type: object
                        type: array
                      sampleLog:
                        description: SampleLog 样例日志
                        type: string
                    type: object
                  recursiveDir:
                    description: RecursiveDir 是否递归采集 SrcDir 下满足 MatchPattern 的所有文件，包括子目录
                    type: boolean
                  srcDir:
                    description: SrcDir 日志采集目录，注意是目录，当 SrcType=Container, LogType=stdout
                      时 不需要指定 SrcDir
                    type: string
                  srcType:
                    type: string
                  timeFormat:
                    description: TimeFormat 用于投 BOS 时，原文件路径日期解析
                    type: string
                  timestampKey:
                    description: TimestampKey 指定解析后的字段作为日志时间
                    type: string
                  ttl:
                    description: TTL agent 采集日志时间范围
                    type: integer
                  useMultiline:
                    description: UseMultiline 是否启用多行模式
                    type: boolean
                  harvesterLimit:
                    type: integer
                    description: "日志采集器数量限制，默认为 1"
                required:
                - srcType
                - ttl
                type: object
            required:
            - dstConfig
            - srcConfig
            type: object
          status:
            properties:
              hosts:
                description: Hosts 当前 task 已绑定的 agent 列表
                items:
                  properties:
                    hostId:
                      type: string
                    hostName:
                      type: string
                    ip:
                      type: string
                    isAvailableForNewConfig:
                      type: boolean
                    status:
                      type: string
                    updatedTime:
                      type: string
                  type: object
                type: array
              lastApplyHash:
                description: LastApplyHash
                type: string
              lastApplyHostHash:
                description: LastApplyHostHash
                type: string
              lastApplySpecHash:
                description: LastApplySpecHash
                type: string
              lastProbeTime:
                format: date-time
                type: string
              lastTransitionTime:
                format: date-time
                type: string
              message:
                type: string
              phase:
                type: string
              ready:
                type: boolean
              taskID:
                description: TaskID bls 创建 task 成功后返回的 id
                type: string
            type: object
        type: object
    served: true
    storage: true
    subresources:
      status: {}
