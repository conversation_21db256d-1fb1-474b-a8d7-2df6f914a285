// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/04/21 15:50:00, by <EMAIL>, create
*/
/*
DESCRIPTION
Workflow 实现 workflow.Interface, 完成一个 Instance 部署流程
*/

package workflow

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/oos"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec"
	exectypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/sts"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
)

const (
	OOSTimeout = 240000
)

// Workflow - 实现 workflow.Interface Instance 部署流程
type Workflow_oos struct {
	config   *types.Config
	cluster  *types.Cluster
	instance *types.Instance

	execclient exec.Interface
	roleclient ClusterRoleInterface

	//oosclient *oos.Client
	oosclient oos.OOSInterface
	stsClient sts.Interface

	workflowType ccetypes.InstanceDeployType
}

// DecideDeployType - get decideDeployType，oos or ssh
//
// PARAMS:
//   - ctx: context to trace request
//
// RETURNS:
//
//	exec.InstanceDeployType: oos or ssh
func (w *Workflow_oos) DecideDeployType(ctx context.Context) ccetypes.InstanceDeployType {
	return w.workflowType
}

// 不再只能挂载nvme的本地盘，所有的磁盘类型都在这里和单机的meta-api数据做匹配
func (w *Workflow_oos) ensureGuestDiskConfigsIfNeed(ctx context.Context, configs []*types.DiskMountConfig) error {
	logger.Infof(ctx, "want to mount info %+v,start to find local disks from meta-api", &configs)

	//使用meta-api获取虚机内有多少实际的盘和类型
	command := exectypes.Command{
		Command:  commandListDisk,
		Retry:    20,
		Interval: 4 * time.Second,
		Timeout:  120 * time.Second,
	}
	stdout, _, err := w.execclient.Exec(ctx, &command)
	if err != nil {
		logger.Errorf(ctx, "findLocalDisks err: %s", err)
		return err
	}

	diskSerial := make(map[string]DiskInfo, 0)

	if err := json.Unmarshal([]byte(stdout), &diskSerial); err != nil {
		logger.Errorf(ctx, "get disk serial from meta-api json unmarshal err: %s", err)
		return err
	}

	//hpas当前没有接口，套餐一致，临时按照固定格式匹配
	if w.instance.Machinetype == ccetypes.MachineTypeHPAS {
		//hpas实例查找本地盘，hpas只有nvme
		commandNvme := exectypes.Command{
			Command: commandListNVME,
		}
		nvmeStdout, _, err := w.execclient.Exec(ctx, &commandNvme)
		if err != nil {
			logger.Errorf(ctx, "findLocalDisks err: %s", err)
			return err
		}
		nvmeDevices := strings.Split(strings.TrimSpace(nvmeStdout), "\n")
		nvmeMap := make(map[string]bool)

		// 构建 map（空结构体节省内存）
		for _, dev := range nvmeDevices {
			dev = strings.TrimSpace(dev) // 确保去除空格
			if dev != "" {               // 忽略空行
				nvmeMap[dev] = false
			}
		}

		if len(nvmeMap) == 0 {
			logger.Errorf(ctx, "has no local disk")
			return fmt.Errorf("has no local disk")
		}

		if len(configs) > len(nvmeMap) {
			logger.Errorf(ctx, "need mount local dick more than already has")
			return fmt.Errorf("need mount local dick more than already has")
		}

		for i := range configs {
			if configs[i].Device != "" {
				if _, ok := nvmeMap[configs[i].Device]; ok {
					nvmeMap[configs[i].Device] = true
				}
			} else {
				findCanMountList := findCanMount(nvmeMap)
				configs[i].Device = findCanMountList[len(findCanMountList)-1]
			}
		}

		return nil
	}

	for serial, diskInfo := range diskSerial {
		fillSN(diskInfo, serial, configs)
	}

	//单机查询所有盘的短ID，然后填充到配置中
	diskLinkID, err := w.FindDiskByIDFromOS(ctx, diskSerial)
	if err != nil {
		logger.Errorf(ctx, "FindDiskByIDFromOS err: %s", err)
		return err
	}
	//获取对应的设备名称，比如vdb，vdc等，然后填充到配置中
	fillDevPath(ctx, configs, diskLinkID)
	b, err := json.Marshal(configs)
	if err != nil {
		logger.Errorf(ctx, "failed to marshal configs: %v", err)
		return err
	}

	// 打印到日志
	logger.Infof(ctx, "mount configs is: %s", string(b))
	logger.Infof(ctx, "fill dev path config is %+v", configs, diskLinkID)
	for _, config := range configs {
		logger.Infof(ctx, "can not find dev path for %s, %s, %s, %s", config.Device, config.VolumeID, config.SN, config.Path)
	}
	return nil
}

func findCanMount(nvmeMap map[string]bool) []string {
	canMountList := []string{}
	for k, v := range nvmeMap {
		if !v {
			canMountList = append(canMountList, k)
		}
	}
	sort.Strings(canMountList)
	return canMountList
}

func fillDevPath(ctx context.Context, configs []*types.DiskMountConfig, id []DiskWithPath) {
	for _, config := range configs {
		for _, disk := range id {
			logger.Infof(ctx, "disk.LinkFileName: %s", disk.LinkFileName, config.VolumeID)
			if config.VolumeID != "" && strings.Contains(disk.LinkFileName, config.VolumeID) {
				logger.Infof(ctx, "disk.Path: %s", disk.LinkFileName, config.VolumeID)
				config.Device = fmt.Sprintf("/dev%s", disk.Path)
				break
			} else if config.SN != "" && strings.Contains(disk.LinkFileName, config.SN) {
				config.Device = fmt.Sprintf("/dev%s", disk.Path)
				break
			}
		}
	}
}

func fillSN(info DiskInfo, serial string, configs []*types.DiskMountConfig) {
	for _, config := range configs {

		if config.VolumeID == "" {
			logger.Infof(context.TODO(), "config volume is %s", config.VolumeID)
			continue
		}
		if config.VolumeID == serial {
			config.SN = info.SN
		}
	}
}

// EnsureDiskMounted - 保证 Instance 磁盘挂载
// 磁盘挂载步揍:
// 1. check attached, 磁盘必须挂载到虚机才能做后续操作
// 2. 查询磁盘分区，如果有分区，则使用第一个分区mount，没有分区则使用整个磁盘
// 3. 检查该设备是否已mount，已mount则直接跳过
// 4. 查询该设备的文件系统类型，没有文件系统则创建
// 5. 创建挂载目录并将挂载信息写入/etc/fstab, 用于虚机重启自动挂载
func (w *Workflow_oos) EnsureDiskMounted(ctx context.Context) error {
	// checkExecution
	success, err := w.checkTaskExecution(ctx, "mountDisk")
	if err != nil {
		return err
	}
	if success {
		logger.Infof(ctx, "mountDisk success, skip")
		return nil
	}
	diskMountConfigs := w.instance.DiskMountConfigs
	if len(diskMountConfigs) == 0 {
		logger.Infof(ctx, "DiskMountConfigs is empty, skip mount disk")
		return nil
	}
	logger.Infof(ctx, "diskMountConfigs ensureGuestDiskConfigsIfNeed: %+v", diskMountConfigs)
	if err := w.ensureGuestDiskConfigsIfNeed(ctx, diskMountConfigs); err != nil {
		logger.Errorf(ctx, "ensureGuestDiskConfigsIfNeed failed: %s", err)
		return err
	}
	logger.Infof(ctx, "diskMountConfigs after: %+v", diskMountConfigs)
	scripts, err := getDiskMountedScripts(ctx, diskMountConfigs, string(w.instance.InstanceOS.OSName), w.instance.InstanceOS.OSVersion)
	if err != nil {
		logger.Errorf(ctx, "getDiskMountedScripts err: %v", err)
		return err
	}

	logger.Infof(ctx, "oos Tasks begin %s", "mountDisk")
	err = w.ExecOOSTask(ctx, scripts, "mountDisk")
	if err != nil {
		logger.Errorf(ctx, "mountDisk err: %s", err)
		return err
	}
	return nil
}

func (w *Workflow_oos) FindDiskByIDFromOS(ctx context.Context, diskSerial map[string]DiskInfo) ([]DiskWithPath, error) {

	findDiskByID := exectypes.Command{
		Command:  "bash -c 'ls /dev/disk/by-id'",
		Retry:    20,
		Interval: 4 * time.Second,
		Timeout:  120 * time.Second,
	}

	stdout, _, err := w.execclient.Exec(ctx, &findDiskByID)
	if err != nil {
		logger.Errorf(ctx, "findLocalDisks err: %s", err)
		return nil, err
	}
	var disksWithPath []DiskWithPath

	diskDevices := strings.Split(strings.TrimSpace(stdout), "\n")
	logger.Infof(ctx, "diskDevices: %v", diskDevices)

	//通过匹配盘符和盘id的方式找到对应关系，以及具体的设备
	for _, diskDevice := range diskDevices {
		diskDevice = strings.TrimSpace(diskDevice)
		diskWithPath := DiskWithPath{}
		if strings.Contains(diskDevice, "part") {
			continue
		}
		path := fmt.Sprintf("/dev/disk/by-id/%s", diskDevice)

		readLink := exectypes.Command{
			Command:  "readlink " + path,
			Retry:    20,
			Interval: 4 * time.Second,
			Timeout:  120 * time.Second,
		}

		stdout, _, err := w.execclient.Exec(ctx, &readLink)
		if err != nil {
			logger.Errorf(ctx, " err: %s", err)
			return nil, err
		}
		logger.Infof(ctx, "diskDevice stdout: %s", stdout)

		//找到实际对应的盘符
		realPath := exectypes.Command{
			Command:  "realpath " + stdout,
			Retry:    20,
			Interval: 4 * time.Second,
			Timeout:  120 * time.Second,
		}

		stdout, _, err = w.execclient.Exec(ctx, &realPath)
		if err != nil {
			logger.Errorf(ctx, " err: %s", err)
			return nil, err
		}
		logger.Infof(ctx, "absPath: %s", stdout)
		absPath := strings.Split(strings.TrimSpace(stdout), "\n")
		diskWithPath.LinkFileName = diskDevice
		diskWithPath.Path = absPath[0]
		disksWithPath = append(disksWithPath, diskWithPath)
	}
	return disksWithPath, nil

}

func getDiskMountedScripts(ctx context.Context, diskMountConfigs []*types.DiskMountConfig, osName string, osVersion string) (string, error) {

	for _, mountConfig := range diskMountConfigs {
		if mountConfig.Path == "" {
			logger.Errorf(ctx, "check disk config failed, mountConfig.Path is empty")
			return "", fmt.Errorf("check disk config failed, mountConfig.Path is empty")
		}
	}
	logger.Infof(ctx, "diskMountConfigs: %+v", diskMountConfigs)
	sort.Slice(diskMountConfigs, func(i, j int) bool {
		return diskMountConfigs[i].Device > diskMountConfigs[j].Device
	})
	logger.Infof(ctx, "diskMountConfigs: %+v", diskMountConfigs)

	diskMap := map[string][]string{}
	for i := 0; i < len(diskMountConfigs); i++ {
		diskInfoArr := []string{
			"\"" + diskMountConfigs[i].ID + "\"",
			"\"" + diskMountConfigs[i].VolumeID + "\"",
			"\"" + diskMountConfigs[i].Device + "\"",
			"\"" + diskMountConfigs[i].Path + "\"",
			"\"" + strconv.FormatBool(diskMountConfigs[i].NeedFormat) + "\"",
			"\"" + strconv.FormatBool(diskMountConfigs[i].Local) + "\"",
		}
		diskMap[fmt.Sprintf("arr_%d", i)] = diskInfoArr
	}
	diskStringMap := map[string]string{}
	// 处理键值对
	for key, value := range diskMap {
		arrString := "(" + strings.Join(value, " ") + ")"
		diskStringMap[key] = arrString
	}
	// CentOS7, Ubuntu: mkfs.ext4 /dev/vdb1
	// CentOS6: mkfs.ext4 -i 65536 /dev/vdb1
	mkfsCmd := "mkfs.ext4"
	if strings.EqualFold(string(osName), string(bccimage.OSNameCentOS)) &&
		strings.Contains(osVersion, "6.") {
		mkfsCmd = "mkfs.ext4 -i 65536"
	}

	// 替换模板变量
	tmpl, err := template.New("EnsureDiskMountedShell").Parse(mountDiskTmpl)
	if err != nil {
		logger.Errorf(ctx, "Template master-csr.json failed: %s", err)
		return "", err
	}

	var buf bytes.Buffer
	if err := tmpl.Execute(&buf, struct {
		DiskInfoLength int
		DiskInfo       map[string]string
		MkfsCmd        string
	}{
		DiskInfoLength: len(diskMountConfigs),
		DiskInfo:       diskStringMap,
		MkfsCmd:        mkfsCmd,
	}); err != nil {
		logger.Errorf(ctx, "tmpl.Execute failed: %s", err)
		return "", err
	}
	logger.Infof(ctx, "EnsureDiskMountedShell string: %s", buf.String())

	return buf.String(), nil
}

// DeliverPackage - 安装包分发, 包含二进制, 驱动, 配置等
func (w *Workflow_oos) DeliverPackage(ctx context.Context) error {
	success, err := w.checkTaskExecution(ctx, "deliver_package")
	if err != nil {
		return err
	}
	if success {
		logger.Infof(ctx, "deliver_package success, skip")
		return nil
	}

	taskList, err := w.roleclient.DeliverPackage(ctx)
	if err != nil {
		logger.Errorf(ctx, "DeliverPackage failed: %s", err)
		return err
	}

	if err := w.execTask(ctx, taskList, "deliver_package"); err != nil {
		logger.Errorf(ctx, "ExecTask in DeliverPackage failed: %s", err)
		return err
	}

	return nil
}

// EnsureSystemInited - 保证系统初始化完成
func (w *Workflow_oos) EnsureSystemInited(ctx context.Context) error {
	success, err := w.checkTaskExecution(ctx, "ensure_system_inited")
	if err != nil {
		return err
	}
	if success {
		logger.Infof(ctx, "ensure_system_inited success, skip")
		return nil
	}

	taskList, err := w.roleclient.SystemInitTaskList(ctx)
	if err != nil {
		logger.Errorf(ctx, "SystemInitTaskList failed: %s", err)
		return err
	}

	if err := w.execTask(ctx, taskList, "ensure_system_inited"); err != nil {
		logger.Errorf(ctx, "ExecTask in EnsureSystemInited failed: %s", err)
		return err
	}

	return nil
}

// EnsureK8SServiceRunning - 保证 K8S 组件正常运行
func (w *Workflow_oos) EnsureK8SServiceRunning(ctx context.Context) error {
	success, err := w.checkTaskExecution(ctx, "ensure_k8s_service_running")
	if err != nil {
		return err
	}
	if success {
		logger.Infof(ctx, "ensure_k8s_service_running success, skip")
		return nil
	}

	taskList, err := w.roleclient.K8SServiceTaskList(ctx)
	if err != nil {
		logger.Errorf(ctx, "K8SServiceTaskList failed: %s", err)
		return err
	}

	if err := w.execTask(ctx, taskList, "ensure_k8s_service_running"); err != nil {
		logger.Errorf(ctx, "ExecTask in EnsureK8SServiceRunning failed: %s", err)
		return err
	}

	return nil
}

func (w *Workflow_oos) EnsurePostUserScript(ctx context.Context) error {
	success, err := w.checkTaskExecution(ctx, "ensure_post_user_script")
	if err != nil {
		return err
	}
	if success {
		logger.Infof(ctx, "ensure_post_user_script success, skip")
		return nil
	}

	taskList, err := w.roleclient.PostUserScriptList(ctx)
	if err != nil {
		logger.Errorf(ctx, "PostUserScriptList failed: %s", err)
		return err
	}

	if err := w.execTask(ctx, taskList, "ensure_post_user_script"); err != nil {
		logger.Errorf(ctx, "ExecTask in PostUserScriptList failed: %s", err)
		return err
	}

	return nil
}

// Clean - 清理部署环境
func (w *Workflow_oos) Clean(ctx context.Context) error {
	success, err := w.checkTaskExecution(ctx, "clean")
	if err != nil {
		return err
	}
	if success {
		logger.Infof(ctx, "clean success, skip")
		return nil
	}

	taskList, err := w.roleclient.Clean(ctx)
	if err != nil {
		logger.Errorf(ctx, "Clean failed: %s", err)
		return err
	}

	if err := w.execTask(ctx, taskList, "clean"); err != nil {
		logger.Errorf(ctx, "ExecTask in Clean failed: %s", err)
		return err
	}

	return nil
}

func (w *Workflow_oos) checkTaskExecution(ctx context.Context, taskName string) (success bool, err error) {
	logger.Infof(ctx, "check tasks Execution")

	// 如果是重装流程，则跳过检测，需要重新执行部署流程。
	if w.instance.Rebuild {
		return false, nil
	}

	GetOOSEXEListRequest := &oos.OOSExecutionListRequest{
		Sort:      "startTime",
		Ascending: false,
		Template: oos.TemplateList{
			//Type: "INDIVIDUAL",
			Name: w.getOOSTaskName(taskName),
		},
		//State:     "RUNNING",
		PageNo:   1,
		PageSize: 10,
	}

	logger.Infof(ctx, "GetOOSEXEListRequest: %v", GetOOSEXEListRequest)
	list, err := w.oosclient.GetOOSExecutionListV2(ctx, GetOOSEXEListRequest, w.stsClient.NewSignOption(ctx, w.cluster.AccountID))
	if err != nil || list == nil {
		logger.Errorf(ctx, "GetOOSExecutionListV2 failed: %s", err)
		// 如果查询错误忽略，尝试创建新任务
		return false, nil
	}
	if list.Result.TotalCount == 0 {
		logger.Infof(ctx, "GetOOSExecutionListV2 return list.Result.TotalCount == 0")
		// 未执行
		return false, nil
	}
	if list.Result.TotalCount > 0 {
		// 如果查到，则检查最近一条状态
		if len(list.Result.Executions) == 0 {
			logger.Infof(ctx, "GetOOSExecutionListV2 return len(list.Result.Executions) == 0")
			return false, nil
		}
		executionId := list.Result.Executions[0].Id

		maxRetry := 15
		for i := 0; i < maxRetry; i++ {
			executionDetail, err := w.oosclient.GetOOSExecutionV2(ctx, executionId, w.stsClient.NewSignOption(ctx, w.cluster.AccountID))
			if err != nil {
				msg := fmt.Sprintf("get task: %s execution err: %v", executionId, err)
				logger.Warnf(ctx, msg)
				return false, nil
			}
			executionDetailJson, err := json.Marshal(executionDetail)
			if err != nil {
				logger.Warnf(ctx, "Marshal execution detail err: %v", err)
				time.Sleep(1 * time.Second)
				continue
			}
			logger.Infof(ctx, "execution detail json: %s", string(executionDetailJson))
			if len(executionDetail.Result.Tasks) == 0 {
				logger.Infof(ctx, "task %s execution err: Tasks is nil", executionId)
				time.Sleep(1 * time.Second)
				continue
			}
			if executionDetail.Result.Tasks[0].State == "SUCCESS" {
				logger.Infof(ctx, "task %s execution is done", executionId)
				if executionDetail.Result.Tasks[0].OutputContext.ExitCode == 0 {
					return true, nil
				} else {
					// 如果执行结果非0，重试
					logger.Errorf(ctx, "task %s execution err: exit code: %d", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode)
					logger.Errorf(ctx, "stderr is %s", executionDetail.Result.Tasks[0].OutputContext.Stderr)
					logger.Errorf(ctx, "retry")
					return false, nil
				}
			}
			if executionDetail.Result.Tasks[0].State == "FAILED" {
				logger.Errorf(ctx, "task %s execution FAILED: exit code: %d", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode)
				logger.Errorf(ctx, "stderr is %s", executionDetail.Result.Tasks[0].OutputContext.Stderr)
				return false, nil
			}

			logger.Infof(ctx, "wait task %s, state is %s", executionId, executionDetail.Result.Tasks[0].State)
			if i == (maxRetry - 1) {
				logger.Errorf(ctx, fmt.Sprintf("timeout for execution: {%s}", executionId))
				return false, errors.New(fmt.Sprintf("timeout for execution: {%s}", executionId))
			}
			time.Sleep(1 * time.Second)
		}
	}
	return false, nil
}

func (w *Workflow_oos) getOOSTaskName(OOSTaskName string) string {
	// cce-machine-id  bcc-id
	return fmt.Sprintf("%s-%s-%s", w.instance.CCEInstanceID, w.instance.MachineID, OOSTaskName)
}

// ExecTaskWithOOS - 执行 Task, 分成两部分:
// 1. 生成脚本;
// 2. 下发执行.
func (w *Workflow_oos) execTask(ctx context.Context, tasks []types.TaskInterface, OOSTaskName string) error {
	// 1. 查询task是否存在，查询 task 状态
	// 2. 渲染task配置
	if len(tasks) == 0 {
		return nil
	}
	logger.Infof(ctx, "oos Tasks begin %s", OOSTaskName)
	scripts, err := w.getTaskScrips(ctx, tasks)
	if err != nil {
		logger.Errorf(ctx, "getTaskScripts failed: %s", err)
		return err
	}
	err = w.ExecOOSTask(ctx, scripts, OOSTaskName)
	if err != nil {
		logger.Errorf(ctx, "ExecOOSTask failed: %s", err)
		return err
	}
	return nil
}

func (w *Workflow_oos) getTaskScrips(ctx context.Context, tasks []types.TaskInterface) (string, error) {
	scripts := []string{"#!/bin/bash"}
	//scripts = append(scripts, fmt.Sprintf("echo \"Starting execution of command at $(date)\""))
	// 渲染 task
	for _, task := range tasks {
		if task == nil {
			continue
		}
		logger.Infof(ctx, "Tasks name,%s", task.Name(ctx))

		// 配置分发
		configs, err := task.Configs(ctx, w.config, w.cluster, w.instance)
		if err != nil {
			logger.Infof(ctx, "Task %s getconfigs failed: %s", task.Name(ctx), err)
			return "", err
		}
		if configs != nil {
			for _, config := range configs {
				if config == nil {
					logger.Infof(ctx, "config is nil")
					continue
				}

				tt, err := w.execclient.GetWriteContents(ctx, config)
				if err != nil {
					logger.Infof(ctx, "Write failed: %s", err)
					return "", err
				}
				if scripts != nil {
					scripts = append(scripts, tt...)
				}
			}
		}

		// 命令执行
		commands, err := task.Commands(ctx, w.config, w.cluster, w.instance)
		if err != nil {
			logger.Infof(ctx, "Task %s get commands failed: %s", task.Name(ctx), err)
			return "", err
		}
		if commands != nil {
			for _, command := range commands {
				if command == nil {
					continue
				}
				commandScripts, _, err := w.execclient.GeneratorScript(ctx, command)
				if err != nil {
					logger.Infof(ctx, "get Exec scripts %s failed: err='%s'", command.Command, err)
					return "", err
				}
				scripts = append(scripts, commandScripts)

			}
		}

	}
	//scripts = append(scripts, fmt.Sprintf("echo \"end execution of command at $(date)\""))
	scriptString := strings.Join(scripts, "\n") + "\n"

	scriptString = strings.Replace(scriptString, "`", "\\`", -1)
	return scriptString, nil
}

func (w *Workflow_oos) CheckOOSClientOnline(ctx context.Context) (online bool, err error) {
	var OOSDefaultRetry = wait.Backoff{
		Steps:    4,
		Duration: 5 * time.Second,
		Factor:   1.2,
		Jitter:   0.1,
	}
	var getOOSAgentArgs = &oos.GetOOSAgentRequest{
		Agents: []string{w.instance.InstanceUUID},
		Instances: []oos.AgentInstance{
			{
				ID:         w.instance.InstanceUUID,
				InstanceId: w.instance.MachineID,
			},
		},
	}

	var fn = func() (bool, error) {
		agents, err := w.oosclient.GetOOSAgent(ctx, getOOSAgentArgs, w.stsClient.NewSignOption(ctx, w.cluster.AccountID))
		logger.Infof(ctx, "get bsm agent: %v, err: %s", agents, err)
		if agents == nil || err != nil {
			logger.Errorf(ctx, "get bsm agent failed: %v", err)
			return false, nil
		}
		if len(agents.Result.Agents) == 0 {
			logger.Errorf(ctx, fmt.Sprintf("oos anget not found for instance: %s", w.instance.MachineID))
			return false, nil
		}

		if agents.Result.Agents[0].State == "ONLINE" {
			logger.Infof(ctx, fmt.Sprintf("agent %s, %s is ONLINE", w.instance.MachineID, w.instance.InstanceUUID))
			return true, nil
		} else if agents.Result.Agents[0].State != "ONLINE" {
			logger.Errorf(ctx, fmt.Sprintf("agent %s, %s is OFFLINE", w.instance.MachineID, w.instance.InstanceUUID))
			return false, nil
		}
		return false, fmt.Errorf(fmt.Sprintf("agent %s, %s is OFFLINE", w.instance.MachineID, w.instance.InstanceUUID))
	}

	// 成功返回：(true, nil)
	// 失败重试返回：(false, nil)
	// 失败不重试返回：(false, error) or (true, error)
	retryErr := wait.ExponentialBackoff(OOSDefaultRetry, fn)

	if retryErr != nil {
		return false, retryErr
	}

	return true, nil
}

func (w *Workflow_oos) ExecOOSTask(ctx context.Context, scripts string, OOSTaskName string) error {

	online, err := w.CheckOOSClientOnline(ctx)
	if err != nil {
		logger.Errorf(ctx, "CheckOOSClientOnline err: %v", err)
		return err
	}
	if !online {
		logger.Errorf(ctx, "CheckOOSClientOnline err: %v", err)
		return errors.New("bsm agent is not online")
	}

	args, err := w.getCreateExecutionTaskRequest(ctx, scripts, OOSTaskName)
	if err != nil {
		logger.Errorf(ctx, "getCreateExecutionTaskRequest err: %v", err)
		return err
	}
	logger.Infof(ctx, "args: %v", args)
	detail, err := w.oosclient.CreateOOSExecutionV2(ctx, args, w.stsClient.NewSignOption(ctx, w.cluster.AccountID))
	if err != nil {
		logger.Errorf(ctx, "CreateOOSExecutionV2 err: %v", err)
		return err
	}
	if detail == nil || detail.Result.Id == "" {
		logger.Errorf(ctx, "CreateOOSExecutionV2 result is nil")
		return fmt.Errorf("CreateOOSExecutionV2 result is nil")
	}
	executionId := detail.Result.Id

	// 这里提交oos任务以后，最长等待任务成功时间为160s。
	maxRetry := 160
	for i := 0; i < maxRetry; i++ {
		//time.Sleep(4 * time.Second)
		executionDetail, err := w.oosclient.GetOOSExecutionV2(ctx, executionId, w.stsClient.NewSignOption(ctx, w.cluster.AccountID))
		if err != nil {
			msg := fmt.Sprintf("get task: %s execution err: %v", executionId, err)
			logger.Errorf(ctx, msg)
			return errors.New(msg)
		}
		executionDetailJson, err := json.Marshal(executionDetail)
		if err != nil {
			logger.Warnf(ctx, "Marshal execution detail err: %v", err)
			time.Sleep(1 * time.Second)
			continue
		}
		logger.Infof(ctx, "execution detail json: %s", string(executionDetailJson))
		if len(executionDetail.Result.Tasks) == 0 {
			logger.Infof(ctx, "task %s execution err: Tasks is nil", executionId)
			time.Sleep(1 * time.Second)
			continue
		}
		if executionDetail.Result.Tasks[0].State == "SUCCESS" {
			logger.Infof(ctx, "task %s execution is done", executionId)
			if executionDetail.Result.Tasks[0].OutputContext.ExitCode == 0 {
				break
			} else {
				logger.Errorf(ctx, "task %s execution err: exit code: %d", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode)
				logger.Errorf(ctx, "stderr is %s", executionDetail.Result.Tasks[0].OutputContext.Stderr)
				return errors.New(fmt.Sprintf("oos task %s, exit code: %d, stderr: %s", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode, executionDetail.Result.Tasks[0].OutputContext.Stderr))
			}
		}
		if executionDetail.Result.Tasks[0].State == "FAILED" {
			logger.Errorf(ctx, "task %s execution FAILED: exit code: %d", executionId, executionDetail.Result.Tasks[0].OutputContext.ExitCode)
			logger.Errorf(ctx, "stderr is %s", executionDetail.Result.Tasks[0].OutputContext.Stderr)
			return errors.New(fmt.Sprintf("oos task %s FAILED, failed reason: %s, exit code: %d, stderr: %s", executionId, executionDetail.Result.Reason, executionDetail.Result.Tasks[0].OutputContext.ExitCode, executionDetail.Result.Tasks[0].OutputContext.Stderr))
		}

		logger.Infof(ctx, "wait task %s, state is %s", executionId, executionDetail.Result.Tasks[0].State)
		if i == (maxRetry - 1) {
			logger.Errorf(ctx, fmt.Sprintf("retryCount is %v, maxRetry is %v, task {%s} has reached the maximum limit of retries", i+1, maxRetry, executionId))
			return errors.New(fmt.Sprintf("retryCount is %v, maxRetry is %v, task {%s} has reached the maximum limit of retries", i+1, maxRetry, executionId))
		}
		time.Sleep(1 * time.Second)
	}

	return nil
}

func (w *Workflow_oos) getCreateExecutionTaskRequest(ctx context.Context, scripts string, ExecutionName string) (*oos.CreateOOSExecutionRequestV2, error) {

	encodeString := base64.StdEncoding.EncodeToString([]byte(scripts))
	writeScripts := fmt.Sprintf("mkdir -p /deploy/ && echo '%s' | base64 -d > /deploy/%s.sh && chmod a+x /deploy/%s.sh && /deploy/%s.sh > /deploy/%s.log 2>&1",
		//writeScripts := fmt.Sprintf("mkdir -p /deploy/ && echo '%s' | base64 -d > /deploy/%s.sh && chmod a+x /deploy/%s.sh && /deploy/%s.sh",
		encodeString, ExecutionName, ExecutionName, ExecutionName, ExecutionName)
	oosScripts := []string{}
	oosScripts = append(oosScripts, writeScripts)
	oosScripts = append(oosScripts, "exit_code=$?")
	oosScripts = append(oosScripts, "if [ $exit_code != 0 ]; then")
	oosScripts = append(oosScripts, fmt.Sprintf("  tail -n 5 /deploy/%s.log >&2", ExecutionName))
	oosScripts = append(oosScripts, "fi")
	oosScripts = append(oosScripts, "exit $exit_code")
	scriptString := strings.Join(oosScripts, "\n") + "\n"
	/**
	exit_code=$?
	if [ $exit_code -eq 1 ]; then
	  cat /deploy/ensure_system_inited.log >&2
	fi

	exit $exit_code
	*/
	logger.Infof(ctx, "scripts: %s", scriptString)
	createOOSExecutionRequestV2 := oos.CreateOOSExecutionRequestV2{
		Template: oos.Template{
			Name:   w.getOOSTaskName(ExecutionName),
			Region: w.config.Region,
			Linear: true,
			Operators: []oos.Operator{
				{
					Name:          "run_scripts",
					Description:   "description",
					Operator:      "BCE::Agent::ExecuteShell",
					Retries:       0,
					RetryInterval: 3000,
					// 毫秒
					Timeout: OOSTimeout,
					Properties: map[string]interface{}{
						"content": scriptString,
						"user":    "root",
						"workDir": "/",
						"__workerSelectors__": map[string]string{
							"id":      w.instance.InstanceUUID,
							"shortId": w.instance.MachineID,
						},
					},
				},
			},
		},
	}

	return &createOOSExecutionRequestV2, nil
}
