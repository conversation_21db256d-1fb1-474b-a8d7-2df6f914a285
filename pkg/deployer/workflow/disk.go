// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/6/22 上午11:28, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	exectypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/logger"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

type DiskInfo struct {
	SN   string `json:"sn"`
	Type string `json:"type"`
}

func assignLocalDevices(ctx context.Context, configs []*types.DiskMountConfig, devices []string) error {
	deviceIndex := 0
	usedDevices := make(map[string]bool) // 记录已使用的设备

	// 第一遍处理：处理已指定 device 的配置
	for i := range configs {
		if configs[i].Local && configs[i].Device != "" {
			// 检查指定的设备是否在 nvmeDevices 中
			found := false
			for _, device := range devices {
				if configs[i].Device == device {
					if !usedDevices[device] {
						usedDevices[device] = true
						found = true
						break
					} else {
						return fmt.Errorf("device %s is already used", device)
					}
				}
			}
			if !found {
				return fmt.Errorf("specified device %s not found in available NVME devices, devices: %v", configs[i].Device, devices)
			}
		}
	}

	// 第二遍处理：处理未指定 device 的配置
	for i := range configs {
		if configs[i].Local && configs[i].Device == "" {
			// 分配一个未使用的设备
			for deviceIndex < len(devices) && usedDevices[devices[deviceIndex]] {
				deviceIndex++
			}
			if deviceIndex >= len(devices) {
				return fmt.Errorf("not enough NVME devices available")
			}
			configs[i].Device = devices[deviceIndex]
			usedDevices[devices[deviceIndex]] = true
			deviceIndex++
		}
	}

	return nil
}

type Disk struct {
	Device string
	Path   string
}

type DiskWithPath struct {
	LinkFileName string
	Serial       string
	Path         string
}

func (w *Workflow) FindDiskByIDFromOS(ctx context.Context, diskSerial map[string]DiskInfo) ([]DiskWithPath, error) {

	findDiskByID := exectypes.Command{
		Command: "bash -c 'ls /dev/disk/by-id'",
	}

	stdout, _, err := w.execclient.Exec(ctx, &findDiskByID)
	if err != nil {
		logger.Errorf(ctx, "findLocalDisks err: %s", err)
		return nil, err
	}
	var disksWithPath []DiskWithPath

	diskDevices := strings.Split(strings.TrimSpace(stdout), "\n")
	logger.Infof(ctx, "diskDevices: %v", diskDevices)

	//通过匹配盘符和盘id的方式找到对应关系，以及具体的设备
	for _, diskDevice := range diskDevices {
		diskDevice = strings.TrimSpace(diskDevice)
		diskWithPath := DiskWithPath{}
		if strings.Contains(diskDevice, "part") {
			continue
		}
		path := fmt.Sprintf("/dev/disk/by-id/%s", diskDevice)

		readLink := exectypes.Command{
			Command: "readlink " + path,
		}

		stdout, _, err := w.execclient.Exec(ctx, &readLink)
		if err != nil {
			logger.Errorf(ctx, " err: %s", err)
			return nil, err
		}
		logger.Infof(ctx, "diskDevice stdout: %s", stdout)

		//找到实际对应的盘符
		realPath := exectypes.Command{
			Command: "realpath " + stdout,
		}

		stdout, _, err = w.execclient.Exec(ctx, &realPath)
		if err != nil {
			logger.Errorf(ctx, " err: %s", err)
			return nil, err
		}
		logger.Infof(ctx, "absPath: %s", stdout)
		absPath := strings.Split(strings.TrimSpace(stdout), "\n")
		diskWithPath.LinkFileName = diskDevice
		diskWithPath.Path = absPath[0]
		disksWithPath = append(disksWithPath, diskWithPath)
	}
	return disksWithPath, nil

}

func (w *Workflow) ensureGuestDiskConfigsIfNeed(ctx context.Context, configs []*types.DiskMountConfig) error {
	logger.Infof(ctx, "want to mount info, start to find local disks from meta-api", &configs)

	//使用meta-api获取虚机内有多少实际的盘和类型
	command := exectypes.Command{
		Command: commandListDisk,
	}
	stdout, _, err := w.execclient.Exec(ctx, &command)
	if err != nil {
		logger.Errorf(ctx, "findLocalDisks err: %s", err)
		return err
	}
	//hpas当前没有接口，套餐一致，临时按照固定格式匹配
	if w.instance.Machinetype == ccetypes.MachineTypeHPAS {
		//hpas实例查找本地盘，hpas只有nvme
		nvmeCommand := exectypes.Command{
			Command: commandListNVME,
		}
		nvmeStdout, _, err := w.execclient.Exec(ctx, &nvmeCommand)
		if err != nil {
			logger.Errorf(ctx, "findLocalDisks err: %s", err)
			return err
		}
		nvmeDevices := strings.Split(strings.TrimSpace(nvmeStdout), "\n")
		nvmeMap := make(map[string]bool, 0)
		// 构建 map（空结构体节省内存）
		for _, dev := range nvmeDevices {
			dev = strings.TrimSpace(dev) // 确保去除空格
			if dev != "" {               // 忽略空行
				nvmeMap[dev] = false
			}
		}
		logger.Infof(ctx, "nvmeMap %v", nvmeMap)
		if len(nvmeMap) == 0 {
			logger.Errorf(ctx, "has no local disk")
			return fmt.Errorf("has no local disk")
		}

		if len(configs) > len(nvmeMap) {
			logger.Errorf(ctx, "need mount local dick more than already has")
			return fmt.Errorf("need mount local dick more than already has")
		}

		logger.Infof(ctx, "nvme map %v", nvmeMap)

		for i := 0; i < len(configs); i++ {
			logger.Infof(ctx, "want mount config is %v,nvme map %v", configs[i].Device, nvmeMap)
			if configs[i].Device != "" {
				if _, ok := nvmeMap[configs[i].Device]; ok {
					nvmeMap[configs[i].Device] = true
				}
			} else {
				findCanMountList := findCanMount(nvmeMap)
				configs[i].Device = findCanMountList[len(findCanMountList)-1]
				if _, ok := nvmeMap[configs[i].Device]; ok {
					nvmeMap[configs[i].Device] = true
				}
				logger.Infof(ctx, "mount config device is %v,nvme map %v", configs[i].Device, nvmeMap)
			}
		}
		return nil
	}
	diskSerial := make(map[string]DiskInfo, 0)

	if err := json.Unmarshal([]byte(stdout), &diskSerial); err != nil {
		logger.Errorf(ctx, "get disk serial from meta-api json unmarshal err: %s", err)
		return err
	}
	logger.Infof(ctx, "diskSerial is ", diskSerial)

	for serial, diskInfo := range diskSerial {
		fillSN(diskInfo, serial, configs)
	}

	//单机查询所有盘的短ID，然后填充到配置中
	diskLinkID, err := w.FindDiskByIDFromOS(ctx, diskSerial)
	if err != nil {
		logger.Errorf(ctx, "FindDiskByIDFromOS err: %s", err)
		return err
	}
	//获取对应的设备名称，比如vdb，vdc等，然后填充到配置中
	fillDevPath(ctx, configs, diskLinkID)
	b, err := json.Marshal(configs)
	if err != nil {
		logger.Errorf(ctx, "failed to marshal configs: %v", err)
		return err
	}

	// 打印到日志
	logger.Infof(ctx, "mount configs is: %s", string(b))
	logger.Infof(ctx, "fill dev path config is %+v", configs, diskLinkID)
	for _, config := range configs {
		logger.Infof(ctx, "can not find dev path for %s, %s, %s, %s", config.Device, config.VolumeID, config.SN, config.Path)
	}
	return nil
}

// checkDiskConfig - 检查 Disk 挂载配置
func (w *Workflow) ensureDiskConfig(ctx context.Context, config *types.DiskMountConfig) error {
	if config.Path == "" {
		return fmt.Errorf("check disk config failed, config.Path is empty")
	}

	if config.Local {
		return nil
	}

	logger.Infof(ctx, "disk config: %+v", config)

	// 解决盘符漂移问题:
	// 虚机挂载了多块盘，重装系统后，调 cds 的接口获取到的 device 可能不准确
	// 比如接口拿到的是 /dev/vdc, 虚机上实际是 /dev/vdb
	// 通过 /sys/block/vd*/serial 来判断 （有可能是短ID、也有可能是uuid 前8位）
	// eg: grep . /sys/block/vd*/serial
	// /sys/block/vda/serial:v-UjMB1yp1
	// /sys/block/vdb/serial:v-dU6rZcWz
	stdout, stderr, err := w.execclient.Exec(ctx, &exectypes.Command{
		Command: fmt.Sprintf(`grep . /sys/block/vd*/serial`),
	})
	if err != nil {
		logger.Errorf(ctx, "get disk serial failed, stderr: %s", stderr)
		return err
	}

	var newDevice string
	devices := strings.Split(stdout, "\n")
	for _, dev := range devices {
		if len(dev) == 0 {
			continue
		}
		// eg. dev: /sys/block/vda/serial:v-UjMB1yp1
		kv := strings.Split(strings.TrimSpace(dev), ":")
		if len(kv) != 2 {
			logger.Warnf(ctx, "unexpected device serial: %s", dev)
			continue
		}
		// 匹配短ID 或 uuid 前8位
		if kv[1] == config.VolumeID || strings.HasPrefix(config.ID, kv[1]) {
			strs := strings.Split(kv[0], "/")
			if len(strs) != 5 {
				logger.Warnf(ctx, "unexpected device serial: %s, use default device", dev)
				continue
			}
			newDevice = "/dev/" + strs[3]
			break
		}
	}

	// 虚机上未查到该CDS，则报错
	if newDevice == "" {
		logger.Errorf(ctx, "not found cds: %s in instance", config.VolumeID)
		return fmt.Errorf(fmt.Sprintf("not found cds: %s in instance", config.VolumeID))
	}

	// 使用查询到的盘符，而不是接口获取到的，避免盘符漂移
	logger.Infof(ctx, "get device from interface: %s, real device is : %s", config.Device, newDevice)
	config.Device = newDevice
	return nil
}

// checkDiskAttached - 检查 Disk 是否attach
// diskPartition: 磁盘分区名 /dev/vdb1
func (w *Workflow) checkDiskAttached(ctx context.Context, config *types.DiskMountConfig) (bool, error) {
	// 判断是否attach: ls /dev/vdb 2>/dev/null | wc -l
	stdout, stderr, err := w.execclient.Exec(ctx, &exectypes.Command{
		Command: fmt.Sprintf(`ls %s 2>/dev/null | wc -l`, config.Device),
	})
	if err != nil {
		logger.Errorf(ctx, "checkDiskAttached failed, stderr: %s", stderr)
		return false, err
	}
	if stdout == "1" {
		logger.Infof(ctx, "Disk %s is attached", config.Device)
		return true, nil
	}

	return false, nil
}

// getMountSrcOfDev - 获取用来分区的设备名
func (w *Workflow) getMountSrcOfDev(ctx context.Context, config *types.DiskMountConfig) (string, error) {
	logger.Infof(ctx, "try to find first partition of dev %s", config.Device)
	// devName[0]: "", devName[1]: "dev", devName[0]: "vdb"
	devName := strings.Split(config.Device, "/")
	if len(devName) != 3 {
		//设备名改成从单机获取，此时跳过判断是否有分区
		logger.Errorf(ctx, "invalid dev %s", config.Device)
		return "", fmt.Errorf("invalid dev %s", config.Device)
	}

	// 查询所有设备
	allDevs := make([]string, 0)
	devFiles, err := w.execclient.ReadDir(ctx, "/dev")
	if err != nil {
		return "", fmt.Errorf("error occurs when ls /dev, err: %s", err)
	}
	if devFiles == nil {
		return "", fmt.Errorf("ls /dev is nil")
	}

	// 获取该磁盘的所有文件，例如: vdb2, vdb1, vdb
	for _, devFile := range devFiles {
		if !devFile.IsDir() && strings.Contains(devFile.Name(), devName[2]) {
			allDevs = append(allDevs, devFile.Name())
		}
	}
	logger.Infof(ctx, "all dev files found for dev %s: %v", config.Device, allDevs)

	// 如果只有1个，则表示该磁盘未分区，使用整块盘挂载
	if len(allDevs) == 1 {
		logger.Infof(ctx, "no partition found for %s, use device name as mount src", config.Device)
		return config.Device, nil
	}
	// 磁盘已分区，使用第一个分区挂载
	// allDevs 排序: vdb, vdb1, vdb2...
	sort.Strings(allDevs)
	if len(allDevs) >= 2 && strings.HasPrefix(allDevs[1], allDevs[0]) {
		logger.Infof(ctx, "first partition %s found for %s, use it as mount src", allDevs[1], config.Device)
		return "/dev/" + allDevs[1], nil
	}

	errStr := fmt.Sprintf("cannot find first partition nor device file for dev %s", config.Device)
	logger.Errorf(ctx, errStr)
	return "", fmt.Errorf(errStr)
}

// checkDiskMounted - 检查 Disk 是否已挂载至目标挂载点
// 已挂载但没有挂载至目标挂载点，return err
// devSrc: 需挂载设备名
func (w *Workflow) checkDiskMounted(ctx context.Context, config *types.DiskMountConfig, devSrc string) (bool, error) {
	// 判断是否已挂载: df -h | awk '{print $1}' | grep /dev/vdb | wc -l
	stdout, stderr, err := w.execclient.Exec(ctx, &exectypes.Command{
		Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep %s | wc -l`, devSrc),
	})
	if err != nil {
		return false, fmt.Errorf("checkDiskMounted failed, stderr: %s", stderr)
	}
	if stdout == "1" {
		// 已挂载，判断挂载目录是否正确: df -h | grep /dev/vdb | grep /data | awk '{print $6}'
		stdout, stderr, err = w.execclient.Exec(ctx, &exectypes.Command{
			Command: fmt.Sprintf(`df -h | grep %s | grep %s | awk '{print $6}'`, devSrc, config.Path),
		})
		if err != nil {
			return true, fmt.Errorf("checkDiskMounted failed, stderr: %s", stderr)
		}
		if stdout != config.Path {
			return true, fmt.Errorf("disk %s aleady mounted and mount path is not as expected: %s", devSrc, config.Path)
		}

		return true, nil
	}

	logger.Infof(ctx, "Disk %s dont mounted, start mount", config.Device)
	return false, nil
}

// getFilesystemType - 查询文件系统类型
// 文件系统存在：
// exec: blkid -o udev /dev/vda1, stdout:
//
//	ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05
//	ID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05
//	ID_FS_TYPE=ext4
//
// 文件系统不存在:
// 返回空字符串, err: Process exited with status 2
func (w *Workflow) getFilesystemType(ctx context.Context, devSrc string) (string, error) {
	blkidInfo, err := w.getBlkidInfo(ctx, devSrc)
	// 执行结果不符合预期，比如命令超时
	if err != nil && err.Error() != "Process exited with status 2" {
		logger.Errorf(ctx, "exec blkid error, err: %v", err)
		return "", err
	}
	// 没有创建文件系统
	if err != nil && err.Error() == "Process exited with status 2" {
		logger.Infof(ctx, "dev: %s file system dont exist", devSrc)
		return "", nil
	}

	if _, ok := blkidInfo["ID_FS_TYPE"]; !ok {
		logger.Warnf(ctx, "ID_FS_TYPE not exist in blkid")
		return "", nil
	}

	logger.Infof(ctx, "dev: %s file system: %s", devSrc, blkidInfo["ID_FS_TYPE"])
	return blkidInfo["ID_FS_TYPE"], nil
}

// ensureDiskExt4 - 分区文件系统 ext4
// CentOS7，Ubuntu 创建文件系统耗时秒级
// CentOS6 创建文件系统时间较长，
func (w *Workflow) ensureDiskExt4(ctx context.Context, devSrc string) error {
	// CentOS7, Ubuntu: mkfs.ext4 /dev/vdb1
	// CentOS6: mkfs.ext4 -i 65536 /dev/vdb1
	mkfsCmd := fmt.Sprintf("mkfs.ext4 %s", devSrc)
	if strings.EqualFold(string(w.instance.InstanceOS.OSName), string(bccimage.OSNameCentOS)) &&
		strings.Contains(w.instance.InstanceOS.OSVersion, "6.") {
		mkfsCmd = fmt.Sprintf("mkfs.ext4 -i 65536 %s", devSrc)
	}

	stdout, stderr, err := w.execclient.Exec(ctx, &exectypes.Command{
		Command: mkfsCmd,
		Timeout: 600,
	})
	if err != nil {
		logger.Errorf(ctx, fmt.Sprintf("RemoteExec: %s failed, stderr: %s, err: %s", mkfsCmd, stderr, err))
		return err
	}

	logger.Infof(ctx, fmt.Sprintf("ensureDiskExt4 succed, stdout: %s", stdout))
	return nil
}

// writeFstab - 写入分区挂载信息
// devSrc: 分区名, 如：/dev/vdb
// mountPath: 挂载目录, 如: /data
// fsUUID: 分区UUID, 如: ID_FS_UUID="fc621823-5135-42a3-9639-b2ec688ec6b3"
func (w *Workflow) writeFstab(ctx context.Context, devSrc, mountPath string) error {
	// get ID_FS_UUID
	blkidInfo, err := w.getBlkidInfo(ctx, devSrc)
	if err != nil {
		logger.Errorf(ctx, "get blkid info error, err: %v", err)
		return err
	}

	if _, ok := blkidInfo["ID_FS_UUID"]; !ok {
		logger.Errorf(ctx, "ID_FS_UUID not exist in blkid")
		return fmt.Errorf("ID_FS_UUID")
	}

	logger.Infof(ctx, "ID_FS_UUID: %s", blkidInfo["ID_FS_UUID"])

	fs, err := w.execclient.Read(ctx, "/etc/fstab")
	if err != nil {
		logger.Errorf(ctx, "Read fstab failed: %s", err)
		return err
	}

	// 判断 /etc/fstab 中是否已包含该分区挂载信息、已有分区信息,并且挂载路径一致就跳过
	fstabStr := string(fs[:])
	if strings.Contains(fstabStr, blkidInfo["ID_FS_UUID"]) || strings.Contains(fstabStr, devSrc+" ") {
		logger.Warnf(ctx, "Disk %s already mount, /etc/fstab: %s", blkidInfo["ID_FS_UUID"], fstabStr)
		//挂载目录不一致，则更新挂载目录
		if !strings.Contains(fstabStr, mountPath) {
			if err := w.updateFstab(ctx, devSrc, mountPath, blkidInfo); err != nil {
				return err
			}
		} else {
			return nil
		}
	}

	if err := w.updateFstab(ctx, devSrc, mountPath, blkidInfo); err != nil {
		return err
	}

	logger.Infof(ctx, "Write fstab success, fsUUID: %s", blkidInfo["ID_FS_UUID"])
	return nil
}

func (w *Workflow) updateFstab(ctx context.Context, devSrc, mountPath string, blkidInfo map[string]string) error {
	// 更新 /etc/fstab 文件
	// 新增 nofail 参数， 避免因磁盘异常导致虚机启动失败，详情参考 wiki:
	// http://wiki.baidu.com/pages/viewpage.action?pageId=1627798506
	fstab := fmt.Sprintf("UUID=%s %s auto defaults,nofail 0 0", blkidInfo["ID_FS_UUID"], mountPath)
	logger.Infof(ctx, "write /etc/fstab: %s", fstab)
	if err := w.execclient.Write(ctx, &exectypes.File{
		WriteType: exectypes.WriteTypeAppend,

		Config: &exectypes.AppendFileConfig{
			Contents: []string{fstab},
			Name:     "fstab",
			Marker:   fmt.Sprintf("# CCE UUID=%s init", blkidInfo["ID_FS_UUID"]),
			Dir: &exectypes.Dir{
				Path: "/etc/",
			},
		},
	}); err != nil {
		logger.Errorf(ctx, "Write fstab failed: %s", err)
		return err
	}
	return nil
}

// getBlkidInfo - 查询块设备信息
// exec: blkid -o udev /dev/vda1, stdout:
//
//	ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05
//	ID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05
//	ID_FS_TYPE=ext4
//	ID_FS_PARTUUID=037b748a-01
//
// 文件系统不存在:
//
//	返回空字符串, err: Process exited with status 2
func (w *Workflow) getBlkidInfo(ctx context.Context, devSrc string) (map[string]string, error) {
	stdout, stderr, err := w.execclient.Exec(ctx, &exectypes.Command{
		Command: fmt.Sprintf(`blkid -o udev %s`, devSrc),
	})
	// 命令超时、没有创建文件系统等
	if err != nil {
		logger.Warnf(ctx, "exec blkid error, stdout: %s, stderr: %s, err: %v", stdout, stderr, err)
		return nil, err
	}

	logger.Infof(ctx, "get blkid info success: %s", stdout)
	m := parseBlkidOutput(stdout)
	return m, nil
}

func parseBlkidOutput(outputString string) map[string]string {
	outputs := strings.Split(outputString, "\n")
	m := make(map[string]string)
	for _, kvString := range outputs {
		kv := strings.Split(kvString, "=")
		if len(kv) == 2 {
			m[kv[0]] = kv[1]
		}
	}
	return m
}
