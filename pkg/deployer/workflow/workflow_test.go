// Copyright 2019 Baidu Inc. All rights reserved
// Use of this source code is governed by a CCE
// license that can be found in the LICENSE file.
/*
modification history
--------------------
2020/6/20 下午3:36, by <EMAIL>, create
*/
/*
DESCRIPTION
*/

package workflow

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/bcesdk/bccimage"
	exectypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/types"

	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec"
	execMocks "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/exec/mocks"
	system "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/task"
	k8stask "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/task/k8s"
	"icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/deployer/types"
	ccetypes "icode.baidu.com/baidu/jpaas-caas/cce-stack/pkg/types"
)

type fakeFileInfo struct {
	name     string
	contents string
	mode     os.FileMode
}

func (fi *fakeFileInfo) Name() string {
	return fi.name
}

func (fi *fakeFileInfo) Size() int64 {
	return 0
}

func (fi *fakeFileInfo) Mode() os.FileMode {
	return 777
}

func (fi *fakeFileInfo) ModTime() time.Time {
	return time.Now()
}

func (fi *fakeFileInfo) IsDir() bool {
	return false
}

func (fi *fakeFileInfo) Sys() interface{} {
	return nil
}

func TestWorkflow_EnsureDiskMounted(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type fields struct {
		config     *types.Config
		cluster    *types.Cluster
		instance   *types.Instance
		execclient exec.Interface
		roleclient ClusterRoleInterface
	}
	type args struct {
		ctx context.Context
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		wantErr bool
	}{
		{
			name: "异常返回",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("ta-SSSTC_ER2-GD480_0023191W01JN           "+
							"nvme-eui.0100000000000000c8d6b7afb9500f50    "+
							"nvme-INTEL_SSDPF2KX038T1_BTAX407202WX3P8CGN  "+
							"scsi-SATA_SSSTC_ER2-GD480_0023191W01JN  virtio-v-xEsse39s-part1\nnvme-eui.0100000000000000c8d6b76399500f50  nvme-INTEL_SSDPF2KX038T1_BTAX407200U23P8CGN  "+
							"scsi-0ATA_SSSTC_ER2-GD480_0023191W01JN       virtio-v-3feaeZuo                       virtio-v-xEsse39s-part2\nnvme-eui.0100000000000000c8d6b766bd500f50  nvme-INTEL_SSDPF2KX038T1_BTAX407201RU3P8CGN  scsi-1ATA_SSSTC_ER2-GD480_0023191W01JN       virtio-v-M9KDHAkK                       wwn-0x538f601610645775", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("/vdh", "", nil).AnyTimes(),
					// mock already attached
					// mock /dev 目录下的文件，用于获取磁盘分区
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					// mock 磁盘未挂载的情况
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep %s | wc -l`, "/dev/vdb"),
					}).Return("0", "", nil).AnyTimes(),
					//// mock 未创建文件系统
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev %s`, "/dev/vdb"),
					}).Return("", "", fmt.Errorf("Process exited with status 2")).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev %s`, "/dev/vdb"),
					}).Return("", "", fmt.Errorf("Process exited with status 2")).AnyTimes(),
					//// mock 创建文件系统
					//mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
					//	Command: fmt.Sprintf("mkfs.ext4 %s", "/dev/vdb"),
					//	Timeout: 600,
					//}).Return("", "", nil),
					// mock 创建挂载目录
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								VolumeID: "v-dU6rZcWz",
								Device:   "/dev/vdb",
								Path:     "/data",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("ta-SSSTC_ER2-GD480_0023191W01JN           "+
							"nvme-eui.0100000000000000c8d6b7afb9500f50    "+
							"nvme-INTEL_SSDPF2KX038T1_BTAX407202WX3P8CGN  "+
							"scsi-SATA_SSSTC_ER2-GD480_0023191W01JN  virtio-v-xEsse39s-part1\nnvme-eui.0100000000000000c8d6b76399500f50  nvme-INTEL_SSDPF2KX038T1_BTAX407200U23P8CGN  "+
							"scsi-0ATA_SSSTC_ER2-GD480_0023191W01JN       virtio-v-3feaeZuo                       virtio-v-xEsse39s-part2\nnvme-eui.0100000000000000c8d6b766bd500f50  nvme-INTEL_SSDPF2KX038T1_BTAX407201RU3P8CGN  scsi-1ATA_SSSTC_ER2-GD480_0023191W01JN       virtio-v-M9KDHAkK                       wwn-0x538f601610645775", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("/vdh", "", nil).AnyTimes(),
					// mock already attached
					// mock /dev 目录下的文件，用于获取磁盘分区
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					// mock 磁盘未挂载的情况
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep %s | wc -l`, "/dev/vdb"),
					}).Return("0", "", nil).AnyTimes(),
					//// mock 未创建文件系统
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev %s`, "/dev/vdb"),
					}).Return("", "", fmt.Errorf("Process exited with status 2")).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev %s`, "/dev/vdb"),
					}).Return("", "", fmt.Errorf("Process exited with status 2")).AnyTimes(),
					//// mock 创建文件系统
					//mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
					//	Command: fmt.Sprintf("mkfs.ext4 %s", "/dev/vdb"),
					//	Timeout: 600,
					//}).Return("", "", nil),
					// mock 创建挂载目录
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								VolumeID: "v-dU6rZcWz",
								Device:   "/dev/vdb",
								Path:     "/data",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								VolumeID: "v-dU6rZcWz",
								Device:   "/dev/vdb",
								Path:     "/data",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).
						Return("{\"7201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CD", "", nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								VolumeID: "v-dU6rZcWz",
								Device:   "/dev/vdb",
								Path:     "/data",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("0", "", fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								VolumeID: "v-dU6rZcWz",
								Device:   "/dev/vdb",
								Path:     "/data",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx-part", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:     "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device: "/dev/vdb",
								Path:   "/data",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("readlink /dev/disk/by-id/v-xxxx", "", fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/vdb",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/vdb",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("/vdh", "", fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/vdb",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/vdb",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("/vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/vdb",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/vdb",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "异常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`grep . /sys/block/vd*/serial`),
					}).Return("", "", fmt.Errorf("error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "正常情况",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`grep . /sys/block/vd*/serial`),
					}).Return("/sys/block/vdh/serial:v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vdh",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/vdh | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),

					mockExec.EXPECT().Read(gomock.Any(), "/etc/fstab").Return([]byte("UUID=12cc02a3-0105-4520-9272-1e782e29c640 /disk1 auto defaults,nofail 0 0"), nil).AnyTimes(),
					mockExec.EXPECT().Write(gomock.Any(), &exectypes.File{
						WriteType: exectypes.WriteTypeAppend,

						Config: &exectypes.AppendFileConfig{
							Contents: []string{"UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /data auto defaults,nofail 0 0"},
							Name:     "fstab",
							Marker:   fmt.Sprintf("# CCE UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 init"),
							Dir: &exectypes.Dir{
								Path: "/etc/",
							},
						}}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: "mount -a",
					}).Return("", "", nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
		{
			name: "fstab重复情况 1",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`grep . /sys/block/vd*/serial`),
					}).Return("/sys/block/vdh/serial:v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vdh",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/vdh | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),

					mockExec.EXPECT().Read(gomock.Any(), "/etc/fstab").Return([]byte("UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /disk1 auto defaults,nofail 0 0"), nil).AnyTimes(),
					mockExec.EXPECT().Write(gomock.Any(), &exectypes.File{
						WriteType: exectypes.WriteTypeAppend,

						Config: &exectypes.AppendFileConfig{
							Contents: []string{"UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /data auto defaults,nofail 0 0"},
							Name:     "fstab",
							Marker:   fmt.Sprintf("# CCE UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 init"),
							Dir: &exectypes.Dir{
								Path: "/etc/",
							},
						}}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: "mount -a",
					}).Return("", "", nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: false,
		},
		{
			name: "fstab重复情况 2",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`grep . /sys/block/vd*/serial`),
					}).Return("/sys/block/vdh/serial:v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vdh",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/vdh | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),

					mockExec.EXPECT().Read(gomock.Any(), "/etc/fstab").Return([]byte("UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /disk1 auto defaults,nofail 0 0"), nil).AnyTimes(),
					mockExec.EXPECT().Write(gomock.Any(), &exectypes.File{
						WriteType: exectypes.WriteTypeAppend,

						Config: &exectypes.AppendFileConfig{
							Contents: []string{"UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /data auto defaults,nofail 0 0"},
							Name:     "fstab",
							Marker:   fmt.Sprintf("# CCE UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 init"),
							Dir: &exectypes.Dir{
								Path: "/etc/",
							},
						}}).Return(fmt.Errorf("error")).AnyTimes(),
					//mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
					//	Command: "mount -a",
					//}).Return("", "", nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "fstab重复情况 3",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("bash -c 'ls /dev/disk/by-id'"),
					}).Return("v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("readlink /dev/disk/by-id/v-xxxx"),
					}).Return("../../vdh", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("realpath ../../vdh"),
					}).Return("", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vda",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`grep . /sys/block/vd*/serial`),
					}).Return("/sys/block/vdh/serial:v-xxxx", "", nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "vdh",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "vdb",
							mode: os.ModeDevice,
						},
						&fakeFileInfo{
							name: "testDir",
							mode: os.ModeDir,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/vdh | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/vdh`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),

					mockExec.EXPECT().Read(gomock.Any(), "/etc/fstab").Return([]byte("UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /disk1 auto defaults,nofail 0 0"), nil).AnyTimes(),
					mockExec.EXPECT().Write(gomock.Any(), &exectypes.File{
						WriteType: exectypes.WriteTypeAppend,

						Config: &exectypes.AppendFileConfig{
							Contents: []string{"UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /data auto defaults,nofail 0 0"},
							Name:     "fstab",
							Marker:   fmt.Sprintf("# CCE UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 init"),
							Dir: &exectypes.Dir{
								Path: "/etc/",
							},
						}}).Return(fmt.Errorf("error")),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "fstab重复情况 3",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("for dev in /sys/block/nvme*; do if [ -e \"$dev\" ]; then echo /dev/$(basename \"$dev\"); fi; done 2>/dev/null"),
					}).Return("/dev/nvme0n1\n/dev/nvme1n1\n/dev/nvme2n1\n/dev/nvme3n1", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/nvme0n1 | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/nvme0n1`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "nvme0n1",
							mode: os.ModeDevice,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Read(gomock.Any(), "/etc/fstab").Return([]byte("UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /disk1 auto defaults,nofail 0 0"), nil).AnyTimes(),
					mockExec.EXPECT().Write(gomock.Any(), &exectypes.File{
						WriteType: exectypes.WriteTypeAppend,

						Config: &exectypes.AppendFileConfig{
							Contents: []string{"UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 /data auto defaults,nofail 0 0"},
							Name:     "fstab",
							Marker:   fmt.Sprintf("# CCE UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05 init"),
							Dir: &exectypes.Dir{
								Path: "/etc/",
							},
						}}).Return(fmt.Errorf("error")),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/nvme0n1",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
						Machinetype: ccetypes.MachineTypeHPAS,
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "fstab重复情况 4",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("for dev in /sys/block/nvme*; do if [ -e \"$dev\" ]; then echo /dev/$(basename \"$dev\"); fi; done 2>/dev/null"),
					}).Return("", "", fmt.Errorf("has error")).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/nvme0n1",
								Path:     "/data",
								VolumeID: "v-xxxx",
							},
						},
						Machinetype: ccetypes.MachineTypeHPAS,
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "fstab重复情况 5",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("for dev in /sys/block/nvme*; do if [ -e \"$dev\" ]; then echo /dev/$(basename \"$dev\"); fi; done 2>/dev/null"),
					}).Return("/dev/nvme0n1\n", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/nvme0n1 | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/nvme0n1`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "nvme0n1",
							mode: os.ModeDevice,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/data",
					}).Return(nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "/dev/nvme0n1",
								Path:     "/data",
								VolumeID: "",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/test",
								VolumeID: "",
							},
						},
						Machinetype: ccetypes.MachineTypeHPAS,
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "fstab重复情况 6",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("for dev in /sys/block/nvme*; do if [ -e \"$dev\" ]; then echo /dev/$(basename \"$dev\"); fi; done 2>/dev/null"),
					}).Return("", "", nil),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/nvme0n1 | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/nvme0n1`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/test",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "nvme0n1",
							mode: os.ModeDevice,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/test",
					}).Return(nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/test",
								VolumeID: "",
							},
						},
						Machinetype: ccetypes.MachineTypeHPAS,
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
		{
			name: "fstab重复情况 7",
			fields: func() fields {
				mockExec := execMocks.NewMockInterface(ctrl)
				gomock.InOrder(
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("curl ***************/1.0/meta-data/disk-serial-description/disk_serial_desc"),
					}).Return("{\"v-Ug6uJKEa\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201UG3P8CGN\"},\"v-M9KDHAkK\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-5Bk05H0v\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407200U23P8CGN\"},\"v-BJ3UDSe5\":{\"type\":\"LOCAL\",\"sn\":\"0023191W01JN\"},\"v-3feaeZuo\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-3naSQl4W\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407202WX3P8CGN\"},\"v-xEsse39s\":{\"type\":\"CDS\",\"sn\":\"\"},\"v-7TKhzSuY\":{\"type\":\"LOCAL\",\"sn\":\"BTAX407201RU3P8CGN\"}}", "", nil),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf("for dev in /sys/block/nvme*; do if [ -e \"$dev\" ]; then echo /dev/$(basename \"$dev\"); fi; done 2>/dev/null"),
					}).Return("/dev/nvme0n1\n/dev/nvme1n1\n/dev/nvme2n1\n/dev/nvme3n1\n", "", nil),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`df -h | awk '{print $1}' | grep /dev/nvme0n1 | wc -l`),
					}).Return("0", "", nil).AnyTimes(),
					mockExec.EXPECT().Exec(gomock.Any(), &exectypes.Command{
						Command: fmt.Sprintf(`blkid -o udev /dev/nvme0n1`),
					}).Return("ID_FS_UUID=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_UUID_ENC=f6c35221-b37e-47e1-94bf-52b2721c9e05\nID_FS_TYPE=ext4\nID_FS_PARTUUID=037b748a-01", "", nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/test",
					}).Return(nil).AnyTimes(),
					mockExec.EXPECT().Read(gomock.Any(), "/etc/fstab").Return([]byte("UUID=12cc02a3-0105-4520-9272-1e782e29c640 /disk1 auto defaults,nofail 0 0"), nil).AnyTimes(),
					mockExec.EXPECT().ReadDir(gomock.Any(), "/dev").Return([]os.FileInfo{
						&fakeFileInfo{
							name: "nvme0n1",
							mode: os.ModeDevice,
						},
					}, nil).AnyTimes(),
					mockExec.EXPECT().Mkdir(gomock.Any(), &exectypes.Dir{
						Path: "/test",
					}).Return(nil).AnyTimes(),
				)

				return fields{
					instance: &types.Instance{
						InstanceOS: ccetypes.InstanceOS{
							OSName:    bccimage.OSNameCentOS,
							OSVersion: "7.3",
						},
						DiskMountConfigs: []*types.DiskMountConfig{
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/home/<USER>",
								VolumeID: "",
							}, {
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data",
								VolumeID: "",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/var/lib/kubelet",
								VolumeID: "",
							},
							{
								ID:       "d20e395b-29ac-43a8-8007-fb117d05d4ba",
								Device:   "",
								Path:     "/data/agentsmith",
								VolumeID: "",
							},
						},
						Machinetype: ccetypes.MachineTypeHPAS,
					},
					execclient: mockExec,
				}
			}(),
			args: args{
				ctx: context.TODO(),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			w := &Workflow{
				config:     tt.fields.config,
				cluster:    tt.fields.cluster,
				instance:   tt.fields.instance,
				execclient: tt.fields.execclient,
				roleclient: tt.fields.roleclient,
			}
			if err := w.EnsureDiskMounted(tt.args.ctx); (err != nil) != tt.wantErr {
				t.Errorf("EnsureDiskMounted() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func Test_createRuntimeOrNot(t *testing.T) {
	type args struct {
		runtimeType ccetypes.RuntimeType
		create      bool
	}
	tests := []struct {
		name string
		args args
		want types.TaskInterface
	}{
		{
			name: "正常流程: docker",
			args: args{
				runtimeType: ccetypes.RuntimeTypeDocker,
				create:      true,
			},
			want: k8stask.InstallDocker(),
		},
		{
			name: "正常流程: containerd",
			args: args{
				runtimeType: ccetypes.RuntimeTypeContainerd,
				create:      true,
			},
			want: k8stask.InstallContainerd(),
		},
		{
			name: "正常流程: 跳过部署",
			args: args{
				runtimeType: ccetypes.RuntimeTypeDocker,
				create:      false,
			},
			want: nil,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := createRuntimeOrNot(tt.args.runtimeType, tt.args.create); !reflect.DeepEqual(got, tt.want) {
				t.Errorf("createRuntimeOrNot() = %v, want %v", got, tt.want)
			}
		})
	}
}

func Test_execTask(t *testing.T) {
	ctrl := gomock.NewController(t)
	type args struct {
		ctx   context.Context
		tasks []types.TaskInterface
	}

	tests := []struct {
		name    string
		args    args
		execErr error
		wantErr bool
	}{
		{
			name: "正常流程: docker",
			args: args{
				ctx: context.TODO(),
				tasks: []types.TaskInterface{
					system.Template(),
				},
			},
		},
		{
			name: "oos执行失败流程，打印日志",
			args: args{
				ctx: context.TODO(),
				tasks: []types.TaskInterface{
					system.Template(),
				},
			},
			execErr: fmt.Errorf("error"),
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockExec := execMocks.NewMockInterface(ctrl)
			mockExec.EXPECT().Exec(gomock.Any(), gomock.Any()).Return("", "", tt.execErr).AnyTimes()

			w := Workflow{
				execclient: mockExec,
				config:     &types.Config{},
				cluster:    &types.Cluster{},
				instance:   &types.Instance{},
			}
			err := w.execTask(tt.args.ctx, tt.args.tasks)
			if (err == nil) && tt.wantErr {
				t.Errorf("createRuntimeOrNot() = %v, want %v", err, tt.wantErr)
			}
		})
	}
}

func Test_NewWorkflow(t *testing.T) {
	ctx := context.TODO()
	type args struct {
		ctx      context.Context
		config   *types.Config
		cluster  *types.Cluster
		instance *types.Instance
	}
	tests := []struct {
		name string
		args args
		want *Workflow
	}{
		{
			name: "正常流程: docker",
			args: args{
				ctx: ctx,
				config: &types.Config{
					Region: "bj",
				},
				cluster: &types.Cluster{
					SkipInstallRuntime: false,
				},
				instance: &types.Instance{
					EnableImageAccelerate: true,
					ClusterRole:           ccetypes.ClusterRoleNode,
					SSHIP:                 "xxx.xxx.xx.x",
					SSHPort:               80,
					RunUser:               "root",
					Local:                 true,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := NewWorkflow(tt.args.ctx, tt.args.config, tt.args.cluster, tt.args.instance, nil)
			if err != nil {
				t.Errorf("Test_NewWorkflow(), err is not nil")
			}
		})
	}
}
