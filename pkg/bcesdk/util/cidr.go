package util

import (
	"math/big"
	"math/rand"
	"net"
	"time"

	gocidr "github.com/apparentlymart/go-cidr/cidr"
	netutil "k8s.io/utils/net"
)

var (
	_, PrivateIPv4Net1, _ = net.ParseCIDR("10.0.0.0/8")
	_, PrivateIPv4Net2, _ = net.ParseCIDR("**********/12")
	_, PrivateIPv4Net3, _ = net.ParseCIDR("***********/16")
	_, PrivateIPv4Net4, _ = net.ParseCIDR("*******/8")
	_, PrivateIPv4Net5, _ = net.ParseCIDR("*******/8")
	//新增私有网段使用x.x.x.x/8的形式，确保容器网段和节点网段不冲突，申请vpc的网段可以小于x.x.x.x/8，比如x.x.x.x/16
	_, PrivateIPv4Net6, _ = net.ParseCIDR("*******/8")

	PrivateIPv4Nets = []*net.IPNet{PrivateIPv4Net1, PrivateIPv4Net2, PrivateIPv4Net3, PrivateIPv4Net4,
		PrivateIPv4Net5, PrivateIPv4Net6}
	PrivateIPv4NetsString = []string{"10.0.0.0/8", "**********/12", "***********/16", "*******/8", "*******/8", "*******/8"}

	// IPv6 本地唯一地址段是 fd00::/8，但目前仅使用了 FD00::/8
	// FC00::/8被预留到以后扩展用
	// 本地唯一地址也拥有全球唯一前缀，使用随机方式生成
	_, PrivateIPv6Net, _  = net.ParseCIDR("fd00::/8")
	PrivateIPv6Nets       = []*net.IPNet{PrivateIPv6Net}
	PrivateIPv6NetsString = []string{"fd00::/8"}

	_, IPv4ZeroCIDR, _ = net.ParseCIDR("0.0.0.0/0")
	_, IPv6ZeroCIDR, _ = net.ParseCIDR("::/0")

	_, LinkLocalCIDR, _ = net.ParseCIDR("***********/16")

	ipRand = rand.New(rand.NewSource(time.Now().UnixNano()))

	// 百舸专用
	_, clusterIPv4Net1, _   = net.ParseCIDR("********/8")
	PreserveClusterIPv4Nets = []*net.IPNet{clusterIPv4Net1}
)

func IsValidCIDR(cidr string) bool {
	ip, ipnet, err := net.ParseCIDR(cidr)
	if err != nil || !ip.Equal(ipnet.IP) {
		return false
	}
	return true
}

// IsConflictCIDR 检查两个网段是否冲突
func IsConflictCIDR(cidr1, cidr2 *net.IPNet) bool {
	var err error
	subnets := []*net.IPNet{cidr1, cidr2}

	// different IP family not conflict
	if netutil.IsIPv6CIDR(cidr1) != netutil.IsIPv6CIDR(cidr2) {
		return false
	}

	if netutil.IsIPv6CIDR(cidr1) {
		err = gocidr.VerifyNoOverlap(subnets, IPv6ZeroCIDR)
	} else {
		err = gocidr.VerifyNoOverlap(subnets, IPv4ZeroCIDR)
	}

	if err == nil {
		return false
	}
	return true
}

// IsConflictCIDRString 检查两个网段是否冲突
func IsConflictCIDRString(cidr1, cidr2 string) bool {
	_, c1, _ := net.ParseCIDR(cidr1)
	_, c2, _ := net.ParseCIDR(cidr2)
	return IsConflictCIDR(c1, c2)
}

// IsPrivateNetCIDR 检查网段是否属于私有网段
func IsPrivateNetCIDR(cidr *net.IPNet) bool {
	first, last := gocidr.AddressRange(cidr)
	if netutil.IsIPv6CIDR(cidr) {
		return PrivateIPv6Net.Contains(first) && PrivateIPv6Net.Contains(last)
	}

	for _, net := range PrivateIPv4Nets {
		if net.Contains(first) && net.Contains(last) {
			return true
		}
	}

	return false
}

// IsPrivateNetCIDRString 检查网段是否属于私有网段
func IsPrivateNetCIDRString(cidr string) bool {
	_, c, _ := net.ParseCIDR(cidr)
	return IsPrivateNetCIDR(c)
}

// IsPrivateNetIP 检查 ip (ipv4) 是否属于私有网段
func IsPrivateNetIP(ip string) bool {
	ipAddress := net.ParseIP(ip)

	for _, n := range PrivateIPv4Nets {
		if n.Contains(ipAddress) {
			return true
		}
	}

	return false
}

func IsPreserveClusterIP(cidr string) bool {
	_, c, err := net.ParseCIDR(cidr)
	if err != nil {
		return false
	}
	return IsPreserveClusterIPCIDR(c)
}

// IsPreserveClusterIPCIDR 检查网段是否属于预留的ClusterIP网段
func IsPreserveClusterIPCIDR(cidr *net.IPNet) bool {
	first, last := gocidr.AddressRange(cidr)

	for _, net := range PreserveClusterIPv4Nets {
		if net.Contains(first) && net.Contains(last) {
			return true
		}
	}

	return false
}

// 随机生成IPv6子CIDR
// IPv6 地址生成规则：
// * ClusterIP 采用一个64位前缀的本地唯一地址段，如： fd40:8500:5d:1::/64，约有1.84467440737E19个可用地址。
// * Prefix固定为fd00::/8表示本地唯一单播地址
// * L固定设置为1代表该地址由本地管理
// * Global ID40位的唯一ID用于代表一个唯一的网络，采用复制VPC第16-56位字符的方式进行分配。
// 这样通过GlobalID也能关联到一个ClusterIP所属的VPC。
// * Subnet ID在rfc4193中，子网ID用于聚合路由。但在ClusterIP领域，我们用它来代表“任播”地址的范围。
// ClusterIP的子网ID采用随机的方式来分配，用来代表唯一的子网ID
// https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/Jk_2Ea1IJe/aiZAXYYI0j/jAQVDhf1bzla_X#anchor-23f31870-6f34-11ed-be58-5b2a0073fe1d
func GenerateRandomIPv6SubCIDR(ipnet *net.IPNet, maskSize int) *net.IPNet {
	mask := net.CIDRMask(maskSize, 8*len(ipnet.IP))

	// 生成推荐子网的第一个地址，以 0xfd00::/8 的开头的地址段
	start := netutil.BigForIP(ipnet.IP)

	// 1. 随机生成Glonal ID，固定为40位
	globalID := big.NewInt(1<<40 - 1)
	globalID.Rand(ipRand, globalID)
	globalID.Lsh(globalID, 80)
	start.Add(start, globalID)

	// 2. 随机生成子网号，固定为16位
	subnetID := big.NewInt(1<<16 - 1)
	subnetID.Rand(ipRand, subnetID)
	subnetID.Lsh(subnetID, 64)
	start.Add(start, subnetID)

	// 3. 随机填充interfaceID前缀，使IPv6地址凑够128位
	if maskSize > 64 {
		interfaceID := big.NewInt(int64(maskSize - 64))
		interfaceID.Rand(ipRand, interfaceID)
		interfaceID.Lsh(interfaceID, uint(128-maskSize))
		start.Add(start, interfaceID)
	}

	startIP := netutil.AddIPOffset(start, 0)
	subnet := &net.IPNet{IP: startIP.Mask(mask), Mask: mask}
	return subnet
}

// IsSubnet 检查子网 CIDR(subCIDR)是否是父网 CIDR(parentCIDR)的子集
func IsSubnet(subCIDR, parentCIDR string) bool {
	_, parentNet, _ := net.ParseCIDR(parentCIDR)
	_, subNet, _ := net.ParseCIDR(subCIDR)

	// 检查子网的网络号是否在父网范围内
	if !parentNet.Contains(subNet.IP) {
		return false
	}

	// 检查子网的广播地址是否在父网范围内
	_, broadcast := gocidr.AddressRange(subNet)
	return parentNet.Contains(broadcast)
}
