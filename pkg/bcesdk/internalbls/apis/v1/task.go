package v1

type TaskDetailGroup struct {
	Status TaskDetail `json:"status"`
	Config Config     `json:"config"`
}

type TaskDetail struct {
	ID               string `json:"id"`
	Name             string `json:"name"`
	Type             string `json:"type"`
	Status           string `json:"status"`
	CreationDateTime string `json:"creationDateTime"`
	TaskVersion      string `json:"taskVersion"`
}

type ProcessConfig struct {
	Regex            string `json:"regex,omitempty"`
	Separator        string `json:"separator,omitempty"`
	Quote            string `json:"quote,omitempty"`
	SampleLog        string `json:"sampleLog,omitempty"`
	Keys             string `json:"keys,omitempty"`
	DataType         string `json:"dataType,omitempty"`
	DiscardOnFailure bool   `json:"discardOnFailure,omitempty"`
	KeepOriginal     bool   `json:"keepOriginal,omitempty"`
}

type SrcConfig struct {
	SrcType        string        `json:"srcType"`
	TaskType       string        `json:"taskType,omitempty"`
	LogType        string        `json:"logType,omitempty"`
	SrcDir         string        `json:"srcDir,omitempty"`
	MatchedPattern string        `json:"matchedPattern,omitempty"`
	MetaEnv        []string      `json:"metaEnv,omitempty"`
	IgnorePattern  string        `json:"ignorePattern,omitempty"`
	TimeFormat     string        `json:"timeFormat,omitempty"`
	TTL            int           `json:"ttl,omitempty"`
	UseMultiline   bool          `json:"useMultiline,omitempty"`
	MultilineRegex string        `json:"multilineRegex,omitempty"`
	RecursiveDir   bool          `json:"recursiveDir,omitempty"`
	ProcessType    string        `json:"processType,omitempty"`
	ProcessConfig  ProcessConfig `json:"processConfig,omitempty"`
	LogTime        string        `json:"logTime,omitempty"`
	TimestampKey   string        `json:"timestampKey,omitempty"`
	DateFormat     string        `json:"dateFormat,omitempty"`
	FilterExpr     string        `json:"filterExpr,omitempty"`
	LabelWhite     []Label       `json:"labelWhite"`
	LabelBlack     []Label       `json:"labelBlack"`
	EnvWhite       []Label       `json:"envWhite"`
	EnvBlack       []Label       `json:"envBlack"`
	Units          []string      `json:"units,omitempty"`
	CloseInactive  string        `json:"closeInactive,omitempty"`
	HarvesterLimit int           `json:"HarvesterLimit,omitempty"`
	PodLabelBlack  []Label       `json:"podLabelBlack"`
	PodLabelWhite  []Label       `json:"podLabelWhite"`
	MetaLabel      []string      `json:"metaLabel"`
}

type DestConfig struct {
	LogStore            string `json:"logStore,omitempty"`
	AccountID           string `json:"accountID,omitempty"`
	Project             string `json:"project,omitempty"`
	CodeC               string `json:"codeC,omitempty"`
	DestDir             string `json:"destDir,omitempty"`
	DestType            string `json:"destType,omitempty"`
	LogAggrByHost       string `json:"logAggrByHost,omitempty"`
	LogAggrByTime       string `json:"logAggrByTime,omitempty"`
	LogAggrByTimeFormat string `json:"logAggrByTimeFormat,omitempty"`
	NeedNotify          bool   `json:"needNotify,omitempty"`
	RateLimit           int    `json:"rateLimit,omitempty"`
	BESClusterID        string `json:"destClusterId,omitempty"`
	BESUser             string `json:"destUser,omitempty"`
	BESPasswd           string `json:"destPasswd,omitempty"`
	BESIndexPrefix      string `json:"indexPrefix,omitempty"`
	BESIndexRolling     string `json:"indexRolling,omitempty"`
	BESIsPwChange       bool   `json:"isPwChange,omitempty"`
}

type Config struct {
	SrcConfig  SrcConfig  `json:"srcConfig"`
	DestConfig DestConfig `json:"destConfig"`
}

type Host struct {
	HostID                  string `json:"hostId"`
	HostName                string `json:"hostName"`
	IP                      string `json:"ip"`
	UpdatedTime             string `json:"updatedTime,omitempty"`
	IsAvailableForNewConfig bool   `json:"isAvailableForNewConfig"`
	Status                  string `json:"status"`
}

type Label struct {
	Key   string `json:"key"`
	Value string `json:"value"`
}

type Tag struct {
	TagValue string `json:"tagValue"`
	TagKey   string `json:"tagKey"`
}
